﻿using System;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;

namespace Apq.NotifyChange
{
    /// <summary>
    /// 属性更改通知(可暂停)
    /// </summary>
    /// <remarks>使用OnDestroy替换IDisposable接口</remarks>
    public class NotifyPropertyChangeMonoBehaviour : MonoBehaviour
    {
        public NotifyPropertyChangeMonoBehaviour()
        {
	        StartEventListener_PropertyChange();
        }

        /// <summary>
        /// 开始监听自己的属性更改事件
        /// </summary>
        public void StartEventListener_PropertyChange()
        {
	        PropertyChanging += Me_PropertyChanging;
	        PropertyChanged += Me_PropertyChanged;
        }

        /// <summary>
        /// 停止监听自己的属性更改事件
        /// </summary>
        public void StopEventListener_PropertyChange()
        {
	        PropertyChanging -= Me_PropertyChanging;
	        PropertyChanged -= Me_PropertyChanged;
        }

		protected virtual bool Me_PropertyChanging(NotifyPropertyChangeMonoBehaviour me, PropertyChangeEventArgs e)
        {
            return false;
        }

        protected virtual void Me_PropertyChanged(NotifyPropertyChangeMonoBehaviour me, PropertyChangeEventArgs e)
        {
        }

        protected virtual void OnDestroy()
        {
            PropertyChanging -= Me_PropertyChanging;
            PropertyChanged -= Me_PropertyChanged;
        }

        #region INotifyPropertyChanging
        private readonly List<string> __EventSuspend_PropertyChanging = new();
        /// <summary>
        /// 属性即将更改事件
        /// </summary>
        public event Func<NotifyPropertyChangeMonoBehaviour, PropertyChangeEventArgs, bool> PropertyChanging;

        /// <summary>
        /// 通知属性即将更改
        /// </summary>
        protected virtual bool OnPropertyChanging<T>(string propertyName, T originalValue, T newValue)
        {
            return FirePropertyChanging(propertyName, originalValue, newValue);
        }

        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        /// <returns>是否撤消更改</returns>
        public virtual bool FirePropertyChanging<T>(string propertyName, T originalValue, T newValue)
        {
            if (PropertyChanging != null
                && !__EventSuspend_PropertyChanging.Contains(propertyName)
                && !__EventSuspend_PropertyChanging.Any(i => NotifyPropertyChange.EventSuspendAnyProperty.Any(n => n.Equals(i, StringComparison.OrdinalIgnoreCase)))
            )
            {
                var e = new PropertyChangeEventArgs(propertyName)
                {
                    OriginalValue = originalValue,
                    NewValue = newValue,
                };
                PropertyChanging?.Invoke(this, e);
                return e.Cancel;
            }
            return false;
        }

        /// <summary>
        /// 暂停或恢复 引发属性即将更改事件
        /// </summary>
        /// <param name="propertyName">属性名("All"/"Any"表示任意属性)</param>
        /// <param name="Resume">是否恢复引发事件</param>
        public void EventSuspend_PropertyChanging(string propertyName, bool Resume = false)
        {
            if (Resume && __EventSuspend_PropertyChanging.Contains(propertyName))
            {
                __EventSuspend_PropertyChanging.Remove(propertyName);
            }
            else if (!Resume && !__EventSuspend_PropertyChanging.Contains(propertyName))
            {
                __EventSuspend_PropertyChanging.Add(propertyName);
            }
        }
        #endregion

        #region INotifyPropertyChanged
        private readonly List<string> __EventSuspend_PropertyChanged = new();
        /// <summary>
        /// 属性已更改事件
        /// </summary>
        public event Action<NotifyPropertyChangeMonoBehaviour, PropertyChangeEventArgs> PropertyChanged;

        /// <summary>
        /// 通知属性已更改
        /// </summary>
        protected void OnPropertyChanged<T>(string propertyName, T originalValue, T newValue)
        {
            FirePropertyChanged(propertyName, originalValue, newValue);
        }

        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        public virtual void FirePropertyChanged<T>(string propertyName, T originalValue, T newValue)
        {
            if (PropertyChanged != null
                && !__EventSuspend_PropertyChanged.Contains(propertyName)
                && !__EventSuspend_PropertyChanging.Any(i => NotifyPropertyChange.EventSuspendAnyProperty.Any(n => n.Equals(i, StringComparison.OrdinalIgnoreCase)))
            )
            {
                var e = new PropertyChangeEventArgs(propertyName)
                {
                    OriginalValue = originalValue,
                    NewValue = newValue,
                };
                PropertyChanged?.Invoke(this, e);
            }
        }

        /// <summary>
        /// 暂停或恢复 引发属性已更改事件
        /// </summary>
        /// <param name="propertyName">属性名("All"/"Any"表示任意属性)</param>
        /// <param name="Resume">是否恢复引发事件</param>
        public void EventSuspend_PropertyChanged(string propertyName, bool Resume = false)
        {
            if (Resume && __EventSuspend_PropertyChanged.Contains(propertyName))
            {
                __EventSuspend_PropertyChanged.Remove(propertyName);
            }
            else if (!Resume && !__EventSuspend_PropertyChanged.Contains(propertyName))
            {
                __EventSuspend_PropertyChanged.Add(propertyName);
            }
        }
        #endregion
    }
}
