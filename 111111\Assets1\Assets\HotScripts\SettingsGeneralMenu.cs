using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using TMPro;
using Spine.Unity;
using UnityEngine.SceneManagement;
public class SettingsGeneralMenu : MonoBehaviour
{

    public Button mainButton;

    [SerializeField] private Image displayArea;
    [SerializeField] private TextMeshP<PERSON>UGUI musiclbl;
    [SerializeField] private TextM<PERSON>ProUGUI soundlbl;
    [SerializeField] private TextMesh<PERSON>roUGUI resetProgressLabel;
    [SerializeField] private CustomButton musicButton;
    [SerializeField] private CustomButton soundButton;
    [SerializeField] private CustomButton resetButton;
    [SerializeField] private CustomButton videosButton;
    [SerializeField] private CustomButton languageButton;
    [SerializeField] private CustomButton supportButton;
    [SerializeField] private TextMeshProUGUI lbl = null;
    [SerializeField] private PopUp popUp;

    private bool isEnabled = false;
    private List<TextMeshProUGUI> labelArray = new List<TextMeshProUGUI>();
    private List<CustomButton> menuArray = new List<CustomButton>();
    private SkeletonAnimation rays = null;
    private List<GameObject> buttonArray = new List<GameObject>();
    private int currentSelected = 0;
    private Vector2 winSize;
    private Image bg;


    [SerializeField] private VideoMenu videoPopUp;
    [SerializeField] private LanguagesMenu languagePopUp;

    private void Start()
    {
        Init();
    }
    private void Init()
    {



        isEnabled = true;
        //{


        //    _bg = ui::ImageView::create(POPUP_BASE);
        //    this.addChild(_bg);

        //    _bg.setScale9Enabled(true);
        //    _bg.setContentSize(BG_SIZE);
        //    _bg.updateTransform();
        //    _bg.setAnchorPoint(cocos2d::Point::ANCHOR_BOTTOM_LEFT);



        //    const float newOffset = 20;



        //    const float scale = 1.0f;
        //    _mainButton = ui::Button::create("res/SettingsPopup/middleYellow.png");
        //    _bg.addChild(_mainButton);
        //    _mainButton.setScale9Enabled(true);
        //    _mainButton.setScale(scale, scale);
        //    _mainButton.setContentSize(Size(BG_SIZE.width / 2 - 50, _mainButton.getContentSize().height * 1.75f - 5));
        //    _mainButton.setAnchorPoint(Point(0.5, 0));
        //    _mainButton.setZoomScale(0.0f);


        //    {
        //        Sprite* leftAttachment = Sprite::create("res/SettingsPopup/Straight_Corner2.png");
        //        _mainButton.addChild(leftAttachment);
        //        leftAttachment.setPosition(Point(0, _mainButton.getContentSize().height));
        //        leftAttachment.setAnchorPoint(Point(1, 1));
        //        _mainButton.setPosition(Point(+leftAttachment.getContentSize().width + _mainButton.getContentSize().width / 2, _bg.getContentSize().height - 23));
        //    }
        //    {
        //        Sprite* rightAttachment = Sprite::create("res/SettingsPopup/Curved_Corner5.png");
        //        _mainButton.addChild(rightAttachment);
        //        rightAttachment.setPosition(Point(_mainButton.getContentSize().width, _mainButton.getContentSize().height));
        //        rightAttachment.setAnchorPoint(Point(0, 1));
        //    }
        //    float fontSize = 40;
        //    if (GameData.instance.getCurrentLanguageCode() == "de" || GameData.instance.getCurrentLanguageCode() == "nl")
        //    {
        //        fontSize = 36;
        //    }
        //    {
        //        lbl = GameData.instance.GetMenuData(Globals.SETTINGS_MENU_MOBILE)["general"] as string;//, GAME_FONT, fontSize);
        //        _mainButton.addChild(lbl);
        //        lbl.setPosition(_mainButton.getContentSize() / 2);
        //        Shared::fontToCustom(lbl);


        //    }
        //    _mainButton.setCascadeColorEnabled(true);


        CreateLabelButtons();
        CreateOtherButtons();
        CreateKeyBoardAndControllerSupport();
        currentSelected = 0;

        //return true;
    }


    public void SetEnabledNode(bool enable)
    {
        isEnabled = enable;
        if (enable)
        {
            mainButton.image.color = new Color(255, 255, 255);
            GetComponent<Canvas>().sortingOrder = 5;
            lbl.color = Color.yellow;
            //        _mainButton.setPositionY(_mainButton.getPositionY() + Position);
            //        lbl.setPositionY(lbl.getPositionY() - Position/2);

        }
        else
        {
            mainButton.image.color = new Color(160, 160, 160);
            GetComponent<Canvas>().sortingOrder = 2;
            lbl.color = Color.white;
            //        _mainButton.setPositionY(_mainButton.getPositionY() - Position);
            //        lbl.setPositionY(lbl.getPositionY() + Position/2);
        }

    }
    public bool GetEnabledNode()
    {
        return isEnabled;
    }

    void Update()
    {
    }

    void CreateLabelButtons()
    {
        musiclbl.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["music"] as string;//, GAME_FONT, 45);
        //Shared::fontToCustom(Musiclbl);
        //musiclbl.gameObject.SetActive(false);
        //_bg.addChild(Musiclbl);
        //Musiclbl.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));

        soundlbl.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["sound"] as string;//, GAME_FONT, 45);
        //Shared::fontToCustom(Soundlbl);
        //_bg.addChild(Soundlbl);
        //soundlbl.gameObject.SetActive(false);
        //Soundlbl.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        labelArray.Add(soundlbl);
        labelArray.Add(musiclbl);
#if !UNITY_STANDALONE
        //Shared::alignItemsVerticallyWithPadding(Point(250, 250), 70, _labelArray, false);

#else
        //Shared::alignItemsVerticallyWithPadding(Point(250, 250), 70, _labelArray, false);



#endif


        //const Size buttonSize = Size(320, 160);

        {//music On
            //Vector<Label*> allLabels;

            musicButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAME_FONT, 90);
                                                                                                        //Shared::fontToCustom(lbl);
                                                                                                        //allLabels.Add(lbl);
                                                                                                        //log("ON: %d", lbl.getStringLength());

            musicButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAME_FONT, 90);
                                                                                                          //Shared::fontToCustom(lbl2);
                                                                                                          //log("OFF: %d", lbl2.getStringLength());
                                                                                                          //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instance.getCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //_bg.addChild(onButton);
            //onButton.setPosition(Vec2(Musiclbl.getPosition().x + 300, Musiclbl.getPosition().y));


            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.setVisible(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //_bg.addChild(offButton);


            // offButton.setPosition(Vec2(Musiclbl.getPosition().x + 300, Musiclbl.getPosition().y));
            menuArray.Add(musicButton);
            musicButton.defaultAction = () =>
            {
                musicButton.SetIsOn(false);
                //onButton.setVisible(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///code to execute
                Globals.AllowBgMusic = false;
                //TODO
                Globals.StopAllSounds();
                AudioManager.instance.SetMusicActive(true);
                PlayerPrefs.SetInt("MusicState", 0);
                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            musicButton.offAction = () =>
            {
                musicButton.SetIsOn(true);
                //offButton.setVisible(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                ///code to execute
                Globals.AllowBgMusic = true;
                if (Globals.AllowBgMusic)
                {
                    //TODO
                    AudioManager.instance.SetMusicActive(false);
                    // AudioManager.instance.PlayMusic(Track.mainMenuMusic, false, 0.25f);
                    AudioManager.instance.PlayMusic(7009);
                    //mainMenuMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/mainMenuMusic.mp3", true, 0.25f * UserDefault::getInstance().getFloatForKey("MUSIC_VOLUME", 1.0f));
                }
                PlayerPrefs.SetInt("MusicState", 1);
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
                ///
            };

            if (PlayerPrefs.GetInt("MusicState") == 1)
            {
                musicButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.setVisible(false);
                //offButton.setEnabled(false);

            }
            else
            {
                musicButton.SetIsOn(false);
                //onButton.setVisible(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }
        }

        {//sfx On

            soundButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAME_FONT, 90);
                                                                                                        //Shared::fontToCustom(lbl);
                                                                                                        //allLabels.Add(lbl);

            soundButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAME_FONT, 90);
            //Shared::fontToCustom(lbl2);
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);

            //CustomButton* onButton;
            //CustomButton* offButton;
            //int buttonWidth = 65;
            //if (GameData.instance.getCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}


            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //_bg.addChild(onButton);
            //onButton.setPosition(Vec2(Musiclbl.getPosition().x + 300, Soundlbl.getPosition().y));




            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.setVisible(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //_bg.addChild(offButton);
            //offButton.setPosition(Vec2(Musiclbl.getPosition().x + 300, Soundlbl.getPosition().y));
            menuArray.Add(soundButton);
            soundButton.defaultAction = () =>
              {
                  soundButton.SetIsOn(false);
                  //onButton.setVisible(false);
                  //onButton.setEnabled(false);
                  //offButton.setVisible(true);
                  //offButton.onTouchUp();
                  //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                  //    offButton.setEnabled(true);
                  //}), NULL));


                  ///code to execute
                  PlayerPrefs.SetInt("SoundState", 0);
                  AudioManager.instance.SetSoundEffectsActive(true);
                  Globals.StopAllSounds();
                  if (Globals.AllowBgMusic)
                  {
                      //TODO
                    //   AudioManager.instance.PlayMusic(Track.mainMenuMusic, false, 0.25f);
                    AudioManager.instance.PlayMusic(7009);
                      //mainMenuMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/mainMenuMusic.mp3", true, 0.25f * UserDefault::getInstance().getFloatForKey("MUSIC_VOLUME", 1.0f));
                  }
                  Globals.AllowMusic = false;
                  AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                  //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                  ///
              };

            soundButton.offAction = () =>
              {

                  soundButton.SetIsOn(true);
                  //offButton.setVisible(false);
                  //offButton.setEnabled(false);

                  AudioManager.instance.SetSoundEffectsActive(false);
                  //onButton.setVisible(true);
                  //onButton.onTouchUp();
                  //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                  //    onButton.setEnabled(true);
                  //}), NULL));

                  //AllowMusic = true;
                  AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                  //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);


                  PlayerPrefs.SetInt("SoundState", 1);
                  ///
              };

            if (PlayerPrefs.GetInt("SoundState") == 1)
            {
                soundButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.setVisible(false);
                //offButton.setEnabled(false);
            }
            else
            {
                soundButton.SetIsOn(false);
                //    onButton.setVisible(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }
        }
    }

    void CreateOtherButtons()
    {
        resetProgressLabel.text = GameData.instance.GetMenuData(Globals.SETTINGS_MENU_MOBILE)["resetProgress"] as string;//, GAME_FONT, 90);

        //Shared::fontToCustom(resetProgressLabel);

        resetButton.defaultAction = () =>
        {
            if (this.isEnabled)
            {
                popUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.MAIN_MENU)["back"] as string,
                    " ALL PROGRESS WILL BE LOST,\n ARE YOU SURE YOU WANT TO RESET?", () =>
                {
                    //, " ALL PROGRESS WILL BE LOST,\n ARE YOU SURE YOU WANT TO RESET?", [=](Ref * sender){
                    Globals.canRegenerateHealth = true;
                    Globals.introComplete = false;
                    Globals.skipFTUX = false;
                    Globals.ftuxSidekicksSpawned = false;
                    Globals.specialAbilityUsedInTutorial = false;
                    Globals.specialAbilityTutorialExecuted = false;
                    Globals.tutorialOrbsDropState = false;
                    Globals.canActivateSpecialOnControllerTutorial = false;
                    //TODO
                    //GameData.instance.fileHandler.EmptyData();
                    GameData.instance.fileHandler.ReadData();
                    GameData.instance.fileHandler.SaveData();
                    //FileHandler::getInstance().saveData();
                    PlayerPrefs.DeleteAll();
                    PlayerPrefs.SetInt("firstLaunch", 1);

                    GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));
                    Globals.StopAllSounds();
                    SceneManager.LoadScene("Splash");
                    //Director::getInstance().replaceScene(IntroScene::createScene());
                    //SceneManager.LoadScene("Splash");
                });
                //Director::getInstance().getRunningScene().addChild(pp, INT_MAX);
            }
            float buttonWidth = 800;
            //if (GameData.instance.getCurrentLanguageCode() == "en" || GameData.instance.getCurrentLanguageCode() == "en-ca" || GameData.instance.getCurrentLanguageCode() == "en-gb")
            //{
            //    buttonWidth = 800;
            //}
            //else if (GameData.instance.getCurrentLanguageCode() == "fr" || GameData.instance.getCurrentLanguageCode() == "fr-ca")
            //{
            //    buttonWidth = 1300;

            //}
            //else if (GameData.instance.getCurrentLanguageCode() == "it")
            //{
            //    buttonWidth = 1150;

            //}
            //else if (GameData.instance.getCurrentLanguageCode() == "de")
            //{
            //    buttonWidth = 1100;

            //}
            //else
            //{
            //    buttonWidth = 1050;

            //}

            //resetButton.setContentSize(cocos2d::Size(buttonWidth, 170));
            //resetButton.addToButton(resetProgressLabel, false);
            //resetProgressLabel.setPositionY(resetProgressLabel.getPosition().y + resetProgressLabel.getPosition().y * 0.2f);

            //resetButton.setScale(0.45);
            //menuArray.pushBack(resetButton);
            //_bg.addChild(resetButton);
            //resetButton.setPosition(Vec2(400, 240));
        };
        //videosButton = NewButton::create("res/World/Explottens_RedBase.png", "res/World/Explottens_WhiteBase.png", NewButtonConsts::BasePath + "VideosIcon.json", GameData.instance.GetMenuData(Globals.MAP_MENU)["videosButton"] as string;//, NULL);
        {
            videosButton.defaultAction = () =>
                {
                    if (this.isEnabled)
                    {
                        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp,0.15f);

                        //videosButton.playAnimation();

                        videoPopUp.Init();

                }
                };
            buttonArray.Add(videosButton.gameObject);
        }


        {
            //NewButton* languageButton = NewButton::create("res/World/Explottens_RedBase.png", "res/World/Explottens_WhiteBase.png", NewButtonConsts::BasePath + "LanguagesIcon.json", GameData.instance.GetMenuData(Globals.MAP_MENU)["languagesButton"] as string;//, NULL);
            languageButton.defaultAction = () =>
            {


                if (isEnabled)
                {
                    //languageButton.playAnimation();

                    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

                    //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
                    languagePopUp.Init();
                    //UICustom::Popup* pop = UICustom::Popup::create(Size(700, 600), GameData.instance.GetMenuData(Globals.MAP_MENU)["languagesButton"] as string;//);
                    //Director::getInstance().getRunningScene().addChild(pop, INT_MAX);
                    //ui::ScrollView* mainScroll = ui::ScrollView::create();
                    //pop.getDisplayArea().addChild(mainScroll);
                    //mainScroll.setContentSize(Size(pop.getDisplayArea().getContentSize().width - 20, pop.getDisplayArea().getContentSize().height - 20));
                    //UILanguages* mc = UILanguages::create();
                    //mainScroll.addChild(mc);
                    //mainScroll.setInnerContainerSize(Size(pop.getDisplayArea().getContentSize().width, mc.getLabelArray().size() * 80));
                    //mainScroll.setVisible(false);
                    //mainScroll.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
                    //mainScroll.setPosition(Point(10, 10));
                    //mc.setScrollView(mainScroll);
                    //mc.setPosition(pop.getDisplayArea().getContentSize().width / 2, mainScroll.getInnerContainerSize().height / 2 + 160);
                    //mc.setVisible(false);
                    //mc.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
                    //CustomButton* customCloseButton = CustomButton::createWithCustomButton(SPRITES::CIRCLE, Color3B(171, 0, 0), [=](Ref * sender){

                    //    pop.exitCallback();
                    //});
                    //pop.addChild(customCloseButton);
                    //customCloseButton.setPosition(Vec2(pop.getContentSize().width / 2 + 350, pop.getContentSize().height / 2 + 300));
                    //customCloseButton.addToButton(Sprite::create("res/World/cross.png"), false);
                    //customCloseButton.setAllowColorChange(true);
                    //customCloseButton.setScale(0.6f);
                    //customCloseButton.setVisible(false);
                    //customCloseButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
                }
            };



            //bg.addChild(languageButton, 3);
            //        languageButton.setPosition(_winSize.width/2 - 240, _winSize.height/2 - 120);
            //languageButton.setVisible(false);
            //languageButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
            buttonArray.Add(languageButton.gameObject);

        }

        {
            //NewButton* supportButton = NewButton::create("res/World/Explottens_RedBase.png", "res/World/Explottens_WhiteBase.png", NewButtonConsts::BasePath + "SupportIcon.json", GameData.instance.GetMenuData(Globals.MAP_MENU)["supportButton"] as string;//, NULL);
            supportButton.defaultAction = () =>
            {

                if (isEnabled)
                {
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

                    //MacLicenses* mc = MacLicenses::create();
                    //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
                    //supportButton.playAnimation();
                    //UICustom::Popup* pop = UICustom::Popup::create(Size(800, 350), GameData.instance.GetMenuData(Globals.MAP_MENU)["supportButton"] as string;//);
                    //Director::getInstance().getRunningScene().addChild(pop, INT_MAX);
                    //pop.getDisplayArea().addChild(mc);
                    //mc.setPosition(pop.getDisplayArea().getContentSize() / 2);
                    //mc.setVisible(false);
                    //mc.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
                    //CustomButton* customCloseButton = CustomButton::createWithCustomButton(SPRITES::CIRCLE, Color3B(171, 0, 0), [=](Ref * sender){

                    //    pop.exitCallback();
                    //});
                    //pop.addChild(customCloseButton);
                    //customCloseButton.setPosition(Vec2(pop.getContentSize().width / 2 + 400, pop.getContentSize().height / 2 + 175));
                    //customCloseButton.addToButton(Sprite::create("res/World/cross.png"), false);
                    //customCloseButton.setAllowColorChange(true);
                    //customCloseButton.setScale(0.6f);
                    //customCloseButton.setVisible(false);
                    //customCloseButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
                }
            };


            //bg.addChild(supportButton, 3);
            //supportButton.setVisible(false);
            //supportButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
            buttonArray.Add(supportButton.gameObject);

        }

        //TODO All
        //        if (Globals.isJoystickConnected)
        //        {

        //            NewButton* controllerSettingsButton = NewButton::create("res/World/Explottens_RedBase.png", "res/World/Explottens_WhiteBase.png", NewButtonConsts::BasePath + "Controller.json", GameData.instance.GetMenuData(Globals.MAP_MENU)["controls"] as string;//, NULL);
        //            controllerSettingsButton.MainFunc =  [=](Ref * sender){
        //                if (_isEnabled)
        //                {
        //                    Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
        //                    controllerSettingsButton.playAnimation();

        //                    if (isJoystickConnected)
        //                    {

        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
        //                        ThirdPartyInterface::OpenControllerSettings();
        //#endif

        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_MAC || CC_TARGET_PLATFORM == CC_PLATFORM_TVOS)

        //                        ControllerSettingsMenu* csm = ControllerSettingsMenu::create();
        //                        controllerSetting = UserDefault::getInstance().getIntegerForKey("controllerSetting", 1);
        //                        csm.showSetting(ControllerSettingsMenu::SETTING_TYPE(controllerSetting));


        //                        UICustom::Popup* pop = UICustom::Popup::create(Size(1100, 650), GameData.instance.GetMenuData(Globals.MAP_MENU)["gameControllerButton"] as string;//);
        //                        Director::getInstance().getRunningScene().addChild(pop, INT_MAX);
        //                        pop.getDisplayArea().addChild(csm);
        //                        csm.setPosition(pop.getDisplayArea().getContentSize() / 2);
        //                        csm.setVisible(false);
        //                        csm.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));

        //                        CustomButton* customCloseButton = CustomButton::createWithCustomButton(SPRITES::CIRCLE, Color3B(171, 0, 0), [=](Ref * sender){
        //                            pop.exitCallback();
        //                        });
        //                        pop.addChild(customCloseButton);
        //                        customCloseButton.setPosition(Vec2(pop.getContentSize().width / 2 + 550, pop.getContentSize().height / 2 + 325));
        //                        customCloseButton.addToButton(Sprite::create("res/World/cross.png"), false);
        //                        customCloseButton.setAllowColorChange(true);
        //                        customCloseButton.setScale(0.6f);
        //                        customCloseButton.setVisible(false);
        //                        customCloseButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));


        //#endif
        //                    }
        //                    else
        //                    {
        //#if Desktop
        //                UICustom::Popup *popup = UICustom::Popup::createAsMessage(GameData.instance.GetMenuData(Globals.MAP_MENU)["gameControllerNotConnected"] as string;//, GameData.instance.GetMenuData(Globals.MAP_MENU)["connectGameController"] as string;//);

        //                Director::getInstance().getRunningScene().addChild(popup,INT_MAX);
        //                popup.setTag(986723);


        //#else
        //                        if (CC_TARGET_PLATFORM != CC_PLATFORM_TVOS)
        //                        {
        //                            UICustom::Popup* p = UICustom::Popup::create(Size(850, 550), GameData.instance.GetMenuData(Globals.MAP_MENU)["controls"] as string;//);
        //                            Director::getInstance().getRunningScene().addChild(p, INT_MAX);
        //                            p.setTag(986723);
        //                            MobileDpadSettings* mc = MobileDpadSettings::create();
        //                            p.getDisplayArea().addChild(mc);
        //                            mc.setPosition(p.getDisplayArea().getContentSize() / 2);


        //                            CustomButton* customCloseButton = CustomButton::createWithCustomButton(SPRITES::CIRCLE, Color3B(171, 0, 0), [=](Ref * sender){

        //                                p.exitCallback();
        //                            });
        //                            p.addChild(customCloseButton);
        //                            customCloseButton.setPosition(Vec2(p.getContentSize().width / 2 + 425, p.getContentSize().height / 2 + 275));
        //                            customCloseButton.addToButton(Sprite::create("res/World/cross.png"), false);
        //                            customCloseButton.setAllowColorChange(true);
        //                            customCloseButton.setScale(0.6f);
        //                            customCloseButton.setVisible(false);
        //                            customCloseButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        //                        }

        //#endif
        //                    }

        //                }
        //            };


        //            _bg.addChild(controllerSettingsButton, 3);
        //            controllerSettingsButton.setVisible(false);
        //            controllerSettingsButton.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        //            _buttonArray.pushBack(controllerSettingsButton);


        //        }

        //rays = SkeletonAnimation::createWithJsonFile("res/vfx/buttonRays.json", "res/vfx/buttonRays.atlas");
        //_bg.addChild(rays, 1);
        //rays.setAnimation(0, "idle", true);
        //rays.setScale(0.75f);
        //rays.setVisible(false);

        //for (auto node : _buttonArray)
        //{
        //    //Shared::rescale(node, 0.8f);
        //    node.setScale(0.8f);

        //}

        ////    Shared::alignItemsHorizontallyWithPadding(Point(-375,-125), 150, _buttonArray, false);
        //if (!Globals.isJoystickConnected)
        //{
        //    //
        //    //Shared::alignItemsHorizontallyWithPadding(Point(45, 120), 180, _buttonArray, false);

        //}
        //else
        //{
        //    //Shared::alignItemsHorizontallyWithPadding(Point(-45, 120), 180, _buttonArray, false);

        //}








    }
    void CreateKeyBoardAndControllerSupport()
    {

    }


}
