using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
using System;

public enum MK_States
{
    idle,
    State1,
    State2,
    State3,
    State4,
    Aim1,
    Aim2,
    finalState
}

public class TomasScratcher : Enemy
{
    #region CONSTANTS
    private const float ENEMYSCALE = 0.18f;
    #endregion

    #region VARIABLES
    private MK_States _currentState;
    private bool allowDamage = false;
    private bool isTurboDead = false;
    private bool isSparkyDead = false;
    private bool allowShooting = false;
    private float chaseSpeed = 0.0f;
    private int soundCounter = 0;
    private int enemyDirection = -1;
    #endregion

    #region REFERENCES
    Bone bone = null;
    Bullet bullet = null;
    
    [SerializeField] private BoundingBoxFollower boundingBox;
    private Bounds bounds;
    [SerializeField] Sprite bulletImage;
    [SerializeField] GameObject orbPrefab;
    [SerializeField] GameObject orbPrefabParent;
    [SerializeField] SkeletonAnimation blast;
    [SerializeField] SkeletonAnimation flameAnim;
    [SerializeField] SkeletonAnimation explosion;
    
    TomasMinnion _turbo = null; //TODO
    TomasMinnion _sparky = null;
    #endregion
    Sequence seq;
    // Start is called before the first frame update
    public override void Init()
    {
        if (initialized)
            return;

        base.Init();
        InitStats();
        isBoss = true;
        allowRelocate = false;
        scheduleUpdate = true;
        //bounds = boundingBox.CurrentCollider.bounds;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.mafiaIntro);

        //    Shared::playSound("res/Sounds/Bosses/boss5/mafiaIntro.mp3"); TODO
        //}), NULL));
        InitializeEnemyParameters();
        allowDamage = false;
        //Globals.LEFTBOUNDARY = Globals.RIGHTBOUNDARY - Globals.CocosToUnity(1800);

        DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
        {
            ChangeStatesCall(MK_States.Aim1);
            allowDamage = true;
        });
        
        enemySprite.state.Event += HandleSpineAnimation;
        Observer.DispatchCustomEvent("TURBO_DEAD");
        
        Observer.RegisterCustomEvent(gameObject, "TURBO_DEAD", () =>
        {
            isTurboDead = true;
        });
        Observer.DispatchCustomEvent("SPARKY_DEAD");
        
        Observer.RegisterCustomEvent(gameObject, "SPARKY_DEAD", () =>
        {
            isSparkyDead = true;
        });
    }

    private void InitializeEnemyParameters()
    {
        DOTween.Sequence().AppendInterval(0.005f).AppendCallback(() =>
        {
            enemySprite.timeScale = 8;
            enemySprite.state.SetAnimation(0, "entry", false);
        });
        
        enemySprite.state.AddAnimation(0, "idle", true);

        enemySprite.state.Data.SetMix("idle", "shootUp1", 0.3f);
        enemySprite.state.Data.SetMix("idle", "shootDown1", 0.3f);
        enemySprite.state.Data.SetMix("shootDown1", "idle", 0.3f);
        enemySprite.state.Data.SetMix("shootUp1", "idle", 0.3f);

        transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1500), Globals.CocosToUnity(500));
        
        _currentState = MK_States.idle;
    }

    private void HandleSpineAnimation(TrackEntry entry, Spine.Event spineEvent)
    {
        if(spineEvent.Data.Name == "shoot")
        {
            Shoot();
        }

        if(spineEvent.Data.Name == "jump")
        {
            Globals.LEFTBOUNDARY = Globals.bossPosition.x - Globals.CocosToUnity(2500);//3800 //5300
            Globals.RIGHTBOUNDARY = Globals.bossPosition.x + Globals.CocosToUnity(500);//2500 //2300
            Jump();
        }
    }

    private void ChangeStatesCall(MK_States state)
    {
        _currentState = state;
        if(_currentState == MK_States.Aim1)
        {
            DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
            {
                enemySprite.state.SetAnimation(0, "aimShoot", true);
            });
        }
    }

    private void Jump()
    {
        enemySprite.timeScale = 1;
        //Globals.zoomToBossForSec = 0.5f;
        //Globals.slowDownForBoss = true;
        //Globals.zoomValueOnBoss = Globals.CocosToUnity(350);
        transform.DOBlendableMoveBy(new Vector2(0, Globals.CocosToUnity(300)), 12f);
        //enemySprite->runAction(MoveBy::create(12, cocos2d::Point(0, 300)));
    }

    private void Shoot()
    {
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.setDamage(stats.bulletDamage);
        bullet.SetSpriteFrame(bulletImage);
        bullet.setRadiusEffectSquared(0.5f);
        bone = enemySprite.skeleton.FindBone("fire");
        bullet.transform.position = bone.GetWorldPosition(enemySprite.transform);
        bullet.duration = 4.0f;
        bullet.transform.SetRotation(bone.WorldRotationX-80);
        bullet.gameObject.SetActive(true);
        bullet.transform.localScale = Vector3.one * 2f;
        Vector2 dest = new Vector2(Globals.CocosToUnity(4000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation())*-1, Globals.CocosToUnity(4000)* Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation()));
        bullet.PlayBulletAnim(bullet.duration, dest);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);

        if (soundCounter % 2 == 0)
        {
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spikyShoot);
            //Shared::playSound("res/Sounds/Bosses/Boss1/spikyShoot.mp3"); TODO
        }

        soundCounter++;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (Vector2.Distance(P1,transform.position) < Globals.CocosToUnity(1600))
        {
            if (boundingBox.CurrentCollider)
            {
                return boundingBox.CurrentCollider.bounds.Contains(P1);
            }
            else
                return false;
        }
        else
        {
            return false;
        }
    }

    // Update is called once per frame
    void Update()
    {
        if(scheduleUpdate)
        {
            if (healthBar)
            {
                //healthBar->setPosition(enemySprite->getPosition().x, enemySprite->getPosition().y - 100);
            }

            //bounds = boundingBox.CurrentCollider.bounds;
            //spSkeletonBounds_update(bounds, enemySprite->getSkeleton(), true);

            if (isBoss)
            {
                bone = enemySprite.skeleton.FindBone("car");
                Globals.bossPosition = bone.GetWorldPosition(enemySprite.transform);
                //Globals.bossPosition.x = transform.position.x + bone.GetWorldPosition(enemySprite.transform.position.x);
                //Globals.bossPosition.y = transform.position.y + bone.WorldY;
            }

            if (_currentState == MK_States.Aim1 || _currentState == MK_States.Aim2 || _currentState == MK_States.State1 || _currentState == MK_States.State4)
            {
                bone = enemySprite.skeleton.FindBone("gunArm");
                bone.Rotation = -Globals.CalcAngle(player.transform.position, transform.position) + 180+16;
                bone = enemySprite.skeleton.FindBone("carMain");
            }

            if (_currentState == MK_States.State1)
            {
                //enemySprite->setPositionY(enemySprite->getPosition().y + (Player::getInstance()->getPosition().y - enemySprite->getPosition().y) * dt * chaseSpeed);
                transform.position = new Vector2(transform.position.x, transform.position.y + (player.transform.position.y - transform.position.y) * Time.deltaTime * chaseSpeed);
            }

            if (_currentState == MK_States.State4)
            {
                transform.position = new Vector2(transform.position.x, transform.position.y + (player.transform.position.y - transform.position.y) * Time.deltaTime * chaseSpeed);
            }

            if (_currentState == MK_States.State2)
            {
                transform.position = new Vector2(transform.position.x, transform.position.y + (Globals.CocosToUnity(700) - transform.position.y) * Time.deltaTime * chaseSpeed);
                //enemySprite->setPositionY(enemySprite->getPosition().y + (700 - enemySprite->getPosition().y) * dt * chaseSpeed);
            }

            if (_currentState == MK_States.State3 && GameSharedData.Instance.enemyList.Count == 1) //GameSharedData::getInstance()->g_enemyArray.size() == 1)
            {
                _currentState = MK_States.State4;
                enemySprite.skeleton.FindBone("root").ScaleX = 1.0f;
                transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(1300), 0), 2).SetEase(Ease.OutSine);
                enemySprite.state.SetAnimation(0, "changeState", false);

                for (int i = 0; i < 10; i++)
                {
                    enemySprite.state.AddAnimation(0, "changeState", false);
                }
                enemySprite.state.AddAnimation(0, "aimShoot", true);
                chaseSpeed = Mathf.Lerp(0, 1, 10);
                //chaseSpeed = 10f;
                //this->runAction(ActionFloat::create(10, 0, 1, [=](float val){
                //    chaseSpeed = val;
                //}));
                DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
                {
                    //InvokeRepeating(nameof(ShootExplosiveMissile), 0, 1.65f);
                    allowDamage = true;
                });
            }
        }
    }
    
    public override bool TakeHit(double damage)
    {
        if (!allowDamage)
        {
            return false;
        }
        if (healthBar)
        {
            stats.health = stats.health - damage;
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            healthBar.gameObject.SetActive(true);
            //DOTween.Sequence().AppendInterval(3).AppendCallback(()=>
            //{
               
            //    healthBar.gameObject.SetActive(false);
            //});

            enemySprite.GetComponent<Renderer>().material.DOKill();
            enemySprite.GetComponent<Renderer>().material.color = Color.red;
            enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
        }
        
        if (_currentState == MK_States.Aim1 && stats.health < stats.maxHealth.Value * 0.9f)
        {
            enemySprite.state.SetAnimation(0, "changeState", false);
            for (int i = 0; i < 10; i++)
            {
                enemySprite.state.AddAnimation(0, "changeState", false);
            }
            enemySprite.state.AddAnimation(0, "aimShoot", true);

            allowDamage = false;

            Observer.DispatchCustomEvent("ChangeBossState");
            //Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("ChangeBossState");

            _currentState = MK_States.idle;
            DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
            {
                ChangeStatesCall(MK_States.Aim2);
                {
                    TomasMinnion node = GameManager.instance.SpawnEnemy("TOMASMINION") as TomasMinnion;
                    _turbo = node;
                    
                    _turbo.nodeToFollow = transform.gameObject;
                    //_turbo.gameObject.SetActive(true);
                    _turbo.transform.SetWorldPositionX(Globals.RIGHTBOUNDARY + Globals.CocosToUnity(500));
                    _turbo.SetMinionType(MinionType.TURBO);
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                    _turbo.CycleShoot();
                    PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    Ping.Init(_turbo.transform, true);
                }

                {
                    TomasMinnion node = GameManager.instance.SpawnEnemy("TOMASMINION") as TomasMinnion;
                    _sparky = node;
                    
                    _sparky.nodeToFollow = transform.gameObject;
                    _sparky.transform.SetWorldPositionX(Globals.RIGHTBOUNDARY + Globals.CocosToUnity(500));
                    _sparky.SetMinionType(MinionType.SPARKY);
                    _sparky.CycleShoot();
                    _sparky.angleOffset = 120;
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                    PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    Ping.Init(_sparky.transform, true);
                }

            }).AppendInterval(3).AppendCallback(()=> { allowDamage = true; });
        }

        if (_currentState == MK_States.Aim2 && stats.health < stats.maxHealth.Value * 0.82f)
        {
            Observer.DispatchCustomEvent("ChangeBossState");

            chaseSpeed = Mathf.Lerp(0, 2, 15f);
            //chaseSpeed = 15;
            //this->runAction(ActionFloat::create(15, 0, 2, [=](float val){
            //    chaseSpeed = val;
            //}));
            _currentState = MK_States.State1;
        }
        if (_currentState == MK_States.State1 && stats.health < stats.maxHealth.Value * 0.65f)
        {
            if (_sparky)
            {
                _sparky.scheduleUpdate = false;
            }
            if (_turbo)
            {
                _turbo.scheduleUpdate = false;
            }
            
            Observer.DispatchCustomEvent("ChangeBossState");
            _currentState = MK_States.State2;
            
            //InvokeRepeating(nameof(ShootExplosiveMissile), 0, 1.5f);
            enemySprite.state.SetAnimation(0, "changeState", false);

            for (int i = 0; i < 5; i++)
            {
                enemySprite.state.AddAnimation(0, "changeState", false);
            }
            enemySprite.state.AddAnimation(0, "idle", true);
            allowDamage = false;

            DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
            {
                allowDamage = true;
            });
        }

        if (_currentState == MK_States.State2 && stats.health < stats.maxHealth.Value * 0.35f)
        {
            Observer.DispatchCustomEvent("ChangeBossState");
            //CancelInvoke(nameof(ShootExplosiveMissile));
            //unschedule(schedule_selector(TomasScratcher::shootExplosiveMissile)); todo
            _currentState = MK_States.State3;
            if (_sparky)
            {
                if (isSparkyDead == false)
                {
                    _sparky.SetNodeToFollow(null);
                }
            }
            if (_turbo)
            {
                if (isTurboDead == false)
                {
                    _turbo.SetNodeToFollow(null);
                }
            }
            enemySprite.state.SetAnimation(0, "changeState", false);
            for (int i = 0; i < 5; i++)
            {
                enemySprite.state.AddAnimation(0, "changeState", false);
            }
            enemySprite.state.AddAnimation(0, "idle", true);
            allowDamage = false;
            
            seq = DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
            {
                enemySprite.skeleton.FindBone("root").ScaleX = -1.0f;
                transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(1300), 0), 1.25f).SetEase(Ease.InSine).OnComplete(() =>
                {
                    _sparky.isCheckCollision = true;
                    _turbo.isCheckCollision = true;
                });

                if (_turbo)
                {
                    if (isTurboDead == false)
                    {
                        _turbo.CycleShoot();
                        _turbo.CreateHealthbars();
                    }
                }
            }).AppendInterval(1.25f).AppendCallback(()=>
                    {
                    if (_sparky)
                    {
                        if (isSparkyDead == false)
                        {
                            _sparky.CycleShoot();
                            _sparky.CreateHealthbars();
                        }
                    }
            });
        }

        if (stats.health < 0)
        {
            //GameSharedData.Instance.enemyList.Remove(this);
            if (isBoss)
            {
                Globals.isBossMode = false;
                scheduleUpdate = false;
                Globals.bossPosition = Vector2.zero;
            }
            _allowKillPoint = true;
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
            //base.Destroy();
            return true;
            Globals.ResetZoomValue();
            Globals.ResetBoundary();
        }
        return false;
    }

    private void ShootExplosiveMissile()
    {
        Vector3 aimPos = player.transform.position - new Vector3(Globals.CocosToUnity(400), Globals.CocosToUnity(400),0)+new Vector3(UnityEngine.Random.value* Globals.CocosToUnity(800), UnityEngine.Random.value * Globals.CocosToUnity(800),0);
        
        GameObject temp = Instantiate(orbPrefab);
        temp.transform.position = aimPos;
        temp.SetActive(true);

        DOTween.Sequence().Append(temp.transform.DOBlendableScaleBy(new Vector2(5, 5), 1f)).AppendInterval(0.2f).AppendCallback(() =>
        {
            HomingMissile missile = null;
            bool didFindmissile = false;
            foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
            {
                if (!m.isInUse)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindmissile = true;
                    break;
                }
            }

            if (!didFindmissile)
            {
                return;
            }

            missile.isDestructable = false;
            missile.scheduleUpdate = false;
            missile.SetDamage(150);
            missile.transform.position = transform.position;
            missile.gameObject.SetActive(true);

            missile.transform.SetRotation(-Globals.CalcAngle(aimPos, transform.position) + 180);
            missile.duration = Vector2.Distance(transform.position, aimPos) / Globals.CocosToUnity(1000);
            missile.SetSpeed(20);
            missile.transform.DOMove(aimPos, missile.duration).OnComplete(() =>
            {
                if (missile.gameObject.activeSelf)
                {
                    missile.RemoveMissile();
                }
            });
            //missile.CreateWithHomingDuration(missile.duration);
            GameSharedData.Instance.enemyMissilesInUse.Add(missile);
            //missile.Init();

            missile.endFunc += () =>
            {
                float radius = 6;
                blast.gameObject.SetActive(true);
                if(blast != null && blast.state != null)
                {
                    blast.state.SetAnimation(0, "rocketBlast", true);
                }
                DOTween.Sequence().Append(blast.transform.DOScale(radius, 0.2f)).AppendInterval(0.1f).AppendCallback(() =>
                {
                    blast.transform.SetScale(0);
                });
                //blast.transform.DOScale(radius, 0.2f).OnComplete(() =>
                //{
                //    blast.transform.SetScale(0);
                //});

                blast.transform.position = new Vector2(missile.transform.position.x, missile.transform.position.y - Globals.CocosToUnity(30));
                float newVal = radius * Globals.CocosToUnity(100);
                if (Vector2.Distance(missile.transform.position, player.transform.position) < newVal
                && player.canHit)
                {
                    player.GotHit(150);
                }
            };
        }).AppendInterval(Vector2.Distance(aimPos, transform.position) / Globals.CocosToUnity(1000) - 0.25f).AppendCallback(() =>
        {
            temp.GetComponent<SpriteRenderer>().DOFade(0, 0.3f).OnComplete(() =>
            {
                Destroy(temp);
            });
        }); 
    //});
    //    DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
    //    {
    //        temp.transform.DOBlendableScaleBy(new Vector2(5, 5), 1f).OnComplete(() =>
    //        {
                
    //    }).AppendInterval(Vector2.Distance(aimPos, transform.position) / Globals.CocosToUnity(1000) - 0.25f).AppendCallback(()=>
    //    {
    //        temp.GetComponent<SpriteRenderer>().DOFade(0, 0.3f).OnComplete(() =>
    //        {
    //            Destroy(temp);
    //        });
    //    });
        //DOTween.Sequence().AppendInterval((Vector2.Distance(aimPos,transform.position)/1000)-0.25f).AppendCallback(()=>
        //{

        //});
    }
    
    //public override void Destroy()
    //{
    //    if (Globals.gameType == GameType.Survival)
    //    {
    //        Globals.LEFTBOUNDARY = -90000000;
    //        Globals.RIGHTBOUNDARY = 90000000;
    //    }

    //    healthBar.gameObject.SetActive(false);
    //    Globals.RIGHTBOUNDARY += Globals.CocosToUnity(1000);
        
    //    enemySprite.GetComponent<Renderer>().material.color = Color.white;
    //    flameAnim.transform.position = new Vector2(transform.position.x + Globals.CocosToUnity(40), transform.position.y + Globals.CocosToUnity(10));
    //    flameAnim.transform.localScale = new Vector2(5, 5);
    //    transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(300), -Globals.CocosToUnity(600)), 25);
    //    flameAnim.gameObject.SetActive(true);
    //    flameAnim.state.SetAnimation(0, "idle", true);
    //    enemySprite.state.Event += HandleExplosionSpineEvent;
    //    GameManager.instance.ShakeCamera(Globals.CocosToUnity(100), (int)Globals.CocosToUnity(8));
    //    allowDamage = false;
    //    scheduleUpdate = false;
    //    enemySprite.StopAllCoroutines();
    //    CancelInvoke();
    //    DOTween.Kill(enemySprite);

    //    //for (int i=0;i<3;i++)
    //    //{
    //    //    DOTween.Sequence().AppendInterval(i * 0.35f).AppendCallback(() =>
    //    //    {
    //    //        CreateExplosion();
    //    //    });
    //    //}
    //    enemySprite.state.SetAnimation(0, "death", false);
    //}

    private void CreateExplosion()
    {
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x+Globals.CocosToUnity(25), transform.position.y+ Globals.CocosToUnity(25)), false, 1, 2f, 0);
    }

    private void HandleExplosionSpineEvent(TrackEntry trackEntry,Spine.Event spineEvent)
    {
        if(spineEvent.Data.Name == "explosion")
        {
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeShip, new Vector2(transform.position.x, transform.position.y+ Globals.CocosToUnity(200)), false, 1, 1.5f, 1.5f);
        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        isBoss = true;
        int bossNumber = 5;

        if (GameManager.instance.missionManager.missionType == "Boss")
        { 
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }
        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }
}
