using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class TutorialDialogue : MonoBehaviour
{
    public enum TutorialDialogueState
    {
        none,
        Movement_Start,
        LockPlayerMovement,
        Aim_Assist_Rotation,
        Health_Regen,
        SpawnSidekicks,
        Start_Fight,
        Activate_Special_Ability_Mode,
        Enter_Berserk_Mode,
        Building_Destroyed,
        Pre_Berserk_Mode,
        Aim_Assist_Settings

    };

    [HideInInspector] public TutorialDialogueState currentState = TutorialDialogueState.none;
    [SerializeField] private TutorialDialoguePopup tdp;

    [HideInInspector] public bool canCallAttention = true;


    string schedulerId;

    public void Start()
    {
        schedulerId = "TDS" + GetInstanceID();
    }

    public void HideDialogue()
    {
        if (!Globals.popupDestroyedOnSkip)
        {
            currentState = TutorialDialogueState.none;
            tdp.HidePopup();
        }

    }
    public void ShowDialogueWithString(string s)
    {
        TutorialDialogueState state;

        tdp.ShowPopup(s);
        state = currentState;
        DOTween.Sequence().SetId(schedulerId).AppendInterval(10).AppendCallback(()=>{

            if (state == currentState && currentState != TutorialDialogueState.Aim_Assist_Rotation)
            {
                if (canCallAttention)
                {
                    tdp.CallAttention();
                }
            }

        }).Play();
    }

    public void ShowDialogueWithState(TutorialDialogueState state)
    {

        if (Globals.isGameInTutorialState)
        {

            currentState = state;


            if (state == TutorialDialogueState.Movement_Start)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["blueStick"] as string);
                if (Globals.isJoystickConnected)
                {
                    ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerMove"] as string);
                }

            }
            else if (state == TutorialDialogueState.LockPlayerMovement)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["orangeStick"] as string);
                if (Globals.isJoystickConnected)
                {
                    ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerShoot"] as string);
                }
            }


            else if (state == TutorialDialogueState.Aim_Assist_Rotation)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["autoAim"] as string);

            }

            else if (state == TutorialDialogueState.Health_Regen)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["health"] as string);

            }
            else if (state == TutorialDialogueState.SpawnSidekicks)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["sidekicks"] as string);

            }
            else if (state == TutorialDialogueState.Start_Fight)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["startFight"] as string);
            }
            else if (state == TutorialDialogueState.Activate_Special_Ability_Mode)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["specialAbility"] as string);

            }
            else if (state == TutorialDialogueState.Enter_Berserk_Mode)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["collectOrbs"] as string);

            }
            else if (state == TutorialDialogueState.Building_Destroyed)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["congrats"] as string);

            }
            else if (state == TutorialDialogueState.Pre_Berserk_Mode)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["berserkMode"] as string);

            }
            else if (state == TutorialDialogueState.Aim_Assist_Settings)
            {
                ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["autoAimSettings"] as string);

            }

        }
    }
}
