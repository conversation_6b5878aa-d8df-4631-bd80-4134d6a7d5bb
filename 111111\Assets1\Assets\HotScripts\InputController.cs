using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using Spine.Unity;
using UnityEngine.UI;
using DG.Tweening;
public class InputController : MonoBehaviour
{
    public static InputController instance;

    [SerializeField] private GameObject movementDpad;
    [SerializeField] private GameObject mobileControlsContainer;
    [SerializeField] private GameObject leftJoystickObj;
    [SerializeField] private GameObject rightJoystickObj;
    [SerializeField] public SkeletonGraphic leftDpadTutorialHand;
    [SerializeField] public SkeletonGraphic dashTutorialHand;
    [SerializeField] public SkeletonGraphic rightDpadTutorialHand1;
    [SerializeField] public GameObject enemyTarget;
    [SerializeField] public Image dashButtonGlow;
    [SerializeField] private GameObject specialAbilityButtonMobile;
    [SerializeField] private GameObject specialAbilityButtonMobileGlow;
    [SerializeField] private GameObject specialAbilityButtonDesktop;
    [SerializeField] private Image specialAbilityButtonConsole;
    [SerializeField] public Image targetDirectcionSprite;
    private Vector2 acceleration = Vector2.zero;
    private bool previousThrustState = false;
    private bool activateGravity = false;
    private bool targetShoot = false;
    [HideInInspector] public bool dashUnlocked = false;
    private float playerRotation;

    [SerializeField] private Button dashButton;
    [SerializeField] private Button shootButton;


    int movementTutorialTime = 0;
    bool rotationEnabled = true;
    bool movementEnabled = true;
    bool firstTouch = false;
    Vector2 startPointMovement;
    Vector2 startPointRotation;
    SkeletonAnimation cursor;
    float ax;
    float bx;
    //    int enemyPosInArray;
    bool isTutorialMissionPingVisible = false;
    int movementTime = 0;
    bool isShooting = false;
    private bool isJoystickEnabled = true;

    [HideInInspector] public string tweenId;
    [HideInInspector] public string schedulerId;

    private void Awake()
    {
        instance = this;
    }

    public void Start()
    {
        CreateAllEvents();
        tweenId = "IC" + GetInstanceID();
        schedulerId = "ICS" + GetInstanceID();
        if (GameData.instance.fileHandler.missionsCompleted > 0)
        {
            dashUnlocked = true;
            if (GameData.instance.fileHandler.missionsCompleted > 1)
            {
                GameManager.instance.player.playerMovement.allowDash = true;
#if UNITY_ANDROID || UNITY_IOS
                dashButton.gameObject.SetActive(true);
#endif
                //TODO Mobile and Console Inputs
                //dashButton->setVisible(true);
                //dashButton->setEnabled(true);
            }
            else
            {
#if UNITY_ANDROID || UNITY_IOS
                dashButton.gameObject.SetActive(false);
#endif
                //TODO Mobile and Console Inputs
                //dashButton->setVisible(false);
                //dashButton->setEnabled(false);
            }
        }
        else
        {
            GameManager.instance.player.playerMovement.allowDash = false;
#if UNITY_ANDROID || UNITY_IOS
            dashButton.gameObject.SetActive(false);
#endif
            //dashButton->setVisible(false);
            //dashButton->setEnabled(false);
        }
    }

    public void ResetMovementDpad()
    {

    }

    public void DeActivateControls()
    {

    }

    public void ActivateControls()
    {

    }

    public void DisableControlsInstant()
    {

    }

    public void RemoveMovementJoystickHandTutorial()
    {

    }

    public void EnableMovementJoystickHandTutorial()
    {

    }

    public void DisableMovementJoystickHandTutorial()
    {

    }

    public void EnableFireButtonHandtutorial()
    {

    }

    public void DisableFireButtonHandTutorial()
    {

    }

    private void Init()
    {

    }

    private void Update()
    {

    }

    private void RotationUpdate()
    {

    }

    private void MovementUpdate()
    {

    }


    public void SetSpecialAbility(bool val)
    {
        if (GameData.instance.fileHandler.RearGun != (int)AllELEMENTS.noRearGun)
        {
#if UNITY_ANDROID || UNITY_IOS

            if (Globals.isJoystickConnected)
            {
                specialAbilityButtonConsole.gameObject.SetActive(val);
            }
            else
            {
                specialAbilityButtonMobile.gameObject.SetActive(val);
            }


#else
            if (Globals.isJoystickConnected)
            {
                specialAbilityButtonConsole.gameObject.SetActive(val);
            }
            else
            {
                specialAbilityButtonDesktop.gameObject.SetActive(val);
            }
#endif
            //_specialBar->setVisible(true);
            //specialBg->setVisible(true);
        }
        else if (GameData.instance.fileHandler.currentMission == 0)
        {
#if UNITY_ANDROID ||UNITY_IOS

            if (Globals.isJoystickConnected)
            {
                specialAbilityButtonConsole.gameObject.SetActive(val);
            }
            else
            {
                specialAbilityButtonMobile.gameObject.SetActive(val);
            }


#else
            if (Globals.isJoystickConnected)
            {
                specialAbilityButtonConsole.gameObject.SetActive(val);
            }
            else
            {
                specialAbilityButtonDesktop.gameObject.SetActive(val);
            }
#endif
        }
    }

    public void SetDash(bool val)
    {
        dashButton.gameObject.SetActive(val);
    }

    public void SetShoot(bool val)
    {
        SetShootButton(val);
        rightJoystickObj.SetActive(!val);
    }
    public void SetShootButton(bool val)
    {
        shootButton.gameObject.SetActive(val);
        if (Globals.autoShootMode) shootButton.gameObject.SetActive(false);
    }

    public void SetSpecialGlow(bool val)
    {
        specialAbilityButtonMobileGlow.SetActive(val);
    }

    private bool CheckPlayerBoundaries()
    {
        return true;
    }

    private bool CanDash()
    {
        return true;
    }

    public void HideMobileControlsHud()
    {
        DOTween.Kill(tweenId);
        leftJoystickObj.transform.SetScale(1);
        DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).AppendCallback(() =>
        {
            leftJoystickObj.transform.SetScale(0);
            leftJoystickObj.SetActive(false);
            mobileControlsContainer.SetActive(false);
        }).Play();
        if (Globals.isAssistMode)
        {
            shootButton.transform.SetScale(1);
            DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.zero, 0.5f)).AppendCallback(() =>
            {
                shootButton.transform.SetScale(0);
                shootButton.gameObject.SetActive(false);
            }).Play();
        }
        else
        {
            shootButton.transform.SetScale(1);
            DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).AppendCallback(() =>
            {
                rightJoystickObj.transform.SetScale(0);
                rightJoystickObj.SetActive(false);
            }).Play();
        }
        if (GameData.instance.fileHandler.currentMission > 1)
        {
            dashButton.transform.SetScale(1);
            DOTween.Sequence().SetId(tweenId).Append(dashButton.transform.DOScale(Vector2.zero, 0.5f)).AppendCallback(() =>
            {
                dashButton.transform.SetScale(0);
                dashButton.gameObject.SetActive(false);
            }).Play();
        }
        if (GameManager.instance.player.secondaryWeapon.IsEquipped())
        {
            specialAbilityButtonMobile.transform.SetScale(1);
            DOTween.Sequence().SetId(tweenId).Append(specialAbilityButtonMobile.transform.DOScale(Vector2.zero, 0.5f)).AppendCallback(() =>
            {
                specialAbilityButtonMobile.transform.SetScale(0);
                specialAbilityButtonMobile.SetActive(false);
            }).Play();
        }
    }

    public void ShowMobileControlsHud()
    {
        // if (!Globals.isJoystickConnected)
        // {
#if UNITY_IOS || UNITY_ANDROID
            return;
            DOTween.Kill(tweenId);
            leftJoystickObj.transform.SetScale(0);
            leftJoystickObj.SetActive(true);
            mobileControlsContainer.SetActive(true);
            DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.one, 0.5f)).AppendCallback(()=>
            {
                leftJoystickObj.transform.SetScale(1);
            }).Play();

            if (Globals.isAssistMode)
            {
                shootButton.transform.SetScale(0);
                shootButton.gameObject.SetActive(true);
                if(Globals.autoShootMode) shootButton.gameObject.SetActive(false);
                DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.one, 0.5f)).AppendCallback(() =>
                {
                    shootButton.transform.SetScale(1);
                }).Play();
                isShooting = false;
            }
            else
            {
                // rightJoystickObj.transform.SetScale(0);
                // rightJoystickObj.SetActive(true);
                // DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.one, 0.5f)).AppendCallback(() =>
                // {
                //     rightJoystickObj.transform.SetScale(1);
                // }).Play();
            }
            if (GameData.instance.fileHandler.currentMission > 1)
            {
                dashButton.gameObject.SetActive(true);
                dashButton.gameObject.transform.SetScale(0);
                DOTween.Sequence().SetId(tweenId).Append(dashButton.transform.DOScale(Vector2.one, 0.5f)).AppendCallback(() =>
                {
                    dashButton.transform.SetScale(1);
                }).Play();
            }
            if (GameData.instance.fileHandler.currentMission > 0)
            {
                if (GameManager.instance.player.secondaryWeapon.IsEquipped())
                {
                    specialAbilityButtonMobile.SetActive(true);
                    specialAbilityButtonMobile.transform.SetScale(0);
                    DOTween.Sequence().SetId(tweenId).Append(specialAbilityButtonMobile.transform.DOScale(Vector2.one, 0.5f)).AppendCallback(() =>
                    {
                        specialAbilityButtonMobile.transform.SetScale(1);
                    }).Play();
                }
                else
                {
                    specialAbilityButtonMobile.SetActive(false);
                }
            }
#endif
        // }
    }


    private void CreateAllEvents()
    {
        


        

//         {

//             Observer.RegisterCustomEvent(gameObject, "show_dpads_when_joystick_connected", () =>
//             {

// #if UNITY_IOS || UNITY_ANDROID

//                 leftJoystickObj.transform.SetScale(0);
//                 leftJoystickObj.SetActive(true);
//                 DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();


//                 if (Globals.isAssistMode)
//                 {
//                     shootButton.transform.SetScale(0);
//                     shootButton.gameObject.SetActive(true);
//                     if(Globals.autoShootMode) shootButton.gameObject.SetActive(false);
//                     DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();
//                 }
//                 else
//                 {
//                     rightJoystickObj.transform.SetScale(0);
//                     rightJoystickObj.SetActive(true);
//                     DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();
//                 }
//                 dashButton.gameObject.transform.SetScale(0);
//                 dashButton.gameObject.SetActive(true);
//                 DOTween.Sequence().SetId(tweenId).Append(dashButton.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();
// #endif
//             });

//         }




        {

            Observer.RegisterCustomEvent(gameObject, "disable_dash", () =>
            {
                dashButton.gameObject.SetActive(false);
            });


        }




        {
            Observer.RegisterCustomEvent(gameObject, "enable_dash", () =>
            {
                dashButton.gameObject.SetActive(true);
            });

        }


        {

            // Observer.RegisterCustomEvent(gameObject, "hide_dpads", () =>
            // {

            //     DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).SetUpdate(true).AppendCallback(() =>
            //     {
            //         leftJoystickObj.transform.SetScale(1);
            //         leftJoystickObj.SetActive(false);
            //     }).Play();
            //     if (Globals.isAssistMode)
            //     {
            //         shootButton.transform.SetScale(1);
            //         DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.zero, 0.5f)).SetUpdate(true).Play();
            //     }
            //     else
            //     {

            //         rightJoystickObj.transform.SetScale(1);
            //         DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).SetUpdate(true).AppendCallback(() =>
            //         {
            //             rightJoystickObj.SetActive(false);
            //         }).Play();
            //     }
            // });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "show_movement_dpad", () =>
            {
#if UNITY_ANDROID || UNITY_IOS
                leftJoystickObj.transform.SetScale(0);
                leftJoystickObj.SetActive(true);
                DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();

                if (!Globals.isAssistMode)
                {
                    rightJoystickObj.transform.SetScale(0);
                    rightJoystickObj.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.one, 0.5f)).SetUpdate(true).Play();
                }
#endif
            });


        }


        // {
        //     Observer.RegisterCustomEvent(gameObject, "hide_movement_dpad", () =>
        //     {
        //         leftJoystickObj.transform.SetScale(1);
        //         DOTween.Sequence().SetId(tweenId).Append(leftJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).SetUpdate(true).AppendCallback(() =>
        //         {
        //             leftJoystickObj.SetActive(false);
        //         }).Play();
        //     });

        // }

        



        {
            Observer.RegisterCustomEvent(gameObject, "disable_rotation", () =>
            {
                rotationEnabled = false;
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "enable_rotation", () =>
            {
                rotationEnabled = true;
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "disable_movement", () =>
            {
                movementEnabled = false;
                //TODO Check for desktop controls
                //acceleration.x = playerAcceleration.x;
                //acceleration.y = playerAcceleration.y;
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "enable_movement", () =>
            {
                movementEnabled = true;
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "DeActivate_Mobile_Assist_Controls", () =>
            {
                DeActivateControls();
            });

        }


        {
            Observer.RegisterCustomEvent(gameObject, "Activate_Mobile_Assist_Controls", () =>
            {
                ActivateControls();
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "show_shoot_button", () =>
            {
                if (Globals.isAssistMode)
                {
                    shootButton.transform.SetScale(0);
                    shootButton.gameObject.SetActive(true);
                    if (Globals.autoShootMode) shootButton.gameObject.SetActive(false);
                    DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.one, 0.5f)).Play();
                }
                else
                {
                    rightJoystickObj.transform.SetScale(0);
                    rightJoystickObj.gameObject.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.one, 0.5f)).Play();
                }
                isShooting = false;
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "hide_shoot_button", () =>
            {
                DOTween.Sequence().SetId(tweenId).Append(shootButton.transform.DOScale(Vector2.zero, 0.5f)).Play();
                DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).Play();
            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "enable_shooting", () =>
            {
                shootButton.interactable = true;
                Globals.allowShoot = true;


            });

        }
        {
            Observer.RegisterCustomEvent(gameObject, "disable_shooting", () =>
            {
                shootButton.interactable = false;
                Globals.allowShoot = false;
            });

        }
        //----------------------------------------------------------Controller----------------------------------------------------------------------//
        {
            //enemyPosInArray = -1;
            //this.scheduleUpdate();
            //SpriteFrameCache::getInstance().addSpriteFramesWithFile("res/vfx/AimTarget.plist");
            {
                Observer.RegisterCustomEvent(gameObject, "Activate_Controller_Controls", () =>
                {
                    ActivateControls();
                });

            }
            {
                Observer.RegisterCustomEvent(gameObject, "DeActivate_Controller_Controls", () =>
                {
                    DeActivateControls();
                });

            }


        }
        //-------------------------------------------------------------------------Mobile-----------------------------------------------------------------------------//
        {










            {
                Observer.RegisterCustomEvent(gameObject, "show_rotation_dpad", () =>
                {

                    rightJoystickObj.transform.SetScale(0);
                    rightJoystickObj.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.one, 0.5f)).Play();
                    dashButton.gameObject.transform.SetScale(0);
                    dashButton.gameObject.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).Append(dashButton.transform.DOScale(Vector2.one, 0.5f)).Play();


                });

            }


            {
                Observer.RegisterCustomEvent(gameObject, "hide_rotation_dpad", () =>
                {
                    rightJoystickObj.transform.SetScale(0);
                    rightJoystickObj.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).Append(rightJoystickObj.transform.DOScale(Vector2.zero, 0.5f)).Play();



                });

            }

            {
                Observer.RegisterCustomEvent(gameObject, "DeActivate_Mobile_Touch_Controls", () =>
                {
                    this.DeActivateControls();
                });

            }

            {
                Observer.RegisterCustomEvent(gameObject, "Activate_Mobile_Touch_Controls", () =>
                {
                    this.ActivateControls();
                });

            }

        }
        //---------------------------------------------------------------------------Desktop------------------------------------------------------------------------------------------//
        //{

        //    _winSize = Director::getInstance().getVisibleSize();

        //    _cursor = SkeletonAnimation::createWithJsonFile("res/Keys/crosshair.json", "res/Keys/crosshair.atlas");
        //    _cursor.setAnimation(0, "idle", true);
        //    this.addChild(_cursor, 10);
        //    _cursor.SetActive(false);
        //    _cursor.setCameraMask(GAMECAMERA);


        //    _cursor2 = SkeletonAnimation::createWithJsonFile("res/Keys/crosshair.json", "res/Keys/crosshair.atlas");
        //    _cursor2.setAnimation(0, "idle", true);
        //    this.addChild(_cursor2, 10);

        //    _cursor2.setCameraMask(HUDCAMERA);
        //    _cursor2.setScale(0.25f);
        //    Shared::rescale(_cursor2, 0.25f);
        //    _cursor2.SetActive(false);

        //    {
        //        Observer.RegisterCustomEvent(gameObject,"show_cursor", ()=>{

        //            _cursor2.SetActive(true);

        //        });

        //    }
        //    {
        //        Observer.RegisterCustomEvent(gameObject,"hide_cursor", ()=>{

        //            _cursor2.SetActive(false);

        //        });

        //    }

        //    {
        //        Observer.RegisterCustomEvent(gameObject,"DeActivate_Mobile_Touch_Controls", ()=>{
        //            _cursor.SetActive(false);
        //            _cursor2.SetActive(false);
        //        });

        //    }

        //    auto mouseListener = EventListenerMouse::create();
        //    mouseListener.onMouseMove = CC_CALLBACK_1(DesktopControls::onMouseMove, this);
        //    mouseListener.onMouseDown = CC_CALLBACK_1(DesktopControls::onMouseDown, this);
        //    mouseListener.onMouseUp = CC_CALLBACK_1(DesktopControls::onMouseUp, this);

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
        //    if (FileHandler::getInstance().missionsCompleted > 0)
        //    {
        //        dashUnlocked = true;
        //    }

        //    auto keylistener = EventListenerKeyboard::create();
        //    keylistener.onKeyPressed = CC_CALLBACK_2(DesktopControls::onKeyPressed, this);
        //    keylistener.onKeyReleased = CC_CALLBACK_2(DesktopControls::onKeyReleased, this);
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

        //    this.scheduleUpdate();


        //    float minVal = MIN(Director::getInstance().getWinSize().width / 1334, Director::getInstance().getWinSize().height / 750);

        //    camSpace *= minVal;


        //    debug{

        //        auto keylistener = EventListenerKeyboard::create();
        //        keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){

        //            if (keyCode == EventKeyboard::KeyCode::KEY_H)
        //            {
        //                this.SetActive(false);
        //                premanentVisibility = true;
        //            }
        //        };
        //        _eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

        //    }

        //}


    }

    private void createTutorialItems()
    {

    }



    public GameObject GetMovementDpad()
    {
        return movementDpad;
    }

    public void SetJoyStickEnabled(bool val)
    {
        isJoystickEnabled = val;
    }

    private void ControllerInputCallBacks()
    {

        //    listener = EventListenerTouchAllAtOnce::create();

        //    listener.onTouchesBegan = CC_CALLBACK_2(ControllerControls::onTouchesBegan, this);

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);

        //    auto _listener = EventListenerController::create();


        //    _listener.onKeyDown = [=](cocos2d::Controller * controller, int keyCode, cocos2d::Event * event)
        //    {

        //        this.ResumeControls();


        //        if (keyCode == Controller::Key::BUTTON_DPAD_UP)
        //        {
        //            moveUp = true;
        //        }
        //        if (keyCode == Controller::Key::BUTTON_DPAD_DOWN)
        //        {
        //            moveDown = true;
        //        }
        //        if (keyCode == Controller::Key::BUTTON_DPAD_LEFT)
        //        {
        //            moveLeft = true;

        //        }
        //        if (keyCode == Controller::Key::BUTTON_DPAD_RIGHT)
        //        {
        //            moveRight = true;
        //        }
        //        if (keyCode == FileHandler::getInstance().gamePadCodeShoot || keyCode == FileHandler::getInstance().gamePadCodeShoot2)
        //        {

        //            allowShoot = true;
        //            GETPLAYERCONTROLLER.setPlaneShoot();
        //            GETPLAYERCONTROLLER.getFrontGun().setShootMode(true);
        //            isShooting = true;

        //        }
        //        if (keyCode == FileHandler::getInstance().gamePadCodeDash || keyCode == FileHandler::getInstance().gamePadCodeDash2)
        //        {
        //            ExecuteDash();

        //        }
        //        if (keyCode == FileHandler::getInstance().gamePadCodeSpecialAbility)
        //        {

        //            GETPLAYERCONTROLLER.getRearGun().ActivateSpecialAbility();
        //            if (isFTUETutorial && !specialAbilityUsedInTutorial && canActivateSpecialOnControllerTutorial)
        //            {
        //                canActivateSpecialOnControllerTutorial = false;
        //                (static_cast<GameController*>(GETGAMECONTROLLER).mobileAssistiveTutorialObj).changeState(MobileAssistiveControlsTutorial::AssistModeTutorialState::Exit_Special_Ability_Mode);



        //            }


        //        }

        //    };

        //    _listener.onKeyUp = [=](cocos2d::Controller * controller, int keyCode, cocos2d::Event *event)
        //    {
        //        this.ResumeControls();


        //        if (keyCode == Controller::Key::BUTTON_DPAD_UP)
        //        {

        //            moveUp = false;
        //        }

        //        if (keyCode == Controller::Key::BUTTON_DPAD_DOWN)
        //        {
        //            moveDown = false;

        //        }

        //        if (keyCode == Controller::Key::BUTTON_DPAD_LEFT)
        //        {
        //            moveLeft = false;
        //        }

        //        if (keyCode == Controller::Key::BUTTON_DPAD_RIGHT)
        //        {
        //            moveRight = false;
        //        }

        //        if (keyCode == FileHandler::getInstance().gamePadCodeShoot || keyCode == FileHandler::getInstance().gamePadCodeShoot2)
        //        {
        //            isShooting = false;
        //            allowShoot = false;
        //            GETPLAYERCONTROLLER.setPlaneIdle();
        //            GETPLAYERCONTROLLER.getFrontGun().setShootMode(false);

        //        }
        //        if (keyCode == Controller::Key::BUTTON_A)
        //        {
        //            // this will be replaced

        //            EventKeyboard event(EventKeyboard::KeyCode::KEY_ENTER, false);
        //            auto dispatcher = Director::getInstance().getEventDispatcher();
        //            dispatcher.dispatchEvent(&event);
        //            {
        //                EventKeyboard event((EventKeyboard::KeyCode)FileHandler::getInstance().keyCodeSpecial, false);
        //                auto dispatcher = Director::getInstance().getEventDispatcher();
        //                dispatcher.dispatchEvent(&event);
        //            }
        //        }

        //        if (keyCode == Controller::Key::BUTTON_B)
        //        {
        //            // this will be replaced
        //            EventKeyboard event(EventKeyboard::KeyCode::KEY_ESCAPE, false);
        //            auto dispatcher = Director::getInstance().getEventDispatcher();
        //            dispatcher.dispatchEvent(&event);
        //            // skip button
        //            Director::getInstance().getEventDispatcher().dispatchCustomEvent("ZOOM_OUT_BOSS");
        //            if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS || CC_TARGET_PLATFORM == CC_PLATFORM_TVOS)
        //            {
        //                if (FileHandler::getInstance().currentMission == 0)
        //                {
        //                    if (isGameInTutorialState)
        //                    {
        //                        static_cast<GameController*>(GETPLAYERCONTROLLER.getParent()).mobileAssistiveTutorialObj.changeState(MobileAssistiveControlsTutorial::AssistModeTutorialState::SkipTutorial);

        //                    }
        //                    else
        //                    {
        //                        static_cast<GameController*>(GETPLAYERCONTROLLER.getParent()).SkipFTUX(nullptr);
        //                    }
        //                }
        //                if (FileHandler::getInstance().currentMission != 0)
        //                {

        //                    if (gameType != GameType::Arena)
        //                    {
        //                        if (!static_cast<GameController*>(GETPLAYERCONTROLLER.getParent())._hudLayer.isPause)
        //                            static_cast<GameController*>(GETPLAYERCONTROLLER.getParent())._hudLayer.pauseButtonCallback();
        //                    }

        //                }

        //            }
        //            if (CC_TARGET_PLATFORM == CC_PLATFORM_MAC)

        //            {
        //                if (FileHandler::getInstance().currentMission == 0)
        //                {
        //                    static_cast<GameController*>(GETPLAYERCONTROLLER.getParent()).SkipFTUX(nullptr);

        //                }
        //                if (FileHandler::getInstance().currentMission != 0)
        //                {
        //                    static_cast<GameController*>(GETPLAYERCONTROLLER.getParent())._hudLayer.pauseButtonCallback();
        //                }

        //            }

        //        }
        //        if (keyCode == Controller::Key::BUTTON_X)
        //        {
        //            // this will be replaced
        //            EventKeyboard event(EventKeyboard::KeyCode::KEY_TAB, false);
        //            auto dispatcher = Director::getInstance().getEventDispatcher();
        //            dispatcher.dispatchEvent(&event);
        //        }
        //    };
        //    _listener.onConnected = [=](Controller * controller, Event * event){

        //        if (Director::getInstance().getRunningScene().getChildByTag(POPUP_TAG_GAME_CONTROLLER))
        //        {
        //            Director::getInstance().getRunningScene().getChildByTag(POPUP_TAG_GAME_CONTROLLER).removeFromParentAndCleanup(true);
        //        }

        //        Globals.isJoystickConnected = true;
        //        char ch[256];
        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)

        //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("DeActivate_Mobile_Touch_Controls");
        //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("Hide_SpecialAbility_Hud");
        //#endif


        //        this.ActivateControls();
        //        //               Director::getInstance().getEventDispatcher().dispatchCustomEvent("joystick_enabled");

        //        sprintf(ch, "GAME CONTROLLER CONNECTED\n NAME: %s", controller.getDeviceName().c_str());
        //        UICustom::Popup* popup = UICustom::Popup::createAsMessage("GAME CONTROLLER", ch);
        //        Director::getInstance().getRunningScene().addChild(popup, INT_MAX, POPUP_TAG_GAME_CONTROLLER);

        //    };


        //    //bind disconnect event call function
        //    _listener.onDisconnected = [=](Controller * controller, Event * event){

        //        if (Director::getInstance().getRunningScene().getChildByTag(POPUP_TAG_GAME_CONTROLLER))
        //        {
        //            Director::getInstance().getRunningScene().getChildByTag(POPUP_TAG_GAME_CONTROLLER).removeFromParentAndCleanup(true);
        //        }
        //        Globals.isJoystickConnected = false;


        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)

        //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("Show_SpecialAbility_Hud");

        //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("Activate_Mobile_Touch_Controls");

        //#endif
        //        this.DeActivateControls();

        //        // add event here to disable controller controls

        //        //               Director::getInstance().getEventDispatcher().dispatchCustomEvent("joystick_disabled");

        //        char ch[256];
        //        sprintf(ch, "GAME CONTROLLER DISCONNECTED\n NAME: %s", controller.getDeviceName().c_str());
        //        UICustom::Popup* popup = UICustom::Popup::createAsMessage("GAME CONTROLLER", ch);
        //        Director::getInstance().getRunningScene().addChild(popup, INT_MAX, POPUP_TAG_GAME_CONTROLLER);
        //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("isJoystickDisconnected");
        //    };



        //    _listener.onAxisEvent = [=](cocos2d::Controller * controller, int keyCode, cocos2d::Event * event){
        //        this.ResumeControls();

        //        const auto&keyStatus = controller.getKeyStatus(keyCode);

        //        cocos2d::Point newDirection;


        //        const float speed = 10;

        //        if (keyCode == 1000)//x for left stick
        //        {
        //            newDirection.x = keyStatus.value;
        //            _acceleration.x = newDirection.x * speed;
        //            _acceleration.x = clampf(_acceleration.x, -SPEEDLIMIT / 1.25f, SPEEDLIMIT / 1.25f);
        //            usingDpad = true;
        //            wantedAccelerationX = _acceleration.x;
        //            if (isAssistMode)
        //            {
        //                _playerRotationAxis.x = keyStatus.value * 0.5;

        //            }

        //        }
        //        if (keyCode == 1001)//y for left stick
        //        {

        //            newDirection.y = keyStatus.value;
        //            _acceleration.y = -newDirection.y * speed;
        //            _acceleration.y = clampf(_acceleration.y, -SPEEDLIMIT / 1.25f, SPEEDLIMIT / 1.25f);
        //            usingDpad = true;
        //            wantedAccelerationY = _acceleration.y;
        //            if (isAssistMode)
        //            {
        //                _playerRotationAxis.y = keyStatus.value * 0.5;

        //            }

        //        }

        //        if (keyCode == 1002)
        //        {
        //            if (!isAssistMode)
        //            {
        //                _playerRotationAxis.x = keyStatus.value * 0.5;
        //            }



        //        }
        //        if (keyCode == 1003)
        //        {
        //            if (!isAssistMode)
        //            {
        //                _playerRotationAxis.y = keyStatus.value * 0.5;
        //            }

        //        }




        //    };
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(_listener, this);
        //#if (CC_TARGET_PLATFORM != CC_PLATFORM_WIN32 && CC_TARGET_PLATFORM != CC_PLATFORM_WINRT)

        //            Controller::startDiscoveryController();
        //#endif


        //    enemyTarget = Sprite::createWithSpriteFrameName("Target1.png");

        //    //enemyTarget = Sprite::create("res/RangeIndicators/Explottens_UI_Aim_Target.png");
        //    GETGAMECONTROLLER.addChild(enemyTarget);
        //    enemyTarget.setScale(1.5f);
        //    //    enemyTarget.setOpacity(100);

        //    enemyTarget.runAction(Shared::createAnimation("Target%d.png", 1, 20, true, 0.035f));
        //    enemyTarget.SetActive(false);
        //    enemyTarget.setCameraMask(GAMECAMERA);

        //}
    }
}


