using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using System;
public class MissionManager : MonoBehaviour
{
    [SerializeField] PlayerController player;
    [SerializeField] TimerHUD tHud;

    const string MissionTypeStages = "MissionTypeStages";
    private PList MissionMap;
    private Vector2 _boundary;
    private int _powerUp = 0;
    private int _fillerEnemy = 0;

    public string missionType;
    public string missionText;
    public int missionBossNumber;
    public int rewardInCoins = 10;
    public int rewardInXp = 10;
    public int isUnlockSideKick = 0;
    public int stage = 1;
    public float rewardMultiplier = 0.1f;
    //public Label* missionPing;
    public int _backgroundType = 1;
    public int totalPointsRequired;
    public int totalPoints;
    public int autoKillPoints;
    public bool boundary = false;
    public Vector2 Location;
    bool isMissionFailed = false, updateBoundary = false;
    [HideInInspector] public int placementCounter = 0;
    int _boundaryFollowDistance = 0;

    public void Init()
    {
        updateBoundary = false;
        placementCounter = 0;
        StartNewMission();
    }

    void StartNewMission()
    {
        MissionMap = GameData.instance.GetMissions();

        string ch = "Mission" + GameData.instance.fileHandler.currentMission.ToString();

        //if (Globals.gameType == GameType.Survival)
        //{
        //    ch = "Mission100";
        //}

        missionType = (MissionMap[ch] as PList)["Type"] as string;
        missionText = (GameData.instance.GetTextData(Globals.MISSION_DATA)[ch] as PList)["Text"] as string;
        totalPointsRequired = Convert.ToInt32((MissionMap[ch] as PList)["Points"]);
        rewardInCoins = Convert.ToInt32(((MissionMap[ch] as PList)["Rewards"] as PList)["Coins"]);
        rewardInXp = Convert.ToInt32(((MissionMap[ch] as PList)["Rewards"] as PList)["Xp"]);
        isUnlockSideKick = Convert.ToInt32(((MissionMap[ch] as PList)["Rewards"] as PList)["SideKick"]);
        _boundary.x = Convert.ToSingle(((MissionMap[ch] as PList)["boundary"] as PList)["x"]);
        _boundary.x = Globals.CocosToUnity(_boundary.x);
        _boundary.y = Convert.ToSingle(((MissionMap[ch] as PList)["boundary"] as PList)["y"]);
        _boundary.y = Globals.CocosToUnity(_boundary.y);
        stage = 1;
        _powerUp = Convert.ToInt32((MissionMap[ch] as PList)[Globals.POWERUP]);
        _fillerEnemy = Convert.ToInt32((MissionMap[ch] as PList)[Globals.FILLER_ENEMY]);
        _backgroundType = Convert.ToInt32((MissionMap[ch] as PList)["Background"]);
        if (Globals.gameType == GameType.Survival)
        {
            if((MissionMap[ch] as PList).ContainsKey("rewardMultiplier"))
            {

                rewardMultiplier = Convert.ToSingle((MissionMap[ch] as PList)["rewardMultiplier"]);
            }
            else
            {
                rewardMultiplier = 1;
            }
        }

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            totalPointsRequired = 1;
            missionText = "SAVE THE CIVILIANS";
            missionType = "MissionTypeLaserTower";
            _boundary = new Vector2(Globals.CocosToUnity(1000), Globals.CocosToUnity(10000));
            //_boundary.x = Globals.CocosToUnity(1000);
            //_boundary.y = Globals.CocosToUnity(10000);
        }

        if (((MissionMap[ch] as PList)["boundary"] as PList).ContainsKey("z"))
        {
            Globals.UPPERBOUNDARY = Convert.ToSingle(((MissionMap[ch] as PList)["boundary"] as PList)["z"]);
            Globals.UPPERBOUNDARY = Globals.CocosToUnity(Globals.UPPERBOUNDARY);
        }
        else
        {
            Globals.UPPERBOUNDARY = 11.687f;
        }

        if (GameData.instance.fileHandler.missionsCompleted + 1 > GameData.instance.fileHandler.currentMission)
        {
            //        MissionManager::getInstance().rewardInXp = MissionManager::getInstance().rewardInXp * 0.5f ;
            //        MissionManager::getInstance().rewardInCoins = MissionManager::getInstance().rewardInCoins * 0.5f;
        }

        Globals.LEFTBOUNDARY = GetBoundary().x;
        Globals.RIGHTBOUNDARY = GetBoundary().y;
        placementCounter = 0;

        if (Globals.LEFTBOUNDARY > Globals.CocosToUnity(-300))
        {
            Globals.LEFTBOUNDARY = Globals.CocosToUnity(-90000000);
        }
        if (Globals.RIGHTBOUNDARY < Globals.CocosToUnity(300))
        {
            Globals.RIGHTBOUNDARY = Globals.CocosToUnity(90000000);
        }

        totalPoints = 0;
        Location = Vector2.zero;
        Globals.isMissionComplete = false;
        isMissionFailed = false;
        Globals.crateOnBoard = false;

        if (missionType == Globals.MissionTypeBoss)
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            int missionBossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = missionBossNumber;
            // missionBossNumber = System.Convert.ToInt32((MissionMap[ch] as PList)["Boss Number"]);
        }
    }

    Vector2 GetBoundary()
    {
        return _boundary;
    }

    void BoundaryUpdate()
    {
        if (Globals.RIGHTBOUNDARY - Globals.LEFTBOUNDARY < (Globals.zoomValueWhileGame * 4.0f))
        {
            _boundaryFollowDistance = 0;
            return;
        }
        if (Globals.LEFTBOUNDARY < GameManager.instance.player.transform.position.x - _boundaryFollowDistance)
        {
            Globals.LEFTBOUNDARY = Globals.LEFTBOUNDARY + (-Globals.LEFTBOUNDARY + (GameManager.instance.player.transform.position.x - _boundaryFollowDistance)) * Time.deltaTime * 10.0f;
        }
    }

    
    public List<Enemy> PickMission(EnemyFactory _eFactory, string missionStr)
    {
        List<Enemy> missionArray = new List<Enemy>();
        if (Globals.gameType == GameType.Training)
        {
            if (missionStr == MissionTypeStages)
            {
                string stageNumber = "Stage" + stage.ToString();

                List<Enemy> stageMissionArray = PickMission(_eFactory, (_eFactory._sInfo[stageNumber].Mission));
                Globals.RIGHTBOUNDARY = Globals.RIGHTBOUNDARY + Globals.CocosToUnity(Convert.ToSingle(_eFactory._sInfo[stageNumber].SpawnDistance)) + Globals.CocosToUnity(Convert.ToSingle(_eFactory._sInfo[stageNumber].BoundaryDistance));
                if (_eFactory._sInfo[stageNumber].AllowBoundaryFollow != -1 )
                {
                    _boundaryFollowDistance = _eFactory._sInfo[stageNumber].AllowBoundaryFollow;

                    if (_boundaryFollowDistance == 0)
                    {
                        updateBoundary = false;
                    }
                    else
                    {
                        updateBoundary = true;
                    }
                }
                if (_eFactory._sInfo[stageNumber].DestroyAll)
                {
                    Globals.allDamage = 200000;
                }
                _eFactory.SetEnabled(false);
                _eFactory.Reset();
                if(_eFactory._sInfo[stageNumber].EnemyInfoID != -1)
                {
                    _eFactory._eInfo.Clear();
                    _eFactory.SetAllEnemyInfoDataByID(_eFactory._sInfo[stageNumber].EnemyInfoID);
                }
                //_eFactory._eInfo = (_eFactory._sInfo[stageNumber] as PList)["EnemyInfo"] as PList;
                _eFactory.SetEnabled(true);
                if (stageMissionArray.Count > 0)
                {
                    Enemy enemy = stageMissionArray[0];
                    enemy.allowRelocate = false;
                    enemy.transform.position = new Vector3(
                        player.transform.position.x + Globals.CocosToUnity(Convert.ToSingle(_eFactory._sInfo[stageNumber].SpawnDistance)),
                            enemy.transform.position.y,
                            enemy.transform.position.z);
                    Globals.RIGHTBOUNDARY = enemy.transform.position.x + Globals.CocosToUnity(Convert.ToSingle((_eFactory._sInfo[stageNumber].BoundaryDistance)));

                    enemy.additionalOnDestroy += ()=>{
                        AddPoint();
                        stage++;
                        if (totalPoints < totalPointsRequired)
                        {
                            PickMission(_eFactory, MissionTypeStages);
                        }
                    };
                    stageMissionArray.Clear();
                }
            }

            if (missionStr == Globals.MissionTypeEnemyBuilding)
            {
                ControlTower tower = GameManager.instance.InstantiatePrefab("ControlTower").GetComponent<ControlTower>();
                tower.Init();
                //tower.stats.health = 1000;
                //tower.stats.maxHealth = 1000;
                DynamicZoom dz = DynamicZoom.Create(tower.transform, 1.75f,
                    500, Globals.CocosToUnity(1000));
                dz.SetAllowMissionPing(true);
                missionArray.Add(tower);

                //tower.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeEnemyBuilding);
                if (tower.monsterData != null)
                {
                    tower.stats.health = tower.stats.maxHealth.Value = tower.monsterData.Hp;
                    tower.stats.bulletDamage = tower.monsterData.PhysicsAttack;
                    tower.stats.missileDamage = tower.monsterData.Skill;
                    tower.stats.speed = Globals.UnityValueTransform(tower.monsterData.MoveSpeed);
                }
                if (!Globals.isTutorial)
                {
                    tower.SetEnemyDifficulty();
                }
            }
            if (missionStr == Globals.MissionTypeElectricTower)
            {
                ElectricTower et = GameManager.instance.SpawnEnemy("ELECTRICTOWER",null,true).GetComponent<ElectricTower>();
                et.CreateAsType1();
                DynamicZoom dz = DynamicZoom.Create(et.enemySprite.transform, 2.0f, Globals.CocosToUnity(500.0f), Globals.CocosToUnity(2200.0f));
                dz.SetAllowMissionPing(true);
                missionArray.Add(et);

            }
            if (missionStr == Globals.MissionTypeElectricTower2)
            {
                ElectricTower et = GameManager.instance.SpawnEnemy("ELECTRICTOWER", null, true).GetComponent<ElectricTower>();
                et.CreateAsType2();
                DynamicZoom dz = DynamicZoom.Create(et.enemySprite.transform, 2.0f, Globals.CocosToUnity(500.0f), Globals.CocosToUnity(2200.0f));
                dz.SetAllowMissionPing(true);
                missionArray.Add(et);
            }
            if (missionStr == Globals.MissionTypeStoneTower)
            {
                StoneTower st = GameManager.instance.SpawnEnemy("STONETOWER",null,true).GetComponent<StoneTower>();
                st.StoneTowerType(1);
                st.Init();
                DynamicZoom dz = DynamicZoom.Create(st.enemySprite.transform, 2.0f, Globals.CocosToUnity(500.0f), Globals.CocosToUnity(2200.0f));
                dz.SetAllowMissionPing(true);
                missionArray.Add(st);
                //StoneTower* tower = StoneTower::CreateAsType1();
                //this.addChild(tower);
                //DynamicZoom* dz = DynamicZoom::create(tower.enemySprite, 2.0f, 500.0f, 2200.0f);
                //tower.addChild(dz);
                //dz.setAllowMissionPing(true);
                //missionArray.pushBack(tower);


            }
            if (missionStr == Globals.MissionTypeStoneTower2)
            {
                StoneTower st = GameManager.instance.SpawnEnemy("STONETOWER", null, true).GetComponent<StoneTower>();
                st.StoneTowerType(2);
                st.Init();
                DynamicZoom dz = DynamicZoom.Create(st.enemySprite.transform, 2.0f, Globals.CocosToUnity(500.0f), Globals.CocosToUnity(2200.0f));
                dz.SetAllowMissionPing(true);
                missionArray.Add(st);
            }


            if (missionStr == Globals.MissionTypeCivilian)
            {
                for (int i = 0; i < totalPointsRequired; i++)
                {
                    Civilian civ = GameManager.instance.SpawnEnemy("CIVILIAN").GetComponent<Civilian>();
#if !UNITY_STANDALONE
                    if (Globals.isTutorial)
                    {
                        civ.EnablePlayerPing();
                        civ.stats.health = 10;
                        civ.stats.maxHealth.Value = 10;
                    }
#endif

                    //civ.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeCivilian);
                    if (civ.monsterData != null)
                    {
                        civ.stats.health = civ.stats.maxHealth.Value = civ.monsterData.Hp;
                        if (!Globals.isTutorial)
                        {
                            civ.SetEnemyDifficulty(0.65f, 1.75f);
                        }
                    }

                }
            }

            if (missionStr == Globals.MissionTypeSaveBuilding)
            {

                Tesla tesla = GameManager.instance.InstantiatePrefab("TESLA").GetComponent<Tesla>();
                tesla.Create();
                FatCat fc = GameManager.instance.SpawnEnemy("FATCAT").GetComponent<FatCat>();
                fc.AddTesla(tesla);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
                missionArray.Add(fc);
                //fc.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeSaveBuilding);
                if (fc.monsterData != null)
                {
                    fc.stats.health = fc.stats.maxHealth.Value = fc.monsterData.Hp;
                    fc.stats.bulletDamage = fc.monsterData.PhysicsAttack;
                    fc.stats.missileDamage = fc.monsterData.Skill;
                    fc.stats.speed = Globals.UnityValueTransform(fc.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        fc.SetEnemyDifficulty();
                    }
                }
            }


            if (missionStr == Globals.MissionTypeSaveBuildingFromBalloon)
            {
                Tesla tesla = GameManager.instance.InstantiatePrefab("TESLA").GetComponent<Tesla>();
                tesla.Create();
                //BalloonEnemy be = GameManager.instance.SpawnEnemy("BALLOONENEMY").GetComponent<BalloonEnemy>();
                //print("spawned");
                //missionArray.Add(be);
                //Tesla* node = Tesla::create();
                //this.addChild(node);
                DynamicZoom dz = DynamicZoom.Create(tesla.transform, 2.0f, Globals.CocosToUnity(400), Globals.CocosToUnity(2500));
                //node.addChild(dz);
                
                var enemy = GameManager.instance.SpawnEnemy("BALLOON");
                var b = enemy as Balloon;
                b.AddTesla(tesla);
                missionArray.Add(b);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
                //enemy.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeSaveBuildingFromBalloon);
                if (enemy.monsterData != null)
                {
                    enemy.stats.health = enemy.stats.maxHealth.Value = enemy.monsterData.Hp;
                    enemy.stats.bulletDamage = enemy.monsterData.PhysicsAttack;
                    enemy.stats.missileDamage = enemy.monsterData.Skill;
                    enemy.stats.speed = Globals.UnityValueTransform(enemy.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        enemy.SetEnemyDifficulty();
                    }
                }
            }

            if (missionStr == Globals.MissionTypeSaveSideKick)
            {
                CagedSideKick cs = GameManager.instance.SpawnEnemy("CAGEDSIDEKICK").GetComponent<CagedSideKick>();
                missionArray.Add(cs);
            }
            if (missionStr == Globals.MissionTypeEnemyBase)
            {

                EnemyBase eb = GameManager.instance.SpawnEnemy("ENEMYBASE").GetComponent<EnemyBase>();
                missionArray.Add(eb);
                //eb.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeEnemyBase);
                if (eb.monsterData != null)
                {
                    eb.stats.health = eb.stats.maxHealth.Value = eb.monsterData.Hp;
                    eb.stats.bulletDamage = eb.monsterData.PhysicsAttack;
                    eb.stats.missileDamage = eb.monsterData.Skill;
                    eb.stats.speed = Globals.UnityValueTransform(eb.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        eb.SetEnemyDifficulty();
                    }
                }
            }

            if (missionStr == Globals.MissionTypeEnemyLaserBase)
            {

                LaserBase lb = GameManager.instance.SpawnEnemy("LASERBASE").GetComponent<LaserBase>();
                missionArray.Add(lb);
                DynamicZoom dz = DynamicZoom.Create(lb.enemySprite.transform, 2.0f, Globals.CocosToUnity(400), Globals.CocosToUnity(2500));
                //lb.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeEnemyLaserBase);
                if (lb.monsterData != null)
                {
                    lb.stats.health = lb.stats.maxHealth.Value = lb.monsterData.Hp;
                    lb.stats.bulletDamage = lb.monsterData.PhysicsAttack;
                    lb.stats.missileDamage = lb.monsterData.Skill;
                    lb.stats.speed = Globals.UnityValueTransform(lb.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        lb.SetEnemyDifficulty();
                    }
                }
            }

            if (missionStr == Globals.MissionTypeKillBalloons)
            {

                BalloonEnemy balloonEnemy = GameManager.instance.SpawnEnemy("BALLOONENEMY").GetComponent<BalloonEnemy>();
                DynamicZoom dz = DynamicZoom.Create(balloonEnemy.enemySprite.transform, 2.0f, Globals.CocosToUnity(400), Globals.CocosToUnity(1200));
                missionArray.Add(balloonEnemy);
                //balloonEnemy.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeKillBalloons);
                if (balloonEnemy.monsterData != null)
                {
                    balloonEnemy.stats.health = balloonEnemy.stats.maxHealth.Value = balloonEnemy.monsterData.Hp;
                    balloonEnemy.stats.bulletDamage = balloonEnemy.monsterData.PhysicsAttack;
                    balloonEnemy.stats.missileDamage = balloonEnemy.monsterData.Skill;
                    balloonEnemy.stats.speed = Globals.UnityValueTransform(balloonEnemy.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        balloonEnemy.SetEnemyDifficulty();
                    }
                }

            }

            if (missionStr == Globals.MissionTypeLaserTower)
            {

                LaserTower lt = GameManager.instance.SpawnEnemy("LASERTOWER").GetComponent<LaserTower>();
                DynamicZoom dz = DynamicZoom.Create(lt.enemySprite.transform, 2.0f, Globals.CocosToUnity(400), Globals.CocosToUnity(1700));


                missionArray.Add(lt);

                if (Globals.isTutorial)
                {
                    lt.stats.health *= 0.2f;
                    lt.stats.maxHealth.Value = lt.stats.health;
                }
                else
                {
                    //lt.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeLaserTower);
                    if (lt.monsterData != null)
                    {
                        lt.stats.maxHealth.Value = lt.stats.health = lt.monsterData.Hp;
                        lt.stats.bulletDamage = lt.monsterData.PhysicsAttack;
                        lt.stats.missileDamage = lt.monsterData.Skill;
                        lt.stats.speed = Globals.UnityValueTransform(lt.monsterData.MoveSpeed);
                        if (!Globals.isTutorial)
                        {
                            lt.SetEnemyDifficulty();
                        }
                    }
                }


            }
            if (missionStr == Globals.MissionTypeBulletTower)
            {

                BulletTower bt =  GameManager.instance.SpawnEnemy("BULLETTOWER").GetComponent<BulletTower>();
                DynamicZoom dz = DynamicZoom.Create(bt.enemySprite.transform, 2.0f, Globals.CocosToUnity(400), Globals.CocosToUnity(1700));
                missionArray.Add(bt);
                //bt.monsterData = MonsterNewScheme.Instance.GetItem(Globals.MissionTypeBulletTower);
                if (bt.monsterData != null)
                {
                    bt.stats.maxHealth.Value = bt.stats.health = bt.monsterData.Hp;
                    bt.stats.bulletDamage = bt.monsterData.PhysicsAttack;
                    bt.stats.missileDamage = bt.monsterData.Skill;
                    bt.stats.speed = Globals.UnityValueTransform(bt.monsterData.MoveSpeed);
                    if (!Globals.isTutorial)
                    {
                        bt.SetEnemyDifficulty();
                    }
                }

            }
            if (missionStr == Globals.MissionTypeSurvive)
            {
                DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() => {
                    //int pointsRequired = totalPointsRequired;
                    //if (Globals.gameModeType == GamePlayMode.Easy)
                    //{
                    //    pointsRequired = (int)((float)pointsRequired * 0.4f);


                    //}
                    //else if (Globals.gameModeType == GamePlayMode.Hard)
                    //{
                    //    pointsRequired = (int)((float)pointsRequired * 1.35f);

                    //}
                    //totalPointsRequired = pointsRequired;
                }).Play();
                {
                    Turret turret = GameManager.instance.SpawnEnemy("TURRET").GetComponent<Turret>();
                    turret.Init();
                    turret.transform.SetWorldPositionX(Globals.CocosToUnity(4000 - 1100));
                    turret.transform.SetWorldPositionY(-1);
                    turret.Disable();
                    missionArray.Add(turret);
                }
                {
                    Turret turret = GameManager.instance.SpawnEnemy("TURRET").GetComponent<Turret>();
                    turret.Init();
                    turret.transform.SetWorldPositionX(Globals.CocosToUnity(4000 + 1100));
                    turret.transform.SetWorldPositionY(-1);
                    turret.Disable();
                    missionArray.Add(turret);


                }


                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(850));
                PlayerPing ping1;
                PlayerPing ping2;

                {
                    LaserBoundary lb = GameManager.instance.SpawnEnemy("LASERBOUNDRY").GetComponent<LaserBoundary>();
                    lb.Init();
                    lb.transform.SetWorldPositionX(Globals.CocosToUnity(4000 - 1400));
                    lb.transform.SetWorldPositionY(-2);
                    ping1 = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    ping1.Init(lb.transform, true);
                }

                {
                    LaserBoundary lb = GameManager.instance.SpawnEnemy("LASERBOUNDRY").GetComponent<LaserBoundary>();
                    lb.Init();
                    lb.transform.SetWorldPositionX(Globals.CocosToUnity(4000 + 1400));
                    lb.transform.SetWorldPositionY(-2);
                    ping2 = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    ping2.Init(lb.transform, false);
                }


                LocationEvent lcEvent = LocationEvent.Create(new Vector2(Globals.CocosToUnity(4000), Globals.CocosToUnity(500)), true, () =>
                 {
                     Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1000));
                     DialoguePopup.CreateForSurvival();
                     ping1.SetPing(false);
                     ping2.SetPing(false);
                     //    Merlin* merlin = Merlin::create();
                     //    this.addChild(merlin);
                     //    merlin.inGame = true;

                     //    DialoguePopup

                     //merlin.TypeText(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.INGAME_MESSAGES)["Mission18"] as string;
                     if (_eFactory)
                     {
                         _eFactory.SetEnabled(false);
                     }

                     tHud.CreateWithTime(totalPointsRequired);

                     Observer.RegisterCustomEvent(gameObject, "start_timer_investigate_signal", () =>
                      {
                          if (_eFactory)
                          {
                              _eFactory.SetEnabled(true);
                              tHud.StartTimer();
                          }

                      });

                 });
                lcEvent.AddEvent(Globals.ACTIVE_LASER_EVENT);
                lcEvent.AddEvent(Globals.ACTIVATE_TURRET_EVENT);

                DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                {
                    if (_eFactory)
                    {
                        _eFactory.SetEnabled(false);
                    }

                }).Play();


            }

            if (missionStr == Globals.MissionTypeSaveZapper)
            {

                float distanceX = Globals.CocosToUnity(4000);
                    CreateTurret(new Vector2(distanceX - (Globals.CocosToUnity(1100)), Globals.CocosToUnity(1400)));
                    CreateTurret(new Vector2(distanceX - (Globals.CocosToUnity(1100)), Globals.CocosToUnity(400)));
                CreateTurret(new Vector2(distanceX + (Globals.CocosToUnity(1100)), Globals.CocosToUnity(1400)));
                CreateTurret(new Vector2(distanceX + (Globals.CocosToUnity(1100)), Globals.CocosToUnity(400)));

                void CreateTurret(Vector2 pos)
                {

                    FlyingTurret turret = GameManager.instance.SpawnEnemy("FLYINGTURRETS").GetComponent<FlyingTurret>();
                    turret.Init();
                    turret.allowFollow = false;
                    turret.SetPointToMove(pos);
                    turret.Disable();
                    missionArray.Add(turret);
                    if (Globals.gameModeType == GamePlayMode.Easy)
                    {
                        turret.stats.maxHealth.Value *= 0.3f;
                        turret.stats.health = turret.stats.maxHealth.Value;
                    }
                }

                
                    LaserBoundary lb = GameManager.instance.SpawnEnemy("LASERBOUNDRY").GetComponent<LaserBoundary>();
                    lb.Init();
                    lb.transform.SetWorldPositionX(Globals.CocosToUnity(4000) - Globals.CocosToUnity(1500));

                    LaserBoundary lb2 = GameManager.instance.SpawnEnemy("LASERBOUNDRY").GetComponent<LaserBoundary>();
                    lb2.Init();
                    lb2.transform.SetWorldPositionX(Globals.CocosToUnity(4000) + Globals.CocosToUnity(1500));


                CagedZapper cz = GameManager.instance.InstantiatePrefab("CAGEDZAPPER").GetComponent<CagedZapper>();
                cz.Init();
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(700));


                PlayerPing ping1;
                PlayerPing ping2;
                ping1 = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                ping1.Init(transform, true);


                ping2 = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                ping2.Init(transform, false);

                LocationEvent lcEvent = LocationEvent.Create(new Vector2(Globals.CocosToUnity(4000), Globals.CocosToUnity(500)), true,()=>{
                    Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1000));
                    ping2.SetPing(false);
                    ping1.SetPing(false);
                });
                lcEvent.gameObject.transform.parent = this.transform;
                lcEvent.DistanceToActivate = Globals.CocosToUnity(1000);
                lcEvent.AddEvent(Globals.ACTIVE_LASER_EVENT);
                lcEvent.AddEvent(Globals.ACTIVATE_TURRET_EVENT);

            }
        }


        {
            Observer.RegisterCustomEvent(gameObject, "GAMESTART", () =>
            {
                foreach (Enemy enemy in missionArray)
                {
                    enemy.SetEnemyDifficulty();
                }
            });
        }

        return missionArray;
    }


    public void MissionComplete()
    {
        if (Globals.AllowAchievements && GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING && Globals.isMissionComplete == false && Globals.isMissionFailed == false)
        {
            print("Mission Complete");
            Globals.isMissionComplete = true;
            totalPoints = totalPointsRequired;
            //MissionPopUp* node = MissionPopUp::create();
            //this.addChild(node);


            if (Globals.gameType == GameType.Training)
            {
                //Observer.DispatchCustomEvent(Globals.MISSION_COMPLETE);
                //GameManager.instance.GameOver();
            }
        }

    }

    public void MissionFailed()
    {
        // TODO
        if (Globals.isMissionComplete == false)
        {
            //MissionPopUp* node = MissionPopUp::createAsFailed();
            //this.addChild(node);
            isMissionFailed = true;
            player.GotHit(200000);
        }
    }

    public void CheckLocation()
    {
        float dist = Vector3.Distance(GameManager.instance.player.transform.position, Location);
        float distanceSquared = dist * dist;
        if (distanceSquared < totalPointsRequired)
        {
            MissionComplete();
            Location = Vector2.zero;
        }
    }


    public void AddPoint()
    {
        if (Globals.isMissionComplete)
        {
            return;
        }
        totalPoints += 1;
        if (totalPoints == totalPointsRequired && Globals.gameType != GameType.Survival)
        {
            MissionComplete();
        }

    }

    public void SetPoint(int point)
    {

        if (Globals.isMissionComplete)
        {
            return;
        }
        totalPoints = point;

        if (totalPoints == totalPointsRequired && Globals.gameType != GameType.Survival)
        {
            MissionComplete();

        }
    }


    public int GetFillerEnemy()
    {
        return _fillerEnemy;

    }

    public int GetSpawnPowerUp()
    {
        return _powerUp;
    }
    public void SetMissionReward()
    {

    }

}
