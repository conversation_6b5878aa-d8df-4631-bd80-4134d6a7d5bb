﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;

public class Explosion : MonoBehaviour
{
    [HideInInspector] public SkeletonAnimation skeletonAnimation;
    [HideInInspector] public int defaultOrderInLayer;

    private bool inUse = false;

    public bool IsInUse { get { return inUse; } set { inUse = value; } }

    private void Awake()
    {
        skeletonAnimation = GetComponent<SkeletonAnimation>();
        defaultOrderInLayer = skeletonAnimation.GetComponent<MeshRenderer>().sortingOrder;
    }

    public void Reset()
    {
        inUse = false;
        skeletonAnimation.GetComponent<MeshRenderer>().sortingOrder = defaultOrderInLayer;
        gameObject.SetActive(false);
        skeletonAnimation.state.TimeScale = 1;
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        transform.position = Vector3.zero;
    }

    public void PlayAnimation(string name)
    {
        inUse = true;
        gameObject.SetActive(true);
        skeletonAnimation.state.SetAnimation(0, name, false);
    }
}
