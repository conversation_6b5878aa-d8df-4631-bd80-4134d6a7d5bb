using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
public class VolcanoMission : MonoBehaviour
{
    [SerializeField] private BackgroundController bgObj;
    [SerializeField] private SkeletonAnimation volcano;

    private PlayerController player;
    private string tweenId;
    private string schedulerId;

    public void Create()
    {
        player = GameManager.instance.player;
        gameObject.SetActive(true);
    }

    public void AddVolcanoToBg(BackgroundController obj)
    {
        bgObj = obj;
        volcano.gameObject.SetActive(true);
        volcano.transform.position = new Vector3(0, -10, 10);
        volcano.transform.SetScale(8);
        volcano.state.SetAnimation(0, "shoot", true);
        InvokeRepeating("RainOfFire", 0.5f, 5f);
    }

    public void StartMission()
    {

        DOTween.Sequence().SetId(schedulerId).AppendInterval( GameManager.instance.missionManager.totalPointsRequired).AppendCallback(()=>
        {
            GameManager.instance.missionManager.MissionComplete();
            CancelInvoke("RainOfFire");
        });
    }

    private void RainOfFire()
    {
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }


        //bullet.setDamage(125);
        bullet.gameObject.SetActive(true);
        bullet.spriteRenderer.enabled = false;
        bullet.SetBulletChild(BulletChild.FireBall);
        bullet.GetRedFireballTrail().state.SetAnimation(0, "fireball", true);

        bullet.transform.position = new Vector2(player.playerMovement.GetAcceleration().x *Globals.CocosToUnity(100) + player.transform.position.x - Globals.CocosToUnity(800) + Globals.CocosToUnity(Random.value * 1600), Globals.CocosToUnity(1850));
        //bullet.duration = 10;
        Vector2 dest = new Vector2(0, Globals.CocosToUnity(-6500));
        bullet.PlayBulletAnim(dest);
        //bullet.setRadiusEffectSquared(Globals.CocosToUnity(120));
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);

    }
}
