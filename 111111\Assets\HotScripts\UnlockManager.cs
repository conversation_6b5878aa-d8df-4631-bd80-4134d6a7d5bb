using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System.Linq;
public class UnlockManager : MonoBehaviour
{
    [SerializeField] private Image blackOverlay;
    [SerializeField] private UnlockItem[] item;


    private string schedulerId;
    private string tweenId;
    private int itemCount;
    private int currentItem;

    private void Awake()
    {
        schedulerId = "UnlockMS" + GetInstanceID();
        tweenId = "UnlockM" + GetInstanceID();
    }

    public void Show()
    {
        Init();
    }

    private void Init()
    {


        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Shop/ShopSpriteSheet.plist");
        
    }

    private void UnlockItemNow()
    {
        if (currentItem<itemCount)
        {
            currentItem++;
        }
        else
        {
            DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(0, 0.5f)).AppendCallback(()=> {
                gameObject.SetActive(false);
                Observer.DispatchCustomEvent("needOpenReward");
            }).Play();
        }

    }
}
