using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using TMPro;
using UnityEngine.SceneManagement;
public class GameOverMobile : MonoBehaviour
{
    [SerializeField] private GameObject missionMenu;
    [SerializeField] private GameObject bossMenu;
    [SerializeField] private GameObject bossLoseMenu;
    [SerializeField] private Animator objectContainer;
    [SerializeField] private Animator bossContainer;
    [SerializeField] private Image fadeLayer;
    [SerializeField] private TextMeshProUGUI defeatedLabel;
    [SerializeField] private TextMeshProUG<PERSON> killsLabel;
    [SerializeField] private TextMeshProUG<PERSON> coinsLabel;
    [SerializeField] private TextMesh<PERSON>roUGUI bossCoinsLabel;
    [SerializeField] private TextMesh<PERSON>roUG<PERSON> descLabel;
    [SerializeField] private TextMeshProUGUI progressLabel;
    [SerializeField] private Text faildDesLabel;
    [SerializeField] private XPBar xpbar;
    [SerializeField] private XPBar bossXpBar;
    //[SerializeField] private SkeletonGraphic coin;
    [SerializeField] private SkeletonGraphic bossCoin;
    [SerializeField] private SkeletonGraphic chest;
    [SerializeField] private SkeletonGraphic bossChest;
    //[SerializeField] private Image bgLayer;
    //[SerializeField] private UnlockManager unlockManager;
    //[SerializeField] private SideKickUnlockManager sideKickUnlockManager;
    [SerializeField] private GameObject buttonsContainer;
    [SerializeField] private CustomButton shopButton;
    [SerializeField] private CustomButton equipmentButton;
    [SerializeField] private CustomButton airPlaneButton;
    [SerializeField] private CustomButton hqButton;
    [SerializeField] private CustomButton retryButton;
    [SerializeField] private CustomButton nextButton;
    [SerializeField] private CustomButton ADButton;
    [SerializeField] private GameObject chestButton;
    [SerializeField] private GameObject chestButtonBoss;
    [SerializeField] private SkeletonGraphic[] bossAnimation;
    [SerializeField] private GameObject item, coinItem;
    [SerializeField] private Transform itemContent, itemContent2;
    [SerializeField] private TextMeshProUGUI challengDes;
    //[SerializeField] private GameObject killsGo;

    [SerializeField] private GameObject title_bg_01;
    [SerializeField] private GameObject title_bg_02;
    [SerializeField] private GameObject Txt_FaildTip;

    GameObject ChallengTipGo;

    TextMeshProUGUI keyNum;

    int currentMissionPlayed = 0;
    private List<CustomButton> buttonList = new List<CustomButton>();
    private int selectedButton;
    private bool disableMenu = false;
    private int xpReceivedFromMission = 0;
    private int rewardCoins = 0;

    private bool hasChestDropped = false;

    private string tweenId;
    private string schedulerId;

    private int totalKillsCount;
    private bool missionSequencePlayed = false;
    private float incrementorTime = 1.0f;

    private Vector3 initButtonsContainerLocalPosition;
    //private bool isWin;

    private string endBattleParams;
    private int _prizeID;
    private int _challengExtrePrizeID;

    private bool isBossMenu;

    private List<GameObject> _rewardList;

    private void Awake()
    {
        tweenId = "GOM" + GetInstanceID();
        schedulerId = "GOMS" + GetInstanceID();
        initButtonsContainerLocalPosition = buttonsContainer.transform.localPosition;
        _rewardList = new List<GameObject>();
        ChallengTipGo = transform.Find("BottomButtonsContainer/LayoutGroup/NextButton/ChallengTip").gameObject;
        keyNum = ChallengTipGo.transform.Find("TMP_NeedNum").GetComponent<TextMeshProUGUI>();
    }

    public void Show(int kills, int missionPlayed, bool boss = false)
    {
        LuaToCshapeManager.Instance.gameOver = this;
        LuaToCshapeManager.Instance.GameOverMovileIsShow = true;
        GameManager.instance.ClearAllBattle();
        LuaToCshapeManager.Instance.stopSendSkill = true;
        isBossMenu = boss;
        totalKillsCount = kills;
        currentMissionPlayed = missionPlayed;
        gameObject.SetActive(true);
        Init();
        Debug.Log("-------游戏结束-------");
        LuaManager.Instance.RunLuaFunction("BattleManager.End");
        LuaManager.Instance.RunLuaFunction("BattleManager.EndStage", Globals.isMissionComplete);
    }

    private void Init()
    {
        isBossMenu = false;
        Observer.RegisterCustomEvent(gameObject, "needOpenReward", () =>
        {
            //if (Globals.gameType == GameType.Survival)
            //{
            ShowRewards();
            //}
        });
        buttonsContainer.SetActive(false);
        //killsGo.SetActive(Globals.gameType == GameType.Survival);
        //challengDes.gameObject.SetActive(Globals.gameType != GameType.Survival);
        AudioManager.instance.StopAllSoundEffects();
        if (isBossMenu)
        {
            objectContainer = bossContainer;
            chest = bossChest;
            //coin = bossCoin;
            xpbar = bossXpBar;
            coinsLabel = bossCoinsLabel;
            //bossMenu.SetActive(true);
            chestButton = chestButtonBoss;
        }
        else
        {
            missionMenu.SetActive(true);
        }
        coinsLabel.text = "";
        //coin.color = new Color(1, 1, 1, 0);
        coinsLabel.color = new Color(1, 1, 1, 0);
        //bgLayer.transform.localScale = GetSpineScale(460f,280f);
        //DOTween.Sequence().SetId(tweenId).AppendInterval(1f).Append(coin.DOFade(1, 0.5f)).Play();
        DOTween.Sequence().SetId(tweenId).AppendInterval(1f).Append(coinsLabel.DOFade(1, 0.5f)).Play();
        DOTween.Sequence().SetId(tweenId).AppendInterval(1.5f).Append(challengDes.DOFade(1, 0.5f)).Play();
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.5f).AppendCallback(() => { xpbar.FadeXpBar(1, 0.5f); }).Play();
        killsLabel.text = "";
        xpbar.GetLabel().text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SHOP_SCENE)["level"] + " " + GameData.instance.fileHandler.playerLevel;
        string ch = xpReceivedFromMission.ToString();
        xpbar.GetLabel().color = new Color(236 / 255, 167 / 255, 23 / 255, 255);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.9f).AppendCallback(xpbar.StartUpdateBar).Play();
        if (Globals.isMissionComplete)
        {
            if (isBossMenu)
            {
                //暂时屏蔽

                //if (GameData.instance.fileHandler.currentMission == 30)
                //{
                //    sideKickUnlockManager.Show();
                //    DOTween.Sequence().Append(fadeLayer.DOFade(1, 0.5f)).AppendCallback(() =>
                //    {
                //        SceneManager.LoadScene(4);
                //    });
                //}
            }
            //unlockManager.Show();
        }

        //DOTween.Sequence().SetId(tweenId).Append(bgLayer.DOFade(1, 0.5f)).Play();


        //if (Globals.gameType == GameType.Training)
        //{
        missionMenu.SetActive(true);
        if (Globals.isMissionComplete)
        {
            Vector3 pos = buttonsContainer.transform.localPosition;
            pos.y = initButtonsContainerLocalPosition.y;
            pos.y -= 128;
            buttonsContainer.transform.localPosition = pos;
            //bgLayer.AnimationState.SetAnimation(0, "trainingGameOverBg", true);

        }
        else
        {
            buttonsContainer.transform.localPosition = initButtonsContainerLocalPosition;
            //bgLayer.AnimationState.SetAnimation(0, "bossWinScreenBg", true);
        }
        //}
        //else if (isBossMenu)
        //{
        //    if (Globals.isMissionComplete)
        //    {
        //        Vector3 pos = buttonsContainer.transform.localPosition;
        //        pos.y = initButtonsContainerLocalPosition.y;
        //        pos.y -= 128;
        //        buttonsContainer.transform.localPosition = pos;


        //        bossMenu.SetActive(true);
        //        bgLayer.AnimationState.SetAnimation(0, "bossWinScreenBg", true);
        //    }
        //    else
        //    {
        //        buttonsContainer.transform.localPosition = initButtonsContainerLocalPosition;
        //        defeatedLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["defeated"]  as string;
        //        bossLoseMenu.SetActive(true);
        //        bgLayer.AnimationState.SetAnimation(0, "bossLoseScreenBg", true);
        //        Globals.allowDialogueFromBoss = false;
        //        ShowBossImage();
        //    }
        //}

        InitGameEnd();

        DOTween.Sequence().SetId(tweenId).AppendInterval(2f).AppendCallback(() =>
        {
            int val = 0;
            DOTween.To(() => val, x => val = x, totalKillsCount, 1.5f).OnUpdate(() =>
             {
                 killsLabel.text = val.ToString();
             });
        }).Play();

        DOTween.Sequence().SetId(tweenId).AppendInterval(1f).AppendCallback(() => { objectContainer.SetTrigger("FadeIn"); }).Play();
        DOTween.Sequence().SetId(tweenId).AppendInterval(1f).AppendCallback(() =>
        {

            coinsLabel.text = GameData.instance.fileHandler.coins.ToString();
        });
        selectedButton = 1;

        title_bg_01.SetActive(!Globals.isMissionComplete);
        title_bg_02.SetActive(Globals.isMissionComplete);
        killsLabel.transform.parent.gameObject.SetActive(Globals.isMissionComplete);
        coinsLabel.transform.parent.gameObject.SetActive(Globals.isMissionComplete);
        faildDesLabel.text = Globals.g_currentStageData.Desc2;
        Globals.game_speed = 1f;
        GameManager.instance.timeManager.SetTimescale(1);

        if (!Globals.isManualExit && !Globals.isMissionComplete && Globals.g_currentStageData.FrontType == 7)
        {
            Txt_FaildTip.SetActive(false);
            ShowFailRewards();
        }
        else
        {
            Txt_FaildTip.SetActive(true);
            itemContent2.gameObject.SetActive(false);
        }
        int count = GameManager.instance.missionManager.totalPoints - GameManager.instance.missionManager.autoKillPoints;
        //生存积分统计
        LuaManager.Instance.RunLuaFunction<int>("BattleManager.SaveStageScore", count);
    }

    private Vector3 GetSpineScale(float width, float height)
    {
        float realWidth = (Screen.width / ((Screen.height / 1080f) * 1920f)) * 1920f;
        float realHeight = (Screen.width / ((Screen.height / 1080f) * 1920f)) * 1080f;
        Vector3 spine_scale = new Vector3(realWidth / width, realWidth / width, 1);
        return spine_scale;
    }

    private void InitGameEnd()
    {
        if (itemContent.childCount > 0)
        {
            for (int i = 0; i < itemContent.childCount; i++)
            {
                Destroy(itemContent.GetChild(i).gameObject);
            }
        }
        disableMenu = true;

        descLabel.text = Globals.isMissionComplete ? Globals.g_currentStageData.Desc2 : "好可惜，离通关仅一步之遥";

        challengDes.text = Globals.g_currentStageData.Desc2;
        challengDes.gameObject.SetActive(Globals.isMissionComplete);
        string missionProgressString = Globals.isMissionComplete ? (GameManager.instance.missionManager.totalPoints.ToString() + "/" + GameManager.instance.missionManager.totalPointsRequired.ToString()) : ("飞机升星、装备升级、更换伙伴能让你通关更轻松");
        if (Globals.gameType == GameType.Arena)
        {
            missionProgressString = (Globals.isMissionComplete ? GameManager.instance.missionManager.totalPointsRequired.ToString() : "0") + "/" + GameManager.instance.missionManager.totalPointsRequired.ToString();
        }

        progressLabel.text = missionProgressString;
        //胜利或者是失败的时候有捡到物品
        if (Globals.isMissionComplete || LuaToCshapeManager.Instance.GameDropGoodsData.Count > 0)
        {
            //if (Globals.gameType == GameType.Survival)
            //{
            descLabel.text = "";
            progressLabel.text = "";
            //}
            if (Globals.isMissionComplete)
            {
                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chestIdle);
                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chest_drop);
            }

            DOTween.Sequence().SetId(tweenId).AppendInterval(1.6f).AppendCallback(() =>
                {
                    if (Globals.isMissionComplete)
                    {
                        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chest_drop);
                    }

                }).AppendInterval(0.4f).AppendCallback(() =>
                {
                    if (Globals.isMissionComplete)
                    {
                        chest.gameObject.SetActive(true);
                        chest.AnimationState.SetAnimation(0, "chestIdle", true);
                    }
                }).Append(chest.transform.DOScale(Vector2.one * 1f, 0.2f).SetEase(Ease.OutElastic)).AppendCallback(() =>
                {
                    hasChestDropped = true;
                    chestButton.SetActive(true);
                    ChestButtonCall();
                    if (Globals.isMissionComplete)
                    {
                        GameManager.instance.ShakeCamera(Globals.CocosToUnity(30), 5);
                        bool hadUnlock = GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission).ContainsKey("Unlock") && GameData.instance.fileHandler.missionsCompleted < GameData.instance.fileHandler.currentMission;
                        if (!hadUnlock)
                        {
                            ShowRewards();
                        }
                    }
                    else
                    {
                        ShowRewards();
                    }
                }).Play();
        }
        if (!Globals.isMissionComplete)
        {

            AudioManager.instance.PlayMusic(7004);

            DOTween.Sequence().SetId(tweenId).AppendInterval(1.5f).AppendCallback(() =>
            {

                ShowMissionFailedButtons();

            });
        }
    }

    public void ShowRewards(bool isDoubleCount = false)
    {
        if (LuaToCshapeManager.Instance.isFirstBattle)
        {
            _prizeID = Globals.g_currentStageData.FirstPrize;
        }
        else
        {
            _prizeID = Globals.g_currentStageData.PrizeIDs[(int)Globals.gameModeType];
        }

        if (_rewardList.Count > 0)
        {
            foreach (var item in _rewardList)
            {
                Destroy(item);
            }
        }
        string resuletStr = "";

        //胜利
        if (Globals.isMissionComplete)
        {
            resuletStr = LuaManager.Instance.InvokeLuaFunction<string, int>("BattleManager.GetPrizeTableStr", _prizeID);

            //挑战模式如果是胜利的话，需要在难度里面随机30%概率的奖励
            if (Globals.g_currentStageData.FrontType == 2 && Globals.isMissionComplete && Random.Range(0, 10000) <= 3000)
            {
                _challengExtrePrizeID = Globals.g_currentStageData.PrizeLoopIds[(int)Globals.gameModeType];
            }
            else
            {
                _challengExtrePrizeID = 0;
            }

            if (_challengExtrePrizeID != 0)
            {
                string result2 = LuaManager.Instance.InvokeLuaFunction<string, int>("BattleManager.GetPrizeTableStr", _challengExtrePrizeID);
                if (resuletStr == null || resuletStr.Length == 0)
                {
                    resuletStr = result2;
                }
                else
                {
                    resuletStr += ("|" + result2);
                }
            }
        }


        if (LuaToCshapeManager.Instance.GameDropGoodsData.Count > 0)
        {
            string equipStr = "";
            if (Globals.isMissionComplete) equipStr = "|";
            equipStr += LuaManager.Instance.InvokeLuaFunction<string, string>("BattleManager.GetPrizeTableStrByItemID", LuaToCshapeManager.Instance.GetDropGoodsStr());
            resuletStr += equipStr;
        }
        Debug.Log("奖励的结果" + resuletStr);
        string[] datas = resuletStr.Split('|');
        Sequence seq = DOTween.Sequence();

        float startX = Screen.width * (-400) / 1920;

        RectTransform rectTransform = itemContent.GetRectTransform();
        float w = rectTransform.rect.width;

        startX = -1 * (w / 2) + 100;


        for (int i = 0; i < datas.Length; i++)
        {
            string str = datas[i];
            //num .. ";" .. spriteName .. ";" .. quaSprite .. ";" .. atlasName1 .. ";" .. atlasName2
            GameObject go = Instantiate<GameObject>(item);
            _rewardList.Add(go);
            Image frame = go.GetComponent<Image>();
            Image img = go.transform.Find("Img_Icon").GetComponent<Image>();
            Text num = go.transform.Find("Txt_Count").GetComponent<Text>();
            string[] array = str.Split(';');
            num.text = isDoubleCount ? (System.Convert.ToInt32(array[0]) * 2).ToString() : array[0];
            num.gameObject.SetActive(false);
            UnityEngine.U2D.SpriteAtlas imgAtlas = HotResManager.ReadAtlas(array[3]);
            UnityEngine.U2D.SpriteAtlas frameAtlas = HotResManager.ReadAtlas(array[4]);
            img.sprite = imgAtlas.GetSprite(array[1]);
            frame.sprite = frameAtlas.GetSprite(array[2]);

            go.transform.SetParent(itemContent);
            go.transform.localPosition = new Vector3(startX + (i) * 110, 0, 0);
            go.gameObject.SetActive(true);
            img.color = new Color(1, 1, 1, 0);
            frame.color = new Color(1, 1, 1, 0);
            go.transform.localScale = Vector3.one * 4f;

            seq.Append(DOTween.To(() => img.color.a, x => img.color = new Color(1, 1, 1, x), 1, 0.15f).SetEase(Ease.InOutSine)); // 添加改变透明度的动画
            seq.Join(DOTween.To(() => go.transform.localScale.x, x => go.transform.localScale = new Vector3(x, x, x), 1, 0.15f).SetEase(Ease.OutCubic)); // 添加改变缩放比例的动画
            seq.Join(DOTween.To(() => frame.color.a, x => frame.color = new Color(1, 1, 1, x), 1, 0.15f).SetEase(Ease.InOutSine));
            seq.AppendCallback(() =>
            {
                LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 1017);
                num.gameObject.SetActive(true);
            });

        }
    }
    //打开宝箱返回

    public void ChestButtonCall()
    {

        if (missionSequencePlayed)
            return;
        if (!chest)
            return;

        chestButton.SetActive(false);
        missionSequencePlayed = true;

        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            rewardCoins = GameManager.instance.missionManager.rewardInCoins / 2;
            xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp / 2;
        }
        if (Globals.gameModeType == GamePlayMode.Medium)
        {
            rewardCoins = GameManager.instance.missionManager.rewardInCoins;
            xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp;

        }
        if (Globals.gameModeType == GamePlayMode.Hard)
        {
            rewardCoins = GameManager.instance.missionManager.rewardInCoins * 2;
            xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp * 2;

        }
        bool sideKickUnlock = GameManager.instance.missionManager.isUnlockSideKick > 0 ? true : false;

        if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
        {
            GameData.instance.fileHandler.missionUnlock = 1;
        }



        GameData.instance.fileHandler.SaveData();

        chest.AnimationState.SetAnimation(0, "chestOpen", false);
        chest.AnimationState.AddAnimation(0, "rays", true);
        //AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chest_unlock);


        float duration = 5;

        if (rewardCoins > 0)
        {
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 1019);

            //coin.AnimationState.SetAnimation(0, "coin", true);
            StartCoroutine(CoinCollectAnimation());
            if (Globals.gameType == GameType.Training)
            {
                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.95f).AppendCallback(SetCoinsLabel).Play();
            }
            else
            {
                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.95f).AppendCallback(SetCoinsLabel).Play();
            }

            GameData.instance.fileHandler.logTotalCoins += rewardCoins;
            LuaToCshapeManager.Instance.BattleAddCoin += rewardCoins;
            //如果是免广告，增加50%的金币
            if (LuaToCshapeManager.Instance.HadBuyADFree)
            {
                LuaToCshapeManager.Instance.BattleAddCoin = (int)Mathf.Floor(LuaToCshapeManager.Instance.BattleAddCoin * 1.3f);
            }
            PlayerPrefs.SetInt("coins", GameData.instance.fileHandler.coins + rewardCoins);
        }
        else
        {
            disableMenu = false;
        }
        endBattleParams = LuaToCshapeManager.Instance.BattleAddCoin.ToString() + "|" + xpReceivedFromMission.ToString() + "|" + ((int)Globals.gameModeType + 1).ToString() + "|" + LuaToCshapeManager.Instance.GetDropGoodsStr() + "|" + totalKillsCount.ToString();
        //挑战模式如果是胜利的话，需要在难度里面随机30%概率的奖励
        if (Globals.g_currentStageData.FrontType == 2 && Globals.isMissionComplete)
        {
            endBattleParams += ("|" + _challengExtrePrizeID.ToString());
        }

        if (xpReceivedFromMission > 0)
        {
            GameData.instance.fileHandler.playerXP += xpReceivedFromMission;
            if (GameData.instance.fileHandler.playerXP > GameData.instance.fileHandler.xpRequiredThisLevel)
            {
                GameData.instance.fileHandler.playerXP = GameData.instance.fileHandler.playerXP - GameData.instance.fileHandler.xpRequiredThisLevel;
                GameData.instance.fileHandler.playerLevel++;
                var playInfo = PlayerBasePropScheme.Instance.GetItem(GameData.instance.fileHandler.playerLevel);
                GameData.instance.fileHandler.xpRequiredThisLevel = playInfo.LevelExp;
            }
            xpbar.GetLabel().text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SHOP_SCENE)["level"] + " " + GameData.instance.fileHandler.playerLevel;
            string ch = xpReceivedFromMission.ToString();
            xpbar.GetLabel().color = new Color(236 / 255, 167 / 255, 23 / 255, 255);
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.9f).AppendCallback(xpbar.StartUpdateBar).Play();



        }
        duration = incrementorTime + 1.25f;

        //if (Globals.gameType == GameType.Training)
        //{
        //    DOTween.Sequence().SetId(tweenId).AppendInterval(duration).Append(chest.transform.DOScale(Vector2.zero, 0.15f)).Play();
        //    DOTween.Sequence().SetId(schedulerId).AppendInterval(duration + 0.15f).AppendCallback(ShowMissionCompleteButtons).Play();
        //}
        //else
        //{

        DOTween.Sequence().SetId(schedulerId).AppendInterval(duration).AppendCallback(ShowMissionCompleteButtons).Play();

        //}

        //if(sideKickUnlock)
        //{
        //    DOTween.Sequence().SetId(schedulerId).AppendInterval(duration).AppendCallback(() =>
        //    {
        //        sideKickUnlockManager.Show();
        //    }).Play() ;
        //}

    }


    private IEnumerator CoinCollectAnimation()
    {
        Vector3 startPos = chest.transform.position;
        startPos = transform.InverseTransformPoint(startPos);
        //Vector3 endPos = coin.transform.position;
        Vector3 endPos = itemContent.transform.position;
        endPos = transform.InverseTransformPoint(endPos);
        int count = Random.Range(20, 30);
        for (int i = 0; i < count; i++)
        {
            float x = Random.Range(startPos.x - 150, startPos.x + 150);
            float y = Random.Range(startPos.y - 150, startPos.y + 150);
            GameObject item = Instantiate<GameObject>(coinItem, transform);
            item.transform.localPosition = new Vector3(x, y, 0);
            item.SetActive(true);
            item.transform.localScale = Vector3.zero;
            float scale = Random.Range(0.7f, 1.5f);
            item.transform.DOScale(new Vector3(scale, scale, scale), 0.15f).SetEase(Ease.InOutQuad).OnComplete(() =>
                item.transform.DOLocalMove(endPos, 1f).SetEase(Ease.InOutCubic).OnComplete(() => Destroy(item)));

            // wait for the next spawn interval
            yield return new WaitForSeconds(0.01f);
        }
        yield return new WaitForSeconds(0.2f);
        disableMenu = false;
    }

    private void Update()
    {
        CheckInput();
    }

    private void CheckInput()
    {
        if (UnityEngine.Input.GetKeyUp(KeyCode.Return))
        {
            //if (chest.IsActive())
            //{
            //    ChestButtonCall();
            //}
            //else
            //{
            //    buttonList[selectedButton].defaultAction?.Invoke();
            //}
            if (chest.gameObject.activeSelf)
            {
                ChestButtonCall();
            }
            else
            {
                buttonList[selectedButton].defaultAction?.Invoke();
            }
        }
    }

    private void SetXpLabel()
    {
    }

    private void SetCoinsLabel()
    {

        //coin.AnimationState.SetAnimation(0, "coinJump", true);
        int reward = GameManager.instance.missionManager.rewardInCoins;


        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            reward = GameManager.instance.missionManager.rewardInCoins / 2;
        }
        if (Globals.gameModeType == GamePlayMode.Medium)
        {
            reward = GameManager.instance.missionManager.rewardInCoins;

        }
        if (Globals.gameModeType == GamePlayMode.Hard)
        {
            reward = GameManager.instance.missionManager.rewardInCoins * 2;

        }

        int currentEarned = GameData.instance.fileHandler.coins;

        int val = currentEarned;
        DOTween.To(() => val, x => val = x, currentEarned + reward, incrementorTime).OnUpdate(() =>
        {

            coinsLabel.text = val.ToString();
            if ((val) % 4 == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.coinCollected);
            }
            //if (val == currentEarned + reward)
            //{
            //    coin.AnimationState.SetAnimation(0, "coin", true);
            //}
        });
        GameData.instance.fileHandler.coins = GameData.instance.fileHandler.coins + reward;

    }

    public void ShowMissionFailedButtons()
    {
        disableMenu = false;
        endBattleParams = LuaToCshapeManager.Instance.BattleAddCoin.ToString() + "|" + xpReceivedFromMission.ToString() + "|" + ((int)Globals.gameModeType + 1).ToString() + "|" + LuaToCshapeManager.Instance.GetDropGoodsStr() + "|" + totalKillsCount.ToString();
        shopButton.defaultAction = () =>
        {
            if (PlayerPrefs.GetInt("shopTutorial", 0) == 0)
            {
                PlayerPrefs.SetInt("shopTutorial", 1);
            }
            if (!disableMenu)
            {
                ShopCallback();
                disableMenu = true;
                ResetDisableMenu();
            }
        };
        shopButton.SetButtonColor(shopButton.BLUE);
        hqButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                MapCallback();
                disableMenu = true;
                ResetDisableMenu();
            }
        };

        //hqButton.SetButtonColor(hqButton.YELLOW);

        retryButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                RetryPlayCallback();
                disableMenu = true;
                ResetDisableMenu();
            }

        };
        //retryButton.SetButtonColor(retryButton.GREEN);
        equipmentButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                GoEquipmentCallBack();
                disableMenu = true;
                ResetDisableMenu();
            }
        };
        airPlaneButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                GoAirPlaneCallBack();
                disableMenu = true;
                ResetDisableMenu();
            }
        };


        TempContrlButton();

    }
    /// <summary>
    /// 暂时只留一个返回场景的
    /// </summary>

    private void TempContrlButton()
    {

        hqButton.gameObject.SetActive(true);
        //nextButton.gameObject.SetActive(Globals.isMissionComplete && (Globals.g_currentStageData != null && Globals.g_currentStageData.FrontType != 3 && Globals.g_currentStageData.FrontType != 4));
        ADButton.gameObject.SetActive(Globals.isMissionComplete && (Globals.g_currentStageData != null && Globals.g_currentStageData.FrontType != 3 && Globals.g_currentStageData.FrontType != 4));
        retryButton.gameObject.SetActive(!Globals.isMissionComplete && (Globals.g_currentStageData != null && Globals.g_currentStageData.FrontType != 3 && Globals.g_currentStageData.FrontType != 4));
        equipmentButton.gameObject.SetActive(!Globals.isMissionComplete);
        airPlaneButton.gameObject.SetActive(!Globals.isMissionComplete);
        shopButton.gameObject.SetActive(!Globals.isMissionComplete && GameData.instance.fileHandler.missionsCompleted >= 6);
        //buttonsContainer.gameObject.SetActive(true);
        LuaManager.Instance.RunLuaFunction<int>("BattleManager.BattleEndShowBtn", GameData.instance.fileHandler.currentMission);

        ChallengTipGo.SetActive(Globals.g_currentStageData.FrontType == 2);
        keyNum.text = "1/" + Mathf.Max(0, (LuaManager.Instance.InvokeLuaFunction<int, int>("BattleManager.getGoodsNum", 1001) - 1)).ToString();
    }

    private void ShowBossImage()
    {
        DOTween.Sequence().SetId(tweenId).AppendInterval(2f).AppendCallback(() =>
        {
            bossAnimation[GameData.instance.fileHandler.currentEvent - 1].gameObject.SetActive(true);
        }).Play();
    }

    public void ShowMissionCompleteButtons()
    {

        shopButton.defaultAction = () =>
        {
            if (PlayerPrefs.GetInt("shopTutorial", 0) == 0)
            {
                PlayerPrefs.SetInt("shopTutorial", 1);

            }
            if (!disableMenu)
            {
                ShopCallback();
                disableMenu = true;
                ResetDisableMenu();
            }
        };

        shopButton.SetButtonColor(shopButton.BLUE);
        hqButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                MapCallback();
                disableMenu = true;
                ResetDisableMenu();
            }
        };
        hqButton.SetButtonColor(hqButton.YELLOW);
        //nextButton.defaultAction = () => {
        //    if (!disableMenu)
        //    {
        //        NextMission();
        //        disableMenu = true;
        //        ResetDisableMenu();
        //    }
        //};
        ADButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                ADGetDoubleReward();
                disableMenu = true;
                ResetDisableMenu();
            }
        };
        equipmentButton.defaultAction = () =>
        {
            if (!disableMenu)
            {
                GoEquipmentCallBack();
                disableMenu = true;
                ResetDisableMenu();
            }
        };

        TempContrlButton();
    }
    Sequence _disableMenuSeq;
    void ResetDisableMenu()
    {
        if (_disableMenuSeq != null)
        {
            DOTween.Kill(_disableMenuSeq);
        }
        _disableMenuSeq = DOTween.Sequence();
        _disableMenuSeq.AppendInterval(1f);
        _disableMenuSeq.AppendCallback(() =>
        {
            disableMenu = false;
        });
    }

    private void RetryPlayCallback()
    {
        ResetValues();
        //LuaManager.Instance.RunLuaFunction<int>("BattleManager.InitBattle", LuaToCshapeManager.Instance.StageID);
        //LuaToCshapeManager.Instance.ResetAllBattleData();
        //SceneManager.LoadScene("GameScene");
        LuaManager.Instance.RunLuaFunction<int, string>("BattleManager.BattleEndAndNextMission", 0, endBattleParams + "|0");
    }

    private void ShopCallback()
    {
        ResetValues();
        LuaManager.Instance.RunLuaFunction<int, string, int>("BattleManager.BattleEnd", Globals.isMissionComplete ? 1 : 0, endBattleParams, 3);
    }
    private void NextMission()
    {
        ResetValues();
        LuaManager.Instance.RunLuaFunction<int, string>("BattleManager.BattleEndAndNextMission", 1, endBattleParams + "|1");
    }
    private void ADGetDoubleReward()
    {
        LuaManager.Instance.RunLuaFunction("BattleManager.GetDoubleRewardByAD");
    }

    public void ADGetDoubleCallback(bool succ)
    {
        ADButton.gameObject.SetActive(false);
        if (succ)
        {
            ShowRewards(true);
        }
    }

    private void MapCallback()
    {
        ResetValues();
        Globals.showNextMission = false;
        if (endBattleParams == null)
        {
            endBattleParams = "0|0|" + ((int)Globals.gameModeType + 1).ToString() + "|" + LuaToCshapeManager.Instance.GetDropGoodsStr() + "|" + totalKillsCount.ToString();
        }
        LuaManager.Instance.RunLuaFunction<int, string, int>("BattleManager.BattleEnd", Globals.isMissionComplete ? 1 : 0, endBattleParams, 0);
    }

    private void GoEquipmentCallBack()
    {
        ResetValues();
        Globals.showNextMission = true;
        LuaManager.Instance.RunLuaFunction<int, string, int>("BattleManager.BattleEnd", Globals.isMissionComplete ? 1 : 0, endBattleParams, 2);
    }
    private void GoAirPlaneCallBack()
    {
        ResetValues();
        Globals.showNextMission = true;
        LuaManager.Instance.RunLuaFunction<int, string, int>("BattleManager.BattleEnd", Globals.isMissionComplete ? 1 : 0, endBattleParams, 1);
    }


    private void ResetValues()
    {
        GameData.instance.fileHandler.ReadGuns();
        GameData.instance.fileHandler.SaveData();
    }

    public void CloseButtonClick()
    {
        if (!disableMenu)
        {
            MapCallback();
            disableMenu = true;
            ResetDisableMenu();
        }
    }

    /// <summary>
    /// 失败奖励
    /// </summary>
    void ShowFailRewards(bool isDoubleCount = false)
    {
        if (itemContent2.childCount > 0)
        {
            for (int i = 0; i < itemContent2.childCount; i++)
            {
                Destroy(itemContent2.GetChild(i).gameObject);
            }
        }
        int count = GameManager.instance.missionManager.totalPoints - GameManager.instance.missionManager.autoKillPoints;
        count = Mathf.Max(0, count);
        string resuletStr = LuaManager.Instance.InvokeLuaFunction<string, int>("BattleManager.GetPrizeTableStr2", count);
        string[] datas = resuletStr.Split('|');
        Sequence seq = DOTween.Sequence();

        float startX = Screen.width * (-400) / 1920;
        RectTransform rectTransform = itemContent2.GetRectTransform();
        float w = rectTransform.rect.width;
        startX = -1 * (w / 2) + 100;

        for (int i = 0; i < datas.Length; i++)
        {
            string str = datas[i];
            GameObject go = Instantiate<GameObject>(item);
            _rewardList.Add(go);
            Image frame = go.GetComponent<Image>();
            Image img = go.transform.Find("Img_Icon").GetComponent<Image>();
            Text num = go.transform.Find("Txt_Count").GetComponent<Text>();
            string[] array = str.Split(';');
            num.text = isDoubleCount ? (System.Convert.ToInt32(array[0]) * 2).ToString() : array[0];
            num.gameObject.SetActive(false);
            UnityEngine.U2D.SpriteAtlas imgAtlas = HotResManager.ReadAtlas(array[3]);
            UnityEngine.U2D.SpriteAtlas frameAtlas = HotResManager.ReadAtlas(array[4]);
            img.sprite = imgAtlas.GetSprite(array[1]);
            frame.sprite = frameAtlas.GetSprite(array[2]);

            go.transform.SetParent(itemContent2);
            go.transform.localPosition = new Vector3(startX + (i) * 110, 0, 0);
            go.gameObject.SetActive(true);
            img.color = new Color(1, 1, 1, 0);
            frame.color = new Color(1, 1, 1, 0);
            go.transform.localScale = Vector3.one * 4f;

            seq.Append(DOTween.To(() => img.color.a, x => img.color = new Color(1, 1, 1, x), 1, 0.15f).SetEase(Ease.InOutSine)); // 添加改变透明度的动画
            seq.Join(DOTween.To(() => go.transform.localScale.x, x => go.transform.localScale = new Vector3(x, x, x), 1, 0.15f).SetEase(Ease.OutCubic)); // 添加改变缩放比例的动画
            seq.Join(DOTween.To(() => frame.color.a, x => frame.color = new Color(1, 1, 1, x), 1, 0.15f).SetEase(Ease.InOutSine));
            seq.AppendCallback(() =>
            {
                LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 1017);
                num.gameObject.SetActive(true);
            });
        }
        itemContent2.gameObject.SetActive(true);
    }
}
