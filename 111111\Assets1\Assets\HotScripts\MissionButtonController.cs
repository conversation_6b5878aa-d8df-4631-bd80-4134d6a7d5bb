using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using Spine;
using UnityEngine.EventSystems;
public class MissionButtonController : MonoBehaviour
{
    [SerializeField] private GameObject[] stars;

    public SkeletonAnimation skeletonAnimation;
    public GameObject flagObj;

    [HideInInspector] public int currentMission;
    [HideInInspector] public int missionNumber;
    [HideInInspector] public SkeletonAnimation pointer;
    [HideInInspector] public SideMenuController sideMenuObj;
    [HideInInspector] public SideMenuMapMobileController sideMenuMobileObj;

    private Sequence seq;


    public System.Action buttonListener;

    private bool canInvoke = false;


    public static bool canBePressed = true;

    public void SetMissionStars()
    {
        int tempStarUnlock = PlayerPrefs.GetInt("ShowMissionStarUnlock", 0);
        int currentStars = PlayerPrefs.GetInt("MissionStars" + missionNumber,0);
        for (int i = 0; i < currentStars; i++)
        {

            GameObject star = stars[i];
            if (currentStars == 1)
            {
                star.SetActive(true);
            }
            else if (currentStars == 2)
            {
                star.transform.SetLocalPosition(Globals.CocosToUnity(-17 + i * 35), star.transform.localPosition.y);
                star.SetActive(true);
            }
            else if (currentStars == 3)
            {
                star.transform.SetLocalPosition(Globals.CocosToUnity(-35 + i * 35), star.transform.localPosition.y);

            }

            if (PlayerPrefs.GetInt("ShowMissionStarUnlock", 0) == i)
            {
                if (tempStarUnlock == 1)
                {
                    star.transform.SetScale(0);
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.25f + i * 0.25f);
                    seq.Append(star.transform.DORotate(new Vector3(0,0,720),0.15f));
                    seq.Append(star.transform.DOScale(new Vector3(0.8f,0.8f,0.8f),0.15f));
                    seq.Play();
                    PlayerPrefs.SetInt("ShowMissionStarUnlock", 0);
                }
            }
        }
    }

    private void OnMouseUp()
    {
        if (Globals.isMouseOverUI || Globals.isTouchDragging)
        {
            return;
        }
        if (!canInvoke||!canBePressed)
            return;
        if (!Globals.isOnIsland && missionNumber <= 30)
        {
            buttonListener?.Invoke();
        }
        else if(Globals.isOnIsland&&missionNumber>30)
        {
            buttonListener?.Invoke();
        }
    }

    private void OnMouseEnter()
    {
        if (!Globals.isMouseOverUI || !canBePressed)
        {
            canInvoke = true;
            skeletonAnimation.transform.SetScale(1.1f);
        }
    }

    private void OnMouseExit()
    {
        if (!Globals.isMouseOverUI || !canBePressed)
        {
            canInvoke = false;
            skeletonAnimation.transform.SetScale(1f);
        }
    }

}
