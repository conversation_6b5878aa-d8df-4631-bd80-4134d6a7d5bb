using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;
using DG.Tweening;

public class PlayerPowerUp : MonoBehaviour
{
    enum kPowerUp
    {
        PowerUpMechanic,
        PowerUpGunKitty,
        PowerUpJedi,
        PowerupBowie,
        Power<PERSON>pShocker,
        PowerUpLaser,
        PowerUpMulticanon,
        PowerupMachineGun,
        PowerupPlasma,
        PowerupFrontRocket,
        PowerUpCrazyRockets,
        PowerUpCrazyRockets2,
        PowerUpBackFire,
        PowerUpFlameThrower,
        PowerUpProtonCannon,
        PowerUpPlasmaBarrage,
        NumberOfPowerUps,
        PowerUpNoGun,
    }
    [SerializeField] private GameObject bulletFlashParent;
    [SerializeField] private GameObject flameThrowerPrefab, backfireEffectPrefab, laserPrefab, laserImpactPrefab,
        flameThrowerEffectPrefab;
    [SerializeField] private Sprite protonBulletSprite;
    private Animator flameThrowerEffectAnim, flameThrowerAnim, backfireAnim;
    private GameObject[] bulletFlash;
    private GameObject laserStartImpact, laserEndImpact;
    private SpriteRenderer laserRenderer;
    private ProtonBulletSlot[] protonBulletSlots;
    Spine.Bone laserBone;
    Bounds bounds;

    PlayerController player;
    string tweenID, schedulerID;
    int numberOfProtonBullets;
    bool scheduleLaserLogic, isPowerupCollect;

    private void Start()
    {
        Init();
    }


    void Init()
    {
        GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpNoGun;

        player = GameManager.instance.player;

        tweenID = "PowerUpTween";
        schedulerID = "PowerUpScheduler";
        scheduleLaserLogic = false;
        isPowerupCollect = false;

        protonBulletSlots = new ProtonBulletSlot[3];

        for (int i = 0; i < protonBulletSlots.Length; i++)
        {
            protonBulletSlots[i].isOccupied = false;
            protonBulletSlots[i].angle = 120 * i;
        }

        numberOfProtonBullets = 0;

        EndPowerUp();
        //InstantiateObjectsAndEffects();
        InitBulletFlash();
        InitLaser();

        //bounds = spSkeletonBounds_create();
    }

    void InstantiateObjectsAndEffects()
    {
        {
            GameObject obj = Instantiate(flameThrowerEffectPrefab);

            //var bone = player.GetSkeletonAnimation().skeleton.FindBone("flame");
            var pos = player.transform.position;

            obj.transform.parent = player.transform;
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(1.5f, 1.5f, 1);

            flameThrowerEffectAnim = obj.GetComponent<Animator>();
            obj.SetActive(false);

            GameObject obj1 = Instantiate(flameThrowerPrefab);

            //obj1.transform.parent = playerSkeleton.transform;
            obj1.transform.position = pos;
            obj1.transform.localScale = new Vector3(2 * transform.localScale.x, 2 * transform.localScale.x, 1);

            flameThrowerAnim = obj1.transform.GetChild(0).GetComponent<Animator>();
            float flameLevel = 1;
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 1);
        }

        {
            GameObject obj = Instantiate(backfireEffectPrefab);

            Vector3 spawnPoint = player.transform.position * Vector2.one
                     + new Vector2(-Globals.CocosToUnity(80), 0);

            obj.transform.position = spawnPoint;
            obj.transform.localScale = new Vector3(2f, 2f, 1);
            obj.transform.parent = player.transform;
            backfireAnim = obj.GetComponent<Animator>();
            backfireAnim.Play("Main", 0, 1);
        }
    }

    private void InitBulletFlash()
    {
        SpriteRenderer[] t = bulletFlashParent.GetComponentsInChildren<SpriteRenderer>();
        bulletFlash = new GameObject[t.Length];
        for (int i = 0; i < t.Length; i++)
        {
            bulletFlash[i] = t[i].gameObject;
            bulletFlash[i].SetActive(false);
        }
    }

    private void Update()
    {
        if (scheduleLaserLogic)
        {
            LaserLogic();
        }

        if (GameData.instance.fileHandler.PowerUp != (int)kPowerUp.PowerUpProtonCannon)
            return;

        float radius = Globals.CocosToUnity(100);
        float turnSpeed = 2 * 1.5f * 2;

        for (int i = 0; i < protonBulletSlots.Length; i++)
        {
            if (protonBulletSlots[i].isOccupied)
            {
                protonBulletSlots[i].bullet.transform.position = player.transform.position * Vector2.one
            + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
            radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
            }

            protonBulletSlots[i].angle += turnSpeed * Time.deltaTime * 60;
            protonBulletSlots[i].angle = protonBulletSlots[i].angle < 0 ? 360 + protonBulletSlots[i].angle
                : protonBulletSlots[i].angle;
        }
    }

    void Shoot()
    {
        if (player.Mode != PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            return;
        }

        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerupMachineGun)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
            //Shared::playSound("res/Sounds/SFX/warMachine.mp3", false, 1.0f);

            Bullet bullet = null;

            for (int i = 0; i < 5; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bullet = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bullet = b;
                //         bullet.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                bullet.SetBulletType(Bullet.BulletType.FrontMachineGun);

                if (player.transform.position.y < -Globals.CocosToUnity(200))
                {
                    //bulletLayer->setReactToWater(false); TODO
                }

                bullet.setDamage(GameData.instance.fileHandler.PlayerAttack / 2);
                //Sprite* bullet = bulletLayer->bulletSprite;

                //bullet->setSpriteFrame("bullet.png");
                float distance;
                float rotation;
                if (i == 0)
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }
                else if (i == 1)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -10;
                }
                else if (i == 2)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 10;
                }
                else if (i == 3)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -30;
                }
                else if (i == 4)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 30;
                }
                else
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }

                float angle = player.RotationInDegrees + rotation;

                bullet.transform.position =
                    new Vector3(player.transform.position.x + distance * Mathf.Cos(angle * Mathf.Deg2Rad),
                    player.transform.position.y + distance * Mathf.Sin(angle * Mathf.Deg2Rad));
                float duration = 1;
                Vector2 dest = new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                    Globals.CocosToUnity(1250) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
                bullet.PlayBulletAnim(duration, dest);
                bullet.transform.rotation = Quaternion.Euler(0, 0, player.RotationInDegrees);
                bullet.gameObject.SetActive(true);
                GameSharedData.Instance.playerBulletInUse.Add(bullet);

                for (int j = 0; j < bulletFlash.Length; j++)
                {
                    if (!bulletFlash[j].activeSelf)
                    {
                        bulletFlash[j].transform.position = bullet.transform.position;
                        bulletFlash[j].transform.rotation = Quaternion.Euler(0, 0, player.RotationInDegrees);
                        bulletFlash[j].SetActive(true);

                        if (i + 1 == 3 || i + 1 == 4)
                        {
                            bulletFlash[j].GetComponent<SpriteRenderer>().sortingOrder = 1;
                        }
                        else
                        {
                            bulletFlash[j].GetComponent<SpriteRenderer>().sortingOrder = -1;
                        }
                        bulletFlash[j].transform.localScale = new Vector2(3f, 3f);
                        break;
                    }
                }
            }
        }
        else if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpMulticanon)
        {
            //Shared::playSound("res/Sounds/SFX/looseCannon.mp3", false, 1.0f);
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.looseCannon);
            int numberOfbullets = 5;

            Bullet bullet = null;

            for (int i = 0; i < numberOfbullets; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bullet = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bullet = b;
                //         bullet.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                if (player.transform.position.y < -Globals.CocosToUnity(200))
                {
                    //bulletLayer->setReactToWater(false);
                }

                bullet.SetBulletType(Bullet.BulletType.FrontMultiCanon);

                float rotation;
                if (i == 0)
                {
                    bullet.setDamage(8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));

                    if (GameData.instance.fileHandler.multiCanonLevel > 2)
                    {
                        bullet.transform.SetScale(1.5f);
                        bullet.setDamage(20 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel > 4)
                    {
                        bullet.transform.SetScale(3f);
                        bullet.setDamage(25 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));
                    }
                    rotation = 0;
                }
                else if (i == 1)
                {
                    if (GameData.instance.fileHandler.multiCanonLevel == 1 || GameData.instance.fileHandler.multiCanonLevel == 2)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.SetScale(0.5f);
                    }
                    if (GameData.instance.fileHandler.multiCanonLevel == 3)
                    {
                        bullet.transform.SetScale(1f);
                        bullet.setDamage(8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel > 3)
                    {
                        bullet.transform.SetScale(1.25f);
                        bullet.setDamage(15 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));

                    }
                    rotation = -15;
                }
                else if (i == 2)
                {
                    if (GameData.instance.fileHandler.multiCanonLevel == 1 || GameData.instance.fileHandler.multiCanonLevel == 2)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.SetScale(0.5f);
                    }
                    if (GameData.instance.fileHandler.multiCanonLevel == 3)
                    {
                        bullet.transform.SetScale(1);
                        bullet.setDamage(8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));

                    }

                    if (GameData.instance.fileHandler.multiCanonLevel > 3)
                    {
                        bullet.transform.SetScale(1.25f);
                        bullet.setDamage(15 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f));

                    }
                    rotation = 15;
                }
                else if (i == 3)
                {
                    if (GameData.instance.fileHandler.multiCanonLevel == 2 || GameData.instance.fileHandler.multiCanonLevel == 3)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 3);
                        bullet.transform.SetScale(0.3f);
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel == 4)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.SetScale(0.65f);
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel == 5)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)));
                        bullet.transform.SetScale(1);
                    }
                    rotation = -30;
                }
                else
                {
                    if (GameData.instance.fileHandler.multiCanonLevel == 2 || GameData.instance.fileHandler.multiCanonLevel == 3)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 3);
                        bullet.transform.SetScale(0.3f);
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel == 4)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.SetScale(0.65f);
                    }

                    if (GameData.instance.fileHandler.multiCanonLevel == 5)
                    {
                        bullet.setDamage((8 + (GameData.instance.fileHandler.playerLevel * 2 * 0.25f)));
                        bullet.transform.SetScale(1);
                    }
                    rotation = 30;
                }

                bullet.setDamage(GameData.instance.fileHandler.playerLevel / 2);

                float angle = player.RotationInDegrees + rotation;

                bullet.transform.SetWorldPosition(player.transform.position.x + Globals.CocosToUnity(80) * Mathf.Cos(Mathf.Deg2Rad * angle), player.transform.position.y + Globals.CocosToUnity(80) * Mathf.Sin(Mathf.Deg2Rad * angle));
                float duration = 1;
                if (i == 0)
                {
                    duration = 0.7f;
                }

                Vector2 dest = new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(Mathf.Deg2Rad * angle),
                    Globals.CocosToUnity(1250) * Mathf.Sin(Mathf.Deg2Rad * angle));

                bullet.PlayBulletAnim(duration, dest);
                bullet.transform.rotation = Quaternion.Euler(0, 0, angle);
                GameSharedData.Instance.playerBulletInUse.Add(bullet);
            }
        }
        else if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerupPlasma)
        {
            Bullet bullet = null;
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
            bullet = go.GetComponent<Bullet>();
            // bool didFindBullet = false;
            // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
            // {
            //     if (!b.isInUse)
            //     {
            //         bullet = b;
            //         bullet.isInUse = true;
            //         didFindBullet = true;
            //         break;
            //     }
            // }
            // if (!didFindBullet)
            // {
            //     return;
            // }

            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.deatomizerSound);
            //Shared::playSound("res/Sounds/SFX/deatomizerSound.mp3");

            if (player.transform.position.y < -Globals.CocosToUnity(200))
            {
                //bulletLayer->setReactToWater(false);
            }

            bullet.SetBulletType(Bullet.BulletType.FrontPlasma);

            bullet.setDamage(GameData.instance.fileHandler.playerLevel * 0.1f);
            bullet.SetSpriteFrame(null);

            bullet.transform.GetChild(11).gameObject.SetActive(true);

            //ParticleSystemQuad* plasma = ParticleSystemQuad::create("res/Arsenal/Plasma.plist");
            //bulletLayer->addChild(plasma, -20);
            //plasma->setPosition(bullet->getContentSize().width / 2 + 50, bullet->getContentSize().height / 2 + 50);
            float duration = 1;

            //plasma->setPositionZ(-10);
            //plasma->setPosition(Player::getInstance()->getPosition().x + 110 * sinf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation() - 90)), Player::getInstance()->getPosition().y + 110 * cosf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation() - 90)));

            //plasma->runAction(Sequence::create(MoveBy::create(duration * 2, cocos2d::Point(1250 * sinf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation() - 90)), 1250 * cosf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation() - 90)))), NULL));
            //plasma->setScale(1.5);

            bullet.transform.SetWorldPosition(player.transform.position.x + Globals.CocosToUnity(80) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees), player.transform.position.y + Globals.CocosToUnity(80) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
            bullet.PlayBulletAnim(duration * 2, new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees), Globals.CocosToUnity(1250) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees)));

            bullet.transform.rotation = Quaternion.Euler(0, 0, player.RotationInDegrees);
            GameSharedData.Instance.playerBulletInUse.Add(bullet);
        }
        else if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerupFrontRocket)
        {
            Bullet bullet = null;
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
            bullet = go.GetComponent<Bullet>();
            // bool didFindBullet = false;
            // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
            // {
            //     if (!b.isInUse)
            //     {
            //         bullet = b;
            //         bullet.isInUse = true;
            //         didFindBullet = true;
            //         break;
            //     }
            // }
            // if (!didFindBullet)
            // {
            //     return;
            // }

            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.hairball);

            //Shared::playSound("res/Sounds/SFX/hairball.mp3", false, 1.0f);


            if (player.transform.position.y < -Globals.CocosToUnity(200))
            {
                //bulletLayer->setReactToWater(false);
            }
            bullet.SetBulletType(Bullet.BulletType.FrontRocket);


            bullet.setDamage(GameData.instance.fileHandler.PlayerAttack + 25);
            bullet.SetSpriteFrame(null);
            bullet.transform.GetChild(12).gameObject.SetActive(true);
            bullet.transform.SetScale(0.75f);

            float duration = 2;

            bullet.transform.SetWorldPosition(player.transform.position.x + Globals.CocosToUnity(80) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                player.transform.position.y + Globals.CocosToUnity(80) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
            bullet.PlayBulletAnim(duration, new Vector2(Globals.CocosToUnity(1250 * 2) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                Globals.CocosToUnity(1250 * 2) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees)));
            bullet.transform.SetRotation(player.RotationInDegrees);
            GameSharedData.Instance.playerBulletInUse.Add(bullet);
        }

        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpFlameThrower)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.flameThrowerShoot);

            //Shared::playSound("res/Sounds/SFX/flameThrowerShoot.mp3");

            Bullet bullet = null;
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
            bullet = go.GetComponent<Bullet>();
            // bool didFindBullet = false;
            // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
            // {
            //     if (!b.isInUse)
            //     {
            //         bullet = b;
            //         bullet.isInUse = true;
            //         didFindBullet = true;
            //         break;
            //     }
            // }
            // if (!didFindBullet)
            // {
            //     return;
            // }

            bullet.SetBulletType(Bullet.BulletType.RearFlameThrower);

            if (player.transform.position.y < -Globals.CocosToUnity(200))
            {
                //bulletLayer->setReactToWater(false);
            }
            bullet.setDamage(GameData.instance.fileHandler.PlayerAttack / 4);

            float distance = Globals.CocosToUnity(300);

            bullet.transform.SetWorldPosition(player.transform.position.x + distance * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                player.transform.position.y + distance * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));

            bullet.PlayBulletAnim(3, new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                Globals.CocosToUnity(1250) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees)));

            bullet.transform.SetRotation(player.RotationInDegrees);
            GameSharedData.Instance.playerBulletInUse.Add(bullet);

            bullet.SetSpriteFrame(null);
            bullet.transform.SetScale(3);

            string boneName = "flame";
            Spine.Bone bone = player.GetSkeletonAnimation().skeleton.FindBone(boneName);
            Vector3 pos = bone.GetWorldPosition(player.GetSkeletonAnimation().transform);

            StopCoroutine(nameof(PlayFlameThrowerEffectAnim));
            StartCoroutine(nameof(PlayFlameThrowerEffectAnim));

            float flameLevel = 2;
            flameThrowerAnim.transform.parent.position = pos;
            flameThrowerAnim.transform.parent.rotation = Quaternion.Euler(0, 0, player.RotationInDegrees);
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 0);
        }


        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpProtonCannon)
        {
            if (numberOfProtonBullets < 3)
            {
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);

                //Shared::playSound("res/Sounds/SFX/magicFire.mp3");

                numberOfProtonBullets++; 
                Bullet bullet = null;

                for (int i = 0; i < protonBulletSlots.Length; i++)
                {
                    if (!protonBulletSlots[i].isOccupied)
                    {
                        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);
                        //Globals.PlaySound("res/Sounds/SFX/magicFire.mp3");
                        GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                        bullet = go.GetComponent<Bullet>();
                        // bool didFindBullet = false;
                        // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                        // {
                        //     if (!b.isInUse)
                        //     {
                        //         bullet = b;
                        //         didFindBullet = true;
                        //         break;
                        //     }
                        // }
                        // if (!didFindBullet)
                        // {
                        //     return;
                        // }

                        bullet.InitProtonBullet(
                            (Bullet b) =>
                            {
                                for (int j = 0; j < protonBulletSlots.Length; j++)
                                {
                                    if (protonBulletSlots[j].bullet == b)
                                    {
                                        protonBulletSlots[j].isOccupied = false;
                                        protonBulletSlots[j].bullet = null;
                                        numberOfProtonBullets--;
                                        if (numberOfProtonBullets < 0) numberOfProtonBullets = 0;
                                    }
                                }
                            },
                            (Bullet b) =>
                            {
                                for (int j = 0; j < protonBulletSlots.Length; j++)
                                {
                                    if (protonBulletSlots[j].bullet == b)
                                    {
                                        protonBulletSlots[j].isOccupied = false;
                                        protonBulletSlots[j].bullet = null;
                                        numberOfProtonBullets--;
                                        if (numberOfProtonBullets < 0) numberOfProtonBullets = 0;
                                    }
                                }
                            }
                        );

                        bullet.gameObject.SetActive(true);
                        bullet.SetBulletType(Bullet.BulletType.ProtonCanon);
                        bullet.setDamage(GameData.instance.fileHandler.PlayerAttack);
                        bullet.isRemovable = true;
                        bullet.isInUse = true;
                        bullet.SetSpriteFrame(protonBulletSprite);
                        bullet.transform.GetChild(2).gameObject.SetActive(true);
                        float radius = Globals.CocosToUnity(100);

                        bullet.transform.position = player.transform.position * Vector2.one
                            + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                            radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                        bullet.transform.localScale = new Vector3(4.2f, 4.2f, 1);
                        GameSharedData.Instance.playerBulletInUse.Add(bullet);
                        protonBulletSlots[i].bullet = bullet;
                        protonBulletSlots[i].isOccupied = true;

                        break;
                    }
                }
            }
        }

        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpBackFire)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.backFireSound);

            //Globals.PlaySound("res/Sounds/SFX/backFireSound.mp3");

            Bullet bulletLayer = null;

            for (int i = 0; i < 5; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bulletLayer = b;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }
                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                //if (Player::getInstance()->getPositionY() < -200)
                //{
                //    bulletLayer->setReactToWater(false);
                //}
                bulletLayer.setDamage(GameData.instance.fileHandler.PlayerAttack);
                bulletLayer.transform.localScale = new Vector3(2, 2, 1);
                bulletLayer.SetSpriteFrame(null);
                bulletLayer.transform.GetChild(1).gameObject.SetActive(true);
                float duration = 2;


                float distance;
                float rotation;
                if (i == 0)
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }
                else if (i == 1)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -10;
                }
                else if (i == 2)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 10;
                }
                else if (i == 3)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -30;
                }
                else if (i == 4)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 30;
                }
                else
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }

                float rot = player.RotationInDegrees + 180 + rotation;
                rot = rot < 0 ? 360 + rot : rot % 360;

                Vector3 spawnPoint = player.transform.position * Vector2.one
                     + new Vector2(distance * Mathf.Cos(rot * Mathf.Deg2Rad), distance * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.transform.position = spawnPoint;

                rot = (player.RotationInDegrees + 180) % 360;

                Vector2 dest = new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(rot * Mathf.Deg2Rad),
                        Globals.CocosToUnity(1250) * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.PlayBulletAnim(duration, dest, true);
                bulletLayer.transform.rotation = player.transform.rotation;
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.isInUse = true;
                bulletLayer.isRemovable = true;
                bulletLayer.isFlipped = true;
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
                backfireAnim.Play("Main", 0, 0);
            }
        }


        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpCrazyRockets)
        {
            for (int i = 0; i < 30; i++)
            {
                DOTween.Sequence().SetId(tweenID).AppendInterval(0.05f * i).AppendCallback(FireMissile);
            }
        }
        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpCrazyRockets2)
        {
            for (int i = 0; i < 30; i++)
            {
                DOTween.Sequence().SetId(tweenID).AppendInterval(0.05f * i).AppendCallback(FireMissile);
            }
        }

        else if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpPlasmaBarrage)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.deatomizerSound);

            //Shared::playSound("res/Sounds/SFX/deatomizerSound.mp3");

            for (int i = 0; i < 6; i++)
            {
                Bullet bullet = null;
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bullet = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bullet = b;
                //         bullet.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }


                if (player.transform.position.y < -Globals.CocosToUnity(200))
                {
                    //bulletLayer->setReactToWater(false);
                }
                bullet.SetBulletType(Bullet.BulletType.FrontPlasma);


                bullet.setDamage(5 + (GameData.instance.fileHandler.playerLevel * 0.1f));

                bullet.SetSpriteFrame(null);

                bullet.transform.GetChild(11).gameObject.SetActive(true);

                //ParticleSystemQuad* plasma = ParticleSystemQuad::create("res/Arsenal/Plasma.plist");
                //bulletLayer->addChild(plasma, -20);
                //plasma->setPosition(bullet->getContentSize().width / 2 + 50, bullet->getContentSize().height / 2 + 50);
                float duration = 1;

                //plasma->setPositionZ(-10);
                //plasma->setCameraMask(GAMECAMERA);
                //plasma->setPosition(Player::getInstance()->getPosition().x + 110 * sinf(CC_DEGREES_TO_RADIANS(i * 60)), Player::getInstance()->getPosition().y + 110 * cosf(CC_DEGREES_TO_RADIANS(i * 60)));

                //plasma->runAction(Sequence::create(MoveBy::create(duration * 2, cocos2d::Point(1250 * sinf(CC_DEGREES_TO_RADIANS(i * 60)), 1250 * cosf(CC_DEGREES_TO_RADIANS(i * 60)))), NULL));
                //plasma->setScale(1.5);

                bullet.transform.SetWorldPosition(player.transform.position.x + Globals.CocosToUnity(80) * Mathf.Sin(Mathf.Deg2Rad * (i * 60)), player.transform.position.y + Globals.CocosToUnity(80) * Mathf.Cos(Mathf.Deg2Rad * (i * 60)));
                bullet.PlayBulletAnim(duration * 2, new Vector2(Globals.CocosToUnity(1250) * Mathf.Sin(Mathf.Deg2Rad * (i * 60)), Globals.CocosToUnity(1250) * Mathf.Cos(Mathf.Deg2Rad * (i * 60))));

                bullet.transform.rotation = Quaternion.Euler(0, 0, 90 - (i * 60));
                GameSharedData.Instance.playerBulletInUse.Add(bullet);
            }
        }
    }

    IEnumerator PlayFlameThrowerEffectAnim()
    {
        flameThrowerEffectAnim.gameObject.SetActive(true);
        flameThrowerEffectAnim.Play("Main", 0, 0);

        yield return new WaitForSeconds(0.367f);

        flameThrowerEffectAnim.gameObject.SetActive(false);
    }

    void LaserLogic()
    {
        if (!laserRenderer)
        {
            InitLaser();
        }
        
        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpLaser)
        {
            float targetScaleX, targetScaleY, xWorldUnits, yWorldUnits;

            targetScaleX = Globals.CocosToUnity(1150);
            targetScaleY = 3;
            xWorldUnits = laserRenderer.transform.localScale.x * laserRenderer.transform.root.localScale.x
                * (laserRenderer.sprite.rect.width / laserRenderer.sprite.pixelsPerUnit);
            yWorldUnits = laserRenderer.transform.localScale.y * laserRenderer.transform.root.localScale.y
                * (laserRenderer.sprite.rect.height / laserRenderer.sprite.pixelsPerUnit);


            laserRenderer.gameObject.SetActive(true);
            laserStartImpact.gameObject.SetActive(true);
            laserEndImpact.gameObject.SetActive(true);

            laserStartImpact.transform.SetRotation(player.RotationInDegrees);

            if (player.Mode != PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
            {
                laserRenderer.gameObject.SetActive(false);
                laserStartImpact.gameObject.SetActive(false);
                laserEndImpact.gameObject.SetActive(false);
                GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpNoGun;
                scheduleLaserLogic = false;
            }

            float laserDamage = GameData.instance.fileHandler.PlayerAttack / 4;
            Vector2 laserOrigin;

            if (laserRenderer && laserStartImpact && laserEndImpact)
            {
                laserOrigin = player.transform.position;
                laserRenderer.transform.position = laserOrigin;
                laserRenderer.transform.SetRotation(player.RotationInDegrees);

                bool foundEnemy = false;
                float foundDistance = 1300;
                bool alreadyInWater = false;
                const float pointDifferential = 25.0f;
                Enemy enemyFound = null;

                if (player.transform.position.y < Globals.LOWERBOUNDARY - Globals.CocosToUnity(100))
                {
                    foundEnemy = true;
                    foundDistance = 150;
                    alreadyInWater = true;
                    goto alreadyInWater;
                }

                foreach (Enemy enemy in GameSharedData.Instance.enemyList)
                {
                    float distance = foundDistance;

                    for (int i = 1; i < (int)(foundDistance / pointDifferential); i++)
                    {
                        var point = new Vector2(player.transform.position.x + i * Globals.CocosToUnity(pointDifferential) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                            player.transform.position.x + i * Globals.CocosToUnity(pointDifferential) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
                        
                        if (enemy.CheckCollision(point))
                        {
                            if ((i * pointDifferential) < distance)
                            {
                                distance = i * pointDifferential;
                            }
                        }
                    }

                    if (distance < foundDistance)
                    {
                        foundEnemy = true;
                        foundDistance = distance;
                        enemyFound = enemy;
                    }
                }

                if (enemyFound)
                {
                    if (enemyFound.TakeHit(laserDamage))
                    {
                        if (!enemyFound.isDestroyed)
                        {
                            enemyFound.isDestroyed = true;
                            enemyFound.Destroy();
                        }
                        goto exitLaserLoop;
                    }
                }



                foreach (var enemy in GameSharedData.Instance.enemyMissilesInUse)
                {
                    if (enemy.isDestructable)
                    {
                        float angle = Vector2.SignedAngle(player.transform.position, enemy.transform.position);
                        angle = angle < 0 ? 360 + angle : angle;

                        if (player.RotationInDegrees < angle + 10 && player.RotationInDegrees > angle - 10)
                        {
                            float distance = Vector2.Distance(enemy.transform.position, laserOrigin);

                            if (distance < Globals.CocosToUnity(1300))
                            {
                                foundEnemy = true;

                                if (distance < Globals.CocosToUnity(foundDistance))
                                {
                                    foundDistance = distance * 103.63296195f;
                                }


                                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1, enemy.transform.position, false, 3, 0.5f, Globals.CocosToUnity(150));
                                
                                GameManager.instance.ShakeCamera(50, 8);

                                enemy.hasHit = true;
                                enemy.RemoveMissile();

                                goto exitLaserLoop;
                            }
                        }
                    }
                }

                //targetScaleX = GameManager.instance.physicsManager.CheckLaserForEnemies(laserRenderer.transform.position,
                //    targetScaleX, targetScaleY, 100);

                if (!foundEnemy)
                {
                    targetScaleX = Globals.CocosToUnity(foundDistance);
                }
                else
                {
                    float nDistance = foundDistance - 50;
                    if (nDistance < 0)
                    {
                        nDistance = 5;
                    }
                    targetScaleX = Globals.CocosToUnity(nDistance);
                }

                laserRenderer.transform.localScale = new Vector3(
                     laserRenderer.transform.localScale.x,
                     laserRenderer.transform.localScale.y / yWorldUnits * targetScaleY,
                     laserRenderer.transform.localScale.z
                    );

                laserRenderer.size = new Vector2(
                    targetScaleX,
                    laserRenderer.size.y);

                laserEndImpact.transform.position = new Vector3(laserOrigin.x + targetScaleX * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                    laserOrigin.y + targetScaleX * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));

            //laserMaterial.OffsetUpdate(); TODO

            exitLaserLoop:
                laserEndImpact.transform.position = new Vector3(laserOrigin.x + targetScaleX * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                    laserOrigin.y + targetScaleX * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));

                laserEndImpact.transform.SetRotation(player.RotationInDegrees);

                if (laserEndImpact.transform.position.y < Globals.LOWERBOUNDARY)
                {
                    Vector2 targetPos = laserEndImpact.transform.position;
                    float newX = ((Globals.LOWERBOUNDARY - laserOrigin.y) * (targetPos.x - laserOrigin.x)) / (targetPos.y - laserOrigin.y) + laserOrigin.x;
                    float newDistance = Vector2.Distance(new Vector2(newX, Globals.LOWERBOUNDARY), laserOrigin);
                    //newDistance = (player.transform.position.y - Globals.LOWERBOUNDARY) / Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees);

                    laserRenderer.transform.localScale = new Vector3(
                     laserRenderer.transform.localScale.x,
                     laserRenderer.transform.localScale.y / yWorldUnits * targetScaleY,
                     laserRenderer.transform.localScale.z
                    );

                    laserRenderer.size = new Vector2(
                        newDistance,
                        laserRenderer.size.y);

                    laserEndImpact.transform.position = new Vector2(newX, Globals.LOWERBOUNDARY);
                }
            alreadyInWater:
                if (alreadyInWater)
                {
                    float newDistance;
                    newDistance = Globals.CocosToUnity(foundDistance);

                    laserRenderer.transform.localScale = new Vector3(
                     laserRenderer.transform.localScale.x,
                     laserRenderer.transform.localScale.y / yWorldUnits * targetScaleY,
                     laserRenderer.transform.localScale.z
                    );

                    laserRenderer.size = new Vector2(
                        newDistance,
                        laserRenderer.size.y);

                    laserEndImpact.transform.position = new Vector3(laserOrigin.x + newDistance * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                    laserOrigin.y + newDistance * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));

                    laserEndImpact.transform.SetRotation(player.RotationInDegrees);

                }
                //laser->setTextureRect(cocos2d::Rect(laser->getTextureRect().origin.x - 15, laser->getTextureRect().origin.y, laser->getTextureRect().size.width, 128));
            }
        }
        else
        {
            laserRenderer.gameObject.SetActive(false);
            laserEndImpact.SetActive(false);
            laserStartImpact.SetActive(false);
        }
    }

    void FireMissile()
    {
        PlayerMissile missile = null;
        bool didFindBullet = false;
        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        missile.SurvivalTime = 20f;
        missile.Init();
        missile.isInUse = true;

        missile.damage = 12 + GameData.instance.fileHandler.playerLevel;
        missile.transform.position = (Vector2)transform.position;
        GameSharedData.Instance.playerMissilesInUse.Add(missile);


        //if (GameSharedData::getInstance()->playerMissileArray.size() % 5 == 0)
        //{
        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.rocketeerShoot);
            //Shared::playSound("res/Sounds/SFX/rocketeerShoot.mp3");
        //}
    }

    // TODO
    //void PlayerPowerUp::makeTrail(float dt)
    //{
    //    for (auto bullet : GameSharedData::getInstance()->playerBullets)
    //    {
    //        if (bullet->bulletType == frontRocket)
    //        {
    //            Sprite* trail = Sprite::create("res/Arsenal/smokeParticle.png");
    //            this->addChild(trail, -3);
    //            trail->setPosition(bullet->bulletSprite->getPosition().x - 10 + CCRANDOM_0_1() * 20 + (80 * sinf(CC_DEGREES_TO_RADIANS(bullet->bulletSprite->getRotation() + 90))), bullet->bulletSprite->getPosition().y - 10 + CCRANDOM_0_1() * 20 + (80 * cosf(CC_DEGREES_TO_RADIANS(bullet->bulletSprite->getRotation() + 90))));
    //            trail->setScale(0.6 + CCRANDOM_0_1() * 0.4);
    //            trail->setCameraMask(GAMECAMERA);
    //            trail->setRotation(bullet->bulletSprite->getRotation());
    //            trail->runAction(Sequence::create(ScaleTo::create(0.2 + CCRANDOM_0_1() * 0.3, 0), RemoveSelf::create(), NULL));


    //        }
    //    }
    //}

    void InitLaser()
    {
        //暂时屏蔽后面的代码
        return;
        var playerSkeleton = player.GetSkeletonAnimation();
        laserBone = playerSkeleton.skeleton.FindBone("laserGun2");
        var pos = laserBone.GetWorldPosition(playerSkeleton.transform);

        // Laser Begin
        GameObject laser = Instantiate(laserPrefab);
        laser.transform.position = pos;
        laser.transform.parent = playerSkeleton.transform;

        laserRenderer = laser.GetComponent<SpriteRenderer>();
        laser.SetActive(false);
        //Laser End

        // LaserImpact Begin
        for (int i = 0; i < 2; i++)
        {
            GameObject li = Instantiate(laserImpactPrefab);
            li.transform.localScale = new Vector3(2.5f, 2.5f, 1);
            li.transform.parent = playerSkeleton.transform;
            li.transform.position = pos;
            li.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));
            li.SetActive(false);

            if (i == 0)
            {
                laserStartImpact = li;
            }
            else
            {
                laserEndImpact = li;
            }
        }
        // LaserImpact End
    }

    public string Collected()
    {
        if (isPowerupCollect)
            return "";

        isPowerupCollect = true;
        int PowerupType = Random.Range(0, int.MaxValue) % (int)kPowerUp.NumberOfPowerUps;
        int powerupTime = 10;
        float powerUpSpeed = 0.5f;

        string str = GameData.instance.GetMenuData(Globals.POWERUPS)["powerUp"] as string;

        if (PowerupType == (int)kPowerUp.PowerUpMechanic)
        {
            Mechanic node = GameManager.instance.SpawnSidekick(2) as Mechanic;
            node.transform.position = player.transform.position;
            DOTween.Sequence().SetId(node.GetTweenID()).AppendInterval(10).AppendCallback(node.UnscheduleUpdate)
                .Append(node.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2).SetEase(Ease.InExpo))
                .AppendCallback(() =>
                {
                    Destroy(node);
                    Globals.numberOfSideKicks--;
                });

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpMechanic"] as string;
        }

        else if (PowerupType == (int)kPowerUp.PowerUpGunKitty)
        {
            GunKitty node = GameManager.instance.SpawnSidekick(0) as GunKitty;
            node.transform.position = player.transform.position;

            DOTween.Sequence().SetId(node.GetTweenID()).AppendInterval(10).AppendCallback(node.UnscheduleUpdate)
                .Append(node.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2).SetEase(Ease.InExpo))
                .AppendCallback(() =>
                {
                    Destroy(node);
                    Globals.numberOfSideKicks--;
                });

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpGunKitty"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpJedi)
        {
            CatnobiSidekick node = GameManager.instance.SpawnSidekick(5) as CatnobiSidekick;
            node.transform.position = player.transform.position;

            DOTween.Sequence().SetId(node.GetTweenID()).AppendInterval(10).AppendCallback(node.UnscheduleUpdate)
                .Append(node.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2).SetEase(Ease.InExpo))
                .AppendCallback(() =>
                {
                    Destroy(node);
                    Globals.numberOfSideKicks--;
                });

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpJedi"] as string;
        }

        else if (PowerupType == (int)kPowerUp.PowerUpShocker)
        {
            Zapper node = GameManager.instance.SpawnSidekick(4) as Zapper;
            node.transform.position = player.transform.position;

            DOTween.Sequence().SetId(node.GetTweenID()).AppendInterval(10).AppendCallback(node.UnscheduleUpdate)
                .Append(node.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2).SetEase(Ease.InExpo))
                .AppendCallback(() =>
                {
                    Destroy(node);
                    Globals.numberOfSideKicks--;
                });

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpShocker"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerupBowie)
        {
            Bowie node = GameManager.instance.SpawnSidekick(3) as Bowie;
            node.transform.position = player.transform.position;

            DOTween.Sequence().SetId(node.GetTweenID()).AppendInterval(10).AppendCallback(node.UnscheduleUpdate)
                .Append(node.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2).SetEase(Ease.InExpo))
                .AppendCallback(() =>
                {
                    Destroy(node);
                    Globals.numberOfSideKicks--;
                });

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerupBowie"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpProtonCannon)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpProtonCannon;
            powerUpSpeed = 1.0f;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpProtonCannon"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpFlameThrower)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpFlameThrower;
            powerUpSpeed = 0.75f;

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpFlameThrower"] as string;
        }

        else if (PowerupType == (int)kPowerUp.PowerUpLaser)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpLaser;
            //player.GetSkeletonAnimation().skeleton.SetAttachment("laserGun", "laserGun");

            scheduleLaserLogic = true;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpLaser"] as string;
        }

        else if (PowerupType == (int)kPowerUp.PowerUpMulticanon)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpMulticanon;
            powerUpSpeed = 0.14f;

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpMulticanon"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpBackFire)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpBackFire;
            powerUpSpeed = 0.5f;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpBackFire"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerupMachineGun)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerupMachineGun;
            powerUpSpeed = 0.14f;

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerupMachineGun"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerupFrontRocket)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerupFrontRocket;
            powerUpSpeed = 1.0f;

            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerupFrontRocket"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpCrazyRockets)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpCrazyRockets;
            powerupTime = 1;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpCrazyRockets"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpCrazyRockets2)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpCrazyRockets2;
            powerupTime = 1;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpCrazyRockets2"] as string;
        }
        else if (PowerupType == (int)kPowerUp.PowerupPlasma)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerupPlasma;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerupPlasma"] as string;
            powerUpSpeed = 0.75f;
        }
        else if (PowerupType == (int)kPowerUp.PowerUpPlasmaBarrage)
        {
            GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpPlasmaBarrage;
            powerupTime = 3;
            str = GameData.instance.GetMenuData(Globals.POWERUPS)["PowerUpPlasmaBarrage"] as string;
        }

        DOTween.Kill(tweenID);
        DOTween.Sequence().SetId(schedulerID).AppendInterval(powerUpSpeed).AppendCallback(Shoot).SetLoops(-1);
        DOTween.Sequence().SetId(tweenID).AppendInterval(powerupTime).AppendCallback(EndPowerUp);
        CreatePowerupAnimation(str);

        return str;

    }

    void EndPowerUp()
    {
        DOTween.Kill(schedulerID);
        isPowerupCollect = false;

        if (GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpLaser)
        {
            scheduleLaserLogic = false;
            laserRenderer.gameObject.SetActive(false);
            laserStartImpact.SetActive(false);
            laserEndImpact.SetActive(false);
        }

        if(GameData.instance.fileHandler.PowerUp == (int)kPowerUp.PowerUpProtonCannon)
        {
            for (int j = 0; j < protonBulletSlots.Length; j++)
            {
                protonBulletSlots[j].isOccupied = false;

                if (protonBulletSlots[j].bullet)
                {
                    GameSharedData.Instance.RemovePlayerBullet(protonBulletSlots[j].bullet);
                }

                protonBulletSlots[j].bullet = null;
            }
            numberOfProtonBullets = 0;
        }

        if (GameData.instance.fileHandler.FrontGun == (int)AllELEMENTS.frontLaser)
        {
            //player.GetSkeletonAnimation().skeleton.SetAttachment("laserGun", "laserGun");
        }
        GameData.instance.fileHandler.PowerUp = (int)kPowerUp.PowerUpNoGun;
    }


    void EndSidekick(Sidekick sender)
    {
        Sidekick sk = sender;
        sk.UnscheduleUpdate();

        DOTween.Sequence().SetId(sk.GetTweenID())
            .Append(sk.transform.DOBlendableMoveBy(new Vector3(0, Globals.CocosToUnity(2500), 0), 2)
            .SetEase(Ease.InExpo))
            .AppendCallback(() =>
            {
                Destroy(sk);
                Globals.numberOfSideKicks--;
            });
    }

    void CreatePowerupAnimation(string str, float timescale = 1)
    {
        //SkeletonAnimation* bg = SkeletonAnimation::createWithJsonFile("res/Arsenal/powerupBg.json", "res/Arsenal/powerupBg.atlas");
        //this->addChild(bg, 5);
        //bg->setPosition(Director::getInstance()->getWinSize().width / 2, 100);
        //bg->setScale(0.5);
        //bg->setCameraMask((unsigned short)(CameraFlag::USER2));
        //bg->setAnimation(0, "idle", false);
        //bg->runAction(Sequence::create(DelayTime::create(1.4 * 1 / timescale), RemoveSelf::create(), NULL));
        //bg->setTimeScale(timescale);


        //Label* bossLabel = Label::createWithTTF(str, GAME_FONT, 200);
        //bg->addChild(bossLabel, 5);
        //bossLabel->setLineHeight(-100);
        //bossLabel->setLineSpacing(-100);
        //bossLabel->setString(str);
        //bossLabel->setHorizontalAlignment(TextHAlignment::CENTER);
        //bossLabel->setVerticalAlignment(TextVAlignment::TOP);
        //bossLabel->enableOutline(Color4B(0, 177, 203, 255), 20);
        //bossLabel->setScaleY(0);
        //bossLabel->runAction(Sequence::create(DelayTime::create(0.2 * 1 / timescale), ScaleTo::create(0.1 * 1 / timescale, 1), DelayTime::create(0.85 * 1 / timescale), ScaleTo::create(0.1 * 1 / timescale, 1, 0), NULL));
        //bossLabel->setPositionX(-50);
        //bossLabel->runAction(Sequence::create(DelayTime::create(0.2 * 1 / timescale), MoveBy::create(1.0 * 1 / timescale, cocos2d::Point(100, 0)), NULL));
        //bossLabel->setCameraMask((unsigned int)CameraFlag::USER2);
    }
}

