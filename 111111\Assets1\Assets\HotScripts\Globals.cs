using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Runtime.CompilerServices;
using DG.Tweening;
using UnityEngine.EventSystems;

public enum GameType
{
    Training,
    Arena,
    Dungeon,
    Survival
};

public enum GamePlayMode
{
    Easy = 0,
    Medium = 1,
    Hard = 2
};

enum EventBoss
{
    kBossNone,
    kBossSpiky,
    kBossCutin,
    kBossSugarPuff,
    kBossBarkMeow,
    kBossTomasScratcher,
    kBossGreek,
    kBossSpiderCat,
    kBossSentinel,
    kBossPurgana,
    kBossTinyBots,
    kBossMeowthena,
    kBossOkto,

    SpecialkBossSpiky = 101,
    SpecialkBossCutin = 102,
    SpecialkBossSugarPuff = 103,
    SpecialkBossBarkMeow = 104,
    SpecialkBossTomasScratcher = 105,
    SpecialkBossGreek = 106,
    SpecialkBossSpiderCat = 107,
    SpecialkBossSentinel = 108,
    SpecialkBossPurgana = 109,
    SpecialkBossTinyBots = 110,
    SpecialkBossMeowthena = 111,
    SpecialkBossOkto = 112,
}

enum AllELEMENTS
{
    frontMachineGun = 0,
    frontMultiCannon = 1,
    frontPlasma = 2,
    frontRocket = 3,
    frontLaser = 4,
    noFrontGun = 5,
    rearFlameThrower = 6,
    rearHomingRockets = 7,
    rearProtonCannon = 8,
    rearBackFire = 9,
    noRearGun = 10,
    sidekicknone = 11,
    sidekickgun = 12,
    sidekickrocket = 13,
    sidekickmechanic = 14,
    sidekicksue = 15,
    sidekickshocker = 16,
    sidekickcatswalker = 17,
    plane1 = 18,
    plane2 = 19,
    plane3 = 20,
    plane4 = 21,
    plane5 = 22,
    EnemyBulletFireball = 23,
    EnemyBulletExplosive = 24,
    EnemyBulletBlueExplosion = 25,
};

public enum SidekickType { GunKitty = 0, Rockem = 1, Fixit = 2, Bowie = 3, Zapper = 4, Katlando = 5 }
public enum FrontGunType { MachineGun = 0, MultiCanon = 1, Plasma = 3, Rocket = 4, Laser = 2 } //1 霰弹
public enum RearGunType { FlameThrower = 0, ProtonCanon = 1, RearBackFire = 2 }

public enum SkillType
{
    MachineGun = 1,
    MultiCanon = 2,
    Laser = 3,
    Plasma = 4,
    Rocket = 5,
    FlameThrower = 6,
    ProtonCanon = 7,
    RearBackFire = 8,
    FlameThrowerSecond = 9,
    ProtonCanonSecond = 10,
    RearBackFireSecond = 11,
    Refraction = 12,
    Boomerang = 13,
    Skyfire = 14,
    /// <summary>
    /// 米氏力场
    /// </summary>
    ProtectiveShield = 15,
    IceBall = 16,
    Lightning = 17,
    /// <summary>
    /// 霹雳10(导弹)
    /// </summary>
    Missile = 18,
    WhiteCat = 19,
    Hammer = 20,
    WhiteCatB = 21,
    /// <summary>
    /// 激光射线(多条激光) 武器附带
    /// </summary>
    LaserRay_Weapon = 22,
    /// <summary>
    /// 激光射线(多条激光)
    /// </summary>
    LaserRay = 23,
    /// <summary>
    /// 范围激光射线(多条激光)
    /// </summary>
    SectorLaser = 24,
    /// <summary>
    /// 新毒爆弹（自带主动技能）
    /// </summary>
    Skyfire_new01 = 25,
}

public enum PlaneType { TheRedDevil = 0, SuperSonic = 1, BubbleWrap = 2, Gravatron = 3, ShadowBlitz = 4 }


//LittleSpiders
public enum SpiderStates
{
    Walk,
    Shoot,
    Idle
}
//LittleSpiders
public enum SpiderType
{
    LASER,
    GUN
}
public static class Globals
{
    public const string MAIN_MENU = "Mainmenu";
    public const string SHOP_SCENE = "ShopScene";
    public const string SIDEKICKS_SCENE = "SidekickScene";
    public const string TUTORIAL = "Tutorial";
    public const string PAUSE_MENU = "PauseMenu";
    public const string SETTINGS_MENU_MOBILE = "SettingsMenuMobile";

    public const string INGAME_HUD = "InGameHud";
    public const string MISSION_INFO_MENU = "MissionInfoMenu";
    public const string MAP_MENU = "MapMenu";
    public const string UNLOCK_SCENE = "UnlockScene";
    public const string POWERUPS = "PowerUps";
    public const string SUPPORT_MENU = "SupportMenu";
    public const string CREDITS_MENU = "CreditsMenu";
    public const string INGAME_MESSAGES = "InGameMessage";
    public const string NOTIFICATION = "Notifications";
    public const string SURVIVAL = "SurvivalScene";
    public const string NEW_UPDATE = "NewUpdate";
    public const string New_FTUE = "NewFTUE";
    public const string ACCESSIBILITY = "Accessibility";
    public const string SAVEGAME = "SaveGame";

    public const string DIALOGUES = "Dialogues";
    public const string BOSS_DATA = "BossData";
    public const string MISSION_DATA = "MissionData";
    public const string SHOP_DATA = "ShopData";

    public const string POWERUP = "PowerUp";
    public const string FILLER_ENEMY = "FillerEnemies";

    public const string MissionTypeKill = "Kill";
    public const string MissionTypeMultiplier = "Multiplier";
    public const string MissionTypeEnemyBuilding = "EnemyBuilding";
    public const string MissionTypeCivilian = "Civilian";
    public const string MissionTypeChinook = "Chinook";
    public const string MissionTypeSaveBuilding = "SaveBuilding";
    public const string MissionTypeSaveBuildingFromBalloon = "SaveBuildingFromBalloon";
    public const string MissionTypeCollectCrates = "CollectCrates";
    public const string MissionTypeCollectBottles = "CollectBottles";
    public const string MissionTypeSnitch = "Snitch"; //still needs to be worked on
    public const string MissionTypeEnemyBase = "EnemyBase";
    public const string MissionTypeBoss = "Boss";
    public const string MissionTypeKillShips = "KillShips";
    public const string MissionTypeKillBalloons = "KillBalloons";
    public const string MissionTypeSaveSideKick = "SaveSideKick";
    public const string MissionTypeLaserTower = "MissionTypeLaserTower";
    public const string MissionTypeVolcanoMission = "Volcano";
    public const string MissionTypeSurvive = "MissionTypeSurvive";
    public const string MissionTypeEnemyLaserBase = "EnemyLaserBase";
    public const string MissionTypeSaveZapper = "MissionTypeSaveZapper";
    public const string MissionTypeBulletTower = "MissionTypeBulletTower";
    public const string MissionTypeSurvivalMode = "MissionTypeSurvivalMode";
    public const string MissionTypeMeowthena = "MissionTypeMeowthena";
    public const string MissionTypeElectricTower = "MissionTypeElectricTower";
    public const string MissionTypeElectricTower2 = "MissionTypeElectricTower2";

    public const string MissionTypeStoneTower = "MissionTypeStoneTower";
    public const string MissionTypeStoneTower2 = "MissionTypeStoneTower2";


    public const string MissionTypeStages = "MissionTypeStages";


    public const string MISSION_COMPLETE = "MissionComplete";
    public const string MISSION_END = "MissionEnd";

    public const string ACTIVE_LASER_EVENT = "activeLaser";
    public const string DEACTIVE_LASER_EVENT = "deactiveLaser";
    public const string ACTIVATE_TURRET_EVENT = "activateTurret";


    public static bool AllowMusic = true;
    public static bool AllowBgMusic = true;
    public static bool canBerserk = true;

    public static SidekickType sidekickType;
    public static FrontGunType frontGun;
    public static RearGunType rearGun;
    public static PlaneType playerPlane;

    
    public static float enemyTurnSpeedMultiplier = 1.0f;
    public static float enemySpeedMultiplier = 1.0f;
    public static GameType gameType;
    public static GamePlayMode gameModeType = GamePlayMode.Easy;
    public static int survivalModeEnemyLevel = 0;
    public static int numberOfEnemies = 0;
    public static int _coinsThisRun = 0;
    public static float SPEEDLIMIT = 10;
    public static float DASH_DISTANCE = 150;
    public static float GRAVITY = 0.005f;
    public static float DRAG = 0.955f;
    public static float TURN_SPEED = 180;
    public static float TURN_SPEED_WHILE_BOOST = 250;
    public static float THRUST = 0.45f;
    public static float LOWERBOUNDARY = -3.27f; //-6.8f; 
    public static float UPPERBOUNDARY = 11.687f; //6.87f;
    public static float LEFTBOUNDARY = -50;
    public static float RIGHTBOUNDARY = 50;
    public static int difficulty = 1;
    public static uint numberOfProtonBullets = 0;
    public static int numberOfSideKicks;
    public static bool GROUND_ENABLED = false;
    public static bool allowSidekickShoot = true;
    public static bool beginSidekickShoot = false;//部分宠物怪进入圈范围开始射击
    public static Vector3 bossPosition = Vector3.zero;
    public static Vector2 averageBulletDistance;
    public static Vector2 nearestAliveEnemy;
    public static Enemy nearestEnemy;
    public static Vector2 blastAtPosition;
    public static bool isBossMode = false;
    public static bool updateCameraZValueForSidekicks = false;
    public static bool zoomOutCamera = false;
    public static float zoomValueWhileGame = CocosToUnity(400);
    //玩家辅助判断距离
    public static float playerAssistDirection = 13;
    //僚机射击判断距离
    public static float whiteCatAssistDirection = 13;
    public static float bossShouldStayOnScreenAdditionalBoundary = 0;
    public static float zoomInForSec = 0;
    public static float zoomToBossTimeScale = 0.1f;
    public static bool isTutorial = false;
    public static bool isFTUETutorial = false;
    public static int enemyKilledCountInTutorial = 0;
    public static bool lockPlayerMovementForTutorial = false;
    public static bool allowPlayerHit = true;
    public static int controls = 6;
    public static int controllerSetting = 1;
    public static bool isMissionComplete = false;
    /// <summary>
    /// 是手动游戏退出
    /// </summary>
    public static bool isManualExit = false;
    public static bool crateOnBoard = false;
    public static bool AllowAchievements = true;
    public static bool isMissionFailed = false;
    public static int totalEnemiesInCurrentWave = 0;
    public static float allDamage = 0.0f;
    //吃核弹全屏15%伤害
    public static bool skillallDamage = false;
    public static bool canProceedInEnemyFactory = true;
    public static int survivalModeWaveCounter = 1;
    public static bool ENEMIES = true;
    public static bool allowDialogueFromBoss = false;
    public static float zoomToBossForSec = 0f;
    public static float zoomValueOnBoss = CocosToUnity(300);
    public static bool bossShouldStayOnScreen = false;
    public static bool fixDpadsPosition = false;
    public static bool canRegenerateHealth = true;
    public static bool introComplete = false;
    public static bool skipFTUX = false;
    public static bool ftuxSidekicksSpawned = false;
    public static bool specialAbilityTutorialExecuted = false;
    public static bool specialAbilityUsedInTutorial = false;
    public static bool tutorialOrbsDropState = false;
    public static bool canActivateSpecialOnControllerTutorial = false;
    public static float g_SoundVolume = 1;
    public static bool resetControls = false;
    public static bool tutorialEnemy1Killed;
    public static bool tutorialEnemy2Killed;
    public static int enemyPosInArray = -1;
    public static bool alreadyAttacking = false;
    public static bool canDropCoins = true;
    public static bool canDropOrbs = true;
    public static int enemiesTillLastWave = 0;
    public static bool allowShoot = false;
    public static long sessionStartXP = 0;
    public static int enemiesKilledInCurrentSession = 0;
    public static float timerForSurvival = 0;
    public static bool mobileControls = true;
    public static bool focusOnPlayer = true;
    public static bool canGenerateLightening = false;
    public static bool isGameInTutorialState = false;
    public static bool lockTapAndHoldShoot = false;
    public static bool unlockTapShoot = false;
    public static List<Sidekick> sideKickList = new List<Sidekick>();
    public static bool shopTutorial = false;
    public static float maxCameraZoom = 15;
    public static int didLevelUp = 0;
    public static bool gameplayStarted = false;
    public static int numberOfWhiteCat;

    //front
    public const string kMachineGun = "WARMACHINE";
    public const string kMultiCannon = "LOOSECANNON";
    public const string kLaser = "LASER";
    public const string kPlasmaCannon = "DEATOMIZER";
    public const string kRocket = "ROCKET";
    //rear


    public const string kFlameThrower = "INCINERATOR";
    public const string kProtonCannon = "PROTONSHIELD";
    public const string kBackFire = "BACKFIRE";

    //sideKicks

    public const string kGunKitty = "PistolPaws";
    public const string kRockteer = "Rockem";
    public const string kMechanic = "FixitFeline";
    public const string kSuicideKitty = "Bowie";
    public const string kZapper = "Zapper";
    public const string kCatswalker = "Catswalker";

    //plane

    public const string kplane1 = "TheRedDevil";
    public const string kplane2 = "TheSuperSonic";
    public const string kplane3 = "BubbleWrap";
    public const string kplane4 = "Gravatron";
    public const string kplane5 = "Shadowblitz";

    public static string GAME_FONT = "fonts/GrilledCheese BTN Toasted.ttf";
    public static string GAME_BM_FONT = "fonts/gameFont.fnt";
    public static string HEADING_BM_FONT = "fonts/headingFont.fnt";

    public const string FILE_NAME = "res/EnemyDataNew.plist";
    public const string REPEATABLE = "RepeatEnemies";
    public const string ENEMY_INFO = "EnemyInfo";
    public const string STAGE_INFO = "StageInfo";
    public const string WAVE_INFO = "WaveInfo";

    public const string ENEMY_TYPE = "EnemyType";
    public const string DELAY_TIME = "DelayTime";
    public const string INTER_ENEMY_COUNT = "Count";
    public const string COUNT_ON_SCREEN = "CountOnScreen";
    public const string LEVEL = "Level";
    public const string TYPE = "Type";

    //NORMAL ENEMIES
    public const string SEVERSKY = "SEVERSKY";
    public const string CUTIN = "CUTIN";
    public const string BOMBERKITTY = "BOMBERKITTY";
    public const string BALLOON = "BALLOON";
    public const string BALLOONENEMY = "BALLOONENEMY";
    public const string DEATHWING = "DEATHWING";
    public const string DEATHBIRD = "DEATHBIRD";

    //BEE
    public const string BEE = "BEE";
    public const string BULLETFAST = "BULLETFAST";
    public const string BULLETSLOW = "BULLETSLOW";
    public const string BOMBSLOW = "BOMBSLOW";
    public const string LASERATT = "LASERATT";
    public const string THUNDERATT = "THUNDERATT";
    public const string BEEFAST = "BEEFAST";
    public const string BEESLOW = "BEESLOW";
    public const string BEENORMAL = "BEENORMAL";
    public const string BEENORMALSLOW = "BEENORMALSLOW";
    public const string BEENORMALFAST = "BEENORMALFAST";
    public const string BEEHARDSLOW = "BEEHARDSLOW";
    public const string TEST_ENEMY = "TEST_ENEMY";
    public const string TEST_ENEMY01 = "TEST_ENEMY01";
    public const string TEST_ENEMY02 = "TEST_ENEMY02";
    public const string TEST_ENEMY03 = "TEST_ENEMY03";
    public const string TEST_ENEMY04 = "TEST_ENEMY04";

    public const string Spine_XiaoGua_001 = "Spine_XiaoGua_001";
    public const string Spine_XiaoGua_002 = "Spine_XiaoGua_002";
    public const string Spine_XiaoGua_004 = "Spine_XiaoGua_004";
    public const string Spine_XiaoGua_005 = "Spine_XiaoGua_005";
    public const string Spine_XiaoGua_006 = "Spine_XiaoGua_006";
    public const string Spine_XiaoGua_007 = "Spine_XiaoGua_007";
    public const string Spine_XiaoGua_008 = "Spine_XiaoGua_008";
    public const string Spine_XiaoGua_009 = "Spine_XiaoGua_009";
    public const string Spine_XiaoGua_010 = "Spine_XiaoGua_010";
    public const string Spine_XiaoGua_011 = "Spine_XiaoGua_011";
    public const string Spine_XiaoGua_012 = "Spine_XiaoGua_012";
    public const string Spine_XiaoGua_013 = "Spine_XiaoGua_013";
    public const string Spine_XiaoGua_014 = "Spine_XiaoGua_014";
    public const string Spine_XiaoGua_015 = "Spine_XiaoGua_015";
    public const string Spine_XiaoGua_016 = "Spine_XiaoGua_016";
    public const string Spine_XiaoGua_017 = "Spine_XiaoGua_017";
    public const string Spine_XiaoGua_018 = "Spine_XiaoGua_018";
    public const string Spine_XiaoGua_019 = "Spine_XiaoGua_019";
    public const string Spine_XiaoGua_020 = "Spine_XiaoGua_020";
    public const string Spine_XiaoGua_021 = "Spine_XiaoGua_021";
    public const string Spine_XiaoGua_022 = "Spine_XiaoGua_022";
    public const string Spine_XiaoGua_023 = "Spine_XiaoGua_023";
    public const string Spine_XiaoGua_024 = "Spine_XiaoGua_024";
    public const string Spine_XiaoGua_025 = "Spine_XiaoGua_025";
    public const string Spine_XiaoGua_026 = "Spine_XiaoGua_026";
    public const string Spine_XiaoGua_027 = "Spine_XiaoGua_027";
    public const string Spine_XiaoGua_028 = "Spine_XiaoGua_028";
    public const string Spine_XiaoGua_029 = "Spine_XiaoGua_029";
    public const string Spine_XiaoGua_030 = "Spine_XiaoGua_030";
    //NORMAL ENEMIES

    //BOSS MINIONS
    public const string TURRET = "TURRET";    // This has fixed position.
    public const string FLYING_TURRETS = "FLYINGTURRETS";
    public const string SPIDER_CONSTANT_LASER = "SPIDERCONSTANTLASER";
    public const string SPIDER_LASER = "SPIDERLASER";
    public const string SPIDER_BULLET = "SPIDERBULLET";
    public const string BUG_BOT = "BUGBOT";
    public const string BUG_BOT2 = "BUTBOT2";
    public static float aspectRatio = 1.778f;
    public static float screenScaleValueX = 1.0f;
    public static float screenScaleValueY = 1.0f;
    public static bool showNextMission = false;
    public static bool disableScroll = false;
    public static bool g_showDifficultyInUtilityMenu = true;
    public static bool backFromGame = false;
    public static bool onMapMenu = true;
    public static bool onMapController = false;
    public static bool onHQController = false;
    public static bool hqMenuEnabledController = false;
    public static bool hudMenuEnabledController = false;
    public static bool mapMenuEnabledController = false;
    public static bool onMap = false;
    public static bool isAssistMode = false;
    public static bool screenShakeEnabled = false;
    public static bool isOnIsland = false;
    public static bool slowDownForBoss = true;
    public static bool isJoystickConnected = false;
    public static float worldScrollPosX = 0;
    public static float worldScrollPosY = 0;
    public static bool onMainHQ = true;
    public static bool endFtue = false;
    public static bool thrust = false;
    public static bool antiThrust = false;
    public static bool disableShooting = false;
    public static bool tutorialExecuted = false;
    public static bool DONTDIE = false;
    public static bool DIEQUICK = false;
    public static bool openMoveBuff = false;
    public static bool autoShootMode = false;
    public static bool antoShootRestoredEnergying = false;
    public static float autoShootInterval = 0.4f;
    public static float autoShootBerzerInterval = 0.2f;

    //BOSSES
    public const string SPIKY = "SPIKY";
    public const string SUGARPUFF = "SUGARPUFF";
    public const string VLADMIRCUTIN = "VLADMIRCUTIN";
    public const string BARKMEOW = "BARKMEOW";
    public const string MAFIAKITTY = "MAFIAKITTY";
    public const string CUTEUS = "CUTEUS";
    public const string SPIDERCAT = "SPIDERCAT";
    public const string SENTINEL = "SENTINEL";
    public const string DRAGONSISTERS = "DRAGONSISTERS"; //NOT SUPPORTED.
    public const string MEOWTHENA = "MEOWTHENA"; //NOT SUPPORTED.
    public static bool isMouseOverUI = false;
    public static bool isTouchDragging = false;
    public static bool showShop =false;
    public static bool backFromGamePlay = false;
    public static string g_ControllerName;
    public static bool popupDestroyedOnSkip = false;
    public static bool shootingEnergyTutorialExecuted = false;
    public static int playCount = 0;
    public static int g_bgType = 1;
    public static bool spawnBoss = false;
    public static Vector2 movementValues = Vector2.zero;

#region 新增
    public static CatMainStage.Item g_currentStageData = null;
    public static String[] g_currentStageDrops;
    public static string g_currentStageName;
    //enemyFactory 刷boss的时候 用level来当monsternew的id
    public static int boosLevel = 0;
    
    public static bool isClearAllButtle = false;

    public enum UpgradeSkillAttibute
    {
        金币加成 = 0,
        攻击加成,
        生命加成,
        生命恢复,
        攻击速度,
        攻击距离
    }
    public static float game_speed = 1;
#endregion

    public static float CalcAngle(Vector3 p1, Vector3 p2)
    {
        Vector3 diff = p2 - p1;
        float rads = Mathf.Atan2(diff.y, diff.x);
        float degs = Mathf.Rad2Deg * (rads);
        degs = 360 - degs;
        return (degs);
    }

    public static float GetAngle(Vector2 vec)
    {
        return Mathf.Atan2(vec.y, vec.x);
    }

    public static float MoveAngleTowards(float current, float target, float maxDistanceDelta)
    {
        if (current == target)
            return current;

        float angle1 = Mathf.Abs(target - current);
        float angle2 = 360 - angle1;
        float sign = Mathf.Sign(target - current) > 0 ? (angle1 > angle2 ? -1 : 1) : (angle1 > angle2 ? 1 : -1);
        float result = current + maxDistanceDelta * sign;

        result = Mathf.Abs(result - current) > Mathf.Min(angle1, angle2) ? current + Mathf.Min(angle1, angle2) * sign
            : result;

        result = result % 360;

        if (result < 0)
            result = 360 + result;
        return result;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static float CocosToUnity(float val)
    {
        float unityVal = val / 103.63296195f;

        return unityVal;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static float Modulus(float a, float b)
    {
        int result = (int)(a / b);
        return a - (result) * b;
    }
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static double Modulus(double a, double b)
    {
        int result = (int)(a / b);
        return a - (result) * b;
    }
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static float UnityValueTransform(int value)
    {
        float unityValue = value / 10000f;
        return unityValue;
    }
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static float UnityValueTransform(float value)
    {
        var unityValue = value / 10000;
        return unityValue;
    }
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static double UnityValueTransform(double value)
    {
        var unityValue = value / 10000;
        return unityValue;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static float WorldToSpriteScale(float spriteScale, float worldScale, float requiredWorldScale)
    {
        float result = worldScale / spriteScale * requiredWorldScale;

        return result;
    }

    public static Vector2 GetScreenSize()
    {
        return new Vector2(Screen.width, Screen.height);
    }

    public static void Rescale(GameObject go, float scale)
    {
        //elemnent rescale
        //    node->setScale(scale);
        //return;
        Vector2 winSize;
        winSize.x = 1334;
        winSize.y = 750;
        
        Vector2 actualWinSize = GetScreenSize();//Director::getInstance()->getOpenGLView()->getFrameSize();
        //log("actualWinSize %f %f", actualWinSize.width, actualWinSize.height);
        float factorX = Screen.width / winSize.x;
        float factorY = Screen.height / winSize.y;

        float minVal = Mathf.Min(factorX, factorY);
        //    if(actualWinSize.height / actualWinSize.width < winSize.height / winSize.width)
        //        //minVal = MAX(factorX, factorY);
        //        //minVal = winSize.width/winSize.height;
        //        minVal = 1.5f;
        //    log("minVal %f : ",minVal);
        float scaleX = scale / factorX * minVal;
        float scaleY = scale / factorY * minVal;

        minVal = MathF.Min(scaleX, scaleY);
        go.transform.localScale = new Vector2(minVal, minVal);

        //    float scaleYFactor = actualWinSize.height / actualWinSize.width;
        //    if(actualWinSize.height / actualWinSize.width < winSize.height / winSize.width)
        //        scaleYFactor =
        //    node->setScaleX(scale * 1);
        //    node->setScaleY(scale * 1);
    }

    public static void ScaleNode(GameObject node)
    {
#if UNITY_STANDALONE
        return;
#endif
        Vector2 winSize;
        winSize.x = 1334;
        winSize.y = 750;


        Vector2 actualWinSize = GetScreenSize();

        node.transform.SetScaleX(actualWinSize.x / winSize.x);
        node.transform.SetScaleY(actualWinSize.y / winSize.y);
    }

    public static bool Approximately(Vector3 me, Vector3 other, float allowedDifference = 0.1f)
    {
        var dx = me.x - other.x;
        if (Mathf.Abs(dx) > allowedDifference)
            return false;

        var dy = me.y - other.y;
        if (Mathf.Abs(dy) > allowedDifference)
            return false;

        var dz = me.z - other.z;

        return Mathf.Abs(dz) >= allowedDifference;
    }

    public static void PlaySound(string name, bool repeat = false, float volume = 1)
    {

    }

    public static void PlaySound(string str)
    {

    }

    public static float getCurrentAngle(GameObject node)
    {
        float rotAng = node.transform.GetRotation();

        if (rotAng >= 180f)
        {
            rotAng -= 360f;
        }
        else if (rotAng < -180f)
        {
            rotAng += 360f;
        }

        // negative angle means node is facing to its left
        // positive angle means node is facing to its right
        return rotAng;
    }

    public static float getAngleOfTwoVectors(Vector2 vec1, Vector2 vec2)
    {
        Vector2 vectorFromVec1ToVec2 = vec2 - vec1;
        // the angle between two vectors
        return Mathf.Atan2(vectorFromVec1ToVec2.y,vectorFromVec1ToVec2.x) * Mathf.Rad2Deg;
    }
    public static float getAngleDifference(float angle1, float angle2)
    {
        float diffAngle = (angle1 - angle2);

        if (diffAngle >= 180f)
        {
            diffAngle -= 360f;
        }
        else if (diffAngle < -180f)
        {
            diffAngle += 360f;
        }

        // how much angle the node needs to rotate
        return diffAngle;
    }

    public static void RotateNodeToPoint(GameObject node, Vector2 point)
    {
        float angleNodeToRotateTo = getAngleOfTwoVectors(node.transform.position, point);
        float nodeCurrentAngle = getCurrentAngle(node);

        float diffAngle = getAngleDifference(angleNodeToRotateTo, nodeCurrentAngle);

        float rotation = nodeCurrentAngle + diffAngle;

        node.transform.SetRotation(rotation);
    }

    public static void rotateNodeToPointLerp(float speed, GameObject node, Vector2 point)
    {
        float angleNodeToRotateTo = getAngleOfTwoVectors(node.transform.position, point);
        float nodeCurrentAngle = getCurrentAngle(node);

        float diffAngle = getAngleDifference(angleNodeToRotateTo, nodeCurrentAngle);
        if (Mathf.Abs(diffAngle) < speed * Time.deltaTime) // finished rotation
        {
            float rotation = nodeCurrentAngle + diffAngle;
            node.transform.SetRotation(rotation);
        }
        else if (diffAngle > 0)
        {
            node.transform.SetRotation(nodeCurrentAngle + speed * Time.deltaTime);
        }
        else
        {
            node.transform.SetRotation(nodeCurrentAngle - speed * Time.deltaTime);

        }
    }

    public static List<RaycastResult> PointerOverUIObjects(Vector2 pointerPosition)
    {
        PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current);
        eventDataCurrentPosition.position = new Vector2(Input.mousePosition.x, Input.mousePosition.y);
        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventDataCurrentPosition, results);
        return results;
    }

    public static void ShakeScreenFunc(GameObject node, float intensity, float shakeScreen)
    {
        if (!screenShakeEnabled)
            return;

        Vector2 initialPosition = node.transform.position;
        float randomShakeX = (UnityEngine.Random.value * intensity) - intensity / 2;
        float randomShakeY = (UnityEngine.Random.value * intensity) - intensity / 2;


        //node->stopAllActions();
        DOTween.Sequence().AppendCallback(() =>
        {
        node.transform.DOBlendableMoveBy(new Vector2(randomShakeX, -randomShakeY), 0.03f);
        node.transform.DOBlendableMoveBy(new Vector2(-randomShakeX, randomShakeY),0.03f);
        }).AppendInterval(shakeScreen).AppendCallback(()=>
        {
            node.transform.DOMove(initialPosition, 0.01f);
        });
        
        shakeScreen = 0;

    }

    public static void StopAllSounds()
    {

    }

    public static void SetPauseSounds(bool val)
    {
        if(val)
        {
            AudioManager.instance.SetMusicActive(true);
        }
        else
        {
            AudioManager.instance.SetMusicActive(false);
        }
    }

    public static Vector2 GetMidPoint(Vector2 a, Vector2 b)
    {
        Vector2 result;
        result.x = a.x + (a.x - b.x) / 2;
        result.y = a.y + (a.y - b.y) / 2;
        return result;
    }

    /// <summary>
    /// 得到屏幕的边界 上下左右
    /// </summary>
    /// <returns></returns>
    
    public static float[] GetScreenBorder()
    {
        float[] borders = { 0, 0, 0, 0 };
        //世界坐标的右上角  因为视口坐标右上角是1,1,点
        Vector3 cornerPos = Camera.main.ViewportToWorldPoint(new Vector3(1f, 1f, Mathf.Abs(-Camera.main.transform.position.z)));
        //世界坐标左边界
        borders[2] = Camera.main.transform.position.x - (cornerPos.x - Camera.main.transform.position.x);
        //世界坐标右边界
        borders[3] = cornerPos.x;
        //世界坐标上边界
        borders[0] = cornerPos.y;
        //世界坐标下边界
        borders[1] = Camera.main.transform.position.y - (cornerPos.y - Camera.main.transform.position.y);
        return borders;
    }
    /// <summary>
    /// 得到半个屏的距离
    /// </summary>
    /// <returns></returns>
    
    public static float GetScreenHalfWidth()
    {
        Camera mainCamera = GameManager.instance.cameraMovement.GetCamera();
        Vector3 rightUpScreenPoint = new Vector3(1, 1, 0);
        Ray ray = mainCamera.ScreenPointToRay(rightUpScreenPoint); // 将屏幕点转换为射线
        float distance = -ray.origin.z / ray.direction.z; // 计算射线与Z等于0平面的交点参数
        Vector3 worldPoint = ray.GetPoint(distance); // 计算射线与Z等于0平面的交点
        return Mathf.Abs(worldPoint.x - GameManager.instance.player.transform.position.x);
    }


    /// <summary>
    /// 统一来设置相机的zoom，并且调整自动瞄准相关的
    /// </summary>
    /// <param name="value"></param>
    
    public static void SetZoomValueWhileGame(float value)
    {
        zoomValueWhileGame = value;
        Debug.Log("这个是设置相机" + value.ToString());
    }
    /// <summary>
    /// 还原相机
    /// </summary>
    
    public static void ResetZoomValue()
    {
        zoomValueWhileGame = CocosToUnity(400);
        Debug.Log("这个是还原相机");
    }
    /// <summary>
    /// 重新设置当前射击判断距离
    /// </summary>
    
    public static void ResetShootCheckDistance()
    {
        float half = GetScreenHalfWidth();
       // playerAssistDirection = half;
       //whiteCatAssistDirection = half;
    }
    /// <summary>
    /// 重置边界
    /// </summary>
    
    public static void ResetBoundary()
    {       
        Globals.LEFTBOUNDARY = -99999;
        Globals.RIGHTBOUNDARY = 99999;
    }

}
