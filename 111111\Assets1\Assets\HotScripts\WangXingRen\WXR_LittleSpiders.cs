using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;

public class WXR_LittleSpiders : Enemy
{
    #region states
    SpiderStates currentSpiderState;
    private SpiderType currentSpiderType;
    #endregion

    #region Skeleton
    [SerializeField] Sprite bulletImage;
    [SerializeField] private GameObject laser;
    //[SerializeField] Collider2D collider;
    //[SerializeField] GameObject laser;
    [SerializeField] SkeletonAnimation laserImpact;
    [SerializeField] SkeletonAnimation waterExplosion;
    [HideInInspector] public float healthMultiplier = 1;
    private bool allowShooting;//all
    float totalDistanceSquared;
    //private Bounds bounds;
    //private Bone bone;
    private Bullet bullet;
    #endregion

    #region Constants
    const float LASER_LENGTH = 2500.0f;
    #endregion


   public void CreateWithType(SpiderType type)
    {
        currentSpiderType = type;
        Init();
    }
    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowRelocate = false;
        currentSpiderState = SpiderStates.Idle;
        //currentSpiderType = SpiderType.LASER;enemyPoint
        isLittleSpider = true;
        InitStats();
        InitializeEnemyAnimationAtStart();
        SetLaserInitialParams();
        healthBar.transform.position = new Vector2(healthBar.transform.position.x, Globals.CocosToUnity(-50));
        healthBar.gameObject.SetActive(false);
        //Invoke("ChangeSpiderStatesAtStartWithDelay", 2f);
    }

    // Update is called once per frame
    void Update()
    {
        //healthBar.transform.position = new Vector2(enemyPoint.position.x - Globals.CocosToUnity(10), Globals.LOWERBOUNDARY + Globals.CocosToUnity(0.1f));

        //attackPosX = bone.WorldX;
        //attackPosY = bone.WorldY;

        if (currentSpiderState == SpiderStates.Shoot && currentSpiderType == SpiderType.LASER)
        {
            laser.transform.position = new Vector2(enemyPoint.position.x + 0.25f, enemyPoint.position.y - 0.5f);

            if (player.skeletonAnimationTran.position.x < laser.transform.position.x + Globals.CocosToUnity(64)
                && player.skeletonAnimationTran.position.x > laser.transform.position.x - Globals.CocosToUnity(64)
                && laser.gameObject.activeSelf && player.canHit)
            {
                player.GotHit(5, false);
                laser.GetComponent<SpriteRenderer>().size = new Vector2(Mathf.Abs(player.skeletonAnimationTran.position.y - laser.transform.position.y) - Globals.CocosToUnity(50), Globals.CocosToUnity(200));
                laserImpact.transform.localPosition = new Vector2(laser.GetComponent<SpriteRenderer>().size.x + 0.5f, laserImpact.transform.localPosition.y);
                //laser.GetComponent<Rect>().Set(laser.GetComponent<Rect>().center.x-Globals.CocosToUnity(15), laser.GetComponent<Rect>().center.y , Mathf.Abs(player.skeletonAnimationTran.position.y - laser.transform.position.y)-Globals.CocosToUnity(50),Globals.CocosToUnity(128));
            }
            //if (Player::getInstance()->getPosition().x < laser->getPosition().x + 64 && Player::getInstance()->getPosition().x > laser->getPosition().x - 64 && laser->isVisible())
            //{
            //    GETPLAYERCONTROLLER->gotHit(5, false);
            //    laser->setTextureRect(cocos2d::Rect(laser->getTextureRect().origin.x - 15, laser->getTextureRect().origin.y, fabsf(Player::getInstance()->getPosition().y - laser->getPosition().y) - 50, 128));
            //    laserImpact->setPositionX(laser->getTextureRect().size.width);
            //}
            else
            {
                laser.GetComponent<SpriteRenderer>().size = new Vector2(Globals.CocosToUnity(LASER_LENGTH), Globals.CocosToUnity(200));
                laserImpact.transform.localPosition = new Vector2(laser.GetComponent<SpriteRenderer>().size.x + 0.25f, laserImpact.transform.localPosition.y);

                //laser.GetComponent<Rect>().Set(laser.GetComponent<Rect>().center.x - Globals.CocosToUnity(15), laser.GetComponent<Rect>().center.y , LASER_LENGTH, Globals.CocosToUnity(128));
                //laser->setTextureRect(cocos2d::Rect(laser->getTextureRect().origin.x - 15, laser->getTextureRect().origin.y, LASER_LENGTH, 128));
                //laserImpact->setPositionX(laser->getTextureRect().size.width);

            }

        }
        HandleShooting();
        CheckUpdate();
    }

    private void HandleShooting()
    {
        curCheckStateTime += Time.deltaTime;
        if (curCheckStateTime > checkStateTime)
        {
            curCheckStateTime = 0;
            posDiff2 = player.skeletonAnimationTran.position - enemyPoint.position;
            allowShooting = false;
            shootRandom = Random.Range(0, 10000);
            if (shootRandom > skillShootProbability)
            {
                allowShooting = false;
                return;
            }
            totalDistanceSquared = Vector2.SqrMagnitude(enemyPoint.position - player.skeletonAnimationTran.position);
            if (totalDistanceSquared < stats.distanceToShoot)
            {
                
                if (!allowShooting)
                {
                    allowShooting = true;
                    ShootGun();
                }

            }
            else
            {
                allowShooting = false;
            }
        }


    }

    private void InitializeEnemyAnimationAtStart()
    {
        transform.position = new Vector2(player.skeletonAnimationTran.position.x - Globals.CocosToUnity(200) + Random.value * Globals.CocosToUnity(400), -2.87f); // -2.87f
    }

    private void SetLaserInitialParams()
    {
        laser.gameObject.SetActive(false);
        laser.transform.SetRotation(90);
        laserImpact.state.SetAnimation(0, "animation", true);
        laserImpact.transform.position = new Vector2(laserImpact.transform.position.x, Globals.CocosToUnity(64));
    }

    private void HandleSpineEvent(TrackEntry entry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "land")
        {
            //GameManager.instance.ShakeCamera(5, 3);
            if (Globals.zoomValueWhileGame < Globals.CocosToUnity(2))
            {
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(2));
            }
        }

        for (int i = 0; i < 4; i++)
        {
            waterExplosion.state.TimeScale = 1.8f - Random.value;

            if (i == 0)
            {
                waterExplosion.transform.SetScale(6);
            }
            if (i == 1)
            {
                //back leg
            }
            if (i == 2)
            {
                //back leg
            }
            if (i == 3)
            {
                //front leg
                waterExplosion.transform.localScale = new Vector2(4, 4);
            }
            waterExplosion.transform.position = new Vector2(enemyPoint.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(150));
            int bgNum = Mathf.Min(((int)BackgroundController.backgroundType + 1), 7);
            waterExplosion.state.SetAnimation(0, "bg" + (bgNum), false); //will fix it later
            waterExplosion.gameObject.SetActive(false);
        }

        if (spineEvent.Data.Name == "littleLaserOn")
        {
            laser.gameObject.SetActive(true);
        }

        if (spineEvent.Data.Name == "littleLaserOff")
        {
            laser.gameObject.SetActive(false);
        }

        if (spineEvent.Data.Name == "littleShoot")
        {
            ShootGun();
            //call shoot function here
        }
    }

    private IEnumerator ChangeSpiderState()
    {
        while (scheduleUpdate)
        {
            if (currentSpiderState == SpiderStates.Idle)
            {
                scheduleUpdate = false;
                float newPos = player.skeletonAnimationTran.position.x - Globals.CocosToUnity(2) + (Random.value * 5);
                currentSpiderState = SpiderStates.Walk;

                transform.DOMoveX(newPos, Mathf.Abs(newPos - transform.position.x) / Globals.CocosToUnity(2000));
                yield return new WaitForSeconds(Mathf.Abs(newPos - transform.position.x) / Globals.CocosToUnity(2000));
                ReturnToNormalStates();
                yield break;
                //yield return new WaitForSeconds(2.45f);
            }

            else if (currentSpiderState == SpiderStates.Walk)
            {
                currentSpiderState = SpiderStates.Shoot;
                scheduleUpdate = false;
                if (currentSpiderType == SpiderType.LASER)
                {

                    yield return new WaitForSeconds(2.45f);
                    ReturnToNormalStates();
                    yield break;
                    //shootLaser();
                    //ShootGun();

                    // this->runAction(Sequence::create(DelayTime::create(2.45f), CallFunc::create(CC_CALLBACK_0(LittleSpiders::changeState, this)), NULL));

                }
                else if (currentSpiderType == SpiderType.GUN)
                {

                    scheduleUpdate = false;
                    ShootGun();
                    yield return new WaitForSeconds(1.85f);
                    ReturnToNormalStates();
                    yield break;
                    //this->runAction(Sequence::create(DelayTime::create(1.85f), CallFunc::create(CC_CALLBACK_0(LittleSpiders::changeState, this)), NULL));

                }
            }
            else if (currentSpiderState == SpiderStates.Shoot)
            {
                scheduleUpdate = false;
                currentSpiderState = SpiderStates.Idle;
                laser.gameObject.SetActive(false);
                yield return new WaitForSeconds(1.0f);
                ReturnToNormalStates();
                yield break;
                //this->runAction(Sequence::create(DelayTime::create(1.0f), CallFunc::create(CC_CALLBACK_0(LittleSpiders::changeState, this)), NULL));

            }
        }

    }

    public void SetAttributeByLord(float damage, float health)
    {
        stats.bulletDamage = damage / 5;
        stats.health = stats.maxHealth.Value = health / 5;
    }
   
    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 5;
        stats.turnSpeed = baseStats.turnSpeed = 0.85f;
        stats.health = baseStats.health = 100;// = (::getStats()->maxHealth + 20 + difficulty) / 1.5f;
        //    stats.health = baseStats.health = 600;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.bulletDamage = baseStats.bulletDamage = 2.5f + Globals.difficulty + (5.0f * GameData.instance.fileHandler.currentMission / 5);
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.coinAwarded = baseStats.coinAwarded = 1;
        stats.xp = baseStats.xp = stats.maxHealth.Value;

    }

    public void SetAttribute()
    {
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            //int bossNumber = Globals.boosLevel;
            //PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
            //stats.speed = baseStats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
            //stats.health = baseStats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]) / 3;
            //stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
            //stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
            //stats.regen = baseStats.regen = System.Convert.ToSingle((bossStats["regen"] as PList)["value"]);
            //stats.xp = baseStats.xp = System.Convert.ToSingle((bossStats["xp"] as PList)["value"]);
            //stats.coinAwarded = baseStats.coinAwarded = (int)System.Convert.ToSingle((bossStats["coins"] as PList)["value"]);
            //stats.maxHealth = baseStats.maxHealth = stats.health;
            //stats.missileDamage = stats.bulletDamage * 5;
            //if (bossStats.ContainsKey("CatDropID"))
            //{
            //    prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
            //}
            //else
            //{
            //    prizeID = 0;
            //}
        }

    }

    private void ChangeSpiderStatesAtStartWithDelay()
    {
        scheduleUpdate = true;
        healthBar.gameObject.SetActive(true);
        StartCoroutine(ChangeSpiderState());
    }

    private void ReturnToNormalStates()
    {
        scheduleUpdate = true;
        StartCoroutine(ChangeSpiderState());
    }

    public void ShootGun()
    {
        for (int i = 0; i < 1; i++)
        {
            //Bullet bullet = new Bullet();
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            //if (Globals.gameModeType == GamePlayMode.Easy)
            //{
            //    bullet.setDamage(stats.bulletDamage * 0.5f);
            //}
            //if (Globals.gameModeType == GamePlayMode.Medium)
            //{
                bullet.setDamage(stats.bulletDamage);
            //}
            //if (Globals.gameModeType == GamePlayMode.Hard)
            //{
            //    bullet.setDamage(stats.bulletDamage * 1.5f);
            //}

            bullet.SetSpriteFrame(bulletImage);
            bullet.gameObject.SetActive(true);
            bullet.transform.SetScale(1);
            bullet.transform.SetRotation(90);
            //bone = enemySprite.skeleton.FindBone("laser");

            bullet.transform.position = enemyPoint.position;
            bullet.transform.SetRotation(-45 + (i * 45));
            bullet.duration = 6.5f;
            Vector2 dir = player.skeletonAnimationTran.position - enemyPoint.position;
            dir = new Vector2(dir.x + Mathf.Cos(Mathf.Deg2Rad * Random.value) / 100f, dir.y + Mathf.Cos(Mathf.Deg2Rad * Random.value) / 100f);//计算方向偏移
            Vector2 directed = dir.normalized;
            bullet.transform.right = -directed;
            dir = directed * stats.attackDistance * 50f;

            bullet.PlayBulletAnim(stats.bulletSpeed, dir);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            bullet.setRadiusEffectSquared(5f);
        }

        //for (int i = 0; i < 2; i++)
        //{
        //    //Bullet bullet = new Bullet();
        //    bool didFindBullet = false;
        //    foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        //    {
        //        if (!b.isInUse)
        //        {
        //            bullet = b;
        //            bullet.isInUse = true;
        //            didFindBullet = true;
        //            break;
        //        }
        //    }
        //    if (!didFindBullet)
        //    {
        //        return;
        //    }

        //    bullet.setDamage(stats.bulletDamage);
        //    bullet.spriteRenderer.sprite = bulletImage;
        //    bullet.duration = 6.5f;
        //    bullet.transform.SetScale(1);
        //    bullet.transform.SetRotation(90);
        //    //bone = enemySprite.skeleton.FindBone("laser");

        //    bullet.transform.position = new Vector2(enemyPoint.position.x, enemyPoint.position.y - 30);
        //    bullet.transform.SetRotation(-90 + (i * 180));
        //    Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.eulerAngles.z), Globals.CocosToUnity(5000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.eulerAngles.z));

        //    bullet.PlayBulletAnim(2, dest);
        //    bullet.duration = 6.5f;
        //    GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        //    bullet.setRadiusEffectSquared(Globals.CocosToUnity(55));

        //}
    }
}
