using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;
using DG.Tweening;
using Spine;
using Spine.Unity;
using UnityEngine.UI;
using TMPro;
public class GameManager : MonoBehaviour
{
    [System.Serializable] public struct EnemyData { public string name; public Enemy enemy; }
    [System.Serializable] public struct PrefabData { public string name; public GameObject prefab; public Transform parentTransform; }

    public static GameManager instance;
    //public BackgroundController backgroundController;
    public PhysicsManager physicsManager;
    public MissionManager missionManager;
    public PlayerController player;
    public MinionSideKick minionSidekick;
    public PlayerHud playerHud;
    public ControlsTutorialManager controlsTutorialManager;
    public PlayerDeathControls deathControls;

    public Transform dialoguesPanelTransform;
    [SerializeField] private bool spawnEnemies = true;
    [SerializeField] private bool spawnOnlyBoss = false;
    [SerializeField] private Transform playerPingParent;
    [SerializeField] Sidekick[] sidekickList;
    public CameraMovement cameraMovement;
    [SerializeField] PrefabData[] prefabs;
    [SerializeField] private Enemy[] seversky;
    [SerializeField] private Enemy[] boss;
    [SerializeField] private Enemy[] allEnemies;
    [SerializeField] private EnemyData[] enemyData;
    [SerializeField] private PauseMenu pauseMenu;
    [SerializeField] private GameObject levelCompleteEnergyTransition;
    [SerializeField] private GameObject levelfailIris;
    [SerializeField] private GameObject levelfailExplosionTransition;
    [SerializeField] private GameObject speedLines;
    //[SerializeField] private GameObject touchControls;
    //[SerializeField] private GameObject specialAbilityButton;
    //[SerializeField] private GameObject DashButton;
    //[SerializeField] private GameObject shootButton;
    //[SerializeField] private GameObject rightStick;
    //[SerializeField] private GameObject specialGlow;
    [SerializeField] private Image bg;
    [SerializeField] public GameOverMenuManager gameOverMenu;
    [SerializeField] private UtilityMenu utilityMenu;
    [SerializeField] private WaveData[] waveData;
    [SerializeField] private Image endTimer;
    [SerializeField] private VolcanoMission volcanoMission;
    [SerializeField] private TimerHUD timerHUD;
    [SerializeField] private CustomButton skipButton;
    [SerializeField] private TextMeshProUGUI skipLabel;

    private Water[] waters;
    private int waveCount = 0;
    private int bossCount = 0;
    /// <summary>
    /// 出战的宠物
    /// </summary>
    private List<Sidekick> levelSidekick;
    private Enemy bossObject;
    [HideInInspector] public EnemyFactory eFactory;
    private int hammerheadSpawned;
    private int trackerSpawned;
    private int dozerSpawned;
    private int blastorSpawned;
    private int skyrunnerSpawned;
    private bool scheduleUpdate = false;
    private bool runTimerUpdate = false;
    private bool failSequencePlayed = false;
    private int randomBackgroundType = 0;
    private bool isZoomToBoss;

    [HideInInspector] public bool zoomIn;
    [HideInInspector] public Scheduler scheduler;
    [HideInInspector] public TimeManager timeManager;
    [HideInInspector] public string tweenId;
    [HideInInspector] public int killsThisRun;


    private bool enableEndGame = false;
    private int coinsThisRun;
    private long xpThisRun;

    public SpriteRenderer backgroundFadeLayer;

    public void SetSpecialAbilityButton(bool val){InputController.instance.SetSpecialAbility(val);}

    //public void SetDashButton(bool val) { InputController.instance.SetDash(val); }
    public void SetShootButton(bool val) { InputController.instance.SetShoot(val); }
    public void SetSpecialGlow(bool val) { InputController.instance.SetSpecialGlow(val); }


    //新增的属性
    private int _curPlayerLevelID;
    private CatsKillLevelUp.Item _curLevelData;
    private float _curExp;
    private float _curMaxExp;
    public AudioListener audioListener;


    private void Awake()
    {
        DOTween.SetTweensCapacity(1000, 1000);
        timeManager = GetComponent<TimeManager>();
        instance = this;
        Globals.isMissionComplete = false;
        Globals.gameplayStarted = false;
        Globals.maxCameraZoom = Globals.CocosToUnity(1500);

        audioListener.enabled = LuaToCshapeManager.Instance.EquipmentID == 0;
        //TODO
        //InitVariables();
    }

    private void InitVariables()
    {
        _curPlayerLevelID = 0;
        _curLevelData = CatsKillLevelUpScheme.Instance.GetItem(_curPlayerLevelID);
        _curExp = 0;
        _curMaxExp = 0;
        //TODO
        //Device::setAccelerometerEnabled(false);
        //srand((unsigned)time(NULL));
        Globals.movementValues = Vector2.zero;
        Globals.zoomValueOnBoss = Globals.CocosToUnity(430);
        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(400));
        Globals.bossPosition = Vector2.zero;
        Globals.bossShouldStayOnScreen = false;
        Globals.bossShouldStayOnScreenAdditionalBoundary = 0;
        Globals.zoomToBossForSec = 0;
        Globals.zoomToBossTimeScale = 0.1f;
        Globals.backFromGame = true;
        Globals.backFromGamePlay = true;
        Globals.canGenerateLightening = false;

        //    Shared::scaleNode(this);
        timeManager.SetTimescale(1.0f);
        //TODO
        //Director::getInstance().getOpenGLView().setCursorVisible(false);
        zoomIn = false;
        print(Globals.AllowBgMusic);
        

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (isZoomToBoss)
            {
                DOTween.Sequence().SetId(tweenId).AppendInterval(0.3f).AppendCallback(() =>
                {
                    //hudLayer.isZoomed = false;
                }).Play();
                isZoomToBoss = false;
                //ZoomOut();
            }
        }



        if (!Globals.isJoystickConnected)
        {
            skipLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["escToSkip"] as string;

            skipButton.gameObject.SetActive(false);
        }
//#if UNITY_STANDALONE
//        if (!Globals.isJoystickConnected)
//        {
//            skipLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["escToSkip"] as string;

//            skipButton.gameObject.SetActive(false);
//        }
//#endif

        Observer.RegisterCustomEvent(gameObject, "ZOOM_OUT_BOSS", () =>
        {
            if (isZoomToBoss)
            {
                DOTween.Sequence().SetId(tweenId).AppendInterval(0.3f).AppendCallback(() =>
                {
                    //hudLayer.isZoomed = false;
                }).Play();
                isZoomToBoss = false;
                //ZoomOut();
            }
        });

    }


    private void Init()
    {
        tweenId = "GM" + GetInstanceID();
        Globals.numberOfEnemies = 0;
        waters = FindObjectsOfType<Water>();
        //touchControls.SetActive(Globals.mobileControls);
        InitObjects();
        if (Globals.gameType == GameType.Survival)
        {
            Globals.enemiesTillLastWave = 0;
            Globals.survivalModeWaveCounter = 1;
            Globals.enemiesKilledInCurrentSession = 0;
            GameData.instance.fileHandler.totalKillsSurvivalMode = PlayerPrefs.GetInt("totalKillsSurvivalMode");
            GameData.instance.fileHandler.totalWavesSurvivalMode = PlayerPrefs.GetInt("totalWavesSurvivalMode");
        }


        //FIXME: Noor
        if (GameData.instance.fileHandler.currentMission != 0)
        {
            Globals.tutorialEnemy1Killed = false;
            Globals.tutorialEnemy2Killed = false;
            Globals.shootingEnergyTutorialExecuted = false;
            Globals.isTutorial = false;
            Globals.lockPlayerMovementForTutorial = false;
            Globals.lockTapAndHoldShoot = false;
            Globals.unlockTapShoot = true;
            Globals.ftuxSidekicksSpawned = false;
            Globals.canDropOrbs = true;
            Globals.canDropCoins = true;

        }


        Globals.resetControls = true;
        Globals.focusOnPlayer = true;
        scheduleUpdate = false;
        utilityMenu.Init();
        //Debug.Log("PlayMusic:"+ Globals.AllowBgMusic +  "     " + GameData.instance.fileHandler.currentMission);
        if (Globals.AllowBgMusic)
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {
                
                AudioManager.instance.PlayMusic(Track.gamePlayMusic, false, 0.25f);
            }else{

                AudioManager.instance.PlayMusic(5501);
            }
            
        }

    }

    public void Start()
    {
        Init();
        //        tweenId = "GM" + GetInstanceID();
        //        Globals.numberOfEnemies = 0;
        //        waters = FindObjectsOfType<Water>();
        //        missionManager.Init();
        //#if UNITY_STANDALONE
        //        if (GameData.instance.fileHandler.currentMission == 0)
        //        {
        //            Globals.isTutorial = true;
        //            player.Stats.consumption = 0;
        //            Globals.zoomValueWhileGame = Globals.CocosToUnity(200);
        //        }
        //        else
        //        {
        //            Globals.tutorialExecuted = true;
        //            player.Stats.consumption = 1;
        //        }
        //#endif
        //        Globals.zoomValueWhileGame = -Globals.CocosToUnity(400);
        //        timeManager.SetTimescale(0.1f);

        //        Globals.resetControls = true;
        //        Globals.focusOnPlayer = true;
        //        scheduleUpdate = false;
        //        Globals.sidekickType = (SidekickType)PlayerPrefs.GetInt("Category5");
        //        levelSidekick = SpawnSidekick((int)Globals.sidekickType);
        //        utilityMenu.Init();


    }
    [IFix.Patch]
    private void InitObjects()
    {
        scheduleUpdate = false;
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            GameData.instance.fileHandler.currentEvent = 0;

#if UNITY_STANDALONE
            Observer.DispatchCustomEvent("disable_movement");
            Observer.DispatchCustomEvent("disable_rotation");
#endif
        }

        //    Shared::scaleNode(this);
        InitMissionManager();
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            // 

            Globals.isTutorial = true;
            player.Stats.consumption = 0;

            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(200));
        }
        else
        {
            Globals.tutorialExecuted = true;
            player.Stats.consumption = 1;

        }



        InitBackground();
        InitPlayer();
        Globals.playCount++;
        //InitHud();
        if (Globals.gameType == GameType.Arena)
        {
            //TODO
            //_hudLayer.hudPauseButton.setVisible(false);
            //_hudLayer.hudPauseButton.setEnabled(false);
        }
        // add this to initTuTorial




        if (GameData.instance.fileHandler.currentMission == 0)
        {
            DialoguePopup.CreateAsPreBattle();
            Observer.DispatchCustomEvent("SpawnStartingDialogues");
            Globals.isGameInTutorialState = true;
            controlsTutorialManager.Init();

            DOTween.Sequence().SetId(tweenId).AppendInterval(0.1f).AppendCallback(() =>
            {

                controlsTutorialManager.ChangeState(TutorialState.Movement);
                if (Globals.isAssistMode)
                {
                    // add a dialogue popup here
                }

            }).Play();
        }
        else
        {
            if(levelSidekick == null)
            {
                levelSidekick = new List<Sidekick>();
            }
            levelSidekick.Clear();
            if (LuaToCshapeManager.Instance.SidekickEquipmentIDs.Length > 0)
            {
                foreach(string id in LuaToCshapeManager.Instance.SidekickEquipmentIDs)
                {
                    if(id != "")
                    {
                        var sidekick = MedicamentScheme.Instance.GetItem(int.Parse(id));
                        var monster = MonsterNewScheme.Instance.GetItem(sidekick.HCtips);
                        int sideType = sidekick.GroupID - 901;
                        Sidekick side = SpawnSidekick(sideType);
                        side.monsterNew = monster;
                        side.damage = monster.PhysicsAttack;
                        levelSidekick.Add(side);
                        //这个下次打整包的时候要从父类去继承
                        //if (side is WXR_Bowie)
                        //{
                        //    ((WXR_Bowie)side).SetMonsterData();
                        //}
                        if (side is WXR_Zapper)
                        {
                            ((WXR_Zapper)side).SetMonsterData();
                        }
                    }
                }   
                //Equipment.Item CurrentEquipment = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.SidekickEquipmentID);
                //int CurrentGroupID = CurrentEquipment.GroupID - 10001;
                //Globals.sidekickType = (SidekickType)CurrentGroupID; //(SidekickType)PlayerPrefs.GetInt("Category5");
                //levelSidekick = SpawnSidekick((int)Globals.sidekickType);
            }
        }
        //TODO PlayerHUD
        // _playerController._playerHud = _hudLayer.getPlayerHud();


        if (Configuration.ENEMIES)
        {
            int fillerEnemySpawnTime = missionManager.GetFillerEnemy();

            if (fillerEnemySpawnTime != 0)
            {
                DOTween.Sequence().SetId(tweenId).AppendInterval(fillerEnemySpawnTime).AppendCallback(() =>
                {

                    if (GameData.instance.fileHandler.currentMission != 0)
                    {
                        SpawnFillerEnemies(fillerEnemySpawnTime);
                    }
                }).Play();
            }
        }



        int powerUpSpawnTime = missionManager.GetSpawnPowerUp();

        if (powerUpSpawnTime != 0)
        {
            DOTween.Sequence().SetId(tweenId).AppendInterval(powerUpSpawnTime).AppendCallback(GeneratePowerUp).SetLoops(3).Play();
        }


#if UNITY_IOS || UNITY_ANDROID
        //if (GameData.instance.fileHandler.currentMission > 0)
        //{
        //    InputController.instance.ShowMobileControlsHud();
        //}
#endif

        //TODO InputController
        //#if Desktop
        //    auto keylistener = EventListenerKeyboard::create();
        //    keylistener.onKeyPressed = CC_CALLBACK_2(GameController::onKeyPressed, this);
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);
        //#endif

        //#if CC_TARGET_PLATFORM == CC_PLATFORM_IOS
        //        auto keyListener = EventListenerKeyboard::create();
        //        keyListener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);
        //#endif
    }

    private void InitPlayer()
    {

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            print("Setting Assist Mode");
            PlayerPrefs.SetInt("isAssistMode", 1);
            Globals.isAssistMode = true;

        }
#if UNITY_STANDALONE
        //TODO InputController SetUp
        //_controls = DesktopControls::create();
        //this.addChild(_controls);


        //_controls2 = ControllerControls::create();
        //this.addChild(_controls2);
        //static_cast<ControllerControls*>(_controls2).setCursor(static_cast<DesktopControls*>(_controls)._cursor2);

        Observer.DispatchCustomEvent("disable_movement");
        Observer.DispatchCustomEvent("disable_rotation");
        //(static_cast<DesktopControls*>(_controls)).DeActivateControls();


        if (Globals.isJoystickConnected)
        {

            //(static_cast<DesktopControls*>(_controls)).DeActivateControls();

        }
        else
        {
            //static_cast<ControllerControls*>(_controls2).DeActivateControls();

        }
#else
        // check if tvOS or iOS
#if UNITY_TVOS
//TODO InputController SetUp
        //_controls = ControllerControls::create();
        //this.addChild(_controls);
        //if (GameData.instance.fileHandler.currentMission == 0)
        //{
        //    _controls = MobileAssistControls::create();
        //    this.addChild(_controls);
        //    //        Director::getInstance().getEventDispatcher().dispatchCustomEvent("DeActivate_Mobile_Assist_Controls");

        //}
#else
        //TODO InputController SetUp
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            //_controls = MobileAssistControls::create();
            //      this.addChild(_controls);
        }
        else
        {
            if (Globals.isAssistMode && Globals.gameType != GameType.Survival)
            {

                //_controls = MobileAssistControls::create();
                //this.addChild(_controls);
                Observer.DispatchCustomEvent("DeActivate_Mobile_Touch_Controls");
//               if (!Globals.isTutorial)
//                    InputController.instance.DisableControlsInstant();


            }
            else
            {
                //_controls = MobileTouchControls::create();
                //this.addChild(_controls);

            }
        }

        //_controls2 = ControllerControls::create();
        //this.addChild(_controls2);

        if (GameData.instance.fileHandler.currentMission != 0)
        {
            if (Globals.isJoystickConnected)
            {
                if (Globals.isAssistMode)
                {
                    if (Globals.gameType != GameType.Survival)
                    {
                        InputController.instance.DeActivateControls();
                    }
                }
                else
                {
                    InputController.instance.DeActivateControls();

                }
            }

        }


#endif
#endif



    }

    private void InitBackground()
    {
        //    _randomBackground = GameData::getInstance().getMissions(GameData.instance.fileHandler.currentMission).at("Background").asInt();
        randomBackgroundType = missionManager._backgroundType;
        if (missionManager.missionType == Globals.MissionTypeVolcanoMission && Globals.gameType == GameType.Training)
        {
            volcanoMission.Create();

            DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
            {
                //float pointsRequired = missionManager.totalPointsRequired;
                //if (Globals.gameModeType == GamePlayMode.Easy)
                //{
                //    pointsRequired = pointsRequired * 0.5f;

                //}
                //else if (Globals.gameModeType == GamePlayMode.Hard)
                //{
                //    pointsRequired = pointsRequired * 1.35f;

                //}
                //missionManager.totalPointsRequired = (int)pointsRequired;

                volcanoMission.StartMission();
                timerHUD.CreateWithTime(missionManager.totalPointsRequired);
                timerHUD.StartTimer();

            }).Play();

        }
        //if (randomBackgroundType > 0)
        //{
        //    backgroundController.Init(randomBackgroundType);
        //}
        //else
        //{
        //    backgroundController.Init(1 + Globals.playCount % 8);
        //}


        //if (missionManager.missionType == Globals.MissionTypeVolcanoMission && Globals.gameType == GameType.Training)
        //{
        //    volcanoMission.AddVolcanoToBg(backgroundController);
        //}
        Globals.g_bgType = (int)BackgroundController.backgroundType;


        if (Globals.gameType == GameType.Training)
        {
            Globals.spawnBoss = false;
            Globals.isBossMode = false;
        }
        else if (Globals.gameType == GameType.Arena)
        {
            Globals.spawnBoss = true;


            if (Globals.allowDialogueFromBoss)
            {
                //TODO With Zaeem
                //                _bg.runAction(Sequence::create(CallFunc::create([]{
                //#if CC_TARGET_PLATFORM == CC_PLATFORM_IOS

                //                    if (!isJoystickConnected)
                //                    {
                //                        //                    Director::getInstance().getEventDispatcher().dispatchCustomEvent("DeActivate_Mobile_Touch_Controls");

                //                    }

                //#endif
                //                }), DelayTime::create(1),CallFunc::create(CC_CALLBACK_0(GameController::SpawnBoss, this)),DelayTime::create(2), CallFunc::create([]{
                //#if CC_TARGET_PLATFORM == CC_PLATFORM_IOS

                //                    if (!isJoystickConnected)
                //                    {
                //                        //                    Director::getInstance().getEventDispatcher().dispatchCustomEvent("Activate_Mobile_Touch_Controls");

                //                    }
                //#endif
                //                }), NULL));
            }
            else
            {

                //_bg.runAction(Sequence::create(DelayTime::create(2), CallFunc::create(CC_CALLBACK_0(GameController::SpawnBoss, this)), NULL));
            }
            Globals.allowDialogueFromBoss = false;
            Globals.isBossMode = true;
        }
    }

    private void InitMissionManager()
    {
        missionManager.Init();
    }

    public void StartGame()
    {
        if (GameData.instance.fileHandler.currentMission != 0)
        {
            //Debug.Log("通知lua开始战斗");
            LuaManager.Instance.RunLuaFunction<int>("BattleManager.StartBattle",(int)Globals.gameType);
        }

        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(400));
        Globals.resetControls = false;
        //Globals.focusOnPlayer = false;
        timeManager.ResetTimescale();

        if (GameData.instance.fileHandler.currentMission != 0 && levelSidekick.Count > 0)
        {
            foreach(var side in levelSidekick)
            {
                side.StartSidekick();
            }
            //levelSidekick.StartSidekick();
        }

        InitSchedulersAndWaves();
        //12boos 以上是没有对话的
        if (Globals.isBossMode && GameData.instance.fileHandler.currentEvent < 13)
            return;

        StartGameplay();
    }

    public void StartGameplay()
    {
        Globals.gameplayStarted = true;
        playerHud.Init();
        EnableControls();
        Observer.DispatchCustomEvent("InitBossHud");
    }

    public void HideBackground()
    {
        Camera.main.cullingMask &= ~(1 << LayerMask.NameToLayer("BackgroundLayer"));
    }

    public void EnableBackground()
    {
        Camera.main.cullingMask |= 1 << LayerMask.NameToLayer("BackgroundLayer");
    }

    private void DisableBackground()
    {
        //backgroundController.DisableBackground();
    }

    private void Update() // TODO REMOVE
    {
        if (scheduleUpdate)
        {
            // if (UnityEngine.Input.GetKeyUp(KeyCode.Escape))
            // {
            //     pauseMenu.Show();

            // }

            {


                if (Globals.resetControls)
                {
                    //Globals.resetControls = false;
                    Globals.thrust = false;
                    Globals.allowShoot = false;
#if UNITY_STANDALONE
                    //TODO Look into it
                    //if(controls){
                    //static_cast <DesktopControls *>(_controls).resetControls();
                    //}
#endif


                    //player.SetPlaneIdle();
                    //player.weapon.SetShootMode(false);
                }
            }
            if (runTimerUpdate)
            {
                EndTimerUpdate();
            }
        }
    }


    public void PlayPlayerLoseSequence()
    {
        if (!failSequencePlayed)
        {
            failSequencePlayed = true;
            Globals.zoomInForSec = 10;
            levelfailIris.gameObject.SetActive(true);
            DOTween.Sequence().SetId(tweenId).Append(levelfailIris.transform.DOScale(Vector2.one * 2.5f, 2).SetEase(Ease.OutExpo)).Play();
            endTimer.fillAmount = 1;
            runTimerUpdate = true;
        }
    }


    private void EndTimerUpdate()
    {
        if (endTimer)
        {


            // _endTimer.setPercentage(_endTimer.getPercentage() - 0.3f);

            //#if Desktop
            endTimer.fillAmount -= 2 * Time.deltaTime * 0.60f;
            //#endif
            if (endTimer.fillAmount < 0.01)
            {
#if UNITY_ANDROID || UNITY_IOS
                InputController.instance.HideMobileControlsHud();
#endif
                runTimerUpdate = false;
                scheduleUpdate = false;

                xpThisRun = GameData.instance.fileHandler.playerXP - Globals.sessionStartXP;
                float timeForTransition = 0.0f;
                levelfailExplosionTransition.SetActive(true);
                DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
                {
                    DisableBackground();
                }).Play();
                DOTween.Sequence().SetId(tweenId).AppendInterval(timeForTransition + 0.12f).AppendCallback(() =>
                {
                    gameOverMenu.totalCoinsCount = coinsThisRun;
                    gameOverMenu.totalKillsCount = killsThisRun;
                    gameOverMenu.GameOverSequence();
                    AudioManager.instance.StopAllSoundEffects();
                    AudioManager.instance.PauseMusic();
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.gameOverExplosionTransition, 0.5f);
                    //Globals.PlaySound("res/Sounds/SFX/InProgress/gameOverExplosionTransition.mp3", false, 0.5f);
                }).Play();
                //player.weapon.SetAllowShoot(false);
                //_hudLayer.setVisible(false);
                //_bg.runAction(Sequence::create(DelayTime::create(1), Hide::create(), NULL));
                //Sprite* iris = (Sprite*)this.getChildByTag(555);

                DOTween.Sequence().SetId(tweenId).AppendInterval(0.12f).AppendCallback(() =>
                {
                    levelfailIris.SetActive(false);
                }).Play();
                //player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_DEAD;
                //Player::getStats().mode = Player::PLAYER_MODE_DEAD;
                //SkeletonAnimation* parachute = (SkeletonAnimation*)Player::getInstance().getChildByTag(15);
                //if (parachute)
                //{
                //    parachute.setAnimation(0, "chuteDeath", false);
                //}

            }
        }
    }

    public Sidekick SpawnSidekick(int type)
    {
        bool bought = true;// PlayerPrefs.GetInt(((SidekickType)type).ToString()) == 1;

        if (!bought && GameData.instance.fileHandler.currentMission > 0)
            return null;

        var sidekick = Instantiate(sidekickList[type]);
        return sidekick;
    }

    private void LateUpdate()
    {


        if (!scheduleUpdate)
            return;
        Scheduler.CallScheduledActions();

    }

    public GameObject InstantiatePrefab(string name)
    {
        var prefabData = prefabs.FirstOrDefault(p => p.name == name);

        if (prefabData.prefab != null)
        {
            GameObject instantiatedGO = Instantiate(prefabData.prefab);

            if (prefabData.parentTransform != null)
            {
                instantiatedGO.transform.SetParent(prefabData.parentTransform);
            }

            return instantiatedGO;
        }

        return null;
    }

    void InitSchedulersAndWaves()
    {
        scheduleUpdate = true;
        if (missionManager.missionType == "Boss") // Check condition for Boss level TODO
        {
            //GameData.instance.fileHandler.currentEvent = 3; // REMOVE TODO
            BossFactory.Create();
        }

        if (Globals.ENEMIES)
        {
            eFactory = EnemyFactory.Create();
            eFactory.transform.parent = transform;
            missionManager.PickMission(eFactory, missionManager.missionType);
            //print(missionManager.missionType);
            //missionManager.PickMission(eFactory,)
        }

        if (GameData.instance.fileHandler._frontGunStr == Globals.kLaser)
        {
            // TODO
            //this.schedule(schedule_selector(GameController::laserLogic), 0, CC_REPEAT_FOREVER, 0.5f);

            //laserSound = Shared::playSound("res/Sounds/SFX/laserLoop.mp3");
            //experimental::AudioEngine::setLoop(laserSound, true);
            //experimental::AudioEngine::setVolume(laserSound, 0.0f);
        }

        // TODO
        //this.scheduleUpdate();
        //this.runAction(Sequence::create(DelayTime::create(0.01f), CallFunc::create(CC_CALLBACK_0(GameController::showUtilityMenu, this)), NULL));
    }

    private void SpawnAllEnemies()
    {
        foreach (EnemyData e in enemyData)
        {
            SpawnEnemy(e.name, Vector2.zero);
        }
    }

    public Enemy SpawnEnemy(string enemyType, Vector2? position = null, bool dontInitialize=false)
    {
        var enemyInfo = enemyData.FirstOrDefault(e => e.name == enemyType);
        if (!enemyInfo.enemy)
            return null;

        var enemy = Instantiate(enemyInfo.enemy);

        if (dontInitialize)
            return enemy;

        if (position == null)
        {
            enemy.Init();
            return enemy;
        }

        enemy.InitWithPosition(position);
        return enemy;
    }

    private void SpawnEnemyOld()
    {
        if (spawnEnemies)
        {
            if (GameSharedData.Instance.enemyList.Count < 8)
            {
                float xVal;
                if (Random.value < 0.5f)
                {
                    xVal = (Random.Range(-20, -10));
                }
                else
                {
                    xVal = (Random.Range(10, 20));
                }
                Vector2 spawnLocation = new Vector2(player.transform.position.x + xVal, 0);
                if (waveCount < 5)
                {
                    if (hammerheadSpawned < waveData[waveCount].hammerHeadCount)
                    {
                        Enemy.Instantiate(seversky[0], spawnLocation, Quaternion.identity, null);
                        hammerheadSpawned++;
                    }
                    else if (trackerSpawned < waveData[waveCount].trackerCount)
                    {
                        Enemy.Instantiate(seversky[1], spawnLocation, Quaternion.identity, null);
                        trackerSpawned++;
                    }
                    else if (dozerSpawned < waveData[waveCount].dozerCount)
                    {
                        Enemy.Instantiate(seversky[2], spawnLocation, Quaternion.identity, null);
                        dozerSpawned++;
                    }
                    else if (blastorSpawned < waveData[waveCount].blastorCount)
                    {
                        Enemy.Instantiate(seversky[3], spawnLocation, Quaternion.identity, null);
                        blastorSpawned++;
                    }
                    else if (skyrunnerSpawned < waveData[waveCount].skyRunnerCount)
                    {
                        Enemy.Instantiate(seversky[4], spawnLocation, Quaternion.identity, null);
                        skyrunnerSpawned++;
                    }
                    else
                    {
                        waveCount++;
                        skyrunnerSpawned = 0;
                        dozerSpawned = 0;
                        blastorSpawned = 0;
                        trackerSpawned = 0;
                        hammerheadSpawned = 0;
                    }
                    if (Random.Range(0, 100) % 2 == 0)
                    {
                        spawnLocation = new Vector2(player.transform.position.x + xVal, 0);
                        Enemy.Instantiate(seversky[5], spawnLocation, Quaternion.identity, null);
                    }
                }
                else
                {
                    if (Random.Range(0, 100) % 2 == 0)
                    {
                        spawnLocation = new Vector2(player.transform.position.x + xVal, 0);
                        Enemy.Instantiate(seversky[5], spawnLocation, Quaternion.identity, null);
                    }
                    if (bossCount < boss.Length && bossObject == null)
                    {
                        bossObject = Enemy.Instantiate(boss[bossCount]);
                        bossCount++;
                    }
                }
            }
        }
        if (spawnOnlyBoss && !spawnEnemies)
        {
            if (bossCount < 3 && bossObject == null)
            {
                bossObject = Enemy.Instantiate(boss[bossCount]);
                bossCount++;
            }
        }
    }


    private void SkipTutorial()
    {

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {
                if (Globals.isTutorial)
                {
                    if (Globals.isAssistMode)
                    {
                        controlsTutorialManager.ChangeState(TutorialState.SkipTutorial);

                    }

                }
                else
                {
                    SkipFTUX();
                }
            }

        }
    }

    public void ToggleDarkEffect(bool on)
    {
        //backgroundController.ToggleDarkEffect(on);
    }

    public void ShakeCamera(float intensity, int numberOfShakes = 1)
    {
        if(GameData.instance.fileHandler.currentMission == 0)
        {
            cameraMovement.ShakeCamera(intensity, numberOfShakes);
        }else
        {
            if (Random.Range(0, 10000) <= ConstValueScheme.Instance.GetItem(128).Value)
            {
                cameraMovement.ShakeCamera(intensity, numberOfShakes);
            }
        }
    }

    public void HitWater(Vector3 position, float forceFactor = 1)
    {
        foreach (Water water in waters)
        {
            water.HitWater(position, forceFactor);
        }

        SpawnSplash(position, forceFactor);
    }

    public void SpawnSplash(Vector3 position, float scaleFactor)
    {
        Explosion waterExplosion = null;

        bool didFindExplosion = false;
        foreach (Explosion exp in GameSharedData.Instance.waterExplosionsList)
        {
            if (!exp.IsInUse)
            {
                waterExplosion = exp;
                didFindExplosion = true;
                break;
            }

        }
        if (!didFindExplosion)
        {
            return;
        }

        waterExplosion.skeletonAnimation.timeScale = 1.0f;
        waterExplosion.GetComponent<MeshRenderer>().sortingLayerName = "Foreground";
        waterExplosion.GetComponent<MeshRenderer>().sortingOrder = -1;
        waterExplosion.transform.SetScale(scaleFactor);
        Color skeletonColor = waterExplosion.skeletonAnimation.skeleton.GetColor();
        waterExplosion.skeletonAnimation.skeleton.SetColor(
            new Color(skeletonColor.r, skeletonColor.g, skeletonColor.b, 180f / 255f));
        waterExplosion.transform.SetWorldPosition(
            position.x + Globals.CocosToUnity(10),
            Globals.LOWERBOUNDARY - 0.3f - scaleFactor * 0.25f);
        int var = 1;

        if (BackgroundController.backgroundType == BackgroundController.BackgroundType.Day
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.AtlantisDay
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.Waterfall)
            var = 1;
        else if (BackgroundController.backgroundType == BackgroundController.BackgroundType.UnderWater
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.AtlantisUnderwater
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.Snow)
            var = 4;
        else if (BackgroundController.backgroundType == BackgroundController.BackgroundType.Rain
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.Night
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.DarkClouds
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.Darkness)
            var = 2;
        else if (BackgroundController.backgroundType == BackgroundController.BackgroundType.Dusk
            || BackgroundController.backgroundType == BackgroundController.BackgroundType.FTUE)
            var = 3;
        else if (BackgroundController.backgroundType == BackgroundController.BackgroundType.Twilight)
            var = 5;
        else if (BackgroundController.backgroundType == BackgroundController.BackgroundType.Volcano)
            var = 7;
        //waterExplosion.PlayAnimation("bg" + var);
        DOTween.Sequence().AppendInterval(0.5f).AppendCallback(waterExplosion.Reset);
    }

    public void OnRestart()
    {
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
        SceneManager.LoadScene(0);
    }

    public void SpawnEnemyOnButton(int enemyIndex)
    {
        float xVal;
        if (Random.value < 0.5f)
        {
            xVal = (Random.Range(-20, -10));
        }
        else
        {
            xVal = (Random.Range(10, 20));
        }
        Vector2 spawnLocation = new Vector2(player.transform.position.x + xVal, 0);
        if (enemyIndex == 15)
        {
            SpawnSpiderBulletOnButton();
            return;
        }
        Enemy e = Enemy.Instantiate(allEnemies[enemyIndex], spawnLocation, Quaternion.identity, null);

    }

    public void SpawnSpiderBulletOnButton()
    {
        float xVal;
        if (Random.value < 0.5f)
        {
            xVal = (Random.Range(-20, -10));
        }
        else
        {
            xVal = (Random.Range(10, 20));
        }
        Vector2 spawnLocation = new Vector2(player.transform.position.x + xVal, 0);
        Enemy e = Enemy.Instantiate(allEnemies[15], spawnLocation, Quaternion.identity, null);
        e.SpawnSpiderBulletOnButton();

    }

    private void GenerateParticles()
    {
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, Vector3.zero, false, 1, 0.5f, 1.4f);
    }

    public void SpawnDebris()
    {
        GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
        Gibs debris = go.GetComponent<Gibs>();
        debris.isInUse = true;
        debris.transform.position = Vector3.zero;
        debris.CreateWithData(600, 7, false, false);
    }

    public void MissionCompleteCall()
    {
#if UNITY_ANDROID|| UNITY_IOS
        InputController.instance.HideMobileControlsHud();
#endif
        EndAutoShoot();
        EndAutoShootAB();
        Globals.resetControls = true;
        //log("%s", Director::getInstance().getTextureCache().getCachedTextureInfo().c_str());

        LuaManager.Instance.RunLuaFunction("BattleManager.CloseSillView");
        //TODO

        foreach (Coin coin in GameSharedData.Instance.coinsInUse)
        {
            coin.RemoveCoin();
        }

        //if (enableEndGame == true || Globals.isMissionComplete == true)
        {
            //CCLOG("mission comepleted");
            enableEndGame = false;
            //logTest();
            //GameManager.instance.player.Stats.mode = Player::PLAYER_MODE_ENDMISSION;
            player.Stats.absorb = 100000;
            if (player.Stats.health < player.Stats.maxHealth.Value * 0.3f)
            {
                player.Stats.health = player.Stats.maxHealth.Value * 0.4f;
            }
            Globals.SetZoomValueWhileGame(0.2f);
            player.SetSideKickShoot(false);
            foreach (Sidekick sk in GameSharedData.Instance.sidekicksList)
            {
                sk.speed = 30;
            }

            foreach (Enemy enemy in GameSharedData.Instance.enemyList)
            {
                enemy.SetAllowPing(false, PINGTYPE.ENEMY_ALERT);
            }
            if (eFactory)
            {
                eFactory._isEnabled = false;
            }
            DOTween.To(() => Globals.RIGHTBOUNDARY, x => Globals.RIGHTBOUNDARY = x, Globals.RIGHTBOUNDARY + Globals.CocosToUnity(2000), 1);
            //this.runAction(ActionFloat::create(1.0f, RIGHTBOUNDARY, RIGHTBOUNDARY + 2000, [=](float a){
            //    RIGHTBOUNDARY = a;
            //}));

            ManageStars();
            DOTween.Sequence().SetId(tweenId).AppendInterval(1.5f).AppendCallback(() =>
            {
                AudioManager.instance.StopAllSoundEffects();
                if (GameData.instance.fileHandler.currentMission == 0)
                {
                    AudioManager.instance.PauseMusic();
                }
                else
                {
                    LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPauseMusic");
                }
                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.victory);
            }
            ).AppendInterval(2.0f).AppendCallback(() =>
            {
                levelCompleteEnergyTransition.SetActive(true);
            }).AppendInterval(0.5f).AppendCallback(() =>
            {
                DisableBackground();
            }).Play();

            player.TurnOnBoost(true);
            DOTween.Sequence().SetId(player.tweenId).Append(player.transform.DOMove(new Vector2(player.transform.position.x, 3), 1).SetEase(Ease.InOutSine))
                .AppendCallback(() =>
                {
                    player.TurnOnBoost(false);
                }).AppendInterval(0.9f).AppendCallback(() =>
                {
                    player.dashSmall.SetActive(true);
                    DOTween.Sequence().SetId("Dash").Append(player.dashSmall.transform.DOScale(new Vector2(15, 10), 0.1f)).Play();



                }).AppendInterval(0.1f).AppendCallback(() =>
                {

                    //Director::getInstance().getScheduler().unschedule("shake", this);
                    speedLines.SetActive(true);

                    DOTween.Sequence().SetId("speedLines").Append(speedLines.GetComponent<SkeletonGraphic>().DOFade(1, 1)).AppendInterval(0.8f).AppendCallback(() =>
                     {

                         //_bg.setVisible(false);
                         //_playerController.setVisible(false);
                         bg.gameObject.SetActive(false);
                         speedLines.gameObject.SetActive(false);
                         player.gameObject.SetActive(false);
                     }).Play();
                    Globals.RIGHTBOUNDARY = Globals.CocosToUnity(500000);
                    bg.gameObject.SetActive(true);
                    bg.DOColor(new Color(0.09803922f, 0.09803922f, 0.09803922f, 0.5f), 2.2f);
                    //_bg.runAction(TintTo::create(2.2f, 25, 25, 25));
                    for (int i = 0; i < 25; i++)
                    {
                        physicsManager.PlayerCollisionCollectorUpdate();
                    }

                }).Append(player.transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(15000), 0), 2.5f)).Play();

            DOTween.Sequence().SetId(tweenId).AppendInterval(4).AppendCallback(EndGame).Play();
            //Player::getInstance().runAction(EaseSineInOut::create(RotateTo::create(1.0f, 180)));
            //auto scheduler = Director::getInstance().getScheduler();
            //shakeIntensity = 2;


            //        _bg.unscheduleUpdate();
            //        _bg.unscheduleAllCallbacks();
            float shakeIntensity = 0;
            StartCoroutine(Shake());
            IEnumerator Shake()
            {
                while (player.gameObject.activeSelf)
                {
                    shakeIntensity += Globals.CocosToUnity(0.075f);
                    shakeIntensity = Mathf.Clamp(shakeIntensity, 0, Globals.CocosToUnity(50f));
                    ShakeCamera(shakeIntensity, 1);
                    yield return null;
                }
            }
            //scheduler.schedule([](float dt)
            //                    {
            //    shakeScreen = 2;

            //    shakeIntensity += 0.075f;
            //    shakeIntensity = clampf(shakeIntensity, 0, 50);
            //    playerAcceleration = cocos2d::Point::ZERO;
            //}, this, 0.0f, kRepeatForever, 1.0f, false, "shake");

            //_controls.setVisible(false);
        }
    }

    public void ManageStars()
    {
        string ch = "MissionStar" + GameData.instance.fileHandler.currentMission;
        int currentStarsEarned = 0;

        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            currentStarsEarned = 1;
        }
        else if (Globals.gameModeType == GamePlayMode.Medium)
        {
            currentStarsEarned = 2;
        }
        else if (Globals.gameModeType == GamePlayMode.Hard)
        {
            currentStarsEarned = 3;
        }

        if (PlayerPrefs.GetInt(ch, 0) < currentStarsEarned)
        {
            PlayerPrefs.SetInt(ch, currentStarsEarned);
            PlayerPrefs.SetInt("ShowMissionStarUnlock", 1);
            PlayerPrefs.SetInt("ShowMissionStarUnlockNumber", GameData.instance.fileHandler.currentMission);
            //UserDefault::getInstance().flush();
        }
    }

    public void EndGame()
    {
        gameOverMenu.gameObject.SetActive(true);
        gameOverMenu.Init();
        gameOverMenu.totalCoinsCount = coinsThisRun;
        gameOverMenu.totalKillsCount = killsThisRun;

        gameOverMenu.isBossDefeated = true;

        gameOverMenu.GameOverSequence();

        xpThisRun = GameData.instance.fileHandler.playerXP - Globals.sessionStartXP;

#if UNITY_STANDALONE

        //TODO
        //gameOverMenu.setCameraMask(HUDCAMERA);
#endif
        //TODO
        //player.setAllowShoot(false);
        //_bg.runAction(Sequence::create(DelayTime::create(1), Hide::create(), NULL));


    }
    //清除所有影响战斗结束的对象
    public void ClearAllBattle()
    {       
        player.gameObject.SetActive(false);
        eFactory.StopAllCoroutines();

        foreach (MonoBehaviour script in GameSharedData.Instance.allBattleScript)
        {
            if (script != null && script.gameObject != null && script.gameObject.activeInHierarchy)
            {
                script.StopAllCoroutines();
                script.CancelInvoke();
                script.gameObject.SetActive(false);
            }
        }

        foreach (Bullet bullet in GameSharedData.Instance.enemyBulletPool)
        {
            bullet.StopAllCoroutines();
            bullet.gameObject.SetActive(false);
        }
    }

    public void GameOver()
    {
        
        //    this.unschedule(schedule_selector(GameController::laserLogic));
        int rewardCoins = 0;
        int xpReceivedFromMission = 0;
        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            rewardCoins = missionManager.rewardInCoins / 2;
            xpReceivedFromMission = missionManager.rewardInXp / 2;
            //log("easy mode reward");
        }
        if (Globals.gameModeType == GamePlayMode.Medium)
        {
            rewardCoins = missionManager.rewardInCoins;
            xpReceivedFromMission = missionManager.rewardInXp;
            //log("medium mode reward");

        }
        if (Globals.gameModeType == GamePlayMode.Hard)
        {
            rewardCoins = missionManager.rewardInCoins * 2;
            xpReceivedFromMission = missionManager.rewardInXp * 2;
            //log("hard mode reward");

        }
        if (Globals.gameType == GameType.Arena)
        {
            if (!Globals.isBossMode)
            {

                if (Globals.UPPERBOUNDARY < Globals.CocosToUnity(1500))
                {
                    //                UPPERBOUNDARY = 1500;
                    Globals.SetZoomValueWhileGame(Globals.CocosToUnity(300));

                }
                player.Stats.absorb = 15000;

                if (missionManager.missionType == Globals.MissionTypeBoss && missionManager.missionBossNumber == GameData.instance.fileHandler.currentEvent)
                {
                    //TODO Achievement
                    //#if !NSTEAM
                    //                    FileHandler::getInstance().unlockAchievement("grp.com.werplay.explottens.Boss" + to_string(MissionManager::getInstance().missionBossNumber));
                    //#endif


                    //                    FileHandler::getInstance().unlockAchievement("com.werplay.explottens.Boss" + to_string(MissionManager::getInstance().missionBossNumber));

                    missionManager.MissionComplete();
                }
                //            isBossMode = true;

                float timeDelay = 0.5f;
#if UNITY_STANDALONE
                //TODO BossFIght End
                //_hudLayer.endBossFight();

                timeDelay = 0.1f;
                enableEndGame = true;
                Globals.isMissionComplete = true;
                Globals.bossShouldStayOnScreen = false;
                //player.lowHealthSprite.SetActive(false);
                DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
                {
                    coinsThisRun += rewardCoins;
                    //TODO Add Hud Layer
                    //_hudLayer.addXpAndCoinsWithAnimation(rewardCoins, xpReceivedFromMission);

                }).AppendInterval(5.0f).AppendCallback(SpawnEndingDialogues).AppendInterval(timeDelay).AppendCallback(MissionCompleteCall).Play();
                return;
#else
                timeDelay = 0.1f;
                enableEndGame = true;
                Globals.isMissionComplete = true;
                Globals.bossShouldStayOnScreen = false;
                //player.lowHealthSprite.SetActive(false);
                DOTween.Sequence().SetId(tweenId).AppendInterval(5.5f).AppendCallback(SpawnEndingDialogues).AppendInterval(timeDelay).AppendCallback(MissionCompleteCall).Play();
#endif
            }
        }

        if (Globals.gameType == GameType.Training)
        {
            player.Stats.absorb = 15000;


            float timeDelay = 0.5f;
#if UNITY_STANDALONE
            timeDelay = 0.1f;
            enableEndGame = true;
            Globals.isMissionComplete = true;
            Globals.bossShouldStayOnScreen = false;
            //_hudLayer.endBossFight();//TODO Boss Fight
            //player.lowHealthSprite.SetActive(false);

            DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
            {
                coinsThisRun += missionManager.rewardInCoins;
                //TODO Fixes With HUD Layer
                //hudLayer.addXpAndCoinsWithAnimation(xpReceivedFromMission, rewardCoins);
                //hudLayer.setLocalZOrder(10000);
                //hudLayer.setPositionZ(1);


            }).AppendInterval(2.0f).AppendInterval(timeDelay).AppendCallback(MissionCompleteCall).Play();
            return;
#else
            coinsThisRun += rewardCoins;
            Globals.isMissionComplete = true;
            DOTween.Sequence().SetId(tweenId).AppendInterval(1.0f).AppendCallback(()=>{
                //CCLOG("call mission complete");
                MissionCompleteCall();
            }).Play();
#endif
        }

        if (Globals.gameType == GameType.Survival)
        {
            player.Stats.absorb = 15000;


            float timeDelay = 0.5f;

            timeDelay = 0.1f;
            enableEndGame = true;
            Globals.isMissionComplete = true;
            Globals.bossShouldStayOnScreen = false;
            //_hudLayer.endBossFight();//TODO Boss Fight
            //player.lowHealthSprite.SetActive(false);

            DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
            {
                coinsThisRun += missionManager.rewardInCoins;
                //TODO Fixes With HUD Layer
                //hudLayer.addXpAndCoinsWithAnimation(xpReceivedFromMission, rewardCoins);
                //hudLayer.setLocalZOrder(10000);
                //hudLayer.setPositionZ(1);


            }).AppendInterval(2.0f).AppendInterval(timeDelay).AppendCallback(MissionCompleteCall).Play();
            return;

        }
    }

    public void SpawnEndingDialogues()
    {
        //TODO Manage Dialogues
        //DialoguePopUp* d = DialoguePopUp::createAsPostBattle();
        //this.addChild(d);
        print("Ending");
        Observer.DispatchCustomEvent("SpawnEndingDialogues");
    }

    public void FtueEnd()
    {
        InputController.instance.enabled = false;
        scheduleUpdate = false;
        deathControls.Init();
        playerHud.HideHud();
    }

    public void AddXp(int levelID, int xp)
    {
        //原版加经验
        //GameData.instance.fileHandler.playerXP += xp;


        //GameData.instance.fileHandler.totalXP += xp;


        //GameData.instance.fileHandler.TrainingLevel = GameData.instance.fileHandler.playerLevel;


        //if (Globals.gameType == GameType.Training)
        //{
        //    GameData.instance.fileHandler.TrainingPoints += xp;


        //}

        //if (GameData.instance.fileHandler.playerXP >= GameData.instance.fileHandler.xpRequiredThisLevel)
        //{
        //    PlayerLevelUp();
        //}

        //player.playerHud.UpdateXpBar();


        _curExp = xp;
        if (_curPlayerLevelID != levelID)
        {
            _curPlayerLevelID = levelID;
            _curLevelData = CatsKillLevelUpScheme.Instance.GetItem(_curPlayerLevelID);
            _curMaxExp = 0;
            for (int i = 1; i <= _curPlayerLevelID; i++)
            {
                _curMaxExp += CatsKillLevelUpScheme.Instance.GetItem(i).ExpSkill;
            }
            player.LevelUp();
        }
        //player.playerHud.UpdateLevel(_curLevelData.Id);
        //player.playerHud.UpdateXpBar(_curExp / _curMaxExp);
    }

    public void PlayerLevelUp()
    {
        //TODO Add in Player COntroller and HUD
        //player.LevelUp();
        //player.playerHud.UpdateLevel();
    }

    private void OnDestroy()
    {
        DOTween.KillAll();
    }

    public void SkipButtonCall()
    {
        {

            if (Globals.isGameInTutorialState)
            {
                if (Globals.isAssistMode)
                {
                    controlsTutorialManager.ChangeState(TutorialState.SkipTutorial);
                }

            }
            else
            {
                if (!Globals.skipFTUX)
                {
                    //TODO
                    //_hudLayer.skipButton.setVisible(false);
                    //_hudLayer.skipButton.setEnabled(false);
                    Globals.skipFTUX = true;
                    Globals.introComplete = true;
                    Death();
                }


            }
        }
    }

    private void SpawnFillerEnemies(float time)
    {
        if (Globals.isMissionComplete)
        {
            return;
        }

        int numberOfOnes = 0;
        int numberOfTwos = 0;

        if (GameData.instance.fileHandler.playerLevel < 6)
        {

            numberOfOnes = Random.Range(0, 3);

        }

        else
        {


            if (Random.value * 100 < GameData.instance.fileHandler.playerLevel + 5)
            {
                numberOfTwos = Random.Range(0, 2);
            }
            else
            {
                numberOfOnes = Random.Range(0, 3);
            }
        }

        for (int i = 0; i < numberOfOnes; i++)
        {
            BugBot bugBot = (BugBot)SpawnEnemy("BUGBOT");
            bugBot.isAFillerEnemy();
            bugBot.Restat();
        }


        for (int i = 0; i < numberOfTwos; i++)
        {
            BugBot bugBot2 = (BugBot)SpawnEnemy("BUGBOT2");
            bugBot2.isAFillerEnemy();
            bugBot2.Restat();
        }
        DOTween.Sequence().SetId(tweenId).AppendInterval(time + Random.value * 2).AppendCallback(() =>
        {
            SpawnFillerEnemies(time);
        }).Play();
    }

    private void SkipFTUX()
    {
        if (!Globals.skipFTUX)
        {
            Globals.skipFTUX = true;
            Globals.isTutorial = false;
            Globals.unlockTapShoot = true;
            controlsTutorialManager.SetJoystickFirebutton(false, "");
            controlsTutorialManager.SetMovementControls(false, "");
            Globals.introComplete = true;
            Death();

        }
    }

    private void Death()
    {
#if UNITY_IOS || UNITY_ANDROID

        InputController.instance.HideMobileControlsHud();
#endif
        InputController.instance.SetDash(false);
        //TODO
        //_playerController.getMovementController().setEnabled(false);
        if (GameData.instance.fileHandler.currentMission==0)
        {
            print(deathControls);
            InputController.instance.enabled = false;
            deathControls.Init();
            playerHud.HideHud();
            //PlayerDeathControls cnt = PlayerDeathControls::create();
            //this.addChild(cnt);
            //_playerController._playerHud.setVisible(false);
            scheduleUpdate = false;
            return;
        }

        timeManager.SetTimescale(1);


        if (eFactory)
        {
            eFactory.SetEnabled(false);
        }
        DOTween.Sequence().SetId(tweenId).AppendInterval(2).AppendCallback(() =>
        {
            foreach (Enemy e in GameSharedData.Instance.enemyList)
            {
                Destroy(e);
            }
            GameSharedData.Instance.enemyList.Clear();
            timeManager.SetTimescale(1.0f);


        }).Play();

        GameData.instance.fileHandler.FrontGun = PlayerPrefs.GetInt("FrontGun");


        GameData.instance.fileHandler.machineGunLevel = PlayerPrefs.GetInt("machineGunLevel");


        GameData.instance.fileHandler.multiCanonLevel = PlayerPrefs.GetInt("multiCanonLevel");


        GameData.instance.fileHandler.laserLevel = PlayerPrefs.GetInt("laserLevel");

        GameData.instance.fileHandler.SaveData();

        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            //TODO
            //_hudLayer.hudPauseButton.setEnabled(false);
            //_playerController.Death();

            return;
        }

    }

    private void GeneratePowerUp()
    {
        if (Globals.gameType == GameType.Arena)
            return;

        InstantiatePrefab("Powerup");
    }

    private void InitHud()
    {

        playerHud.Init();
        //TODO
        //        _hudLayer = HudLayer::createLayer();
        //        this.addChild(_hudLayer, 1000);
        //        _hudLayer.setCameraMask(HUDCAMERA);
        //        if (FileHandler::getInstance().currentMission == 0)
        //        {

        //#if CC_TARGET_PLATFORM == CC_PLATFORM_TVOS
        //            if (FileHandler::getInstance().currentMission == 0)
        //            {
        //                _hudLayer.setVisible(true);
        //            }
        //#endif

        //#endif
        //#if Desktop
        //        this.runAction(Sequence::create(DelayTime::create(1),CallFunc::create([=]{
        //            createSkipButtonForTutorialFTUX();

        //        }), NULL));

        //#endif

        //        }

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            playerHud.SetWhitePortrait();
        }
    }

    public void EnableControls()
    {
#if UNITY_STANDALONE
        Observer.DispatchCustomEvent("show_cursor");
#elif UNITY_ANDROID || UNITY_IOS

        InputController.instance.ShowMobileControlsHud();
#endif
        //player.weapon.SetShootMode(true);
        Observer.DispatchCustomEvent("enable_movement");
        Observer.DispatchCustomEvent("enable_rotation");
        //TODO
        //static_cast<DesktopControls*>(static_cast<GameController*>(GETGAMECONTROLLER)->getControls())->ActivateControls();
        if (GameData.instance.fileHandler.currentMission == 0 && Globals.isTutorial)
        {
            controlsTutorialManager.ChangeState(TutorialState.Movement_Start);
        }
        //#endif

        //#if CC_TARGET_PLATFORM == CC_PLATFORM_TVOS
        //        if (FileHandler::getInstance()->currentMission == 0 && isTutorial)
        //        {
        //            (static_cast<GameController*>(GETGAMECONTROLLER)->mobileAssistiveTutorialObj)->changeState(MobileAssistiveControlsTutorial::AssistModeTutorialState::Movement_Start);
        //        }
        //#endif

        //#if CC_TARGET_PLATFORM == CC_PLATFORM_IOS

        //        if (isJoystickConnected)
        //        {
        //            Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("Activate_Controller_Controls");

        //        }


        //        if (FileHandler::getInstance()->currentMission == 0 && isTutorial)
        //        {
        //            if (!isJoystickConnected)
        //            {
        //                static_cast<MobileAssistControls*>(static_cast<GameController*>(GETGAMECONTROLLER)->getControls())->EnableMovementJoystickHandTutorial();
        //            }
        //            (static_cast<GameController*>(GETGAMECONTROLLER)->mobileAssistiveTutorialObj)->changeState(MobileAssistiveControlsTutorial::AssistModeTutorialState::Movement_Start);

        //        }






        //        if (FileHandler::getInstance()->currentMission != 0)
        //        {
        //            if (!isAssistMode)
        //            {
        //                static_cast<MobileTouchControls*>(static_cast<GameController*>(GETGAMECONTROLLER)->getControls())->getRotationDpad()->EnableJoystickCall();


        //                static_cast<MobileTouchControls*>(static_cast<GameController*>(GETGAMECONTROLLER)->getControls())->getMovementDpad()->EnableJoystickCall();

        //                Observer.DispatchCustomEvent("Activate_Mobile_Touch_Controls");

        //            }
        //            else

        //            {
        //                Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("Activate_Mobile_Assist_Controls");

        //            }
        //        }


        //#endif
    }

    public void SpawnRequiredEnemy(string enemyName)
    {
        eFactory.SetEnabled(false);

        var e = SpawnEnemy(enemyName, null,false);
        //e.CreateAsType1();

    }

    public void StartAutoShoot()
    {
        if (Globals.autoShootMode)
        {
            if (GameManager.instance.player.Stats.energy > 0 && !Globals.antoShootRestoredEnergying)
            {
                player.StartShooting();
                //if (BattleSkillManager.Instance.whiteCatList != null && BattleSkillManager.Instance.whiteCatList.Count > 0)
                //{
                //    foreach (var item in BattleSkillManager.Instance.whiteCatList)
                //    {
                //        item.player.StartShooting();
                //    }
                //}
                if (BattleSkillManager.Instance.whiteCatBGameObject != null)
                {
                    BattleSkillManager.Instance.whiteCatBGameObject.StartShooting();
                }
            }
        }
    }
    public void StartAutoShootAB()
    {
        if (Globals.autoShootMode)
        {
            if (GameManager.instance.player.Stats.energy > 0 && !Globals.antoShootRestoredEnergying)
            {
                if (BattleSkillManager.Instance.whiteCatList != null && BattleSkillManager.Instance.whiteCatList.Count > 0)
                {
                    foreach (var item in BattleSkillManager.Instance.whiteCatList)
                    {
                        if(!item.player.IsShooting) item.player.StartShooting();
                    }
                }
                //if (BattleSkillManager.Instance.whiteCatBGameObject != null)
                //{
                //    BattleSkillManager.Instance.whiteCatBGameObject.StartShooting();
                //}
            }
        }
    }
    public void EndAutoShoot()
    {

        player.EndShooting();
        //if (BattleSkillManager.Instance.whiteCatList != null && BattleSkillManager.Instance.whiteCatList.Count > 0)
        //{
        //    foreach (var item in BattleSkillManager.Instance.whiteCatList)
        //    {
        //        item.player.EndShooting();
        //    }
        //}   
        if (BattleSkillManager.Instance.whiteCatBGameObject != null)
        {
            BattleSkillManager.Instance.whiteCatBGameObject.EndShooting();
        }
        
    }
    public void EndAutoShootAB()
    {
        if (BattleSkillManager.Instance.whiteCatList != null && BattleSkillManager.Instance.whiteCatList.Count > 0)
        {
            foreach (var item in BattleSkillManager.Instance.whiteCatList)
            {
                item.player.EndShooting();
            }
        }
    }
}



[System.Serializable]
public class WaveData
{
    public int hammerHeadCount;
    public int trackerCount;
    public int dozerCount;
    public int blastorCount;
    public int skyRunnerCount;
}

