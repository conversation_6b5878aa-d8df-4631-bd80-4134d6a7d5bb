using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
public class MapTrailController : MonoBehaviour
{

    [SerializeField] private SkeletonAnimation trail;
    [SerializeField] private Material trailObjMatBigIsland;
    [SerializeField] private Material trailObjMatSmallIsland;
    [SerializeField] private GameObject track;


    private Sequence seq;


    private void Init()
    {
        //this.setCascadeOpacityEnabled(true); TODO:?Ask Bilal Bhai
        
    }

    public int TrailRunFromTo(int start, int stop, bool attuned, Transform parent, float delayTime = 2.2f)
    {
        if (attuned)
        {
            TrailSetTo(start, parent);
            int numberOfDots = 1;
            for (int i = 1; i < 20; i++)
            {
                Bone bone = trail.skeleton.FindBone("mission_" + start + "_dot_" + i);
                if (bone != null)
                {
                    GameObject trackObj = Instantiate(track, bone.GetWorldPosition(trail.transform),Quaternion.identity);
                    if (start > 30)
                    {
                        trackObj.GetComponent<SpriteRenderer>().material = trailObjMatSmallIsland;
                        trackObj.transform.parent = parent;
                    }
                    else
                    {

                        trackObj.transform.parent = trail.transform;
                    }
                    trackObj.transform.SetScale(0);

                    numberOfDots++;
                    //StartCoroutine(TrailCoroutine(1.5f + delayTime + i * 0.2f, trackObj));
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.5f + delayTime + i * 0.2f);
                    seq.AppendCallback(() =>
                    {
                        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.mapTrailTick);
                    });
                    seq.Append(trackObj.transform.DOScale(new Vector3(2f, 2f, 2f), 0.2f));
                    seq.Append(trackObj.transform.DOScale(new Vector3(1, 1, 1), 0.1f));
                    seq.Play();

                    //        testSprite.runAction(RepeatForever::create(RotateBy::create(0.2, 4)));
                }
                else
                {
                    return numberOfDots;

                }


            }
        }
        else
        {
            TrailSetTo(start, parent);
        }

        return 0;
    }

    public void TrailSetTo(int missionNumber, Transform parent)
    {
       
        trail.skeleton.UpdateWorldTransform();

        for (int p = 1; p < missionNumber; p++)
        {
            for (int i = 1; i < 20; i++)
            {

                Bone bone = trail.skeleton.FindBone("mission_" + p + "_dot_" + i);
                if (bone != null)
                {
                    GameObject trackObj = Instantiate(track, bone.GetWorldPosition(trail.transform), Quaternion.identity);
                    if (p > 30)
                    {
                        trackObj.GetComponent<SpriteRenderer>().material = trailObjMatSmallIsland;
                        //trackObj.transform.position = missionTransformPosition[missionTransformName.IndexOf("mission_" + p + "_dot_" + i)].position;
                        trackObj.transform.parent = parent;
                    }
                    else
                    {
                        trackObj.transform.parent = trail.transform;
                    }
                    trackObj.transform.SetScale(1);
                }
                else
                {
                    break;

                }

            }


        }
    }

    public SkeletonAnimation GetTrail()
    {
        return trail;
    }

    public void FadeBigIslandTrail(float value, float duration, float easeAmplitude)
    {
        trailObjMatBigIsland.DOFade(value, duration).SetEase(Ease.Linear, easeAmplitude, 1);
    }

    public void FadeSmallIslandTrail(float value, float duration, float easeAmplitude)
    {
        trailObjMatSmallIsland.DOFade(value, duration).SetEase(Ease.Linear, easeAmplitude, 1);
    }

}
