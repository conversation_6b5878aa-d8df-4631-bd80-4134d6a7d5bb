﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;

public class WXR_Explosion : MonoBehaviour
{
    private bool inUse = false;

    public bool IsInUse { get { return inUse; } set { inUse = value; } }

    private void Awake()
    {
    }

    public void Reset()
    {
        inUse = false;
        gameObject.SetActive(false);
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        transform.position = Vector3.zero;
    }

    public void PlayAnimation()
    {
        inUse = true;
        gameObject.SetActive(true);
    }
}
