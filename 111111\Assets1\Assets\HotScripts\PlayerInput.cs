using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerInput : MonoBehaviour
{
    private PlayerController player;

    PlayerInputActions playerInputActions;

    private void Awake()
    {
        player = GetComponent<PlayerController>();
        playerInputActions = new PlayerInputActions();
    }

    private void OnEnable()
    {
        // Input action doesn't work if it isn't enabled
        playerInputActions.Player.Enable();

        playerInputActions.Player.Shoot.performed += StartShooting;
        playerInputActions.Player.Shoot.canceled += EndShooting;

        playerInputActions.Player.Dash.performed += PerformDash;

        playerInputActions.Player.SpecialAbility.performed += ActivateSpecialAbility; 
    }
    private void OnDisable()
    {
        playerInputActions.Player.Shoot.performed -= StartShooting;
        playerInputActions.Player.Shoot.canceled -= EndShooting;

        playerInputActions.Player.Dash.performed -= PerformDash;

        playerInputActions.Player.SpecialAbility.performed += ActivateSpecialAbility;

        playerInputActions.Player.Disable();
    }

    private void Update()
    {
        player.playerMovement.rightStickVector = playerInputActions.Player.Aim.ReadValue<Vector2>();
    }

    void StartShooting(InputAction.CallbackContext obj)
    {
        if (Globals.isJoystickConnected && !Globals.isAssistMode || Globals.autoShootMode)
            return;

        player.StartShooting();
    }

    void EndShooting(InputAction.CallbackContext obj)
    {
        if (Globals.isJoystickConnected && !Globals.isAssistMode || Globals.autoShootMode)
            return;

        player.EndShooting();
    }

    void PerformDash(InputAction.CallbackContext obj)
    {
        player.PerformDash();
    }

    void ActivateSpecialAbility(InputAction.CallbackContext obj)
    {
        player.secondaryWeapon.ActivateSpecialAbility();
    }
}
