using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using TMPro;
using Spine.Unity;
public class XPBar : MonoBehaviour
{
    [SerializeField] private SkeletonGraphic xpBg;
    [SerializeField] private Slider xpBar;
    [SerializeField] private Image xpSliderBg;
    [SerializeField] private Image xpSliderFill;
    [SerializeField] private TextMeshProUGUI levelLabel;

    private bool sequenceStarted = false;

    public void Init()
    {
        levelLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SHOP_SCENE)["level"] + " " + GameData.instance.fileHandler.playerLevel;
        xpBg.AnimationState.SetAnimation(0, "setup", false);
        UpdateBar();
    }

    public void LevelUp()
    {
        xpBg.AnimationState.SetAnimation(0, "gameOverIdle", false);
        UpdateBar();
        levelLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SHOP_SCENE)["level"] + " " + GameData.instance.fileHandler.playerLevel;
    }

    public void StartUpdateBar()
    {
        if (sequenceStarted)
            return;
        sequenceStarted = true;
        InvokeRepeating(nameof(UpdateBar), 0, 0.025f);
    }

    public void UpdateBar()
    {
        float percentage = ( (float)GameData.instance.fileHandler.playerXP / (float)GameData.instance.fileHandler.xpRequiredThisLevel);
        percentage = Mathf.Clamp(percentage, 0, 1);
        xpBar.value = percentage;
        levelLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SHOP_SCENE)["level"] + " " + GameData.instance.fileHandler.playerLevel;
        levelLabel.color = Color.white;
        if (GameData.instance.fileHandler.playerXP == GameData.instance.fileHandler.xpRequiredThisLevel)
        {
            sequenceStarted = false;
            CancelInvoke(nameof(UpdateBar));
        }
    }

    public void FadeXpBar(float fadeVal, float interval)
    {
        xpBg.DOFade(fadeVal, interval);
        levelLabel.DOFade(fadeVal, interval);
        xpSliderBg.DOFade(fadeVal, interval);
        xpSliderFill.DOFade(fadeVal, interval);
    }

    public SkeletonGraphic GetAnimator()
    {
        return xpBg;
    }

    public TextMeshProUGUI GetLabel()
    {
        return levelLabel;
    }
}
