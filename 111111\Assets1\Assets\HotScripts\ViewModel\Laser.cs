﻿using System.Threading;

using UnityEngine;

namespace ViewModel
{
    /// <summary>
    /// 一条激光
    /// </summary>
    public class Laser
    {
        /// <summary>
        /// 激光起点
        /// </summary>
        public GameObject LaserBegin { get; set; }
        /// <summary>
        /// 激光光路
        /// </summary>
        public GameObject LaserMiddle { get; set; }
        /// <summary>
        /// 激光终点
        /// </summary>
        public GameObject LaserEnd { get; set; }

        /// <summary>
        /// 锁定的敌人
        /// </summary>
        public Enemy Enemy { get; set; }

        /// <summary>
        /// 用于监视该激光是否应该销毁
        /// </summary>
        public CancellationTokenSource cts_Watch { get; set; } = new();

        /// <summary>
        /// 设置激光是否显示出来
        /// </summary>
        public void SetActive(bool active)
        {
            LaserBegin.SetActive(active);
            LaserMiddle.SetActive(active);
            LaserEnd.SetActive(active);
        }

        /// <summary>
        /// 销毁
        /// </summary>
        public void Destroy()
        {
            if (Enemy)
            {
                Enemy.laserDamaging = false;
                Enemy.laser = null;
            }

            if (LaserBegin)
            {
                Object.Destroy(LaserBegin);
            }
            if (LaserMiddle)
            {
                Object.Destroy(LaserMiddle);
            }
            if (LaserEnd)
            {
                Object.Destroy(LaserEnd);
            }
        }
    }
}
