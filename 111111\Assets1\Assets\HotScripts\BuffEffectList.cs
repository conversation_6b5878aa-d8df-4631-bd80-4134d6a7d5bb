﻿using System.Collections.Generic;

public class BuffEffectList : List<BuffEffect.Item>
{
    /// <summary>
    /// 自定义:添加一个BuffEffect(有些同时顶掉其它同类BuffEffect)
    /// </summary>
    public void AddItem(BuffEffect.Item item)
    {
        // 最大生命、攻击
        if (item.Param1 == 1 || item.Param1 == 2)
        {
            RemoveAll(x => x.Param1 == item.Param1 && x.Param2 == item.Param2);
        }

        Add(item);
    }
}
