﻿namespace Apq
{

    public enum EaseType
    {
        /// <summary>
        /// 线性缓动，匀速运动
        /// </summary>
        Linear,
        /// <summary>
        /// 正弦加速缓动，开始缓慢，后来加速
        /// </summary>
        InSine,
        /// <summary>
        /// 正弦减速缓动，开始快速，后来减速
        /// </summary>
        OutSine,
        /// <summary>
        /// 正弦波缓动，先加速后减速
        /// </summary>
        InOutSine,
        /// <summary>
        /// 二次方加速缓动，开始缓慢，后来加速
        /// </summary>
        InQuad,
        /// <summary>
        /// 二次方减速缓动，开始快速，后来减速
        /// </summary>
        OutQuad,
        /// <summary>
        /// 二次方波缓动，先加速后减速
        /// </summary>
        InOutQuad,
        /// <summary>
        /// 三次方加速缓动，开始缓慢，后来加速
        /// </summary>
        InCubic,
        /// <summary>
        /// 三次方减速缓动，开始快速，后来减速
        /// </summary>
        OutCubic,
        /// <summary>
        /// 三次方波缓动，先加速后减速
        /// </summary>
        InOutCubic,
        /// <summary>
        /// 四次方加速缓动，开始缓慢，后来加速
        /// </summary>
        InQuart,
        /// <summary>
        /// 四次方减速缓动，开始快速，后来减速
        /// </summary>
        OutQuart,
        /// <summary>
        /// 四次方波缓动，先加速后减速
        /// </summary>
        InOutQuart,
        /// <summary>
        /// 五次方加速缓动，开始缓慢，后来加速
        /// </summary>
        InQuint,
        /// <summary>
        /// 五次方减速缓动，开始快速，后来减速
        /// </summary>
        OutQuint,
        /// <summary>
        /// 五次方波缓动，先加速后减速
        /// </summary>
        InOutQuint,
        /// <summary>
        /// 指数加速缓动，开始缓慢，后来加速
        /// </summary>
        InExpo,
        /// <summary>
        /// 指数减速缓动，开始快速，后来减速
        /// </summary>
        OutExpo,
        /// <summary>
        /// 指数波缓动，先加速后减速
        /// </summary>
        InOutExpo,
        /// <summary>
        /// 圆形加速缓动，开始缓慢，后来加速
        /// </summary>
        InCirc,
        /// <summary>
        /// 圆形减速缓动，开始快速，后来减速
        /// </summary>
        OutCirc,
        /// <summary>
        /// 圆形波缓动，先加速后减速
        /// </summary>
        InOutCirc,
        /// <summary>
        /// 先正弦减速后正弦加速缓动，开始快速，后来减速，再加速
        /// </summary>
        InOutBack,
        /// <summary>
        /// 回退加速（类似弹簧），开始缓慢，后来加速并回退一定距离，然后减速回弹
        /// </summary>
        InBack,
        /// <summary>
        /// 回退减速（类似弹簧），开始快速，后来减速并回退一定距离，然后加速回弹
        /// </summary>
        OutBack,
        /// <summary>
        /// 弹跳加速缓动，开始缓慢，后来加速并反弹
        /// </summary>
        InBounce,
        /// <summary>
        /// 弹跳减速缓动，开始快速，后来减速并反弹
        /// </summary>
        OutBounce,
        /// <summary>
        /// 弹跳波缓动，先加速后减速并反弹
        /// (有可能是死循环，==测试后没问题再去掉这行注释)
        /// </summary>
        InOutBounce,
        /// <summary>
        /// 弹性加速缓动，开始缓慢，后来加速并弹性拉伸
        /// </summary>
        InElastic,
        /// <summary>
        /// 弹性减速缓动，开始快速，后来减速并弹性拉伸
        /// </summary>
        OutElastic,
        /// <summary>
        /// 弹性波缓动，先加速后减速并弹性拉伸
        /// </summary>
        InOutElastic,
    }
}
