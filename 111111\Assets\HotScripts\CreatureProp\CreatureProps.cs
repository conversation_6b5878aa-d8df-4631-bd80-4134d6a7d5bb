﻿using Apq.NotifyChange;

using JsonStructure;

using UnityEngine;

using X.PB;

/// <summary>
/// 对生物属性进行提升
/// </summary>
public partial class CreatureProps : NotifyPropertyChange
{
	/// <summary>
	/// 属性的所有者(哪个生物的属性)
	/// </summary>
	public CreatureBase Creature { get; set; }

	/// <summary>
	/// 生物类型
	/// </summary>
	public CreatureType CreatureType { get; protected set; } = CreatureType.CreatureTypeActor;

	// 注：玩家的属性由Lua传入，不是读表
	/// <summary>
	/// 提升前的属性(战前属性)
	/// </summary>
	public ActorProp ActorProp { get; } = new();

	/// <summary>
	/// 生物的半径(所有生物都视为圆)
	/// </summary>
	public float Radius { get; set; }

	/// <summary>
	/// 生物的质量(不是固定值会动态改变)
	/// </summary>
	public NotifyChangeProperty<float> Mass { get; }

	#region 属性提升
	/// <summary>
	/// 总提升(对Hoists的汇总)
	/// </summary>
	public TotalHoist TotalHoist { get; protected set; } = new();

	/// <summary>
	/// 拥有的提升
	/// </summary>
	public NotifyChangeList<FightHoist> Hoists { get; } = new();
	#endregion

	#region 提升后的属性,Gen后才有值
	public NotifyChangeProperty<double> MaxHP { get; }
	public NotifyChangeProperty<double> HP { get; }
	/// <remarks>不含百分比提升</remarks>
	public NotifyChangeProperty<double> 攻击 { get; }
	public NotifyChangeProperty<float> 攻击Pct { get; }
	public NotifyChangeProperty<double> 回血量 { get; }
	public NotifyChangeProperty<float> 移动速度 { get; }
	public NotifyChangeProperty<float> 转向速度 { get; }
	public NotifyChangeProperty<float> 攻击速度 { get; }
	//public NotifyChangeProperty<double> 加伤 { get; }
	//public NotifyChangeProperty<float> 加伤Pct { get; }
	//public NotifyChangeProperty<double> 减伤 { get; }
	//public NotifyChangeProperty<float> 减伤Pct { get; }
	#endregion

	/// <summary>
	/// 根据玩家属性创建宠物属性
	/// </summary>
	public void InitPet(PlayerController player)
	{
		CreatureType = CreatureType.CreatureTypePet;

		player.FightProp.ActorProp.CopyTo(ActorProp);
		Radius = 1f;

		// 宠物没有提升，初始化后立即计算一下
		ReGenAll();
	}

	/// <summary>
	/// 根据玩家属性创建分身属性
	/// </summary>
	public void InitSkillPet(PlayerController player)
	{
		CreatureType = CreatureType.CreatureTypeSkillPet;

		player.FightProp.ActorProp.CopyTo(ActorProp);
		Radius = 1f;

		// 宠物没有提升，初始化后立即计算一下
		ReGenAll();
	}

	/// <summary>
	/// 从csv配置表的行初始化对应的属性(怪物)
	/// </summary>
	public void InitFrom(BattleBrushEnemy.Item csvRow_BattleBrushEnemy)
	{
		CreatureType = CreatureType.CreatureTypeMonster;
		ActorProp.LoadFromBattleBrushEnemy(csvRow_BattleBrushEnemy);
		Mass.Value = csvRow_BattleBrushEnemy.Mass;

		// 怪物没有提升，初始化后立即计算一下
		ReGenAll();
	}

	public CreatureProps()
	{
		Mass = new(nameof(Mass), this);
		MaxHP = new(nameof(MaxHP), this);
		HP = new(nameof(HP), this);
		攻击 = new(nameof(攻击), this);
		攻击Pct = new(nameof(攻击Pct), this);
		回血量 = new(nameof(回血量), this);
		移动速度 = new(nameof(移动速度), this);
		转向速度 = new(nameof(转向速度), this);
		攻击速度 = new(nameof(攻击速度), this);
		//加伤 = new(nameof(加伤), this);
		//加伤Pct = new(nameof(加伤Pct), this);
		//减伤 = new(nameof(减伤), this);
		//减伤Pct = new(nameof(减伤Pct), this);

		#region 属性的级联改变
		Hoists.ListChanged += Hoists_ListChanged;
		TotalHoist.PropertyChanged += TotalHoist_PropertyChanged;
		MaxHP.Changed += MaxHP_Changed;
		Mass.Changed += Mass_Changed;
		#endregion
	}

	#region 属性的级联改变
	/// <summary>
	/// 提升列表改变,重新计算总提升
	/// </summary>
	private void Hoists_ListChanged(NotifyChangeListEventArgs<FightHoist> e)
	{
		var n = new TotalHoist();
		n.ReCalc(Hoists);

		n.CopyTo(TotalHoist);
	}

	/// <summary>
	/// 总提升改变,重新生成对应的最终属性值
	/// </summary>
	private void TotalHoist_PropertyChanged(NotifyPropertyChange sender, PropertyChangeEventArgs e)
	{
		switch (e.PropertyName)
		{
			case nameof(TotalHoist.最大生命):
			case nameof(TotalHoist.最大生命Pct):
				{
					GenMaxHP();
				}
				break;
			case nameof(TotalHoist.攻击):
				{
					Gen攻击();
				}
				break;
			case nameof(TotalHoist.攻击Pct):
				{
					Gen攻击Pct();
				}
				break;
			case nameof(TotalHoist.回血):
			case nameof(TotalHoist.回血Pct):
				{
					Gen回血量();
				}
				break;
			case nameof(TotalHoist.移动速度):
			case nameof(TotalHoist.移动速度Pct):
				{
					Gen移动速度();
				}
				break;
			case nameof(TotalHoist.转向速度):
			case nameof(TotalHoist.转向速度Pct):
				{
					Gen转向速度();
				}
				break;
			case nameof(TotalHoist.攻击速度):
			case nameof(TotalHoist.攻击速度Pct):
				{
					Gen攻击速度();
				}
				break;
				//case nameof(TotalHoist.加伤):
				//    {
				//        Gen加伤();
				//    }
				//    break;
				//case nameof(TotalHoist.加伤Pct):
				//    {
				//        Gen加伤Pct();
				//    }
				//    break;
				//case nameof(TotalHoist.减伤):
				//    {
				//        Gen减伤();
				//    }
				//    break;
				//case nameof(TotalHoist.减伤Pct):
				//    {
				//        Gen减伤Pct();
				//    }
				//    break;
		}
	}

	/// <summary>
	/// 最大血量改变,当前血量跟随改变(改变量相同)
	/// </summary>
	private void MaxHP_Changed(string propertyName, double originalValue, double newValue)
	{
		HP.Value += newValue - originalValue;
	}

	/// <summary>
	/// 质量改变后,生物的刚体质量跟随改变
	/// </summary>
	private void Mass_Changed(string propertyName, float originalValue, float newValue)
	{
		if (Creature)
		{
			var rigid = Creature.gameObject.GetComponentInChildren<Rigidbody2D>();
			if (rigid)
			{
				rigid.mass = newValue;
			}
		}
	}
	#endregion

	#region IDisposable
	/// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
	protected override void Dispose(bool disposing)
	{
		if (!disposedValue)
		{
			if (disposing)
			{
				Hoists.ListChanged -= Hoists_ListChanged;
				TotalHoist.PropertyChanged -= TotalHoist_PropertyChanged;
				MaxHP.Changed -= MaxHP_Changed;
				Mass.Changed -= Mass_Changed;
			}

			// TODO: 释放未托管的资源(未托管的对象)并重写终结器
			// TODO: 将大型字段设置为 null
		}

		base.Dispose(disposing);
	}
	#endregion

	#region Gen XX
	/// <summary>
	/// 重新计算所有项
	/// </summary>
	public void ReGenAll()
	{
		GenMaxHP();
		Gen攻击();
		Gen攻击Pct();
		Gen回血量();
		Gen移动速度();
		Gen转向速度();
		Gen攻击速度();
		//Gen加伤();
		//Gen加伤Pct();
		//Gen减伤();
		//Gen减伤Pct();
	}

	public void GenMaxHP()
	{
		// 先点数提升,再百分比提升
		var nValue = ActorProp.HP + TotalHoist.最大生命.Value;
		nValue *= (1 + TotalHoist.最大生命Pct.Value);
		MaxHP.Value = nValue;
	}

	public void Gen攻击()
	{
		攻击.Value = ActorProp.PhysicsAttack + TotalHoist.攻击.Value;
	}

	public void Gen攻击Pct()
	{
		攻击Pct.Value = (float)Globals.UnityValueTransform(ActorProp.Attack) + TotalHoist.攻击Pct.Value;
	}

	public void Gen回血量()
	{
		回血量.Value = ActorProp.Defense + TotalHoist.回血.Value + MaxHP.Value * TotalHoist.回血Pct.Value;
	}

	public void Gen移动速度()
	{
		移动速度.Value = ActorProp.移动速度 * (1 + Globals.UnityValueTransform(ActorProp.MoveSpeed) + TotalHoist.移动速度Pct.Value);
	}

	public void Gen转向速度()
	{
		转向速度.Value = ActorProp.转向速度 * (1 + Globals.UnityValueTransform(ActorProp.TurnSpeed) + TotalHoist.转向速度Pct.Value);
	}

	public void Gen攻击速度()
	{
		攻击速度.Value = Globals.UnityValueTransform(ActorProp.AttackSpeed) + TotalHoist.攻击速度Pct.Value;
	}

	//public void Gen加伤()
	//{
	//    加伤.Value = ActorProp.AntiCriticalStrike + TotalHoist.加伤.Value;
	//}

	//public void Gen加伤Pct()
	//{
	//    加伤Pct.Value = Globals.UnityValueTransform(ActorProp.DamageAdd) + TotalHoist.加伤Pct.Value;
	//}

	//public void Gen减伤()
	//{
	//    减伤.Value = ActorProp.Parry + TotalHoist.减伤.Value;
	//}

	//public void Gen减伤Pct()
	//{
	//    减伤Pct.Value = Globals.UnityValueTransform(ActorProp.DamageReduction) + TotalHoist.减伤Pct.Value;
	//}
	#endregion

	/// <summary>
	/// 获取攻击和攻击Pct
	/// </summary>
	/// <returns></returns>
	public (double, float) GetAttack()
	{
		double attack = 攻击.Value;
		float pct = 攻击Pct.Value;

		if (HP.Value <= MaxHP.Value / 2)
		{
			attack += TotalHoist.半血攻击.Value;
			pct += TotalHoist.半血攻击Pct.Value;
		}

		return (attack, pct);
	}
}
