using DG.Tweening;

using UnityEngine;
/// <summary>
/// 玩家属性、数据相关的
/// </summary>
public class PlayerAttributeManager : Singleton<PlayerAttributeManager>
{
    public float DropDistance;
    private int _curGoldNum;
    private int _curKillNum;
    private int _curPlayerLevel;

    private int _curPlayerLevelID;
    private float _curExp;
    private float _curMaxExp;
    private CatsKillLevelUp.Item _curLevelData;
    private int _curAttack;
    public int _curDefense;
    public int _damageAdd;
    public int _damageReduction;
    //private float _cur
    private int _curCriticalHitProbability;
    private int _curMaxHp;
    private int _curPlayerSpeed;

    private float _restoreLifeTime;
    private float _restoreLifeValuePerSecond;

    //[HideInInspector] public GameManager.instance.player GameManager.instance.player;



    public void InitAttribute()
    {

        //_curPlayerLevelID = 0;
        //_curGoldNum = 0;
        //_curKillNum = 0;
        ////AddKillEnemyNum(0);
        ////AddCollectGold(0);
        //DropDistance = ConstValueScheme.Instance.GetItem(337).Value / 10000;
        ////SetPlayerExp(1, 0);
        //_curMaxHp = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_HP)]);
        ////BattleScene.Instance.PlayerHealth.InitialHealth = _curMaxHp;
        ////BattleScene.Instance.PlayerHealth.MaximumHealth = _curMaxHp;
        //_curDefense = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_PHYSICS_DEFENSE)]);
        ////BattleScene.Instance.PlayerHealth.Defense = _curDefense;
        //_damageAdd = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_DAMAGE_ADD)]);
        //_damageReduction = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_DAMAGE_REDUCTION)]);
        //_curAttack = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_PHYSICS_ATTACK)]);
        ////BattleScene.Instance.characterHandleWeapon.gunAttackValue = _curAttack;
        ////SetAttackText(_curAttack);
        //_curPlayerSpeed = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_MOVE_SPEED)]) / 10000;
        ////BattleScene.Instance.Target.GetComponent<CharacterFlyEX>().FlySpeed = speed;
        //_curCriticalHitProbability = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_CRITICAL_STRIKE)]);
        ////BattleScene.Instance.PlayerBulletWeapon.CriticalHitProbability = _curCriticalHitProbability;
        ////SetSpeedText(speed);
        //_curPlayerLevel = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.CREATURE_FIELD_LEVEL)]);



    }

    public void PlayerLevelUp()
    {

    }

    /// <summary>
    /// 给玩家加buff(三选一Buff)
    /// </summary>
    /// <param name="buffId">id</param>
    /// <param name="isInitBuff">是否是初始随机buff</param>
    public void UpgradeBuff(int buffId, bool isInitBuff = false)
    {
        var buffItem = BuffScheme.Instance.GetItem(buffId);
        if (buffItem.EffectID1 > 0)
        {
            var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(buffItem.EffectID1);
            //Debug.Log($"ApplyBuffEffect:ID1={csvRow_BuffEffect.Id}");
            ApplyBuffEffect(csvRow_BuffEffect);
        }
        if (buffItem.EffectID2 > 0)
        {
            var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(buffItem.EffectID2);
            //Debug.Log($"ApplyBuffEffect:ID2={csvRow_BuffEffect.Id}");
            ApplyBuffEffect(csvRow_BuffEffect);
        }
        if (buffItem.EffectID3 > 0)
        {
            var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(buffItem.EffectID3);
            //Debug.Log($"ApplyBuffEffect:ID3={csvRow_BuffEffect.Id}");
            ApplyBuffEffect(csvRow_BuffEffect);
        }
        if (buffItem.EffectID4 > 0)
        {
            var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(buffItem.EffectID4);
            //Debug.Log($"ApplyBuffEffect:ID4={csvRow_BuffEffect.Id}");
            ApplyBuffEffect(csvRow_BuffEffect);
        }
        if (buffItem.EffectID5 > 0)
        {
            var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(buffItem.EffectID5);
            //Debug.Log($"ApplyBuffEffect:ID5={csvRow_BuffEffect.Id}");
            ApplyBuffEffect(csvRow_BuffEffect);
        }

        if (isInitBuff)
        {
            LuaManager.Instance.RunLuaFunction("BattleBuffModule.AddPlayerInitBuff", buffId);
        }
    }

    /// <summary>
    /// 应用Buff效果
    /// </summary>
    public void ApplyBuffEffect(BuffEffect.Item csvRow_BuffEffect)
    {
        GameManager.instance.player.BuffEffects.AddItem(csvRow_BuffEffect);
        //Debug.LogWarning(buffId+" buffEffectItem.Param3 " + buffEffectItem.Param3.ToString());
        double oldValue, newValue;
        if (csvRow_BuffEffect.Param1 == 1) //生命上限
        {
            (oldValue, newValue) = GameManager.instance.player.ReCalcMaxHP();
            GameManager.instance.player.UpdateHealthBar(true);
            //Debug.LogWarning("玩家的最大生命增加以及总的:" + newValue.ToString() + ","  + GameManager.instance.player.stats.maxHealth.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "生命上限", oldValue.ToString(), newValue.ToString());
        }
        else if (csvRow_BuffEffect.Param1 == 2)//攻击力
        {
            (oldValue, newValue) = GameManager.instance.player.ReCalcAttack();
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "攻击力", oldValue.ToString(), newValue.ToString());
        }
        else if (csvRow_BuffEffect.Param1 == 3) ///防御力
        {
            //if (buffEffectItem.Param2 == 1)
            //{
            //    _curDefense += buffEffectItem.Param3;
            //}
            //else
            //{
            //    _curDefense += ((1 + buffEffectItem.Param3 / 1000) * _curDefense);
            //}
            //BattleScene.Instance.PlayerHealth.Defense = _curDefense;
        }
        else if (csvRow_BuffEffect.Param1 == 23) //技能CD
        {
            Debug.LogWarning("玩家增加攻击速度:" + csvRow_BuffEffect.Param3);
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "攻击速度", "0", System.Math.Floor(csvRow_BuffEffect.Param3 / 100f).ToString() + "%");
            GameManager.instance.player.Stats.attackSpeedPecent = (float)csvRow_BuffEffect.Param3;
            //GameManager.instance.player.weapon._weaponSkillData.Cd = (int)(GameManager.instance.player.weapon._weaponSkillData.Cd * (1 - Mathf.Floor(buffEffectItem.Param3 / 10000f)));
        }
        else if (csvRow_BuffEffect.Param1 == 8) //暴击
        {
            //if (buffEffectItem.Param2 == 1)
            //{
            //    _curCriticalHitProbability += buffEffectItem.Param3;
            //}
            //else
            //{
            //    _curCriticalHitProbability += ((1 + buffEffectItem.Param3 / 1000) * _curCriticalHitProbability);
            //}
            //BattleScene.Instance.PlayerBulletWeapon.CriticalHitProbability = _curCriticalHitProbability;
        }
        else if (csvRow_BuffEffect.Param1 == 24) //生命恢复
        {
            GameManager.instance.player.ReCalcRegen();

            //if (buffItem.KeepTime == 0)//立即恢复
            //{
            //    if (csvRow_BuffEffect.Param2 == 1) //1 点数  2 百分比
            //    {
            //        newValue = csvRow_BuffEffect.Param3;
            //    }
            //    else
            //    {
            //        newValue = Globals.UnityValueTransform(csvRow_BuffEffect.Param3) * GameManager.instance.player.Stats.maxHealth;
            //    }
            //    GameManager.instance.player.Stats.health += newValue;
            //    float health = GameManager.instance.player.Stats.health;
            //    float maxHealth = GameManager.instance.player.Stats.maxHealth;
            //    GameManager.instance.player.Stats.health = health > maxHealth ? maxHealth : health;
            //    GameManager.instance.player.UpdateHealthBar(true);
            //    Debug.LogWarning("玩家的增加的生命:" + newValue.ToString());
            //}
            //else //持续恢复
            //{
            //    if (csvRow_BuffEffect.Param2 == 1) //1 点数  2 百分比
            //    {
            //        _restoreLifeValuePerSecond = csvRow_BuffEffect.Param3;
            //    }
            //    else
            //    {
            //        _restoreLifeValuePerSecond = ((1 + Globals.UnityValueTransform(csvRow_BuffEffect.Param3)) * _curMaxHp);
            //    }
            //    _restoreLifeTime = buffItem.KeepTime;
            //}
        }
        else if (csvRow_BuffEffect.Param1 == 25) //生命瞬间恢复
        {
            GameManager.instance.player.CalcRegenMoment(csvRow_BuffEffect);
        }
        else if (csvRow_BuffEffect.Param1 == 18)//移速
        {
            Debug.LogWarning("玩家的增加前:" + GameManager.instance.player.Stats.speed.ToString());
            if (csvRow_BuffEffect.Param2 == 1)
            {
                newValue = GameManager.instance.player.defaultSpeed + Globals.UnityValueTransform(csvRow_BuffEffect.Param3);
            }
            else
            {
                newValue = ((1 + Globals.UnityValueTransform(csvRow_BuffEffect.Param3)) * GameManager.instance.player.defaultSpeed);
            }
            GameManager.instance.player.Stats.speed = (float)newValue;
            GameManager.instance.player.tempSaveSpeed = (float)newValue;
            Debug.LogWarning("玩家的基础速度:" + newValue.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "移动速度", GameManager.instance.player.defaultSpeed.ToString(), newValue.ToString());
        }

        else if (csvRow_BuffEffect.Param1 == 26)//伤害范围
        {
            Debug.LogWarning("玩家的增加前:" + GameManager.instance.player.Stats.attackScope.ToString());
            if (csvRow_BuffEffect.Param2 == 1)
            {
                newValue = GameManager.instance.player.defaultAttackScope + Globals.UnityValueTransform(csvRow_BuffEffect.Param3);
            }
            else
            {
                newValue = GameManager.instance.player.defaultAttackScope * (1 + Globals.UnityValueTransform(csvRow_BuffEffect.Param3));
            }
            GameManager.instance.player.Stats.attackScope = (float)newValue;
            Debug.LogWarning("玩家的伤害范围半径:" + newValue.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "伤害范围", Mathf.Floor(GameManager.instance.player.defaultAttackScope * 100).ToString() + "%", System.Math.Floor(newValue * 100).ToString() + "%");
        }
        else if (csvRow_BuffEffect.Param1 == 17)//伤害减免值
        {
            Debug.LogWarning("玩家的增加前:" + GameManager.instance.player.Stats.damageReduction.ToString());
            if (csvRow_BuffEffect.Param2 == 1)
            {
                newValue = GameManager.instance.player.defaultDamageReduction + csvRow_BuffEffect.Param3;
            }
            else
            {
                newValue = (Globals.UnityValueTransform(csvRow_BuffEffect.Param3) + GameManager.instance.player.defaultDamageReduction);
            }
            GameManager.instance.player.Stats.damageReduction = newValue;
            Debug.LogWarning("玩家的伤害减免:" + newValue.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "伤害减免", System.Math.Floor(GameManager.instance.player.defaultDamageReduction * 100).ToString() + "%", System.Math.Floor(newValue * 100).ToString() + "%");
        }
        else if (csvRow_BuffEffect.Param1 == 16)//伤害增加(公式后)
        {
            Debug.LogWarning("玩家的增加前:" + GameManager.instance.player.Stats.addDamage.ToString());
            if (csvRow_BuffEffect.Param2 == 1)
            {
                newValue = GameManager.instance.player.defaultAddDamage + csvRow_BuffEffect.Param3;
            }
            else
            {
                newValue = GameManager.instance.player.defaultAddDamage + Globals.UnityValueTransform(csvRow_BuffEffect.Param3);
            }
            GameManager.instance.player.Stats.addDamage = newValue;
            Debug.LogWarning("玩家的伤害增加:" + newValue.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "伤害增加", System.Math.Floor(GameManager.instance.player.defaultAddDamage * 100).ToString() + "%", System.Math.Floor(newValue * 100).ToString() + "%");
        }
        else if (csvRow_BuffEffect.Param1 == 25)//玩家子弹速度
        {
            Debug.LogWarning("玩家的增加前:" + GameManager.instance.player.Stats.bulletSpeedAddRate.ToString());
            if (csvRow_BuffEffect.Param2 == 1)
            {
                newValue = GameManager.instance.player.defaultBulletSpeedAddRate + csvRow_BuffEffect.Param3;
            }
            else
            {
                newValue = GameManager.instance.player.defaultBulletSpeedAddRate * (1 + Globals.UnityValueTransform(csvRow_BuffEffect.Param3));
            }
            GameManager.instance.player.Stats.bulletSpeedAddRate = (float)newValue;
            Debug.LogWarning("玩家的子弹速度增加:" + newValue.ToString());
            //LuaToCshapeManager.Instance.AddBuffDes(buffId, "子弹速度", Mathf.Floor(GameManager.instance.player.defaultBulletSpeedAddRate * 100).ToString() + "%", System.Math.Floor(newValue * 100).ToString() + "%");
        }
        else if (csvRow_BuffEffect.Param1 == 28)//玩家进入游戏主武器技能满级
        {
            Debug.LogWarning("主武器升级到满级");
            BattleSkillManager.Instance._weapon.UpdateToMax(true);
        }
        else if (csvRow_BuffEffect.Param1 == 29)//玩家进入游戏随机技能满级
        {
            Debug.LogWarning("随机多个技能升级到满级");
            for (int i = 0; i < csvRow_BuffEffect.Param3; i++)
            {
                BattleSkillManager.Instance._weapon.UpdateToMax(false);
            }
        }
        else if (csvRow_BuffEffect.Param1 == 27)//玩家所有子弹都增加
        {
            Debug.LogWarning("buff玩家子弹增加" + csvRow_BuffEffect.Param3.ToString());
            GameManager.instance.player.Stats.addBulletNum = (int)csvRow_BuffEffect.Param3;
        }
        else if (csvRow_BuffEffect.Param1 == 30)//生命低于50%攻击力提高百分比
        {
            Debug.LogWarning("生命降低50%攻击力提高百分比" + Globals.UnityValueTransform(csvRow_BuffEffect.Param3).ToString());

            GameManager.instance.player.Stats.maxHealth.Value = GameManager.instance.player.Stats.maxHealth.Value * 0.5f;
            GameManager.instance.player.Stats.health = GameManager.instance.player.Stats.maxHealth.Value;
            GameManager.instance.player.UpdateHealthBar(true);
            Debug.LogWarning("当前血量" + GameManager.instance.player.Stats.maxHealth);
            GameManager.instance.player.Stats.halfHealthAddDamage = 1 + Globals.UnityValueTransform(csvRow_BuffEffect.Param3);
        }
        else if (csvRow_BuffEffect.Param1 == 32)//怪物数量翻倍
        {
            Debug.LogWarning("怪物数量翻倍" + csvRow_BuffEffect.Param3.ToString());
            GameManager.instance.player.enemyCountMulte = 2;
        }
        else if (csvRow_BuffEffect.Param1 == 33)//同屏怪物数量翻倍
        {
            Debug.LogWarning("同屏怪物数量翻倍" + csvRow_BuffEffect.Param3.ToString());
            GameManager.instance.player.enemyScreenCountMulte = 2;
        }
    }

    public void AddPlayerHp(double addHp)
    {
        var curHealth = System.Math.Min((GameManager.instance.player.Stats.health + addHp), GameManager.instance.player.Stats.maxHealth.Value);
        GameManager.instance.player.Stats.health = curHealth;
        GameManager.instance.player.UpdateHealthBar(true);
        Debug.LogWarning("玩家的当前生命:" + GameManager.instance.player.Stats.health.ToString());
    }

    public string GetPlayerEndData()
    {
        int[] result = { _curGoldNum, _curKillNum, _curPlayerLevel };
        string resuleStr = string.Join(",", result);
        return resuleStr;
    }

    private void LateUpdate()
    {
        if (_restoreLifeTime > 0)
        {
            _restoreLifeTime -= Time.deltaTime;
            AddPlayerHp(_restoreLifeValuePerSecond * Time.deltaTime);
        }
    }


    public void RevivePlayer(bool isWin)
    {
        LuaToCshapeManager.Instance.OpenReviveDialog = false;
        if (isWin)
        {
            LuaToCshapeManager.Instance.curReviveNum++;

            GameManager.instance.player.Stats.health = GameManager.instance.player.Stats.maxHealth.Value;
            GameManager.instance.player.Stats.energy = GameManager.instance.player.Stats.maxEnergy;
            GameManager.instance.player.UpdateHealthBar(true);
            GameManager.instance.player.UpdateEnergyBar();
            //3秒无敌
            GameManager.instance.playerHud.FullScreenDamage(false);
            GameManager.instance.player.canHit = false;
            Sequence sequence = DOTween.Sequence();
            sequence.AppendInterval(3f);
            sequence.AppendCallback(() =>
            {
                GameManager.instance.player.canHit = true;
            });
        }
        else
        {
            LuaToCshapeManager.Instance.curReviveNum = int.MaxValue;
            GameManager.instance.player.ChangePlayerDeath();
        }
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
    }
}

