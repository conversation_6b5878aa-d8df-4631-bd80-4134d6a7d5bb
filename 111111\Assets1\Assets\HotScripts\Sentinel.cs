using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
using System;

public class Sentinel : Enemy
{
    private List<BugBot> laserDroidArray = new List<BugBot>();
    private int bombersCount;//60

    private Vector2 displacement;
    private Vector2 displacementSpeed;
    private Bounds bounds;
    private bool isCollidingWithTenticles = false;
    private bool isCollidingWithPlayer = false;
    [SerializeField] private BoundingBoxFollower[] tenticleBoundingBox;
    [SerializeField] private Collider2D[] attackBoundingBox;
    [SerializeField] private BoundingBoxFollower faceBoundingBox;
    [SerializeField] private BugBot bomber;
    [SerializeField] private SkeletonAnimation endExplosion;
    [SerializeField] private Laser laserObj = null;
    private Bone bone;

    private bool allowFollowY = true;
    private int AttackCounter;
    private int tentDestroyed;
    private SentinelStates enemyState;
    private int _numberOfPatternsLoaded = 0;

    private float[] hpTentArray = new float[6];

    private const int NUMBER_OF_BOMBER_DROIDS = 50;
    private const int NUMBER_OF_LASER_DROIDS = 17;
    private const int PATTERN_RUN_LIMIT = 8;//8
    private const int NUMBER_OF_TENTICLES = 6;

    private bool tentAttack = false;

    private enum SentinelStates
    {
        Normal = 0,
        SpawnBugBombers = 1,
        SpawnBugLaser = 2,
        AttackLaser = 3,
        idle = 4,
        idle2 = 5,
        AttackLaser2 = 6,
        Dead = 7
    };

    public override void Init()
    {
        if (initialized)
            return;
        schedulerId = "SENS" + GetInstanceID();
        tweenId = "SEN" + GetInstanceID();
        base.Init();
        InitVariables();
        InitStats();
        CreateSprites();
        allowRelocate = false;
        if (Globals.gameType == GameType.Survival)
        {
            Globals.LEFTBOUNDARY = Globals.CocosToUnity(-90000000);
            Globals.RIGHTBOUNDARY = Globals.CocosToUnity(90000000);
        }
        Globals.bossShouldStayOnScreen = true;

        //laserObj = Laser.create();
        //this.addChild(laserObj);
        laserObj.Init();
        laserObj.setAllowSound = true;
        laserObj.SetIsActive(false);
        laserObj.SetLaserScale(40, 8);
        laserObj.SetLaserRotation(180);
        laserObj.setDamage = stats.bulletDamage / 5;

        explosionType = Explosions.ExplosionType.ExplosionTypeAir1;

        scheduleUpdate = true;

        DOTween.Sequence().SetId(schedulerId).AppendInterval(5).AppendCallback(ChangeStates).Play();

        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.SentinelIntro);
        //Globals.PlaySound("res/Sounds/Bosses/Boss8/SentinelIntro.mp3");

        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
         {
             if (spineEvent.Data.Name == "startAttack")
             {
                 allowFollowY = false;
             }
             if (spineEvent.Data.Name == "endAttack")
             {
                 allowFollowY = true;
             }
             if (spineEvent.Data.Name == "laserOn")
             {
                 laserObj.SetIsActive(true);
             }

         };

        isSentinelBoss = true;
    }

private void InitVariables()
    {
        enemyState = SentinelStates.Normal;//normal
        displacement = new Vector2(Globals.CocosToUnity(1100), Globals.CocosToUnity(-150));
        displacementSpeed = new Vector2(1, 2);
        AttackCounter = 0;
        tentDestroyed = 0;
        bombersCount = NUMBER_OF_BOMBER_DROIDS;
    }
    
    private void InitStats()
    {
        //bounds = faceBoundingBox.CurrentCollider.bounds;
        //TODO change health and tent health to original
        stats = new Attributes();
        baseStats = new Attributes();

        int bossNumber = 8;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {      
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }

        isBoss = true;
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;

        for (int i = 0; i < NUMBER_OF_TENTICLES; i++)
        {
            hpTentArray[i] = 1000/3;
        }
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }

    private void CreateSprites()
    {

        transform.position = new Vector3(player.transform.position.x + Globals.CocosToUnity(1200), Globals.CocosToUnity(500));
        enemySprite.state.Data.SetMix("idle", "idle2", 0.25f);
        enemySprite.state.Data.SetMix("idle2", "attack1", 0.05f);
        enemySprite.state.Data.SetMix("attack1", "idle2", 0.05f);

        enemySprite.state.SetAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle2", true);
    }

    public override bool CheckCollision(Vector2 P1)
    {

        if (enemyState == SentinelStates.Normal && tentDestroyed < 6)
        {
            //bbAttachment = spSkeletonBounds_containsPoint(bounds, P1.x - enemySprite.getPosition().x, P1.y - enemySprite.getPosition().y);
            string ch;
            if (Vector2.Distance(transform.position, P1) < Globals.CocosToUnity(200))
            {
                for (int i=0;i< NUMBER_OF_TENTICLES;i++)// BoundingBoxFollower bb in tenticleBoundingBox)
                {
                    //if (tenticleBoundingBox[i].CurrentCollider != null && tenticleBoundingBox[i].CurrentCollider.bounds.Contains(P1))
                    //{
                        if (hpTentArray[i] > 0)
                        {
                            hpTentArray[i] = hpTentArray[i] - 10;

                            if (hpTentArray[i] <= 0)
                            {
                                ch = "tentacle" + (i + 1);
                                enemySprite.skeleton.SetAttachment(ch, null);

                                ch = "tentHolderTop" + (i + 1);
                                enemySprite.skeleton.SetAttachment(ch, null);

                                ch = "tentHolder" + (i + 1);
                                enemySprite.skeleton.SetAttachment(ch, null);


                                for (int j = 0; j < 5; j++)
                                {
                                    GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(P1.x - i * Globals.CocosToUnity(100), P1.y), false, 1, 1.3f, 0);
                                }

                                tentDestroyed++;
                                if (tentDestroyed > 5)
                                {
                                    DOTween.Kill(schedulerId);
                                    stats.health = stats.maxHealth.Value;
                                    ChangeStates();
                                }
                                GameManager.instance.ShakeCamera(Globals.CocosToUnity(80), 12);
                                //shakeScreen = 12;
                                //shakeIntensity = 80;
                            }
                            return true;
                        }
                    //}
                }
            }

        }

        else
        {
            if (Vector2.SqrMagnitude( (Vector2)transform.position-P1) < Globals.CocosToUnity(160))
            {
                if(faceBoundingBox.CurrentCollider)
                    return faceBoundingBox.CurrentCollider.bounds.Contains(P1);// P1.x - enemySprite.getPosition().x, P1.y - enemySprite.getPosition().y);
            }
            else
            {
                return false;
            }
        }


        return false;

    }

    private void ChangeStates()
    {

        //spSkeletonBounds_update(bounds, enemySprite.getSkeleton(), true);

        if (enemyState == SentinelStates.Normal)
        {
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1000));
            if (tentDestroyed > 5)
            {
               Observer.DispatchCustomEvent("ChangeBossState");

                DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(ChangeStates).Play();
                enemyState = SentinelStates.SpawnBugBombers;

                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.25f).AppendCallback(()=>{
                    enemySprite.state.SetAnimation(0, "laserIdle", true);
                }).Play();
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(Globals.CocosToUnity(-180), Globals.CocosToUnity(100)), false, 3, 5.0f, 50);
                return;
            }


            enemySprite.state.SetAnimation(0, "attack1", false);
            DOTween.Sequence().SetId(schedulerId).AppendInterval(1.23f).AppendCallback(() => { tentAttack = true; }).AppendInterval(0.2f).AppendCallback(()=> { tentAttack = false; }).Play();

            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.sentinelAttack);
            //Globals.PlaySound("res/Sounds/Bosses/Boss8/Bullet/sentinelAttack.mp3");


            enemySprite.state.AddAnimation(0, "idle2", true);
            AttackCounter++;
            DOTween.Sequence().SetId(schedulerId).AppendInterval(5).AppendCallback(ChangeStates).Play();

        }
        else if (enemyState == SentinelStates.SpawnBugBombers)
        {
            allowFollowY = false;
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(800));
            InvokeRepeating("SpawnBombers", 0.2f,0.2f);
            enemyState = SentinelStates.idle;
            Observer.DispatchCustomEvent("ChangeBossState");

        }
        else if (enemyState == SentinelStates.SpawnBugLaser)
        {
            Globals.bossShouldStayOnScreen = false;
            displacement.x = Globals.CocosToUnity(3200);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(800));

            CreateLaserDroid();
            enemyState = SentinelStates.idle2;

        }
        else if (enemyState == SentinelStates.idle)
        {
            displacement.x = Globals.CocosToUnity(1300);
            allowFollowY = true;
            DOTween.Sequence().SetId(schedulerId).AppendInterval(1.4f).AppendCallback(ChangeStates).Play();
            enemyState = SentinelStates.AttackLaser;
            laserObj.SetIsActive(false);

            enemySprite.state.SetAnimation(0, "laserIdle", false);
            enemySprite.state.AddAnimation(0, "laserAttackStart", false);
            displacementSpeed.y = 2;
        }

        else if (enemyState == SentinelStates.AttackLaser)
        {
            DOTween.Sequence().SetId(schedulerId).AppendInterval(3.5f).AppendCallback(ChangeStates).Play();
            enemyState = SentinelStates.idle;
            enemySprite.state.SetAnimation(0, "laserAttack", true);
        }

        else if (enemyState == SentinelStates.idle2)
        {

            Globals.bossShouldStayOnScreen = true;
            displacement.x = Globals.CocosToUnity(1300);
            allowFollowY = true;
            DOTween.Sequence().SetId(schedulerId).AppendInterval(1.4f).AppendCallback(ChangeStates).Play();
            enemyState = SentinelStates.AttackLaser2;
            laserObj.SetIsActive(false);

            enemySprite.state.SetAnimation(0, "laserIdle", false);
            enemySprite.state.AddAnimation(0, "laserAttackStart", false);
            displacementSpeed.y = 2;
        }

        else if (enemyState == SentinelStates.AttackLaser2)
        {
            Globals.bossShouldStayOnScreen = true;

            DOTween.Sequence().SetId(schedulerId).AppendInterval(1.25f).AppendCallback(ChangeStates).Play();
            enemyState = SentinelStates.idle2;
            enemySprite.state.SetAnimation(0, "laserAttack", true);
        }
        print(enemyState);

    }
	
    private void Update()
    {
        //spSkeletonBounds_update(bounds, enemySprite.getSkeleton(), true);

        //healthBarsetPosition(enemySprite.getPosition().x, enemySprite.getPosition().y - 150);
        if (!scheduleUpdate)
            return;
        transform.SetWorldPositionX(transform.position.x + ((player.transform.position.x + displacement.x - transform.position.x) * Time.deltaTime * displacementSpeed.x));

        if (allowFollowY)
        {
            transform.SetWorldPositionY(transform.position.y + ((player.transform.position.y - transform.position.y + displacement.y) * Time.deltaTime * displacementSpeed.y));
        }

        if (transform.position.y < Globals.LOWERBOUNDARY - Globals.CocosToUnity(100))
        {
            transform.SetWorldPositionY(Globals.LOWERBOUNDARY - Globals.CocosToUnity(100));
        }
        Globals.bossPosition = new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(150));

        if (enemyState == SentinelStates.Normal && tentDestroyed < 6)
        {

            //foreach (BoundingBoxFollower bb in attackBoundingBox)
            //{
            //    if (bb.CurrentCollider.bounds.Contains(player.transform.position))
            //    {
            //        isCollidingWithPlayer = true;
            //    }
            //}
            ////bbAttachment = spSkeletonBounds_containsPoint(bounds, Player.getInstance().getPosition().x - enemySprite.getPosition().x, Player.getInstance().getPosition().y - enemySprite.getPosition().y);


            //if (isCollidingWithPlayer)
            //{

            if (tentAttack)
            {
                for (int i = 0; i < NUMBER_OF_TENTICLES; i++)
                {
                    string ch = "attackBB" + (i + 1);
                    if (attackBoundingBox[i].bounds.Contains(player.transform.position))
                    {
                        if (hpTentArray[i] > 0)
                        {
                            if(player.canHit) player.GotHit(stats.bulletDamage, false);
                        }
                    }

                }
            }

            
            //}
        }

        if (enemyState == SentinelStates.idle && stats.health < stats.maxHealth.Value * 0.35f)
        {
            enemyState = SentinelStates.idle2;
            Observer.DispatchCustomEvent("ChangeBossState");
            DOTween.Kill(schedulerId);
            ChangeStates();
            laserObj.SetIsActive(false);
        }
        bone = enemySprite.skeleton.FindBone("laserAttack");
        laserObj.GetLaser().transform.position = bone.GetWorldPosition(transform);
    }
	
    private void SpawnBombers()
    {

        if (GameSharedData.Instance.enemyList.Count > 15)
            return;

        if (bombersCount > 0)
        {
            BugBot b = Instantiate(bomber,transform.position,Quaternion.identity);
            b.Init();
            b._allowKillPoint = false;
            b.SetBulletDamage(stats.bulletDamage);
            bombersCount--;
        }

        if (bombersCount == 0 && GameSharedData.Instance.enemyList.Count < 2)
        {
            CancelInvoke("SpawnBombers");
            //this.unschedule(schedule_selector(Sentinel.spawnBombers));
            DOTween.Sequence().SetId(schedulerId).AppendInterval(1.4f).AppendCallback(ChangeStates).Play();
        }
    }

    private void CreateLaserDroid()
    {
        for (int i = 0; i < NUMBER_OF_LASER_DROIDS; ++i)
        {
            BugBot laserBot = Instantiate(bomber,transform.position,Quaternion.identity);
            laserBot.Init();
            laserBot._allowKillPoint = false;
            laserBot.name = "laserbot"+i;
            laserBot.InitAsLaserDroids(new Vector2(Globals.CocosToUnity(600),  Globals.CocosToUnity(200)+Globals.UPPERBOUNDARY - (i * Globals.CocosToUnity(110))),false);
            //node.enemySprite.setPosition(enemySprite.getPosition());
            laserBot.transform.position = transform.position;
            laserDroidArray.Add(laserBot);
        }

        for (int i = 0; i < NUMBER_OF_LASER_DROIDS; ++i)
        {
            BugBot laserBot = Instantiate(bomber, transform.position, Quaternion.identity);
            laserBot.Init();
            laserBot._allowKillPoint = false;
            laserBot.name = "laserbotright" + i;
            laserBot.InitAsLaserDroids(new Vector2(Globals.CocosToUnity(-600), Globals.CocosToUnity(200) + Globals.UPPERBOUNDARY - (i * Globals.CocosToUnity(110))), true);

            //BugBot* node = BugBot.createAsLaserDroids(cocos2d.Point(-600, 200 + UPPERBOUNDARY - (i * 110)), true);
            //this.addChild(node);
            //node.enemySprite.setPosition(enemySprite.getPosition());
            laserBot.transform.position = transform.position;
            laserDroidArray.Add(laserBot);


        }

        DOTween.Sequence().SetId(schedulerId).AppendInterval(5).AppendCallback(LaserPattern).Play();
    }

    private void LaserPattern()
    {


        int pattern = UnityEngine.Random.Range(1, 6);
        _numberOfPatternsLoaded++;
        //this.stopAllActions();
        DOTween.Kill(schedulerId);

        if (_numberOfPatternsLoaded == PATTERN_RUN_LIMIT)
        {
            int count = 0;
            foreach (BugBot bot in laserDroidArray)
            {
                count++;
                bot.DestroyAfter(count * 0.05f);
            }
            laserDroidArray.Clear();
            bombersCount = 30;
            InvokeRepeating("SpawnBombers", 5.0f,5.0f);
            
            enemySprite.skeleton.SetAttachment("blast", "blast");
            enemySprite.skeleton.SetAttachment("damage", "strobe");
            enemySprite.skeleton.SetAttachment("damage2", "strobe");
            enemySprite.skeleton.SetAttachment("fire", "fire");

            DOTween.Kill(schedulerId);
            DOTween.Sequence().SetId(schedulerId).AppendInterval(8.0f).AppendCallback(ChangeStates).Play();
            return;
        }

        const float PATTERN_DELAY = 2.0f;
        switch (pattern)
        {
            case 1:
                {
                    for (int i = 0; i < NUMBER_OF_LASER_DROIDS - 2; i++)
                    {
                        laserDroidArray[i].TurnOnLaserFor(2, i * 0.15f);
                        laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, i * 0.15f);
                    }

                    DOTween.Sequence().SetId(schedulerId).AppendInterval(PATTERN_DELAY + 2 + (NUMBER_OF_LASER_DROIDS * 0.15f)).AppendCallback(()=>{
                        LaserPattern();
                    });
                }
                break;

            case 2:
                {
                    for (int i = 2; i < NUMBER_OF_LASER_DROIDS; i++)
                    {
                        laserDroidArray[i].TurnOnLaserFor(2, (0.15f * NUMBER_OF_LASER_DROIDS) - (i * 0.15f));
                        laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, (0.15f * NUMBER_OF_LASER_DROIDS) - (i * 0.15f));
                    }

                    DOTween.Sequence().SetId(schedulerId).AppendInterval(PATTERN_DELAY + 2 + (NUMBER_OF_LASER_DROIDS * 0.15f)).AppendCallback(LaserPattern).Play();
                }
                break;

            case 3:
                {
                    for (int i = 0; i < NUMBER_OF_LASER_DROIDS / 2; i++)
                    {
                        laserDroidArray[i].TurnOnLaserFor(2, 0.5f);
                        laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, 0.5f);
                    }
                    for (int i = NUMBER_OF_LASER_DROIDS / 2; i < NUMBER_OF_LASER_DROIDS; i++)
                    {
                        laserDroidArray[i].TurnOnLaserFor(2, 3.0f);
                        laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, 3.0f);
                    }

                    DOTween.Sequence().SetId(schedulerId).AppendInterval(PATTERN_DELAY + 5).AppendCallback(LaserPattern).Play();
                }
                break;

            case 4:
                {
                    for (int i = 0; i < NUMBER_OF_LASER_DROIDS; i++)
                    {

                        if (i % 3 == 0)
                        {
                            laserDroidArray[i].TurnOnLaserFor(2, 1);
                            laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, 1);
                        }
                    }
                    DOTween.Sequence().SetId(schedulerId).AppendInterval(PATTERN_DELAY + 3).AppendCallback(LaserPattern).Play();

                }
                break;

            case 5:
                {
                    for (int i = 0; i < NUMBER_OF_LASER_DROIDS / 2 - 1; i++)
                    {
                        laserDroidArray[i].TurnOnLaserFor(2, i * 0.15f);
                        laserDroidArray[NUMBER_OF_LASER_DROIDS + i].TurnOnLaserFor(2, i * 0.15f);

                        laserDroidArray[NUMBER_OF_LASER_DROIDS - 1 - i].TurnOnLaserFor(2, i * 0.15f);
                        laserDroidArray[(NUMBER_OF_LASER_DROIDS - 1) + NUMBER_OF_LASER_DROIDS - i].TurnOnLaserFor(2, i * 0.15f);

                    }


                    DOTween.Sequence().SetId(schedulerId).AppendInterval((PATTERN_DELAY * 2) + 2 + (NUMBER_OF_LASER_DROIDS / 2 * 0.15f)).AppendCallback(LaserPattern).Play();
                }
                break;
            default:
                break;
        }
    }
	
    public override void Destroy()
    {
        laserObj.SetIsActive(false);
        laserObj.gameObject.SetActive(false);
        //Globals.zoomValueWhileGame += Globals.CocosToUnity(300);
        //if (this.getReferenceCount() != 2)
        //{
        //    CCASSERT(0, "ref count must = to 2");
        //}
        //{
        //StartCoroutine(Dest());
        //IEnumerator Dest()
        //{
        //    yield return new WaitForEndOfFrame();
        //    GameSharedData.Instance.enemyList.Remove(this);
        //    //}

        //    Globals.RIGHTBOUNDARY += Globals.CocosToUnity(1000);
        //    DOTween.Kill(schedulerId);
        //    //enemySprite.setColor(Color3B::WHITE);
        //    enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        //    {
        //        if (spineEvent.Data.Name == "explosion")
        //        {
        //            {
        //            //TODO
        //            //SkeletonAnimation* explosion = SkeletonAnimation::createWithJsonFile("res/Explosions/explosion.json", "res/Explosions/explosion.atlas");
        //            //explosion.setCameraMask((unsigned int)CameraFlag::USER5);
        //            //explosion.setScale(3.0f);
        //            //explosion.setAnimation(0, "explosion7", false);
        //            bone = enemySprite.skeleton.FindBone("ex");
        //                endExplosion.transform.position = bone.GetWorldPosition(transform);
        //                endExplosion.gameObject.SetActive(true);
        //                DOTween.Sequence().SetId("SentinelExplosion").AppendInterval(1.5f).AppendCallback(() => { endExplosion.gameObject.SetActive(false); }).Play();




        //            }

        //            GameManager.instance.ShakeCamera(0.2f, 8);
        //        }
        //    };

        //    //        spSkeleton_findBone(enemySprite.getSkeleton(), "root").ScaleX  = -1;
        //    //        enemySprite.runAction(MoveBy::create(15, Point(-800,-1300)));
        //    DOTween.Kill(schedulerId);
        //    CancelInvoke();
        //    scheduleUpdate = false;
        //    for (int i = 0; i < 3; i++)
        //    {
        //        DOTween.Sequence().SetId(schedulerId).AppendInterval(i * 0.35f).AppendCallback(() =>
        //         {
        //             GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x + Globals.CocosToUnity(25), transform.position.y + Globals.CocosToUnity(25)), false, 1, 2, 0);
        //         }).Play();
        //    }
        //    enemySprite.state.SetAnimation(0, "death", false);
        //    DOTween.Sequence().SetId(schedulerId).AppendInterval(5).AppendCallback(() =>
        //    {
        //        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + Globals.CocosToUnity(25), transform.position.y + Globals.CocosToUnity(25)), false, 1, 2, 0);
        //        ;
        //    }).AppendCallback(base.Destroy).Play();

        //    DOTween.Sequence().SetId(tweenId).Append(transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(-800), Globals.CocosToUnity(-1300)), 25f)).Play();
        //}
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }
}
