using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
enum FlyingTurretState
{
    idle,
    shoot
};

public class FlyingTurret : Enemy
{

    private Vector2 pointToMove;
    private FlyingTurretState currentState;

    private Bone gunBone;
    private Bone shootGun;

   
    [SerializeField] Sprite gunBulletSprite;

    public void SetPointToMove(Vector2 p)
    {
        pointToMove = p;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        scheduleUpdate = true;
        allowRelocate = false;
        allowFollow = true;
        enemyCollisionRadius = 2;//Globals.CocosToUnity(5000.0f);
        InitStats();
        enemySchedulerSpeed = 0;
        InitializeEnemyAtStart();
        SetHealthBarInitialPosition();
        schedulerId = "FTS" + GetInstanceID();
        tweenId = "FT" + GetInstanceID();

    }

    private void InitializeEnemyAtStart()
    {
        if (Random.value < 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1800), Random.value * Globals.CocosToUnity(500) + Globals.CocosToUnity(50));
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1800), Random.value * Globals.CocosToUnity(500) + Globals.CocosToUnity(50));
        }

        enemySprite.state.SetAnimation(0, "idle", false);
        gunBone = enemySprite.skeleton.FindBone("gun");
        shootGun = enemySprite.skeleton.FindBone("gunShoot");
        currentState = FlyingTurretState.idle;
        enemySprite.state.Event += HandleSpineEvent;
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if(spineEvent.Data.Name == "shoot")
        {
            Shoot();
        }

        if(spineEvent.Data.Name == "changeAnim")
        {
            ChangeAnimation();
        }
    }

    private void SetHealthBarInitialPosition()
    {
        healthBar.transform.position = new Vector2(healthBar.transform.position.x, Globals.CocosToUnity(-50));
        healthBar.gameObject.SetActive(true);
        healthBar.ScaleRatio = 2;
        scheduleUpdate = true;

    }

    public void Disable()
    {
        scheduleUpdate = false;
        gameObject.SetActive(false);
        enemySprite.state.SetAnimation(0, "end", false);
        enemySprite.transform.position = new Vector2(pointToMove.x, Globals.CocosToUnity(3000));
        Observer.RegisterCustomEvent(gameObject, Globals.ACTIVATE_TURRET_EVENT, ()=>
        {
            Activate();
        });
    }

    private void Activate()
    {
        scheduleUpdate = true;
        gameObject.SetActive(true);
        enemySprite.state.SetAnimation(0, "idle", false);
        DOTween.Sequence().SetId(tweenId).Append(transform.DOMove(pointToMove, 1.5f).SetEase(Ease.Linear, 0.5f, 1)).Play();
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        healthBar.transform.position = new Vector2(enemySprite.transform.position.x - Globals.CocosToUnity(10), enemySprite.transform.position.y - Globals.CocosToUnity(100));
        if(currentState == FlyingTurretState.idle)
        {
            
            float angle = 180 + Globals.CalcAngle(player.transform.position, enemySprite.transform.position) * -1;

            if (gunBone.Rotation - angle > 180)
            {
                gunBone.Rotation += -360;
            }

            if (gunBone.Rotation - angle < -180)
            {
                gunBone.Rotation += 360;
            }

            gunBone.Rotation += (angle - gunBone.Rotation) * Time.deltaTime * 6.5f;

            if(allowFollow)
            {
                if(Vector2.SqrMagnitude(enemySprite.transform.position-player.transform.position)>Globals.CocosToUnity(10000))
                {
                    transform.position = new Vector2(enemySprite.transform.position.x + Globals.CocosToUnity(10.0f) * Mathf.Sin(Mathf.Deg2Rad * Globals.CalcAngle(player.transform.position, enemySprite.transform.position) - 90f),
                    enemySprite.transform.position.y + Globals.CocosToUnity(10.0f) * Mathf.Cos(Mathf.Deg2Rad * Globals.CalcAngle(player.transform.position, enemySprite.transform.position) - 90));
                }
            }
        }
    }

    private void ChangeAnimation()
    {
        if (currentState == FlyingTurretState.idle)
        {
            enemySprite.state.SetAnimation(0, "shoot", false);
            currentState = FlyingTurretState.shoot;
        }
        else
        {
            currentState = FlyingTurretState.idle;
            enemySprite.state.SetAnimation(0, "idle", false);
        }
    }

    private void Shoot()
    {
        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.SetSpriteFrame(gunBulletSprite);
        bullet.setDamage(stats.bulletDamage);
        bullet.duration = 2.0f;
        
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(120));        
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
        
        bullet.transform.position = shootGun.GetWorldPosition(enemySprite.transform);
        bullet.transform.SetRotation(gunBone.WorldRotationX - 120 + 25);
       
        Vector2 dest = new Vector2(Globals.CocosToUnity(10000) * ((Mathf.Sin(Mathf.Deg2Rad * bullet.transform.eulerAngles.z))*-1), Globals.CocosToUnity(10000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.eulerAngles.z));
        bullet.PlayBulletAnim(stats.bulletSpeed, dest,true);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = baseStats.speed = 1.5f + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = (14 + 10) * 100;
        stats.bulletDamage = baseStats.bulletDamage = 150;
        stats.missileDamage = baseStats.missileDamage = (14) * 10;
        stats.bulletSpeed = baseStats.bulletSpeed = 2.5f;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 15;
        stats.xp = baseStats.xp = stats.maxHealth.Value;
    }
}
