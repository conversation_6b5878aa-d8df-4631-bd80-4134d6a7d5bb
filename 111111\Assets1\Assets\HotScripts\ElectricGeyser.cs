﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;

public class ElectricGeyser : Enemy
{
    [SerializeField] private new Collider2D collider;
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] GameObject orbEffectPrefab;
    private Bounds bounds;
    public static string geyserBasePath;
    public static string geyserAttackPath;

    

    // Update is called once per frame
    void Update()
    {
        UpdateBounds();
    }

    public void CreateType1()
    {
        geyserBasePath = "Geyser1";
        geyserAttackPath = "EnergyRayUp";
        Init();
    }

    public void CreateType2()
    {
        geyserBasePath = "Geyser2";
        geyserAttackPath = "EnergyRayUpNew";
        Init();
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowRelocate = false;
        isDestructable = false;
        takeDamage = false;
        InitializeEnemyParameters();
    }

    private void InitializeEnemyParameters()
    {
        enemySprite.skeleton.SetSkin(geyserBasePath);
        enemySprite.state.SetAnimation(0, "idle", true);
        transform.position = new Vector2(transform.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(30));
        InvokeRepeating(nameof(Attack), 0.1f, 4);
        //this->schedule(schedule_selector(ElectricGeyser::Attack), 4); TODO
        bounds = collider.bounds;
        //this->schedule(schedule_selector(ElectricGeyser::updateBounds), 4, 2, 2); TODO
        enemySprite.state.Event += HandleSpineEvent;

    }

    private void HandleSpineEvent(TrackEntry entry, Spine.Event spineEntry)
    {
        if(spineEntry.Data.Name == "Attack" && gameObject.activeInHierarchy)
        {
            Shoot();
        }
    }

    private void Shoot()
    {
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }

        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(Globals.CocosToUnity(200));
        bullet.duration = 6.5f;
        bullet.transform.position = new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(200));
        //bullet.transform.SetPositionAndRotation(new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(250)), Quaternion.Euler(0,0,0));
        bullet.PlayBulletAnim(20f, new Vector2(0, Globals.CocosToUnity(10000)));
        bullet.transform.localScale = new Vector2(1.0f, 3.0f);
        bullet.transform.DOScale(2.0f, 1.25f).SetEase(Ease.OutElastic);
        
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(135));
        bullet.PushBack = 0.25f;
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        RotateOrbEffect(bullet.transform, bullet.transform.position);
    }

    private void RotateOrbEffect(Transform bullet, Vector2 pos)
    {
        orbEffectPrefab.transform.SetParent(bullet.transform);
        orbEffectPrefab.transform.position = pos;
        orbEffectPrefab.SetActive(true);
        orbEffectPrefab.transform.SetScale(1);
        orbEffectPrefab.transform.GetChild(0).DOBlendableRotateBy(new Vector3(0, 0, 800), 0.5f).SetLoops(-1).SetEase(Ease.Linear);
        orbEffectPrefab.transform.GetChild(1).DOBlendableRotateBy(new Vector3(0, 0, -800), 0.5f).SetLoops(-1).SetEase(Ease.Linear);
    }

    public override bool TakeHit(double damage)
    {
        return false;
    }

    private void Attack()
    {
        enemySprite.state.SetAnimation(0, "attack", false);
        enemySprite.state.AddAnimation(0, "idle", true);
    }

    //public override bool CheckCollision(Vector2 P1)
    //{
    //    return base.CheckCollision(P1);
    //}

    public override void Destroy()
    {
        base.Destroy();
        CancelInvoke(nameof(Attack));
    }

    private void UpdateBounds()
    {
        bounds = collider.bounds;
    }




}
