﻿using System.Collections.Generic;

using UniRx;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace GameEvent
{
    public class Publisher : Singleton<Publisher>
    {
        /// <summary>
        /// 通过默认Broker发布消息
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="msg">消息数据</param>
        /// <param name="flag">按位:{0x01:同步,0x02:异步}</param>
        public void PublishMessage<T>(T msg, byte flag = 3)
        {
            if ((flag & 1) == 1)
            {
                MessageBroker.Default.Publish(msg);
            }
            if ((flag & 2) == 2)
            {
                //必须要使用.Subscribe(),才会执行异步订阅者返回的IObservable<Unit>对象
                AsyncMessageBroker.Default.PublishAsync(msg).Subscribe();
            }
        }

        /// <summary>
        /// 在编辑中运行时,播放结束时需要停止的任务
        /// </summary>
        public List<System.IDisposable> Disposables { get; } = new();

        protected override void InitializeSingleton()
        {
            base.InitializeSingleton();

#if UNITY_EDITOR
            EditorApplication.playModeStateChanged += (obj) =>
            {
                if (obj == PlayModeStateChange.ExitingPlayMode)
                {
                    Disposables.ForEach(x => x.Dispose());
                }
                Disposables.Clear();
            };
#endif
        }
    }
}
