using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class BomberKitty : Enemy
{
    private float originalSpeed;
    private float ScaleFactor = 0.35f;
    private float sinY = 0;
    private bool movingLeft;
    private float bossInitialPositionY = 1500;
    private float sinHeight = 250;
    private float sineIncrement = 0.005f;
    private int animationState;

    public void CreateAsBoss()
    {
        Init();
        Boss();
        AudioManager.instance.PlaySound(AudioType.Enemy,Constants_Audio.Audio.spIntro,1f);
    }

    public override void Init()
    {
        if (initialized)
            return;

        schedulerId = "BKS" + GetInstanceID();
        tweenId = "BK" + GetInstanceID();
        base.Init();
        InitStats();
        animationState = 0;
        allowStack = false;
        movingLeft = false;
        allowRelocate = false;
        enemySprite.state.SetAnimation(0, "flyingEnemy1", true);
        transform.SetWorldPosition(player.transform.position.x - Globals.CocosToUnity(1600), Globals.CocosToUnity(700 + Random.value * 500));
        isBoss = false;
        scheduleUpdate = true;

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Red);
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = 4.5f + Random.value;//bhut taiz
        stats.turnSpeed = 0.5f + Random.value;
        stats.health = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletDamage = 300;
        stats.missileDamage = 300;
        stats.bulletSpeed = 7;
        stats.maxHealth.Value = stats.health;
        stats.coinAwarded = 8;
        stats.xp = stats.maxHealth.Value;
        originalSpeed = stats.speed;
        enemyCollisionRadius = 1.5f;
        baseStats.speed = stats.speed;//bhut taiz
        baseStats.turnSpeed = stats.turnSpeed;
        baseStats.health = GameData.instance.fileHandler.TrainingLevel * 50;
        baseStats.bulletDamage = 300;
        baseStats.missileDamage = 300;
        baseStats.bulletSpeed = 7;
        baseStats.maxHealth.Value = stats.health;
        baseStats.coinAwarded = 8;
        baseStats.xp = baseStats.maxHealth.Value;


        DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(2f).SetLoops(-1).Play();
    }

    public override void Destroy()
    {
        DOTween.Kill(schedulerId);
        DOTween.Kill(tweenId);
        base.Destroy();
    }

    private void EnemySpecialAttributes()
    {
        originalSpeed = stats.speed;
    }

    private void Update()
    {
        Vector2 playerPosition = player.transform.position;

        if (movingLeft)
        {
            transform.SetWorldPositionX(transform.position.x - stats.speed * Time.deltaTime * 0.60f);
            if (transform.position.x - playerPosition.x < Globals.CocosToUnity(-1000) || transform.position.x < Globals.LEFTBOUNDARY)
            {

                movingLeft = false;
                enemySprite.transform.SetScale(isBoss ? 0.35f : 0.225f);
                enemySprite.transform.SetScaleX(isBoss ? 0.35f : 0.225f);

            }
        }
        else
        {
            transform.SetWorldPositionX(transform.position.x + stats.speed * Time.deltaTime * 0.60f);
            if (transform.position.x - playerPosition.x > Globals.CocosToUnity(1000) || transform.position.x > Globals.RIGHTBOUNDARY)
            {
                movingLeft = true;
                enemySprite.transform.SetScale(isBoss ? 0.35f : 0.225f);
                enemySprite.transform.SetScaleX(isBoss ? -0.35f : -0.225f);
            }
        }

        if (transform.position.x - playerPosition.x > Globals.CocosToUnity(1500) || transform.position.x - playerPosition.x < Globals.CocosToUnity(-1500))
        {
            stats.speed++;

        }
        else
        {
            stats.speed = originalSpeed;
        }
    }

    private void Shoot()
    {
        shootRandom = Random.Range(0, 10000);
        if (shootRandom > skillShootProbability)
        {
            return;
        }
        Bomb newBomb = null;
        bool didFindBomb = false;
        foreach (Bomb b in GameSharedData.Instance.enemyBombPool)
        {
            if (!b.isInUse)
            {
                newBomb = b;
                newBomb.isInUse = true;
                didFindBomb = true;
                break;
            }

        }
        if (!didFindBomb)
        {
            return;
        }
        newBomb.Init();
        newBomb.duration = 20;
        if (player.Mode ==  PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {

            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spShoot,1,enemySprite.transform.position);
            //TODO Distance Audio
            //Globals.PlaySound("res/Sounds/Bosses/Boss2/spShoot.mp3", enemySprite.transform.position);
        }
        if (isBoss)
        {
            newBomb.radiusSQ = 0.7f;
            newBomb._doExplodeOnWater = true;
        }
        else
        {
            newBomb.radiusSQ = 0.2f;
            newBomb.missileSprite.transform.localScale = new Vector2(newBomb.missileSprite.transform.localScale.x * 0.75f, newBomb.missileSprite.transform.localScale.y * 0.75f);
        }
        newBomb.transform.position = transform.position;
        newBomb.SetDamage(stats.missileDamage);

        if (movingLeft)
        {
            newBomb.SetHorizontalVelocity(-stats.speed / 500);
            newBomb.missileSprite.transform.SetScaleX(-newBomb.transform.localScale.x);
            newBomb.missileSprite.transform.SetRotation(20);
        }
        else
        {
            newBomb.SetHorizontalVelocity(stats.speed / 500);

        }

        GameSharedData.Instance.enemyMissilesInUse.Add(newBomb);
        newBomb.RemoveAfterDuration();
        //    newBomb.setVerticalVelocity(8);


    }

    private void ChangeAnimation()
    {
        if (animationState == 0)
        {
            animationState = 1;
            enemySprite.state.SetAnimation(0, "flying2", true);
        }

        else
        {

            animationState = 0;
            enemySprite.state.SetAnimation(0, "flying", true);

        }
    }

    private void Boss()
    {
        isBoss = true;
        int bossNumber = 2;
        allowRelocate = (false);

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;

        stats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = System.Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = System.Convert.ToSingle((bossStats["xp"] as PList)["value"]); 
        stats.coinAwarded = System.Convert.ToInt32((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
        enemySprite.transform.SetScale(0.35f);
        ScaleFactor = 0.35f;
        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
        enemyCollisionRadius = 2;
        originalSpeed = stats.speed;
        transform.SetWorldPosition(player.transform.position.x - Globals.CocosToUnity(1400), bossInitialPositionY);
        enemySprite.state.SetAnimation(0, "flying", true);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.35f).AppendCallback(() =>
        {
            DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(1.5f).SetLoops(-1).Play();
        }).Play();
        DOTween.Sequence().SetId(schedulerId).AppendCallback(ChangeAnimation).AppendInterval(4).SetLoops(-1).Play();
    }
}
