using UnityEngine;
using System.Collections;
using Spine.Unity;
using Spine;

public class Tracker : Enemy
{
    [SerializeField] private EnemyFall enemyFallPrefab;
    private int enemyLevel;//all

    private bool allowShooting;//all
    private bool allowBoost;//2+

    private float scaleFactor;//all
    private Vector2 chaseOffset;
    private float chaseDistance;
    private bool allowRotation = true;


    private WaveStructure _info;

    private bool _createdAsType = false;
    private Sprite _ping = null;


    private bool _charge = false;
    private bool _isIntroComplete = false;


    private Bullet bullet;
    [SerializeField] private Sprite bulletSprite;

    private float tSpeed;
    private float rSpeed;
    float totalDistanceSquared;
    Vector2 PlayerPositionToFollow;
    float playerRotation;


    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowFollow = true;
        SetVariable();
        SetAnimationVariables();

        // maskInteraction(GAMECAMERA);
        if (Random.value < 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1500), Random.value * Globals.CocosToUnity(1200 + 200));
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1500), Random.value * Globals.CocosToUnity(1200 + 200));

        }


    }

    private void SetVariable()
    {
        scheduleUpdate = true;
        enemySchedulerSpeed = 0.075f;
        scaleFactor = 0.21f;
        allowPushBack = true;
        allowStack = false;
        allowDeathParticles = true;
        float offset = Globals.CocosToUnity(100);
        chaseOffset = new Vector2((-offset / 2) + (Random.value * offset), (-offset / 2) + (Random.value * offset));
        chaseDistance = Globals.CocosToUnity(30000) + Random.value * Globals.CocosToUnity(70000);
        InitStats();

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Red);
    }

    private void SetAnimationVariables()
    {

        Vector2 posDiff = player.transform.position - enemySprite.transform.position;
        float lookAtPlayer = Mathf.Atan2(posDiff.y, posDiff.x) * Mathf.Rad2Deg;

        enemySprite.transform.rotation = Quaternion.Euler(0, 0, lookAtPlayer);

        enemySprite.transform.localScale = new Vector2(-scaleFactor, scaleFactor);

        enemySprite.state.TimeScale = 1.2f;
        enemySprite.state.SetAnimation(0, "idle", true);
        enemySprite.state.Event += HandleSpineEvent;

        changeAllowFollow();
        changeBoost();
    }

    private void Update()
    {

        SetUpdateVariables();


        //enemySprite.transform.eulerAngles = new Vector3(0,0,(Globals.Modulus(enemySprite.transform.eulerAngles.z + 360f, 360f)));


        //if (Player::getStats()->mode != Player::PLAYER_MODE_FLYING)//TODO
        //{
        //    allowFollow = false;
        //}


        HandleRotation();
        HandleShooting();
        HandleMovement();


        SetEnemyImage();

        if (healthBar)
        {
            healthBar.transform.position = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y - Globals.CocosToUnity(75));
        }


    }
    private void SetUpdateVariables()
    {

        totalDistanceSquared = Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position);//  transform.position.distanceSquared(Player::getInstance()->getPosition());
        PlayerPositionToFollow = player.transform.position;
        if (totalDistanceSquared > chaseDistance && !isBoss)
        {
            PlayerPositionToFollow += chaseOffset;
        }

        tSpeed = 1;
        rSpeed = 1;
        if (enemySprite.transform.position.y < Globals.LOWERBOUNDARY)
        {
            rSpeed = 3.5f;
            tSpeed = 0.25f;

        }
        if (enemySprite.transform.position.y > Globals.UPPERBOUNDARY)
        {
            rSpeed = 3.5f;

        }
    }
   
    private void HandleRotation()
    {
        Vector2 posDiff1 = player.transform.position - transform.position;
        playerRotation = Vector2.Angle(posDiff1, transform.right);
        if (playerRotation == 0)
        {
            playerRotation = 1;
        }

        if (player.transform.position.y < transform.position.y)
        {
            playerRotation *= -1;
            playerRotation += 360;
        }

        if (_info)
        {
            playerRotation = Globals.CalcAngle(enemySprite.transform.position,
                new Vector2(PlayerPositionToFollow.x + _info.sInfo._position.x, PlayerPositionToFollow.y + _info.sInfo._position.y)) - 180;
        }
        if (playerRotation - enemySprite.transform.eulerAngles.z > 200)
        {
            int i = (int)(playerRotation - enemySprite.transform.eulerAngles.z) / 360;
            playerRotation = playerRotation - (360 * (i + 1));


        }
        if (playerRotation - enemySprite.transform.eulerAngles.z < -200)
        {
            int i = (int)(playerRotation - enemySprite.transform.eulerAngles.z) / 360;

            playerRotation = playerRotation + (360 * (i + 1));

        }
        if (allowFollow)
        {

            if (allowRotation)
            {
                float angleDifference = -enemySprite.transform.eulerAngles.z + playerRotation;
                if (Mathf.Abs(angleDifference) < 60 * Time.deltaTime) // finished rotation
                {
                    enemySprite.transform.eulerAngles = new Vector3(0, 0, playerRotation);
                }
                else if (angleDifference > 0)
                {
                    enemySprite.transform.rotation = Quaternion.Euler(0, 0, enemySprite.transform.eulerAngles.z + 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
                else
                {
                    enemySprite.transform.rotation = Quaternion.Euler(0, 0, enemySprite.transform.eulerAngles.z - 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
            }

        }
    }

    private void HandleShooting()
    {
        curCheckStateTime += Time.deltaTime;
        if (curCheckStateTime > checkStateTime)
        {
            curCheckStateTime = 0;
            posDiff2 = player.transform.position - enemySprite.transform.position;
            shootRandom = Random.Range(0, 10000);
            if (shootRandom > skillShootProbability)
            {
                enemySprite.state.SetAnimation(0, "idle", true);
                allowShooting = false;
                return;
            }

            if (Vector2.Angle(posDiff2, enemySprite.transform.right) < angleToShoot && totalDistanceSquared < stats.distanceToShoot && !_charge)
            {

                if (!allowShooting)
                {
                    allowShooting = true;
                    enemySprite.state.SetAnimation(0, "shootEnemyPlane2", true);
                }

            }
            else
            {

                if (allowShooting)
                {
                    enemySprite.state.SetAnimation(0, "idle", true);
                }
                allowShooting = false;
            }
        }
    }
    
    private void HandleMovement()
    {

        transform.position = new Vector2(transform.position.x + stats.speed * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * Time.deltaTime * Globals.CocosToUnity(60f), transform.position.y + stats.speed * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)) * Time.deltaTime * Globals.CocosToUnity(60f) * tSpeed);
        if (allowBoost && Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position) > Globals.CocosToUnity(625))
        {
            enemySprite.transform.position = new Vector2(enemySprite.transform.position.x + stats.speed / 2 * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)), enemySprite.transform.position.y + stats.speed / 2 * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)));
            if (allowFollow)
            {
                enemySprite.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z + (-enemySprite.transform.eulerAngles.z + playerRotation) * Time.deltaTime * stats.turnSpeed / 2);
            }
        }
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shootright")
        {
            if (enemySprite.transform.localScale.y > 0)
            {
                shootLeft();
            }
            else
            {
                shootRight();
            }
        }
        else if (spineEvent.Data.Name == "shootleft")
        {
            if (enemySprite.transform.localScale.y > 0)
            {
                shootRight();
            }
            else
            {
                shootLeft();
            }
        }
    }

    private void initWithInfo(WaveStructure info)
    {

    }


    private void chargeShot(float dt)
    {

    }


    public override void SetEnemySpecialAttributes()
    {

    }



    public void shootLeft()
    {
        if (!allowShooting)
        {
            return;
        }

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);
        bullet.transform.localScale *= 1.5f;
        bullet.transform.position = new Vector2(enemySprite.transform.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 45)),
           enemySprite.transform.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 45)));
        bullet.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z - 90);
        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));

        bullet.gameObject.SetActive(true);
        bullet.PlayBulletAnim(stats.bulletSpeed / (stats.speed / 4), dest, false);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all


    public void shootRight()
    {
        if (!allowShooting)
        {
            return;
        }

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);

        bullet.transform.localScale *= 1.5f;
        bullet.transform.position = new Vector2(enemySprite.transform.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 135)),
            enemySprite.transform.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 135)));

        bullet.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z - 90);

        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));

        bullet.gameObject.SetActive(true);
        bullet.PlayBulletAnim(stats.bulletSpeed / (stats.speed / 4), dest, false);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all

    private void SetEnemyImage()
    {
        float rotation;
        rotation = enemySprite.transform.eulerAngles.z;


        if (rotation < 90 || rotation > 90 + 180)
        {
            enemySprite.transform.localScale = new Vector2(scaleFactor, scaleFactor);

        }
        else
        {
            enemySprite.transform.localScale = new Vector2(scaleFactor, -scaleFactor);

        }
    }//all



    private void shootWaterMissile(float dt)
    {

    }//enemy 4

    private void changeAllowFollow()
    {

    }

    private void changeBoost()
    {

    }
    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 8.5f;
        stats.turnSpeed = baseStats.turnSpeed = 2f;
        stats.health = baseStats.health = 100;// = (::getStats()->maxHealth + 20 + difficulty) / 1.5f;
        //    stats.health = baseStats.health = 600;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health*1.5f;
        stats.bulletDamage = baseStats.bulletDamage = (2.5f + Globals.difficulty + (5.0f * /*FileHandler::getInstance()->currentMission*/ 1 / 5))*1.5f;
        stats.bulletSpeed = baseStats.bulletSpeed = 6;
        stats.coinAwarded = baseStats.coinAwarded = 2;
        stats.xp = baseStats.xp = stats.health;
    }



    public static HammerHead createWithInfo(WaveStructure info)
    {
        return null;
    }

    public override void Destroy()
    {
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(DestroyCoroutine());
        }
        //if (allowDeathParticles) //&& FileHandler::getInstance().currentMission == 2 && gameType == GameType::Arena)TODO
        //{
        //    dynamicFallEnemy(enemy);
        //}
    }

    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        EnemyFall enemyFall = null;
        if (Random.value < 0.2f && allowDeathParticles)
        {
            // enemyFall = Instantiate(enemyFallPrefab);
            // enemyFall.InitFallEnemy(this, 2);
            for (int i = 0; i < 3; i++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;    
                debris.CreateWithData(400, 5, true, true);
                debris.transform.position = transform.position;
            }
        }
        yield return new WaitForEndOfFrame();
        base.Destroy();
    }
}
