﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

using Apq.Extension;

namespace Apq.Net
{
	public static class UrlHelper
	{
		/// <summary>
		/// 获取不含参数的url
		/// </summary>
		/// <param name="url"></param>
		/// <param name="separators"></param>
		/// <param name="encoding"></param>
		/// <param name="encodeQuery">提供编码或解码算法。默认无</param>
		public static (string str, string separator) GetUrlWithoutParams(string url,
			ICollection<string> separators = null,
			Encoding encoding = null,
			Func<string, Encoding, string> encodeQuery = null)
		{
			url ??= string.Empty;
			separators ??= new List<string> { "?", "#" };
			encoding ??= Encoding.UTF8;
			encodeQuery ??= (str, _) => str;

			var indexes = url.IndexOf(separators);
			if (indexes.All(x => x.Item1 < 0))
			{
				return (url, separators.First());
			}

			Tuple<int, string> min = null;
			foreach (var index in indexes.Where(x => x.Item1 > -1))
			{
				if (min == null) min = index;
				else if (index.Item1 < min.Item1)
				{
					min = index;
				}
			}

			return (encodeQuery(url[..min!.Item1], encoding) ?? string.Empty, min.Item2);
		}

		/// <summary>
		/// 在url的QueryString里添加参数
		/// </summary>
		/// <param name="url"></param>
		/// <param name="ps"></param>
		/// <param name="encoding"></param>
		/// <param name="encodeQuery">提供编码或解码算法。默认无</param>
		public static string UrlAddParams(string url,
			ICollection<KeyValuePair<string, string>> ps = null,
			Encoding encoding = null,
			Func<string, Encoding, string> encodeQuery = null)
		{
			url ??= string.Empty;
			encoding ??= Encoding.UTF8;
			encodeQuery ??= (str, _) => str;

			if (ps == null || ps.Count == 0)
			{
				return url;
			}

			var lst = ps.Select(p =>
				$"{encodeQuery(p.Key, encoding)}={encodeQuery(p.Value, encoding)}").ToList();
			var psString = string.Join('&', lst);

			if (!string.IsNullOrWhiteSpace(psString))
			{
				url += (url.Contains('?') ? '&' : '?') + psString;
			}

			return url;
		}

		/// <summary>
		/// 提取QueryString
		/// </summary>
		/// <param name="url"></param>
		/// <param name="separator">分隔符(这个字符之后的子串算为QueryString)</param>
		public static string PickQueryString(string url, string separator = null)
		{
			url ??= string.Empty;
			separator ??= "?";

			var rtn = string.Empty;

			var idx = url.IndexOf(separator, StringComparison.Ordinal);
			if (idx != -1)
			{
				rtn = url[(idx + separator.Length)..];
			}
			return rtn;
		}

		/// <summary>
		/// 从QueryString中提取参数
		/// </summary>
		/// <param name="queryString"></param>
		/// <param name="encoding"></param>
		/// <param name="encodeQuery">提供编码或解码算法。默认使用 HttpUtility.UrlDecode</param>
		/// <param name="separator"></param>
		/// <param name="assignment"></param>
		/// <returns></returns>
		public static ICollection<KeyValuePair<string, string>> PickParams(string queryString,
			Encoding encoding = null,
			Func<string, Encoding, string> encodeQuery = null,
			ICollection<string> separator = null, string assignment = null)
		{
			queryString ??= string.Empty;
			separator ??= new List<string> { "&" };
			assignment ??= "=";
			encoding ??= Encoding.UTF8;
			encodeQuery ??= (str, _) => HttpUtility.UrlDecode(str);

			queryString = queryString.TrimStart('?', '#', '&');

			return (from strKv in queryString.Split(separator.ToArray(), StringSplitOptions.RemoveEmptyEntries)
					let idx = strKv.IndexOf(assignment, StringComparison.Ordinal)
					let k = encodeQuery(idx == -1 ? string.Empty : strKv[..idx], encoding) ?? string.Empty
					let v = encodeQuery(idx == -1 ? strKv : strKv[(idx + assignment.Length)..], encoding) ?? string.Empty
					select new KeyValuePair<string, string>(k, v)).ToList();
		}

		/// <summary>
		/// 从QueryString中提取参数值
		/// </summary>
		/// <typeparam name="T">值的类型</typeparam>
		/// <param name="queryString"></param>
		/// <param name="key">参数名称(如果同名参数有多个,只取第一个)</param>
		/// <param name="encoding"></param>
		/// <param name="encodeFunc">提供编码或解码算法。默认无</param>
		/// <param name="separator"></param>
		/// <param name="assignment"></param>
		/// <returns>参数值,是否获取成功</returns>
		public static (T, bool) PickParamValue<T>(string queryString, string key,
			Encoding encoding = null,
			Func<string, Encoding, string> encodeFunc = null,
			ICollection<string> separator = null, string assignment = null)
		{
			queryString ??= string.Empty;
			separator ??= new List<string> { "&" };
			assignment ??= "=";
			encoding ??= Encoding.UTF8;
			encodeFunc ??= (str, _) => str;

			queryString = queryString.TrimStart('?', '#', '&');

			try
			{
				var val = (from strKv in queryString.Split(separator.ToArray(), StringSplitOptions.RemoveEmptyEntries)
						   let idx = strKv.IndexOf(assignment, StringComparison.Ordinal)
						   let k = encodeFunc(idx == -1 ? string.Empty : strKv[..idx], encoding) ?? string.Empty
						   let v = encodeFunc(idx == -1 ? strKv : strKv[(idx + assignment.Length)..], encoding) ?? string.Empty
						   select new KeyValuePair<string, string>(k, v))
					.FirstOrDefault(x => key.Equals(x.Key));

				return Cast.ChangeType<T>(val);
			}
			catch
			{
				return default;
			}
		}

		/// <summary>
		/// 构建QueryString(k=v&amp;k=v...)
		/// </summary>
		/// <param name="ps"></param>
		/// <param name="encoding"></param>
		/// <param name="encodeQuery">提供编码或解码算法。默认无</param>
		/// <returns></returns>
		public static string BuildQueryString(ICollection<KeyValuePair<string, string>> ps,
			Encoding encoding = null,
			Func<string, Encoding, string> encodeQuery = null)
		{
			encoding ??= Encoding.UTF8;
			encodeQuery ??= (str, _) => str;

			if (ps == null || ps.Count == 0)
			{
				return string.Empty;
			}

			var lst = ps.Select(p =>
				$"{encodeQuery(p.Key, encoding)}={encodeQuery(p.Value, encoding)}").ToList();
			return string.Join('&', lst);
		}
	}
}
