using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using DG.Tweening;
using Spine.Unity;
public class UICustomButton : MonoBehaviour
{
    public Image mainImage;
    // public TextMeshProUGUI defaultLabel;
    public Text btn_txt;
    [SerializeField] private Color fontColor;

    [Header("For Buttons with Labels")]
    [SerializeField] private Shader greyScaleShader;
    [SerializeField] private Image baseImage;
    public Color BLUE;
    public Color GREEN;
    public Color YELLOW;
    public Color RED;
    public Color MUTED_GREEN;
    public Color PURPLE;
    public TextMeshProUGUI offLabel;


    [Header("For Spine and Normal Button")]
    [SerializeField] private bool isSpineButton;
    [SerializeField] private Transform parent;
    [SerializeField] private SkeletonGraphic spineObject;
    [SerializeField] private Sprite pressedSprite;
    private Sprite defaultSprite;

    [Header("For Buttons with Image")]
    [SerializeField] private Image topImage;

    public System.Action defaultAction;
    public System.Action offAction;

    private bool isOn = true;
    private bool isInteractable = true;

    private float defaultPos;

    private bool isMouseDown = false;
    private bool isAttentive = false;
    private bool isMouseDragging = false;

    private Material imageMat, baseOriginalMat, mainOriginalMat;

    Sequence seq;

    private void Start()
    {
        defaultPos = mainImage.GetComponent<RectTransform>().anchoredPosition.y;
        defaultSprite = mainImage.sprite;

    }

    public void SetIsOn(bool val)
    {
        if (val)
        {
            if(btn_txt) btn_txt.gameObject.SetActive(true);
            offLabel.gameObject.SetActive(false);
            mainImage.color = GREEN;
            baseImage.color = GREEN * 0.7f;
            isOn = val;
        }
        else
        {
            if (btn_txt) btn_txt.gameObject.SetActive(false);
            offLabel.gameObject.SetActive(true);
            mainImage.color = RED;
            baseImage.color = RED * 0.7f;
            isOn = val;
        }
    }

    public void SetInteractable(bool val)
    {
        isInteractable = val;
        if (greyScaleShader)
        {
            if (!imageMat)
            {
                baseOriginalMat = baseImage.material;
                mainOriginalMat = mainImage.material;
                imageMat = new Material(greyScaleShader);
                baseImage.material = imageMat;
                mainImage.material = imageMat;
            }
            if (!isInteractable)
            {
                baseImage.material = imageMat;
                mainImage.material = imageMat;
                imageMat.SetFloat("_EffectAmount", 1);
            }
            else
            {
                imageMat.SetFloat("_EffectAmount", 0);
                baseImage.material = baseOriginalMat;
                mainImage.material = mainOriginalMat;
            }
        }
    }

    public bool GetInteractable()
    {
        return isInteractable;
    }

    public void ShowPopUp(GameObject popUp)
    {

    }



    public void OnMouseEnter()
    {
#if UNITY_ANDROID || UNITY_IOS
        if (Globals.isTouchDragging)
            return;
#endif
        if (isInteractable)
        {
            MissionButtonController.canBePressed = false;
            if (isSpineButton)
            {
                if (btn_txt)
                {
                    btn_txt.color = fontColor;
                }
                parent.SetScale(1.1f);
                if (spineObject)
                    spineObject.AnimationState.SetAnimation(0, "animation", true);
            }
            else
            {
                mainImage.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, defaultPos + 3);
                if (isOn)
                {
                    // if (defaultLabel)
                    // {
                        // defaultLabel.color = fontColor;
                    // }
                    // else
                    // {
                        // topImage.color = fontColor;
                    // }
                    if (btn_txt)
                    {
                        btn_txt.color = fontColor;
                    }
                }
                else
                {
                    offLabel.color = fontColor;
                }

            }
            // AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_HOVER);
            //Globals.PlaySound(Constants.AudioClips.SOUND_HOVER, false, 1.0f);

        }


    }

    public void OnMouseExit()
    {

#if UNITY_ANDROID || UNITY_IOS
        if (Globals.isTouchDragging)
            return;
#endif
        if (isInteractable)
        {
            MissionButtonController.canBePressed = true;
            if (isSpineButton)
            {
                if (btn_txt)
                {
                    btn_txt.color = fontColor;
                }
                parent.SetScale(1f);
                if (spineObject)
                {
                    spineObject.AnimationState.SetAnimation(0, "none", true);
                }
                if (pressedSprite)
                {
                    mainImage.sprite = defaultSprite;
                }
            }
            else
            {
                if (isOn)
                {
                    // if (defaultLabel)
                    // {
                        // defaultLabel.color = Color.white;
                    // }
                    // else
                    // {
                        // topImage.color = Color.white;
                    // }
                    if (btn_txt)
                    {
                        btn_txt.color = fontColor;
                    }
                }
                else
                {
                    offLabel.color = Color.white;
                }

                if (!isAttentive)
                {
                    mainImage.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, defaultPos);
                }
                isMouseDown = false;
            }
        }
    }

    public void Submit()
    {
        isMouseDown = true;
        OnMouseUp();
    }

    public void OnMouseDown()
    {
#if UNITY_ANDROID || UNITY_IOS
        if (Globals.isTouchDragging)
            return;
#endif
        if (isInteractable)
        {
            MissionButtonController.canBePressed = false;
            if (!isMouseDown)
            {
                if (isSpineButton)
                {
                    if (pressedSprite)
                    {
                        mainImage.sprite = pressedSprite;
                    }
                }
                else
                {
                    if (!isAttentive)
                    {
                        mainImage.GetComponent<RectTransform>().DOAnchorPosY(defaultPos - 7.5f, 0.04f);
                    }
                }
                isMouseDown = true;
            }
        }
    }

    public void OnMouseUp()
    {
#if UNITY_ANDROID || UNITY_IOS
        if (Globals.isTouchDragging||isMouseDragging)
            return;
#endif
        if (isInteractable)
        {
            MissionButtonController.canBePressed = true;
            if (isMouseDown )
            {
                if (isSpineButton)
                {
                    if (pressedSprite)
                    {
                        mainImage.sprite = defaultSprite;
                    }
                }
                else
                {
                    if (!isAttentive)
                        mainImage.GetComponent<RectTransform>().DOJumpAnchorPos(Vector2.zero, 15 / 1.25f, 1, 0.04f * 2.5f);
                }


                if (isOn)
                {
                    defaultAction?.Invoke();
                }
                else
                {
                    offAction?.Invoke();
                }
                isMouseDown = false;
            }
        }
    }

    public void OnBeginDrag()
    {
        isMouseDragging = true;
    }

    public void OnEndDrag()
    {
#if UNITY_ANDROID || UNITY_IOS
        isMouseDragging = false;
#endif
       
    }

    public void CallForAttention(float speed)
    {
        isAttentive = true;
        mainImage.GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, defaultPos), 0);
        seq.Kill();
        DOTween.Sequence().Append(mainImage.GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, defaultPos - 0.75f), 0.04f * speed)).AppendInterval(0.04f * (speed + 1)).Append(mainImage.GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, 15f / 3.0f), 0.04f * speed)).Play();
        seq = DOTween.Sequence().AppendInterval(0.04f * (2 + speed * 4)).AppendCallback(() => { CallForAttention(speed); }).Play();
    }
    public void StopAttention()
    {
        isAttentive = false;
        seq.Kill();
        mainImage.GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, defaultPos), 0.1f);
    }

    public void SetButtonColor(Color color)
    {
        mainImage.color = color;
        baseImage.color = color * 0.7f;
    }

    public SkeletonGraphic GetSpineObject()
    {
        return spineObject;
    }
}




