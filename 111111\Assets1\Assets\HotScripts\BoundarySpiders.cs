using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
using Spine;

public class BoundarySpiders : Enemy
{
    enum States
    {
        Walk,
        Shoot,
        Idle
    };

    [SerializeField] Collider2D boundsCollider;
    [SerializeField] GameObject laser;

    [HideInInspector] public float healthMultiplier = 1;

    [HideInInspector] public int _isLeft;
    bool _didStartWalking = false;
    [HideInInspector] public float boundaryPosX = 0;
    bool _allowBoundaryChange;
    Bounds _bounds;
    Bone _bone;
    States _currentState;
    Transform playerTransform;

    public bool AllowBoundaryChange { get { return _allowBoundaryChange; } set { _allowBoundaryChange = value; } }

    public void CreateWithPosition(float pos, int Left)
    {
        boundaryPosX = pos;
        _isLeft = Left;
        Init();
    }
	
    public override void Init()
    {
        if (initialized)
            return;

        base.Init();
        InitStats();
        //SetAllowRelocate(false);

        playerTransform = GameManager.instance.player.transform;
        _currentState = States.Idle;
        explosionType = Explosions.ExplosionType.ExplosionTypeBuilding;

        transform.position = new Vector3(playerTransform.position.x, -2.87f);

        enemySprite.state.Data.SetMix("littleEntry", "littleIdle", 0.2f);
        enemySprite.state.Data.SetMix("littleIdle", "littleWalk", 0.2f);
        enemySprite.state.Data.SetMix("littleWalk", "littleLaser", 0.2f);
        enemySprite.state.Data.SetMix("littleLaser", "littleIdle", 0.2f);
        enemySprite.state.Data.SetMix("littleLaserOn", "littleLaserOn2", 0.2f);

        enemySprite.state.SetAnimation(0, "littleEntry", false);
        enemySprite.state.AddAnimation(0, "littleIdle", true);
        enemySprite.timeScale = 10 + Random.value;


        laser.SetActive(false);

        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        {
            if (spineEvent.Data.Name == "land")
            {
                GameManager.instance.ShakeCamera(2, 10);
                for (int i = 0; i < 4; i++)
                {
                    //SkeletonAnimation* waterExplosion = SkeletonAnimation::createWithJsonFile("res/Explosions/waterSplash.json", "res/Explosions/waterSplash.atlas", 0.25f);
                    //waterExplosion->setTimeScale(1.8 - CCRANDOM_0_1());
                    //waterExplosion->setScale(6, 6);

                    if (i == 0)
                    {
                        _bone = enemySprite.skeleton.FindBone("15");//front most leg
                        //waterExplosion->setScale(6, 6);
                    }
                    if (i == 1)
                    {
                        _bone = enemySprite.skeleton.FindBone("8");//back leg
                    }
                    if (i == 2)
                    {
                        _bone = enemySprite.skeleton.FindBone("4");//back leg
                    }
                    if (i == 3)
                    {
                        _bone = enemySprite.skeleton.FindBone("12");//front leg
                        //waterExplosion->setScale(4, 4);
                    }

                    //waterExplosion->setPosition(enemySprite->getPosition().x + _bone->worldX, LOWERBOUNDARY - 150);
                    //char ch[32];
                    //sprintf(ch, "bg%d", g_bgType);
                    //waterExplosion->setAnimation(0, ch, false);

                    //waterExplosion->setVisible(false);
                    //waterExplosion->runAction(Sequence::create(Show::create(), DelayTime::create(0.5), RemoveSelf::create(), NULL));
                }
            }

            if (spineEvent.Data.Name == "littleLaserOn")
            {
                laser.SetActive(true);

            }
            if (spineEvent.Data.Name == "littleLaserOff")
            {
                laser.SetActive(false);
            }
            if (spineEvent.Data.Name == "startWalk" && !isDestroyed)
            {
                enemySprite.timeScale = 0.5f;

                if (!_didStartWalking)
                {
                    _didStartWalking = true;
                    if (_isLeft == 1)
                    {
                        transform.DOBlendableMoveBy(new Vector3(Globals.CocosToUnity(2500), 0, 0), 30).OnComplete(() =>
                            {
                                enemySprite.state.SetAnimation(0, "littleLaserOn", true);
                            });
                    }
                    else if (_isLeft == 0)
                    {
                        transform.DOBlendableMoveBy(new Vector3(-Globals.CocosToUnity(2500), 0, 0), 30).OnComplete(() =>
                            {
                                enemySprite.state.SetAnimation(0, "littleLaserOn", true);
                            });
                    }
                }

            }
        };

        DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
        {
            ChangeState();
        });

        healthBar.gameObject.SetActive(true);
    }

    void ChangeState()
    {
        if (isDestroyed) return;
        if (_currentState == States.Idle)
        {
            _currentState = States.Walk;
            enemySprite.state.SetAnimation(0, "littleWalk", true);
            enemySprite.timeScale = 3;

            transform.DOMove(new Vector3(boundaryPosX, transform.position.y, transform.position.z),
                Mathf.Abs(boundaryPosX - transform.position.x) / Globals.CocosToUnity(2000)).OnComplete(() =>
                {
                    ChangeState();
                });
        }
        else if (_currentState == States.Walk)
        {
            _currentState = States.Shoot;
            if (_isLeft != 3)
            {
                enemySprite.state.SetAnimation(0, "littleLaserOn", false);
                enemySprite.state.AddAnimation(0, "littleLaserOn2", true);
            }
            else
            {
                enemySprite.state.SetAnimation(0, "littleLaserOn", true);
            }
            ShootLaser();
            enemySprite.timeScale = 1;
        }
        else if (_currentState == States.Shoot)
        {
            _currentState = States.Idle;
            laser.SetActive(false);
            enemySprite.state.SetAnimation(0, "littleIdle", true);
            DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
            {
                ChangeState();
            });
        }
    }

    public void SetAttributeByLord(double damage, double health)
    {
        stats.bulletDamage = damage / 5;
        stats.health = stats.maxHealth.Value = health / 5;
    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 2;

        stats.health = baseStats.health = 3500 * healthMultiplier;
        stats.turnSpeed = baseStats.turnSpeed = 2;
        stats.bulletDamage = baseStats.bulletDamage = 4;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50;
        stats.coinAwarded = baseStats.coinAwarded = 10;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
    }


    public override bool CheckCollision(Vector2 P1)
    {
        if (Vector2.Distance(transform.position, P1) < Globals.CocosToUnity(400))
        {
            return _bounds.Contains(P1);
        }
        else
        {
            return false;
        }
    }

    void Update()
    {
        _bounds = boundsCollider.bounds;
        //healthBar->setPosition(enemySprite->getPosition().x - 60, LOWERBOUNDARY + 10);


        if (_allowBoundaryChange == true && _isLeft != 3 && _currentState == States.Shoot)
        {
            if (Globals.RIGHTBOUNDARY - Globals.LEFTBOUNDARY > Globals.CocosToUnity(4000))
            {
                if (_isLeft == 0)
                {
                    Globals.RIGHTBOUNDARY = Globals.RIGHTBOUNDARY +
                            (-Globals.RIGHTBOUNDARY + transform.position.x) * Time.deltaTime;
                }
                if (_isLeft == 1)
                {
                    Globals.LEFTBOUNDARY = Globals.LEFTBOUNDARY
                            + (-Globals.LEFTBOUNDARY + transform.position.x) * Time.deltaTime;
                }
            }
        }
        if (_currentState == States.Shoot)
        {

            _bone = enemySprite.skeleton.FindBone("laser");//back leg
            laser.transform.position = _bone.GetWorldPosition(enemySprite.transform);

            if (playerTransform.position.x > laser.transform.position.x - 0.5f
                && playerTransform.position.x < laser.transform.position.x + 0.5f)
            {
                if (laser.activeInHierarchy)
                {
                    if(GameManager.instance.player.canHit) GameManager.instance.player.GotHit(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 30, false);
                }
            }
        }
    }
    
    public override void Destroy()
    {
        StopAllActions();
        base.Destroy();
        //Globals.ResetBoundary();

    }
    
    public void StopAllActions()
    {
        if (transform != null && !isDestroyed)
        {
            transform.DOKill();
        }
    }

    void ShootLaser()
    {
        laser.SetActive(true);
    }

}
