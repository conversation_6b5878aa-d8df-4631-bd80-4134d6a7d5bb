using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
public class CagedSideKick : Enemy
{
    [SerializeField] private Turret turretLeft;
    [SerializeField] private Turret turretRight;
    [SerializeField] private GameObject powerUpEffect;
    [SerializeField] private Rocketeer sideKickSprite;
    [SerializeField] private Image timer;
    private bool isSideKickSaved;
    private PlayerPing missionPing;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowRelocate = false;

        isSideKickSaved = false;

        sideKickSprite.scheduleUpdate = false;
        enemyCollisionRadius = Globals.CocosToUnity(2000);
        transform.position = new Vector2(Globals.CocosToUnity(5900), Globals.LOWERBOUNDARY - 0.5f);
        enemySprite.state.SetAnimation(0, "idle", true);
        //sideKickSprite.transform.localPosition = new Vector2(0, enemySprite.transform.localPosition.x + Globals.CocosToUnity(20));
        sideKickSprite.SideKickSkeleton.state.SetAnimation(0, "menuIdle", true);
        //sideKickSprite.transform.parent = null;
        sideKickSprite.Init();
        sideKickSprite.transform.localPosition = new Vector2(0, 2.04f);

        DOTween.Sequence().SetId(schedulerId).AppendInterval(1f).AppendCallback(() =>
        {
            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);


            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);
        }).Play();

        turretLeft.Init();
        turretLeft.transform.parent = null;
        turretRight.Init();
        turretRight.transform.parent = null;
        DynamicZoom dz = DynamicZoom.Create(enemySprite.transform, 2, Globals.CocosToUnity(400), Globals.CocosToUnity(1300));

        scheduleUpdate = true;
        sideKickSprite.scheduleUpdate = false;
    }

    public override bool TakeHit(double damage)
    {
        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            damage *= 2.0f;
        }
        else if (Globals.gameModeType == GamePlayMode.Medium)
        {
            damage *= 1.0f;
        }
        else if (Globals.gameModeType == GamePlayMode.Hard)
        {
            damage *= 0.5f;
        }
        //print("Clamped" + Mathf.InverseLerp(0, 1, damage / 13.5f));
        //print("Original" + (damage / 135f));
        //timer.fillAmount += (damage / 13.5f) / 100f;

        //if (GameData.instance.fileHandler.FrontGun == (int)AllELEMENTS.frontLaser)
        //{
        //    timer.fillAmount += (damage / 10f) / 100;
        //}

        return false;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (Vector2.SqrMagnitude(P1 - (Vector2)enemySprite.transform.position) < enemyCollisionRadius)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;

        if (timer.fillAmount >= 1)
        {
            timer.gameObject.SetActive(false);
            sideKickSprite.SideKickSkeleton.state.SetAnimation(0, "select", false);
            sideKickSprite.SideKickSkeleton.state.AddAnimation(0, "idle", true);
            powerUpEffect.gameObject.SetActive(true);
            DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() => { powerUpEffect.gameObject.SetActive(false); }).Play();
            sideKickSprite.transform.parent = null;
            GameManager.instance.missionManager.MissionComplete();
            DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() =>
            {
                sideKickSprite.enabled = true;
            }).AppendInterval(0.1f).AppendCallback(() =>
            {
                sideKickSprite.scheduleUpdate = true;
                sideKickSprite.StartSidekick();
            }).Play();
            GameSharedData.Instance.sidekicksList.Add(sideKickSprite);
            GameManager.instance.missionManager.AddPoint();
            scheduleUpdate = false;
        }

        else
        {
            timer.fillAmount -= 0.07f * Time.deltaTime;
            timer.fillAmount = Mathf.Clamp(timer.fillAmount, 0, 1);
        }
    }
}
