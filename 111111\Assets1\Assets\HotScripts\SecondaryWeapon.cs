using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;

public class SecondaryWeapon : MonoBehaviour
{
    enum RearGunStrings { INCINERATOR = 0, PROTONSHIELD = 1, BACKFIRE = 2 }

    public int gunType;
    //public SkillType primarySkill;
    //public SkillType secondSkill;

    #region Serialized Fields
    [SerializeField] private PlayerController player;
    [SerializeField] private GameObject flameThrowerEffectPrefab, flameThrowerPrefab, backfireEffectPrefab;
    [SerializeField] private Animator specialWingsAnim;
    [SerializeField] private SkeletonAnimation protonUlti, coinsPowerupPrefab;
    [SerializeField] private Sprite[] bulletSprites;
    #endregion

    #region Private Fields
    private int gunLevel, numberOfProtonBullets = 0, backFireLevel = 3;
    private bool specialAbilityActive = false, equipped;
    private float rearGunTimerVal, rearGunCDFromPlist, bulletDamageFromPlist, bulletMultiplierFromPlist;
    private Bullet bulletLayer;
    private SkeletonAnimation playerSkeleton;
    private Animator flameThrowerEffectAnim, flameThrowerAnim, backfireAnim;
    private SkeletonAnimation flameThrowerEffectAnimation;
    private ProtonBulletSlot[] protonBulletSlots ;
    #endregion

    #region 新增的一些属性
    private Equipment.Item _secondEquipmentData;
    private int _protonNum;
    private CatSkill.Item _primarySkill;
    private CatSkill.Item _secondarySkill;
    private double defaultDamege;
    private double secondaryDamage;
    #endregion

    private void Start()
    {
        if (player.isWhiteCatType)
        {
            enabled = false;
            return;
        }
        if(GameData.instance.fileHandler.currentMission != 0)
        {
            //副武器这边改成读pb

            //怒气恢复读pb
            if (LuaToCshapeManager.Instance.SecondEquipmentID != 0)
            {
                _secondEquipmentData = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.SecondEquipmentID);
                rearGunCDFromPlist = _secondEquipmentData.CalcLevel * player.Stats.rearGunCD;
                equipped = true;
                _primarySkill = CatSkillScheme.Instance.GetItem(_secondEquipmentData.ConsignmentStyle);
                _secondarySkill = CatSkillScheme.Instance.GetItem(_secondEquipmentData.ActvID);
                gunType = _primarySkill.Type;
            }
            else
            {
                _secondEquipmentData = null;
                rearGunCDFromPlist = 0;
                equipped = false;
            }
            //粒子冲击波的动画混合设置
            //protonUlti.skeletonDataAsset.GetAnimationStateData().SetMix("idle", "idle", 0);
            //写死测试


            //枪的等级，主要和伤害，以及离子转动的速度有关
            gunLevel = 1;
            GameManager.instance.SetSpecialAbilityButton(equipped);
            if (!equipped)
                return;

            _protonNum = _primarySkill.AttackCount + player.Stats.addBulletNum;
            backFireLevel = _secondarySkill.AttackCount + player.Stats.addBulletNum;
            protonBulletSlots = new ProtonBulletSlot[_protonNum];

            for(int i = 0; i < protonBulletSlots.Length; i++)
            {
                protonBulletSlots[i].isOccupied = false;
                protonBulletSlots[i].angle = (360 / _protonNum) * i;
            }

            #region 辅助定时设计相关的
            Observer.RegisterCustomEvent(gameObject, "start_rear_shoot", () =>
            {
                GameManager.instance.SetSpecialAbilityButton(true);
                InvokeRepeating("InitRearWeapons", 2, 1);
            });
            #endregion

       

            SetAnimComponents();
            InstantiateEffects();
            InvokeRepeating("InitRearWeapons", 2, 1);

            rearGunTimerVal = 0;
            specialAbilityActive = false;
        }
        else
        {
            //protonUlti.skeletonDataAsset.GetAnimationStateData().SetMix("idle", "idle", 0);
            gunType = (int)(RearGunType)PlayerPrefs.GetInt("Category2");

            if (GameData.instance.fileHandler.currentMission == 0)
            {
                gunType = (int)RearGunType.ProtonCanon;
            }

            string gunName = ((GameData.instance.GetShop()["Category2"] as PList)["Gun" + (int)(gunType + 1)] as PList)["Name"] as string;
            int level = PlayerPrefs.GetInt(gunName);
            equipped = gunType == (int)RearGunType.FlameThrower && level <= 0 ? false : true;
            GameManager.instance.SetSpecialAbilityButton(equipped);

            if (!equipped)
                return;

            protonBulletSlots = new ProtonBulletSlot[3];

            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                protonBulletSlots[i].isOccupied = false;
                protonBulletSlots[i].angle = 120 * i;
            }
            {
                Observer.RegisterCustomEvent(gameObject, "start_rear_shoot", () =>
                {
                    GameManager.instance.SetSpecialAbilityButton(true);
                    InvokeRepeating("InitRearWeapons", 2, 1);
                });
            }
            switch ((int)gunType)
            {
                case 0:
                    gunLevel = GameData.instance.fileHandler.flameThrowerLevel;
                    break;
                case 1:
                    gunLevel = GameData.instance.fileHandler.protonGunLevel;
                    break;
                case 2:
                    gunLevel = GameData.instance.fileHandler.backFireLevel;
                    break;
                default:
                    gunLevel = 0;
                    break;
            }

            gunLevel = level;

            if (gunLevel == 0)
            {
                gunLevel = 1;
            }

            for (int i = 1; i <= (int)(GameData.instance.GetShop()["Category2"] as PList)["Total"]; i++)
            {
                string ch = "Gun" + i;

                if (((GameData.instance.GetShop()["Category2"] as PList)[ch] as PList)["Name"] as string == ((RearGunStrings)(int)gunType).ToString())
                {
                    string ch2 = "L" + gunLevel.ToString();
                    bulletDamageFromPlist = (int)(((((GameData.instance.GetShop()["Category2"] as PList)[ch] as PList)
                        ["Stats"] as PList)["stat1"] as PList)["Level"] as PList)[ch2];
                    bulletMultiplierFromPlist = System.Convert.ToSingle(((GameData.instance.GetShop()["Category2"] as PList)[ch] as PList)["Multiplier"]);
                    rearGunCDFromPlist = System.Convert.ToSingle(((GameData.instance.GetShop()["Category2"] as PList)[ch] as PList)["CoolDown"]);
                }
            }

            SetAnimComponents();
            InstantiateEffects();
            InvokeRepeating("InitRearWeapons", 2, 1);

            numberOfProtonBullets = 0;
            rearGunTimerVal = 0;
            specialAbilityActive = false;
            if (GameData.instance.fileHandler.currentMission == 0)
            {
                SetShootMode(false);
            }
        }   
    }

    private void SetAnimComponents()
    {
        //playerSkeleton = player.GetSkeletonAnimation();

        //if (gunType == ((int)(SkillType.FlameThrower)))
        //{
        //    playerSkeleton.skeleton.SetAttachment("flameGlow", "flameGlow");
        //    playerSkeleton.skeleton.SetAttachment("flame", "flame");
        //    playerSkeleton.skeleton.SetAttachment("flameThrower", "flameThrower");
        //}

        //if (gunType == ((int)SkillType.RearBackFire))
        //{
        //    playerSkeleton.skeleton.SetAttachment("backGlow", "backGlow");
        //    playerSkeleton.skeleton.SetAttachment("backGun", "backGun");
        //}
        //else if (gunType == ((int)SkillType.ProtonCanon))
        //{
        //    playerSkeleton.skeleton.SetAttachment("protonBar", "protonBar");
        //    playerSkeleton.skeleton.SetAttachment("protonCannon", "protonCannon");
        //    playerSkeleton.skeleton.SetAttachment("protonLight", "protonLight");
        //}
        //if (GameData.instance.fileHandler.currentMission == 0)
        //{
        //    playerSkeleton.skeleton.SetAttachment("protonBar", "protonBar");
        //    playerSkeleton.skeleton.SetAttachment("protonCannon", "protonCannon");
        //    playerSkeleton.skeleton.SetAttachment("protonLight", "protonLight");
        //}

        specialWingsAnim.Play("Main", 0, 1);
    }

    public void RemoveAttachments()
    {
        if (!equipped)
            return;

        playerSkeleton.skeleton.SetAttachment("flameGlow", null);
        playerSkeleton.skeleton.SetAttachment("flame", null);
        playerSkeleton.skeleton.SetAttachment("flameThrower", null);
        playerSkeleton.skeleton.SetAttachment("backGlow", null);
        playerSkeleton.skeleton.SetAttachment("backGun", null);
        playerSkeleton.skeleton.SetAttachment("protonBar", null);
        playerSkeleton.skeleton.SetAttachment("protonCannon", null);
        playerSkeleton.skeleton.SetAttachment("protonLight", null);
    }

    private void Update()
    {
        if (_primarySkill != null && gunType == ((int)SkillType.ProtonCanon))
        {
            ProtonUpdate();
        }
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            ProtonUpdate();
        }


    }
    /// <summary>
    /// 离子副武器旋转
    /// </summary>
    
    private void ProtonUpdate()
    {
        
        float radius = GameData.instance.fileHandler.currentMission != 0 ? (_primarySkill.AttackRadius / 10000) : Globals.CocosToUnity(100);
        float turnSpeed = Globals.UnityValueTransform(_primarySkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate); //GameData.instance.fileHandler.currentMission != 0 ? ((_primarySkill.Level + 1) * 1.5f * 2) : ((gunLevel + 1) * 1.5f * 2);

        for (int i = 0; i < protonBulletSlots.Length; i++)
        {
            if (protonBulletSlots[i].isOccupied && protonBulletSlots[i].bullet != null && player != null)
            {
                protonBulletSlots[i].bullet.transform.position = player.transform.position * Vector2.one
                                                                + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                                                                radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
            }

            protonBulletSlots[i].angle += turnSpeed * Time.deltaTime * 60;
            protonBulletSlots[i].angle = protonBulletSlots[i].angle < 0 ? 360 + protonBulletSlots[i].angle
                : protonBulletSlots[i].angle;
        }

    }

    private void InstantiateEffects()
    {
        if (gunType == ((int)SkillType.FlameThrower))
        {
            GameObject obj = Instantiate(flameThrowerEffectPrefab);

            var bone = playerSkeleton.skeleton.FindBone("flame");
            var pos = bone.GetWorldPosition(playerSkeleton.transform);

            obj.transform.parent = playerSkeleton.transform;
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(1.5f, 1.5f, 1) * Globals.UnityValueTransform(_primarySkill.BulletScale);

            flameThrowerEffectAnimation = obj.GetComponent<SkeletonAnimation>();
            obj.SetActive(false);


            GameObject obj1 = Instantiate(flameThrowerPrefab);

            //obj1.transform.parent = playerSkeleton.transform;
            obj1.transform.position = pos;
            obj1.transform.localScale = new Vector3(2 * transform.localScale.x, 2 * transform.localScale.x, 1);

            flameThrowerAnim = obj1.transform.GetChild(0).GetComponent<Animator>();
            float flameLevel = 1;
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 1);
        }
        else if (gunType == ((int)SkillType.RearBackFire))
        {
            GameObject obj = Instantiate(backfireEffectPrefab);

            Vector3 spawnPoint = player.transform.position * Vector2.one
                     + new Vector2(-Globals.CocosToUnity(80), 0);

            obj.transform.position = spawnPoint;
            obj.transform.localScale = new Vector3(2f, 2f, 1);
            obj.transform.parent = playerSkeleton.transform;
            backfireAnim = obj.GetComponent<Animator>();
            backfireAnim.Play("Main", 0, 1);
        }
    }
    /// <summary>
    /// 副武器循环设计
    /// </summary>
    
    void InitRearWeapons()
    {
        if (Globals.resetControls || !equipped)
            return;
        
        if (gunType == ((int)SkillType.FlameThrower) && equipped)
        {
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
            bulletLayer = go.GetComponent<Bullet>();
            // bool didFindBullet = false;
            // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
            // {
            //     if (!b.isInUse)
            //     {
            //         bulletLayer = b;
            //         didFindBullet = true;
            //         break;
            //     }
            // }
            // if (!didFindBullet)
            // {
            //     return;
            // }

            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.flameThrowerShoot);
           

            if (GameData.instance.fileHandler.currentMission != 0)
            {
                // Debug.LogWarning("副武器的id" + _primarySkill.Id.ToString());
                defaultDamege = _primarySkill.AttackValue;
                defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(_primarySkill.DamageCoe));
            }
            else
            {
                defaultDamege = bulletDamageFromPlist + (player.Stats.attack * bulletMultiplierFromPlist);
            }
            bulletLayer.setDamage(defaultDamege);



            bulletLayer.SetBulletType(Bullet.BulletType.RearFlameThrower);

            string boneName = "flame";
            Spine.Bone bone = playerSkeleton.skeleton.FindBone(boneName);
            Vector3 pos = bone.GetWorldPosition(playerSkeleton.transform);
            bulletLayer.transform.position = pos;

            float duration = 0.3f;
            bulletLayer.setRadiusEffectSquared(Globals.CocosToUnity(120));

            bulletLayer.gameObject.SetActive(true);
            bulletLayer.isInUse = true;
            bulletLayer.isRemovable = false;
            bulletLayer.SetSpriteFrame(null);
            bulletLayer.SetBulletType(Bullet.BulletType.RearFlameThrower);
            GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);

            float distance = 4;
            Vector2 dest = new Vector2(distance * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
                    distance * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
            bulletLayer.PlayBulletAnim(duration, dest, true);

            if (gameObject.activeInHierarchy)
            {
                StopCoroutine(nameof(PlayFlameThrowerEffectAnim));
                StartCoroutine(nameof(PlayFlameThrowerEffectAnim));
            }

            float flameLevel = 2;
            flameThrowerAnim.transform.parent.position = pos;
            flameThrowerAnim.transform.parent.rotation = Quaternion.Euler(0, 0, player.RotationInDegrees);
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 0);
        }

        if (gunType == ((int)SkillType.ProtonCanon) && equipped)
        {
            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                if (!protonBulletSlots[i].isOccupied)
                {
                    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);
                    //GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                    //bulletLayer = go.GetComponent<Bullet>();
                    bool didFindBullet = false;
                    foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                    {
                        if (!b.isInUse)
                        {
                            bulletLayer = b;
                            didFindBullet = true;
                            break;
                        }
                    }
                    if (!didFindBullet)
                    {
                        return;
                    }

                    bulletLayer.InitProtonBullet(
                        (Bullet b) =>
                        {
                            for(int j = 0; j < protonBulletSlots.Length; j++)
                            {
                                if(protonBulletSlots[j].bullet == b)
                                {
                                    protonBulletSlots[j].isOccupied = false;
                                    protonBulletSlots[j].bullet = null;
                                }
                            }
                        },
                        (Bullet b) =>
                        {
                            for (int j = 0; j < protonBulletSlots.Length; j++)
                            {
                                if (protonBulletSlots[j].bullet == b)
                                {
                                    protonBulletSlots[j].isOccupied = false;
                                    protonBulletSlots[j].bullet = null;
                                }
                            }
                        }
                    );

                    bulletLayer.gameObject.SetActive(true);
                    bulletLayer.SetBulletType(Bullet.BulletType.ProtonCanon);
                    if (GameData.instance.fileHandler.currentMission != 0)
                    {
                        // Debug.LogWarning("副武器的id" + _primarySkill.Id.ToString());
                        defaultDamege = _primarySkill.AttackValue;
                        defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(_primarySkill.DamageCoe));
                    }
                    else
                    {
                        defaultDamege = bulletDamageFromPlist + (player.Stats.attack * bulletMultiplierFromPlist);
                    }
                    bulletLayer.setDamage(defaultDamege);

                    bulletLayer.isRemovable = true;
                    bulletLayer.isDestroyAfterCollision = true;
                    bulletLayer.isInUse = true;
                    bulletLayer.SetSpriteFrame(bulletSprites[0]);
                    bulletLayer.transform.GetChild(2).gameObject.SetActive(true);
                    float radius = Globals.CocosToUnity(100);

                    bulletLayer.transform.position = player.transform.position * Vector2.one
                        + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                        radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                    bulletLayer.transform.localScale = new Vector3(4.2f, 4.2f, 1) * Globals.UnityValueTransform(_primarySkill.BulletScale);
                    bulletLayer.canCollionWithNormalEnemyBullet = true;
                    GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
                    protonBulletSlots[i].bullet = bulletLayer;
                    protonBulletSlots[i].isOccupied = true;

                    bulletLayer.spriteRenderer.sortingLayerName = "Player";
                    bulletLayer.spriteRenderer.sortingOrder = -19;
                    break;
                }
            }
        }

        if (gunType == ((int)SkillType.RearBackFire) && equipped)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.backFireSound);
            float realSkillSpeed = _primarySkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate;
            float bulletDistance = Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(_primarySkill.AttackTime);
            for (int i = 0; i < _protonNum; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    // Debug.LogWarning("副武器的id" + _primarySkill.Id.ToString());
                    defaultDamege = _primarySkill.AttackValue;
                    defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(_primarySkill.DamageCoe));
                }
                else
                {
                    defaultDamege = bulletDamageFromPlist + (player.Stats.attack * bulletMultiplierFromPlist);
                }
                bulletLayer.setDamage(defaultDamege);

                bulletLayer.transform.localScale = new Vector3(2, 2, 1) * Globals.UnityValueTransform(_primarySkill.BulletScale);
                bulletLayer.SetSpriteFrame(null);
                bulletLayer.transform.GetChild(1).gameObject.SetActive(true);
                float duration = Globals.UnityValueTransform(_primarySkill.AttackTime);


                float distance;
                float rotation;
                if (i == 0)
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }
                else if (i == 1)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -10;
                }
                else if (i == 2)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 10;
                }
                else if (i == 3)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = -30;
                }
                else if (i == 4)
                {
                    distance = Globals.CocosToUnity(60);
                    rotation = 30;
                }
                else
                {
                    distance = Globals.CocosToUnity(80);
                    rotation = 0;
                }

                float rot = player.RotationInDegrees + 180 + rotation;
                rot = rot < 0 ? 360 + rot : rot % 360;

                Vector3 spawnPoint = player.transform.position * Vector2.one + new Vector2(distance * Mathf.Cos(rot * Mathf.Deg2Rad), distance * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.transform.position = spawnPoint;

                rot = (player.RotationInDegrees + 180) % 360;

                Vector2 dest = new Vector2(bulletDistance * Mathf.Cos(rot * Mathf.Deg2Rad),
                        bulletDistance * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.PlayBulletAnim(duration, dest, true);
                bulletLayer.transform.rotation = playerSkeleton.transform.rotation;
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.isInUse = true;
                bulletLayer.isRemovable = true;
                bulletLayer.isFlipped = true;
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);

                backfireAnim.Play("Main", 0, 0);
            }
        }

        if (LuaToCshapeManager.Instance.SecondEquipmentID == 0)
        {
            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                if (!protonBulletSlots[i].isOccupied)
                {
                    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);

                    GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                    bulletLayer = go.GetComponent<Bullet>();
                    // bool didFindBullet = false;
                    // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                    // {
                    //     if (!b.isInUse)
                    //     {
                    //         bulletLayer = b;
                    //         didFindBullet = true;
                    //         break;
                    //     }
                    // }
                    // if (!didFindBullet)
                    // {
                    //     return;
                    // }

                    bulletLayer.InitProtonBullet(
                        (Bullet b) =>
                        {
                            for (int j = 0; j < protonBulletSlots.Length; j++)
                            {
                                if (protonBulletSlots[j].bullet == b)
                                {
                                    protonBulletSlots[j].isOccupied = false;
                                    protonBulletSlots[j].bullet = null;
                                }
                            }
                        },
                        (Bullet b) =>
                        {
                            for (int j = 0; j < protonBulletSlots.Length; j++)
                            {
                                if (protonBulletSlots[j].bullet == b)
                                {
                                    protonBulletSlots[j].isOccupied = false;
                                    protonBulletSlots[j].bullet = null;
                                }
                            }
                        }
                    );

                    bulletLayer.gameObject.SetActive(true);
                    bulletLayer.SetBulletType(Bullet.BulletType.ProtonCanon);
                    if (GameData.instance.fileHandler.currentMission != 0)
                    {
                        // Debug.LogWarning("副武器的id" + _primarySkill.Id.ToString());
                        defaultDamege = _primarySkill.AttackValue;
                        defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(_primarySkill.DamageCoe));
                    }
                    else
                    {
                        defaultDamege = bulletDamageFromPlist + (player.Stats.attack * bulletMultiplierFromPlist);
                    }
                    bulletLayer.setDamage(defaultDamege);

                    bulletLayer.isRemovable = true;
                    bulletLayer.isInUse = true;
                    bulletLayer.SetSpriteFrame(bulletSprites[0]);
                    bulletLayer.transform.GetChild(2).gameObject.SetActive(true);
                    float radius = Globals.CocosToUnity(100);

                    bulletLayer.transform.position = player.transform.position * Vector2.one
                        + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                        radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                    bulletLayer.transform.localScale = new Vector3(4.2f, 4.2f, 1) * (GameData.instance.fileHandler.currentMission != 0 ? Globals.UnityValueTransform(_primarySkill.BulletScale) : 1);
                    GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
                    protonBulletSlots[i].bullet = bulletLayer;
                    protonBulletSlots[i].isOccupied = true;

                    bulletLayer.spriteRenderer.sortingLayerName = "Player";
                    bulletLayer.spriteRenderer.sortingOrder = -19;
                    break;
                }
            }
        }
        rearGunTimerVal += rearGunCDFromPlist;
        player.playerHud.UpdateSpecialBar(rearGunTimerVal);
        //if (rearGunTimerVal > 99 && rearGunKey->isVisible() == false)
        //{
        //    comboFront->setAnimation(0, "berserk", true);
        //    rearGunKey->setVisible(true);
        //}
    }

    IEnumerator PlayFlameThrowerEffectAnim()
    {
        flameThrowerEffectAnimation.gameObject.SetActive(true);
        flameThrowerEffectAnimation.AnimationState.AddAnimation(0, "idle", false, 0);//.Play("Main", 0, 0);

        yield return new WaitForSeconds(0.367f);

        flameThrowerEffectAnimation.gameObject.SetActive(false);
    }

    
    public void ActivateSpecialAbility()
    {
        if (specialAbilityActive || !equipped)
            return;

        if (rearGunTimerVal >= 100 && player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            rearGunTimerVal = 0;
            player.playerHud.UpdateSpecialBar(rearGunTimerVal);
            ActiveSpecial();
            //rearGunKey->setVisible(false); TODO
            //comboFront->setAnimation(0, "idle", true); TODO
            if (GameData.instance.fileHandler.currentMission==0 && !Globals.specialAbilityUsedInTutorial)
            {
                Globals.specialAbilityUsedInTutorial = true;
                GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Exit_Special_Ability_Mode);
            }
        }
    }
    
    void ActiveSpecial()
    {
        bool didActivate = false;

        if (GameData.instance.fileHandler.currentMission != 0)
        { 
            if (_secondarySkill.Type == ((int)SkillType.ProtonCanonSecond))
            {
                didActivate = true;
                //protonUlti.transform.SetScale(1 / transform.root.localScale.x * 5.75f);
                //protonUlti.gameObject.SetActive(true);
                //protonUlti.state.SetAnimation(0, "idle", false);
                //DOTween.Sequence().AppendInterval(2.5f).AppendCallback(() =>
                //{
                //    protonUlti.gameObject.SetActive(false);
                //});

                DOTween.Sequence().AppendInterval(2.5f).AppendCallback(() => specialAbilityActive = false);

                secondaryDamage = _secondarySkill.AttackValue * player.Stats.rearGunDamage;
                secondaryDamage = Helper.GetPlayerBulletRealDamage(secondaryDamage, Globals.UnityValueTransform(_secondarySkill.DamageCoe));

                //Globals.allDamage = secondaryDamage;
                Globals.isClearAllButtle = true;
            }
            if (_secondarySkill.Type == ((int)SkillType.RearBackFireSecond))
            {
                didActivate = true;

                float destroyTime = 0.75f;
                {
                    SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
                    pAnimation.state.SetAnimation(0, "powerUp", false);

                    pAnimation.skeleton.SetColor(Color.yellow);
                    pAnimation.transform.SetScale(35);
                    pAnimation.transform.parent = transform;
                    pAnimation.transform.localPosition = Vector3.zero;
                    DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                    {
                        Destroy(pAnimation);
                    });
                }
                {
                    SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
                    pAnimation.state.SetAnimation(0, "powerUp", false);

                    pAnimation.skeleton.SetColor(Color.yellow);
                    pAnimation.transform.SetScale(25);
                    pAnimation.transform.parent = transform;
                    pAnimation.transform.localPosition = Vector3.zero;
                    DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                    {
                        Destroy(pAnimation);
                    });
                }
                {
                    SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
                    pAnimation.state.SetAnimation(0, "powerUp", false);

                    pAnimation.skeleton.SetColor(Color.yellow);
                    pAnimation.transform.SetScale(15);
                    pAnimation.transform.parent = transform;
                    pAnimation.transform.localPosition = Vector3.zero;
                    DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                    {
                        Destroy(pAnimation);
                    });
                }
                {
                    SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
                    pAnimation.state.SetAnimation(0, "powerUp", false);

                    pAnimation.skeleton.SetColor(Color.yellow);
                    pAnimation.transform.SetScale(10);
                    pAnimation.transform.parent = transform;
                    pAnimation.transform.localPosition = Vector3.zero;
                    DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                    {
                        Destroy(pAnimation);
                    });
                }
                DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                {
                    DOTween.Sequence().AppendCallback(BackfireSpecial).AppendInterval(0.04f).SetLoops(backFireLevel);
                });

                DOTween.Sequence().AppendInterval(4).AppendCallback(() => specialAbilityActive = false);
            }
            if (_secondarySkill.Type == ((int)SkillType.FlameThrowerSecond))
            {
                didActivate = true;

                float destroyTime = 0.75f;
                {
                    SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
                    pAnimation.state.SetAnimation(0, "powerUp", false);

                    pAnimation.skeleton.SetColor(Color.yellow);
                    pAnimation.transform.SetScale(8);
                    pAnimation.transform.parent = transform;
                    pAnimation.transform.localPosition = Vector3.zero;
                    DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
                    {
                        Destroy(pAnimation);
                    });
                }

                specialWingsAnim.Play("Main", 0, 0);

                //TODO
                //Sprite* wings = Sprite::createWithSpriteFrameName("wings1.png");
                //this->addChild(wings, -1);
                //wings->setScale(2.0);
                //wings->setAnchorPoint(Point(0.5, 0.15));
                //wings->setCameraMask(GAMECAMERA);
                //wings->setPosition(Player::getInstance()->getPosition());
                //wings->runAction(Sequence::create(Shared::createAnimation("wings%d.png", 1, 30, false), RemoveSelf::create(), NULL));
                //Shared::playSound("res/Sounds/SFX/wings.mp3");
                //wings->runAction(ScaleBy::create(0.5, 2));
                //wings->runAction(Repeat::create(Sequence::create(CallFunc::create([=](){
                //    wings->setPosition(Player::getInstance()->getPosition());


                //}),DelayTime::create(1.0f / 60.0f),NULL), 2000));
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.wings);

                for (int i = 0; i < _secondarySkill.AttackCount; i++)
                {
                    DOTween.Sequence().AppendInterval(0.5f + (0.05f * i)).AppendCallback(() =>
                    {
                        PlayerMissile missile = null;
                        bool didFindBullet = false;
                        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
                        {
                            if (!m.isInUse)
                            {
                                missile = m;
                                didFindBullet = true;
                                break;
                            }
                        }
                        if (!didFindBullet)
                        {
                            return;
                        }
                        missile.curSpeed = Globals.UnityValueTransform(_secondarySkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate);
                        missile.acceleration = 90;
                        missile.SurvivalTime = Globals.UnityValueTransform(_secondarySkill.AttackTime);
                        missile.Init();
                        missile.isInUse = true;
                        missile.isHoming = false;
                        DOTween.Sequence().AppendInterval(0.1f + Random.value * 0.1f).AppendCallback(() =>
                        {
                            missile.isHoming = true;
                        });
                        secondaryDamage = _secondarySkill.AttackValue * player.Stats.rearGunDamage;
                        secondaryDamage = Helper.GetPlayerBulletRealDamage(secondaryDamage, Globals.UnityValueTransform(_secondarySkill.DamageCoe));
                        missile.curSpeed = Globals.UnityValueTransform(_secondarySkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate);
                        missile.damage = secondaryDamage;
                        missile.transform.position = (Vector2)transform.position;
                        Vector3 rotation1 = playerSkeleton.transform.eulerAngles;
                        missile.transform.rotation = Quaternion.Euler(rotation1);
                        missile.boomRadius = Globals.UnityValueTransform(_secondarySkill.DamageRadius);
                        missile.isSpecialTracking = true;
                        GameSharedData.Instance.playerMissilesInUse.Add(missile);
                    });

                    //        this->runAction(Sequence::create(DelayTime::create(0.5f + (0.05 * i)), CallFunc::create([=](){
                    //    Sprite* rocketSmoke = Sprite::createWithSpriteFrameName("smokeSlashRight1.png");
                    //    this->addChild(rocketSmoke, -1);
                    //    rocketSmoke->setScale(1.5f);
                    //    rocketSmoke->setAnchorPoint(Point(0, 0.5f));
                    //    rocketSmoke->setPosition(missile->missileSprite->getPosition());
                    //    rocketSmoke->setRotation(missile->missileSprite->getRotation() - 180);
                    //    rocketSmoke->runAction(Sequence::create(Shared::createAnimation("smokeSlashRight%d.png", 1, 13, false), RemoveSelf::create(), NULL));
                    //    rocketSmoke->setCameraMask(GAMECAMERA);

                    //    if (GameSharedData::getInstance()->playerMissileArray.size() % 5 == 0)
                    //    {
                    //        Shared::playSound("res/Sounds/SFX/rocketeerShoot.mp3");

                    //    }
                    //}), NULL));
                }

                DOTween.Sequence().AppendInterval(2).AppendCallback(() => specialAbilityActive = false);
            }
        }
        else
        {
            didActivate = true;
            //protonUlti.transform.SetScale(1 / transform.root.localScale.x * 5.75f);
            //protonUlti.gameObject.SetActive(true);
            //protonUlti.state.SetAnimation(0, "idle", false);
            //DOTween.Sequence().AppendInterval(2.5f).AppendCallback(() =>
            //{
            //    protonUlti.gameObject.SetActive(false);
            //});

            DOTween.Sequence().AppendInterval(2.5f).AppendCallback(() => specialAbilityActive = false);

            Globals.allDamage = 50.0f;
        }


        if (didActivate)
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.powerUpCollected2);
            specialAbilityActive = true;
        }
    }

    void ForceActivateWingsSpecial()
    {
        float destroyTime = 0.75f;
        {
            SkeletonAnimation pAnimation = Instantiate(coinsPowerupPrefab);
            pAnimation.state.SetAnimation(0, "powerUp", false);

            pAnimation.skeleton.SetColor(Color.yellow);
            pAnimation.transform.SetScale(8);
            pAnimation.transform.parent = transform;
            pAnimation.transform.localPosition = Vector3.zero;
            DOTween.Sequence().AppendInterval(destroyTime).AppendCallback(() =>
            {
                Destroy(pAnimation);
            });
        }

        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.wings);
        for (int i = 0; i < 40; i++)
        {
            DOTween.Sequence().AppendInterval(0.5f + (0.05f * i)).AppendCallback(() =>
            {
                PlayerMissile missile = null;
                bool didFindBullet = false;
                foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
                {
                    if (!m.isInUse)
                    {
                        missile = m;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                missile.Init();
                missile.isInUse = true;
                missile.isHoming = false;
                DOTween.Sequence().AppendInterval(0.15f + Random.value * 0.15f).AppendCallback(() =>
                {
                    missile.isHoming = true;
                });

                missile.damage = 12 + (GameData.instance.fileHandler.playerLevel * gunLevel);
                missile.transform.position = (Vector2)transform.position;
                GameSharedData.Instance.playerMissilesInUse.Add(missile);
            });
        }
    }
    [IFix.Patch]
    void BackfireSpecial()
    {
        if (player.Mode != PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            return;
        }

        player.transform.SetWorldPosition(player.transform.position.x - Globals.CocosToUnity(3.5f) * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
            player.transform.position.y - Globals.CocosToUnity(3.5f) * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));

        GameManager.instance.ShakeCamera(0.2f, 1);
        GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
        bulletLayer = go.GetComponent<Bullet>();

        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.backFireSound);

        bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

        if (player.transform.position.y < -Globals.CocosToUnity(200))
        {
            bulletLayer.setReactToWater(false);
        }
        if (GameData.instance.fileHandler.currentMission != 0)
        {
            // Debug.LogWarning("副武器的id" + _primarySkill.Id.ToString());
            defaultDamege = _primarySkill.AttackValue;
            defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(_primarySkill.DamageCoe));
        }
        else
        {
            defaultDamege = bulletDamageFromPlist + (player.Stats.attack * bulletMultiplierFromPlist);
        }
        bulletLayer.setDamage(defaultDamege);

        bulletLayer.SetSpriteFrame(null);
        bulletLayer.transform.GetChild(1).gameObject.SetActive(true);
        bulletLayer.transform.SetScale(2);
        float duration = Globals.UnityValueTransform(_secondarySkill.AttackTime);
        //bullet->setBlendFunc(BlendFunc::ADDITIVE); TODO

        bulletLayer.isRemovable = true;
        bulletLayer.isFlipped = true;
        

        backfireAnim.Play("Main", 0, 0);
        float realSkillSpeed = _secondarySkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate;

        float bulletDistance = Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(_secondarySkill.AttackTime);//Globals.CocosToUnity(30);
        float distance = Globals.CocosToUnity(30);
        float rotation = -30 + Random.value * 60;

        bulletLayer.transform.SetWorldPosition(player.transform.position.x + distance * Mathf.Cos(Mathf.Deg2Rad * player.RotationInDegrees),
            player.transform.position.y + distance * Mathf.Sin(Mathf.Deg2Rad * player.RotationInDegrees));
        //bulletLayer.duration = duration;
        Vector2 dest = new Vector2(-bulletDistance * Mathf.Cos((player.RotationInDegrees + rotation) * Mathf.Deg2Rad),
                        -bulletDistance * Mathf.Sin((player.RotationInDegrees + rotation) * Mathf.Deg2Rad));
        bulletLayer.PlayBulletAnim(duration, dest, true);
        bulletLayer.transform.SetRotation(player.RotationInDegrees + rotation);
        GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
    }

    void FireMissile()
    {
        PlayerMissile missile = null;
        bool didFindBullet = false;
        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        missile.Init();
        missile.isInUse = true;

        missile.damage = 5;
        missile.transform.position = (Vector2)transform.position;
        GameSharedData.Instance.playerMissilesInUse.Add(missile);
    }

    public void SetShootMode(bool val)
    {
        if (val)
        {
            InvokeRepeating("InitRearWeapons", 2, 1);
        }
        else
        {

            CancelInvoke("InitRearWeapons");
        }
    }

    public bool IsEquipped()
    {
        return equipped;
    }
}

public struct ProtonBulletSlot
{
    public Bullet bullet;
    public float angle;
    public bool isOccupied;
}

public struct WhiteCateStruct
{
    public PlayerController player;
    public float angle;
    public bool isOccupied;
}

