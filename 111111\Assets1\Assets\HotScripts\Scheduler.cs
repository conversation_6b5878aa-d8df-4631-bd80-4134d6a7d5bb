using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class Scheduler
{
    private struct scheduleParam { public string name; public Action action; }
    private static List<scheduleParam> scheduledActions = new List<scheduleParam>();

    public static void CallScheduledActions()
    {
        foreach (scheduleParam s in scheduledActions)
        {
           s.action?.Invoke();
        }
    }

    public static void UnSchedule(string name)
    {
        for (int i=0;i<scheduledActions.Count;i++)
        {
            if (scheduledActions[i].name == name)
            {
                scheduledActions.RemoveAt(i);
                i--;
            }
        }
    }

    public static void Schedule(string name, Action action)
    {
        scheduleParam param = new scheduleParam();
        param.name = name;
        param.action = action;
        scheduledActions.Add(param);
    }
}
