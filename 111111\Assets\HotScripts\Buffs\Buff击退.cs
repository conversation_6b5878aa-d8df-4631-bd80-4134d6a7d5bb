﻿using System.Linq;
using System.Threading;

using UnityEngine;

using X.PB;

namespace Buffs
{
    /// <summary>
    /// 击退效果(需要配置成接管移动)
    /// </summary>
    public class Buff击退 : BuffEffectBase
    {
        protected override void DoWork_Do(CancellationToken cancel)
        {
            if (Buff.BuffScheduler.Creature.FightProp.CreatureType != CreatureType.CreatureTypeMonster) return;
            var 怪物刚体 = Buff.BuffScheduler.gameObject.GetComponentInChildren<Rigidbody2D>();
            if (!怪物刚体) return;
            
            var 击退速度 = Globals.UnityValueTransform(CsvRow_BuffEffect.Param3);

            // 玩家技能对击退速度的提升
            var HitBackSpeed = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.TotalEffect.HitBackSpeed.Value);
            var HitBackSpeedPct = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.TotalEffect.HitBackSpeedPct.Value);
            击退速度 += HitBackSpeed;
            击退速度 *= 1 + HitBackSpeedPct;

            var dir = Buff.BuffScheduler.transform.position - GameManager.instance.player.transform.position;
            var dir_1 = dir.normalized;
            var speed = 击退速度 * dir_1;

            if ((Buff.BuffScheduler.Creature as Enemy)!.isBoss)
            {
                // Boss的击退速度降低
                speed *= 0.2f;
            }
            else
            {
                // 非Boss的 质量加大
                //Buff.BuffScheduler.Creature.FightProp.Mass.Value = 10000;
                怪物刚体.isKinematic = true;
            }

            怪物刚体.velocity = speed;
        }

        protected override void DoKill_Do()
        {
            if (Buff.BuffScheduler.Creature.FightProp.CreatureType != CreatureType.CreatureTypeMonster) return;
            if ((Buff.BuffScheduler.Creature as Enemy)!.isBoss) return;
            
            // 非Boss 质量复原
            //Buff.BuffScheduler.Creature.FightProp.Mass.Value = Buff.BuffScheduler.Creature.FightProp.ActorProp.Mass;
            var 怪物刚体 = Buff.BuffScheduler.gameObject.GetComponentInChildren<Rigidbody2D>();
            if (怪物刚体)
            {
                怪物刚体.isKinematic = false;
            }
        }
    }
}