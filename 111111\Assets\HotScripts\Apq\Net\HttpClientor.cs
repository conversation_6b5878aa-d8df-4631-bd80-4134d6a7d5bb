﻿using System;
using System.Net.Http;

namespace Apq.Net
{
    /// <summary>
    /// HttpClient的单态封装
    /// </summary>
    public class HttpClientor : ISingleton<HttpClientor>
    {
        public static HttpClientor Instance => GlobalObject.GetOrAddSingleton<HttpClientor>();

        #region 封装的HttpClient对象

        private static readonly object LockObj = new();
        private static HttpClient _httpClient;

        /// <summary>
        /// 用于延迟创建对象(延迟到首次使用其Value属性时)
        /// </summary>
        private readonly Lazy<HttpClient> _lazyClient = new(() =>
        {
            lock (LockObj)
            {
                _httpClient ??= new HttpClient();
                return _httpClient;
            }
        });
        public HttpClient Client => _lazyClient.Value;
        #endregion

        /// <summary>
        /// 对新创建的实例进行初始化(可以为空方法,实现类应在创建单态实例后立即调用)
        /// </summary>
        public virtual HttpClientor Init()
        {
            return this;
        }

        protected bool CacheDisabled_m;
        /// <summary>
        /// 是否禁用缓存
        /// </summary>
        public bool CacheDisabled
        {
            get => CacheDisabled_m;
            set
            {
                if (CacheDisabled_m == value) return;

                if (value)
                {
                    Client.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                }
                else if (Client.DefaultRequestHeaders.Contains("Cache-Control"))
                {
                    Client.DefaultRequestHeaders.Remove("Cache-Control");
                }
                CacheDisabled_m = value;
            }
        }
    }
}