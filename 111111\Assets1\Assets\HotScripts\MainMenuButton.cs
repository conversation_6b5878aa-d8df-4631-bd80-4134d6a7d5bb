using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using DG.Tweening;
using UnityEngine.EventSystems;
public class MainMenuButton : MonoBehaviour
{
    public RectTransform rectTransform;
    [SerializeField] private System.Action mainFunc;
    [SerializeField] private Color selectedColor;
    private System.Action selectedFunc;
    private System.Action unselectedFunc;
    public System.Action mouseOverFunc;


    private bool isSelected = false;
    private bool childColors = false;
    private float renderFontSize = 50;
    [SerializeField] private TextMeshProUGUI mainLabel;
    [SerializeField] private TextMeshProUGUI childLabel;
    private bool isPointerOver;
    Sequence seq;
    private bool setChildColor = false;

    public void SetChildColorEnabled(bool val)
    {
        setChildColor = val;
    }

    public void InitVariables(string lblName, System.Action func, string fontName = "", bool useNewFont = false, int fontSize = 50)
    {
        if (useNewFont && fontName != "")
        {
            mainLabel.text = lblName;//, fontName, fontSize);TODO

        }
        else
        {
            mainLabel.text = lblName;//Label::createWithTTF(lblName, GAME_FONT, fontSize); TODO

        }
        mainFunc = func;
        Init();

    }

    public void SetIsSelected(bool val)
    {
        //Debug.Log("SetIsSelected:" + val);
        if (val)
        {
            if (isSelected)
            {
                return;
            }
            selectedFunc?.Invoke();
            mainLabel.color = selectedColor;
            if (setChildColor)
            {
                if(childLabel)
                    childLabel.color = selectedColor;
            }
            seq = DOTween.Sequence().SetUpdate(true).Append(mainLabel.transform.DOScale(new Vector2(1.05f, 1.05f), 0.35f).SetEase(Ease.InOutSine)).Append(mainLabel.transform.DOScale(new Vector2(1f, 1f), 0.35f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();

        }
        else
        {
            if(seq != null)
            {
                seq.Kill();
            }
            mainLabel.transform.SetScale(1);
            mainLabel.color = Color.white;
            if (setChildColor)
            {
                if(childLabel)
                    childLabel.color = Color.white;
            }
            unselectedFunc?.Invoke();
        }

        isSelected = val;
    }

    public bool GetIsSelected()
    {
        return isSelected;
    }


    public bool CheckMouseOver()
    {
        return true;
    }
    public void SetMouseOverFunc(System.Action func)
    {
        mouseOverFunc = func;
    }
    public void SetMainFunc(System.Action func)
    {
        mainFunc = func;
    }

    public void SetSelectedFunc(System.Action func)
    {
        selectedFunc = func;
    }

    public void SetUnSelectedFunc(System.Action func)
    {
        unselectedFunc = func;
    }

    public TextMeshProUGUI GetMainLabel()
    {
        return mainLabel;
    }

    private void Init()
    {
        //Shared::fontToCustom(_mainLabel);TODO
        //_renderFontSize = _mainLabel->getScaleX();TODO

        //if (_mainLabel->getLabelType() == Label::LabelType::TTF)
        //{
        //    _mainLabel->enableOutline(Color4B::BLACK, _mainLabel->getTTFConfig().fontSize / 18);
        //    _mainLabel->enableShadow(Color4B::BLACK, cocos2d::Size(0, -_mainLabel->getTTFConfig().fontSize / 22), 1);

        //}
        //else
        //{
        //    Shared::fontToCustom(_mainLabel);

        //}
        //return true;
    }

    public void SetPointerEnter(bool val)
    {
        //Debug.Log("SetPointerEnter:" + val);
        isPointerOver = val;
        if (val)
        {
            mouseOverFunc?.Invoke();
        }
    }

    public bool GetPointerOver()
    {
        return isPointerOver;
    }


    public void CallMainFunc()
    {
        mainFunc?.Invoke();
    }

    public void OnMouseDown()
    {

    }
}
