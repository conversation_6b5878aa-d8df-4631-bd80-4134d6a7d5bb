using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
public class CuteusMaximus : Enemy
{
    public enum States
    {
        idle,
        attack1,
        attack2,
        attack3,
        attack4,
        attack5,
        attack6,
        attack7,
        attack8,
        rest
    };

    States currentState;
    int followCounter;
    int restCounter;

    [HideInInspector] public bool waitCheckBetweenAttack;
    [HideInInspector] public bool allowDefelection = true;
    [SerializeField] private TrailRenderer trail;

    private const float TIME_IN_ATTACKS = 0.12f;


    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();

        enemyCollisionRadius = 0;
        //code here
        enemySprite.state.SetAnimation(0, "idle", true);
        enemySprite.state.Data.SetMix("attack1", "idle", 0.1f);
        enemySprite.state.Data.SetMix("attack2", "idle", 0.1f);
        enemySprite.state.Data.SetMix("attack3", "idle", 0.1f);
        scheduleUpdate = true;
        //Update();
        //enemySprite.setPosition(cocos2d.Point(Player.getInstance().getPosition().x + 1400, Player.getInstance().getPosition().y + 50));



        //        enemySprite.state.Event+=(TrackEntry entry, Spine.Event spineEveny)=>{

        //        if (Player.getStats().mode != Player.PLAYER_MODE_DEATHDROP)
        //        {
        //            if (strcmp(event.Data.Name, "") == 0){

        //    }

        //    }
        //});






        //MotionStreak* streak = MotionStreak.create(0.4f, 10, 45, Color3B.WHITE, "res/Arsenal/cuteusStreak.png");
        //this.addChild(streak, 1, 5);
        //streak.setVisible(false);
        //streak.setFastMode(false);
        //streak.setCameraMask(GAMECAMERA);



    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = baseStats.speed = 2;
        stats.health = baseStats.health = (player.Stats.maxHealth.Value + 20) + Globals.difficulty * 5;
        stats.turnSpeed = baseStats.turnSpeed = 2;
        stats.bulletDamage = baseStats.bulletDamage = 4;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50; ;
        stats.coinAwarded = baseStats.coinAwarded = 10;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;


    }

    public void CuteusUpdate()
    {
        if (!scheduleUpdate)
            return;
        if (allowDefelection)
            FindBullets();
    }

    private void ManageState()
    {
        if (currentState == States.idle)
        {
            currentState = States.attack1;
            enemySprite.state.SetAnimation(0, "attack1", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();
        }
        else if (currentState == States.attack1)
        {
            currentState = States.attack2;
            enemySprite.state.SetAnimation(0, "attack2", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();


        }
        else if (currentState == States.attack2)
        {
            currentState = States.attack3;
            enemySprite.state.SetAnimation(0, "attack3", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();


        }
        else if (currentState == States.attack3)
        {
            currentState = States.attack4;
            enemySprite.state.SetAnimation(0, "attack1", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();

        }
        else if (currentState == States.attack4)
        {
            currentState = States.attack5;
            enemySprite.state.SetAnimation(0, "attack2", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();

        }
        else if (currentState == States.attack5)
        {
            currentState = States.attack6;
            enemySprite.state.SetAnimation(0, "attack3", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();

        }
        else if (currentState == States.attack6)
        {
            currentState = States.attack7;
            enemySprite.state.SetAnimation(0, "attack1", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();

        }
        else if (currentState == States.attack7)
        {
            currentState = States.attack1;
            enemySprite.state.SetAnimation(0, "attack2", false);
            waitCheckBetweenAttack = true;
            DOTween.Sequence().AppendInterval(TIME_IN_ATTACKS).AppendCallback(IdleAnimation).Play();

        }
        enemySprite.state.AddAnimation(0, "idle", false);

    }

    private void IdleAnimation()
    {
        waitCheckBetweenAttack = false;
        enemySprite.state.SetAnimation(0, "idle", true);
        //trail.emitting =false;
        //StopAllCoroutines();
        //StartCoroutine(TurnOffTrail());
    }

    private void SpawnBullet(Bullet bullet)
    {

        //bulletLayer.setDamage(enemyBullet.getDamage() * 2);
        //bulletLayer.setRadiusEffectSquared(100);
        //Sprite* bullet = bulletLayer.bulletSprite;

        //bulletLayer.setBulletTexture(enemyBullet.bulletSprite.getTexture().getPath());
        //bullet.setScale(enemyBullet.bulletSprite.getScale());
        //bullet.setFlippedX(true);
        //bullet.setCameraMask(GAMECAMERA);


        //bullet.setPosition(enemySprite.getPosition().x, enemySprite.getPosition().y);
        //float duration = 4;
        //bullet.setRotation(enemyBullet.bulletSprite.getRotation() + 90);
        //bulletLayer.duration = duration;
        //bullet.runAction(Sequence.create(MoveBy.create(duration, cocos2d.Point(5000 * sinf(CC_DEGREES_TO_RADIANS(bullet.getRotation())), 5000 * cosf(CC_DEGREES_TO_RADIANS(bullet.getRotation())))).Play();
        //bullet.setRotation(bullet.getRotation() + 90);
        //bullet.setColor(Color3B(255, 180, 180));
        //GameSharedData.getInstance().g_bulletsToAddArray.pushBack(bulletLayer);


    }

    private void FindBullets()
    {
        followCounter++;
        foreach (Bullet bullet in  GameSharedData.Instance.playerBulletInUse)
        {
            if (Vector2.SqrMagnitude(transform.position-bullet.transform.position) < Globals.CocosToUnity(250) && waitCheckBetweenAttack == false)
            {
                //trail.emitting=true;
                if (bullet.transform.position.x< transform.position.x)
                {
                    transform.SetScaleX(-1);
                }
                else
                {
                    transform.SetScaleX(1);
                }
                transform.position= bullet.transform.position;
                ManageState();


                //            spawnBullet(bullet);
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.catsWalkerAttack);
                //Globals.PlaySound("res/Sounds/SFX/catsWalkerAttack.mp3");
                bullet.HasHit();
                //bullet.hasHit = true;
                //if (bullet.isRemovable)
                //{
                //    GameSharedData.Instance.RemovePlayerBullet(bullet);
                //}
                return;
            }
        }
        if (followCounter > 60)
        {
            currentState = States.idle;
            followCounter = 0;
        }
    }

    IEnumerator TurnOffTrail()
    {
        yield return new WaitForSeconds(0.2f);

        trail.emitting = false;
    }

}
