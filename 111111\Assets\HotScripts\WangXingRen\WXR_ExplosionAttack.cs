using Spine.Unity;

using UnityEngine;

using X.PB;

public class WXR_ExplosionAttack : MonoBehaviour
{
    [HideInInspector] public SkeletonAnimation skeletonAnimation;
    [HideInInspector] public int defaultOrderInLayer;

    [HideInInspector] public CatSkill.Item skillData;
    [HideInInspector] public double damage;

    private bool inUse = false;

    public bool IsInUse { get { return inUse; } set { inUse = value; } }


    private void Awake()
    {
        skeletonAnimation = GetComponent<SkeletonAnimation>();
        defaultOrderInLayer = skeletonAnimation.GetComponent<MeshRenderer>().sortingOrder;
        //skeletonAnimation.state.SetAnimation(0, "explosion8", false);
    }


    public void Reset()
    {
        if (skeletonAnimation)
        {
            skeletonAnimation.state.TimeScale = 0;
            skeletonAnimation.GetComponent<MeshRenderer>().sortingOrder = defaultOrderInLayer;
        }
        gameObject.SetActive(false);
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        transform.position = Vector3.zero;
        //GameSharedData.Instance.wxrExplosionsAttackInUse.Remove(this);
        inUse = false;
    }
    public void PlayAnimation()
    {
        //inUse = true;
        //gameObject.SetActive(true);
        ////skeletonAnimation.state.TimeScale = 1;
        //var scale = Globals.UnityValueTransform(skillData.DamageRadius);
        //if(skillData.NextLv == 0) {
        //    EffectMgr.Instance.ShowEffect(EffectPath.JS_huan_Nv01_skill01_hit, transform.position, scale, EffectMgr.Instance.transform);
        //} else {
        //    EffectMgr.Instance.ShowEffect(EffectPath.JS_jian_Nv01_skill3, transform.position, scale, EffectMgr.Instance.transform);
        //}
    }

}
