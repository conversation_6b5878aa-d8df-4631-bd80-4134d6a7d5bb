using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Bowie : Sidekick
{
    //SuicideKitty::~SuicideKitty() TODO
    //{
    //    //    THRUST = THRUST - 0.25;
    //    //    TURN_SPEED = TURN_SPEED - 80;
    //    Player::getStats()->energyRegen -= 0.35f;
    //    Player::getStats()->attackSpeed -= 0.1f;
    //}
    
    void Start()
    {
        Init();
        

        //player.attackSpeedMultiplier = 1.1f;
        //player.Stats.energyRegen += 0.35f;

        player.SetMusicParticle(true);

        //this->schedule(schedule_selector(SuicideKitty::generateParticles), 0.2f);
        //this->schedule(schedule_selector(SuicideKitty::generatePlayerParticles), 0.4f);
    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);
        SetStartingPosition();

        transform.localScale = new Vector3(scale, scale, 1);
        Globals.blastAtPosition = Vector2.zero;
    }
   
    public void SetMonsterData()
    {
        if(monsterNew != null)
        {
            player.Stats.health += (Globals.UnityValueTransform(monsterNew.Hp) * player.Stats.health);
            player.Stats.maxHealth.Value = player.Stats.health;
            player.Stats.attack += monsterNew.PhysicsAttack;
        }
    }

    public override void StartSidekick()
    {
        sidekickSkeleton.state.SetAnimation(0, "idle", true);
    }

    private void Update()
    {
        SidekicksUpdate();
    }

    //void GeneratePlayerParticles()
    //{
    //    {
    //        Sprite* trail = Sprite::createWithSpriteFrameName("musicNote" + to_string(1 + rand() % 3) + ".png");
    //        Player::getInstance()->addChild(trail, -2);
    //        //    trail->setPosition( CCRANDOM_0_1()*10 + ( 200*sinf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()+90 ))),  CCRANDOM_0_1()*10  + ( 200*cosf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()+90 ))));
    //        trail->setPosition(-300 + CCRANDOM_0_1() * 600, -150 + CCRANDOM_0_1() * 300);
    //        trail->setScale(5 + CCRANDOM_0_1() * 3.0f);
    //        trail->setCameraMask(GAMECAMERA);

    //        trail->runAction(MoveTo::create(0.5f + CCRANDOM_0_1() * 0.1f, cocos2d::Point(0, trail->getPosition().x + 400 + CCRANDOM_0_1() * 200)));
    //        //    trail->runAction(MoveBy::create(0.4, cocos2d::Point( CCRANDOM_0_1()*10 + ( 100*sinf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()-90 ))),  CCRANDOM_0_1()*10  + ( 100*cosf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()-90 ))))));
    //        trail->runAction(Sequence::create(DelayTime::create(0.2), FadeTo::create(0.3f + CCRANDOM_0_1() * 0.1f, 0), RemoveSelf::create(), NULL));
    //        if (rand_0_1() < 0.5f)
    //        {
    //            trail->setLocalZOrder(2);
    //        }
    //    }
    //}


    //void GenerateParticles()
    //{
    //    {
    //        Sprite* trail = Sprite::createWithSpriteFrameName("musicNote" + to_string(1 + rand() % 3) + ".png");
    //        this->addChild(trail, -2);
    //        trail->setPosition(sideKickSprite->getPosition().x - 30 + CCRANDOM_0_1() * 60, sideKickSprite->getPosition().y - 10 + CCRANDOM_0_1() * 40);
    //        trail->setScale(0.5f + CCRANDOM_0_1() * 0.4f);
    //        trail->setCameraMask(GAMECAMERA);

    //        trail->runAction(MoveBy::create(0.5f + CCRANDOM_0_1() * 0.1f, cocos2d::Point(0, 100 + CCRANDOM_0_1() * 100)));
    //        trail->runAction(TintTo::create(0.3f, 255, 255, 255));
    //        trail->runAction(Sequence::create(DelayTime::create(0.2), FadeTo::create(0.3f + CCRANDOM_0_1() * 0.1f, 0), RemoveSelf::create(), NULL));
    //        if (rand_0_1() < 0.5f)
    //        {
    //            trail->setLocalZOrder(2);
    //        }
    //    }
    //}
}
