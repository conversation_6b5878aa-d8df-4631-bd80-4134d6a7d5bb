using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class Gibs : MonoBehaviour
{

    [HideInInspector] public bool isInUse = false;

    [SerializeField] private new SpriteRenderer renderer;
    [SerializeField] private Sprite[] mainSprite;
    [SerializeField] private Sprite smokeSprite;
    [SerializeField] private Sprite smokeSprite2;
    [SerializeField] private GameObject fire;
    [SerializeField] private GameObject strobe;
    [SerializeField] private GameObject trail;
    [SerializeField] private GameObject trail600;
    [SerializeField] private GameObject trail700;

    private const float gravity = 0.2f;
    private Vector2 _acceleration;
    private bool _belowWater = false;
    private int particleCount = 500;
    private int particleDecreaseVal = 18;
    private float force = 8;
    private bool _onlyUp = false;
    private bool _isBodyPart = false;
    private float timeToLast = 5;
    private bool scheduleUpdate = false;
    private bool generateParticles;

    public void Create()
    {
        Init();
    }

    public void CreateWithData(int particleCount, float force, bool onlyUp = false, bool planePart=false)
    {
        this.particleCount = particleCount;
        this.force = force;
        _onlyUp = onlyUp;
        _isBodyPart = planePart;
        scheduleUpdate = false;
        _acceleration = Vector2.zero;
        if (particleCount == 400)
        {
            trail.SetActive(true);
        }
        else if (particleCount == 600)
        {
            trail600.SetActive(true);
        }
        else if (particleCount == 700)
        {
            trail700.SetActive(true);
        }
        StopAllCoroutines();
        Init();
    }
    private void Init()
    {

        //print("here");
        //code here
        gameObject.SetActive(true);
        //StartCoroutine(GenerateParticles(0.03f));
        

        generateParticles = true;
        scheduleUpdate = true;
        //    this.runAction(Sequence::create(DelayTime::create(2), RemoveSelf::create(), NULL));

        if (_isBodyPart)
        {
            renderer.sprite = mainSprite[Random.Range(1,6)];
            //        mainSprite.setRotation(rand_0_1() * 360);
            //fire.transform.position=renderer.size / 2;
            fire.SetActive(true);
            particleDecreaseVal = 12;
        }
        else
        {
            renderer.sprite = smokeSprite2;
            renderer.transform.SetScale(1+particleCount / 500.0f);
            renderer.transform.DOScale(Vector3.zero, particleCount / 600f);
            strobe.SetActive(true);
            //strobe.transform.position =renderer.size / 2;
        }
        //    mainSprite.setColor(Color3B(255, 255, 51));
        //    mainSprite.setBlendFunc(BlendFunc::ADDITIVE);



        //    childSprite.setOpacity(100);
        //    mainSprite.setVisible(false;)



        //    _mStreak = MotionStreak::create(0.65f, 5, 20, Color3B::WHITE, "res/Arsenal/smokeTrail.png");
        //    this.addChild(_mStreak,-1);
        //    _mStreak.setCameraMask(GAMECAMERA);
        //    _mStreak.setBlendFunc(BlendFunc::ADDITIVE);

        if (Random.value < 0.5f)
        {
            _acceleration.x = force + Random.value * force;
        }
        else
        {
            _acceleration.x = -force - Random.value * force;

        }

        if (Random.value < 0.6f || _onlyUp)
        {
            _acceleration.y = force * 2 + Random.value * force * 2;
        }
        else
        {
            _acceleration.y = -force + Random.value * force;

        }
        if (_isBodyPart)
        {
            //        this.runAction(Sequence::create(DelayTime::create(1.5f), CallFunc::create([=](){
            //            this.unscheduleUpdate();
            //            this.unschedule(schedule_selector(Gibs::generateParticles));
            //
            //        }),DelayTime::create(3),RemoveSelf::create(), NULL));
        }
        else
        {
            StartCoroutine(ResetRoutine(1.5f,3f));
        }

        //print(_acceleration);
    }

    private void DisableTrail()
    {
        trail.GetComponent<ParticleSystem>().Stop();
        trail600.GetComponent<ParticleSystem>().Stop();
        trail700.GetComponent<ParticleSystem>().Stop();
    }

    //private IEnumerator GenerateParticles(float interval)
    //{
    //    while (generateParticles)
    //    {
    //        yield return new WaitForSeconds(interval);
    //        particleCount -= particleDecreaseVal;
    //        if (particleCount < 0)
    //        {
    //            if (!_isBodyPart)
    //            {
    //                renderer.DOFade(0, 0.1f);
    //            }
    //            yield break;
    //        }
    //        for (int i = 0; i < trail.Length; i++)
    //        {
    //            if (!trail[i].gameObject.activeInHierarchy)
    //            {
    //                trail[i].gameObject.SetActive(true);
    //                trail[i].transform.SetScale(particleCount / 200.0f);
    //                trail[i].transform.SetRotation(Random.value * 360);
    //                trail[i].transform.SetWorldPosition(trail[i].transform.position.x - Random.value * Globals.CocosToUnity(20), trail[i].transform.position.y - Random.value * Globals.CocosToUnity(20));

    //                trail[i].gameObject.SetActive(true);
    //                //    trail.setGlobalZOrder(-55);
    //                colorSequence = DOTween.Sequence();
    //                colorSequence.Append(trail[i].DOBlendableColor(new Color(244 / 255, 65 / 255, 0, 255 / 255), 0.06f)).Append(trail[i].DOBlendableColor(new Color(57 / 255, 43 / 255, 30 / 255, 255 / 255), 0.3f));
    //                colorSequence.Play();
    //                endSequence = DOTween.Sequence();
    //                endSequence.Append(trail[i].transform.DOBlendableScaleBy(new Vector2(particleCount / 100.0f, particleCount / 100.0f), 0.25f + Random.value * 0.1f)).Append(trail[i].DOFade(0, particleCount / 500.0f)).OnComplete(() => trail[i].gameObject.SetActive(false));
    //                endSequence.Play();
    //            }
    //        }
    //    }
    //}

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (GameManager.instance.timeManager.TimeScale <= 0)
                return;
            if (_isBodyPart && !_belowWater)
            {
                fire.transform.SetRotation(90 + Mathf.Rad2Deg * Globals.GetAngle(_acceleration));
            }
            if (transform.position.y > Globals.LOWERBOUNDARY - 0.5f)
            {
                _acceleration.y -= gravity * Time.deltaTime * 60;
            }
            else
            {

                if (!_belowWater)
                {
                    _belowWater = true;
                    //StopCoroutine(nameof(GenerateParticles));
                    DisableTrail();
                    if (!_isBodyPart)
                    {
                        Reset();
                    }
                    else
                    {
                        fire.SetActive(false);
                        strobe.SetActive(false);
                    }
                    return;
                }
                if (_acceleration.y < -2)
                {
                    _acceleration.y = -2;
                }
                _acceleration.y += gravity /4;
                if (_acceleration.x > 0)
                {
                    _acceleration.x -= gravity * Time.deltaTime * 60;
                }
                else
                {
                    _acceleration.x += gravity * Time.deltaTime * 60;
                }
            }
            transform.SetWorldPosition(transform.position.x + _acceleration.x * Time.deltaTime * Globals.CocosToUnity(60), transform.position.y + _acceleration.y * Time.deltaTime * Globals.CocosToUnity(60));
            timeToLast -= Time.deltaTime;
            if (timeToLast < 0)
            {
                scheduleUpdate = false;
                Reset();
            }
        }
    }

    private IEnumerator ResetRoutine(float startDelay, float endDelay)
    {
        yield return new WaitForSeconds(startDelay);
        scheduleUpdate = false;
        generateParticles = false;
        yield return new WaitForSeconds(endDelay);
        Reset();
    }

    public void Reset()
    {
        StopAllCoroutines();
        timeToLast = 5;
        renderer.transform.DOKill();
        scheduleUpdate = false;
        isInUse = false;
        _belowWater = false;
        _isBodyPart = false;
        trail.SetActive(false);
        trail600.SetActive(false);
        trail700.SetActive(false);
        transform.position = Vector3.zero;
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        renderer.sprite = null;
        renderer.transform.SetScale(1.4f);
        gameObject.SetActive(false);
    }
}
