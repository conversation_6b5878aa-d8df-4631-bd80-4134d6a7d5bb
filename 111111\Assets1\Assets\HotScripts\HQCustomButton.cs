using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
public class HQCustomButton : MonoBehaviour
{
    public System.Action onClick;
    public System.Action onEnter;
    public System.Action onExit;
    private new int tag;



    public void OnMouseUp()
    {
        if (Globals.onMainHQ && !Globals.isMouseOverUI && !Globals.isTouchDragging)
            onClick?.Invoke();
    }

    public void OnMouseExit()
    {
        if (Globals.onMainHQ && !Globals.isMouseOverUI && !Globals.isTouchDragging)
            onExit?.Invoke();
    }

    public void OnMouseEnter()
    {
        if (Globals.onMainHQ && !Globals.isMouseOverUI && !Globals.isTouchDragging)
            onEnter?.Invoke();
    }

    public void SetTag(int val)
    {
        tag = val;
    }

    public int GetTag()
    {
        return tag;
    }
}
