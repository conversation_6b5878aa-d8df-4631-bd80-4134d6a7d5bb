using UnityEngine;
using Spine.Unity;
using System.Collections;
using Spine;
using DG.Tweening;
using System;

public class SugarPuff : Enemy
{
    [SerializeField] private GameObject dash;
    [SerializeField] private Sprite bombSprite;
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] private GameObject flameObj;

    private enum STATES
    {
        STATE1,
        STATE2,
        STATE3
    }


    private bool movingLeft;
    private float originalSpeed = 0;
    private int bombCount = 0;
    private bool _bmode = false;
    private int sugarBombs = 8;
    private Bullet bullet;
    private Bomb newBomb;

    private MinionSideKick mSk;

    private STATES _currentState = STATES.STATE1;

    float xMin, xMax;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        Globals.isBossMode = true;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spIntro);
        //Globals.PlaySound("res/Sounds/Bosses/Boss2/spIntro.mp3");

        //code here
        transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(140), Globals.UPPERBOUNDARY + 2f);// Globals.CocosToUnity(50));
        Globals.bossPosition = transform.position;
        enemySprite.state.SetAnimation(0, "flying", true);
        mSk = GameManager.instance.minionSidekick;
        //enemySprite.state.Event+=setEventListener
        scheduleUpdate = true;
        //this.scheduleUpdate();
        xMin = Globals.bossPosition.x - Globals.CocosToUnity(1250);
        xMax = Globals.bossPosition.x + Globals.CocosToUnity(1250);

        //if (Globals.gameType != GameType.Survival)
        //{
        Globals.LEFTBOUNDARY = Globals.bossPosition.x - Globals.CocosToUnity(1050);
        Globals.RIGHTBOUNDARY = Globals.bossPosition.x + Globals.CocosToUnity(1050);
        //}

        InvokeRepeating(nameof(Shoot), 1.5f, 0.35f);
        Globals.bossShouldStayOnScreen = false;
        scheduleUpdate = true;
        //this.schedule(schedule_selector(SugarPuff::shoot), 0.35f, CC_REPEAT_FOREVER, 1.5f);

        Invoke(nameof(ResetBoundary), 2f);
    }

    private void ResetBoundary()
    {
        xMin = player.transform.position.x - Globals.GetScreenHalfWidth() - 2;
        xMax = player.transform.position.x + Globals.GetScreenHalfWidth() + 2;
        Globals.LEFTBOUNDARY = player.transform.position.x - Globals.GetScreenHalfWidth();
        Globals.RIGHTBOUNDARY = player.transform.position.x + Globals.GetScreenHalfWidth();
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (healthBar)
            {
                //healthBar.setPosition(enemySprite.getPosition().x - 25, enemySprite.getPosition().y - 120);
            }
            Vector2 position = player.transform.position;

            if (movingLeft)
            {
                transform.SetWorldPositionX(transform.position.x - stats.speed * Time.deltaTime * 0.6f);
                if (transform.position.x < xMin)//Globals.LEFTBOUNDARY - 30)//Globals.CocosToUnity(200))
                {
                    movingLeft = false;
                    enemySprite.skeleton.FindBone("root").ScaleX = 1;
                    //spSkeleton_findBone(enemySprite.getSkeleton(), "root").ScaleX = 1;
                    //            enemySprite.setScaleX(1);
                }
            }
            else
            {
                transform.SetWorldPositionX(transform.position.x + stats.speed * Time.deltaTime * 0.6f);//.setPositionX(enemySprite.getPosition().x + stats.speed * dt * 60);

                if (transform.position.x > xMax)//Globals.RIGHTBOUNDARY + Globals.CocosToUnity(200))
                {

                    movingLeft = true;
                    //spSkeleton_findBone(enemySprite.getSkeleton(), "root").ScaleX = -1;
                    enemySprite.skeleton.FindBone("root").ScaleX = -1;

                }
            }



            if (isBoss)
            {
                Globals.bossPosition = transform.position;//.getPosition();
                if (stats.health < stats.maxHealth.Value * 0.75f)
                {

                }


                if (Vector2.SqrMagnitude(player.transform.position - transform.position) < enemyCollisionRadius + player.CollisionRadius
                    && player.canHit)//  Player::getInstance().getPosition().distanceSquared(enemySprite.getPosition()) < enemyCollisionRadius + 1)
                {
                    //GETPLAYERCONTROLLER.gotHit(1.5f, false);
                    player.GotHit(1.5f);
                }
            }
        }
    }

    private void ShootInvincibleMissiles()
    {
        for (int i = 0; i < 10; i++)
        {
            HomingMissile missile = null;
            bool didFindMissile = false;
            foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
            {
                if (!m.isInUse)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindMissile = true;
                    break;
                }

            }
            if (!didFindMissile)
            {
                return;
            }
            missile.transform.position = transform.position;
            missile.missileSprite.sprite = bombSprite;
            missile.duration = 35;
            missile.transform.rotation = Quaternion.Euler(0, 0, i * 36);
            print(missile.transform.rotation.eulerAngles.z);
            missile.SetSpeed(8);
            missile.SetDamage(stats.missileDamage);
            missile.homingMultiplier = 1.0f;
            missile.isDestructable = false;
            missile.endFunc += () =>
            {
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, missile.transform.position, false, 1, 3, 0);
            };
            GameSharedData.Instance.enemyMissilesInUse.Add(missile);
            missile.CreateWithHomingDuration(20f);
            missile.RemoveAfterDuration();
        }
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        //    if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)
        //    {
        //        if (strcmp(event.Data.Name, "") == 0){

        //}
    }

    private void HandleBlast(Transform blast)
    {
        if (Vector2.Distance(blast.position, transform.position) < Globals.CocosToUnity(800))
        {
            dash.gameObject.SetActive(true);
            dash.transform.rotation = Quaternion.Euler(0, 0, -90 + Globals.CalcAngle(blast.position, transform.position) % 360);
            //dmgTaken->setRotation(-90 + Shared::calcAngle(blast->getPosition(), enemySprite->getPosition()));
            float blastDamage = 100;// 600; TODO return to 600
            TakeHit(blastDamage);
            //                shieldHp--;
            //                float opacity = _shield.getOpacity();
            //                _shield.setOpacity(255);
            //                _shield.runAction(FadeTo::create(0.8f, opacity - 20.0f));
            //                this.UpdateShieldHp();
            //                if(shieldHp == 0)
            //                {
            //                    Director::getInstance().getEventDispatcher().dispatchCustomEvent("shieldOff");
            //                }
        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();



        isBoss = true;
        int bossNumber = 3;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn);
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }
        //setAllowRelocate(false); TODO check
        allowRelocate = false;

        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.missileDamage = stats.bulletDamage * 5;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        explosionType = Explosions.ExplosionType.ExplosionTypeAir2;
        enemyCollisionRadius = 2;
        originalSpeed = stats.speed;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }

    private void OnExit()
    {

    }

    private void Shoot()
    {
        //if (!healthBar)TODO
        //{
        //    healthBar = HealthBar::create();
        //    this.addChild(healthBar);
        //    healthBar.setPositionY(-150);
        //    healthBar.setScaleRatio(2.0f);
        //    healthBar.setVisible(false);
        //}
        if (_bmode)
        {
            return;
        }
        bool didFindBomb = false;
        foreach (Bomb b in GameSharedData.Instance.enemyBombPool)
        {
            if (!b.isInUse)
            {
                newBomb = b;
                newBomb.isInUse = true;
                didFindBomb = true;
                break;
            }

        }
        if (!didFindBomb)
        {
            return;
        }
        newBomb.duration = 8;
        bombCount++;
        newBomb.Init();
        GameSharedData.Instance.enemyMissilesInUse.Add(newBomb);
        if (bombCount % sugarBombs != 0)
        {
            newBomb.isDestructable = false;
            newBomb.missileSprite.sprite = bombSprite;
            //newBomb._endFunc = (EnemyMissile e) =>{




            //};
        }
        else
        {
            newBomb.isDestructable = true;
            newBomb.endFunc += () =>
            {

                //Explosion* blast = Explosion::createWithExplosion(Explosion::ExplosionType::ExplosionTypeAoe, cocos2d::Point::ZERO, false, 1, 3, 0);
                //sender.addChild(blast, -1);
                //blast.setScale(1.5f);
                //blast.setPosition(newBomb.missileSprite.getPosition());
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAoe, newBomb.transform.position, false, 1, 3f, 0);

                //if (blast.getPosition().y > LOWERBOUNDARY)
                //{
                //    for (int i = 0; i < 1 + rand() % 2; i++)
                //    {
                //        Coins* coin = Coins::createAsOrb(blast.getPosition());
                //        sender.addChild(coin);
                //    }
                //}
                if (newBomb.transform.position.y < Globals.LOWERBOUNDARY + 1f)
                {
                    for (int i = 0; i < 7; i++)
                    {
                        bool didFindBullet = false;
                        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                        {
                            if (!b.isInUse)
                            {
                                bullet = b;
                                bullet.isInUse = true;
                                didFindBullet = true;
                                break;
                            }
                        }
                        if (!didFindBullet)
                        {
                            return;
                        }
                        bullet.setDamage(stats.bulletDamage);
                        bullet.SetSpriteFrame(bulletSprite);
                        bullet.duration = 6.5f;
                        bullet.transform.localScale = Vector2.one;
                        //bullet.setAnchorPoint(cocos2d::Point(0.5, 0.5));
                        //bullet.setCameraMask(GAMECAMERA);

                        bullet.setRadiusEffectSquared(0.7f);

                        bullet.transform.position = newBomb.transform.position;
                        bullet.transform.rotation = Quaternion.Euler(0, 0, -90 + (i * 30));
                        bullet.PlayBulletAnim(6, new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z + 90)), Globals.CocosToUnity(5000) * Mathf.Sin(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z + 90))));


                        GameSharedData.Instance.enemyBulletInUse.Add(bullet);// getInstance().g_bulletsToAddArray.pushBack(bulletLayer);
                        bullet.gameObject.SetActive(true);
                    }

                    AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spiderCatBullet2, 1, newBomb.missileSprite.transform.position);
                    //TODO Distance Sound
                    //Globals.PlaySound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3", newBomb.missileSprite.transform.position);
                }
                if (Globals.isBossMode)
                {
                    //Director::getInstance().getEventDispatcher().dispatchCustomEvent("blast", (void*)blast);
                    HandleBlast(newBomb.transform);
                }

                AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spiderCatBullet2, 1, newBomb.missileSprite.transform.position);
                //TODO Distance Sound
                //Globals.PlaySound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3", newBomb.missileSprite.transform.position);
            };
        }

        AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spShoot, 1, enemySprite.transform.position);
        //TODO Distance Audio
        //Globals.PlaySound("res/Sounds/Bosses/Boss2/spShoot.mp3", enemySprite.transform.position);
        if (isBoss)
        {
            newBomb.radiusSQ = 0.2f;//8000;
            newBomb._doExplodeOnWater = true;
        }
        else
        {
            newBomb.radiusSQ = 0.2f;
            newBomb.transform.localScale = new Vector2(newBomb.transform.localScale.x * 0.75f, newBomb.transform.localScale.y * 0.75f);

        }
        newBomb.transform.position = new Vector2(transform.position.x, transform.position.y + 0.2f);
        newBomb.SetDamage(stats.bulletDamage);

        if (movingLeft)
        {
            newBomb.SetHorizontalVelocity(-stats.speed / 500);
            newBomb.transform.localScale = new Vector2(-newBomb.transform.localScale.x, newBomb.transform.localScale.y);
            newBomb.transform.rotation = Quaternion.Euler(0, 0, -20);
        }
        else
        {
            newBomb.SetHorizontalVelocity(stats.speed / 500);

        }
        newBomb.RemoveAfterDuration();
    }

    public override bool TakeHit(double damage)
    {
        //if (_bmode || _currentState == STATES.STATE2)
        //{
        //    AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.enemyHit);

        //    //Globals.PlaySound("res/Sounds/SFX/enemyHit.mp3"); 
        //    return false;
        //}

        if (_currentState == STATES.STATE2)
        {
            return false;
        }
        if (healthBar)
        {
            stats.health -= damage;
            healthBar.gameObject.SetActive(true);
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            enemySprite.GetComponent<Renderer>().material.DOKill();
            enemySprite.GetComponent<Renderer>().material.color = Color.red;
            enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
        }


        if (stats.health <= stats.maxHealth.Value * 0.45f && _currentState == STATES.STATE1)
        {

            CancelInvoke();
            //Director::getInstance().getEventDispatcher().dispatchCustomEvent("ChangeBossState"); TODO in BossHUD

            _currentState = STATES.STATE2;

            //Director::getInstance().getEventDispatcher().dispatchCustomEvent("shieldOn");
            TurnShieldOn();
            GameManager.instance.ShakeCamera(1, 5);
            //shakeScreen = 5;
            //shakeIntensity = 50;

            DOTween.To(() => stats.speed, x => stats.speed = x, 0, 0.2f).OnUpdate(() =>
            {
                //stats.speed = val;
                if (stats.speed == 0)
                {
                    enemySprite.state.TimeScale = 1.5f;
                    enemySprite.state.SetAnimation(0, "flying", false);
                    for (int i = 0; i < 5; i++)
                    {
                        enemySprite.state.AddAnimation(0, "flying2", false, 0);
                        enemySprite.state.AddAnimation(0, "flying", false, 0);
                        enemySprite.state.AddAnimation(0, "flying2", false, 0);
                        enemySprite.state.AddAnimation(0, "flying", false, 0);
                        enemySprite.state.AddAnimation(0, "flying2", false, 0);
                        enemySprite.state.AddAnimation(0, "flying", false, 0);

                    }
                    if (player.transform.position.x < transform.position.x)
                    {
                        movingLeft = true;
                        //spSkeleton_findBone(enemySprite.getSkeleton(), "root").ScaleX = -1;
                        enemySprite.skeleton.FindBone("root").ScaleX = -1;
                    }
                    else
                    {

                        movingLeft = false;
                        //spSkeleton_findBone(enemySprite.getSkeleton(), "root").ScaleX = 1;
                        enemySprite.skeleton.FindBone("root").ScaleX = 1;
                    }
                }
            });
            StartCoroutine(StateChangeCoroutine());
        }
        if (stats.health < 0)
        {
            if (isBoss)
            {
                Globals.isBossMode = false;
                //this.unscheduleUpdate();
                Globals.bossPosition = Vector2.zero;
            }

            return true;
        }

        return false;
    }

    private IEnumerator StateChangeCoroutine()
    {
        yield return new WaitForSeconds(3f);
        ShootInvincibleMissiles();
        scheduleUpdate = false;
        transform.DOBlendableMoveBy(new Vector2(0, 8), 1f);
        yield return new WaitForSeconds(10f);
        transform.DOBlendableMoveBy(new Vector2(0, -8), 2.5f);
        _bmode = false;
        yield return new WaitForSeconds(2.5f);
        scheduleUpdate = true;
        stats.speed = originalSpeed + 2.0f;
        _currentState = STATES.STATE3;
        InvokeRepeating(nameof(Shoot), 1.5f, 0.2f);


        //mSk = MinionSideKick::create();
        //mSk.setFollowNode(this.enemySprite);
        //this.addChild(mSk);
        //mSk.SetFollowMode(this);
        //mSk.gameObject.SetActive(true);
        enemySprite.state.TimeScale = 1;
        enemySprite.state.SetAnimation(0, "flying", true);
    }



    private void TurnShieldOn()
    {
        enemySprite.state.SetAnimation(0, "flying", true);
        enemyCollisionRadius = 2;
        stats.speed = originalSpeed;
        _bmode = true;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        return base.CheckCollision(P1);
    }

    public override void Destroy()
    {
        //estroy(mSk.gameObject);
        healthBar.gameObject.SetActive(false);
        //if(gameObject != null && gameObject.activeInHierarchy)
        //    StartCoroutine(DestroyCoroutine());
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }

    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        GameSharedData.Instance.enemyList.Remove(this);
        isBoss = false;
        CancelInvoke();
        flameObj.SetActive(true);
        if (player.transform.position.x < transform.position.x)
        {
            movingLeft = true;
            enemySprite.skeleton.FindBone("root").ScaleX = -1;
            transform.DOBlendableMoveBy(new Vector2(player.transform.position.x, -13), 15).OnComplete(base.Destroy);//FIXME changed from -8 to player position
            flameObj.transform.localScale = new Vector2(0.75f, flameObj.transform.localScale.y);
            flameObj.transform.rotation = Quaternion.Euler(0, 0, 10);
            StartCoroutine(DestroyExplosionsCoroutine(true));


        }
        else
        {

            movingLeft = false;
            enemySprite.skeleton.FindBone("root").ScaleX = 1;
            transform.DOBlendableMoveBy(new Vector2(player.transform.position.x, -13), 15).OnComplete(base.Destroy);//FIXME changed from 8 to player position
            flameObj.transform.localScale = new Vector2(-0.75f, flameObj.transform.localScale.y);
            flameObj.transform.rotation = Quaternion.Euler(0, 0, -10);
            StartCoroutine(DestroyExplosionsCoroutine(false));


        }
        scheduleUpdate = false;
        enemySprite.state.SetAnimation(0, "defeatTalk2", true);
    }

    private IEnumerator DestroyExplosionsCoroutine(bool left)
    {
        for (int i = 0; i < 20; i++)
        {
            yield return new WaitForSeconds(0.35f);
            if (left)
            {
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x + 0.25f, transform.position.y + 0.25f), false, 1, 2, 0);
            }
            else
            {
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x - 0.25f, transform.position.y + 0.25f), false, 1, 2, 0);
            }
        }
    }

}
