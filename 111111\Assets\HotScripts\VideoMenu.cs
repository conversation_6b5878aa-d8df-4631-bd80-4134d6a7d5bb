using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
using UnityEngine.UI;
public class VideoMenu : MonoBehaviour
{
    int sceneNumber = 1;
    int fireworksCount = 0;
    int currentSelected = 0;
    [SerializeField] private SkeletonAnimation rays;
    List<GameObject> buttonArray = new List<GameObject>();
    [SerializeField] private GameObject blur;
    [SerializeField] private CustomButton video1Button;
    [SerializeField] private CustomButton video2Button;
    [SerializeField] private CustomButton video3Button;
    [SerializeField] private CustomButton closeButton;
    [SerializeField] private SkeletonGraphic introVideo;
    [SerializeField] private SkeletonGraphic outroVideo;
    [SerializeField] private GameObject videoPlayer;
    [SerializeField] private GameObject electricTransition;
    [SerializeField] private GameObject explosionTransition;
    [SerializeField] private SkeletonGraphic video3Lock;


    private void HandleAnimEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "end")
        {
            videoPlayer.SetActive(false);
        }
        if (spineEvent.Data.Name == "FULL_SCREEN_EXPLOSION")
        {
            float timeForTransition = 0.0f;
            //if (SpriteFrameCache::getInstance()->getSpriteFrameByName("ExplosionTransition1.png"))
            //{

            //    Sprite* energyTransition = Sprite::createWithSpriteFrameName("ExplosionTransition1.png");
            //    if (energyTransition)
            //    {
            //        energyTransition->setVisible(false);
            //        energyTransition->runAction(Sequence::create(DelayTime::create(timeForTransition), Show::create(), Shared::createAnimation("ExplosionTransition%d.png", 1, 27, false, 0.04f), RemoveSelf::create(), NULL));
            //        pop->addChild(energyTransition, 2);
            //        energyTransition->setPosition(-OFFSETX / 2 + winSize.width / 2, -OFFSETY / 2 + winSize.height / 2);
            //        energyTransition->setScale(Director::getInstance()->getWinSize().width / energyTransition->getContentSize().width, Director::getInstance()->getWinSize().height / energyTransition->getContentSize().height);
            //    }
            //}
            explosionTransition.SetActive(true);
        }

        if (spineEvent.Data.Name == "BLUE_EFFECT")
        {
            float timeForTransition = 0.0f;
            //if (SpriteFrameCache::getInstance()->getSpriteFrameByName("electricTransition1.png"))
            //{

            //    Sprite* energyTransition = Sprite::createWithSpriteFrameName("electricTransition1.png");
            //    if (energyTransition)
            //    {
            //        energyTransition->setVisible(false);
            //        energyTransition->runAction(Sequence::create(DelayTime::create(timeForTransition), Show::create(), Shared::createAnimation("electricTransition%d.png", 1, 11, false, 0.04f), FadeOut::create(0.05f), RemoveSelf::create(), NULL));
            //        pop->addChild(energyTransition, 2);
            //        energyTransition->setPosition(-OFFSETX / 2 + winSize.width / 2, -OFFSETY / 2 + winSize.height / 2);
            //        energyTransition->setScale(Director::getInstance()->getWinSize().width / energyTransition->getContentSize().width, Director::getInstance()->getWinSize().height / energyTransition->getContentSize().height);
            //    }
            //}
            electricTransition.SetActive(true);
        }

    }



    public void Init()
    {
        transform.localScale = Vector3.zero;
        gameObject.SetActive(true);
        closeButton.defaultAction = ClosePopUp;
        transform.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutElastic).OnComplete(()=> { blur.SetActive(true); });
        {


            //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/EndingScene/Fireworks.plist");

            {
                video1Button.defaultAction = () =>
               {



                   videoPlayer.SetActive(true);
                   //experimental::AudioEngine::pauseAll();
                   //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Transitions/electricTransition.plist");

                   //SkeletonAnimation* skAnimation = SkeletonAnimation::createWithJsonFile("res/Intro/FTUX_INTRO.json", "res/Intro/FTUX_INTRO.atlas");
                   //pop->addChild(skAnimation, 1);
                   //skAnimation->setPosition(winSize / 2);


                   //skAnimation->setAnimation(0, "animation", false);
                   //        Shared::rescale(skAnimation, 0.78f);
                   //skAnimation->setScale(0.78);
                   AudioManager.instance.PlaySound(AudioType.Menu, "introVideoPart1");

                   //Globals.PlaySound("res/Intro/introVideoPart1.mp3");
                   //Sprite* blackSpriteTop = Sprite::create("res/Backgrounds/black.png");
                   //Sprite* blackSpriteBottom = Sprite::create("res/Backgrounds/black.png");
                   //pop->addChild(blackSpriteTop, INT_MAX);
                   //pop->addChild(blackSpriteBottom, INT_MAX);
                   //float blackSpritesScaleX = 1;
                   //float blackSpritesScaleY = 1;

                   //if (aspectRatio < 1.7)
                   //{
                   //    blackSpritesScaleX = winSize.width / blackSpriteTop->getContentSize().width;
                   //    blackSpritesScaleY = ((-OFFSETY) / blackSpriteTop->getContentSize().height);
                   //    blackSpriteTop->setScale(blackSpritesScaleX, blackSpritesScaleY);
                   //    blackSpriteBottom->setScale(blackSpritesScaleX, blackSpritesScaleY);
                   //    blackSpriteTop->setPosition(pop->getContentSize().width / 2, pop->getContentSize().height);
                   //    blackSpriteBottom->setPosition(pop->getContentSize().width / 2, 0);
                   //}
                   //else if (aspectRatio > 1.8)
                   //{
                   //    blackSpritesScaleX = ((-OFFSETX) / blackSpriteTop->getContentSize().width);
                   //    blackSpritesScaleY = winSize.height / blackSpriteTop->getContentSize().height;
                   //    blackSpriteTop->setScale(blackSpritesScaleX, blackSpritesScaleY);
                   //    blackSpriteBottom->setScale(blackSpritesScaleX, blackSpritesScaleY);
                   //    blackSpriteTop->setPosition(pop->getContentSize().width, pop->getContentSize().height / 2);
                   //    blackSpriteBottom->setPosition(0, pop->getContentSize().height / 2);
                   //}
                   //else
                   //{
                   //    blackSpriteBottom->setVisible(false);
                   //    blackSpriteTop->setVisible(false);
                   //}
                   introVideo.AnimationState.Event += HandleAnimEvent;



                   buttonArray.Add(video1Button.gameObject);

               };

                video2Button.defaultAction = () =>
                 {


                     videoPlayer.SetActive(true);
                     //video2Layer = Layer::create();
                     //pop1->addChild(video2Layer);
                     // experimental::AudioEngine::pauseAll();
                     outroVideo.gameObject.SetActive(true);
                     //SkeletonAnimation* skAnimation = SkeletonAnimation::createWithJsonFile("res/Intro/FTUX_OUTRO.json", "res/Intro/FTUX_OUTRO.atlas");
                     //pop1->addChild(skAnimation, 1);
                     //skAnimation->setPosition(winSize / 2);
                     //skAnimation->setAnimation(0, "animation", false);
                     //skAnimation->setScale(0.78);
                     //const float OFFSETX = (1334 - (1334 / pop1->getScaleX())) / 1.0f;
                     //const float OFFSETY = (750 - (750 / pop1->getScaleY())) / 1.0f;
                     //skAnimation->setPosition(-OFFSETX / 2 + winSize.width / 2, -OFFSETY / 2 + winSize.height / 2); Shared::playSound("res/Intro/introVideoPart2.mp3");
                     //Sprite* blackSpriteTop = Sprite::create("res/Backgrounds/black.png");
                     //Sprite* blackSpriteBottom = Sprite::create("res/Backgrounds/black.png");
                     //pop1->addChild(blackSpriteTop, INT_MAX);
                     //pop1->addChild(blackSpriteBottom, INT_MAX);
                     //float blackSpritesScaleX = 1;
                     //float blackSpritesScaleY = 1;

                     //if (aspectRatio < 1.7)
                     //{
                     //    blackSpritesScaleX = winSize.width / blackSpriteTop->getContentSize().width;
                     //    blackSpritesScaleY = ((-OFFSETY) / blackSpriteTop->getContentSize().height);
                     //    blackSpriteTop->setScale(blackSpritesScaleX, blackSpritesScaleY);
                     //    blackSpriteBottom->setScale(blackSpritesScaleX, blackSpritesScaleY);
                     //    blackSpriteTop->setPosition(pop1->getContentSize().width / 2, pop1->getContentSize().height);
                     //    blackSpriteBottom->setPosition(pop1->getContentSize().width / 2, 0);
                     //}
                     //else if (aspectRatio > 1.8)
                     //{
                     //    blackSpritesScaleX = ((-OFFSETX) / blackSpriteTop->getContentSize().width);
                     //    blackSpritesScaleY = winSize.height / blackSpriteTop->getContentSize().height;
                     //    blackSpriteTop->setScale(blackSpritesScaleX, blackSpritesScaleY);
                     //    blackSpriteBottom->setScale(blackSpritesScaleX, blackSpritesScaleY);
                     //    blackSpriteTop->setPosition(pop1->getContentSize().width, pop1->getContentSize().height / 2);
                     //    blackSpriteBottom->setPosition(0, pop1->getContentSize().height / 2);
                     //}
                     //else
                     //{
                     //    blackSpriteBottom->setVisible(false);
                     //    blackSpriteTop->setVisible(false);
                     //}
                     outroVideo.AnimationState.Event += HandleAnimEvent;



                     buttonArray.Add(video2Button.gameObject);

                 };

                video3Button.SetInteractable(false);

                video3Button.defaultAction = () =>
                {

                    sceneNumber = 1;
                    videoPlayer.SetActive(true);
                    //experimental::AudioEngine::pauseAll();
                    Globals.StopAllSounds();
                    DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() =>
                    {
                        // AudioManager.instance.PlayMusic(Track.endMusic);
                        AudioManager.instance.PlayMusic(7007);

                        //Globals.PlaySound("res/Sounds/BGM/endMusic.mp3");
                    }).Play();


                    //video3Layer  = Layer::create();
                    //pop->addChild(video3Layer);
                    AddListenertoSkeleton();
                };
                //FIXME, 29 to 30
                if (GameData.instance.fileHandler.missionsCompleted > 29)
                {
                    video3Button.SetInteractable(true);

                }
                else
                {
                    video3Lock.gameObject.SetActive(true);
                }


                buttonArray.Add(video3Button.gameObject);

            }

            //rays = SkeletonAnimation::createWithJsonFile("res/vfx/buttonRays.json", "res/vfx/buttonRays.atlas");
            //this->addChild(rays, 1);
            //rays->setAnimation(0, "idle", true);
            //rays->setScale(0.75f);
            //rays->setVisible(false);

            //auto keyListener = EventListenerKeyboard::create();
            //keyListener->onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)
            //    {

            //    if (keyCode == EventKeyboard::KeyCode::KEY_ESCAPE)
            //    {
            //        return;
            //    }

            //        event->stopPropagation();


            //    if (keyCode == EventKeyboard::KeyCode::KEY_RIGHT_ARROW)
            //    {

            //        if (_currentSelected < _buttonArray.size() - 1 && _currentSelected >= 0)
            //        {
            //            _currentSelected++;
            //            rays->setPosition(_buttonArray.at(_currentSelected)->getPosition());
            //            rays->setVisible(true);
            //            Shared::playSound(SOUND_HOVER);

            //        }

            //    }

            //    else if (keyCode == EventKeyboard::KeyCode::KEY_LEFT_ARROW)
            //    {

            //        if (_currentSelected <= _buttonArray.size() && _currentSelected > 0)
            //        {
            //            _currentSelected--;
            //            rays->setPosition(_buttonArray.at(_currentSelected)->getPosition());
            //            rays->setVisible(true);
            //            Shared::playSound(SOUND_HOVER);


            //        }


            //    }

            //    else if (keyCode == EventKeyboard::KeyCode::KEY_ENTER)
            //    {

            //        NewButton* btn = dynamic_cast<NewButton*>(_buttonArray.at(_currentSelected));
            //        if (btn)
            //        {
            //            if (btn->getButtonEnabled())
            //            {
            //                btn->MainFunc(NULL);
            //                Shared::playSound(SOUND_BUTTON_TAP);
            //            }

            //        }
            //    }


            //};


            //_eventDispatcher->addEventListenerWithSceneGraphPriority(keyListener, this);
            //Shared::alignItemsHorizontallyWithPadding(Point(-400, 0), 200, _buttonArray, false);
            //if (isJoystickConnected)
            //{
            //    _currentSelected = 0;
            //    rays->setVisible(true);
            //    rays->setPosition(_buttonArray.at(_currentSelected)->getPosition());
            //}
            //return true;
        }
    }

    private void AddListenertoSkeleton()
    {

    }

    private void RemoveAllVideoTexture()
    {

    }

    public void ClosePopUp()
    {
        gameObject.SetActive(false);
    }



}
