﻿using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Apq.Net
{
    /// <summary>
    /// 代表一个连接到服务端的长连接(客户端)
    /// </summary>
    public abstract class PersistentClient : IDisposable
    {
        /// <summary>
        /// 代表一个连接到服务端的长连接(客户端)
        /// </summary>
        /// <param name="provider_NewClient">提供创建新连接的方法</param>
        protected PersistentClient(Func<object> provider_NewClient)
        {
            Provider_NewClient = provider_NewClient;
        }

        #region 服务器地址

        /// <summary>
        /// 域名/IP/Url
        /// </summary>
        public string Server { get; set; } = string.Empty;

        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; set; }

        #endregion

        /// <summary>
        /// 由服务端分配的会话ID(客户端标识)
        /// </summary>
        public Guid SessionID { get; set; }

        /// <summary>
        /// 客户端
        /// </summary>
        public object Client { get; protected set; }

        /// <summary>
        /// 任务的取消令牌:连接、接收、心跳、[发送]
        /// </summary>
        public CancellationTokenSource CTS_Connect { get; set; } = new();

        /// <summary>
        /// 任务的取消令牌:上一次连接
        /// </summary>
        public CancellationTokenSource CTS_PreConnect { get; set; } = new();

        /// <summary>
        /// 提供:创建新连接实例的方法。重连时使用
        /// </summary>
        /// <returns>基础TcpClient实例(或WebSocket等)</returns>
        public Func<object> Provider_NewClient { get; }

        #region IDisposable

        protected bool disposedValue;

        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposedValue) return;

            if (disposing)
            {
                UnBindClientEvents();
            }

            // 释放未托管的资源(未托管的对象)并重写终结器
            // 将大型字段设置为 null
            disposedValue = true;
		}

		// // TODO: 仅当“Dispose(bool disposing)”拥有用于释放未托管资源的代码时才替代终结器
		// ~PersistentClient()
		// {
		//     // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
		//     Dispose(false);
		// }

		public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion

        /// <summary>
        /// 设置Client相关事件的处理
        /// </summary>
        protected abstract void BindClientEvents();

        /// <summary>
        /// 取消Client相关事件的处理
        /// </summary>
        protected abstract void UnBindClientEvents();

        #region 连接

        /// <summary>
        /// 单次连接超时(秒)(国内网络环境，一般情况下3秒就够久了)
        /// </summary>
        /// <remarks> 总超时时长 = Timeout_Connect * (MaxTryTimes + 1) </remarks>
        public float ConnectTimeout { get; set; } = 3;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public abstract bool Connected { get; }

        /// <summary>
        /// 已尝试连接的次数
        /// </summary>
        public int TryTimes { get; set; }

        /// <summary>
        /// 手动连接时的最大重连的次数(&lt;0表示不限)
        /// </summary>
        public int MaxTryTimes { get; set; } = 8;

        /// <summary>
        /// 最后发起连接的时间
        /// </summary>
        public float LastConnectTime { get; private set; } = Time.unscaledTime;

        /// <summary>
        /// 连接到服务器(最多自动重连MaxTryTimes次)
        /// </summary>
        /// <returns>是否成功</returns>
        public async UniTask<bool> Connect(CancellationToken token = default)
        {
            // 取消断线重连任务
            CTS_ReconnectWhenInterrupted.Cancel();

            return await TryConnect(MaxTryTimes, token);
        }

        /// <summary>
        /// 连接到服务器(最多重连指定次数)
        /// </summary>
        /// <returns>是否成功</returns>
        protected async UniTask<bool> TryConnect(int maxTryTimes, CancellationToken token = default)
        {
            try
            {
                var success = false;
                for (TryTimes = 0; maxTryTimes < 0 || TryTimes < maxTryTimes; TryTimes++)
                {
                    CTS_PreConnect = CTS_Connect;
                    CTS_Connect = new();

                    // 取消使用旧连接的任务
                    CTS_PreConnect.Cancel();

                    token.ThrowIfCancellationRequested();

                    // 启动新连接的任务:连接一次
                    var cts = CancellationTokenSource.CreateLinkedTokenSource(token, CTS_Connect.Token);
                    bool isTimeout;
                    (success, isTimeout) = await TryConnectOnce(cts.Token);
                    if (success) break;

                    // 单次连接超时
                    if (isTimeout)
                    {
                        OnAfterConnectTimeoutOnce();
                    }
                }

                if (!success)
                {
                    // 经过重连也没成功,就算超时
                    OnAfterConnectTimeout();
                }

                return success;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试连接到服务器(一次)
        /// </summary>
        /// <returns>是否成功, 是否超时</returns>
        protected async UniTask<(bool success, bool isTimeout)> TryConnectOnce(
            CancellationToken token = default)
        {
            try
            {
                // 取消旧连接的事件处理
                UnBindClientEvents();

                // 创建新连接
                Client = Provider_NewClient.Invoke();

                // 设置新连接的事件处理
                BindClientEvents();

                LastConnectTime = Time.unscaledTime;
                var cts_once = new CancellationTokenSource();
                var cts = CancellationTokenSource.CreateLinkedTokenSource(token, cts_once.Token);

                // 发起连接
                DoTask_Connect(cts.Token).Forget();

                // 超时前是否连接成功
                if (await WaitConnectedWithTimeout(ConnectTimeout, cts.Token))
                {
                    OnAfterConnected();
                    return (true, false);
                }

                // 连接失败(含取消或超时),取消关联任务和连接的事件处理
                cts_once.Cancel();
                UnBindClientEvents();
                try
                {
                    // 关闭连接
                    CloseClient();
                }
                catch
                {
                    // ignore
                }

                return (false, true);
            }
            catch
            {
                return (false, false);
            }
        }

        /// <summary>
        /// Client发起连接
        /// </summary>
        protected abstract UniTaskVoid DoTask_Connect(CancellationToken token = default);

        /// <summary>
        /// 超时前是否连接成功
        /// </summary>
        /// <param name="timeout">超时时长(秒)</param>
        /// <param name="token"></param>
        protected async UniTask<bool> WaitConnectedWithTimeout(float timeout, CancellationToken token = default)
        {
            var endTime = Time.unscaledTime + timeout;
            for (; Time.unscaledTime < endTime; await UniTask.NextFrame())
            {
                if (token.IsCancellationRequested) return false;

                if (Connected)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 连接超时事件(一次连接)
        /// </summary>
        public event Action AfterConnectTimeoutOnce;

        protected void FireAfterConnectTimeoutOnce()
        {
            AfterConnectTimeoutOnce?.Invoke();
        }

        protected virtual void OnAfterConnectTimeoutOnce()
        {
            FireAfterConnectTimeoutOnce();
        }

        /// <summary>
        /// 连接超时事件
        /// </summary>
        public event Action AfterConnectTimeout;

        protected void FireAfterConnectTimeout()
        {
            AfterConnectTimeout?.Invoke();
        }

        protected virtual void OnAfterConnectTimeout()
        {
            FireAfterConnectTimeout();
        }

        /// <summary>
        /// 连接过程中发生异常时
        /// </summary>
        public event Action<Exception> AfterConnectError;

        protected void FireAfterConnectError(Exception e)
        {
            AfterConnectError?.Invoke(e);
        }

        protected virtual void OnAfterConnectError(Exception e)
        {
            FireAfterConnectError(e);
        }

        /// <summary>
        /// 连接成功事件
        /// </summary>
        public event Action AfterConnected;

        protected void FireAfterConnected()
        {
            AfterConnected?.Invoke();
        }

        /// <summary>
        /// 连接成功后的处理
        /// </summary>
        protected virtual void OnAfterConnected()
        {
            LastActiveTime = Time.unscaledTime;

            var token = CTS_Connect.Token;

			// 启动发送任务
			if (UseSendQueue)
			{
				StartSend(token);
			}

            // 启动接收任务
            StartReceive(token);

            // 启动心跳任务
            StartHeartBeat(token);

            FireAfterConnected();
        }

        #endregion

        #region 关闭

        /// <summary>
        /// 调用Client的关闭方法
        /// </summary>
        protected abstract void CloseClient();

        /// <summary>
        /// 关闭原因
        /// </summary>
        public string CloseReason { get; set; } = string.Empty;

        /// <summary>
        /// 是否主动关闭
        /// </summary>
        public bool IsCloseBySelf { get; protected set; }

        /// <summary>
        /// 连接关闭前
        /// </summary>
        public event Action<PersistentClient, string> BeforeClose;

        protected void FireBeforeClose(string e)
        {
            BeforeClose?.Invoke(this, e);
        }

        protected virtual void OnBeforeClose(string e)
        {
            FireBeforeClose(e);
        }

        /// <summary>
        /// 连接关闭后
        /// </summary>
        public event Action<PersistentClient, string> AfterClose;

        protected void FireAfterClose(string e)
        {
            AfterClose?.Invoke(this, e);
        }

        protected virtual void OnAfterClose(string e)
        {
            FireAfterClose(e);
        }

        /// <summary>
        /// 异步关闭长连接
        /// </summary>
        public async UniTaskVoid CloseAsync(string reason, bool bySelf = true)
        {
            IsCloseBySelf = bySelf;
            OnBeforeClose(reason);

            if (await DoTask_Close(reason))
            {
                OnAfterClose(reason);
            }
        }

        /// <summary>
        /// 任务实现:关闭长连接
        /// </summary>
        /// <returns>是否关闭成功</returns>
        protected UniTask<bool> DoTask_Close(string reason)
        {
            CloseReason = reason;

            // 取消 连接任务 和 断线重连任务
            CTS_Connect.Cancel();
            CTS_ReconnectWhenInterrupted.Cancel();

            // 关闭Client实例
            try
            {
                CloseClient();
            }
            catch
            {
                // ignore
            }

            return UniTask.FromResult(true);
        }

		#endregion

		#region 发送

		/// <summary>
		/// 是否使用发送队列。不使用时，发到队列的数据也是立即发送
		/// </summary>
		public bool UseSendQueue { get; set; }

		/// <summary>
		/// 待发送的数据队列
		/// </summary>
		public Queue<IList<byte>> SendQueue = new();

        /// <summary>
        /// 待发送的消息队列
        /// </summary>
        public Queue<string> SendMsgQueue = new();

        /// <summary>
        /// 是否暂停发送队列
        /// </summary>
        public bool SuspendSendQueue { get; set; }

        /// <summary>
        /// 启动发送任务
        /// </summary>
        protected void StartSend(CancellationToken token = default)
        {
            DoTask_SendQueue(token).Forget();
            DoTask_SendMsgQueue(token).Forget();
        }

        /// <summary>
        /// 任务实现:发送
        /// </summary>
        protected virtual async UniTask DoTask_SendQueue(CancellationToken token = default)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    try
                    {
                        if (!Connected || SuspendSendQueue || SendQueue is { Count: 0 }) continue;
                        token.ThrowIfCancellationRequested();
                        if (!SendQueue.TryPeek(out var data)) continue;
                        if (await Send(data, token))
                        {
                            SendQueue.TryDequeue(out _);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError(ex);
                        Debug.LogException(ex);
                        // 只要不是操作取消,就一直运行
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 任务实现:发送消息
        /// </summary>
        protected virtual async UniTask DoTask_SendMsgQueue(CancellationToken token = default)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    try
                    {
                        if (!Connected || SuspendSendQueue || SendMsgQueue is { Count: 0 }) continue;
                        token.ThrowIfCancellationRequested();
                        if (!SendMsgQueue.TryPeek(out var msg)) continue;
                        if (await Send(msg, token))
                        {
                            SendMsgQueue.TryDequeue(out _);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError(ex);
                        Debug.LogException(ex);
                        // 只要不是操作取消,就一直运行
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 发送数据入队
        /// </summary>
        public void Enqueue(IList<byte> data)
        {
	        if (UseSendQueue)
	        {
		        SendQueue.Enqueue(data);
	        }
	        else
	        {
		        Send(data).GetAwaiter();
	        }
        }

        /// <summary>
        /// 发送消息入队
        /// </summary>
        public void Enqueue(string msg)
        {
	        if (UseSendQueue)
	        {
		        SendMsgQueue.Enqueue(msg);
	        }
	        else
			{
				Send(msg).GetAwaiter();
			}
        }

        /// <summary>
        /// 发送数据到服务端
        /// </summary>
        /// <returns>是否发送成功</returns>
        public async UniTask<bool> Send(IList<byte> data, CancellationToken token = default)
        {
            OnBeforeSend(data);
            var rtn = await DoTask_Send(data, token);
            OnAfterSend(data, rtn);
            // 发送失败,说明连接有问题了,直接关闭
            if (!rtn)
            {
                CloseAsync("发送数据失败,关闭连接").Forget();
            }

            return rtn;
        }

        /// <summary>
        /// 发送数据到服务端(实现)
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected abstract UniTask<bool> DoTask_Send(IList<byte> data, CancellationToken token = default);

        /// <summary>
        /// 发送消息到服务端
        /// </summary>
        /// <returns>是否发送成功</returns>
        public async UniTask<bool> Send(string msg, CancellationToken token = default)
        {
            OnBeforeSendMsg(msg);
            var rtn = await DoTask_Send(msg, token);
            OnAfterSendMsg(msg, rtn);
            // 发送失败,说明连接有问题了,直接关闭
            if (!rtn)
            {
                CloseAsync("发送消息失败,关闭连接").Forget();
            }

            return rtn;
        }

        /// <summary>
        /// 发送消息到服务端(实现)
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected abstract UniTask<bool> DoTask_Send(string msg, CancellationToken token = default);

        /// <summary>
        /// 发送前
        /// </summary>
        public event Action<PersistentClient, IList<byte>> BeforeSend;

        protected void FireBeforeSend(IList<byte> e)
        {
            BeforeSend?.Invoke(this, e);
        }

        protected virtual void OnBeforeSend(IList<byte> e)
        {
            FireBeforeSend(e);
        }

        /// <summary>
        /// 发送后
        /// </summary>
        public event Action<PersistentClient, IList<byte>, bool> AfterSend;

        protected void FireAfterSend(IList<byte> data, bool success)
        {
            AfterSend?.Invoke(this, data, success);
        }

        protected virtual void OnAfterSend(IList<byte> data, bool success)
        {
            FireAfterSend(data, success);
        }

        /// <summary>
        /// 发送前(文本)
        /// </summary>
        public event Action<PersistentClient, string> BeforeSendMsg;

        protected void FireBeforeSendMsg(string e)
        {
            BeforeSendMsg?.Invoke(this, e);
        }

        protected virtual void OnBeforeSendMsg(string e)
        {
            FireBeforeSendMsg(e);
        }

        /// <summary>
        /// 发送后(文本)
        /// </summary>
        public event Action<PersistentClient, string, bool> AfterSendMsg;

        protected void FireAfterSendMsg(string msg, bool success)
        {
            AfterSendMsg?.Invoke(this, msg, success);
        }

        protected virtual void OnAfterSendMsg(string msg, bool success)
        {
            FireAfterSendMsg(msg, success);
        }

        #endregion

        #region 接收

        /// <summary>
        /// 启动接收任务
        /// </summary>
        protected void StartReceive(CancellationToken token = default)
        {
            DoTask_Receive(token).Forget();
        }

        /// <summary>
        /// 任务实现:接收
        /// </summary>
        protected abstract UniTaskVoid DoTask_Receive(CancellationToken token = default);

        /// <summary>
        /// 接收到服务端发送的消息后
        /// </summary>
        public event Action<PersistentClient, string> AfterReceiveMsg;

        protected void FireAfterReceiveMsg(string e)
        {
            AfterReceiveMsg?.Invoke(this, e);
        }

        protected virtual void OnAfterReceiveMsg(string e)
        {
            LastActiveTime = Time.unscaledTime;
            FireAfterReceiveMsg(e);
        }

        /// <summary>
        /// 接收到服务端发送的数据后
        /// </summary>
        public event Action<PersistentClient, IList<byte>> AfterReceiveData;

        protected void FireAfterReceiveData(IList<byte> e)
        {
            AfterReceiveData?.Invoke(this, e);
        }

        protected virtual void OnAfterReceiveData(IList<byte> e)
        {
            LastActiveTime = Time.unscaledTime;
            FireAfterReceiveData(e);
        }

        #endregion

        #region 心跳

        /// <summary>
        /// 心跳检测的间隔时长(秒)
        /// </summary>
        public float Interval_HeartBeat { get; set; } = 5;

        /// <summary>
        /// 最大空闲时长(秒)，超过则视为断开
        /// </summary>
        public float Timeout_Idle { get; set; } = 15;

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public float LastActiveTime { get; protected set; } = Time.unscaledTime;

        /// <summary>
        /// 连接是否应该关闭(已空闲过久)
        /// </summary>
        public bool ShouldClose => Time.unscaledTime - LastActiveTime > Timeout_Idle;

        /// <summary>
        /// 提供心跳消息(返回不为空就发)
        /// </summary>
        public Func<string> Provider_HeartBeatMsg { get; set; }

        /// <summary>
        /// 提供心跳数据(返回不为空就发)
        /// </summary>
        public Func<IList<byte>> Provider_HeartBeatData { get; set; }

        /// <summary>
        /// 是否开启断线重连
        /// </summary>
        public bool ReconnectWhenInterrupted { get; set; } = true;

        /// <summary>
        /// 断线重连时的最大重连次数(&lt;0:无限重连, 0:不重连, &gt;0:最大重连次数)
        /// </summary>
        public int MaxTryTimesWhenInterrupted { get; set; } = -1;

        /// <summary>
        /// 任务的取消令牌:断线重连
        /// </summary>
        public CancellationTokenSource CTS_ReconnectWhenInterrupted { get; set; } = new();

        /// <summary>
        /// 启动心跳任务
        /// </summary>
        protected void StartHeartBeat(CancellationToken token = default)
        {
            DoTask_HeartBeat(token).Forget();
        }

        /// <summary>
        /// 任务实现:心跳检测
        /// </summary>
        public async UniTaskVoid DoTask_HeartBeat(CancellationToken token = default)
        {
            for (;; await UniTask.Delay(200, true, cancellationToken: token))
            {
                try
                {
                    if (token.IsCancellationRequested) return;

                    //Debug.Log("心跳检测进行中...");

                    // 已断线 或 心跳超时了
                    if (!Connected || ShouldClose)
                    {
                        Debug.LogError($"检测到断线!!!");

                        OnAfterHeartBeatTimeout();

                        // 断线重连
                        if (ReconnectWhenInterrupted && MaxTryTimesWhenInterrupted != 0)
                        {
                            DoReconnectWhenInterrupted().Forget();
                        }

                        return;
                    }

                    // 没闲多久，不发
                    if (LastActiveTime + Interval_HeartBeat > Time.unscaledTime) continue;

                    // 空闲够久了,发心跳包
                    var sent = false;
                    {
                        var msg = Provider_HeartBeatMsg?.Invoke();
                        if (!string.IsNullOrWhiteSpace(msg))
                        {
                            await Send(msg, token);
                            sent = true;
                            //Debug.Log("已发送心跳消息");
                        }
                    }
                    {
                        var data = Provider_HeartBeatData?.Invoke();
                        if (data?.Count > 0)
                        {
                            await Send(data, token);
                            sent = true;
                            //Debug.Log("已发送心跳包");
                        }
                    }

                    // 如果没有发心跳包,说明不需保活,再明确关闭一下
                    if (!sent)
                    {
                        CloseAsync("心跳检测超时,明确关闭一下。").Forget();
                    }
                }
                catch (OperationCanceledException)
                {
                    return;
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex);
                    Debug.LogException(ex);
                    // 只要不是操作取消,就一直运行
                }
            }
        }

        /// <summary>
        /// 心跳超时后
        /// </summary>
        public event Action AfterHeartBeatTimeout;

        protected void FireAfterHeartBeatTimeout()
        {
            AfterHeartBeatTimeout?.Invoke();
        }

        /// <summary>
        /// 心跳超时后的处理
        /// </summary>
        protected virtual void OnAfterHeartBeatTimeout()
        {
            FireAfterHeartBeatTimeout();
        }

        /// <summary>
        /// 异步开始断线重连(等到下一帧开始)
        /// </summary>
        protected async UniTaskVoid DoReconnectWhenInterrupted()
        {
            await UniTask.NextFrame();
            Debug.Log($"开始重连,最大重连次数[{MaxTryTimesWhenInterrupted}]");
            // 断线重连
            CTS_ReconnectWhenInterrupted = new();
            TryConnect(MaxTryTimesWhenInterrupted, CTS_ReconnectWhenInterrupted.Token).GetAwaiter();
        }

        #endregion
    }
}