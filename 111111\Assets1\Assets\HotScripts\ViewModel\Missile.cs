﻿using System.Collections.Generic;

using Apq;
using Apq.Extension;

using UnityEngine;

namespace ViewModel
{
    /// <summary>
    /// 导弹发射器(同时也代表了一条轨迹)
    /// </summary>
    public class Missile
    {
        /// <summary>
        /// 代表导弹发射器
        /// </summary>
        public GameObject MissileEjecter { get; set; }

        /// <summary>
        /// 锁定的敌人
        /// </summary>
        public Enemy Enemy { get; set; }
        /// <summary>
        /// 落点的位置(取锁定敌人的当前或死亡时的世界坐标系位置)
        /// </summary>
        public Vector3 EndPosition { get; set; }
        /// <summary>
        /// 敌人半径
        /// </summary>
        public float EnemyRadius { get; set; }
        /// <summary>
        /// 导弹配置行
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Style", "IDE1006:命名样式", Justification = "<挂起>")]
        public CatSkill.Item csvRow_CatSkill { get; set; }
        /// <summary>
        /// 导弹飞行速度
        /// </summary>
        public float Speed { get; set; }
        /// <summary>
        /// 已发出的导弹数量
        /// </summary>
        public int EjectNum { get; set; }

        /// <summary>
        /// 导弹的延迟发射时长(毫秒)
        /// </summary>
        public int EjectDelay { get; set; } = RandomNum.RandomInt(100, 150);

        // 以下属性值用于计算时均应在 导弹发射器 的坐标系中
        /// <summary>
        /// 发射角度范围[AngleBackMin, AngleBackMax]
        /// </summary>
        public float AngleBackMin { get; set; } = 95f;
        /// <summary>
        /// 发射角度范围[AngleBackMin, AngleBackMax]
        /// </summary>
        public float AngleBackMax { get; set; } = 175f;
        /// <summary>
        /// 后方远点与发射器的距离范围[DistanceBackFarMin, DistanceBackFarMax]
        /// </summary>
        public float DistanceBackFarMin { get; set; } = 1f;
        /// <summary>
        /// 后方远点与发射器的距离范围[DistanceBackFarMin, DistanceBackFarMax]
        /// </summary>
        public float DistanceBackFarMax { get; set; } = 2f;
        protected Vector3? m_PositionBackFar;
        /// <summary>
        /// 后方远点(有值之后不改变)
        /// </summary>
        public Vector3 PositionBackFar
        {
            get
            {
                if (!m_PositionBackFar.HasValue)
                {
                    var angleBack = RandomNum.RandomFloat(AngleBackMin, AngleBackMax);
                    var distanceBackFar = RandomNum.RandomFloat(DistanceBackFarMin, DistanceBackFarMax);
                    m_PositionBackFar = new Vector3(distanceBackFar, 0).RotateAround(Vector3.zero, Vector3.forward, angleBack);
                }
                return m_PositionBackFar.Value;
            }
        }
        /// <summary>
        /// 中间高点的Y坐标范围[DistanceYMin, DistanceYMax]
        /// </summary>
        public float PositionMiddleYMin { get; set; } = 2f;
        /// <summary>
        /// 中间高点的Y坐标范围[DistanceYMin, DistanceYMax]
        /// </summary>
        public float PositionMiddleYMax { get; set; } = 4f;
        /// <summary>
        /// 中间高点的x坐标
        /// </summary>
        public float PositionMiddleX { get; set; } = 0f;
        protected Vector3? m_PositionMiddle;
        /// <summary>
        /// 中间高点(有值之后不改变)
        /// </summary>
        public Vector3 PositionMiddle
        {
            get
            {
                if (!m_PositionMiddle.HasValue)
                {
                    var positionMY = RandomNum.RandomFloat(PositionMiddleYMin, PositionMiddleYMax);
                    m_PositionMiddle = new Vector3(PositionMiddleX, positionMY);
                }
                return m_PositionMiddle.Value;
            }
        }
        /// <summary>
        /// 比例: 前方收紧点到落点的直线距离 / 发射器到敌人的直线距离
        /// </summary>
        public float PctFrontX { get; set; } = 0.3f;
        /// <summary>
        /// 前方收紧点y坐标最小值
        /// </summary>
        public float FrontYMin { get; set; } = 0.2f;
        /// <summary>
        /// 前方收紧点y坐标最大值
        /// </summary>
        public float FrontYMax { get; set; } = 0.4f;
        protected float? m_FrontY;
        /// <summary>
        /// 前方收紧点y坐标(有值之后不改变)
        /// </summary>
        public float FrontY
        {
            get
            {
                if (!m_FrontY.HasValue)
                {
                    m_FrontY = RandomNum.RandomFloat(FrontYMin, FrontYMax);
                }
                return m_FrontY.Value;
            }
        }

        /// <summary>
        /// 计算导弹的关键点(在发射器坐标系中,落点的y坐标也必是0)
        /// </summary>
        /// <returns></returns>
        public List<Vector3> CalcKeyPositions()
        {
            var dir = EndPosition - MissileEjecter.transform.position;
            var distance = dir.magnitude;
            var rtn = new List<Vector3>
            {
                // 1.发射点
                Vector3.zero,
                // 2.后方远点
                PositionBackFar,
                // 3.中间高点
                PositionMiddle,
                // 4.前方收紧点
                new Vector3(distance * (1 - PctFrontX), FrontY),
                // 5.y=0点(落点)
                new Vector3(distance, 0)
            };
            return rtn;
        }
    }
}
