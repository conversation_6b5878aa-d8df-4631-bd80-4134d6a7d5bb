using System.Collections;
using System.Collections.Generic;
using Spine;
using Spine.Unity;
using UnityEngine;
using DG.Tweening;

public class BulletTower : Enemy
{
    enum BulletTowerType
    {
        Shielded,
        unshielded
    }

    [SerializeField] Sprite creepyBullet;
    [SerializeField] Collider2D shieldCollider, towerCollider;
    [SerializeField] Generator generator;
    [SerializeField] SkeletonAnimation flameSkeletonAnimation;

    Collider2D currentCollider;

    PlayerPing missionPing;
    BulletTowerType currentState;
    Bounds bounds;

    float Pattern1Angle = 0;
    bool onFire;

    private IEnumerator NextFrameGetBounds()
    {
        yield return null;
        bounds = currentCollider.bounds;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        //Globals.zoomValueWhileGame = Globals.CocosToUnity(500);

        tweenId = "BulletTower" + gameObject.GetInstanceID().ToString();
        schedulerId = "BTSchedules" + gameObject.GetInstanceID().ToString();

        Globals.numberOfEnemies++;
        currentState = BulletTowerType.Shielded;
        allowRelocate = false;
        isBoss = false;
        InitStats();

        healthBar.gameObject.SetActive(false);
        enemySprite.state.SetAnimation(0, "idleBulletShield", true);
        transform.position =
            new Vector3(Globals.CocosToUnity(3000) + Random.value * Globals.CocosToUnity(1000), Globals.LOWERBOUNDARY);

        //healthBar.setScaleRatio(3);
        //healthBar.transform.position = new Vector3(-50, -10);
        currentCollider = shieldCollider;
        StartCoroutine(NextFrameGetBounds());


        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, false);
        missionPing.SetPing(false);
        _jsonScale = 2.5f;
        explosionType = Explosions.ExplosionType.ExplosionTypeBuildingMission;

        UpdateBounds();
        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(UpdateBounds).SetLoops(-1);
        generator.Init();
        generator.transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(600));
        generator.transform.SetWorldPositionY(Globals.LOWERBOUNDARY);


        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeStages)
        {
            //generator.transform.SetWorldPositionX(Globals.CocosToUnity(800000));
            //DOTween.Sequence().SetId(generator.tweenId).AppendInterval(1.5f).AppendCallback(() =>
            //{
            //    generator.transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(600));
            //});
        }
        scheduleUpdate = true;


        additionalOnDestroy += () =>
        {
            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeBulletTower
            && Globals.gameType == GameType.Training)
            {
                GameManager.instance.missionManager.MissionComplete();
                //MissionManager::getInstance().missionComplete();
            }
        };

        enemySprite.state.Event += ShootEvent;
    }

    private void OnDisable()
    {
        enemySprite.state.Event -= ShootEvent;
    }

    void ShootEvent(TrackEntry entry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(0.08f).SetLoops(30);
        }
    }

    void Shoot()
    {
        {
            for (int i = 0; i < 2; i++)
            {
                Bullet bullet = null;

                bool didFindBullet = false;
                foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        bullet.isInUse = true;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                bullet.setDamage(stats.bulletDamage);
                bullet.setRadiusEffectSquared(1);
                bullet.SetSpriteFrame(creepyBullet);
                bullet.duration = stats.bulletSpeed;
                bullet.gameObject.SetActive(true);
                bullet.transform.SetRotation(90);


                bullet.transform.position = new Vector3(transform.position.x, transform.position.y + Globals.CocosToUnity(900));
                bullet.transform.SetRotation(Pattern1Angle + (i * 180));
                bullet.PlayBulletAnim(stats.bulletSpeed,
                    new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(bullet.transform.eulerAngles.z * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Cos(bullet.transform.eulerAngles.z * Mathf.Deg2Rad)));

                GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            }
        }

        AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spiderCatBullet2, 1, new Vector2(transform.position.x, transform.position.y + 8.73f));
        //TODO Distance Sound
        //Globals.PlaySound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3",new Vector2(transform.position.x, transform.position.y + 8.73f));


        Pattern1Angle += 11;

    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = 10.0f;//bhut taiz
        stats.turnSpeed = 0.5f + Random.value;
        stats.health = 10000;

        stats.bulletDamage = 100;
        stats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletSpeed = 20;
        stats.maxHealth.Value = stats.health;
        stats.coinAwarded = 25;
        stats.xp = stats.maxHealth.Value / 2;

        baseStats.health = 10000;
        baseStats.bulletDamage = 100;
        baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        baseStats.bulletSpeed = 20;
        baseStats.maxHealth.Value = baseStats.health;
        baseStats.coinAwarded = 25;
        baseStats.xp = baseStats.maxHealth.Value / 2;

    }

    public override void Destroy()
    {
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Shared::playSound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        base.Destroy();
    }


    void UpdateBounds()
    {
        bounds = currentCollider.bounds;
    }


    public override bool TakeHit(double damage)
    {
        if (currentState == BulletTowerType.unshielded)
        {
            stats.health = stats.health - damage * 3; // TODO REMOVE '* 3'
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            healthBar.gameObject.SetActive(true);
            DOTween.Kill(healthBar.TweenID);
            DOTween.Sequence().SetId(healthBar.TweenID).AppendInterval(3)
                .AppendCallback(() => healthBar.gameObject.SetActive(false));

            if (stats.health < 0)
            {
                if (isBoss)
                {
                    Globals.isBossMode = false;
                    scheduleUpdate = false;
                    Globals.bossPosition = Vector2.zero;
                }

                return true;
            }

            return false;
        }
        else
        {
            return false;
        }
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (bounds == null) return false;
        if (currentState == BulletTowerType.Shielded)
        {
            return bounds.Contains(P1);
        }
        else
        {
            if (stats.health / stats.maxHealth.Value < 0.4f && onFire == false)
            {
                flameSkeletonAnimation.gameObject.SetActive(true);
                flameSkeletonAnimation.transform.localPosition = new Vector3(0, -Globals.CocosToUnity(50));
                flameSkeletonAnimation.transform.SetScale(3);
                flameSkeletonAnimation.state.SetAnimation(0, "idle", true);
                onFire = true;

                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAboveWater,
                    flameSkeletonAnimation.transform.position, false, 1, 1, 1);
            }
            return bounds.Contains(P1);
        }
    }

    void Update()
    {
        if (!scheduleUpdate)
            return;

        if (currentState == BulletTowerType.Shielded)
        {
            if (generator._isDead == true)
            {
                enemySprite.state.SetAnimation(0, "idleBulletShoot", true);
                currentState = BulletTowerType.unshielded;
                currentCollider = towerCollider;
                UpdateBounds();

                missionPing.SetPing(true);
                missionPing.bgImage.color = new Color(1, 127 / 255f, 0);
                missionPing.image.color = new Color(1, 127 / 255f, 0);
            }
        }
    }

}
