using Spine;

using UnityEngine;

public class WXR_Rocketeer : Sidekick
{

    public void Start()
    {
        Init();

    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        //sideKickSprite->setMix("select", "idle", 0.4f);
        SetStartingPosition();
        //transform.localScale = new Vector3(scale, scale, 1);
    }

    public override void StartSidekick()
    {
        InvokeRepeating("Shoot", 0.18f, 0.18f);
    }

    void ShootEvent(TrackEntry trackEntry, Spine.Event e)
    {
        if (e.Data.Name == "shoot")
            Shoot();
    }

    private void Update()
    {
        SidekicksUpdate();
    }



    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;
        if (!Globals.beginSidekickShoot)
            return;
        //Globals.PlaySound("res/Sounds/SFX/rocketeerShoot.mp3", false, 0.3f);

        PlayerMissile missile = null;
        bool didFindBullet = false;
        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                didFindBullet = true;
                break;
            }
        }

        if (!didFindBullet)
        {
            return;
        }
        missile.SurvivalTime = 2f;

        missile.Init();
        if (spriteFrame) missile.SetSpriteFrame(spriteFrame);
        missile.isInUse = true;
        //var (damage, _) = Helper.CalcDamage(player.FightProp, false);
        //missile.damage = damage;
        missile.transform.position = (Vector2)transform.position;
        GameSharedData.Instance.playerMissilesInUse.Add(missile);

        //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.rocketeerShoot, 0.3f);

        //if (sideKickSprite->getScaleX() < 0)
        //{
        //    missile->missileSprite->setRotation(0);
        //}
        //else
        //{
        //    missile->missileSprite->setRotation(180);
        //}
    }
}
