using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using TMPro;
using DG.Tweening;
using UnityEngine.UI;

public class TutorialDialoguePopup : MonoBehaviour
{
    [SerializeField] private GameObject dialoguePopUp;
    [SerializeField] private Image[] popUpImage;
    [SerializeField] private TextMesh<PERSON>roUGUI textLabel;
    [SerializeField] private SkeletonGraphic character;
    private string tweenId;
    private string schedulerId;
    private void Awake()
    {
        tweenId = "TDP" + GetInstanceID();
        schedulerId = "TDPS" + GetInstanceID();
    }

    public void ShowPopup(string s)
    {
        textLabel.text = s;
        character.gameObject.SetActive(true);
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        DOTween.Sequence().SetId(tweenId).SetUpdate(true).AppendInterval(0.3f).AppendCallback(() =>
        {
            dialoguePopUp.transform.DOScale(Vector3.one, 1).SetEase(Ease.OutElastic).SetUpdate(true);
            popUpImage[0].DOFade(1, 0.5f).SetUpdate(true);
            popUpImage[1].DOFade(1, 0.5f).SetUpdate(true);
            textLabel.DOFade(1, 0.5f).SetUpdate(true);
        }).Play();
    }

    public void HidePopup()
    {
        if (!Globals.popupDestroyedOnSkip)
        {
            character.gameObject.SetActive(false);
            DOTween.Kill(tweenId);
            DOTween.Kill(schedulerId);
            DOTween.Sequence().SetId(tweenId).SetUpdate(true).AppendCallback(() =>
            {
                dialoguePopUp.transform.DOScale(Vector3.zero, 0.3f).SetEase(Ease.OutElastic).SetUpdate(true);
                popUpImage[0].DOFade(0, 0.3f).SetUpdate(true);
                popUpImage[1].DOFade(0, 0.3f).SetUpdate(true);
                textLabel.DOFade(0, 0.3f).SetUpdate(true);
            }).Play();
        }

    }

    public void CallAttention()
    {
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        DOTween.Sequence().SetId(tweenId).Append(dialoguePopUp.transform.DOScale(Vector3.one * 0.8f, 0.3f).SetUpdate(true)).Append(dialoguePopUp.transform.DOScale(Vector3.one, 0.3f).SetUpdate(true)).SetUpdate(true).SetLoops(-1).Play();
    }
}
