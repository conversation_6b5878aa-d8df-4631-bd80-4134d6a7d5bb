using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine;
using Spine.Unity;
using TMPro;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
public class StartMenu : BaseMenu
{
    [SerializeField] private Image fadeImage;
    [SerializeField] private Color fadeColor;
    [SerializeField] private RectTransform buttonsContainer;
    [SerializeField] private SkeletonGraphic menuAnimation;
    [SerializeField] private OptionsMenu optionsMenu;
    [SerializeField] private MainMenuButton gameCenterButton;
    [SerializeField] private MainMenuButton startButton;
    [SerializeField] private MainMenuButton optionsButton;
    [SerializeField] private MainMenuButton loadGameButton;
    [SerializeField] private MainMenuButton quitButton;
    [SerializeField] private PopUp popUp;
    float mainOffset = 80;
    int fontSize = 50;
    float spacing = 50;
   

    //GameCenterAccessPointUI* _gameCenterAccessPointObj = nullptr;



    public void InitStartMenu()
    {

#if !UNITY_STANDALONE
        mainOffset = 120;
        fontSize = 70;
        spacing = 70;

#endif
        mainOffset = 80;
        fontSize = 50;
        spacing = 50;
        selectedPosition = 1;

        //setOpacity(0.2f);
        exitIndex = 2;
        clickSound = Constants.AudioClips.SOUND_BUTTON_TAP;
        //transform.localScale = Vector3.one * 0.85f;
        float animationTime = 1.5f;
        //TODO GameCenter
        //if (ThirdPartyInterface::isGameCenterAvailable())
        //{
        //    _gameCenterAccessPointObj = GameCenterAccessPointUI::create();
        //    this.addChild(_gameCenterAccessPointObj);
        //    _gameCenterAccessPointObj.setPosition(Vec2(_winSize.width / 2 - 700, _winSize.height - 30));
        //    Shared::rescale(_gameCenterAccessPointObj, 1);
        //}

        gameCenterButton.InitVariables(" ", () =>
        {

                //_gameCenterAccessPointObj.GameCenterAccessPointCall();

            });
        //buttonsList.Add(gameCenterButton);
        //macOptionsMenu.SetOpacity(0.1);
        gameCenterButton.SetSelectedFunc(() =>
        {
                //if (_gameCenterAccessPointObj != nullptr)
                //  _gameCenterAccessPointObj.SelectedCall();

            });
        gameCenterButton.SetUnSelectedFunc(() =>
        {
                //if (_gameCenterAccessPointObj != nullptr)
                //_gameCenterAccessPointObj.UnSelectedCall();

            });



        startButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["start"] as string, () =>
        {

                //this.setVisible(false);
                //this.setEnable(false);
                //if (_gameCenterAccessPointObj != nullptr)
                //{
                //    _gameCenterAccessPointObj.setVisible(false);
                //}
                gameObject.SetActive(false);
            AudioManager.instance.StopMusic();
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.menuToMap);

            //Globals.PlaySound("res/Sounds/SFX/menuToMap.mp3");

            menuAnimation.AnimationState.SetAnimation(0, "MenuToMap3", false);

            fadeImage.color = fadeColor;
            DOTween.Sequence().AppendInterval(0.3f).Append(
                fadeImage.DOFade(1, 0.1f)).AppendCallback(() =>
                    {
                        SceneManager.LoadScene("MainMenu");
                        //fadeImage.gameObject.SetActive(false);
                        //fadeImage.color = Color.black;
                        //worldMenu.SetActive(true);
                        //worldMap.SetActive(true);
                        //mainMenu.SetActive(false);
                    })
            .Play();


#if UNITY_TVOS
                    //TODO
                //if (!ThirdPartyInterface::isInternetAvailable())
                //{
                //    UICustom::Popup* p = UICustom::Popup::createAsMessage(GameData.instance.GetMenuData(Globals.MAIN_MENU)["device"] as string + "OFFLINE", GameData.instance.GetMenuData(Globals.SAVEGAME)["deviceOfflinePopupText1"] as string + "\n" + GameData.instance.GetMenuData(Globals.SAVEGAME)["deviceOfflinePopupText2"] as string + "\n" + GameData.instance.GetMenuData(Globals.SAVEGAME)["deviceOfflinePopupText3"] as string);
                //    Director::getInstance().getRunningScene().addChild(p);
                //    p.setTag(986723);

                //}
#endif
            }, Configuration.GAME_FONT, false, fontSize);


        buttonsList.Add(startButton);

#if UNITY_ANDROID||UNITY_IOS
        startButton.rectTransform.DOAnchorPosY(50f, animationTime).SetEase(Ease.OutExpo);
#else
        startButton.rectTransform.DOAnchorPosY(50f, animationTime).SetEase(Ease.OutExpo);
#endif



#if UNITY_STANDALONE
        optionsButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["options"] as string, () =>
        {

                //            Director::getInstance().replaceScene(TransitionProgressVertical::create(0.1,  OptionsScene::createScene()));
                for (int i = 0; i < buttonsList.Count; i++)
            {
                DOTween.Sequence().AppendInterval(0.24f - (i * 0.08f)).Append(buttonsList[i].rectTransform.DOAnchorPosY(buttonsList[i].rectTransform.anchoredPosition.y - 500, 0.15f).SetEase(Ease.InExpo)).Play();
            }
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.whoosh1);

            //Globals.PlaySound("res/Sounds/SFX/whoosh1.mp3");

            DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
            {
                menuAnimation.AnimationState.SetAnimation(0, "MenuToOptions", false);
            }).AppendInterval(0.5f).AppendCallback(() =>
            {
                SetEnable(false);
                //OptionsMenuDesktop *op = OptionsMenuDesktop::create(menuAnimation);
                //this.addChild(op);
                optionsMenu.InitOptionsMenu();

                }).Play();



        }, Configuration.GAME_FONT, false, fontSize);
        optionsButton.rectTransform.DOAnchorPosY(150f, animationTime).SetEase(Ease.OutExpo);
        
        buttonsList.Add(optionsButton);
#else
        optionsButton.gameObject.SetActive(false);
#endif


        loadGameButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["loadGame"] as string, () =>
        {
                //TODO
                //UISaveGame* sg = UISaveGame::create();
                //Director::getInstance().getRunningScene().addChild(sg, INT_MAX);


            }, Configuration.GAME_FONT, false, fontSize);

        buttonsList.Add(loadGameButton);
#if UNITY_ANDROID||UNITY_IOS
        loadGameButton.rectTransform.DOAnchorPosY(0f, animationTime).SetEase(Ease.OutExpo);
#else
        loadGameButton.rectTransform.DOAnchorPosY(100f, animationTime).SetEase(Ease.OutExpo);
#endif

#if UNITY_STANDALONE


        {
            quitButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["quit"] as string, () =>
            {

                popUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.MAIN_MENU)["exitPopupHeading"] as string, GameData.instance.GetMenuData(Globals.MAIN_MENU)["exitPopupText"] as string, () =>
                {
                    //                Director::getInstance().getScheduler().unscheduleAll();
                    //                Director::getInstance().getScheduler().removeAllFunctionsToBePerformedInCocosThread();
                    //Director::getInstance().pause();
                    //Director::getInstance().end();

                    DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
                {
                    //Director::getInstance().end();

                    Application.Quit();

                }).Play();

                });


            }, Configuration.GAME_FONT, false, fontSize);
            buttonsList.Add(quitButton);
            quitButton.rectTransform.DOAnchorPosY(0f, animationTime).SetEase(Ease.OutExpo);
        }
        exitIndex = buttonsList.Count - 1;

#else
        quitButton.gameObject.SetActive(false);

#endif

        selectedPosition = 0;
        UpdateSelected();
#if UNITY_STANDALONE
        Observer.RegisterCustomEvent(gameObject, "backFromOptions", () =>
        {

            for (int i = 0; i < buttonsList.Count; i++)
            {
                DOTween.Sequence().AppendInterval(0.5f + i * 0.05f).Append(buttonsList[i].rectTransform.DOAnchorPosY(buttonsList[i].rectTransform.anchoredPosition.y+500, animationTime).SetEase(Ease.OutExpo)).Play();
            }
            SetEnable(true);
        });
#endif
        //buttonsContainer.DOAnchorPosY(0f, animationTime);
        Init();
    }
}
