﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Apq.ApqMath
{
    /// <summary>
    /// 加权随机算法
    /// </summary>
    public class RandomWithWeighting<T>
    {
        /// <summary>
        /// 按对应位置生成加权随机范围
        /// </summary>
        public static IList<WeightingItem<T>> GenList(
            IList<T> values,
            IList<int> weightings,
            int defaultWeighting = 0)
        {
            List<WeightingItem<T>> rtn = new();
            for (int i = 0; i < values.Count; i++)
            {
                WeightingItem<T> v = new() { Value = values[i], Weighting = defaultWeighting };
                if (weightings.Count > i)
                {
                    v.Weighting = weightings[i];
                }
                if (v.Weighting > 0)
                {
                    rtn.Add(v);
                }
            }
            return rtn;
        }

        /// <summary>
        /// 按权重随机取一条
        /// </summary>
        /// <param name="predicate">随机的范围。</param>
        public static WeightingItem<T> GetOne(
            IEnumerable<WeightingItem<T>> range,
            Func<WeightingItem<T>, bool> predicate = null)
        {
            if (predicate != null)
            {
                range = range.Where(predicate);
            }

            var totalWeighting = range.Sum(x => x.Weighting);
            var rnd = UnityEngine.Random.Range(0, totalWeighting);
            //var rnd = RandomNum.RandomInt(0, totalWeighting);

            var visitor = range.GetEnumerator();
            var iWeighting = 0;
            while (visitor.MoveNext())
            {
                iWeighting += visitor.Current.Weighting;
                if (iWeighting > rnd)
                {
                    visitor.Current.GotTimes++;
                    return visitor.Current;
                }
            }
            return default;
        }

        /// <summary>
        /// 按权重随机取多条
        /// </summary>
        /// <param name="count">随机结果需要多少条</param>
        /// <param name="predicate">每次随机的范围。可通过该参数使产生的随机结果中没有重复项</param>
        /// <remarks>如果范围内已无法随机出结果,就会停止。因此不一定能返回指定的条数</remarks>
        public static IList<WeightingItem<T>> GetMulti(
            IEnumerable<WeightingItem<T>> range,
            int count,
            Func<WeightingItem<T>, bool> predicate = null)
        {
            List<WeightingItem<T>> rtn = new();
            for (int i = 0; i < count && rtn.Count == i; i++)
            {
                var one = GetOne(range, predicate);
                if (one != null)
                {
                    rtn.Add(one);
                }
            }

            return rtn;
        }
    }
}
