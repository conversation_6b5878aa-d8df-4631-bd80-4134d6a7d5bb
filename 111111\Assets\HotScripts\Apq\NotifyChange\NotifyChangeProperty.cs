﻿using System;

namespace Apq.NotifyChange
{
    /// <summary>
    /// 可暂停的更改通知
    /// </summary>
    public abstract class NotifyChangeProperty
    {
        public NotifyChangeProperty(
        string propertyName,
        NotifyPropertyChange instance = null,
        NotifyPropertyChangeMonoBehaviour component = null)
        {
            PropertyName = propertyName;
            Instance = instance;
            Component = component;
        }

        #region 属性名称
        /// <summary>
        /// 属性所属实例
        /// </summary>
        public NotifyPropertyChange Instance { get; }
        /// <summary>
        /// 属性所属组件
        /// </summary>
        public NotifyPropertyChangeMonoBehaviour Component { get; }

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; }
        #endregion

        /// <summary>
        /// 是否已暂停更改前事件
        /// </summary>
        public bool ChangingSuspend { get; set; }
        /// <summary>
        /// 是否已暂停更改后事件
        /// </summary>
        public bool ChangedSuspend { get; set; }
    }

    /// <summary>
    /// 支持更改通知的属性(可暂停)
    /// </summary>
    public partial class NotifyChangeProperty<T> : NotifyChangeProperty
        where T : IEquatable<T>
    {
        public NotifyChangeProperty(
            string propertyName,
            NotifyPropertyChange instance = null,
            NotifyPropertyChangeMonoBehaviour component = null)
            : base(propertyName, instance, component)
        {

        }

        /// <summary>
        /// 更改前
        /// </summary>
        public event Func<string, T, T, bool> Changing;
        /// <summary>
        /// 更改后
        /// </summary>
        public event Action<string, T, T> Changed;

        #region Value
        protected T Value_m = default;
        /// <summary>
        /// 当前值
        /// </summary>
        public T Value
        {
            get
            {
                return Value_m;
            }
            set
            {
                var originalValue = Value_m;
                //bool equals = EqualityComparer<T>.Default.Equals(originalValue, value);
                bool equals = Utils.Util.IsEquals(originalValue, value);
                if (equals) return;

                // 不相等，执行更改
                if (!OnChanging(originalValue, value))
                {
                    Value_m = value;
                    OnChanged(originalValue, value);
                }
            }
        }
        /// <summary>
        /// 属性是否有值
        /// </summary>
        public bool HasValue => Value != null;
        #endregion

        #region On
        /// <summary>
        /// 更改前(派生类重写时可以直接调用Fire方法而不调用此方法)
        /// </summary>
        /// <returns>是否阻止更改</returns>
        protected virtual bool OnChanging(T originalValue, T newValue)
        {
            return FireChanging(originalValue, newValue);
        }
        /// <summary>
        /// 更改后(派生类重写时可以直接调用Fire方法而不调用此方法)
        /// </summary>
        protected virtual void OnChanged(T originalValue, T newValue)
        {
            FireChanged(originalValue, newValue);
        }
        #endregion

        #region Fire
        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        /// <returns>是否阻止更改</returns>
        public virtual bool FireChanging(T originalValue, T newValue)
        {
            bool cancel = false;
            if (ChangingSuspend) cancel = true;
            if (!cancel && Instance != null
                && NotifyPropertyChange.EventSuspendList_Contains(Instance.__EventSuspend_PropertyChanging, PropertyName))
            {
                cancel = true;
            }

            if (!cancel && Changing != null)
            {
                cancel = Changing.Invoke(PropertyName, originalValue, newValue);
            }
            if (!cancel && Instance != null)
            {
                cancel = Instance.FirePropertyChanging(PropertyName, originalValue, newValue);
            }
            if (!cancel && Component != null)
            {
                cancel = Component.FirePropertyChanging(PropertyName, originalValue, newValue);
            }
            return cancel;
        }
        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        public virtual void FireChanged(T originalValue, T newValue)
        {
            bool cancel = false;
            if (ChangedSuspend) cancel = true;
            if (!cancel && Instance != null
                && NotifyPropertyChange.EventSuspendList_Contains(Instance.__EventSuspend_PropertyChanged, PropertyName))
            {
                cancel = true;
            }

            if (!cancel && Changed != null)
            {
                Changed.Invoke(PropertyName, originalValue, newValue);
            }
            if (!cancel && Instance != null)
            {
                Instance.FirePropertyChanged(PropertyName, originalValue, newValue);
            }
            if (!cancel && Component != null)
            {
                Component.FirePropertyChanged(PropertyName, originalValue, newValue);
            }
        }
        #endregion
    }
}
