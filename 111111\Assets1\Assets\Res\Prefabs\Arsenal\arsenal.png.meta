fileFormatVersion: 2
guid: a1de3c56433ec498f8971ffc07fa46ba
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2043017983467968750
    second: arrow
  - first:
      213: -6270828003723659175
    second: backBullet-backBullet_0
  - first:
      213: -4530162714627483410
    second: backBullet-backBullet_1
  - first:
      213: -6533416653296894049
    second: backBullet-backBullet_2
  - first:
      213: -8506171300609620687
    second: backBullet-backBullet_3
  - first:
      213: -1033352019633192189
    second: backBullet-backBullet_4
  - first:
      213: -5606454401843334223
    second: backBullet-backBullet_5
  - first:
      213: -2172564189294941297
    second: backBullet-backBullet_6
  - first:
      213: 6877459146545688526
    second: backBullet-backBullet_7
  - first:
      213: -5043327471307307866
    second: backBullet-backBullet_8
  - first:
      213: 5775752823730166878
    second: birdBullet
  - first:
      213: -7606737191796897976
    second: blast
  - first:
      213: -6832688457854466055
    second: blueBlast_00000
  - first:
      213: -2514885938123355466
    second: blueBlast_00001
  - first:
      213: -6152372397722805377
    second: blueBlast_00002
  - first:
      213: -1380459318128098719
    second: blueBlast_00003
  - first:
      213: 8536625908055808687
    second: blueBlast_00004
  - first:
      213: -4523958017931882942
    second: blueBlast_00005
  - first:
      213: -6751803155554726590
    second: blueBlast_00006
  - first:
      213: -2394863761650087865
    second: bomb
  - first:
      213: 9080725562399585884
    second: boost0001
  - first:
      213: 1621330230224011072
    second: boost1vfx
  - first:
      213: 4909318569251658181
    second: boost0002
  - first:
      213: 1431462076126238466
    second: boost2vfx
  - first:
      213: 7888679534063001762
    second: boost0003
  - first:
      213: 7165000326359416051
    second: boost3vfx
  - first:
      213: -2185158730100831472
    second: boost0004
  - first:
      213: -9186561557120315190
    second: boost4vfx
  - first:
      213: -1039792837012191275
    second: boost0005
  - first:
      213: -5935293119231869750
    second: boost0006
  - first:
      213: 9021536855584771665
    second: boost0007
  - first:
      213: 4858985075089381772
    second: boost0008
  - first:
      213: -3180955747226026765
    second: boost0009
  - first:
      213: -7720549273852887707
    second: boost0010
  - first:
      213: -8492723142278499262
    second: boost0011
  - first:
      213: -4990617233000464713
    second: boost0012
  - first:
      213: -1044467907025595641
    second: buildingMine
  - first:
      213: 820357171280843453
    second: bullet
  - first:
      213: -5492326328662307968
    second: bulletEnemy
  - first:
      213: 6659537152580015793
    second: bulletEnemy2
  - first:
      213: -4166978870104779439
    second: bullethit_00000
  - first:
      213: -7317594148289285981
    second: bullethit_00001
  - first:
      213: 6573348927393324373
    second: bullethit_00002
  - first:
      213: -1061793716416178266
    second: bullethit_00003
  - first:
      213: -758060631967113232
    second: bullethit_00004
  - first:
      213: -3496227455086532949
    second: bulletHitA001
  - first:
      213: 504331350770280012
    second: bulletHitA002
  - first:
      213: -5556600137956252827
    second: bulletHitA003
  - first:
      213: -8335193475834789413
    second: bulletHitA004
  - first:
      213: -251211288538232752
    second: bulletHitA005
  - first:
      213: -5476883780937744343
    second: bulletHitB001
  - first:
      213: 7465631657191007269
    second: bulletHitB002
  - first:
      213: -7816795302711831785
    second: bulletHitB003
  - first:
      213: -3200270788225506786
    second: bulletHitB004
  - first:
      213: 7320540924202356298
    second: bulletOval
  - first:
      213: 1493436792003014023
    second: bulletShoot001
  - first:
      213: -1553468953432191107
    second: bulletShoot002
  - first:
      213: 427940558411606343
    second: bulletShoot003
  - first:
      213: 3417523087808819792
    second: bulletShoot004
  - first:
      213: 1427253756915425591
    second: bulletShoot005
  - first:
      213: -1044441893299726510
    second: creepyBullet
  - first:
      213: -4601184599733052067
    second: dashFront1
  - first:
      213: -822152551968487720
    second: dashFront2
  - first:
      213: -3419325423756013402
    second: enemyBullet
  - first:
      213: -2408651515654577241
    second: fatCatBullet
  - first:
      213: -6915290125270005605
    second: fireLoopUp1
  - first:
      213: -1133719495621742859
    second: fireLoopUp2
  - first:
      213: 761673182975844726
    second: fireLoopUp3
  - first:
      213: 4521121442649527314
    second: fireLoopUp4
  - first:
      213: -3828210586841031868
    second: fireLoopUp5
  - first:
      213: -7857238572401879128
    second: fireLoopUp6
  - first:
      213: -7916984612500069469
    second: fireLoopUp7
  - first:
      213: 8201116498062771117
    second: gunEffect1
  - first:
      213: -3421456558652915013
    second: gunEffect2
  - first:
      213: -9057116269548606069
    second: gunEffect3
  - first:
      213: 8969088938365532248
    second: gunEffect4
  - first:
      213: -1128892796134844796
    second: gunEffect5
  - first:
      213: 120131071560389271
    second: gunEffect6
  - first:
      213: -5447306410197136504
    second: lineParticle
  - first:
      213: 8276471322595730488
    second: lineParticle2
  - first:
      213: -694734316328894766
    second: mine
  - first:
      213: 7895150657509526253
    second: missile
  - first:
      213: 8672738046598779535
    second: musicNote1
  - first:
      213: -5077580727278861704
    second: musicNote2
  - first:
      213: 4086447403618135089
    second: musicNote3
  - first:
      213: -2593139731216370392
    second: oktoMine
  - first:
      213: -872426996321734805
    second: parts1
  - first:
      213: 8363996562039459157
    second: parts2
  - first:
      213: -8145496423555723364
    second: parts3
  - first:
      213: 1174680064125906951
    second: parts4
  - first:
      213: -2465476069727962379
    second: parts5
  - first:
      213: 2850253032870813646
    second: parts6
  - first:
      213: 3589697798265019642
    second: plasma01
  - first:
      213: 9089508280450411246
    second: plasma02
  - first:
      213: -8449537997764129709
    second: plasma03
  - first:
      213: -3492184301670267595
    second: plasma04
  - first:
      213: -1508766535731263620
    second: plasma05
  - first:
      213: 9103019498875847148
    second: plasma06
  - first:
      213: 6112874996653657079
    second: plasma07
  - first:
      213: 1532884163945180987
    second: plasmaEffect1
  - first:
      213: -238294037115883943
    second: plasmaEffect2
  - first:
      213: 4460442126884211789
    second: plasmaEffect3
  - first:
      213: 8342599505416889076
    second: plasmaEffect4
  - first:
      213: 5037224043404097741
    second: plasmaEffect5
  - first:
      213: -4245921386647904971
    second: plasmaEffect6
  - first:
      213: 6557577617161096940
    second: plasmaEffect7
  - first:
      213: 2939388540566015243
    second: plasmaEffect8
  - first:
      213: 4419726799460948409
    second: playerMissile
  - first:
      213: 2757812181304609028
    second: rocketSmoke1
  - first:
      213: -393580494151408610
    second: rocketSmoke2
  - first:
      213: 4627920743006832828
    second: rocketSmoke3
  - first:
      213: -1938536724283520104
    second: rocketSmoke4
  - first:
      213: 7606953552092242701
    second: rocketSmoke5
  - first:
      213: 2115556264472304489
    second: rocketSmoke6
  - first:
      213: -8418845626886843202
    second: rocketSmoke7
  - first:
      213: -5089590915246299501
    second: shipBullet
  - first:
      213: -5416749673430358429
    second: smokeParticle
  - first:
      213: 4249659329539980050
    second: smokeParticle3
  - first:
      213: 6741891542672802449
    second: smokeParticlePlayer
  - first:
      213: -774322330101989367
    second: smokeParticlePlayer2
  - first:
      213: 2740175673993663403
    second: smokeParticlePlayer3
  - first:
      213: 4445782692295652814
    second: strobe
  - first:
      213: 3930648500950312510
    second: sugarBomb
  - first:
      213: -3039799889462296866
    second: vaderBullet
  - first:
      213: 8712084068885489476
    second: vaderMissile
  - first:
      213: -5883303774238271965
    second: waterMissile
  - first:
      213: 4522597817097587796
    second: waterMissileActivated
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 1
    textureFormat: -1
    textureCompression: 3
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 1
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 1
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: arrow
      rect:
        serializedVersion: 2
        x: 616
        y: 837
        width: 14
        height: 76
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21728a9e7fdb5a3e0800000000000000
      internalID: -2043017983467968750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_0
      rect:
        serializedVersion: 2
        x: 677
        y: 479
        width: 60
        height: 49
      alignment: 9
      pivot: {x: 0.5416667, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 958ba76a72b89f8a0800000000000000
      internalID: -6270828003723659175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_1
      rect:
        serializedVersion: 2
        x: 897
        y: 418
        width: 49
        height: 46
      alignment: 9
      pivot: {x: 0.5408163, y: 0.51086956}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee8d8eb4301a121c0800000000000000
      internalID: -4530162714627483410
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_2
      rect:
        serializedVersion: 2
        x: 677
        y: 430
        width: 50
        height: 47
      alignment: 9
      pivot: {x: 0.45, y: 0.5212766}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f9ffd9a5b24a455a0800000000000000
      internalID: -6533416653296894049
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_3
      rect:
        serializedVersion: 2
        x: 420
        y: 426
        width: 50
        height: 38
      alignment: 9
      pivot: {x: 0.49, y: 0.4868421}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1358a8c054204f980800000000000000
      internalID: -8506171300609620687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_4
      rect:
        serializedVersion: 2
        x: 979
        y: 571
        width: 44
        height: 32
      alignment: 9
      pivot: {x: 0.4659091, y: 0.484375}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 303c4d520dbc8a1f0800000000000000
      internalID: -1033352019633192189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_5
      rect:
        serializedVersion: 2
        x: 401
        y: 1
        width: 47
        height: 32
      alignment: 9
      pivot: {x: 0.43617022, y: 0.484375}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b3f3b00d6fd132b0800000000000000
      internalID: -5606454401843334223
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_6
      rect:
        serializedVersion: 2
        x: 558
        y: 688
        width: 55
        height: 34
      alignment: 9
      pivot: {x: 0.44545454, y: 0.5147059}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f8b275d616089d1e0800000000000000
      internalID: -2172564189294941297
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_7
      rect:
        serializedVersion: 2
        x: 472
        y: 429
        width: 61
        height: 35
      alignment: 9
      pivot: {x: 0.46721312, y: 0.47142857}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ecfed1bc7a4a17f50800000000000000
      internalID: 6877459146545688526
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: backBullet-backBullet_8
      rect:
        serializedVersion: 2
        x: 979
        y: 539
        width: 43
        height: 30
      alignment: 9
      pivot: {x: 0.5, y: 0.45}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a442c2b260820ab0800000000000000
      internalID: -5043327471307307866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: birdBullet
      rect:
        serializedVersion: 2
        x: 607
        y: 421
        width: 68
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5c55e88798972050800000000000000
      internalID: 5775752823730166878
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blast
      rect:
        serializedVersion: 2
        x: 420
        y: 469
        width: 101
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 847813783d07f6690800000000000000
      internalID: -7606737191796897976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00000
      rect:
        serializedVersion: 2
        x: 729
        y: 378
        width: 68
        height: 65
      alignment: 9
      pivot: {x: 0.44117647, y: 0.50769234}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9f74ca87c0a6d21a0800000000000000
      internalID: -6832688457854466055
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00001
      rect:
        serializedVersion: 2
        x: 396
        y: 35
        width: 77
        height: 79
      alignment: 9
      pivot: {x: 0.5064935, y: 0.5063291}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ba488f4094591dd0800000000000000
      internalID: -2514885938123355466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00002
      rect:
        serializedVersion: 2
        x: 316
        y: 32
        width: 78
        height: 79
      alignment: 9
      pivot: {x: 0.5, y: 0.5063291}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7b50d3a6e16e9aa0800000000000000
      internalID: -6152372397722805377
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00003
      rect:
        serializedVersion: 2
        x: 281
        y: 157
        width: 78
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 162c81c609f97dce0800000000000000
      internalID: -1380459318128098719
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00004
      rect:
        serializedVersion: 2
        x: 475
        y: 40
        width: 78
        height: 76
      alignment: 9
      pivot: {x: 0.5, y: 0.5131579}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: faa101ec800387670800000000000000
      internalID: 8536625908055808687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00005
      rect:
        serializedVersion: 2
        x: 558
        y: 629
        width: 54
        height: 57
      alignment: 9
      pivot: {x: 0.5185185, y: 0.47368422}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24ec362d62ca731c0800000000000000
      internalID: -4523958017931882942
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueBlast_00006
      rect:
        serializedVersion: 2
        x: 677
        y: 379
        width: 48
        height: 49
      alignment: 9
      pivot: {x: 0.625, y: 0.3877551}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24d8cc77dc6cc42a0800000000000000
      internalID: -6751803155554726590
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bomb
      rect:
        serializedVersion: 2
        x: 741
        y: 492
        width: 92
        height: 62
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7480469e81cb3ced0800000000000000
      internalID: -2394863761650087865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0001
      rect:
        serializedVersion: 2
        x: 835
        y: 466
        width: 119
        height: 61
      alignment: 9
      pivot: {x: 0.38235295, y: 0.45081967}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c56f2a989c7350e70800000000000000
      internalID: 9080725562399585884
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost1vfx
      rect:
        serializedVersion: 2
        x: 536
        y: 128
        width: 69
        height: 68
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04fba14114f108610800000000000000
      internalID: 1621330230224011072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0002
      rect:
        serializedVersion: 2
        x: 420
        y: 656
        width: 136
        height: 66
      alignment: 9
      pivot: {x: 0.43014705, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c959716a37612440800000000000000
      internalID: 4909318569251658181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost2vfx
      rect:
        serializedVersion: 2
        x: 555
        y: 58
        width: 69
        height: 68
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20792fc8c239dd310800000000000000
      internalID: 1431462076126238466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0003
      rect:
        serializedVersion: 2
        x: 616
        y: 708
        width: 148
        height: 66
      alignment: 9
      pivot: {x: 0.47635135, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a028be7d383a7d60800000000000000
      internalID: 7888679534063001762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost3vfx
      rect:
        serializedVersion: 2
        x: 369
        y: 116
        width: 69
        height: 69
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fcf534c5d13f6360800000000000000
      internalID: 7165000326359416051
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0004
      rect:
        serializedVersion: 2
        x: 835
        y: 662
        width: 148
        height: 67
      alignment: 9
      pivot: {x: 0.5236486, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 01b0f7266b1cca1e0800000000000000
      internalID: -2185158730100831472
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost4vfx
      rect:
        serializedVersion: 2
        x: 948
        y: 355
        width: 69
        height: 68
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac09590bfe6c28080800000000000000
      internalID: -9186561557120315190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0005
      rect:
        serializedVersion: 2
        x: 632
        y: 776
        width: 154
        height: 67
      alignment: 9
      pivot: {x: 0.5032467, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d784ed7ce9e191f0800000000000000
      internalID: -1039792837012191275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0006
      rect:
        serializedVersion: 2
        x: 632
        y: 845
        width: 155
        height: 66
      alignment: 9
      pivot: {x: 0.5, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acc3d56ca5a91ada0800000000000000
      internalID: -5935293119231869750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0007
      rect:
        serializedVersion: 2
        x: 835
        y: 466
        width: 119
        height: 61
      alignment: 9
      pivot: {x: 0.38235295, y: 0.45081967}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15e8935d8ffe23d70800000000000000
      internalID: 9021536855584771665
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0008
      rect:
        serializedVersion: 2
        x: 420
        y: 656
        width: 136
        height: 66
      alignment: 9
      pivot: {x: 0.43014705, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8990a98e259e6340800000000000000
      internalID: 4858985075089381772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0009
      rect:
        serializedVersion: 2
        x: 616
        y: 708
        width: 148
        height: 66
      alignment: 9
      pivot: {x: 0.47635135, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fcfa50f999fad3d0800000000000000
      internalID: -3180955747226026765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0010
      rect:
        serializedVersion: 2
        x: 835
        y: 662
        width: 148
        height: 67
      alignment: 9
      pivot: {x: 0.5236486, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56d391537591bd490800000000000000
      internalID: -7720549273852887707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0011
      rect:
        serializedVersion: 2
        x: 632
        y: 776
        width: 154
        height: 67
      alignment: 9
      pivot: {x: 0.5032467, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24430c86c49c32a80800000000000000
      internalID: -8492723142278499262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost0012
      rect:
        serializedVersion: 2
        x: 632
        y: 845
        width: 155
        height: 66
      alignment: 9
      pivot: {x: 0.5, y: 0.49242425}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7be70196114cdbab0800000000000000
      internalID: -4990617233000464713
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: buildingMine
      rect:
        serializedVersion: 2
        x: 835
        y: 861
        width: 168
        height: 162
      alignment: 9
      pivot: {x: 0.4940476, y: 0.4814815}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 703917ee8fd4181f0800000000000000
      internalID: -1044467907025595641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullet
      rect:
        serializedVersion: 2
        x: 614
        y: 530
        width: 125
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db29327308e726b00800000000000000
      internalID: 820357171280843453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletEnemy
      rect:
        serializedVersion: 2
        x: 440
        y: 118
        width: 46
        height: 73
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08bce8dbd4657c3b0800000000000000
      internalID: -5492326328662307968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletEnemy2
      rect:
        serializedVersion: 2
        x: 488
        y: 123
        width: 46
        height: 73
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b65c0ca1cd6b6c50800000000000000
      internalID: 6659537152580015793
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullethit_00000
      rect:
        serializedVersion: 2
        x: 199
        y: 216
        width: 54
        height: 21
      alignment: 9
      pivot: {x: 0.25, y: 0.54761904}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15598d49bcaeb26c0800000000000000
      internalID: -4166978870104779439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullethit_00001
      rect:
        serializedVersion: 2
        x: 243
        y: 131
        width: 36
        height: 83
      alignment: 9
      pivot: {x: -0.125, y: 0.48795182}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a07db474eea27a90800000000000000
      internalID: -7317594148289285981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullethit_00002
      rect:
        serializedVersion: 2
        x: 343
        y: 521
        width: 75
        height: 118
      alignment: 9
      pivot: {x: 0.46, y: 0.470339}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55d59f9b50a393b50800000000000000
      internalID: 6573348927393324373
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullethit_00003
      rect:
        serializedVersion: 2
        x: 20
        y: 97
        width: 80
        height: 127
      alignment: 9
      pivot: {x: 0.49375, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a795449d30c341f0800000000000000
      internalID: -1061793716416178266
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bullethit_00004
      rect:
        serializedVersion: 2
        x: 20
        y: 226
        width: 81
        height: 127
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ff54612fd3da75f0800000000000000
      internalID: -758060631967113232
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitA001
      rect:
        serializedVersion: 2
        x: 523
        y: 534
        width: 19
        height: 36
      alignment: 9
      pivot: {x: 0.6052632, y: 0.625}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba6699547a7ea7fc0800000000000000
      internalID: -3496227455086532949
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitA002
      rect:
        serializedVersion: 2
        x: 286
        y: 2
        width: 42
        height: 22
      alignment: 9
      pivot: {x: 0.5595238, y: 1.0227273}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4661c301bebff600800000000000000
      internalID: 504331350770280012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitA003
      rect:
        serializedVersion: 2
        x: 376
        y: 382
        width: 72
        height: 41
      alignment: 9
      pivot: {x: 0.4652778, y: 0.5487805}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5632b3a5d9df2e2b0800000000000000
      internalID: -5556600137956252827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitA004
      rect:
        serializedVersion: 2
        x: 739
        y: 445
        width: 77
        height: 45
      alignment: 9
      pivot: {x: 0.512987, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd1c6ba02b1735c80800000000000000
      internalID: -8335193475834789413
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitA005
      rect:
        serializedVersion: 2
        x: 818
        y: 419
        width: 77
        height: 45
      alignment: 9
      pivot: {x: 0.512987, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0584c1ff9a4838cf0800000000000000
      internalID: -251211288538232752
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitB001
      rect:
        serializedVersion: 2
        x: 616
        y: 827
        width: 12
        height: 8
      alignment: 9
      pivot: {x: 0.45833334, y: 3.375}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9208b1118333ef3b0800000000000000
      internalID: -5476883780937744343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitB002
      rect:
        serializedVersion: 2
        x: 626
        y: 317
        width: 35
        height: 46
      alignment: 9
      pivot: {x: 0.47142857, y: 0.5869565}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 524e7c600704b9760800000000000000
      internalID: 7465631657191007269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitB003
      rect:
        serializedVersion: 2
        x: 626
        y: 365
        width: 47
        height: 54
      alignment: 9
      pivot: {x: 0.5212766, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7135beb561a258390800000000000000
      internalID: -7816795302711831785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletHitB004
      rect:
        serializedVersion: 2
        x: 677
        y: 323
        width: 46
        height: 54
      alignment: 9
      pivot: {x: 0.4673913, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e1262c28caa5693d0800000000000000
      internalID: -3200270788225506786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletOval
      rect:
        serializedVersion: 2
        x: 103
        y: 214
        width: 94
        height: 202
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4e8ac64f29c79560800000000000000
      internalID: 7320540924202356298
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletShoot001
      rect:
        serializedVersion: 2
        x: 565
        y: 401
        width: 37
        height: 25
      alignment: 9
      pivot: {x: -0.0945946, y: 0.52}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 78da6d989d0c9b410800000000000000
      internalID: 1493436792003014023
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletShoot002
      rect:
        serializedVersion: 2
        x: 524
        y: 8
        width: 70
        height: 30
      alignment: 9
      pivot: {x: 0.4642857, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7f831da738f07ae0800000000000000
      internalID: -1553468953432191107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletShoot003
      rect:
        serializedVersion: 2
        x: 494
        y: 393
        width: 69
        height: 30
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7492b19aba950f500800000000000000
      internalID: 427940558411606343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletShoot004
      rect:
        serializedVersion: 2
        x: 450
        y: 3
        width: 72
        height: 30
      alignment: 9
      pivot: {x: 0.5625, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0527a8c342b7d6f20800000000000000
      internalID: 3417523087808819792
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bulletShoot005
      rect:
        serializedVersion: 2
        x: 330
        y: 3
        width: 69
        height: 27
      alignment: 9
      pivot: {x: 0.5869565, y: 0.44444445}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7312941eabf9ec310800000000000000
      internalID: 1427253756915425591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: creepyBullet
      rect:
        serializedVersion: 2
        x: 336
        y: 436
        width: 82
        height: 83
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2575559b1a56181f0800000000000000
      internalID: -1044441893299726510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dashFront1
      rect:
        serializedVersion: 2
        x: 1
        y: 564
        width: 108
        height: 70
      alignment: 9
      pivot: {x: 0.5, y: 0.75}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d5dd9e21cfe4520c0800000000000000
      internalID: -4601184599733052067
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dashFront2
      rect:
        serializedVersion: 2
        x: 1
        y: 636
        width: 110
        height: 69
      alignment: 9
      pivot: {x: 0.5, y: 0.75}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d6ccb42c902794f0800000000000000
      internalID: -822152551968487720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: enemyBullet
      rect:
        serializedVersion: 2
        x: 766
        y: 726
        width: 16
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a09f89b4ad1c80d0800000000000000
      internalID: -3419325423756013402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fatCatBullet
      rect:
        serializedVersion: 2
        x: 232
        y: 26
        width: 82
        height: 83
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a3e3b54530c29ed0800000000000000
      internalID: -2408651515654577241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp1
      rect:
        serializedVersion: 2
        x: 985
        y: 651
        width: 38
        height: 81
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b98c3841544f700a0800000000000000
      internalID: -6915290125270005605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp2
      rect:
        serializedVersion: 2
        x: 985
        y: 734
        width: 38
        height: 87
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5fee78c10283440f0800000000000000
      internalID: -1133719495621742859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp3
      rect:
        serializedVersion: 2
        x: 183
        y: 21
        width: 47
        height: 88
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 671673279b1029a00800000000000000
      internalID: 761673182975844726
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp4
      rect:
        serializedVersion: 2
        x: 789
        y: 816
        width: 44
        height: 95
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 214d6878fff3ebe30800000000000000
      internalID: 4521121442649527314
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp5
      rect:
        serializedVersion: 2
        x: 20
        y: 5
        width: 40
        height: 90
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44b53f501c67fdac0800000000000000
      internalID: -3828210586841031868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp6
      rect:
        serializedVersion: 2
        x: 135
        y: 8
        width: 46
        height: 89
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a71d78d52b75f290800000000000000
      internalID: -7857238572401879128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fireLoopUp7
      rect:
        serializedVersion: 2
        x: 788
        y: 726
        width: 45
        height: 88
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a323bfd078312290800000000000000
      internalID: -7916984612500069469
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect1
      rect:
        serializedVersion: 2
        x: 312
        y: 617
        width: 29
        height: 38
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da380ace00830d170800000000000000
      internalID: 8201116498062771117
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect2
      rect:
        serializedVersion: 2
        x: 420
        y: 572
        width: 129
        height: 82
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb20017336b8480d0800000000000000
      internalID: -3421456558652915013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect3
      rect:
        serializedVersion: 2
        x: 616
        y: 590
        width: 131
        height: 116
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8de4205db8ae4280800000000000000
      internalID: -9057116269548606069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect4
      rect:
        serializedVersion: 2
        x: 205
        y: 697
        width: 138
        height: 113
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 858b9228eda987c70800000000000000
      internalID: 8969088938365532248
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect5
      rect:
        serializedVersion: 2
        x: 835
        y: 529
        width: 142
        height: 131
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48692e8abfd5550f0800000000000000
      internalID: -1128892796134844796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gunEffect6
      rect:
        serializedVersion: 2
        x: 835
        y: 731
        width: 148
        height: 128
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79218d6b29acaa100800000000000000
      internalID: 120131071560389271
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: lineParticle
      rect:
        serializedVersion: 2
        x: 1005
        y: 823
        width: 17
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88bcd755ea74764b0800000000000000
      internalID: -5447306410197136504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: lineParticle2
      rect:
        serializedVersion: 2
        x: 1
        y: 12
        width: 17
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 834a60931deebd270800000000000000
      internalID: 8276471322595730488
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: mine
      rect:
        serializedVersion: 2
        x: 205
        y: 381
        width: 100
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2de31f5f1decb56f0800000000000000
      internalID: -694734316328894766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: missile
      rect:
        serializedVersion: 2
        x: 748
        y: 556
        width: 85
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dea0c0551b5319d60800000000000000
      internalID: 7895150657509526253
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: musicNote1
      rect:
        serializedVersion: 2
        x: 310
        y: 529
        width: 24
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f821981ad41cb5870800000000000000
      internalID: 8672738046598779535
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: musicNote2
      rect:
        serializedVersion: 2
        x: 312
        y: 657
        width: 31
        height: 38
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8728c201b3fc889b0800000000000000
      internalID: -5077580727278861704
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: musicNote3
      rect:
        serializedVersion: 2
        x: 310
        y: 488
        width: 24
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 13ce9fd334af5b830800000000000000
      internalID: 4086447403618135089
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: oktoMine
      rect:
        serializedVersion: 2
        x: 420
        y: 724
        width: 194
        height: 189
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82575eb4721530cd0800000000000000
      internalID: -2593139731216370392
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts1
      rect:
        serializedVersion: 2
        x: 312
        y: 579
        width: 25
        height: 36
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6f28c7e44484e3f0800000000000000
      internalID: -872426996321734805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts2
      rect:
        serializedVersion: 2
        x: 310
        y: 446
        width: 23
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55d43fe0f82e21470800000000000000
      internalID: 8363996562039459157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts3
      rect:
        serializedVersion: 2
        x: 956
        y: 502
        width: 21
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9f0fb2d82265fe80800000000000000
      internalID: -8145496423555723364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts4
      rect:
        serializedVersion: 2
        x: 232
        y: 2
        width: 52
        height: 22
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70035fa6b4d4d4010800000000000000
      internalID: 1174680064125906951
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts5
      rect:
        serializedVersion: 2
        x: 307
        y: 405
        width: 23
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5fa73a5c39ed8cdd0800000000000000
      internalID: -2465476069727962379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: parts6
      rect:
        serializedVersion: 2
        x: 450
        y: 385
        width: 42
        height: 38
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ecf9e3838022e8720800000000000000
      internalID: 2850253032870813646
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma01
      rect:
        serializedVersion: 2
        x: 424
        y: 915
        width: 207
        height: 108
      alignment: 9
      pivot: {x: 0.5072464, y: 0.4537037}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: afc9694e02b21d130800000000000000
      internalID: 3589697798265019642
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma02
      rect:
        serializedVersion: 2
        x: 1
        y: 814
        width: 208
        height: 106
      alignment: 9
      pivot: {x: 0.49519232, y: 0.43396226}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eeae73f6f9b642e70800000000000000
      internalID: 9089508280450411246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma03
      rect:
        serializedVersion: 2
        x: 633
        y: 913
        width: 200
        height: 110
      alignment: 9
      pivot: {x: 0.485, y: 0.42727274}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35c8a1765f53dba80800000000000000
      internalID: -8449537997764129709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma04
      rect:
        serializedVersion: 2
        x: 213
        y: 916
        width: 209
        height: 107
      alignment: 9
      pivot: {x: 0.49760765, y: 0.39252338}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5358fb841e4498fc0800000000000000
      internalID: -3492184301670267595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma05
      rect:
        serializedVersion: 2
        x: 1
        y: 922
        width: 210
        height: 101
      alignment: 9
      pivot: {x: 0.5, y: 0.41584158}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c77cdbc54d8cf0be0800000000000000
      internalID: -1508766535731263620
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma06
      rect:
        serializedVersion: 2
        x: 211
        y: 812
        width: 207
        height: 102
      alignment: 9
      pivot: {x: 0.5024155, y: 0.42156863}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce103fd110c645e70800000000000000
      internalID: 9103019498875847148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasma07
      rect:
        serializedVersion: 2
        x: 1
        y: 707
        width: 202
        height: 105
      alignment: 9
      pivot: {x: 0.48019803, y: 0.42857143}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fb88eb0c6b45d450800000000000000
      internalID: 6112874996653657079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect1
      rect:
        serializedVersion: 2
        x: 766
        y: 611
        width: 67
        height: 113
      alignment: 9
      pivot: {x: 0.41791046, y: 0.88495576}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b3f26c98606e54510800000000000000
      internalID: 1532884163945180987
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect2
      rect:
        serializedVersion: 2
        x: 102
        y: 99
        width: 58
        height: 113
      alignment: 9
      pivot: {x: 0.5, y: 0.8761062}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95e2d95f5d861bcf0800000000000000
      internalID: -238294037115883943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect3
      rect:
        serializedVersion: 2
        x: 285
        y: 241
        width: 70
        height: 138
      alignment: 9
      pivot: {x: 0.54285717, y: 0.7246377}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d4c0f126b7ca6ed30800000000000000
      internalID: 4460442126884211789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect4
      rect:
        serializedVersion: 2
        x: 199
        y: 239
        width: 84
        height: 140
      alignment: 9
      pivot: {x: 0.47619048, y: 0.71428573}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f229b79c0ed6c370800000000000000
      internalID: 8342599505416889076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect5
      rect:
        serializedVersion: 2
        x: 345
        y: 641
        width: 73
        height: 169
      alignment: 9
      pivot: {x: 0.49315068, y: 0.5739645}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc878214490d7e540800000000000000
      internalID: 5037224043404097741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect6
      rect:
        serializedVersion: 2
        x: 416
        y: 193
        width: 69
        height: 187
      alignment: 9
      pivot: {x: 0.5362319, y: 0.5240642}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 53d1e94aef47315c0800000000000000
      internalID: -4245921386647904971
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect7
      rect:
        serializedVersion: 2
        x: 487
        y: 198
        width: 63
        height: 185
      alignment: 9
      pivot: {x: 0.47619048, y: 0.5243243}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cee80f96912310b50800000000000000
      internalID: 6557577617161096940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: plasmaEffect8
      rect:
        serializedVersion: 2
        x: 361
        y: 187
        width: 53
        height: 192
      alignment: 9
      pivot: {x: 0.28301886, y: 0.5208333}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0d8a49dd4ecac820800000000000000
      internalID: 2939388540566015243
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: playerMissile
      rect:
        serializedVersion: 2
        x: 523
        y: 489
        width: 86
        height: 42
      alignment: 9
      pivot: {x: 0.5, y: 0.5119048}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9b9d42e7b16065d30800000000000000
      internalID: 4419726799460948409
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke1
      rect:
        serializedVersion: 2
        x: 535
        y: 428
        width: 50
        height: 59
      alignment: 9
      pivot: {x: 0.52, y: 0.9830508}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40908ef3197b54620800000000000000
      internalID: 2757812181304609028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke2
      rect:
        serializedVersion: 2
        x: 551
        y: 533
        width: 61
        height: 94
      alignment: 9
      pivot: {x: 0.55737704, y: 0.5851064}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e14259730a8b98af0800000000000000
      internalID: -393580494151408610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke3
      rect:
        serializedVersion: 2
        x: 162
        y: 111
        width: 79
        height: 101
      alignment: 9
      pivot: {x: 0.5063291, y: 0.5445545}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb8859f556da93040800000000000000
      internalID: 4627920743006832828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke4
      rect:
        serializedVersion: 2
        x: 113
        y: 597
        width: 90
        height: 108
      alignment: 9
      pivot: {x: 0.56666666, y: 0.5277778}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89b080dbe1fe815e0800000000000000
      internalID: -1938536724283520104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke5
      rect:
        serializedVersion: 2
        x: 205
        y: 486
        width: 103
        height: 112
      alignment: 9
      pivot: {x: 0.52427185, y: 0.49107143}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d03aa57c3f3519960800000000000000
      internalID: 7606953552092242701
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke6
      rect:
        serializedVersion: 2
        x: 1
        y: 457
        width: 101
        height: 105
      alignment: 9
      pivot: {x: 0.57425743, y: 0.42857143}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96b593eb637fb5d10800000000000000
      internalID: 2115556264472304489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: rocketSmoke7
      rect:
        serializedVersion: 2
        x: 205
        y: 600
        width: 105
        height: 95
      alignment: 9
      pivot: {x: 0.552381, y: 0.37894738}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb41f8ed1804a2b80800000000000000
      internalID: -8418845626886843202
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: shipBullet
      rect:
        serializedVersion: 2
        x: 62
        y: 6
        width: 71
        height: 89
      alignment: 9
      pivot: {x: 0.5070422, y: 0.505618}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 39660b447042e59b0800000000000000
      internalID: -5089590915246299501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: smokeParticle
      rect:
        serializedVersion: 2
        x: 979
        y: 495
        width: 42
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36668f50fd6d3d4b0800000000000000
      internalID: -5416749673430358429
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: smokeParticle3
      rect:
        serializedVersion: 2
        x: 979
        y: 605
        width: 44
        height: 44
      alignment: 9
      pivot: {x: 0.48863637, y: 0.5113636}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21bb7a105a2d9fa30800000000000000
      internalID: 4249659329539980050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: smokeParticlePlayer
      rect:
        serializedVersion: 2
        x: 332
        y: 381
        width: 42
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 192093353a2009d50800000000000000
      internalID: 6741891542672802449
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: smokeParticlePlayer2
      rect:
        serializedVersion: 2
        x: 281
        y: 113
        width: 42
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9004e9c71fd0145f0800000000000000
      internalID: -774322330101989367
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: smokeParticlePlayer3
      rect:
        serializedVersion: 2
        x: 325
        y: 113
        width: 42
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba31f86e14f070620800000000000000
      internalID: 2740175673993663403
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: strobe
      rect:
        serializedVersion: 2
        x: 1
        y: 355
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec5bcdacdc792bd30800000000000000
      internalID: 4445782692295652814
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sugarBomb
      rect:
        serializedVersion: 2
        x: 111
        y: 533
        width: 92
        height: 62
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e36a0147cf77c8630800000000000000
      internalID: 3930648500950312510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: vaderBullet
      rect:
        serializedVersion: 2
        x: 956
        y: 425
        width: 66
        height: 68
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: edef3582b1670d5d0800000000000000
      internalID: -3039799889462296866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: vaderMissile
      rect:
        serializedVersion: 2
        x: 596
        y: 1
        width: 79
        height: 55
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44762435d4a87e870800000000000000
      internalID: 8712084068885489476
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: waterMissile
      rect:
        serializedVersion: 2
        x: 104
        y: 418
        width: 96
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32610de636e4a5ea0800000000000000
      internalID: -5883303774238271965
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: waterMissileActivated
      rect:
        serializedVersion: 2
        x: 104
        y: 467
        width: 99
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4921875}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45c0f0eb0ce73ce30800000000000000
      internalID: 4522597817097587796
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: a4cc212c24cb04397ad8308c23cf55fe
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      bulletHitB002: 7465631657191007269
      blueBlast_00006: -6751803155554726590
      plasmaEffect6: -4245921386647904971
      rocketSmoke5: 7606953552092242701
      bulletHitA004: -8335193475834789413
      lineParticle2: 8276471322595730488
      boost4vfx: -9186561557120315190
      plasmaEffect3: 4460442126884211789
      missile: 7895150657509526253
      bullethit_00004: -758060631967113232
      gunEffect2: -3421456558652915013
      backBullet-backBullet_8: -5043327471307307866
      dashFront1: -4601184599733052067
      mine: -694734316328894766
      bulletEnemy: -5492326328662307968
      fatCatBullet: -2408651515654577241
      sugarBomb: 3930648500950312510
      waterMissileActivated: 4522597817097587796
      boost0010: -7720549273852887707
      fireLoopUp1: -6915290125270005605
      musicNote2: -5077580727278861704
      plasmaEffect5: 5037224043404097741
      bulletShoot002: -1553468953432191107
      parts2: 8363996562039459157
      boost0004: -2185158730100831472
      bulletShoot005: 1427253756915425591
      bullethit_00003: -1061793716416178266
      musicNote1: 8672738046598779535
      boost0012: -4990617233000464713
      fireLoopUp7: -7916984612500069469
      gunEffect4: 8969088938365532248
      bulletOval: 7320540924202356298
      gunEffect3: -9057116269548606069
      plasmaEffect1: 1532884163945180987
      smokeParticlePlayer2: -774322330101989367
      gunEffect6: 120131071560389271
      enemyBullet: -3419325423756013402
      smokeParticle3: 4249659329539980050
      boost0003: 7888679534063001762
      blueBlast_00002: -6152372397722805377
      blueBlast_00004: 8536625908055808687
      boost0007: 9021536855584771665
      bulletHitA002: 504331350770280012
      playerMissile: 4419726799460948409
      smokeParticle: -5416749673430358429
      plasmaEffect7: 6557577617161096940
      rocketSmoke3: 4627920743006832828
      fireLoopUp6: -7857238572401879128
      backBullet-backBullet_6: -2172564189294941297
      boost0008: 4858985075089381772
      backBullet-backBullet_2: -6533416653296894049
      blueBlast_00005: -4523958017931882942
      arrow: -2043017983467968750
      boost0006: -5935293119231869750
      blueBlast_00000: -6832688457854466055
      backBullet-backBullet_0: -6270828003723659175
      boost0009: -3180955747226026765
      bulletHitA003: -5556600137956252827
      shipBullet: -5089590915246299501
      plasma04: -3492184301670267595
      bomb: -2394863761650087865
      buildingMine: -1044467907025595641
      plasma02: 9089508280450411246
      vaderMissile: 8712084068885489476
      bulletShoot003: 427940558411606343
      rocketSmoke4: -1938536724283520104
      bulletEnemy2: 6659537152580015793
      boost0002: 4909318569251658181
      fireLoopUp2: -1133719495621742859
      gunEffect5: -1128892796134844796
      parts6: 2850253032870813646
      rocketSmoke6: 2115556264472304489
      fireLoopUp3: 761673182975844726
      smokeParticlePlayer: 6741891542672802449
      plasmaEffect4: 8342599505416889076
      boost0001: 9080725562399585884
      dashFront2: -822152551968487720
      bulletShoot004: 3417523087808819792
      smokeParticlePlayer3: 2740175673993663403
      backBullet-backBullet_5: -5606454401843334223
      rocketSmoke1: 2757812181304609028
      fireLoopUp5: -3828210586841031868
      creepyBullet: -1044441893299726510
      oktoMine: -2593139731216370392
      plasmaEffect2: -238294037115883943
      parts3: -8145496423555723364
      boost0011: -8492723142278499262
      boost3vfx: 7165000326359416051
      bullethit_00002: 6573348927393324373
      boost2vfx: 1431462076126238466
      boost0005: -1039792837012191275
      bullethit_00001: -7317594148289285981
      bulletHitB004: -3200270788225506786
      gunEffect1: 8201116498062771117
      bulletShoot001: 1493436792003014023
      blueBlast_00001: -2514885938123355466
      blueBlast_00003: -1380459318128098719
      parts5: -2465476069727962379
      bulletHitB001: -5476883780937744343
      musicNote3: 4086447403618135089
      birdBullet: 5775752823730166878
      bulletHitA005: -251211288538232752
      boost1vfx: 1621330230224011072
      bulletHitA001: -3496227455086532949
      vaderBullet: -3039799889462296866
      parts4: 1174680064125906951
      lineParticle: -5447306410197136504
      plasma07: 6112874996653657079
      rocketSmoke2: -393580494151408610
      plasma05: -1508766535731263620
      rocketSmoke7: -8418845626886843202
      parts1: -872426996321734805
      strobe: 4445782692295652814
      fireLoopUp4: 4521121442649527314
      waterMissile: -5883303774238271965
      backBullet-backBullet_3: -8506171300609620687
      bullet: 820357171280843453
      bulletHitB003: -7816795302711831785
      backBullet-backBullet_4: -1033352019633192189
      backBullet-backBullet_7: 6877459146545688526
      plasma03: -8449537997764129709
      backBullet-backBullet_1: -4530162714627483410
      plasma01: 3589697798265019642
      plasma06: 9103019498875847148
      blast: -7606737191796897976
      bullethit_00000: -4166978870104779439
      plasmaEffect8: 2939388540566015243
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
