﻿using UnityEngine;

public class FollowCameraObject : MonoBehaviour
{
    float offsetX, offsetY;
    float interpolationSpeed;
    float zPosition;
    bool followCamX, followCamY;

    Transform mainCameraTransform;

    public void Init(float xOffset, float yOffset, float positionZ = 0, float lerpSpeed = 0, bool followX = true,
        bool followY = true)
    {
        offsetX = xOffset;
        offsetY = yOffset;
        zPosition = positionZ;
        interpolationSpeed = lerpSpeed;
        followCamX = followX;
        followCamY = followY;
    }

    private void Awake()
    {
        mainCameraTransform = Camera.main.transform;
    }

    void LateUpdate()
    {
        float speed = interpolationSpeed == 0 ? 1 : interpolationSpeed * Time.deltaTime;
        float targetX = followCamX ? mainCameraTransform.position.x + offsetX : transform.position.x;
        float targetY = followCamY ? mainCameraTransform.position.y + offsetY : transform.position.y;

        Vector3 targetPosition = new Vector3(targetX, targetY, zPosition);
        transform.position = Vector3.Lerp(transform.position, targetPosition, speed);
    }
}
