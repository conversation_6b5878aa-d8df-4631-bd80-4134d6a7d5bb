﻿using Spine;

using UnityEngine;

/// <summary>
/// 跟WXR_GunKitty一样了
/// </summary>
public class WXR_Mechanic : Sidekick
{
    [HideInInspector] public Vector2 direction;
    [HideInInspector] public bool shootLeft;
    float shootAngle = 0;

    private float distance;
    private float maxDistance = 11;
    private void Start()
    {
        Init();

    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        scheduleUpdate = false;
        shootLeft = true;
        //transform.localScale = new Vector3(scale, scale, 1);
        SetStartingPosition();
    }

    public override void StartSidekick()
    {
        InvokeRepeating("Shoot", 0.15f, 0.15f);
    }


    void ShootEvent(TrackEntry trackEntry, Spine.Event e)
    {
        if (e.Data.Name == "shoot")
            Shoot();
    }

    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;

        if (!Globals.beginSidekickShoot)
            return;

        if (transform.localScale.x < 0)
        {
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                Bullet bulletLayer = go.GetComponent<Bullet>();
                

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                double playerDamage = Helper.GetPlayerBulletRealDamage(damage);
                bulletLayer.setDamage(playerDamage);


                bulletLayer.transform.localScale = new Vector3(1f, 1f, 1);
                //bullet->setFlippedX(true);

                var gun1Pos = (Vector2)sidekickSkeletonTran.position;

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun1Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun1Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = Vector2.SignedAngle(direction - gun1Pos, transform.right) - 95 + 180;
                float angle = shootAngle - 95;
                angle = angle < 0 ? 360 + angle : angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);

                //bullet->setRotation(spBone_getWorldRotationX(gun1));

                var dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.PlayBulletAnim(duration, dest);
                if (spriteFrame) bulletLayer.SetSpriteFrame(spriteFrame);
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }


            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                Bullet bulletLayer = go.GetComponent<Bullet>();
                

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                double playerDamage = Helper.GetPlayerBulletRealDamage(damage);
                bulletLayer.setDamage(playerDamage);
                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun2Pos = (Vector2)sidekickSkeletonTran.position;

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun2Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun2Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = Vector2.SignedAngle(direction - gun2Pos, transform.right) - 70 + 180;
                float angle = shootAngle - 120;
                angle = angle < 0 ? 360 + angle : angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);

                //bullet->setRotation(spBone_getWorldRotationX(gun2));
                var dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.PlayBulletAnim(duration, dest, false);
                if (spriteFrame) bulletLayer.SetSpriteFrame(spriteFrame);
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }
        }
        else
        {
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                Bullet bulletLayer = go.GetComponent<Bullet>();
                

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.setDamage(3 + player.Stats.attack * 0.5f);


                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun1Pos = (Vector2)sidekickSkeletonTran.position;

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun1Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun1Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = -95 - Vector2.SignedAngle(direction - gun1Pos, transform.right);
                float angle = shootAngle - 90; //-95 - shootAngle;
                angle = angle < 0 ? 360 + angle : angle;
                float duration = 2;//子弹移动到一段距离的时间

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, angle);

                var dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);

                bulletLayer.PlayBulletAnim(duration, dest, false);
                if (spriteFrame) bulletLayer.SetSpriteFrame(spriteFrame);
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }


            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                Bullet bulletLayer = go.GetComponent<Bullet>();
                

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                double playerDamage = Helper.GetPlayerBulletRealDamage(damage);
                bulletLayer.setDamage(playerDamage);


                var gun2Pos = (Vector2)sidekickSkeletonTran.position;

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun2Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun2Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = -70 - Vector2.SignedAngle(direction - gun2Pos, transform.right);
                float angle = shootAngle - 90;// -70 - shootAngle;
                angle = angle < 0 ? 360 + angle : angle;
                float duration = 2;//子弹移动到一段距离的时间

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, angle);
                //bullet->setRotation(-spBone_getWorldRotationX(gun2) - 180);

                var dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);

                bulletLayer.PlayBulletAnim(duration, dest, false);
                if (spriteFrame) bulletLayer.SetSpriteFrame(spriteFrame);
                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }

        }
    }


    void Update()
    {
        if (!scheduleUpdate)
            return;

        if (!sidekickSkeletonTran)
        {
            return;
        }

        if (Globals.beginSidekickShoot)
        {
            direction = Globals.nearestAliveEnemy;
            float dir = Vector2.SignedAngle(Vector2.right, direction - (Vector2)transform.position);
            shootAngle = Globals.mobileControls
                ? dir < 0 ? 360 + dir : dir
                : player.RotationInDegrees;
        }
    }
}
