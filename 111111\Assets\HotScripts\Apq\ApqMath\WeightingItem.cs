﻿namespace Apq.ApqMath
{
    /// <summary>
    /// 加权项
    /// </summary>
    public class WeightingItem
    {
        /// <summary>
        /// 权重
        /// </summary>
        public int Weighting { get; set; } = 1;
        /// <summary>
        /// 已随机到的次数
        /// </summary>
        public int GotTimes { get; set; }
    }

    /// <summary>
    /// 加权项
    /// </summary>
    /// <typeparam name="T">该加权项代表的实例的类型</typeparam>
    public class WeightingItem<T> : WeightingItem
    {
        /// <summary>
        /// 代表的实例项
        /// </summary>
        public T Value { get; set; }
    }
}
