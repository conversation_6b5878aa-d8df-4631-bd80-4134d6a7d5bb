using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public enum SchemeType
{
    GameScene,
    Monster,
    Sound,
    Creature,
    CatMainStage,
    CatBrushMonster,
    CatMonsterBrushScope,
    CatSkill,
    CatSkillBullet,
    CatSkillBulletFly,
    Equipment,
    CatDrop,
    Medicament, //特殊
    ConstValue,//特殊
    Ai,
    AiA,
    AiE,
    CatSkillLevelUp,
    Buff,
    BuffEffect,
    MonsterNewScheme,
    PrizeTable,
    PlayerBaseProp,
    BattleEnemyInfo,
    BattleEventInfo,
    MissionInfo,
    BattleBrushEnemy,
    MonsterGold,
    EquipEffect,
}
// TODO, 将所有的pb类整合到这里来管理
public class HandlePBManager : Singleton<HandlePBManager>
{
    public string[] PbNameList =
    {
        "GameScene",
        "Monster",
        "Sound",
        "Creature",
        "CatMainStage",
        "CatBrushMonster",
        "CatMonsterBrushScope",
        "CatSkill",
        "CatSkillBullet",
        "CatSkillBulletFly",
        "Equipment",
        "CatDrop",
        "Medicament", //特殊
        "ConstValue",//特殊
        "Ai",
        "<PERSON><PERSON>",
        "Ai<PERSON>",
        "CatsKillLevelUp",
        "Buff",
        "BuffEffect",
        "MonsterNew",
        "PrizeTable",
        "PlayerBaseProp",
        "BattleEnemyInfo",
        "BattleEventInfo",
        "MissionInfo",
        "BattleBrushEnemy",
        "MonsterGold",
        "EquipEffect"
    };
    //public Dictionary<int, Dictionary<int, Component>> SchemeCacheData;

    //public void GetSchemeItemByID(global::ProtoBuf.IExtensible schemeType, int ID)
    //{
    //    int schemeIndex = (int)schemeType;
    //    if (_allSchemeData == null)
    //    {
    //        _allSchemeData = new Dictionary<int, Dictionary<int, ProtoBuf.IExtensible>>();
    //    }
    //    if (_allSchemeData.ContainsKey(schemeIndex))
    //    {
    //        Dictionary<int, ProtoBuf.IExtensible> data = _allSchemeData[schemeIndex];
    //        if (data.ContainsKey(ID))
    //        {
    //            return data.
    //        }
    //    }
    //}
}


