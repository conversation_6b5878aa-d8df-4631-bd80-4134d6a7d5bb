using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
public class ComboManager : MonoBehaviour
{
    private bool schedulerUpdate=false;

    [SerializeField] private Image timer;
    bool allowIncrease = true;

    [HideInInspector] public int killComboCounter;
    string schedulerId;
    string tweenId;

    

    public void Init()
    {
        schedulerId = "CMS" + GetInstanceID();
        tweenId = "CM" + GetInstanceID();
        killComboCounter = 0;


        schedulerUpdate = true;

    }


    private void EndKillCombo()
    {
        if (Globals.isFTUETutorial)
            return;

        killComboCounter--;

        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeMultiplier)
        {
            GameManager.instance.missionManager.SetPoint(killComboCounter);

        }
        Observer.DispatchCustomEvent("ComboEnd");
    }

    private void Update()
    {
        if (!schedulerUpdate)
            return;
        if (killComboCounter == 0)
        {
            timer.fillAmount =1;
            return;
        }
        float time = 4 - (0.2f * killComboCounter);
        if (time < 1.5f)
        {
            time = 1.5f;
        }

        float value = (timer.fillAmount*100) - (100.0f / (60.0f * time));
        if (value < 0)
        {
            value = 75;
            EndKillCombo();
        }

        timer.fillAmount =value/100;

    }


    public void AddKill()
    {
        if (allowIncrease)
        {
            timer.fillAmount = 1;;
            killComboCounter++;
            DOTween.Kill(schedulerId);
            DOTween.Kill(tweenId);
            GameManager.instance.playerHud.AddComboPoint();

            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeMultiplier)
            {
                GameManager.instance.missionManager.SetPoint(killComboCounter);

            }

            if (killComboCounter == 10)
            {
//               GameManager.instance.player.EnterBerzerkMode();
                //Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("ComboEnd", (void*)(unsigned long)killComboCounter);

                killComboCounter = 0;

                allowIncrease = false;
                DOTween.Sequence().SetId(schedulerId).AppendInterval(10).AppendCallback(() =>
                {
                    Observer.DispatchCustomEvent("ComboEnd");

                    allowIncrease = true;


                }).Play() ;
            }
        }
    }
}
