﻿using System.Diagnostics;
using System.Linq;
using System.Reflection;

namespace Apq.Utils
{
    public static class Util
    {
        #region IsDebug
        private static bool? _isDebugMode;
        /// <summary>
        /// 检查当前正在运行的主程序是否是在 Debug 配置下编译生成的。
        /// </summary>
        public static bool IsDebug
        {
            get
            {
                if (_isDebugMode == null)
                {
                    var assembly = Assembly.GetEntryAssembly() ?? new StackTrace().GetFrames().Last().GetMethod()!.Module.Assembly;
                    var debuggableAttribute = assembly.GetCustomAttribute<DebuggableAttribute>();
                    _isDebugMode = debuggableAttribute?.DebuggingFlags
                        .HasFlag(DebuggableAttribute.DebuggingModes.EnableEditAndContinue);
                }

                return _isDebugMode ?? false;
            }
        }
        #endregion

        #region GetCallingMethod
        /// <summary>
        /// GetCallingMethod(调用时注意需要加try-catch块防止优化)
        /// </summary>
        /// <returns>当前方法的调用方法</returns>
        /// <remarks>获取当前方法使用MethodBase.GetCurrentMethod()</remarks>
        public static MethodBase GetCallingMethod()
        {
            MethodBase mb = null;
            try
            {
                StackFrame frame = new(2);
                mb = frame.GetMethod();
            }
            catch (System.Exception ex)    // 一般是不可能捕获到异常的,防止编译器优化
            {
                UnityEngine.Debug.LogWarning($"运行 {nameof(GetCallingMethod)}() 时发生异常:{ex.Message}");
            }
            return mb;
        }
        #endregion

        #region ExchangeValue 值对调
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (long, long) ExchangeValue(long value1, long value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (ulong, ulong) ExchangeValue(ulong value1, ulong value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (int, int) ExchangeValue(int value1, int value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (uint, uint) ExchangeValue(uint value1, uint value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (short, short) ExchangeValue(short value1, short value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (ushort, ushort) ExchangeValue(ushort value1, ushort value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (sbyte, sbyte) ExchangeValue(sbyte value1, sbyte value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        /// <summary>
        /// 值对调
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        public static (byte, byte) ExchangeValue(byte value1, byte value2)
        {
            value1 ^= value2;
            value2 ^= value1;
            value1 ^= value2;
            return (value1, value2);
        }
        #endregion

        #region IsEquals
        /// <summary>
        /// 调用Equals方法判断是否相等
        /// </summary>
        public static bool IsEquals(object value1, object value2)
        {
            // 都是null，视为相等(这在ReferenceEquals中是视为不等)
            if (value1 == null || value2 == null) return true;

            if (ReferenceEquals(value1, value2)) return true;

            // 到这里时，至少有一个不为null。调用Equals方法判断是否相等
            if (value1 != null) return value1.Equals(value2);
            return value2.Equals(value1);
        }
        #endregion
    }
}
