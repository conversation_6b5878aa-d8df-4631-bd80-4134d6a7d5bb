using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BugBot2 : BugBot
{
    [SerializeField] private Sprite bulletSprite;

    public override void Destroy()
    {
        float random = Random.value * 4000;
        for (int i = 0; i < 6; i++)
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.setDamage(stats.bulletDamage / 2);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.duration = 6.5f;
            bullet.transform.SetScale(0.75f);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(50));
            bullet.transform.position = enemySprite.transform.position;
            bullet.transform.SetRotation(random + (i * 60));
            Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.eulerAngles.z), Globals.CocosToUnity(5000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.eulerAngles.z));
            bullet.PlayBulletAnim(8, dest);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }

        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spiderCatBullet2);
        base.Destroy();
    }
}
