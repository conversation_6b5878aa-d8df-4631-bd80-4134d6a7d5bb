using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using UnityEngine.SceneManagement;
using TMPro;
public class GameOverWinDesktop : BaseMenu
{
    [SerializeField] private Image blackOverlay;
    [SerializeField] private SkeletonGraphic player;
    [SerializeField] private SkeletonGraphic rayEffect;
    [SerializeField] private MainMenuButton hqButton;
    [SerializeField] private MainMenuButton nextMissionButton;
    [SerializeField] private MainMenuButton shopButton;
    [SerializeField] private MainMenuButton exitButton;
    [SerializeField] private GameObject shopHighlight;
    [SerializeField] private TextMeshProUGUI shopHighlightText;
    [SerializeField] private UnlockManager unlockManager;
    [SerializeField] private SideKickUnlockManager sideKickUnlockManager;

    public void Show()
    {
        if (!gameObject.activeSelf)
        {
            gameObject.SetActive(true);
            selectedPosition = 0;
            InitChild();
        }
    }

    private void InitChild()
    {
#if UNITY_STANDALONE
        //TODO
    //DesktopPointer *dp = DesktopPointer::create(true);
    //this.addChild(dp, INT_MAX);
    //dp.getPointer().setGlobalZOrder(INT_MAX);
#endif
        //TODO
        //Director::getInstance().getOpenGLView().setCursorVisible(false);

        if (GameData.instance.fileHandler.currentMission == 30)
        {
            sideKickUnlockManager.Show();

            Globals.StopAllSounds();

            if (GameData.instance.fileHandler.missionsCompleted < 30)
            {
                GameData.instance.fileHandler.missionsCompleted = 30;
            }
            DOTween.Sequence().Append(blackOverlay.DOFade(1, 0.5f)).AppendCallback(() => {
                //TODO Check EndingScene
                //Director::getInstance().replaceScene(EndingScene::createScene());
            }).Append(blackOverlay.DOFade(0, 0.5f)).Play();
        }

        if (GameData.instance.fileHandler._currentPlane == Globals.kplane1)
        {
            player.AnimationState.SetAnimation(0, "flying1", true);
            player.Skeleton.SetSkin("playerPlane1");


        }
        else if (GameData.instance.fileHandler._currentPlane == Globals.kplane2)
        {


            player.AnimationState.SetAnimation(0, "flying2", true);
            player.Skeleton.SetSkin("playerPlane2");
        }
        else if (GameData.instance.fileHandler._currentPlane == Globals.kplane3)
        {

            player.AnimationState.SetAnimation(0, "flying3", true);
            player.Skeleton.SetSkin("playerPlane3");
        }

        else if (GameData.instance.fileHandler._currentPlane == Globals.kplane4)
        {

            player.AnimationState.SetAnimation(0, "flying4", true);
            player.Skeleton.SetSkin("playerPlane4");
        }

        else if (GameData.instance.fileHandler._currentPlane == Globals.kplane5)
        {

            player.AnimationState.SetAnimation(0, "flying5", true);
            player.Skeleton.SetSkin("playerPlane5");
        }
        DOTween.Sequence().Append(player.GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, -230), 1f).SetEase(Ease.OutExpo)).Play();
        //player.setPositionY(150);
        //player.runAction(EaseExponentialOut::create(MoveTo::create(1.0f, cocos2d::Point(_winSize.width / 2, _winSize.height / 2 + 100))));
        //player.setRotation(-15);
        //player.setScale(0.5f);
        //player.setCameraMask(HUDCAMERA);

        rayEffect.AnimationState.SetAnimation(0, "levelUp3", true);

        DOTween.Sequence().Append(rayEffect.transform.DOScale(Vector2.one * 2.5f, 1f)).Play();
        rayEffect.AnimationState.TimeScale=0.5f;
        //    this.schedule(schedule_selector(GameOverBossWin::particles), 0.25);

        if (GameData.instance.fileHandler.currentMission != 1)
        {
            PlayerPrefs.SetInt("shopTutorial", 3);
        }
        //if (PlayerPrefs.GetInt("shopTutorial", 0) != 0)
        //{
            //{
                hqButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["headQuarters"] as string, () =>
                {
                    Globals.showNextMission = false;

                    GameData.instance.fileHandler.ReadGuns();
                    GameData.instance.fileHandler.SaveData();

                    GameManager.instance.timeManager.SetTimescale(1);
                    LuaManager.Instance.RunLuaFunction<int>("BattleManager.BattleEnd", 1);
                    //SceneManager.LoadScene(2);
                });
                buttonsList.Add(hqButton);
            //}
        //}
        //else
        //{
        //    hqButton.gameObject.SetActive(true);
        //}
        {
            if (PlayerPrefs.GetInt("shopTutorial", 0) != 0)
            {
                nextMissionButton.InitVariables("NEXT MISSION", () =>
                {

                    Globals.showNextMission = true;

                    GameData.instance.fileHandler.ReadGuns();
                    GameData.instance.fileHandler.SaveData();

                    GameManager.instance.timeManager.SetTimescale(1);
                    SceneManager.LoadScene(2);
                });
                buttonsList.Add(nextMissionButton);
            }
            else
            {
                nextMissionButton.gameObject.SetActive(false);
            }
        }

        {
           shopButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["visitShop"]as string, ()=>{
                if (PlayerPrefs.GetInt("shopTutorial", 0) == 0)
                {
                    PlayerPrefs.SetInt("shopTutorial", 1);
                   Globals.shopTutorial = true;
                }

                GameData.instance.fileHandler.ReadGuns();
                GameData.instance.fileHandler.SaveData();
                //Director::getInstance().getOpenGLView().setCursorVisible(false);
                //Director::getInstance().getScheduler().setTimeScale(1.0f);

                //Director::getInstance().resume();
                //Director::getInstance().replaceScene(ShopScene::createScene(false));
               GameManager.instance.timeManager.SetTimescale(1);
               SceneManager.LoadScene(3);
           });
            buttonsList.Add(shopButton);
            int getShopItemsAvailable = GameData.instance.CalculateShopItemsToBeUnlocked();
            if (getShopItemsAvailable > 0)
            {
                shopHighlight.SetActive(true);
                DOTween.Sequence().SetId("highlight").SetUpdate(true).Append(shopHighlight.transform.DOScale(Vector3.one * 1.87f, 0.75f).SetEase(Ease.InOutSine)).Append(shopHighlight.transform.DOScale(Vector3.one * 1.75f, 0.75f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();
                shopHighlightText.text = getShopItemsAvailable.ToString();
                
            }




        }

        if (PlayerPrefs.GetInt("shopTutorial", 0) != 0)
        {

            {
                exitButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAIN_MENU)["exitPopupHeading"] as string, () =>
                {

                    Application.Quit();

                });
                //this.addChild(node);
                buttonsList.Add(exitButton);

            }
        }
        else
        {
            exitButton.gameObject.SetActive(false);
        }
        Init();
        UpdateSelected();
        if (GameManager.instance.missionManager.isUnlockSideKick==1)
        {
            UnlockSideKick();
        }
        CheckItemUnlock();
    }

    private void UnlockSideKick()
    {
        sideKickUnlockManager.Show();
    }

    private void CheckItemUnlock()
    {
        //unlockManager.Init();
        unlockManager.Show();
    }
}
