using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine;
using Spine.Unity;
using TMPro;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
public class StartMenu : BaseMenu
{
    [SerializeField] private Image fadeImage;
    [SerializeField] private Color fadeColor;
    [SerializeField] private RectTransform buttonsContainer;
    [SerializeField] private SkeletonGraphic menuAnimation;
    [SerializeField] private OptionsMenu optionsMenu;
    [SerializeField] private MainMenuButton gameCenterButton;
    [SerializeField] private MainMenuButton startButton;
    [SerializeField] private MainMenuButton optionsButton;
    [SerializeField] private MainMenuButton loadGameButton;
    [SerializeField] private MainMenuButton quitButton;
    [SerializeField] private PopUp popUp;
    float mainOffset = 80;
    int fontSize = 50;
    float spacing = 50;


    //GameCenterAccessPointUI* _gameCenterAccessPointObj = nullptr;



    public void InitStartMenu()
    {

#if !UNITY_STANDALONE
        mainOffset = 120;
        fontSize = 70;
        spacing = 70;

#endif
        mainOffset = 80;
        fontSize = 50;
        spacing = 50;
        selectedPosition = 1;

        //setOpacity(0.2f);
        exitIndex = 2;
        clickSound = Constants.AudioClips.SOUND_BUTTON_TAP;
        //transform.localScale = Vector3.one * 0.85f;
        float animationTime = 1.5f;
        //TODO GameCenter
        //if (ThirdPartyInterface::isGameCenterAvailable())
        //{
        //    _gameCenterAccessPointObj = GameCenterAccessPointUI::create();
        //    this.addChild(_gameCenterAccessPointObj);
        //    _gameCenterAccessPointObj.setPosition(Vec2(_winSize.width / 2 - 700, _winSize.height - 30));
        //    Shared::rescale(_gameCenterAccessPointObj, 1);
        //}

        gameCenterButton.InitVariables(" ", () =>
        {

            //_gameCenterAccessPointObj.GameCenterAccessPointCall();

        });
        //buttonsList.Add(gameCenterButton);
        //macOptionsMenu.SetOpacity(0.1);
        gameCenterButton.SetSelectedFunc(() =>
        {
            //if (_gameCenterAccessPointObj != nullptr)
            //  _gameCenterAccessPointObj.SelectedCall();

        });
        gameCenterButton.SetUnSelectedFunc(() =>
        {
            //if (_gameCenterAccessPointObj != nullptr)
            //_gameCenterAccessPointObj.UnSelectedCall();

        });



    }
}
