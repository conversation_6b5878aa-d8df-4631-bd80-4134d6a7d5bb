﻿using System.Collections.Generic;

using X.PB;

namespace JsonStructure
{
    /// <summary>
    /// Buff列表中的一项
    /// </summary>
    public class Buff
    {
        /// <summary>
        /// 第一级ID
        /// </summary>
        public int BuffID { get; set; }
        /// <summary>
        /// 配置行
        /// </summary>
        public X.PB.Buff.Item CsvRow_Buff { get; set; }
        /// <summary>
        /// 包含的效果
        /// </summary>
        public List<BuffEffect.Item> BuffEffects { get; set; }
    }
}
