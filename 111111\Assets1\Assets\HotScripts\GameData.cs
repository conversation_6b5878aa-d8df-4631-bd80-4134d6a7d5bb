using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.SceneManagement;

public class GameData : MonoBehaviour
{
    public static GameData instance;

    PList mainValueMap;
    PList menuValueMap;
    PList textValueMap;

    int _currentGameSlot = 1;
    int _totalGameSlots = 1;


    int _numberOfItemsAvailableInShop = 0;
    int _numberOfItemsAvailableInSideKickShop = 0;

    int _numberOfShopItemTobeUnlocked = 0;
    int _numberOfSkItemTobeUnlocked = 0;
    string _currentLangugeCode;

    [HideInInspector] public FileHandler fileHandler;

    private void Awake()
    {
        if (!instance)
        {
            instance = this;
        }
        else
        {
            //Destroy(gameObject);
            return;
        }
        Debug.LogWarning("初始化gameData数据");
        InitNew();
    }

    public void InitNew()
    {
        fileHandler = new FileHandler();
        //DontDestroyOnLoad(gameObject);

        //Scene scene = SceneManager.GetActiveScene();

        //if(scene.name == "Splash")
        //{
        //    mainValueMap = new PList("GameData0");
        //    textValueMap = new PList("GameDataText0");
        //    menuValueMap = new PList("GameMenus0");
        //}
        //else
        //{
            mainValueMap = new PList("GameData");
            textValueMap = new PList("GameDataText"); 
            menuValueMap = new PList("GameMenus"); 
        //}



        fileHandler.ReadData();
    }

    //void OnHttpRequestCompleted(cocos2d::network::HttpClient* sender, network::HttpResponse* response) TODO
    //{
    //    if (200 == response->getResponseCode())
    //    {


    //        std::vector<char>* v = response->getResponseData();
    //        size_t len = v->size();
    //        char* buffer = new char[len + 1];
    //        for (unsigned int i = 0; i < len; i++)
    //        {
    //            buffer[i] = v->at(i);
    //        }
    //        buffer[len] = 0;

    //        stringplistString(buffer);


    //        auto path = FileUtils::getInstance()->getWritablePath();
    //        auto filepath = path.append("temp.plist");

    //        std::ofstream file(filepath, std::ofstream::out);
    //        file << buffer;
    //        file.close();


    //        mainValueMap = FileUtils::getInstance()->getValueMapFromFile(filepath);
    //        isFileDownloaded = true;
    //        auto path2 = FileUtils::getInstance()->getWritablePath();
    //        auto filepath2 = path2.append("GameData.plist");
    //        FileUtils::getInstance()->writeToFile(mainValueMap, filepath2);
    //        UserDefault::getInstance()->setBoolForKey("isFileDownloaded", isFileDownloaded);

    //    }
    //    else
    //    {
    //        if (isFileDownloaded == 0)
    //        {
    //            mainValueMap = FileUtils::getInstance()->getValueMapFromFile("res/GameData.plist");
    //        }
    //        else
    //        {
    //            auto path = FileUtils::getInstance()->getWritablePath();
    //            auto filepath = path.append("GameData.plist");
    //            if (FileUtils::getInstance()->isFileExist(filepath))
    //            {
    //                mainValueMap = FileUtils::getInstance()->getValueMapFromFile(filepath);
    //            }
    //            else
    //            {
    //                //#if Desktop
    //                mainValueMap = FileUtils::getInstance()->getValueMapFromFile("res/GameData.plist");
    //            }
    //        }

    //    }
    //}

    public PList GetMain()
    {
        return mainValueMap;
    }

    public PList GetShop()
    {
        return mainValueMap["ShopData"] as PList;
    }

    public PList GetPlayerData()
    {
        return mainValueMap["PlayerData"] as PList;
    }

    public PList GetMissions()
    {
        return mainValueMap["MissionData"] as PList;
    }

    public PList GetBoss(int bossNumber)
    {
        string ch = "boss" + bossNumber.ToString();
        return (mainValueMap["BossData"] as PList)[ch] as PList;
    }

    public PList GetMissions(int missionNumber)
    {
        string ch = "Mission" + missionNumber.ToString();
        return (mainValueMap["MissionData"] as PList)[ch] as PList;
    }

    public PList GetMissionsText(int missionNumber)
    {
        string ch = "Mission" + missionNumber.ToString();
        return (textValueMap["MissionData"] as PList)[ch] as PList;
    }

    public PList GetEnemy(string type)
    {
        return (mainValueMap["EnemyData"] as PList)[type] as PList;
    }

    void Init()
    {
        //isFileDownloaded = UserDefault::getInstance()->getBoolForKey("isFileDownloaded", 0);
        //isFileDownloaded = 0;
        //_currentGameSlot = UserDefault::getInstance()->getIntegerForKey("CurrentGameSlot", -100);
        //if (_currentGameSlot == -100)
        //{
        //    UserDefault::getInstance()->setIntegerForKey("CurrentGameSlot", -100);
        //}

//        if (ALLOW_DATA)
//        {

//            network::HttpRequest* request = new network::HttpRequest();
//#if Desktop
//        request->setUrl("http://www.werplay.com/devs/Explottens/GameData.plist");
//#else
//            request->setUrl("http://www.werplay.com/devs/Explottens/GameDataMobile.plist");
//#endif
//            request->setResponseCallback(CC_CALLBACK_2(GameData::onHttpRequestCompleted, this));
//            request->setRequestType(network::HttpRequest::Type::GET);
//            network::HttpClient::getInstance()->send(request);

//        }
//        else
//        {
//            Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(ResourceManager::EVENT_FINISHED);

//        }

        //setLanguage(UserDefault::getInstance()->getStringForKey("currentLanguage", Application::getInstance()->getCurrentLanguageCode()));

        //log("Application::getInstance()->getCurrentLanguageCode()::%s", Application::getInstance()->getCurrentLanguageCode());
    }

    public PList GetMenuData(string name)
    {
        return menuValueMap[name] as PList;
    }

    public PList GetTextData(string name)
    {
        return textValueMap[name] as PList;
    }

    public int CalculateItemsAvailableInShop(int coins)
    {
        const string kMachineGun = "WARMACHINE";
        const string kMultiCannon = "LOOSECANNON";
        const string kLaser = "LASER";
        const string kPlasmaCannon = "DEATOMIZER";
        const string kRocket = "ROCKET";
        //rear


        const string kFlameThrower = "INCINERATOR";
        const string kProtonCannon = "PROTONSHIELD";
        const string kBackFire = "BACKFIRE";

        //plane

        const string kplane1 = "TheRedDevil";
        const string kplane2 = "TheSuperSonic";
        const string kplane3 = "BubbleWrap";
        const string kplane4 = "Gravatron";
        const string kplane5 = "Shadowblitz";

        List<string> cat1 = new()
        {
            kMachineGun,
            kMultiCannon,
            kLaser,
            kPlasmaCannon,
            kRocket
        };

        List<string> cat2 = new()
        {
            kFlameThrower,
            kProtonCannon,
            kBackFire
        };

        List<string> cat3 = new()
        {
            kplane1,
            kplane2,
            kplane3,
            kplane4,
            kplane5
        };

        _numberOfItemsAvailableInShop = 0;
        int count = 1;
        foreach (string str in cat1)
        {
            int level = PlayerPrefs.GetInt(str);
            if (level < 5)
            {
                int price = (int)(((GetShop()["Category1"] as PList)["Gun" + count.ToString()] as PList)["Price"] as PList)["L" + (level + 1).ToString()];

                if (price < coins)
                {
                    _numberOfItemsAvailableInShop++;
                }
            }

            count++;
        }

        count = 1;

        foreach (string str in cat2)
        {
            int level = PlayerPrefs.GetInt(str);
            if (level < 5)
            {
                int price = (int)(((GetShop()["Category2"] as PList)["Gun" + count.ToString()] as PList)["Price"] as PList)["L" + (level + 1).ToString()];

                if (price < coins)
                {
                    _numberOfItemsAvailableInShop++;
                }
            }

            count++;
        }
        count = 1;

        foreach (string str in cat3)
        {
            int level = PlayerPrefs.GetInt(str);
            if (level < 5)
            {
                int price = (int)(((GetShop()["Category3"] as PList)["Gun" + count.ToString()] as PList)["Price"] as PList)["L" + (level + 1).ToString()];

                if (price < coins)
                {
                    _numberOfItemsAvailableInShop++;
                }
            }

            count++;
        }
        return _numberOfItemsAvailableInShop;
    }


    public int GetItemsAvailableInShop()
    {
        return _numberOfItemsAvailableInShop;
    }


    public int CalculateItemsAvailebleInSideKickShop(int coins)
    {
        const string k_GunKitty = "gunkitty";
        const string k_Rockteer = "rocketeer";
        const string k_Mechanic = "mechanic";
        const string k_SuicideKitty = "suicidekitty";
        const string k_Zapper = "zapper";
        const string k_Catswalker = "catwalker";


        List<string> cat1 = new List<string>();
        cat1.Add(k_GunKitty);
        cat1.Add(k_Rockteer);
        cat1.Add(k_Mechanic);
        cat1.Add(k_SuicideKitty);
        cat1.Add(k_Zapper);
        cat1.Add(k_Catswalker);



        _numberOfItemsAvailableInSideKickShop = 0;
        int count = 1;

        foreach (string str in cat1)
        {
            if (count < fileHandler.sideKickUnlocked + 1)
            {
                bool bought = PlayerPrefs.GetInt(str) == 1;
                if (!bought)
                {
                    int price = (int)(((GetShop()["Category5"] as PList)["Gun" + count.ToString()] as PList)["Price"] as PList)["L1"];

                    if (price < coins)
                    {
                        _numberOfItemsAvailableInSideKickShop++;
                    }
                }

            }
            count++;

        }

        return _numberOfItemsAvailableInSideKickShop;
    }

    public int GetItemsAvailableInSideKickShop()
    {
        return _numberOfItemsAvailableInSideKickShop;
    }



    public int CalculateShopItemsToBeUnlocked()
    {
        _numberOfShopItemTobeUnlocked = 0;


        int currentLevel = fileHandler.missionsCompleted;
        int levelOnLastVisit = fileHandler.lastLevelVisitingShop;
        for (int i = 1; i <= 3; i++)
        {
            for (int count = 1; count <= (int)(GetShop()["Category" + i.ToString()] as PList)["Total"]; count++)
            {
                int unlockLevel = (int)((GetShop()["Category" + i.ToString()] as PList)["Gun" + count.ToString()] as PList)["UnlockLevel"];
                if (unlockLevel > levelOnLastVisit && unlockLevel <= currentLevel)
                {
                    _numberOfShopItemTobeUnlocked++;
                }


            }
        }

        return _numberOfShopItemTobeUnlocked;
    }



    public int CalculateSkItemsToBeUnlocked()
    {
        _numberOfSkItemTobeUnlocked = 0;


        int currentLevel = fileHandler.missionsCompleted;
        int levelOnLastVisit = fileHandler.lastLevelVisitingShop;
        for (int i = 1; i <= 3; i++)
        {
            for (int count = 1; count <= (int)(GetShop()["Category" + i.ToString()] as PList)["Total"]; count++)
            {
                int unlockLevel = (int)((GetShop()["Category" + i.ToString()] as PList)["Gun" + count.ToString()] as PList)["UnlockLevel"];
                if (unlockLevel > levelOnLastVisit && unlockLevel < currentLevel)
                {
                    _numberOfSkItemTobeUnlocked++;
                }
            }
        }

        return _numberOfSkItemTobeUnlocked;

    }

    public void SetLanguage(string languageCode)
    {
        PlayerPrefs.SetString("currentLanguage", languageCode);
        {
            string localstr = "GameData";


            mainValueMap = new PList(localstr);
            _currentLangugeCode = languageCode;

            Globals.GAME_FONT = System.Convert.ToString((mainValueMap["UIFONT"] as PList)["GAME_FONT"]);
            Globals.GAME_BM_FONT = System.Convert.ToString((mainValueMap["UIFONT"] as PList)["GAME_BM_FONT"]);
            Globals.HEADING_BM_FONT = System.Convert.ToString((mainValueMap["UIFONT"] as PList)["HEADING_BM_FONT"]);
            if ((mainValueMap["UIFONT"] as PList).ContainsKey("VERTICAL_OFFSET"))
            {
                //Director::getInstance()->setLetterOffestY(mainValueMap.at("UIFONT").asValueMap().at("VERTICAL_OFFSET").asInt());
            }
        }

        //{
        //    string localstr = Globals.assetsPath + languageCode + "/GameMenus.plist";
        //    if (FileUtils::getInstance()->isFileExist(localstr))
        //    {
        //        menuValueMap = FileUtils::getInstance()->getValueMapFromFile(localstr);
        //    }
        //    else
        //    {

        //        menuValueMap = FileUtils::getInstance()->getValueMapFromFile("localization/en/GameMenus.plist");
        //        log("Couldn't find File ... using english");
        //    }
        //}
        //{
        //    stringlocalstr = "localization/" + languageCode + "/GameDataText.plist";
        //    log("local String %s", localstr.c_str());
        //    if (FileUtils::getInstance()->isFileExist(localstr))
        //    {

        //        textValueMap = FileUtils::getInstance()->getValueMapFromFile(localstr);
        //        log("Found File");

        //    }
        //    else
        //    {

        //        textValueMap = FileUtils::getInstance()->getValueMapFromFile("localization/en/GameDataText.plist");
        //        log("Couldn't find File ... using english");
        //    }
        //    GAME_FONT = textValueMap.at("UIFONT").asValueMap().at("GAME_FONT").asString();
        //    GAME_BM_FONT = textValueMap.at("UIFONT").asValueMap().at("GAME_BM_FONT").asString();
        //    HEADING_BM_FONT = textValueMap.at("UIFONT").asValueMap().at("HEADING_BM_FONT").asString();
        //    if (!textValueMap.at("UIFONT").asValueMap()["VERTICAL_OFFSET"].isNull())
        //    {
        //        Director::getInstance()->setLetterOffestY(textValueMap.at("UIFONT").asValueMap().at("VERTICAL_OFFSET").asInt());
        //    }
        //}
    }

    string GetStringFromLanguageCode(string languageCode)
    {
        return "";
    }


}
