using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using DG.Tweening;
using UnityEngine.UI;
public class SettingsAccessiblityMenu : MonoBehaviour
{


    [SerializeField] private Image bg;
    [SerializeField] private TextMeshProUGUI lbl = null;
    [SerializeField] private TextMesh<PERSON><PERSON><PERSON>G<PERSON> screenShakeLabel = null;
    [SerializeField] private TextMeshP<PERSON>UGUI reducedExplosionsLabel = null;
    [SerializeField] private TextMeshProUGUI aimAssistLabel = null;
    [SerializeField] private TextMeshProUGUI hightContrastLabel = null;
    [SerializeField] private TextMeshProUGUI colorBlindLabel = null;
    [SerializeField] private TextMeshProUGUI hapticFeedbackLabel = null;
    [SerializeField] private TextMeshProUGUI increasedFontSizeLabel = null;
    [SerializeField] private TextMeshProUGUI infiniteGunEnergyLabel = null;
    [SerializeField] private TextMeshProUG<PERSON> staticDpadLabel = null;
    [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> notificationsLabel = null;
    [SerializeField] private Image displayArea = null;
    [SerializeField] private Canvas canvas;


    [SerializeField] private CustomButton screenShakeButton = null;
    [SerializeField] private CustomButton reducedExplosionsButton = null;
    [SerializeField] private CustomButton aimAssistButton = null;
    [SerializeField] private CustomButton hightContrastButton = null;
    [SerializeField] private CustomButton colorBlindButton = null;
    [SerializeField] private CustomButton hapticFeedbackButton = null;
    [SerializeField] private CustomButton increasedFontSizeButton = null;
    [SerializeField] private CustomButton infiniteGunEnergyButton = null;
    [SerializeField] private CustomButton staticDpadButton = null;
    [SerializeField] private CustomButton notificationsButton = null;

    [SerializeField] private PopUp popUp;

    private List<CustomButton> menuArray;
    private int currentSelected;
    private bool lockToolTipVisible = false;
    private Vector2 winSize;

    Sequence seq;

    private void Start()
    {
        Init();
    }

    private void Init()
    {
        

        const float newOffset = 20;


        const float scale = 1.0f;


        
        
        float fontSize = 40;
        //if (GameData.instance.getCurrentLanguageCode() == "de" || GameData::getInstance().getCurrentLanguageCode() == "nl")
        //{
        //    fontSize = 37;
        //}

        {
            lbl.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["accessibility"] as string;//, GAMEFONT, fontSize);


        }
        CreateLabels();
        CreateKeyBoardAndControllerSupport();
        currentSelected = 0;

    }
    private void onExit()
    {

    }

    private void Update()
    {

    }

    private void CreateLabels()
    {
        screenShakeLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["screenShake"] as string;// GAMEFONT, 36);
        //Shared::fontToCustom(screenShakeLabel);
        //screenShakeLabel.gameObject.SetActive(false);
        //screenShakeLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        


        aimAssistLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["aimAssist"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(aimAssistLabel);
        //aimAssistLabel.gameObject.SetActive(false);
        //aimAssistLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
#if UNITY_STANDALONE
        if(!Globals.isJoystickConnected)
        {
            aimAssistLabel.color=Color.grey;
        }
#endif

        staticDpadLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["staticDpads"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(staticDpadLabel);
        //staticDpadLabel.gameObject.SetActive(false);
       // bg.addChild(staticDpadLabel);
        //staticDpadLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
#if UNITY_STANDALONE
    staticDpadLabel.color = Color.grey;
#endif
        notificationsLabel.text = GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationHeading"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(notificationsLabel);
        //notificationsLabel.gameObject.SetActive(false);
        //notificationsLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));



        reducedExplosionsLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["reducedExplosions"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(reducedExplosionsLabel);
        //reducedExplosionsLabel.gameObject.SetActive(false);
        //reducedExplosionsLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        reducedExplosionsLabel.color =Color.grey;
        reducedExplosionsButton.SetInteractable(false);

        hightContrastLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["highContrast"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(hightContrastLabel);
        //hightContrastLabel.gameObject.SetActive(false);
        //hightContrastLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        hightContrastLabel.color =Color.grey;
        hightContrastButton.SetInteractable(false);

        colorBlindLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["colorBlind"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(colorBlindLabel);
        //colorBlindLabel.gameObject.SetActive(false);
        //colorBlindLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        colorBlindLabel.color =Color.grey;
        colorBlindButton.SetInteractable(false);

        hapticFeedbackLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["hapticFeedback"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(hapticFeedbackLabel);
        //hapticFeedbackLabel.gameObject.SetActive(false);
        //hapticFeedbackLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        hapticFeedbackLabel.color =Color.grey;
        hapticFeedbackButton.SetInteractable(false);

        float fontSizeFontLabel = 36;
        //if (GameData.instance.GetCurrentLanguageCode() == "it")
        //{
        //    fontSizeFontLabel = 27;
        //}

        increasedFontSizeLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["increasedFontSize"] as string;//, GAMEFONT, fontSizeFontLabel);
        //Shared::fontToCustom(increasedFontSizeLabel);
        //increasedFontSizeLabel.gameObject.SetActive(false);
        //increasedFontSizeLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        increasedFontSizeLabel.color =Color.grey;
        increasedFontSizeButton.SetInteractable(false);


        infiniteGunEnergyLabel.text = GameData.instance.GetMenuData(Globals.ACCESSIBILITY)["infiniteGunEnergy"] as string;//, GAMEFONT, 36);
        //Shared::fontToCustom(infiniteGunEnergyLabel);
        //infiniteGunEnergyLabel.gameObject.SetActive(false);
        //infiniteGunEnergyLabel.runAction(Sequence::create(DelayTime::create(0.15f), Show::create(), NULL));
        infiniteGunEnergyLabel.color =Color.grey;
        infiniteGunEnergyButton.SetInteractable(false);

        //if (GameData.instancegetCurrentLanguageCode() == "zh-Hans" || GameData.instancegetCurrentLanguageCode() == "zh-Hant")

        //{
        //    Shared::alignItemsVerticallyWithPadding(Point(270, -20), 13, labelArray, false);

        //}
        //else if (GameData.instancegetCurrentLanguageCode() == "ar")
        //{
        //    Shared::alignItemsVerticallyWithPadding(Point(270, -12), 17, labelArray, false);

        //}
        //else if (GameData.instancegetCurrentLanguageCode() == "ko")
        //{
        //    Shared::alignItemsVerticallyWithPadding(Point(270, -15), 30, labelArray, false);

        //}
        //else if (GameData.instancegetCurrentLanguageCode() == "ja")
        //{
        //    Shared::alignItemsVerticallyWithPadding(Point(270, -26), 13, labelArray, false);

        //}
        //else
        //{
        //    Shared::alignItemsVerticallyWithPadding(Point(270, -31), 31, labelArray, false);
        //}
        Vector2 buttonSize = new Vector2(290, 105);
        const float fontSizeLabel = 67;


        {//static dpads   On
            List<TextMeshProUGUI> allLabels = new List<TextMeshProUGUI>();

            staticDpadButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            allLabels.Add(staticDpadButton.defaultLabel);
            //log("ON: %d", lbl.getStringLength());

            staticDpadButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            allLabels.Add(staticDpadButton.offLabel);

            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);


            staticDpadButton.defaultAction = ()=>
            {
                Globals.fixDpadsPosition = false;
                PlayerPrefs.SetInt("fixDpadsPosition", 0);

                staticDpadButton.SetIsOn(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

            staticDpadButton.offAction = ()=>{
                Globals.fixDpadsPosition = true;
                PlayerPrefs.SetInt("fixDpadsPosition", 1);
                staticDpadButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };
            if (PlayerPrefs.GetInt("fixDpadsPosition")==1)
            {
                staticDpadButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

            }
            else
            {
                staticDpadButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }
#if UNITY_STANDALONE
        //onButton.setEnabled(false);
        //offButton.setEnabled(false);
            staticDpadButton.SetInteractable(false);
#endif
        }
        {//screen shake  On

            screenShakeButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            screenShakeButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //allLabels.clear();
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(screenShakeLabel.getPosition().x + 350, screenShakeLabel.getPosition().y));


            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);


            //offButton.setPosition(Vec2(screenShakeLabel.getPosition().x + 350, screenShakeLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            screenShakeButton.defaultAction=()=>{
                Globals.screenShakeEnabled = false;
                PlayerPrefs.SetInt("screenShakeEnabled", 0);
                //UserDefault::getInstance().flush();

                screenShakeButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            screenShakeButton.offAction = ()=>{
                Globals.screenShakeEnabled = true;
                PlayerPrefs.SetInt("screenShakeEnabled", 1);
                //UserDefault::getInstance().flush();

                screenShakeButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

            if (PlayerPrefs.GetInt("screenShakeEnabled")==1)
            {
                screenShakeButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

            }
            else
            {
                screenShakeButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }
        }
        {//aim assist   On
            aimAssistButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            aimAssistButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);
            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(aimAssistLabel.getPosition().x + 350, aimAssistLabel.getPosition().y));

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);



            //offButton.setPosition(Vec2(aimAssistLabel.getPosition().x + 350, aimAssistLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            aimAssistButton.defaultAction=()=>{
                Globals.isAssistMode = false;
                PlayerPrefs.SetInt("isAssistMode", 0);
                //UserDefault::getInstance().flush();

                aimAssistButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            aimAssistButton.offAction=()=>{
                Globals.isAssistMode = true;
                PlayerPrefs.SetInt("isAssistMode", 1);
                //UserDefault::getInstance().flush();
                aimAssistButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

            if (PlayerPrefs.GetInt("isAssistMode")==1)
            {
                aimAssistButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

            }
            else
            {

                aimAssistButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }


#if UNITY_STANDALONE
        if(!Globals.isJoystickConnected)
            {
                aimAssistButton.SetInteractable(false);
            }
#endif

        }
        {
            //notifications   On

            notificationsButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            notificationsButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);
            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(notificationsLabel.getPosition().x + 350, notificationsLabel.getPosition().y));

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);



            //offButton.setPosition(Vec2(notificationsLabel.getPosition().x + 350, notificationsLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            notificationsButton.defaultAction=()=>{
                // add here
                //TODO
                popUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationHeading"] as string, GameData.instance.GetMenuData(Globals.NOTIFICATION)["blockNotificationText1"] as string + "\n" + GameData.instance.GetMenuData(Globals.NOTIFICATION)["blockNotificationText2"] as string, ()=>{
                    //ThirdPartyInterface::UnregisterForNotifications();
                    //offButton.setVisible(true);
                    //offButton.setEnabled(true);
                    //onButton.gameObject.SetActive(false);
                    PlayerPrefs.SetInt("notificationsEnabled", 0);
                    notificationsButton.SetIsOn(false);
                    //DOTween.Sequence.
                    //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                    //    offButton.setEnabled(true);
                    //}), NULL));
                });
                //Director::getInstance().getRunningScene().addChild(p, INTMAX);

                //offButton.onTouchUp();

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            notificationsButton.offAction=()=>{
                // add here
                popUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationHeading"] as string, (GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationText1"] as string + "\n" + GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationText2"] as string), ()=>{
                //ThirdPartyInterface::registerForNotifications();
                notificationsButton.SetIsOn(true);
                    //onButton.setVisible(true);
                    //onButton.setEnabled(true);
                    //offButton.gameObject.SetActive(false);
                    PlayerPrefs.SetInt("notificationsEnabled", 1);
                    //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                    //    onButton.setEnabled(true);
                    //}), NULL));


                });
                //Director::getInstance().getRunningScene().addChild(p, INTMAX);
                //onButton.onTouchUp();
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

            if (PlayerPrefs.GetInt("notificationsEnabled")==1)
            {
                notificationsButton.SetIsOn(true);
                //onButton.setVisible(true);
                //onButton.setEnabled(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

            }
            else
            {
                notificationsButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.setEnabled(true);
            }



        }
        {//reduced explosions   On

            reducedExplosionsButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            reducedExplosionsButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(reducedExplosionsLabel.getPosition().x + 350, reducedExplosionsLabel.getPosition().y));
            //onButton.setEnabled(false);


            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);
            //offButton.setEnabled(false);



            //offButton.setPosition(Vec2(reducedExplosionsLabel.getPosition().x + 350, reducedExplosionsLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            reducedExplosionsButton.defaultAction=()=>{
                reducedExplosionsButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            reducedExplosionsButton.offAction=()=> {
                reducedExplosionsButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

        }
        {//haptic feedback   On
            hapticFeedbackButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            hapticFeedbackButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(hapticFeedbackLabel.getPosition().x + 350, hapticFeedbackLabel.getPosition().y));
            //onButton.setEnabled(false);


            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);


            //offButton.setPosition(Vec2(hapticFeedbackLabel.getPosition().x + 350, hapticFeedbackLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            hapticFeedbackButton.defaultAction=()=>{
                hapticFeedbackButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };
            //offButton.setEnabled(false);

            hapticFeedbackButton.offAction=()=>{

                hapticFeedbackButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };

        }
        {//high contrast   On
            hightContrastButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            hightContrastButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(hightContrastLabel.getPosition().x + 350, hightContrastLabel.getPosition().y));
            //onButton.setEnabled(false);

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);
            //offButton.setEnabled(false);

            //offButton.setPosition(Vec2(hightContrastLabel.getPosition().x + 350, hightContrastLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            hightContrastButton.defaultAction=()=>{
                hightContrastButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            hightContrastButton.offAction=()=>{
                hightContrastButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };
        }
        {//infifnite gun energy   On
            infiniteGunEnergyButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            infiniteGunEnergyButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(infiniteGunEnergyLabel.getPosition().x + 350, infiniteGunEnergyLabel.getPosition().y));
            //onButton.setEnabled(false);

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);
            //offButton.setEnabled(false);



            //offButton.setPosition(Vec2(infiniteGunEnergyLabel.getPosition().x + 350, infiniteGunEnergyLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            infiniteGunEnergyButton.defaultAction=()=>{
                infiniteGunEnergyButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            infiniteGunEnergyButton.offAction = ()=> {
                infiniteGunEnergyButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };
        }
        {//increased font size  On

            increasedFontSizeButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            increasedFontSizeButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(increasedFontSizeLabel.getPosition().x + 350, increasedFontSizeLabel.getPosition().y));
            //onButton.setEnabled(false);

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);


            //offButton.setPosition(Vec2(increasedFontSizeLabel.getPosition().x + 350, increasedFontSizeLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            increasedFontSizeButton.defaultAction=()=>{
                increasedFontSizeButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            increasedFontSizeButton.offAction=()=>{
                increasedFontSizeButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };
            //offButton.setEnabled(false);

        }
        {//color blind  On
            colorBlindButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl);
            //allLabels.Add(lbl);
            //log("ON: %d", lbl.getStringLength());

            colorBlindButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;//, GAMEFONT, fontSizeLabel);
            //Shared::fontToCustom(lbl2);
            //log("OFF: %d", lbl2.getStringLength());
            //allLabels.Add(lbl2);

            //int a = Shared::getBiggestLength(allLabels);
            //int buttonWidth = 65;
            //if (GameData.instancegetCurrentLanguageCode() == "ko")
            //{
            //    buttonWidth = 92;
            //}

            //CustomButton* onButton;
            //CustomButton* offButton;
            //float onbuttonWidth = clampf(a * buttonWidth, 320, 500);
            //log("%f", onbuttonWidth);

            //onButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::GREEN, nullptr);
            //onButton.setContentSize(cocos2d::Size(onbuttonWidth, buttonSize.height));
            //onButton.addToButton(lbl, false);
            //lbl.setPositionY(lbl.getPosition().y + lbl.getPosition().y * 0.2f);

            //onButton.setScale(0.45);
            //bg.addChild(onButton);
            //onButton.setPosition(Vec2(colorBlindLabel.getPosition().x + 350, colorBlindLabel.getPosition().y));
            //onButton.setEnabled(false);

            //offButton = CustomButton::createWithCustomButton(SPRITES::SQUARE, COLORS::RED, nullptr);
            //offButton.gameObject.SetActive(false);
            //float offbuttonWidth = clampf(a * buttonWidth, 320, 500);

            //offButton.setContentSize(cocos2d::Size(offbuttonWidth, buttonSize.height));
            //offButton.addToButton(lbl2, false);
            //lbl2.setPositionY(lbl2.getPosition().y + lbl2.getPosition().y * 0.2f);

            //offButton.setScale(0.45);
            //bg.addChild(offButton);
            //offButton.setEnabled(false);


            //offButton.setPosition(Vec2(colorBlindLabel.getPosition().x + 350, colorBlindLabel.getPosition().y));
            //menuArray.Add(onButton);
            //menuArray.Add(offButton);
            colorBlindButton.defaultAction=()=>{
                colorBlindButton.SetIsOn(false);
                //onButton.gameObject.SetActive(false);
                //onButton.setEnabled(false);
                //offButton.setVisible(true);
                //offButton.onTouchUp();
                //onButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    offButton.setEnabled(true);
                //}), NULL));


                ///
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            };

            colorBlindButton.offAction=()=>{
                //colorBlindButton.SetIsOn(true);
                //offButton.gameObject.SetActive(false);
                //offButton.setEnabled(false);

                //onButton.setVisible(true);
                //onButton.onTouchUp();
                //offButton.runAction(Sequence::create(DelayTime::create(0.05f), CallFunc::create([=](){
                //    onButton.setEnabled(true);
                //}), NULL));

                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            };
        }


    }

    void CreateKeyBoardAndControllerSupport()
    {

    }


}
