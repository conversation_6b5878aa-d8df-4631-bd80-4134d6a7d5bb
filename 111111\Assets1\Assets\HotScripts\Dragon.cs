using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
using System;

public class Dragon : Enemy
{
    public enum DragonPosition
    {
        LEFT,
        RIGHT
    };

    private enum DragonStates
    {
        FLYING,
        ATTACKING
    };

    #region REFERENCES
    public DragonPosition _dragonPosition = DragonPosition.RIGHT;
    DragonStates _dState = DragonStates.FLYING;
    Bone _bone = null;
    Bounds _bounds1,bounds2, bounds3, bounds4, bounds5, bounds6, bounds7, bounds8, bounds9;
    StateMachine _dragonStateMachine = null;
    [SerializeField] private Sprite bulletSprite;
    
    [SerializeField] Collider2D topColl, coll1, coll2, coll3, coll4, coll5, coll6, coll7, lowerColl;
    private Animator flameThrowerAnimator;
    private GameObject purganaAttackBullet;
    [SerializeField] GameObject flameThrowerPrefab;
    #endregion

    #region VARIABLES
    private const float ENEMY_SPRITE_SCALE = 1.0f;
    private string _skinName = "dragonBlue";
    public Vector2 _initialPosition;
    public bool _killMode = false;
    private bool _allowChangeLocations = false;
    private bool _isDead = false;
    public bool IsDead { get { return _isDead; } set { _isDead = value; } }
    public const string TWEEN_ID = "Dragon", SCHEDULAR_ID = "DragonSchedular";
    #endregion

    public void setAllowChangeLocations(bool val) { _allowChangeLocations = val; }

    public override void Init()
    {
        if(initialized)
            return;

        base.Init();
        InitStateMachine();
        InitStats();
        
        allowRelocate = false;
        enemySchedulerSpeed = 0;
        _bounds1 = topColl.bounds;
        bounds2 = coll1.bounds;
        bounds3 = coll2.bounds;
        bounds4 = coll3.bounds;
        bounds5 = coll4.bounds;
        bounds6 = coll5.bounds;
        bounds7 = coll6.bounds;
        bounds8 = coll7.bounds;
        bounds9 = lowerColl.bounds;
        //code here
        SetAnimationMix();
        InitializeEnemyParams();

        DOTween.Sequence().SetId(SCHEDULAR_ID).AppendInterval(1.0f).AppendCallback(() =>
        {
            UpdateBounds();
        }).SetLoops(-1);

        DOTween.Sequence().SetId(SCHEDULAR_ID).AppendInterval(3.0f).AppendCallback(() =>
        {
            CheckPurganaAttack();
        }).SetLoops(-1);

        //InvokeRepeating(nameof(UpdateBounds), 0, 1.0f);
        //InvokeRepeating(nameof(CheckPurganaAttack), 0, 3.0f);
        scheduleUpdate = true;
        //this->scheduleUpdate(); todo
    }

    private void InitStateMachine()
    {
        _dragonStateMachine = DragonStateMachine.Create(this);
        _dragonStateMachine.AddState<DragonStateIdle>();
        _dragonStateMachine.AddState<DragonStateFly>();
        _dragonStateMachine.AddState<DragonStateAttack>();
        _dragonStateMachine.AddState<DragonStateDeath>();
    }

    private void SetAnimationMix()
    {
        enemySprite.state.Data.SetMix("idle", "fly", 0.5f);
        enemySprite.state.Data.SetMix("fly", "idle", 0.5f);
        enemySprite.state.Data.SetMix("idle", "attack", 0.5f);

        enemySprite.state.Data.SetMix("attack", "fly", 0.5f);
        enemySprite.state.Data.SetMix("fly", "attack", 0.5f);

        enemySprite.state.Data.SetMix("attack", "shootUp", 0.35f);
        enemySprite.state.Data.SetMix("shootUp", "idle", 0.5f);

        enemySprite.state.Data.SetMix("fly", "attackAim", 0.35f);
        enemySprite.state.Data.SetMix("attackAim", "fly", 0.35f);
        enemySprite.state.Data.SetMix("attackAim", "attackAim", 0.15f);
    }

    private void InitializeEnemyParams()
    {
        transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1200), Globals.CocosToUnity(600));
        _initialPosition = transform.position;
        CreateWithSkin(_skinName);
        //enemySprite.skeleton.SetSkin(_skinName);
        enemySprite.gameObject.SetActive(false);
        InstantiateFlameThrower();
        enemySprite.state.Event += HandleSpineEvent;
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shootUp")
        {
            Shoot(true);
            //call shoot func here with true
        }

        if (spineEvent.Data.Name == "shootDown")
        {
            Shoot(false);
            //call shoot func here with false
        }

        if (spineEvent.Data.Name == "shootSky")
        {
            ShootUpEvent();
            //call shootUp func here 
        }

        if (spineEvent.Data.Name == "shootAim")
        {
            ShootAtYou();
            //call shootatYou func here 
        }

        if (spineEvent.Data.Name == "fireWater")
        {
            Observer.DispatchCustomEvent("RIVER_FIRE");
        }

        if (spineEvent.Data.Name == "startFire")
        {
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.startFire ,0.5f);
            //Shared::playSound("res/Sounds/Bosses/boss9/startFire.mp3", false, 0.5f);todo
        }

        if (spineEvent.Data.Name == "dragonAttack")
        {

            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.dragonAttack,1,transform.position);
            //TODO Distance Sound
            //Globals.PlaySound("res/Sounds/Bosses/boss9/dragonAttack.mp3", transform.position);
        }

        if (spineEvent.Data.Name == "purganaAttack")
        {
            PurganaAttack();
            //call purganaattack func here
        }
    }


    public void CreateWithSkin(string skinName)
    {
        enemySprite.skeleton.SetSkin(skinName);
        //set skin here 
    }
    
    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        int bossNumber = 9;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn);
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }

        isBoss = true;
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;










        //stats.speed = baseStats.speed = 2;
        //stats.health = baseStats.health = 35000;//35000
        //stats.turnSpeed = baseStats.turnSpeed = 2;
        //stats.bulletDamage = baseStats.bulletDamage = 200;
        //stats.regen = baseStats.regen = 0;
        //stats.xp = baseStats.xp = 50;
        //stats.coinAwarded = baseStats.coinAwarded = 10;
        //stats.missileDamage = baseStats.missileDamage = 4;
        //stats.maxHealth = baseStats.maxHealth = stats.health;
    }

    private void InstantiateFlameThrower()
    {
        purganaAttackBullet = Instantiate(flameThrowerPrefab);
        flameThrowerAnimator = purganaAttackBullet.transform.GetChild(0).GetComponent<Animator>();
        purganaAttackBullet.SetActive(false);
    }

    public void PurganaAttack()
    {
        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(null);
        bullet.duration = 1.5f;
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(250));
        _bone = enemySprite.skeleton.FindBone("purganaAttack");
        //bullet->setOpacity(1);

        bullet.transform.position = new Vector2(transform.position.x + transform.localScale.y * _bone.WorldX, transform.position.y + transform.localScale.y * _bone.WorldY);
        float angle = Globals.CalcAngle(player.transform.position, bullet.transform.position);
        bullet.gameObject.SetActive(true);
        Vector2 dest = new Vector2(Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (angle - 90)), Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (angle - 90)));
        
        bullet.transform.DOBlendableMoveBy(dest, bullet.duration).OnComplete(()=>
        {
            if(bullet.gameObject.activeSelf)
            {
                bullet.RemoveWithSplash();
            }
        });

        bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
        purganaAttackBullet.SetActive(true);
        purganaAttackBullet.transform.SetScale(3);
        purganaAttackBullet.transform.SetPositionAndRotation(new Vector2(bullet.transform.position.x - Globals.CocosToUnity(100), bullet.transform.position.y), Quaternion.Euler(0, 0, 180));
        flameThrowerAnimator.Play("flame4", 0, 1);

        //bullet.GetFlameThrower().gameObject.SetActive(true);
        //bullet.GetFlameThrower().Play("flame1", 0, 1);
        //bullet.GetFlameThrower().transform.SetScale(3);
        //bullet.GetFlameThrower().transform.SetPositionAndRotation(new Vector2(bullet.transform.GetComponent<SpriteRenderer>().size.x / 2 - Globals.CocosToUnity(100), bullet.transform.GetComponent<SpriteRenderer>().size.y / 2), Quaternion.Euler(0, 0, 180));
        bullet.setDamage(stats.bulletDamage);
        //bullet.PlayBulletAnim(bullet.duration, dest);
        enemySprite.timeScale = 0.8f;
        bullet.transform.SetRotation(angle);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    public void CheckPurganaAttack()
    {
        if(_isDead)
        {
            return;
        }
        if(_killMode)
        {
            return;
        }

        if (_dragonPosition == DragonPosition.RIGHT)
        {
            if (player.transform.position.x > transform.position.x - Globals.CocosToUnity(500))
            {
                enemySprite.state.SetAnimation(1, "purganaAttack", false);
            }
        }

        if (_dragonPosition == DragonPosition.LEFT)
        {
            if (player.transform.position.x < transform.position.x + Globals.CocosToUnity(500))
            {
                enemySprite.state.SetAnimation(1, "purganaAttack", false);
            }
        }
    }

    public void Shoot(bool isUp)
    {
        if (_isDead)
        {
            return;
        }

        if (_killMode)
        {
            return;
        }

        if (!healthBar)
        {
            //enemySprite->addChild(healthBar);
            healthBar.transform.SetLocalPositionY(-Globals.CocosToUnity(200));
            healthBar.gameObject.SetActive(false);
            healthBar.ScaleRatio = 2;
        }

        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.SetSpriteFrame(null);
        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            bullet.setDamage(stats.bulletDamage * 0.5f);
        }
        if (Globals.gameModeType == GamePlayMode.Medium)
        {
            bullet.setDamage(stats.bulletDamage);
        }
        if (Globals.gameModeType == GamePlayMode.Hard)
        {
            bullet.setDamage(stats.bulletDamage * 2.0f);
        }

        _bone = enemySprite.skeleton.FindBone("fire");
        bullet.gameObject.SetActive(true);
        bullet.duration = 12;
        bullet.transform.SetRotation(100);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(120));
        bullet.GetRedFireballTrail().transform.SetRotation(100 + 360);
        bullet.GetRedFireballTrail().gameObject.SetActive(true);
        bullet.GetRedFireballTrail().state.SetAnimation(0, "fireball", true);
        bullet.transform.position = new Vector2(transform.position.x + transform.localScale.y * _bone.WorldX, transform.position.y + transform.localScale.y * _bone.WorldY);
        enemySprite.state.TimeScale = 0.8f;
        bullet.isFireBallActive = true;
        
        if (enemySprite.skeleton.FindBone("root").ScaleX == -1)
        {
           bullet.moveFireballLeft = false;
        }

        if (!isUp)
        {
            bullet.transform.SetRotation(100 + 180);
            bullet.GetRedFireballTrail().transform.SetRotation(100 + 180);
        }
        bullet.ResetWithDelay();
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    //private IEnumerator Move(Bullet bullet)
    //{
    //    while (true)
    //    {
    //        bullet.transform.SetRotation(Globals.Modulus(bullet.transform.GetRotation(), 360));
    //        if (moveFireballLeft)
    //        {
    //            //bullet.transform.DOMoveX(bullet.transform.position.x - 5, bullet.duration);
    //            bullet.transform.SetWorldPositionX(bullet.transform.position.x - Globals.CocosToUnity(10));
    //            //bulletSprite->setPositionX(bulletSprite->getPosition().x - _speed);
    //        }
    //        else
    //        {
    //            //bullet.transform.DOMoveX(bullet.transform.position.x + 5, bullet.duration);
    //            bullet.transform.SetWorldPositionX(bullet.transform.position.x + Globals.CocosToUnity(10));
    //            //bulletSprite->setPositionX(bulletSprite->getPosition().x + _speed);
    //        }

    //        //bullet.transform.DOMoveY(bullet.transform.position.y + Globals.CocosToUnity(15) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), bullet.duration);
    //        bullet.transform.SetWorldPositionY(bullet.transform.position.y + Globals.CocosToUnity(20) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()));
    //        bullet.transform.SetRotation(bullet.transform.GetRotation() + 1.5f);

    //        bullet.GetRedFireballTrail().transform.position = bullet.transform.position;

    //        if (bullet.transform.GetRotation() > 180)
    //        {
    //            bullet.GetRedFireballTrail().transform.SetRotation(bullet.GetRedFireballTrail().transform.GetRotation() + (bullet.transform.GetRotation() + 180 - bullet.GetRedFireballTrail().transform.GetRotation()) * Time.deltaTime * 3);
    //        }
    //        else
    //        {
    //            bullet.GetRedFireballTrail().transform.SetRotation(bullet.GetRedFireballTrail().transform.GetRotation() + ((-bullet.transform.GetRotation() + 180 + 360) - bullet.GetRedFireballTrail().transform.GetRotation()) * Time.deltaTime * 3);
    //        }
    //        yield return null;
    //    }
    //}

    
    public void ShootUp()
    {
        if (_isDead)
            return;
        if (_killMode)
            return;

        if (!healthBar)
        {
            healthBar.transform.SetLocalPositionY(-Globals.CocosToUnity(200));
            healthBar.gameObject.SetActive(false);
            healthBar.ScaleRatio = 2;
        }

        enemySprite.state.SetAnimation(0, "shootUp", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "attack", true);
    }

    public void ShootUpEvent()
    {
        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage * 4);
        //bullet->setSpriteFrame("bulletEnemy.png");
        bullet.transform.SetScale(0.5f);
        //bullet->setOpacity(1);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(220));
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
        _bone = enemySprite.skeleton.FindBone("fire");

        bullet.transform.position = new Vector2(transform.position.x + transform.localScale.y * _bone.WorldX, transform.position.y + transform.localScale.y * _bone.WorldY);
        bullet.duration = 5;
        bullet.gameObject.SetActive(true);
        Vector2 dest;
        if (_dragonPosition == DragonPosition.RIGHT)
        {
            dest = new Vector2(-Globals.CocosToUnity(500), Globals.CocosToUnity(1500));
            //bullet->runAction(Sequence::create(MoveBy::create(1, cocos2d::Point(-500, 1500)), NULL));
        }
        else
        {
            dest = new Vector2(Globals.CocosToUnity(500), Globals.CocosToUnity(1500));
            //bullet->runAction(Sequence::create(MoveBy::create(1, cocos2d::Point(500, 1500)), NULL));

        }
        bullet.GetRedFireballTrail().gameObject.SetActive(true);
        bullet.GetRedFireballTrail().state.SetAnimation(0, "fireball", true);
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
        bullet.transform.DOBlendableMoveBy(dest, 1).OnComplete(()=>
        {
            if(bullet.gameObject.activeSelf)
            {
                bullet.RemoveWithSplash();
            }
        });
        //bullet.PlayBulletAnim(1, dest);
        bullet.transform.DOScale(3, 0.25f);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    public void EntryFly()
    {
        enemySprite.gameObject.SetActive(true);
        enemySprite.skeleton.SetSkin(_skinName);
        enemySprite.state.SetAnimation(0, "flyUp", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "fireWater", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "attack", true);

        var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();
        bossHud._enemy = this;
        bossHud.SetSkin("Purgana1");
    }

    public void Entry()
    {
        enemySprite.gameObject.SetActive(true);
        enemySprite.state.SetAnimation(0, "flyUp", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "attack", true);
    }

    public void EnterKillMode()
    {
        _killMode = true;
        if (_dragonStateMachine == null)
        {
            InitStateMachine();
        }
        _dragonStateMachine.SetState<DragonStateFly>();
        //_dragonStateMachine.changeState(new DragonStateFly(enemySprite));

        if (!healthBar)
        {
            healthBar.transform.SetLocalPositionY(-Globals.CocosToUnity(200));
            healthBar.gameObject.SetActive(false);
            healthBar.ScaleRatio = 2;
        }
    }

    public void UpdateBounds()
    {
        _bounds1 = topColl.bounds;
        bounds2 = coll1.bounds;
        bounds3 = coll2.bounds;
        bounds4 = coll3.bounds;
        bounds5 = coll4.bounds;
        bounds6 = coll5.bounds;
        bounds7 = coll6.bounds;
        bounds8 = coll7.bounds;
        bounds9 = lowerColl.bounds;

        if (_allowChangeLocations == false)
        {
            return;
        }
        if (_killMode)
        {
            return;
        }

        if (_dragonPosition == DragonPosition.RIGHT)
        {
            if (player.transform.position.x > transform.position.x)
            {
                MoveLeft();
            }
        }
        else if (_dragonPosition == DragonPosition.LEFT)
        {
            if (player.transform.position.x < transform.position.x)
            {
                MoveRight();
            }
        }
    }

    public void ShootAtYou()
    {
        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage * 1.5f);
        bullet.duration = 1;
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(200));
        _bone = enemySprite.skeleton.FindBone("fire");
        //bullet->setOpacity(1);
        bullet.transform.SetRotation(100);
        bullet.transform.SetPositionAndRotation(new Vector2(transform.position.x + transform.localScale.y * _bone.WorldX, transform.position.y + transform.localScale.y * _bone.WorldY),Quaternion.Euler(0,0,100));
        float angle = Globals.CalcAngle(player.transform.position, bullet.transform.position);
        Vector2 dest = new Vector2(Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (angle - 90)), Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (angle - 90)));
        bullet.PlayBulletAnim(1, dest);
        //bullet->runAction(Sequence::create(MoveBy::create(1, cocos2d::Point(3000 * (sinf(CC_DEGREES_TO_RADIANS(angle - 90))), 3000 * (cosf(CC_DEGREES_TO_RADIANS(angle - 90))))), NULL));
        enemySprite.timeScale = 0.8f;
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);

        bullet.GetRedFireballTrail().gameObject.SetActive(true);
        bullet.transform.DOScale(2, 0.25f);
        bullet.GetRedFireballTrail().state.SetAnimation(0, "fireball", true);
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
    }

    public void MoveLeft()
    {
        _dragonPosition = DragonPosition.LEFT;
        enemySprite.state.SetAnimation(0, "fly", true);
        DOTween.Sequence().Append(transform.DOMove(new Vector2(_initialPosition.x - Globals.CocosToUnity(DragonManager.GROUND_WIDTH) + Globals.CocosToUnity(200), _initialPosition.y), 2.0f)).OnComplete(()=>
        {
            enemySprite.state.SetAnimation(0, "idle", false);
            enemySprite.state.SetAnimation(0, "attack", true);

            enemySprite.skeleton.FindBone("root").ScaleX = -1;
        });
    }

    public void MoveRight()
    {
        _dragonPosition = DragonPosition.RIGHT;
        enemySprite.state.SetAnimation(0, "fly", true);
        DOTween.Sequence().Append(transform.DOMove(new Vector2(_initialPosition.x , _initialPosition.y), 2.0f)).OnComplete(() =>
        {
            enemySprite.state.SetAnimation(0, "idle", false);
            enemySprite.state.SetAnimation(0, "attack", true);
            enemySprite.skeleton.FindBone("root").ScaleX = 1;
        });
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (Vector2.Distance(transform.position, P1) < Globals.CocosToUnity(400))
        {
            if (_bounds1.Contains(P1) || bounds2.Contains(P1) || bounds3.Contains(P1) || bounds4.Contains(P1) || bounds5.Contains(P1) || bounds6.Contains(P1) || bounds7.Contains(P1) || bounds8.Contains(P1) || bounds9.Contains(P1))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    private void Update()
    {
        if (_killMode)
        {
            _dragonStateMachine.UpdateWithDeltaTime();
            
            //if(_dragonStateMachine.GetState().GetStateType()=="attack")
            //{
                _bone = enemySprite.skeleton.FindBone("mouthTop");
            //}

            float angle = Globals.CalcAngle(player.transform.position, new Vector2(transform.position.x + _bone.WorldX, transform.position.y + _bone.WorldY)) + 180;
            float firstAngle = angle * -1;
            float secondAngle = angle + 180;

            if(enemySprite.skeleton.FindBone("root").ScaleX>0)
            {
                if (_bone.Rotation - firstAngle > 180)
                {
                    _bone.Rotation += -360;
                }
                if (_bone.Rotation - firstAngle < -180)
                {
                    _bone.Rotation += 360;
                }

                _bone.Rotation = _bone.Rotation + (-_bone.Rotation + (firstAngle)) * Time.deltaTime * 3.0f;
            }
           
            else
            {
                if (_bone.Rotation - secondAngle > 180)
                {
                    _bone.Rotation += -360;
                }
                if (_bone.Rotation - secondAngle < -180)
                {
                    _bone.Rotation += 360;
                }
                _bone.Rotation = _bone.Rotation + (-_bone.Rotation + (secondAngle)) * Time.deltaTime * 3.0f;
            }
        }
    }

    public override void Destroy()
    {
        enemySprite.state.SetAnimation(0, "death", false);

        if (enemySprite.skeleton.FindBone("root").ScaleX<0)
        {
            enemySprite.skeleton.FindBone("dragonFire").ScaleX = -enemySprite.skeleton.FindBone("dragonFire").ScaleX;
            enemySprite.skeleton.FindBone("dragonFire2").ScaleX = -enemySprite.skeleton.FindBone("dragonFire2").ScaleX;
        }

        enemySprite.state.AddAnimation(0, "idleStop", false);
        _dragonStateMachine.SetState<DragonStateDeath>();
        DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
        {
            enemySprite.gameObject.SetActive(false);
        });
        
        //_dragonManager.DragonDead();
        
        //scheduleUpdate = false;
        //DOTween.Kill(gameObject);

        _isDead = true;
        //GameSharedData.Instance.enemyList.Remove(this);
        //GameSharedData::getInstance()->g_enemyArray.eraseObject(this);
    }
}

public class DragonStateMachine : StateMachine
{
    public Dragon _dragon;

    DragonStateMachine(Dragon d)
    {
        _dragon = d;
    }

    public static DragonStateMachine Create(Dragon d)
    {
        DragonStateMachine ssm = new DragonStateMachine(d);

        return ssm;
    }
}

class DragonStateIdle : State
{
    DragonStateMachine dragonStateMachine;
    Dragon dragon;

    void Init()
    {
        dragonStateMachine = stateMachine as DragonStateMachine;
        dragon = dragonStateMachine._dragon;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (dragonStateMachine == null)
            Init();
    }

    public override void UpdateState()
    {
    }

    public override string GetStateType()
    {
        return "idle";
    }
}

public class DragonStateFly: State
{
    DragonStateMachine dragonStateMachine;
    Dragon dragon;
    PlayerController player;

    void Init()
    {
        dragonStateMachine = stateMachine as DragonStateMachine;
        dragon = dragonStateMachine._dragon;
        player = GameManager.instance.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (dragonStateMachine == null)
            Init();

        //dragon.enemySprite.state.SetAnimation(0, "fly", true);
    }

    public override void UpdateState()
    {
        const float DRAGON_SPEED = 5;
        if (player.transform.position.x - dragon.transform.position.x < -Globals.CocosToUnity(700))
        {
            dragon.transform.SetWorldPositionX(dragon.transform.position.x - DRAGON_SPEED * Time.deltaTime * 5);
        }

        else if (player.transform.position.x - dragon.transform.position.x > Globals.CocosToUnity(700))
        {
            dragon.transform.SetWorldPositionX(dragon.transform.position.x + DRAGON_SPEED * Time.deltaTime * 5);
        }
        else
        {
            dragonStateMachine.SetState<DragonStateAttack>();
        }

        if (player.transform.position.x - dragon.transform.position.x < -Globals.CocosToUnity(1300))
        {
            dragon.transform.SetWorldPositionX(dragon.transform.position.x - DRAGON_SPEED / 3 * Time.deltaTime * 5);
        }

        else if (player.transform.position.x - dragon.transform.position.x > Globals.CocosToUnity(1300))
        {
            dragon.transform.SetWorldPositionX(dragon.transform.position.x + DRAGON_SPEED / 3 * Time.deltaTime * 5);
        }

    }

    public override string GetStateType()
    {
        return "fly";
    }

}

class DragonStateAttack: State
{
    DragonStateMachine dragonStateMachine;
    Dragon dragon;
    PlayerController player;

    void Init()
    {
        dragonStateMachine = stateMachine as DragonStateMachine;
        dragon = dragonStateMachine._dragon;
        player = GameManager.instance.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (dragonStateMachine == null)
            Init();

        dragon.enemySprite.state.SetAnimation(0, "attackAim", true);
    }

    public override void UpdateState()
    {
        if (player.transform.position.x - dragon.transform.position.x < -Globals.CocosToUnity(1600))
        {
            dragonStateMachine.SetState<DragonStateFly>();
        }

        else if (player.transform.position.x - dragon.transform.position.x > Globals.CocosToUnity(1600))
        {
            dragonStateMachine.SetState<DragonStateFly>();
        }

        if (player.transform.position.x < dragon.transform.position.x)
        {
            dragon.enemySprite.skeleton.FindBone("root").ScaleX = 1;
        }
        else
        {
            dragon.enemySprite.skeleton.FindBone("root").ScaleX = -1;
        }
    }

    public override string GetStateType()
    {
        return "attack";
    }
}

class DragonStateDeath : State
{
    DragonStateMachine dragonStateMachine;
    Dragon dragon;

    void Init()
    {
        dragonStateMachine = stateMachine as DragonStateMachine;
        dragon = dragonStateMachine._dragon;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (dragonStateMachine == null)
            Init();
    }

    public override void UpdateState()
    {
    }

    public override string GetStateType()
    {
        return "death";
    }
}
