using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
public class WorldScrollLayerController : MonoBehaviour
{
    public DG.Tweening.Sequence mapSeq;
    public DG.Tweening.Sequence waterSeq;
    [SerializeField] private HQMenuController hq;
    [SerializeField] private SkeletonAnimation map;
    [SerializeField] private SkeletonAnimation hqMap;
    [SerializeField] private GameObject water;
    [SerializeField] private SwipeScrollerWorldSpace scrollView = null;
    private const float ZOOM_SCALE = 1.0f;
    private static Vector2 MAP_SIZE = new Vector2(3557, 2696);
    private Vector2 _winSize;
    private bool onLeft = true;
    private float heightG = MAP_SIZE.y / 2;
    private bool scheduleUpdate;

    [SerializeField] private int missionsCompleted;
    private DG.Tweening.Sequence seq;
    void Start()
    {
        mapSeq = DOTween.Sequence();
        waterSeq = DOTween.Sequence();
        _winSize.x = 1334;
        _winSize.y = 750;
        Vector2 positionOffset = Vector2.zero;
        Vector2 winMinBounds = new Vector2(-positionOffset.x * Globals.screenScaleValueX, -positionOffset.y * Globals.screenScaleValueY);
        Vector2 winMaxBounds = new Vector2((1334 - positionOffset.x) * Globals.screenScaleValueX, (750 - positionOffset.y) * Globals.screenScaleValueY);
        map.state.SetAnimation(0, "mapBgAnim", true);
        map.state.SetAnimation(1, "boat", true);
        map.state.SetAnimation(2, "Smoke", true);
        map.state.SetAnimation(3, "clouds", true);
        map.state.SetAnimation(4, "snow_Man", true);
        map.state.SetAnimation(5, "volcano_glow", true);
        PlayWaterAnim();
        scheduleUpdate = true;

        if (PlayerPrefs.GetInt("newIslandAnimationPlayed", 0) == 0 && GameData.instance.fileHandler.missionsCompleted >= 30)
        {
            map.state.SetAnimation(6, "island3", false);
            map.state.AddAnimation(6, "island2", false);
            map.state.AddAnimation(6, "island", true);
            seq = DOTween.Sequence();
            seq.AppendInterval(2.0f).AppendCallback(()=>
            {
                PlayerPrefs.SetInt("newIslandAnimationPlayed", 1);
                PlayerPrefs.SetInt("IsIslandUnlocked", 1);
            });
        }
        else if (PlayerPrefs.GetInt("newIslandAnimationPlayed", 0)==1)
        {
            map.state.SetAnimation(6, "island", true);
        }


#if !UNITY_STANDALONE
    scrollView.SetTouchEnabled  (false);
#endif

    }

    public void PlayWaterAnim()
    {
        DG.Tweening.Sequence waterSeq = DOTween.Sequence();
        waterSeq.Append(water.transform.DOScale(new Vector3(1.1f, 1.1f, 1.1f), 10).SetEase(Ease.InOutSine));
        waterSeq.Append(water.transform.DOScale(new Vector3(1, 1, 1), 10).SetEase(Ease.InOutSine));
        waterSeq.SetLoops(-1);
        waterSeq.Play();
    }

    void Update()
    {

        if (!scheduleUpdate)
            return;
#if !UNITY_STANDALONE


        if (Globals.disableScroll)
        {
            scrollView.SetTouchEnabled(false);
        }
        else
        {
            scrollView.SetTouchEnabled(true);

        }
#endif

        //Globals.worldScrollPosX =  abs(scrollView.getInnerContainer().getPositionX()); TODO Ask Bilal Bhai
        //Globals.worldScrollPosY = abs(scrollView.getInnerContainer().getPositionY());

#if UNITY_IOS || UNITY_ANDROID

        if (Globals.isOnIsland)
            return;

        if (scrollView.GetContentPos().x >-7.5f  && Globals.onMainHQ)
        {
            Observer.DispatchCustomEvent("show_back_to_HQ_button");
            seq = DOTween.Sequence();
            seq.AppendInterval(0.25f).AppendCallback(()=>
            {
                Observer.DispatchCustomEvent("ON_DISABLE_HQ");
                Globals.onMainHQ = false;
                Globals.onMap = true;
            });
            seq.Play();
            scrollView.yMax = 4;
            scrollView.yMin = -4;
        }
        else if (!Globals.onMainHQ && scrollView.GetContentPos().x <= -7.5f)
        {
            Observer.DispatchCustomEvent("hide_back_to_HQ_button");
            seq = DOTween.Sequence().AppendInterval(0.25f).AppendCallback(()=>
            {
                Observer.DispatchCustomEvent("ON_ENABLE_HQ");
                Globals.onMainHQ = true;
                Globals.onMap = false;
            }).Play();

            scrollView.yMax = -1.5f;
            scrollView.yMin = -2f;
        }

        if (scrollView.GetContentPos().x > 11)
        {
            //TODO
            float Value = Mathf.Clamp(scrollView.GetContentPos().x, 0, 11);

            float Value2 = MAP_SIZE.y / 2 - (500 - Value);
            //scrollView.getInnerContainer().setContentSize(Size(MAP_SIZE.width / 2 + 1000, Value2));
        }
#endif
    }

    public SkeletonAnimation GetMap()
    {
        return map;
    }

    public GameObject GetBackgroundWater()
    {
        return water;
    }

    public SwipeScrollerWorldSpace GetScrollView()
    {
        return scrollView;
    }

    public void ScrollToPosition(Vector3 p, bool attuned)
    {
        if (attuned)
        {
            //        scrollView.scrollToPercentHorizontal(100  - (((MAP_SIZE.width)-p.x)/MAP_SIZE.width)*75, 1.5f, true);
            scrollView.ScrollToTarget(p, 0.1f, true);
        }
        else
        {
            //        scrollView.scrollToPercentHorizontal(100  - (((MAP_SIZE.width)-p.x)/MAP_SIZE.width)*115, 1.5f, true);

            scrollView.JumpToTarget(p,4f);
        }
    }

    public void SlideToPos(Vector3 p)
    {
        scrollView.SlideToTarget(p);
    }


    public void EnableVerticalScroll()
    {

    }

    public void DisableVerticalScroll()
    {

    }

    public GameObject GetContent()
    {
        return scrollView.gameObject;
    }

}
