using System;
using DG.Tweening;
using Spine;
using Spine.Unity;
using UnityEngine;

public class TinyBots : Enemy
{
    int boneNumber = 0;
    public bool AttachToBone = false;
    Bone _bone;

    [HideInInspector] public string tweenID = "TinyBots";

    void InitStats()
    {
        //SetAllowRelocate(false);

        stats = new Attributes();
        baseStats = new Attributes();


        isBoss = false;
        int bossNumber = 10;

        var bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;

        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = 180;
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = 0;
        stats.coinAwarded = baseStats.coinAwarded = 0;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
        SetEnemyDifficulty();
    }

    void InitHealthBar()
    {
        //healthBar.setPositionY(250);
        //healthBar.setVisible(false);
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenID += gameObject.GetInstanceID().ToString();
        boneNumber = 0;
        isBoss = false;
        InitStats();
        InitHealthBar();
        _bone = enemySprite.skeleton.FindBone("BB");

        //healthBar.setPositionY(10000); // TODO ASK

        DOTween.Sequence().SetId(tweenID).AppendInterval(0.1f)
            .Append(transform.DOBlendableMoveBy(new Vector3(-Globals.CocosToUnity(250) + UnityEngine.Random.value * Globals.CocosToUnity(500),
                Globals.CocosToUnity(200) + UnityEngine.Random.value * Globals.CocosToUnity(200)), 0.35f));

        DOTween.Sequence().SetId(tweenID)
            .Append(transform.DOBlendableRotateBy(Vector3.forward * (-500 + UnityEngine.Random.value * 1000), 0.45f))
            .AppendCallback(AddToArray);
    }

    public override bool TakeHit(double damage)
    {
        {
            stats.health -= damage * 10; // TODO Remove '* 10'
            enemySprite.skeleton.SetColor(Color.red);
            DOTween.Sequence().SetId(tweenID)
                .Append(
                    DOTween.To(() => enemySprite.skeleton.GetColor(), current => enemySprite.skeleton.SetColor(current),
                    new Color(1, 1, 1), 0.2f)
                );
        }

        if (stats.health < 0)
        {
            DOTween.Kill(tweenID);
            return true;
        }

        return false;
    }

    void AddToArray()
    {

        AttachToBone = true;
        DOTween.Kill(tweenID);
        enemySprite.skeleton.SetColor(Color.white);
    }

    public override bool CheckCollision(Vector2 P1)
    {
        var bonePos = _bone.GetWorldPosition(enemySprite.transform);
        var dist = Vector2.Distance(P1, bonePos);
        if (dist * dist < enemyCollisionRadius)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    public void AssignBone(int bN)
    {
        boneNumber = bN;
    }

    public int GetBoneAssigned()
    {
        return boneNumber;
    }
}
