using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;

public class DeathWing : Enemy
{
    private Vector2 enemyAcceleration;
    private bool BoostOn;
    private bool allowShooting;
    [SerializeField] GameObject boost;
    [SerializeField] Sprite missileSprite;
    [SerializeField] private EnemyFall enemyFallPrefab;
    private Bone gun;
    private bool chargingLaser;
    private bool isLaserActivated;
    private float speedLimit;

    private const float ENEMYSCALE = 0.23f;
    private const float GRAVITY = 0.05f;//0.1f;   //0.15 works only in y-axis
    private const float DRAG = 0.99f;
    
    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        scheduleUpdate = true;
        enemyAcceleration = Vector2.zero;
        BoostOn = false;
        allowShooting = false;
        chargingLaser = false;
        allowPushBack = true;
        speedLimit = 6 * 1.0f;
        InitializeEnemyParameters();

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Red);
    }

    private void InitializeEnemyParameters()
    {
        if (Random.value < 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1800), Random.value * Globals.CocosToUnity(100) + Globals.CocosToUnity(50));
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1800), Random.value * Globals.CocosToUnity(100) + Globals.CocosToUnity(50));
        }

        enemySprite.transform.SetScale(ENEMYSCALE);
        enemySprite.state.SetAnimation(0, "shootEnemyPlane7", true);
        enemySprite.state.Event += HandleSpineAnimation;
    }

    private void HandleSpineAnimation(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if(spineEvent.Data.Name == "shootleft")
        {
            Shoot(true);
            //call shoot left
        }
        if (spineEvent.Data.Name == "shootright")
        {
            Shoot(false);
            //call shoot right
        }
    }

    private void ActivateBoost(bool val)
    {
        boost.SetActive(val);
    }

    void Update()
    {
        RotationLogic();
        BoostLogic();

        if (enemySprite.transform.eulerAngles.z < 90 || enemySprite.transform.eulerAngles.z > 90 + 180)
        {
            enemySprite.transform.SetScaleY(ENEMYSCALE);
        }
        else
        {
            enemySprite.transform.SetScaleY(-ENEMYSCALE);
        }
    }

    private void RotationLogic()
    {
        Globals.rotateNodeToPointLerp(stats.turnSpeed, enemySprite.gameObject, player.transform.position);

        float angleNodeToRotateTo = Globals.getAngleOfTwoVectors(enemySprite.transform.position, player.transform.position);
        float nodeCurrentAngle = Globals.getCurrentAngle(enemySprite.gameObject);
        float diffAngle = Globals.getAngleDifference(angleNodeToRotateTo, nodeCurrentAngle);

      
        if (diffAngle > -30 && diffAngle < 30)
        {
            if (!allowShooting)
            {
                enemySprite.state.SetAnimation(0, "shootEnemyPlane7", true);
            }
            allowShooting = true;
        }
        else
        {
            if (allowShooting)
            {
                enemySprite.state.SetAnimation(0, "idle", true);
            }
            allowShooting = false;
        }
    }

    void BoostLogic()
    {
        if (Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position) > stats.distanceToShoot)
        {
            BoostOn = true;
            ActivateBoost(true);
        }
        else
        {
            BoostOn = false;
            ActivateBoost(false);
        }

        if (!isLaserActivated && !chargingLaser)
        {
            transform.position = new Vector2(transform.position.x + enemyAcceleration.x * Time.deltaTime * Globals.CocosToUnity(60), transform.position.y + enemyAcceleration.y * Time.deltaTime * Globals.CocosToUnity(60)*0.25f);
        }
        if (BoostOn)
        {
            if (Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position) > Globals.CocosToUnity(9500))
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed / 4;
                enemyAcceleration.x = Mathf.Clamp(enemyAcceleration.x, -speedLimit - 8, speedLimit + 8);

                enemyAcceleration.y = enemyAcceleration.y + Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)) * stats.speed / 4;
                enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit - 8, speedLimit + 8);
            }

            else
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed;
                if (enemyAcceleration.x > speedLimit)
                {
                    enemyAcceleration.x -= 0.4f;
                }

                if (enemyAcceleration.x < -speedLimit)
                {
                    enemyAcceleration.x += 0.4f;
                }

                enemyAcceleration.y = enemyAcceleration.y + Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed;
                if (enemyAcceleration.y > speedLimit)
                {
                    enemyAcceleration.y -= 0.4f;
                }

                if (enemyAcceleration.y < -speedLimit)
                {
                    enemyAcceleration.y += 0.4f;
                }

                enemyAcceleration.x = Mathf.Clamp(enemyAcceleration.x, -speedLimit - 8, speedLimit + 8);
                enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit - 8, speedLimit + 8);
            }
        }
        else
        {
            enemyAcceleration = new Vector2(enemyAcceleration.x, enemyAcceleration.y - GRAVITY);
            enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit * 1.5f, speedLimit * 1.5f);
            enemyAcceleration.x = enemyAcceleration.x * DRAG;
        }

        if (transform.position.y < Globals.CocosToUnity(1))
        {
            transform.position = new Vector2(transform.position.x, Globals.CocosToUnity(1));
        }
    }

    private void Shoot(bool direction)
    {
        if (!allowShooting)
        {
            return;
        }
        shootRandom = Random.Range(0, 10000);
        if (shootRandom > skillShootProbability)
        {
            return;
        }

        if (direction)
        {
            gun = enemySprite.skeleton.FindBone("rocketLeft");
        }
        else
        {
            gun = enemySprite.skeleton.FindBone("rocketRight");
        }

        bool didFindMissile = false;
        HomingMissile missile = null;
        
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }
        }
        if (!didFindMissile)
        {
            return;
        }

        missile.Init();
        missile.missileSprite.sprite = missileSprite;
        missile.SetDamage(stats.missileDamage);
        missile.SetSpeed(18);
        missile.homingMultiplier = 0.5f;
        missile.transform.localScale = new Vector2(-0.8f, 0.8f);
        
        missile.transform.position = new Vector2(enemySprite.transform.position.x + enemySprite.transform.localScale.x * gun.WorldX * Mathf.Sin(Mathf.Deg2Rad*enemySprite.transform.GetRotation()), enemySprite.transform.position.y + enemySprite.transform.localScale.y * gun.WorldY * Mathf.Cos(Mathf.Deg2Rad*enemySprite.transform.GetRotation()));
        missile.transform.SetRotation(enemySprite.transform.GetRotation());
        
        //Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));

        //bullet.PlayBulletAnim(2.5f, dest, false);

        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.radiusSQ = Globals.CocosToUnity(90);
        missile.EnableTrail();
        missile.RemoveAfterDuration();
        //bullet.setRadiusEffectSquared(2);
    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        int bossNumber = 4;
        //ValueMap bossStats = GameSharedData.Instance.bo GameData::getInstance()->getBoss(bossNumber).at("Stats").asValueMap();

        stats.speed = baseStats.speed = 2f;//0.35f;
        stats.health = baseStats.health = 1250;
        stats.turnSpeed = baseStats.turnSpeed = 100;
        stats.bulletDamage = baseStats.bulletDamage = 150;
        stats.regen = baseStats.regen = 1;
        stats.xp = baseStats.xp = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 25;
        stats.missileDamage = baseStats.missileDamage = 150;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
    }

    public override void Destroy()
    {
        if(gameObject != null && gameObject.activeInHierarchy)
            StartCoroutine(DestroyCoroutine());
        //if (allowDeathParticles) //&& FileHandler::getInstance().currentMission == 2 && gameType == GameType::Arena)TODO
        //{
        //    dynamicFallEnemy(enemy);
        //}
    }

    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        EnemyFall enemyFall = null;
        if (Random.value < 0.2f && allowDeathParticles)
        {
            // enemyFall = Instantiate(enemyFallPrefab);
            // enemyFall.InitFallEnemy(this, 5);
            for (int i = 0; i < 3; i++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;    
                debris.CreateWithData(400, 5, true, true);
                debris.transform.position = transform.position;
            }
        }
        yield return new WaitForEndOfFrame();
        base.Destroy();
    }
}
