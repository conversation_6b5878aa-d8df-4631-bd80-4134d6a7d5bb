﻿using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Reflection;

namespace Apq.Reflection
{
    /// <summary>
    /// 类型缓存,避免重复读取类型
    /// </summary>
    public sealed class TypeCache : ISingleton<TypeCache>
    {
        public static TypeCache Instance => GlobalObject.GetOrAddSingleton<TypeCache>();

        /// <summary>
        /// 对新创建的实例进行初始化
        /// </summary>
        public TypeCache Init()
        {
            return this;
        }

        private ConcurrentDictionary<string, Type> CachedTypes { get; } = new();

        private ConcurrentDictionary<string, Assembly> CachedAssemblies { get; } = new();

        /// <summary>
        /// 获取程序集,缓存中不存在时,按路径加载到缓存
        /// </summary>
        /// <param name="dllPath">程序集路径</param>
        public Assembly GetAssembly(string dllPath)
        {
            if (CachedAssemblies.TryGetValue(dllPath, out var assembly)) return assembly;

            assembly = Assembly.LoadFrom(dllPath);
            CachedAssemblies.AddOrUpdate(dllPath, assembly, (_, v) => v);
            return assembly;
        }

        /// <summary>
        /// 获取类型,缓存中不存在时,自动寻找类型并加入缓存
        /// </summary>
        /// <param name="fullClassName">类全名</param>
        /// <param name="dllPath">指定在哪个程序集中寻找</param>
        /// <returns></returns>
        public Type GetType(string fullClassName, string dllPath = null)
        {
            if (CachedTypes.TryGetValue(fullClassName, out var type)) return type;

            type = Type.GetType(fullClassName);
            if (type == null)
            {
                if (!string.IsNullOrWhiteSpace(dllPath))
                {
                    var asm = GetAssembly(dllPath);
                    if (asm != null)
                    {
                        type = asm.GetType(fullClassName);
                    }
                }
                else
                {
                    foreach (var assembly in CachedAssemblies.Values)
                    {
                        type = assembly.GetType(fullClassName);
                        if (type != null) break;
                    }
                }
            }

            if (type != null)
            {
                CachedTypes.AddOrUpdate(fullClassName, type, (_, v) => v);
            }
            return type;
        }

        /// <summary>
        /// 将类型添加到缓存
        /// </summary>
        /// <param name="type"></param>
        /// <param name="replace">重复添加时是否替换</param>
        /// <remarks>程序集加入缓存使用GetAssembly方法</remarks>
        public void AddType(Type type, bool replace = false)
        {
            var ct = CachedTypes.Values.FirstOrDefault(x => x.FullName == type.FullName);
            if (replace && ct != null)
            {
                CachedTypes.TryRemove(ct.FullName!, out _);
                ct = null;
            }

            if (ct == null)
            {
                CachedTypes.AddOrUpdate(type.FullName!, type, (_, v) => v);
            }
        }
    }
}
