using UnityEngine.InputSystem.EnhancedTouch;
using ETouch = UnityEngine.InputSystem.EnhancedTouch;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Events;

[RequireComponent(typeof(CanvasGroup))]
public class FloatingJoystick : MonoBehaviour
{
    public enum JoyStickType { Left, Right }

    [SerializeField] private JoyStickType type;
    [SerializeField] private RectTransform parentRectTransform;
    [SerializeField] private RectTransform knobRectTransform;
    [SerializeField] private bool isFloating = true;
    [SerializeField] private UnityEvent onDownEvent, onUpEvent;
    private RectTransform rectTransform;
    private CanvasGroup canvasGroup;
    private Finger movementFinger;
    private Vector2 movementAmount, initialPosition;

    public Vector2 Value { get { return movementAmount; } }

    private void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
        rectTransform = GetComponent<RectTransform>();
    }

    private void Start()
    {
        canvasGroup.alpha = 1;
        canvasGroup.blocksRaycasts = !isFloating;
        canvasGroup.interactable = !isFloating;
        initialPosition = rectTransform.anchoredPosition;
    }

    private void OnEnable()
    {
        EnhancedTouchSupport.Enable();
        ETouch.Touch.onFingerDown += HandleFingerDown;
        ETouch.Touch.onFingerUp += HandleLostFinger;
        ETouch.Touch.onFingerMove += HandleMovedFinger;
    }
    private void OnDisable()
    {
        ETouch.Touch.onFingerDown -= HandleFingerDown;
        ETouch.Touch.onFingerUp -= HandleLostFinger;
        ETouch.Touch.onFingerMove -= HandleMovedFinger;
        EnhancedTouchSupport.Disable();

        movementFinger = null;
        knobRectTransform.anchoredPosition = Vector2.zero;
        movementAmount = Vector2.zero;

        rectTransform.anchoredPosition = initialPosition;
    }

    private void HandleMovedFinger(Finger movedFinger)
    {
        if (movedFinger != movementFinger)
            return;

        float maxMovement = rectTransform.sizeDelta.x / 2;
        Vector2 touchPosition =
            new Vector2(movedFinger.currentTouch.screenPosition.x / Screen.width * parentRectTransform.rect.width,
            movedFinger.currentTouch.screenPosition.y / Screen.height * parentRectTransform.rect.height);
        Vector2 anchoredPosition = type == JoyStickType.Left ? rectTransform.anchoredPosition
            : new Vector2(parentRectTransform.rect.width + rectTransform.anchoredPosition.x,
            rectTransform.anchoredPosition.y);
        Vector2 movementVector = touchPosition - anchoredPosition;
        Vector2 directionVector = movementVector.normalized;
        Vector2 knobPosition = directionVector * Mathf.Clamp(movementVector.magnitude, 0, maxMovement);

        knobRectTransform.anchoredPosition = knobPosition;
        movementAmount = knobPosition / maxMovement;
    }

    private void HandleLostFinger(Finger lostFinger)
    {
        if (lostFinger != movementFinger)
            return;

        onUpEvent?.Invoke();

        movementFinger = null;
        knobRectTransform.anchoredPosition = Vector2.zero;
        movementAmount = Vector2.zero;

        rectTransform.anchoredPosition = initialPosition;
    }

    private void HandleFingerDown(Finger touchedFinger)
    {
        var touchedObjects = Globals.PointerOverUIObjects(touchedFinger.screenPosition);
        if (touchedObjects.Count > 0)
        {
            if (touchedObjects[0].gameObject.name != gameObject.name)
                return;
        }
        else if (!isFloating) return;

        bool inArea = type == JoyStickType.Right ? touchedFinger.screenPosition.x > Screen.width / 2
            : touchedFinger.screenPosition.x < Screen.width / 2;

        if (movementFinger != null || !inArea)
            return;

        movementFinger = touchedFinger;
        knobRectTransform.anchoredPosition = Vector2.zero;
        movementAmount = Vector2.zero;
        onDownEvent?.Invoke();

        if (!isFloating)
        {
            float maxMovement = rectTransform.sizeDelta.x / 2;
            Vector2 touchPosition =
                new Vector2(movementFinger.currentTouch.screenPosition.x / Screen.width * parentRectTransform.rect.width,
                movementFinger.currentTouch.screenPosition.y / Screen.height * parentRectTransform.rect.height);
            Vector2 anchoredPosition = type == JoyStickType.Left ? rectTransform.anchoredPosition
            : new Vector2(parentRectTransform.rect.width + rectTransform.anchoredPosition.x,
            rectTransform.anchoredPosition.y);
            Vector2 movementVector = touchPosition - anchoredPosition;
            Vector2 directionVector = movementVector.normalized;
            Vector2 knobPosition = directionVector * Mathf.Clamp(movementVector.magnitude, 0, maxMovement);

            knobRectTransform.anchoredPosition = knobPosition;
            movementAmount = knobPosition / maxMovement;
            return;
        }

        Vector2 canvasPosition =
            new Vector2(touchedFinger.screenPosition.x / Screen.width * parentRectTransform.rect.width
            - (type == JoyStickType.Left ? 0 : parentRectTransform.rect.width),
            touchedFinger.screenPosition.y / Screen.height * parentRectTransform.rect.height);

        rectTransform.anchoredPosition = ClampStartPosition(canvasPosition);
    }

    private Vector2 ClampStartPosition(Vector2 canvasPosition)
    {
        if(type == JoyStickType.Left)
        {
            if (canvasPosition.x < rectTransform.sizeDelta.x / 2)
            {
                canvasPosition.x = rectTransform.sizeDelta.x / 2;
            }
        }
        else
        {
            if (canvasPosition.x > -rectTransform.sizeDelta.x / 2)
            {
                canvasPosition.x = -rectTransform.sizeDelta.x / 2;
            }
        }

        if (canvasPosition.y < rectTransform.sizeDelta.y / 2)
        {
            canvasPosition.y = rectTransform.sizeDelta.y / 2;
        }
        else if(canvasPosition.y > parentRectTransform.rect.height - rectTransform.sizeDelta.y / 2)
        {
            canvasPosition.y = parentRectTransform.rect.height - rectTransform.sizeDelta.y / 2;
        }

        return canvasPosition;
    }
}
