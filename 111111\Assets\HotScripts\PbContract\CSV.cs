// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: temp/proto25/CSV.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace X.PB {

  /// <summary>Holder for reflection information generated from temp/proto25/CSV.proto</summary>
  public static partial class CSVReflection {

    #region Descriptor
    /// <summary>File descriptor for temp/proto25/CSV.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CSVReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChZ0ZW1wL3Byb3RvMjUvQ1NWLnByb3RvEgRYLlBCIskBCg9Db25zb2xlTG9n",
            "Q2xvc2USLgoIQ1NWVGFibGUYASADKAsyHC5YLlBCLkNvbnNvbGVMb2dDbG9z",
            "ZS5DU1ZSb3cahQEKBkNTVlJvdxIRCglDbGFzc05hbWUYASABKAkSEgoKTWV0",
            "aG9kTmFtZRgCIAEoCRIdCgVMZXZlbBgDIAEoDjIOLlguUEIuTG9nTGV2ZWwS",
            "GQoRQ2xhc3NCeURlZmluaXRpb24YBCABKAgSGgoSTWV0aG9kQnlEZWZpbml0",
            "aW9uGAUgASgIIqQBCglQcm9wSG9pc3QSKAoIQ1NWVGFibGUYASADKAsyFi5Y",
            "LlBCLlByb3BIb2lzdC5DU1ZSb3cabQoGQ1NWUm93Eg8KB0hvaXN0SUQYASAB",
            "KAUSDAoETmFtZRgCIAEoCRIOCgZSZW1hcmsYAyABKAkSEQoJSG9pc3RUeXBl",
            "GAQgASgFEhIKCkhvaXN0VmFsdWUYBSABKAESDQoFSXNQY3QYBiABKAgi6AMK",
            "CUZpZ2h0QnVmZhIoCghDU1ZUYWJsZRgBIAMoCzIWLlguUEIuRmlnaHRCdWZm",
            "LkNTVlJvdxqwAwoGQ1NWUm93Eg4KBkJ1ZmZJRBgBIAEoBRIMCgROYW1lGAIg",
            "ASgJEg4KBlJlbWFyaxgDIAEoCRIOCgZQcmV2SUQYBCABKAUSDgoGTmV4dElE",
            "GAUgASgFEg8KB0J1ZmZDbHMYBiABKAkSEQoJQnVmZkNsc0lEGAcgASgFEhQK",
            "DEJ1ZmZDYXRlZ29yeRgIIAEoCRIQCghUYWtlTW92ZRgJIAEoCBIMCgRJY29u",
            "GAogASgJEi4KD0J1ZmZRdWV1aW5nTW9kZRgLIAEoDjIVLlguUEIuQnVmZlF1",
            "ZXVpbmdNb2RlEhIKCkltcG9ydGFuY2UYDCABKAUSKAoMQnVmZlN0b3BUeXBl",
            "GA0gAygOMhIuWC5QQi5CdWZmU3RvcFR5cGUSEAoIQnVmZkxpZmUYDiABKAIS",
            "GwoTU3RvcFdoZW5JbXBvc2VyRGVhZBgPIAEoCBIcChRTdG9wV2hlblJlY2Vw",
            "dG9yRGVhZBgQIAEoCBIhChlBZGRCdWZmVG9SZWNlcHRvcldoZW5TdG9wGBEg",
            "AygFEiAKGEFkZEJ1ZmZUb0ltcG9zZXJXaGVuU3RvcBgSIAMoBSJ9CgpGaWdo",
            "dEJ1ZmYxEikKCENTVlRhYmxlGAEgAygLMhcuWC5QQi5GaWdodEJ1ZmYxLkNT",
            "VlJvdxpECgZDU1ZSb3cSCgoCSUQYASABKAUSDAoETmFtZRgCIAEoCRIOCgZS",
            "ZW1hcmsYAyABKAkSEAoISG9pc3RJRHMYBCADKAUigQEKCkZpZ2h0QnVmZjMS",
            "KQoIQ1NWVGFibGUYASADKAsyFy5YLlBCLkZpZ2h0QnVmZjMuQ1NWUm93GkgK",
            "BkNTVlJvdxIKCgJJRBgBIAEoBRIMCgROYW1lGAIgASgJEg4KBlJlbWFyaxgD",
            "IAEoCRIUCgxIaXRCYWNrU3BlZWQYBCABKAIiuwEKCkZpZ2h0QnVmZjUSKQoI",
            "Q1NWVGFibGUYASADKAsyFy5YLlBCLkZpZ2h0QnVmZjUuQ1NWUm93GoEBCgZD",
            "U1ZSb3cSCgoCSUQYASABKAUSDAoETmFtZRgCIAEoCRIOCgZSZW1hcmsYAyAB",
            "KAkSEAoIUG9zaXRpb24YBCADKAISFQoNUG9zUmVsYXRpdmVseRgFIAEoBRIO",
            "CgZSYWRpdXMYBiABKAISFAoMRGVjZWxlcmF0aW9uGAcgASgCKtsCCghMb2dM",
            "ZXZlbBIQCgxMb2dMZXZlbF9BbGwQABIUCg9Mb2dMZXZlbF9GaW5lc3QQkE4S",
            "FAoOTG9nTGV2ZWxfVHJhY2UQoJwBEhQKDkxvZ0xldmVsX0RlYnVnELDqARIT",
            "Cg1Mb2dMZXZlbF9JbmZvEMC4AhIVCg9Mb2dMZXZlbF9Ob3RpY2UQ0IYDEhMK",
            "DUxvZ0xldmVsX1dhcm4Q4NQDEhgKEkxvZ0xldmVsX0V4Y2VwdGlvbhDo+wMS",
            "FAoOTG9nTGV2ZWxfRXJyb3IQ8KIEEhUKD0xvZ0xldmVsX1NldmVyZRCA8QQS",
            "FwoRTG9nTGV2ZWxfQ3JpdGljYWwQkL8FEhQKDkxvZ0xldmVsX0FsZXJ0EKCN",
            "BhIUCg5Mb2dMZXZlbF9GYXRhbBCw2wYSGAoSTG9nTGV2ZWxfRW1lcmdlbmN5",
            "EMCpBxIUCgxMb2dMZXZlbF9PZmYQ/////wcq1AEKCkZyZWV6ZU1vZGUSFQoR",
            "RnJlZXplTW9kZV9JZ25vcmUQABITCg9GcmVlemVNb2RlX05vbmUQARIYChRG",
            "cmVlemVNb2RlX1Bvc2l0aW9uWBACEhgKFEZyZWV6ZU1vZGVfUG9zaXRpb25Z",
            "EAMSGAoURnJlZXplTW9kZV9Qb3NpdGlvbloQBBIYChRGcmVlemVNb2RlX1Jv",
            "dGF0aW9uWBAFEhgKFEZyZWV6ZU1vZGVfUm90YXRpb25ZEAYSGAoURnJlZXpl",
            "TW9kZV9Sb3RhdGlvbloQByo4CghNb3ZlUm9hZBIVChFNb3ZlUm9hZF9TdHJh",
            "aWdodBAAEhUKEU1vdmVSb2FkX1BhcmFib2xhEAEqYAoTZVNraWxsRGlyZWN0",
            "aW9uVHlwZRIoCiRlU2tpbGxEaXJlY3Rpb25UeXBlX05lYXJlc3RPckZvcndh",
            "cmQQABIfChtlU2tpbGxEaXJlY3Rpb25UeXBlX0ZvcndhcmQQASo6CgtTaGFw",
            "ZVR5cGUzRBIWChJTaGFwZVR5cGUzRF9TcGhlcmUQABITCg9TaGFwZVR5cGUz",
            "RF9Cb3gQASqCAQoPQnVmZlF1ZXVpbmdNb2RlEhgKFEJ1ZmZRdWV1aW5nTW9k",
            "ZV9Ob25lEAASHQoZQnVmZlF1ZXVpbmdNb2RlX1VuaXZlcnNhbBABEhoKFkJ1",
            "ZmZRdWV1aW5nTW9kZV9TaW5nbGUQAhIaChZCdWZmUXVldWluZ01vZGVfVG9w",
            "cGxlEAMqbAoMQnVmZlN0b3BUeXBlEhUKEUJ1ZmZTdG9wVHlwZV9Ob25lEAAS",
            "FQoRQnVmZlN0b3BUeXBlX09uY2UQARIWChJCdWZmU3RvcFR5cGVfTmV2ZXIQ",
            "AhIWChJCdWZmU3RvcFR5cGVfVGltZXIQAyqdAQoKRm9sbG93VHlwZRIdChlG",
            "b2xsb3dUeXBlX0FwcHJvYWNoQmVoaW5kEAASHAoYRm9sbG93VHlwZV9BcHBy",
            "b2FjaEZyb250EAESGwoXRm9sbG93VHlwZV9BcHByb2FjaExlZnQQAhIcChhG",
            "b2xsb3dUeXBlX0FwcHJvYWNoUmlnaHQQAxIXChNGb2xsb3dUeXBlX1N1cnJv",
            "dW5kEARiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::X.PB.LogLevel), typeof(global::X.PB.FreezeMode), typeof(global::X.PB.MoveRoad), typeof(global::X.PB.eSkillDirectionType), typeof(global::X.PB.ShapeType3D), typeof(global::X.PB.BuffQueuingMode), typeof(global::X.PB.BuffStopType), typeof(global::X.PB.FollowType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.ConsoleLogClose), global::X.PB.ConsoleLogClose.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.ConsoleLogClose.Types.CSVRow), global::X.PB.ConsoleLogClose.Types.CSVRow.Parser, new[]{ "ClassName", "MethodName", "Level", "ClassByDefinition", "MethodByDefinition" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.PropHoist), global::X.PB.PropHoist.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.PropHoist.Types.CSVRow), global::X.PB.PropHoist.Types.CSVRow.Parser, new[]{ "HoistID", "Name", "Remark", "HoistType", "HoistValue", "IsPct" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff), global::X.PB.FightBuff.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff.Types.CSVRow), global::X.PB.FightBuff.Types.CSVRow.Parser, new[]{ "BuffID", "Name", "Remark", "PrevID", "NextID", "BuffCls", "BuffClsID", "BuffCategory", "TakeMove", "Icon", "BuffQueuingMode", "Importance", "BuffStopType", "BuffLife", "StopWhenImposerDead", "StopWhenReceptorDead", "AddBuffToReceptorWhenStop", "AddBuffToImposerWhenStop" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff1), global::X.PB.FightBuff1.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff1.Types.CSVRow), global::X.PB.FightBuff1.Types.CSVRow.Parser, new[]{ "ID", "Name", "Remark", "HoistIDs" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff3), global::X.PB.FightBuff3.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff3.Types.CSVRow), global::X.PB.FightBuff3.Types.CSVRow.Parser, new[]{ "ID", "Name", "Remark", "HitBackSpeed" }, null, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff5), global::X.PB.FightBuff5.Parser, new[]{ "CSVTable" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::X.PB.FightBuff5.Types.CSVRow), global::X.PB.FightBuff5.Types.CSVRow.Parser, new[]{ "ID", "Name", "Remark", "Position", "PosRelatively", "Radius", "Deceleration" }, null, null, null, null)})
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 日志级别(数值与log4net的定义相同),只显示比配置值大的日志
  /// </summary>
  public enum LogLevel {
    /// <summary>
    /// 开启所有日志
    /// </summary>
    [pbr::OriginalName("LogLevel_All")] All = 0,
    /// <summary>
    /// 精细
    /// </summary>
    [pbr::OriginalName("LogLevel_Finest")] Finest = 10000,
    /// <summary>
    /// 跟踪
    /// </summary>
    [pbr::OriginalName("LogLevel_Trace")] Trace = 20000,
    /// <summary>
    /// 调试
    /// </summary>
    [pbr::OriginalName("LogLevel_Debug")] Debug = 30000,
    /// <summary>
    /// 信息
    /// </summary>
    [pbr::OriginalName("LogLevel_Info")] Info = 40000,
    /// <summary>
    /// 提醒
    /// </summary>
    [pbr::OriginalName("LogLevel_Notice")] Notice = 50000,
    /// <summary>
    /// 警告
    /// </summary>
    [pbr::OriginalName("LogLevel_Warn")] Warn = 60000,
    /// <summary>
    /// 异常(因为不一定代表有错,所以设定比Error小)
    /// </summary>
    [pbr::OriginalName("LogLevel_Exception")] Exception = 65000,
    /// <summary>
    /// 错误
    /// </summary>
    [pbr::OriginalName("LogLevel_Error")] Error = 70000,
    /// <summary>
    /// 严重
    /// </summary>
    [pbr::OriginalName("LogLevel_Severe")] Severe = 80000,
    /// <summary>
    /// 关键
    /// </summary>
    [pbr::OriginalName("LogLevel_Critical")] Critical = 90000,
    /// <summary>
    /// 警报
    /// </summary>
    [pbr::OriginalName("LogLevel_Alert")] Alert = 100000,
    /// <summary>
    /// 致命
    /// </summary>
    [pbr::OriginalName("LogLevel_Fatal")] Fatal = 110000,
    /// <summary>
    /// 紧急
    /// </summary>
    [pbr::OriginalName("LogLevel_Emergency")] Emergency = 120000,
    /// <summary>
    /// 关闭所有日志
    /// </summary>
    [pbr::OriginalName("LogLevel_Off")] Off = 2147483647,
  }

  /// <summary>
  /// 冻结模式
  /// </summary>
  public enum FreezeMode {
    /// <summary>
    /// 忽略(不改变)
    /// </summary>
    [pbr::OriginalName("FreezeMode_Ignore")] Ignore = 0,
    /// <summary>
    /// 没有冻结
    /// </summary>
    [pbr::OriginalName("FreezeMode_None")] None = 1,
    /// <summary>
    /// X坐标不变
    /// </summary>
    [pbr::OriginalName("FreezeMode_PositionX")] PositionX = 2,
    /// <summary>
    /// Y坐标不变
    /// </summary>
    [pbr::OriginalName("FreezeMode_PositionY")] PositionY = 3,
    /// <summary>
    /// Z坐标不变
    /// </summary>
    [pbr::OriginalName("FreezeMode_PositionZ")] PositionZ = 4,
    /// <summary>
    /// 不能绕X轴旋转
    /// </summary>
    [pbr::OriginalName("FreezeMode_RotationX")] RotationX = 5,
    /// <summary>
    /// 不能绕Y轴旋转
    /// </summary>
    [pbr::OriginalName("FreezeMode_RotationY")] RotationY = 6,
    /// <summary>
    /// 不能绕Z轴旋转
    /// </summary>
    [pbr::OriginalName("FreezeMode_RotationZ")] RotationZ = 7,
  }

  /// <summary>
  /// 移动路线
  /// </summary>
  public enum MoveRoad {
    /// <summary>
    /// 直线
    /// </summary>
    [pbr::OriginalName("MoveRoad_Straight")] Straight = 0,
    /// <summary>
    /// 抛物线
    /// </summary>
    [pbr::OriginalName("MoveRoad_Parabola")] Parabola = 1,
  }

  /// <summary>
  /// 射击时，英雄的朝向(也就是子弹的方向)
  /// </summary>
  public enum eSkillDirectionType {
    /// <summary>
    /// 先最近的敌人，后垂直中线的方向
    /// </summary>
    [pbr::OriginalName("eSkillDirectionType_NearestOrForward")] NearestOrForward = 0,
    /// <summary>
    /// 永远朝着垂直中线的方向，如龙卷风
    /// </summary>
    [pbr::OriginalName("eSkillDirectionType_Forward")] Forward = 1,
  }

  /// <summary>
  /// 3D形状
  /// </summary>
  public enum ShapeType3D {
    /// <summary>
    /// 球形
    /// </summary>
    [pbr::OriginalName("ShapeType3D_Sphere")] Sphere = 0,
    /// <summary>
    /// 盒形
    /// </summary>
    [pbr::OriginalName("ShapeType3D_Box")] Box = 1,
  }

  /// <summary>
  /// Buff排队方式
  /// </summary>
  public enum BuffQueuingMode {
    /// <summary>
    /// 不排队
    /// </summary>
    [pbr::OriginalName("BuffQueuingMode_None")] None = 0,
    /// <summary>
    /// 普通排队
    /// </summary>
    [pbr::OriginalName("BuffQueuingMode_Universal")] Universal = 1,
    /// <summary>
    /// 同类Buff只允许添加一个(已存在就不添加)
    /// </summary>
    [pbr::OriginalName("BuffQueuingMode_Single")] Single = 2,
    /// <summary>
    /// 同类Buff只允许添加一个(顶掉同类Buff)
    /// </summary>
    [pbr::OriginalName("BuffQueuingMode_Topple")] Topple = 3,
  }

  /// <summary>
  /// Buff何时停止
  /// </summary>
  public enum BuffStopType {
    /// <summary>
    /// 无定义
    /// </summary>
    [pbr::OriginalName("BuffStopType_None")] None = 0,
    /// <summary>
    /// 执行一次后结束
    /// </summary>
    [pbr::OriginalName("BuffStopType_Once")] Once = 1,
    /// <summary>
    /// 永不结束
    /// </summary>
    [pbr::OriginalName("BuffStopType_Never")] Never = 2,
    /// <summary>
    /// 定时结束
    /// </summary>
    [pbr::OriginalName("BuffStopType_Timer")] Timer = 3,
  }

  /// <summary>
  /// 跟随方式
  /// </summary>
  public enum FollowType {
    /// <summary>
    /// 靠近到后边
    /// </summary>
    [pbr::OriginalName("FollowType_ApproachBehind")] ApproachBehind = 0,
    /// <summary>
    /// 靠近到前边
    /// </summary>
    [pbr::OriginalName("FollowType_ApproachFront")] ApproachFront = 1,
    /// <summary>
    /// 靠近到左边
    /// </summary>
    [pbr::OriginalName("FollowType_ApproachLeft")] ApproachLeft = 2,
    /// <summary>
    /// 靠近到右边
    /// </summary>
    [pbr::OriginalName("FollowType_ApproachRight")] ApproachRight = 3,
    /// <summary>
    /// 环绕
    /// </summary>
    [pbr::OriginalName("FollowType_Surround")] Surround = 4,
  }

  #endregion

  #region Messages
  /// <summary>
  /// CSV表格 ===========================================================================================
  /// 控制台日志的 关闭配置表
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ConsoleLogClose : pb::IMessage<ConsoleLogClose>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ConsoleLogClose> _parser = new pb::MessageParser<ConsoleLogClose>(() => new ConsoleLogClose());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ConsoleLogClose> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConsoleLogClose() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConsoleLogClose(ConsoleLogClose other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConsoleLogClose Clone() {
      return new ConsoleLogClose(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.ConsoleLogClose.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.ConsoleLogClose.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.ConsoleLogClose.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.ConsoleLogClose.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.ConsoleLogClose.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ConsoleLogClose);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ConsoleLogClose other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ConsoleLogClose other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the ConsoleLogClose message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.ConsoleLogClose.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          className_ = other.className_;
          methodName_ = other.methodName_;
          level_ = other.level_;
          classByDefinition_ = other.classByDefinition_;
          methodByDefinition_ = other.methodByDefinition_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "ClassName" field.</summary>
        public const int ClassNameFieldNumber = 1;
        private string className_ = "";
        /// <summary>
        /// 类名
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string ClassName {
          get { return className_; }
          set {
            className_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "MethodName" field.</summary>
        public const int MethodNameFieldNumber = 2;
        private string methodName_ = "";
        /// <summary>
        /// 方法名(留空表示整个类的配置)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string MethodName {
          get { return methodName_; }
          set {
            methodName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Level" field.</summary>
        public const int LevelFieldNumber = 3;
        private global::X.PB.LogLevel level_ = global::X.PB.LogLevel.All;
        /// <summary>
        /// 关闭的最高日志级别(不输出&lt;=该级别的日志，留空是全输出)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::X.PB.LogLevel Level {
          get { return level_; }
          set {
            level_ = value;
          }
        }

        /// <summary>Field number for the "ClassByDefinition" field.</summary>
        public const int ClassByDefinitionFieldNumber = 4;
        private bool classByDefinition_;
        /// <summary>
        /// 类名是否为定义的名称(预留字段，未使用)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool ClassByDefinition {
          get { return classByDefinition_; }
          set {
            classByDefinition_ = value;
          }
        }

        /// <summary>Field number for the "MethodByDefinition" field.</summary>
        public const int MethodByDefinitionFieldNumber = 5;
        private bool methodByDefinition_;
        /// <summary>
        /// 方法名是否为定义的名称(预留字段，未使用)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool MethodByDefinition {
          get { return methodByDefinition_; }
          set {
            methodByDefinition_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ClassName != other.ClassName) return false;
          if (MethodName != other.MethodName) return false;
          if (Level != other.Level) return false;
          if (ClassByDefinition != other.ClassByDefinition) return false;
          if (MethodByDefinition != other.MethodByDefinition) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (ClassName.Length != 0) hash ^= ClassName.GetHashCode();
          if (MethodName.Length != 0) hash ^= MethodName.GetHashCode();
          if (Level != global::X.PB.LogLevel.All) hash ^= Level.GetHashCode();
          if (ClassByDefinition != false) hash ^= ClassByDefinition.GetHashCode();
          if (MethodByDefinition != false) hash ^= MethodByDefinition.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (ClassName.Length != 0) {
            output.WriteRawTag(10);
            output.WriteString(ClassName);
          }
          if (MethodName.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(MethodName);
          }
          if (Level != global::X.PB.LogLevel.All) {
            output.WriteRawTag(24);
            output.WriteEnum((int) Level);
          }
          if (ClassByDefinition != false) {
            output.WriteRawTag(32);
            output.WriteBool(ClassByDefinition);
          }
          if (MethodByDefinition != false) {
            output.WriteRawTag(40);
            output.WriteBool(MethodByDefinition);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (ClassName.Length != 0) {
            output.WriteRawTag(10);
            output.WriteString(ClassName);
          }
          if (MethodName.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(MethodName);
          }
          if (Level != global::X.PB.LogLevel.All) {
            output.WriteRawTag(24);
            output.WriteEnum((int) Level);
          }
          if (ClassByDefinition != false) {
            output.WriteRawTag(32);
            output.WriteBool(ClassByDefinition);
          }
          if (MethodByDefinition != false) {
            output.WriteRawTag(40);
            output.WriteBool(MethodByDefinition);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (ClassName.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(ClassName);
          }
          if (MethodName.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(MethodName);
          }
          if (Level != global::X.PB.LogLevel.All) {
            size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Level);
          }
          if (ClassByDefinition != false) {
            size += 1 + 1;
          }
          if (MethodByDefinition != false) {
            size += 1 + 1;
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.ClassName.Length != 0) {
            ClassName = other.ClassName;
          }
          if (other.MethodName.Length != 0) {
            MethodName = other.MethodName;
          }
          if (other.Level != global::X.PB.LogLevel.All) {
            Level = other.Level;
          }
          if (other.ClassByDefinition != false) {
            ClassByDefinition = other.ClassByDefinition;
          }
          if (other.MethodByDefinition != false) {
            MethodByDefinition = other.MethodByDefinition;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 10: {
                ClassName = input.ReadString();
                break;
              }
              case 18: {
                MethodName = input.ReadString();
                break;
              }
              case 24: {
                Level = (global::X.PB.LogLevel) input.ReadEnum();
                break;
              }
              case 32: {
                ClassByDefinition = input.ReadBool();
                break;
              }
              case 40: {
                MethodByDefinition = input.ReadBool();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 10: {
                ClassName = input.ReadString();
                break;
              }
              case 18: {
                MethodName = input.ReadString();
                break;
              }
              case 24: {
                Level = (global::X.PB.LogLevel) input.ReadEnum();
                break;
              }
              case 32: {
                ClassByDefinition = input.ReadBool();
                break;
              }
              case 40: {
                MethodByDefinition = input.ReadBool();
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  /// <summary>
  /// 属性提升
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PropHoist : pb::IMessage<PropHoist>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PropHoist> _parser = new pb::MessageParser<PropHoist>(() => new PropHoist());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PropHoist> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PropHoist() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PropHoist(PropHoist other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PropHoist Clone() {
      return new PropHoist(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.PropHoist.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.PropHoist.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.PropHoist.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.PropHoist.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.PropHoist.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PropHoist);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PropHoist other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PropHoist other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the PropHoist message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.PropHoist.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          hoistID_ = other.hoistID_;
          name_ = other.name_;
          remark_ = other.remark_;
          hoistType_ = other.hoistType_;
          hoistValue_ = other.hoistValue_;
          isPct_ = other.isPct_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "HoistID" field.</summary>
        public const int HoistIDFieldNumber = 1;
        private int hoistID_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int HoistID {
          get { return hoistID_; }
          set {
            hoistID_ = value;
          }
        }

        /// <summary>Field number for the "Name" field.</summary>
        public const int NameFieldNumber = 2;
        private string name_ = "";
        /// <summary>
        /// 名称
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Remark" field.</summary>
        public const int RemarkFieldNumber = 3;
        private string remark_ = "";
        /// <summary>
        /// 说明
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Remark {
          get { return remark_; }
          set {
            remark_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "HoistType" field.</summary>
        public const int HoistTypeFieldNumber = 4;
        private int hoistType_;
        /// <summary>
        /// 提升类型
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int HoistType {
          get { return hoistType_; }
          set {
            hoistType_ = value;
          }
        }

        /// <summary>Field number for the "HoistValue" field.</summary>
        public const int HoistValueFieldNumber = 5;
        private double hoistValue_;
        /// <summary>
        /// 提升值
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public double HoistValue {
          get { return hoistValue_; }
          set {
            hoistValue_ = value;
          }
        }

        /// <summary>Field number for the "IsPct" field.</summary>
        public const int IsPctFieldNumber = 6;
        private bool isPct_;
        /// <summary>
        /// 是否百分比提升
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool IsPct {
          get { return isPct_; }
          set {
            isPct_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (HoistID != other.HoistID) return false;
          if (Name != other.Name) return false;
          if (Remark != other.Remark) return false;
          if (HoistType != other.HoistType) return false;
          if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(HoistValue, other.HoistValue)) return false;
          if (IsPct != other.IsPct) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (HoistID != 0) hash ^= HoistID.GetHashCode();
          if (Name.Length != 0) hash ^= Name.GetHashCode();
          if (Remark.Length != 0) hash ^= Remark.GetHashCode();
          if (HoistType != 0) hash ^= HoistType.GetHashCode();
          if (HoistValue != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(HoistValue);
          if (IsPct != false) hash ^= IsPct.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (HoistID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(HoistID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (HoistType != 0) {
            output.WriteRawTag(32);
            output.WriteInt32(HoistType);
          }
          if (HoistValue != 0D) {
            output.WriteRawTag(41);
            output.WriteDouble(HoistValue);
          }
          if (IsPct != false) {
            output.WriteRawTag(48);
            output.WriteBool(IsPct);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (HoistID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(HoistID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (HoistType != 0) {
            output.WriteRawTag(32);
            output.WriteInt32(HoistType);
          }
          if (HoistValue != 0D) {
            output.WriteRawTag(41);
            output.WriteDouble(HoistValue);
          }
          if (IsPct != false) {
            output.WriteRawTag(48);
            output.WriteBool(IsPct);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (HoistID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(HoistID);
          }
          if (Name.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (Remark.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Remark);
          }
          if (HoistType != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(HoistType);
          }
          if (HoistValue != 0D) {
            size += 1 + 8;
          }
          if (IsPct != false) {
            size += 1 + 1;
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.HoistID != 0) {
            HoistID = other.HoistID;
          }
          if (other.Name.Length != 0) {
            Name = other.Name;
          }
          if (other.Remark.Length != 0) {
            Remark = other.Remark;
          }
          if (other.HoistType != 0) {
            HoistType = other.HoistType;
          }
          if (other.HoistValue != 0D) {
            HoistValue = other.HoistValue;
          }
          if (other.IsPct != false) {
            IsPct = other.IsPct;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                HoistID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 32: {
                HoistType = input.ReadInt32();
                break;
              }
              case 41: {
                HoistValue = input.ReadDouble();
                break;
              }
              case 48: {
                IsPct = input.ReadBool();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                HoistID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 32: {
                HoistType = input.ReadInt32();
                break;
              }
              case 41: {
                HoistValue = input.ReadDouble();
                break;
              }
              case 48: {
                IsPct = input.ReadBool();
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  /// <summary>
  /// 战斗Buff 配置表
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FightBuff : pb::IMessage<FightBuff>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FightBuff> _parser = new pb::MessageParser<FightBuff>(() => new FightBuff());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FightBuff> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff(FightBuff other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff Clone() {
      return new FightBuff(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.FightBuff.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.FightBuff.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.FightBuff.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.FightBuff.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.FightBuff.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FightBuff);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FightBuff other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FightBuff other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the FightBuff message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.FightBuff.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          buffID_ = other.buffID_;
          name_ = other.name_;
          remark_ = other.remark_;
          prevID_ = other.prevID_;
          nextID_ = other.nextID_;
          buffCls_ = other.buffCls_;
          buffClsID_ = other.buffClsID_;
          buffCategory_ = other.buffCategory_;
          takeMove_ = other.takeMove_;
          icon_ = other.icon_;
          buffQueuingMode_ = other.buffQueuingMode_;
          importance_ = other.importance_;
          buffStopType_ = other.buffStopType_.Clone();
          buffLife_ = other.buffLife_;
          stopWhenImposerDead_ = other.stopWhenImposerDead_;
          stopWhenReceptorDead_ = other.stopWhenReceptorDead_;
          addBuffToReceptorWhenStop_ = other.addBuffToReceptorWhenStop_.Clone();
          addBuffToImposerWhenStop_ = other.addBuffToImposerWhenStop_.Clone();
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "BuffID" field.</summary>
        public const int BuffIDFieldNumber = 1;
        private int buffID_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int BuffID {
          get { return buffID_; }
          set {
            buffID_ = value;
          }
        }

        /// <summary>Field number for the "Name" field.</summary>
        public const int NameFieldNumber = 2;
        private string name_ = "";
        /// <summary>
        /// 名称
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Remark" field.</summary>
        public const int RemarkFieldNumber = 3;
        private string remark_ = "";
        /// <summary>
        /// 说明
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Remark {
          get { return remark_; }
          set {
            remark_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "PrevID" field.</summary>
        public const int PrevIDFieldNumber = 4;
        private int prevID_;
        /// <summary>
        /// 上一级ID
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int PrevID {
          get { return prevID_; }
          set {
            prevID_ = value;
          }
        }

        /// <summary>Field number for the "NextID" field.</summary>
        public const int NextIDFieldNumber = 5;
        private int nextID_;
        /// <summary>
        /// 下一级ID
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int NextID {
          get { return nextID_; }
          set {
            nextID_ = value;
          }
        }

        /// <summary>Field number for the "BuffCls" field.</summary>
        public const int BuffClsFieldNumber = 6;
        private string buffCls_ = "";
        /// <summary>
        /// C#类名后缀
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string BuffCls {
          get { return buffCls_; }
          set {
            buffCls_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "BuffClsID" field.</summary>
        public const int BuffClsIDFieldNumber = 7;
        private int buffClsID_;
        /// <summary>
        /// 子类ID
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int BuffClsID {
          get { return buffClsID_; }
          set {
            buffClsID_ = value;
          }
        }

        /// <summary>Field number for the "BuffCategory" field.</summary>
        public const int BuffCategoryFieldNumber = 8;
        private string buffCategory_ = "";
        /// <summary>
        /// Buff分类
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string BuffCategory {
          get { return buffCategory_; }
          set {
            buffCategory_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "TakeMove" field.</summary>
        public const int TakeMoveFieldNumber = 9;
        private bool takeMove_;
        /// <summary>
        /// 是否接管移动
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool TakeMove {
          get { return takeMove_; }
          set {
            takeMove_ = value;
          }
        }

        /// <summary>Field number for the "Icon" field.</summary>
        public const int IconFieldNumber = 10;
        private string icon_ = "";
        /// <summary>
        /// Buff图标
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Icon {
          get { return icon_; }
          set {
            icon_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "BuffQueuingMode" field.</summary>
        public const int BuffQueuingModeFieldNumber = 11;
        private global::X.PB.BuffQueuingMode buffQueuingMode_ = global::X.PB.BuffQueuingMode.None;
        /// <summary>
        /// 排队方式(0不排队就是由Buff自行调度)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::X.PB.BuffQueuingMode BuffQueuingMode {
          get { return buffQueuingMode_; }
          set {
            buffQueuingMode_ = value;
          }
        }

        /// <summary>Field number for the "Importance" field.</summary>
        public const int ImportanceFieldNumber = 12;
        private int importance_;
        /// <summary>
        /// 重要度(使用顶掉方式时，可以顶掉不比自己大的)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int Importance {
          get { return importance_; }
          set {
            importance_ = value;
          }
        }

        /// <summary>Field number for the "BuffStopType" field.</summary>
        public const int BuffStopTypeFieldNumber = 13;
        private static readonly pb::FieldCodec<global::X.PB.BuffStopType> _repeated_buffStopType_codec
            = pb::FieldCodec.ForEnum(106, x => (int) x, x => (global::X.PB.BuffStopType) x);
        private readonly pbc::RepeatedField<global::X.PB.BuffStopType> buffStopType_ = new pbc::RepeatedField<global::X.PB.BuffStopType>();
        /// <summary>
        /// 如何停止
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public pbc::RepeatedField<global::X.PB.BuffStopType> BuffStopType {
          get { return buffStopType_; }
        }

        /// <summary>Field number for the "BuffLife" field.</summary>
        public const int BuffLifeFieldNumber = 14;
        private float buffLife_;
        /// <summary>
        /// 定时结束:Buff时长-秒
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public float BuffLife {
          get { return buffLife_; }
          set {
            buffLife_ = value;
          }
        }

        /// <summary>Field number for the "StopWhenImposerDead" field.</summary>
        public const int StopWhenImposerDeadFieldNumber = 15;
        private bool stopWhenImposerDead_;
        /// <summary>
        /// 是否施加者死亡了就结束
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool StopWhenImposerDead {
          get { return stopWhenImposerDead_; }
          set {
            stopWhenImposerDead_ = value;
          }
        }

        /// <summary>Field number for the "StopWhenReceptorDead" field.</summary>
        public const int StopWhenReceptorDeadFieldNumber = 16;
        private bool stopWhenReceptorDead_;
        /// <summary>
        /// 是否受体死亡了就结束
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool StopWhenReceptorDead {
          get { return stopWhenReceptorDead_; }
          set {
            stopWhenReceptorDead_ = value;
          }
        }

        /// <summary>Field number for the "AddBuffToReceptorWhenStop" field.</summary>
        public const int AddBuffToReceptorWhenStopFieldNumber = 17;
        private static readonly pb::FieldCodec<int> _repeated_addBuffToReceptorWhenStop_codec
            = pb::FieldCodec.ForInt32(138);
        private readonly pbc::RepeatedField<int> addBuffToReceptorWhenStop_ = new pbc::RepeatedField<int>();
        /// <summary>
        /// Buff停止后给受体添加一些Buff
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public pbc::RepeatedField<int> AddBuffToReceptorWhenStop {
          get { return addBuffToReceptorWhenStop_; }
        }

        /// <summary>Field number for the "AddBuffToImposerWhenStop" field.</summary>
        public const int AddBuffToImposerWhenStopFieldNumber = 18;
        private static readonly pb::FieldCodec<int> _repeated_addBuffToImposerWhenStop_codec
            = pb::FieldCodec.ForInt32(146);
        private readonly pbc::RepeatedField<int> addBuffToImposerWhenStop_ = new pbc::RepeatedField<int>();
        /// <summary>
        /// Buff停止后给施加者添加一些Buff
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public pbc::RepeatedField<int> AddBuffToImposerWhenStop {
          get { return addBuffToImposerWhenStop_; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (BuffID != other.BuffID) return false;
          if (Name != other.Name) return false;
          if (Remark != other.Remark) return false;
          if (PrevID != other.PrevID) return false;
          if (NextID != other.NextID) return false;
          if (BuffCls != other.BuffCls) return false;
          if (BuffClsID != other.BuffClsID) return false;
          if (BuffCategory != other.BuffCategory) return false;
          if (TakeMove != other.TakeMove) return false;
          if (Icon != other.Icon) return false;
          if (BuffQueuingMode != other.BuffQueuingMode) return false;
          if (Importance != other.Importance) return false;
          if(!buffStopType_.Equals(other.buffStopType_)) return false;
          if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(BuffLife, other.BuffLife)) return false;
          if (StopWhenImposerDead != other.StopWhenImposerDead) return false;
          if (StopWhenReceptorDead != other.StopWhenReceptorDead) return false;
          if(!addBuffToReceptorWhenStop_.Equals(other.addBuffToReceptorWhenStop_)) return false;
          if(!addBuffToImposerWhenStop_.Equals(other.addBuffToImposerWhenStop_)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (BuffID != 0) hash ^= BuffID.GetHashCode();
          if (Name.Length != 0) hash ^= Name.GetHashCode();
          if (Remark.Length != 0) hash ^= Remark.GetHashCode();
          if (PrevID != 0) hash ^= PrevID.GetHashCode();
          if (NextID != 0) hash ^= NextID.GetHashCode();
          if (BuffCls.Length != 0) hash ^= BuffCls.GetHashCode();
          if (BuffClsID != 0) hash ^= BuffClsID.GetHashCode();
          if (BuffCategory.Length != 0) hash ^= BuffCategory.GetHashCode();
          if (TakeMove != false) hash ^= TakeMove.GetHashCode();
          if (Icon.Length != 0) hash ^= Icon.GetHashCode();
          if (BuffQueuingMode != global::X.PB.BuffQueuingMode.None) hash ^= BuffQueuingMode.GetHashCode();
          if (Importance != 0) hash ^= Importance.GetHashCode();
          hash ^= buffStopType_.GetHashCode();
          if (BuffLife != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(BuffLife);
          if (StopWhenImposerDead != false) hash ^= StopWhenImposerDead.GetHashCode();
          if (StopWhenReceptorDead != false) hash ^= StopWhenReceptorDead.GetHashCode();
          hash ^= addBuffToReceptorWhenStop_.GetHashCode();
          hash ^= addBuffToImposerWhenStop_.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (BuffID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(BuffID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (PrevID != 0) {
            output.WriteRawTag(32);
            output.WriteInt32(PrevID);
          }
          if (NextID != 0) {
            output.WriteRawTag(40);
            output.WriteInt32(NextID);
          }
          if (BuffCls.Length != 0) {
            output.WriteRawTag(50);
            output.WriteString(BuffCls);
          }
          if (BuffClsID != 0) {
            output.WriteRawTag(56);
            output.WriteInt32(BuffClsID);
          }
          if (BuffCategory.Length != 0) {
            output.WriteRawTag(66);
            output.WriteString(BuffCategory);
          }
          if (TakeMove != false) {
            output.WriteRawTag(72);
            output.WriteBool(TakeMove);
          }
          if (Icon.Length != 0) {
            output.WriteRawTag(82);
            output.WriteString(Icon);
          }
          if (BuffQueuingMode != global::X.PB.BuffQueuingMode.None) {
            output.WriteRawTag(88);
            output.WriteEnum((int) BuffQueuingMode);
          }
          if (Importance != 0) {
            output.WriteRawTag(96);
            output.WriteInt32(Importance);
          }
          buffStopType_.WriteTo(output, _repeated_buffStopType_codec);
          if (BuffLife != 0F) {
            output.WriteRawTag(117);
            output.WriteFloat(BuffLife);
          }
          if (StopWhenImposerDead != false) {
            output.WriteRawTag(120);
            output.WriteBool(StopWhenImposerDead);
          }
          if (StopWhenReceptorDead != false) {
            output.WriteRawTag(128, 1);
            output.WriteBool(StopWhenReceptorDead);
          }
          addBuffToReceptorWhenStop_.WriteTo(output, _repeated_addBuffToReceptorWhenStop_codec);
          addBuffToImposerWhenStop_.WriteTo(output, _repeated_addBuffToImposerWhenStop_codec);
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (BuffID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(BuffID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (PrevID != 0) {
            output.WriteRawTag(32);
            output.WriteInt32(PrevID);
          }
          if (NextID != 0) {
            output.WriteRawTag(40);
            output.WriteInt32(NextID);
          }
          if (BuffCls.Length != 0) {
            output.WriteRawTag(50);
            output.WriteString(BuffCls);
          }
          if (BuffClsID != 0) {
            output.WriteRawTag(56);
            output.WriteInt32(BuffClsID);
          }
          if (BuffCategory.Length != 0) {
            output.WriteRawTag(66);
            output.WriteString(BuffCategory);
          }
          if (TakeMove != false) {
            output.WriteRawTag(72);
            output.WriteBool(TakeMove);
          }
          if (Icon.Length != 0) {
            output.WriteRawTag(82);
            output.WriteString(Icon);
          }
          if (BuffQueuingMode != global::X.PB.BuffQueuingMode.None) {
            output.WriteRawTag(88);
            output.WriteEnum((int) BuffQueuingMode);
          }
          if (Importance != 0) {
            output.WriteRawTag(96);
            output.WriteInt32(Importance);
          }
          buffStopType_.WriteTo(ref output, _repeated_buffStopType_codec);
          if (BuffLife != 0F) {
            output.WriteRawTag(117);
            output.WriteFloat(BuffLife);
          }
          if (StopWhenImposerDead != false) {
            output.WriteRawTag(120);
            output.WriteBool(StopWhenImposerDead);
          }
          if (StopWhenReceptorDead != false) {
            output.WriteRawTag(128, 1);
            output.WriteBool(StopWhenReceptorDead);
          }
          addBuffToReceptorWhenStop_.WriteTo(ref output, _repeated_addBuffToReceptorWhenStop_codec);
          addBuffToImposerWhenStop_.WriteTo(ref output, _repeated_addBuffToImposerWhenStop_codec);
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (BuffID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuffID);
          }
          if (Name.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (Remark.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Remark);
          }
          if (PrevID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(PrevID);
          }
          if (NextID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(NextID);
          }
          if (BuffCls.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(BuffCls);
          }
          if (BuffClsID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuffClsID);
          }
          if (BuffCategory.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(BuffCategory);
          }
          if (TakeMove != false) {
            size += 1 + 1;
          }
          if (Icon.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Icon);
          }
          if (BuffQueuingMode != global::X.PB.BuffQueuingMode.None) {
            size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BuffQueuingMode);
          }
          if (Importance != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(Importance);
          }
          size += buffStopType_.CalculateSize(_repeated_buffStopType_codec);
          if (BuffLife != 0F) {
            size += 1 + 4;
          }
          if (StopWhenImposerDead != false) {
            size += 1 + 1;
          }
          if (StopWhenReceptorDead != false) {
            size += 2 + 1;
          }
          size += addBuffToReceptorWhenStop_.CalculateSize(_repeated_addBuffToReceptorWhenStop_codec);
          size += addBuffToImposerWhenStop_.CalculateSize(_repeated_addBuffToImposerWhenStop_codec);
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.BuffID != 0) {
            BuffID = other.BuffID;
          }
          if (other.Name.Length != 0) {
            Name = other.Name;
          }
          if (other.Remark.Length != 0) {
            Remark = other.Remark;
          }
          if (other.PrevID != 0) {
            PrevID = other.PrevID;
          }
          if (other.NextID != 0) {
            NextID = other.NextID;
          }
          if (other.BuffCls.Length != 0) {
            BuffCls = other.BuffCls;
          }
          if (other.BuffClsID != 0) {
            BuffClsID = other.BuffClsID;
          }
          if (other.BuffCategory.Length != 0) {
            BuffCategory = other.BuffCategory;
          }
          if (other.TakeMove != false) {
            TakeMove = other.TakeMove;
          }
          if (other.Icon.Length != 0) {
            Icon = other.Icon;
          }
          if (other.BuffQueuingMode != global::X.PB.BuffQueuingMode.None) {
            BuffQueuingMode = other.BuffQueuingMode;
          }
          if (other.Importance != 0) {
            Importance = other.Importance;
          }
          buffStopType_.Add(other.buffStopType_);
          if (other.BuffLife != 0F) {
            BuffLife = other.BuffLife;
          }
          if (other.StopWhenImposerDead != false) {
            StopWhenImposerDead = other.StopWhenImposerDead;
          }
          if (other.StopWhenReceptorDead != false) {
            StopWhenReceptorDead = other.StopWhenReceptorDead;
          }
          addBuffToReceptorWhenStop_.Add(other.addBuffToReceptorWhenStop_);
          addBuffToImposerWhenStop_.Add(other.addBuffToImposerWhenStop_);
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                BuffID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 32: {
                PrevID = input.ReadInt32();
                break;
              }
              case 40: {
                NextID = input.ReadInt32();
                break;
              }
              case 50: {
                BuffCls = input.ReadString();
                break;
              }
              case 56: {
                BuffClsID = input.ReadInt32();
                break;
              }
              case 66: {
                BuffCategory = input.ReadString();
                break;
              }
              case 72: {
                TakeMove = input.ReadBool();
                break;
              }
              case 82: {
                Icon = input.ReadString();
                break;
              }
              case 88: {
                BuffQueuingMode = (global::X.PB.BuffQueuingMode) input.ReadEnum();
                break;
              }
              case 96: {
                Importance = input.ReadInt32();
                break;
              }
              case 106:
              case 104: {
                buffStopType_.AddEntriesFrom(input, _repeated_buffStopType_codec);
                break;
              }
              case 117: {
                BuffLife = input.ReadFloat();
                break;
              }
              case 120: {
                StopWhenImposerDead = input.ReadBool();
                break;
              }
              case 128: {
                StopWhenReceptorDead = input.ReadBool();
                break;
              }
              case 138:
              case 136: {
                addBuffToReceptorWhenStop_.AddEntriesFrom(input, _repeated_addBuffToReceptorWhenStop_codec);
                break;
              }
              case 146:
              case 144: {
                addBuffToImposerWhenStop_.AddEntriesFrom(input, _repeated_addBuffToImposerWhenStop_codec);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                BuffID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 32: {
                PrevID = input.ReadInt32();
                break;
              }
              case 40: {
                NextID = input.ReadInt32();
                break;
              }
              case 50: {
                BuffCls = input.ReadString();
                break;
              }
              case 56: {
                BuffClsID = input.ReadInt32();
                break;
              }
              case 66: {
                BuffCategory = input.ReadString();
                break;
              }
              case 72: {
                TakeMove = input.ReadBool();
                break;
              }
              case 82: {
                Icon = input.ReadString();
                break;
              }
              case 88: {
                BuffQueuingMode = (global::X.PB.BuffQueuingMode) input.ReadEnum();
                break;
              }
              case 96: {
                Importance = input.ReadInt32();
                break;
              }
              case 106:
              case 104: {
                buffStopType_.AddEntriesFrom(ref input, _repeated_buffStopType_codec);
                break;
              }
              case 117: {
                BuffLife = input.ReadFloat();
                break;
              }
              case 120: {
                StopWhenImposerDead = input.ReadBool();
                break;
              }
              case 128: {
                StopWhenReceptorDead = input.ReadBool();
                break;
              }
              case 138:
              case 136: {
                addBuffToReceptorWhenStop_.AddEntriesFrom(ref input, _repeated_addBuffToReceptorWhenStop_codec);
                break;
              }
              case 146:
              case 144: {
                addBuffToImposerWhenStop_.AddEntriesFrom(ref input, _repeated_addBuffToImposerWhenStop_codec);
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  /// <summary>
  /// 提升角色属性类的Buff
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FightBuff1 : pb::IMessage<FightBuff1>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FightBuff1> _parser = new pb::MessageParser<FightBuff1>(() => new FightBuff1());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FightBuff1> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff1() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff1(FightBuff1 other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff1 Clone() {
      return new FightBuff1(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.FightBuff1.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.FightBuff1.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.FightBuff1.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.FightBuff1.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.FightBuff1.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FightBuff1);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FightBuff1 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FightBuff1 other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the FightBuff1 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.FightBuff1.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          iD_ = other.iD_;
          name_ = other.name_;
          remark_ = other.remark_;
          hoistIDs_ = other.hoistIDs_.Clone();
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "ID" field.</summary>
        public const int IDFieldNumber = 1;
        private int iD_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int ID {
          get { return iD_; }
          set {
            iD_ = value;
          }
        }

        /// <summary>Field number for the "Name" field.</summary>
        public const int NameFieldNumber = 2;
        private string name_ = "";
        /// <summary>
        /// 名称
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Remark" field.</summary>
        public const int RemarkFieldNumber = 3;
        private string remark_ = "";
        /// <summary>
        /// 说明
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Remark {
          get { return remark_; }
          set {
            remark_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "HoistIDs" field.</summary>
        public const int HoistIDsFieldNumber = 4;
        private static readonly pb::FieldCodec<int> _repeated_hoistIDs_codec
            = pb::FieldCodec.ForInt32(34);
        private readonly pbc::RepeatedField<int> hoistIDs_ = new pbc::RepeatedField<int>();
        /// <summary>
        /// 包含的提升ID
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public pbc::RepeatedField<int> HoistIDs {
          get { return hoistIDs_; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ID != other.ID) return false;
          if (Name != other.Name) return false;
          if (Remark != other.Remark) return false;
          if(!hoistIDs_.Equals(other.hoistIDs_)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (ID != 0) hash ^= ID.GetHashCode();
          if (Name.Length != 0) hash ^= Name.GetHashCode();
          if (Remark.Length != 0) hash ^= Remark.GetHashCode();
          hash ^= hoistIDs_.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          hoistIDs_.WriteTo(output, _repeated_hoistIDs_codec);
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          hoistIDs_.WriteTo(ref output, _repeated_hoistIDs_codec);
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (ID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(ID);
          }
          if (Name.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (Remark.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Remark);
          }
          size += hoistIDs_.CalculateSize(_repeated_hoistIDs_codec);
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.ID != 0) {
            ID = other.ID;
          }
          if (other.Name.Length != 0) {
            Name = other.Name;
          }
          if (other.Remark.Length != 0) {
            Remark = other.Remark;
          }
          hoistIDs_.Add(other.hoistIDs_);
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 34:
              case 32: {
                hoistIDs_.AddEntriesFrom(input, _repeated_hoistIDs_codec);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 34:
              case 32: {
                hoistIDs_.AddEntriesFrom(ref input, _repeated_hoistIDs_codec);
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  /// <summary>
  /// 击退Buff
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FightBuff3 : pb::IMessage<FightBuff3>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FightBuff3> _parser = new pb::MessageParser<FightBuff3>(() => new FightBuff3());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FightBuff3> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff3() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff3(FightBuff3 other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff3 Clone() {
      return new FightBuff3(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.FightBuff3.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.FightBuff3.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.FightBuff3.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.FightBuff3.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.FightBuff3.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FightBuff3);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FightBuff3 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FightBuff3 other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the FightBuff3 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.FightBuff3.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          iD_ = other.iD_;
          name_ = other.name_;
          remark_ = other.remark_;
          hitBackSpeed_ = other.hitBackSpeed_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "ID" field.</summary>
        public const int IDFieldNumber = 1;
        private int iD_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int ID {
          get { return iD_; }
          set {
            iD_ = value;
          }
        }

        /// <summary>Field number for the "Name" field.</summary>
        public const int NameFieldNumber = 2;
        private string name_ = "";
        /// <summary>
        /// 名称
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Remark" field.</summary>
        public const int RemarkFieldNumber = 3;
        private string remark_ = "";
        /// <summary>
        /// 说明
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Remark {
          get { return remark_; }
          set {
            remark_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "HitBackSpeed" field.</summary>
        public const int HitBackSpeedFieldNumber = 4;
        private float hitBackSpeed_;
        /// <summary>
        /// 击退速度
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public float HitBackSpeed {
          get { return hitBackSpeed_; }
          set {
            hitBackSpeed_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ID != other.ID) return false;
          if (Name != other.Name) return false;
          if (Remark != other.Remark) return false;
          if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(HitBackSpeed, other.HitBackSpeed)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (ID != 0) hash ^= ID.GetHashCode();
          if (Name.Length != 0) hash ^= Name.GetHashCode();
          if (Remark.Length != 0) hash ^= Remark.GetHashCode();
          if (HitBackSpeed != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(HitBackSpeed);
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (HitBackSpeed != 0F) {
            output.WriteRawTag(37);
            output.WriteFloat(HitBackSpeed);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          if (HitBackSpeed != 0F) {
            output.WriteRawTag(37);
            output.WriteFloat(HitBackSpeed);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (ID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(ID);
          }
          if (Name.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (Remark.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Remark);
          }
          if (HitBackSpeed != 0F) {
            size += 1 + 4;
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.ID != 0) {
            ID = other.ID;
          }
          if (other.Name.Length != 0) {
            Name = other.Name;
          }
          if (other.Remark.Length != 0) {
            Remark = other.Remark;
          }
          if (other.HitBackSpeed != 0F) {
            HitBackSpeed = other.HitBackSpeed;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 37: {
                HitBackSpeed = input.ReadFloat();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 37: {
                HitBackSpeed = input.ReadFloat();
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  /// <summary>
  /// 减速陷阱Buff
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FightBuff5 : pb::IMessage<FightBuff5>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FightBuff5> _parser = new pb::MessageParser<FightBuff5>(() => new FightBuff5());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FightBuff5> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::X.PB.CSVReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff5() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff5(FightBuff5 other) : this() {
      cSVTable_ = other.cSVTable_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FightBuff5 Clone() {
      return new FightBuff5(this);
    }

    /// <summary>Field number for the "CSVTable" field.</summary>
    public const int CSVTableFieldNumber = 1;
    private static readonly pb::FieldCodec<global::X.PB.FightBuff5.Types.CSVRow> _repeated_cSVTable_codec
        = pb::FieldCodec.ForMessage(10, global::X.PB.FightBuff5.Types.CSVRow.Parser);
    private readonly pbc::RepeatedField<global::X.PB.FightBuff5.Types.CSVRow> cSVTable_ = new pbc::RepeatedField<global::X.PB.FightBuff5.Types.CSVRow>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::X.PB.FightBuff5.Types.CSVRow> CSVTable {
      get { return cSVTable_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FightBuff5);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FightBuff5 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!cSVTable_.Equals(other.cSVTable_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= cSVTable_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      cSVTable_.WriteTo(output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      cSVTable_.WriteTo(ref output, _repeated_cSVTable_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += cSVTable_.CalculateSize(_repeated_cSVTable_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FightBuff5 other) {
      if (other == null) {
        return;
      }
      cSVTable_.Add(other.cSVTable_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            cSVTable_.AddEntriesFrom(ref input, _repeated_cSVTable_codec);
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the FightBuff5 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
      public sealed partial class CSVRow : pb::IMessage<CSVRow>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<CSVRow> _parser = new pb::MessageParser<CSVRow>(() => new CSVRow());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<CSVRow> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::X.PB.FightBuff5.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow(CSVRow other) : this() {
          iD_ = other.iD_;
          name_ = other.name_;
          remark_ = other.remark_;
          position_ = other.position_.Clone();
          posRelatively_ = other.posRelatively_;
          radius_ = other.radius_;
          deceleration_ = other.deceleration_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public CSVRow Clone() {
          return new CSVRow(this);
        }

        /// <summary>Field number for the "ID" field.</summary>
        public const int IDFieldNumber = 1;
        private int iD_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int ID {
          get { return iD_; }
          set {
            iD_ = value;
          }
        }

        /// <summary>Field number for the "Name" field.</summary>
        public const int NameFieldNumber = 2;
        private string name_ = "";
        /// <summary>
        /// 名称
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Name {
          get { return name_; }
          set {
            name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Remark" field.</summary>
        public const int RemarkFieldNumber = 3;
        private string remark_ = "";
        /// <summary>
        /// 说明
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public string Remark {
          get { return remark_; }
          set {
            remark_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        /// <summary>Field number for the "Position" field.</summary>
        public const int PositionFieldNumber = 4;
        private static readonly pb::FieldCodec<float> _repeated_position_codec
            = pb::FieldCodec.ForFloat(34);
        private readonly pbc::RepeatedField<float> position_ = new pbc::RepeatedField<float>();
        /// <summary>
        /// 陷阱位置
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public pbc::RepeatedField<float> Position {
          get { return position_; }
        }

        /// <summary>Field number for the "PosRelatively" field.</summary>
        public const int PosRelativelyFieldNumber = 5;
        private int posRelatively_;
        /// <summary>
        /// 位置关系(0:相对受体位置，1:相对施加者位置)
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int PosRelatively {
          get { return posRelatively_; }
          set {
            posRelatively_ = value;
          }
        }

        /// <summary>Field number for the "Radius" field.</summary>
        public const int RadiusFieldNumber = 6;
        private float radius_;
        /// <summary>
        /// 陷阱半径
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public float Radius {
          get { return radius_; }
          set {
            radius_ = value;
          }
        }

        /// <summary>Field number for the "Deceleration" field.</summary>
        public const int DecelerationFieldNumber = 7;
        private float deceleration_;
        /// <summary>
        /// 减速速度
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public float Deceleration {
          get { return deceleration_; }
          set {
            deceleration_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as CSVRow);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(CSVRow other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ID != other.ID) return false;
          if (Name != other.Name) return false;
          if (Remark != other.Remark) return false;
          if(!position_.Equals(other.position_)) return false;
          if (PosRelatively != other.PosRelatively) return false;
          if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Radius, other.Radius)) return false;
          if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Deceleration, other.Deceleration)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (ID != 0) hash ^= ID.GetHashCode();
          if (Name.Length != 0) hash ^= Name.GetHashCode();
          if (Remark.Length != 0) hash ^= Remark.GetHashCode();
          hash ^= position_.GetHashCode();
          if (PosRelatively != 0) hash ^= PosRelatively.GetHashCode();
          if (Radius != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Radius);
          if (Deceleration != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Deceleration);
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          position_.WriteTo(output, _repeated_position_codec);
          if (PosRelatively != 0) {
            output.WriteRawTag(40);
            output.WriteInt32(PosRelatively);
          }
          if (Radius != 0F) {
            output.WriteRawTag(53);
            output.WriteFloat(Radius);
          }
          if (Deceleration != 0F) {
            output.WriteRawTag(61);
            output.WriteFloat(Deceleration);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (ID != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ID);
          }
          if (Name.Length != 0) {
            output.WriteRawTag(18);
            output.WriteString(Name);
          }
          if (Remark.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Remark);
          }
          position_.WriteTo(ref output, _repeated_position_codec);
          if (PosRelatively != 0) {
            output.WriteRawTag(40);
            output.WriteInt32(PosRelatively);
          }
          if (Radius != 0F) {
            output.WriteRawTag(53);
            output.WriteFloat(Radius);
          }
          if (Deceleration != 0F) {
            output.WriteRawTag(61);
            output.WriteFloat(Deceleration);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (ID != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(ID);
          }
          if (Name.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
          }
          if (Remark.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Remark);
          }
          size += position_.CalculateSize(_repeated_position_codec);
          if (PosRelatively != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(PosRelatively);
          }
          if (Radius != 0F) {
            size += 1 + 4;
          }
          if (Deceleration != 0F) {
            size += 1 + 4;
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(CSVRow other) {
          if (other == null) {
            return;
          }
          if (other.ID != 0) {
            ID = other.ID;
          }
          if (other.Name.Length != 0) {
            Name = other.Name;
          }
          if (other.Remark.Length != 0) {
            Remark = other.Remark;
          }
          position_.Add(other.position_);
          if (other.PosRelatively != 0) {
            PosRelatively = other.PosRelatively;
          }
          if (other.Radius != 0F) {
            Radius = other.Radius;
          }
          if (other.Deceleration != 0F) {
            Deceleration = other.Deceleration;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 34:
              case 37: {
                position_.AddEntriesFrom(input, _repeated_position_codec);
                break;
              }
              case 40: {
                PosRelatively = input.ReadInt32();
                break;
              }
              case 53: {
                Radius = input.ReadFloat();
                break;
              }
              case 61: {
                Deceleration = input.ReadFloat();
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                ID = input.ReadInt32();
                break;
              }
              case 18: {
                Name = input.ReadString();
                break;
              }
              case 26: {
                Remark = input.ReadString();
                break;
              }
              case 34:
              case 37: {
                position_.AddEntriesFrom(ref input, _repeated_position_codec);
                break;
              }
              case 40: {
                PosRelatively = input.ReadInt32();
                break;
              }
              case 53: {
                Radius = input.ReadFloat();
                break;
              }
              case 61: {
                Deceleration = input.ReadFloat();
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  #endregion

}

#endregion Designer generated code
