﻿using UnityEngine;
using System.Collections;

public static class Configuration
{
   public static int COINS = 0;
   public static bool UNLOCKALLBOSSES = false;
   public static bool UNLOCKALLSIDEKICKS = false;
   public static bool AllowAchievements = true;
   public static bool DEATHMODE = false;
   public static bool DIEQUICK = false;
   public static bool DONTDIE = false;
   public static bool ENEMIES = true;
   public static bool FORCE_MOVIE = false;
   public static bool HIDEHUD = false;
   public static float GAMESPEED = 1.0f;
   public static bool FIRSTTIME = true;
    public static string GAME_FONT = "fonts/GrilledCheese BTN Toasted.ttf";
    public static string GAME_BM_FONT = "fonts/gameFont.fnt";
    public static string HEADING_BM_FONT = "fonts/headingFont.fnt";
}
