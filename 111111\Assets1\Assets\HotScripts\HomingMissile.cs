using UnityEngine;
using System.Collections;
using DG.Tweening;
public class HomingMissile : EnemyMissile
{
    [SerializeField] private Sprite defaultSprite;
    [SerializeField] private GameObject trail;
    [SerializeField] private GameObject boost;
    [HideInInspector] public bool isHoming;
    [HideInInspector] public float homingMultiplier = 1;
    //MotionStreak* streak; TODO
    float myAngle;
    private float _speed = 14f;
    private float _homingDuration = 2;

    private void Awake()
    {
        tweenId = "Homing" + GetInstanceID().ToString();
        schedulerId = "HomingS" + GetInstanceID().ToString();
    }

    public override void Init()
    {
        if (hasHit)
        {
            return;
        }
        base.Init();
        radiusSQ = 2f;
        myAngle = transform.eulerAngles.z;
        allowPing = true;
        DOTween.Sequence().SetId(schedulerId).AppendInterval(_homingDuration).AppendCallback(StopHoming).Play();
        isHoming = true;
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (isHoming)
            {
                float rotationSpeed = 50f;
                float dir = Vector2.SignedAngle(Vector2.right, player.transform.position - transform.position);
                float rotationDir = dir < 0 ? 360 + dir : dir;
                myAngle = Globals.MoveAngleTowards(myAngle, rotationDir, rotationSpeed * Time.deltaTime * homingMultiplier);
                var rot = Quaternion.AngleAxis(myAngle, Vector3.forward);
                transform.rotation = rot;
            }
            transform.position = new Vector2(transform.position.x + _speed * Mathf.Cos(Mathf.Deg2Rad * (transform.eulerAngles.z)) * Time.deltaTime * 0.6f, transform.position.y + _speed * Mathf.Sin(Mathf.Deg2Rad * (transform.eulerAngles.z)) * Time.deltaTime * 0.6f);
        }
    }

    private void StopHoming()
    {
        isHoming = false;
    }

    public override void RemoveMissile()
    {
        trail.SetActive(false);
        missileSprite.sprite = defaultSprite;
        base.RemoveMissile();
    }

    public void EnableTrail()
    {
        trail.SetActive(true);
    }

    //private void MakeTrail()
    //{
    //    bool didFindTrail = false;
    //    GameObject trail = null;
    //    foreach (GameObject g in trailObjects)
    //    {
    //        if (!g.activeInHierarchy)
    //        {
    //            trail = g;
    //            didFindTrail = true;
    //            break;
    //        }
    //    }
    //    if (!didFindTrail)
    //    {
    //        return;
    //    }
    //    trail.transform.SetWorldPosition(transform.position.x - Globals.CocosToUnity(10) + Random.value * Globals.CocosToUnity(20) + (Globals.CocosToUnity(50) * Mathf.Sin(Mathf.Deg2Rad*(transform.eulerAngles.z + 90))),
    //        transform.position.y - Globals.CocosToUnity(10) + Random.value * Globals.CocosToUnity(20) + (Globals.CocosToUnity(50) * Mathf.Sin(Mathf.Deg2Rad*(transform.eulerAngles.z + 90))));
    //    trail.transform.SetScale(0.6f + Random.value * 0.4f);
    //    trail.transform.SetRotation(transform.eulerAngles.z);
    //    trail.gameObject.SetActive(true);
    //    trail.transform.DOScale(Vector3.zero, 0.2f + Random.value * 0.3f).OnComplete(() => trail.SetActive(false));
    //}

    public static void Create()
    {

    }

    public void CreateWithHomingDuration(float homingDuration)
    {
        if (hasHit)
        {
            return;
        }
        myAngle = transform.eulerAngles.z;
        allowPing = true;
        _homingDuration = homingDuration;
        Init();
        //DOTween.Sequence().SetId(schedulerId).AppendInterval(homingDuration).AppendCallback(StopHoming).Play();
        //isHoming = true;
        radiusSQ = 0.7f;
        //base.Init();
    }

    public float CalcAngle(Vector2 p1, Vector2 p2)
    {
        return 0;
    }

    public float Modulus(float a, float b)
    {
        int result = (int)(a / b);
        return a - (float)(result) * b;
    }


    public void SetSpeed(float spd)
    {
        _speed = spd;
    }

    public void ResetBoostPosition()
    {
        boost.transform.localPosition = new Vector2(missileSprite.bounds.extents.x+0.35f, 0);
    }

    public void EnableBoost()
    {
        boost.gameObject.SetActive(true);
    }

    public void DisableBoost()
    {
        boost.gameObject.SetActive(false);
    }
}
