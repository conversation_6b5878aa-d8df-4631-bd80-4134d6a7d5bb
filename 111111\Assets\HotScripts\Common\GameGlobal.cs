using UnityEngine;
using System;
using System.Collections;

public class GameGlobal
{
    public static string channelFileName = "CurChannel";
    public static string versionFileName = "versionName";
    public static string dllVersionFileName = "dllVersion";
    public static bool openConsole = true;
    public static string channelStr = "None";
    //这个包名主要是用来再次更整包的时候传到aar里面
    public static string curPackageName = "com.deer.ymtt";


    public static string subChannelStr = "";
    public static string cdnUrl = "https://cdnbpmweb.emaygames.cn/bpm-01/";

    public static string url_zone_
    {
        get
        {
            return "http://builder/zone.lua?t=" + DateTime.Now.Ticks;
        }
    }

    public static string url_zone2_
    {
        get
        {
            return "http://builder2/zone.lua?t=" + DateTime.Now.Ticks;
        }
    }

    public static string url_zone3_
    {
        get
        {
            return "http://builder3/zone.lua?t=" + DateTime.Now.Ticks;
        }
    }

    public static string url_ver_
    {
#if UNITY_IPHONE
        get 
        {
            return "http://*************/deer/hot/ios/" + versionFileName + "?t=" + DateTime.Now.Ticks; 
        }
#else
        get
        {
            return cdnUrl + versionFileName;
        }
#endif
    }

    public static string url_ver2_
    {
#if UNITY_IPHONE
        get 
        {
            return "http://urlver2/deer/hot/ios/" + versionFileName + "?t=" + DateTime.Now.Ticks; 
        }
#else
        get
        {
            return "http://urlver2/deer/hot/android/" + versionFileName + "?t=" + DateTime.Now.Ticks;
        }
#endif
    }

    public static string url_ver3_
    {
#if UNITY_IPHONE
        get 
        {
            return "http://urlver3/deer/hot/ios/" + versionFileName + "?t=" + DateTime.Now.Ticks; 
        }
#else
        get
        {
            return "http://urlver3/deer/hot/android/" + versionFileName + "?t=" + DateTime.Now.Ticks;
        }
#endif
    }
    
    // 游戏包名
    public const string packName = "selfpackname";

    // 客户端异常上报地址
    public static string url_report = "http://**************:83/collector/reportex?";

    public static bool needMix = false;
    public const string abFilePath = "hx/hx_1";
    public static readonly byte[] abFileKey = {11,58,68,47,25,77,120,34,53,86,110};
}
