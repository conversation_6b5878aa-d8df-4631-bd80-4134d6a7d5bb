﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WaveStructure : MonoBehaviour
{

    public struct SeverskyInfo
    {
        public int _type;
        public Vector2 _position;
        public float _rotation;
        public float _stickToFormationFor_Sec;
        public bool outOfScreenLeft;
        public bool isGenerated;
    };


    public struct EnemyInfo
    {
        public Attributes _stats;
        public int _numberOfEnemiesOnScreen;
        public int _spawnCount;
        public bool _generateAlert;
    };

    public SeverskyInfo sInfo;
    public EnemyInfo _eInfo;
    public int enemyType;

    public static WaveStructure create(int type)
    {
        WaveStructure node = new WaveStructure();
        if (node)
        {
            node.enemyType = type;
            //node.autorelease();
            return node;
        }
        Destroy(node);
        return null;
    }

    public static WaveStructure createWithStats(int type, EnemyInfo eInfo)
    {
        WaveStructure node = new WaveStructure();
        if (node)
        {
            node.enemyType = type;
            node._eInfo = eInfo;
            //node->autorelease();
            return node;
        }
        //CC_SAFE_DELETE(node);
        Destroy(node);
        return null;
    }

    public static EnemyInfo createInfo()
    {
        EnemyInfo node = new EnemyInfo();
        node._stats = new Attributes();


        return node;
    }
}
