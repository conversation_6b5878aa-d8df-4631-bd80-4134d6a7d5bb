using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Rendering;
public class SplashManager : MonoBehaviour
{

    [SerializeField] private GameObject report;
    private bool didEnd = false;
    private SplashState currentState = SplashState.APPLE;
    bool skipVideo = false;

    public enum  SplashState
    {
        APPLE,
        WERPLAY,
        FTUX,
        END
    }

    private void Awake()
    {
        
#if UNITY_ANDROID || UNITY_IOS
        Globals.mobileControls = true;
        //if (Input.GetJoystickNames().Length > 0)
        //{
        //    Globals.isJoystickConnected = true;
        //    Globals.mobileControls = false;
        //}
        Application.targetFrameRate = 60;

#else
        Globals.mobileControls = false; 
        // 暂时注释掉模拟设备
        //if (Input.GetJoystickNames().Length > 0)
        //{
        //    Globals.isJoystickConnected = true;
        //}
#endif
    }

    public void Start()
    {
        DontDestroyOnLoad(report);
        //首次登陆
        //PlayerPrefs.DeleteAll();
        if (PlayerPrefs.GetInt("firstLaunch", 1) == 1)
        {
            PlayerPrefs.SetInt("isAssistMode", 1);
            Globals.isAssistMode = true;
        }
        else
        {
            Globals.isAssistMode = PlayerPrefs.GetInt("isAssistMode")==1?true:false;
        }
        currentState = SplashState.WERPLAY;
        EndOfSplash();
    }


    public void FirstLaunch()
    {
        //        SpriteFrameCache::getInstance().addSpriteFramesWithFile("res/Transitions/electricTransition.plist");

        //        SkeletonAnimation* skAnimation = SkeletonAnimation::createWithJsonFile("res/Intro/FTUX_INTRO.json", "res/Intro/FTUX_INTRO.atlas");
        //        this.addChild(skAnimation);
        //        skAnimation.setPosition(winSize / 2);
        //        skAnimation.setAnimation(0, "animation", false);
        //        Shared::rescale(skAnimation, 0.78f);
        //        Shared::playSound("res/Intro/introVideoPart1.mp3");
        //        const float OFFSETX = (1334.0f - (1334.0f * screenScaleValueX)) / 1.0f;
        //        const float OFFSETY = (750.0f - (750.0f * screenScaleValueY)) / 1.0f;
        //        Sprite* blackSpriteTop = Sprite::create("res/Backgrounds/black.png");
        //        Sprite* blackSpriteBottom = Sprite::create("res/Backgrounds/black.png");
        //        this.addChild(blackSpriteTop, INT_MAX - 1);
        //        this.addChild(blackSpriteBottom, INT_MAX - 1);
        //        float blackSpritesScaleX = 1;
        //        float blackSpritesScaleY = 1;
        //        if (aspectRatio < 1.7)
        //        {
        //            blackSpritesScaleX = winSize.width / blackSpriteTop.getContentSize().width;
        //            blackSpritesScaleY = ((-OFFSETY) / blackSpriteTop.getContentSize().height) / screenScaleValueY;
        //            blackSpriteTop.setScale(blackSpritesScaleX, blackSpritesScaleY);
        //            blackSpriteBottom.setScale(blackSpritesScaleX, blackSpritesScaleY);
        //            blackSpriteTop.setPosition(winSize.width / 2, winSize.height);
        //            blackSpriteBottom.setPosition(winSize.width / 2, 0);
        //        }
        //        else if (aspectRatio > 1.8)
        //        {
        //            blackSpritesScaleX = ((-OFFSETX) / blackSpriteTop.getContentSize().width) / screenScaleValueX;
        //            blackSpritesScaleY = winSize.height / blackSpriteTop.getContentSize().height;
        //            blackSpriteTop.setScale(blackSpritesScaleX, blackSpritesScaleY);
        //            blackSpriteBottom.setScale(blackSpritesScaleX, blackSpritesScaleY);
        //            blackSpriteTop.setPosition(winSize.width, winSize.height / 2);
        //            blackSpriteBottom.setPosition(0, winSize.height / 2);
        //        }
        //        else
        //        {
        //            blackSpriteBottom.setVisible(false);
        //            blackSpriteTop.setVisible(false);
        //        }
        //        skAnimation.setEventListener( [=](spTrackEntry * entry, spEvent * event){

        //        if (strcmp(event.data.name, "end") == 0)
        //        {
        //        this.loadFTUX();
        //    }

        //        if(strcmp(event.data.name, "FULL_SCREEN_EXPLOSION") == 0)
        //        {
        //        float timeForTransition = 0.0f;
        //        if (SpriteFrameCache::getInstance().getSpriteFrameByName("ExplosionTransition1.png"))
        //        {
        //            Sprite* energyTransition = Sprite::createWithSpriteFrameName("ExplosionTransition1.png");
        //            if (energyTransition)
        //            {
        //                energyTransition.setVisible(false);
        //                energyTransition.runAction(Sequence::create(DelayTime::create(timeForTransition), Show::create(), Shared::createAnimation("ExplosionTransition%d.png", 1, 27, false, 0.04f), RemoveSelf::create(), NULL));
        //                this.addChild(energyTransition, INT_MAX - 2);
        //                energyTransition.setPosition(Director::getInstance().getWinSize() / 2);
        //                energyTransition.setScale(Director::getInstance().getWinSize().width / energyTransition.getContentSize().width, Director::getInstance().getWinSize().height / energyTransition.getContentSize().height);
        //            }
        //        }
        //    }

        //        if(strcmp(event.data.name, "BLUE_EFFECT") == 0)
        //        {
        //        float timeForTransition = 0.0f;
        //        if (SpriteFrameCache::getInstance().getSpriteFrameByName("electricTransition1.png"))
        //        {
        //            Sprite* energyTransition = Sprite::createWithSpriteFrameName("electricTransition1.png");
        //            if (energyTransition)
        //            {
        //                energyTransition.setVisible(false);
        //                energyTransition.runAction(Sequence::create(DelayTime::create(timeForTransition), Show::create(), Shared::createAnimation("electricTransition%d.png", 1, 11, false, 0.04f), FadeOut::create(0.05f), RemoveSelf::create(), NULL));
        //                this.addChild(energyTransition, INT_MAX - 2);
        //                energyTransition.setPosition(Director::getInstance().getWinSize() / 2);
        //                energyTransition.setScale(Director::getInstance().getWinSize().width / energyTransition.getContentSize().width, Director::getInstance().getWinSize().height / energyTransition.getContentSize().height);
        //            }
        //        }
        //    }

        //    });

        //#if UNITY_STANDALONE||UNITY_TVOS
        //    PHButton* pb = PHButton::create(GameData::getInstance().getMenuData(GAME_DATA_SCENE::TUTORIAL).at("skipButton").asString(), 1.0f, [=](Ref * sender){
        //        this.loadFTUX();
        //});
        //this.addChild(pb, INT_MAX);
        //pb.setPosition(Director::getInstance().getWinSize().width - 50, Director::getInstance().getWinSize().height - 10);
        //Shared::rescale(pb, 1.0f);
        //#else
        //    Label* skipButtonLabel = Label::createWithTTF(GameData::getInstance().getMenuData(GAME_DATA_SCENE::TUTORIAL).at("skipButton").asString(), GAME_FONT, 100);
        //    MenuItemLabel *skipButton = MenuItemLabel::create(skipButtonLabel, [=](Ref *sender){
        //        this.loadFTUX();
        //    });

        //    skipButton.setScale(0.35);
        //    //Shared::rescale(skipButton, 0.35);

        //    //Shared::fontToCustom(skipButtonLabel);
        //    Shared::rescale(skipButtonLabel, 1.0f);
        //    skipButtonLabel.enableOutline(Color4B::BLACK, 3.0f);
        //    //skipButton.setScale(0.35f);
        //    Menu *m = Menu::create(skipButton,NULL);
        //    this.addChild(m, INT_MAX);
        //    m.setPosition(Director::getInstance().getWinSize().width - 50, Director::getInstance().getWinSize().height - 10);
        //    skipButton.setAnchorPoint(Point(1,1));
        //    //skipButton.runAction(RepeatForever::create(Sequence::create(ScaleTo::create(1, 0.36 / screenScaleValueX, 0.36 / screenScaleValueY),ScaleTo::create(1, 0.35 / screenScaleValueX, 0.35 / screenScaleValueY), NULL)));

        //    skipButton.runAction(RepeatForever::create(Sequence::create(ScaleTo::create(1, 0.36),ScaleTo::create(1, 0.35), NULL)));

        //#endif

        //auto keyListener = EventListenerKeyboard::create();
        //keyListener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)
        //    {

        //    if (keyCode == EventKeyboard::KeyCode::KEY_ESCAPE)
        //    {
        //        this.loadFTUX();
        //    }
        //};
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);
        LoadFTUX();

    }

    public void LoadFTUX()
    {

        if (this.didEnd)
            return;


        didEnd = true;

#if UNITY_STANDALONE
        string fileName = ("res/GameStates/gameState5.plist");
#else
    string fileName = ("res/GameStates/gameState5Mobile.plist");
#endif

        //    if (FileUtils::getInstance().isFileExist(fileName))
        //    {
        //        ValueMap v = FileUtils::getInstance().getValueMapFromFile(fileName);
        //        for (auto keyVal : v)
        //                {
        //    string key = keyVal.first;

        //    Value val = keyVal.second;
        //    if (val.getType() == Value::Type::INTEGER)
        //    {
        //        UserDefault::getInstance().setIntegerForKey(key.c_str(), val.asInt());
        //    }
        //    if (val.getType() == Value::Type::STRING)
        //    {
        //        UserDefault::getInstance().setStringForKey(key.c_str(), val.asString());
        //    }
        //    if (val.getType() == Value::Type::BOOLEAN)
        //    {
        //        UserDefault::getInstance().setBoolForKey(key.c_str(), val.asBool());
        //    }
        //}
        //            }
        PlayerPrefs.SetInt("missionsCompleted", -1);
        GameData.instance.fileHandler.currentMission = 0;
        GameData.instance.fileHandler.SaveData(false);
        SceneManager.LoadScene("VideoScene");
    }

    public void GetVideoName()
    {

    }

    public void GetVideoNameMac()
    {

    }

    private void Init()
    {

    }

    private void EndOfWerplaySplash()
    {

    }

    private void update(float dt)
    {

    }


    private void EndOfSplash()
    {
        if (currentState == SplashState.WERPLAY)
        {
            if (PlayerPrefs.GetInt("firstLaunch", 1) == 1)
            {
                FirstLaunch();
                currentState = SplashState.FTUX;

            }
            else
            {
                currentState = SplashState.END;
                //SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex + 1);
                SceneManager.LoadScene("GameStart");
            }

        }
    }
}
