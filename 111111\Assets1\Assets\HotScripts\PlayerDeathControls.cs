using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using UnityEngine.SceneManagement;
public class PlayerDeathControls : MonoBehaviour
{
    private Vector2 acceleration;
    private PlayerController player;
    [SerializeField] private SkeletonAnimation playerFlame;

    private string tweenId;
    private string schedulerId;

    private bool scheduleUpdate = false;
    public void Init()
    {
        tweenId = "PDC" + GetInstanceID();
        schedulerId = "PDCS" + GetInstanceID();
        player = GameManager.instance.player;
        player.playerMovement.SetSeeMouse(false);
        //float rot = player.GetSkeletonAnimation().transform.GetRotation();
        //DOTween.To(() => rot, x => rot = x, 0, 0.5f).OnUpdate(() =>
        //{

        //    player.playerMovement.RotatePlayer(rot); ;
        //});
        //DOTween.Sequence().SetId(tweenId).Append(player.GetSkeletonAnimation().transform.DORotate(Vector3.zero, 0.5f)).Play();

       GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1, player.transform.position, false, 4, 0.35f, 0);
        Observer.DispatchCustomEvent("DeathControlsInit");
        float val = 1;

        DOTween.To(() => val, x => val = x, 0.75f, 1).OnUpdate(() =>
         {
             GameManager.instance.timeManager.SetTimescale(val);
         }
        ).SetEase(Ease.OutQuad).SetUpdate(true);

        DOTween.To(() => Globals.zoomValueWhileGame, x => Globals.SetZoomValueWhileGame(x), 0.2f, 1);

        Globals.LEFTBOUNDARY = Globals.CocosToUnity(-10000);
        Globals.RIGHTBOUNDARY = Globals.CocosToUnity(10000);

        if (player.transform.position.y < Globals.LOWERBOUNDARY+Globals.CocosToUnity(300))
        {

            if (player.transform.localScale.x < 0)
                player.playerMovement.SetPlayerRotation(120);
            else
            {
                player.playerMovement.SetPlayerRotation(80);

            }
        }
        if (player.playerMovement.playerDir < 90 || player.playerMovement.playerDir > 270)
        {
            player.playerMovement.SetPlayerRotation(30);
            acceleration.y = Mathf.Cos(Mathf.Deg2Rad * player.playerMovement.playerDir) * 10;
            acceleration.x = Mathf.Sin(Mathf.Deg2Rad * player.playerMovement.playerDir) * 10;
        }
        else
        {
            player.playerMovement.SetPlayerRotation(150);
            acceleration.y = Mathf.Cos(Mathf.Deg2Rad * player.playerMovement.playerDir) * 10*-1;
            acceleration.x = Mathf.Sin(Mathf.Deg2Rad * player.playerMovement.playerDir) * 10*-1;

            //player.GetSkeletonAnimation().transform.SetRotation(270 - 120);
        }
        //Debug.Break();
        scheduleUpdate = true;
        float rot3 = player.playerMovement.playerDir;
        float target = rot3 < 90 ? 0f : 180f;
        DOTween.To(() => rot3, x => rot3 = x, target, 10f).OnUpdate(() =>
        {
            player.playerMovement.RotatePlayer(rot3);
        });
        //DOTween.Sequence().SetId(tweenId).Append(player.GetSkeletonAnimation().transform.DORotate(Vector3.forward * 90, 10f)).Play();
        SpriteRenderer blackScreen = GameManager.instance.backgroundFadeLayer;
        //blackScreen.SetOpacity(0);
        //blackScreen.gameObject.SetActive(false);

        DOTween.Sequence().SetId(schedulerId).SetUpdate(true).AppendInterval(2.5f).AppendCallback(() =>
        {
            Observer.DispatchCustomEvent("KatStarFocus");
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.energy29ElectricityChargeHit);
            float val = 0.25f;
            DOTween.To(() => val, x => val = x, 0, 0.1f).OnUpdate(() =>
                 {
                     AudioManager.instance.SetMusicVolume(val);
                 });
            //TODO
            //experimental::AudioEngine::play2d("res/Sounds/SFX/InProgress/SFX - Energy 29 Electricity Charge Hit.mp3", false, 1.0f);

            //this.runAction(EaseOut::create(ActionFloat::create(0.1f, 0.25f, 0, [](float val){

            //    experimental::AudioEngine::setVolume(gameplayMusicId, val);

            //}), 2));
        }).Play();


        Observer.RegisterCustomEvent(gameObject, "WhiteScreen", () =>
        {
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.25f * 3.5f).AppendCallback(() =>
            {
                if (Globals.introComplete)
                {
                    SceneManager.LoadScene("VideoScene");
                }

            }).Play();
        });
        DOTween.To(() => Globals.g_SoundVolume, x => Globals.g_SoundVolume = x, 0, 0.25f);


    }


    private void Update()
    {
        if (!scheduleUpdate)
            return;
        player.transform.position= new Vector2(player.transform.position.x + acceleration.x * Time.deltaTime * 0.60f,player.transform.position.y + acceleration.y * Time.deltaTime * 0.60f);
        acceleration.x = acceleration.x * 0.99999f;
        acceleration.y = acceleration.y - 0.03f * Time.deltaTime * 0.60f;
    }
}
