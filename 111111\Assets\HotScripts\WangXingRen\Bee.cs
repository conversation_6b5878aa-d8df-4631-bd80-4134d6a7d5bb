using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class Bee : Enemy
{
    private float originalSpeed;
    private float ScaleFactor = 0.35f;
    private float sinY = 0;
    private bool movingLeft;
    private float bossInitialPositionY = 1500;
    private float sinHeight = 250;
    private float sineIncrement = 0.005f;
    private int animationState;
    public void CreateAsBoss()
    {
        Init();
        Boss();
        AudioManager.instance.PlaySound(AudioType.Enemy,Constants_Audio.Audio.spIntro,1f);
    }

    public override void Init()
    {
        if (initialized)
            return;
        schedulerId = "BKS" + GetInstanceID();
        tweenId = "BK" + GetInstanceID();
        base.Init();
        InitStats();
        animationState = 0;
        allowStack = false;
        movingLeft = false;
        allowRelocate = false;
        //enemySprite.state.SetAnimation(0, "flyingEnemy1", true);
        //transform.SetWorldPosition(player.transform.position.x - Globals.CocosToUnity(1600), Globals.CocosToUnity(700 + Random.value * 500));
        isBoss = false;
        scheduleUpdate = true;

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Red);
    }

    private void InitStats()
    {
        //FightProp.移动速度 = 4.5f + Random.value;//bhut taiz
        //FightProp.转向速度 = 0.5f + Random.value;
        //FightProp.HP = GameData.instance.fileHandler.TrainingLevel * 50;
        //stats.bulletDamage = 300;
        //missileDamage = 300;
        //FightProp.子弹速度 = 7;
        //FightProp.MaxHP = FightProp.HP;
        //stats.coinAwarded = 8;
        //stats.xp = FightProp.MaxHP;
        //originalSpeed = FightProp.移动速度;
        //enemyCollisionRadius = 1.5f;
        //baseStats.speed = FightProp.移动速度;//bhut taiz
        //baseStats.turnSpeed = FightProp.转向速度;
        //baseStats.health = GameData.instance.fileHandler.TrainingLevel * 50;
        //baseStats.bulletDamage = 300;
        //baseStats.missileDamage = 300;
        //baseFightProp.子弹速度 = 7;
        //baseStats.maxHealth = FightProp.HP;
        //baseStats.coinAwarded = 8;
        //baseStats.xp = baseStats.maxHealth;


        //DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(2f).SetLoops(-1).Play();
    }

    public override void Destroy()
    {
        DOTween.Kill(schedulerId);
        DOTween.Kill(tweenId);
        base.Destroy();
    }

    private void EnemySpecialAttributes()
    {
        originalSpeed = FightProp.移动速度.Value;
    }

    private void Update()
    {
        CheckUpdate();
        //if (movingLeft)
        //{
        //    transform.SetWorldPositionX(transform.position.x - FightProp.移动速度 * Time.deltaTime * 0.60f);
        //    if (transform.position.x - playerPosition.x < Globals.CocosToUnity(-1000) || transform.position.x < Globals.LEFTBOUNDARY)
        //    {

        //        movingLeft = false;
        //        //enemySprite.transform.SetScale(isBoss ? 0.35f : 0.225f);
        //        //enemySprite.transform.SetScaleX(isBoss ? 0.35f : 0.225f);

        //    }
        //}
        //else
        //{
        //    transform.SetWorldPositionX(transform.position.x + FightProp.移动速度 * Time.deltaTime * 0.60f);
        //    if (transform.position.x - playerPosition.x > Globals.CocosToUnity(1000) || transform.position.x > Globals.RIGHTBOUNDARY)
        //    {
        //        movingLeft = true;
        //        //enemySprite.transform.SetScale(isBoss ? 0.35f : 0.225f);
        //        //enemySprite.transform.SetScaleX(isBoss ? -0.35f : -0.225f);
        //    }
        //}
        Vector2 playerPosition = player.transform.position;
        if (transform.position.x - playerPosition.x > Globals.CocosToUnity(2500) || transform.position.x - playerPosition.x < Globals.CocosToUnity(-2500))
        {
            FightProp.移动速度.Value++;

        }
        else
        {
            if (originalSpeed != 0)
            {
                FightProp.移动速度.Value = originalSpeed;
            }
            else
            {
                originalSpeed = FightProp.移动速度.Value;
            }
            
        }
    }

    //不走physicsManager的计数，这里要加计数以及刷波数
    //public void KillCount()
    //{
    //    if (!_allowKillPoint) return;
    //    GameData.instance.fileHandler.totalKills++;
    //    GameManager.instance.killsThisRun++;
    //    GameManager.instance.playerHud.survivalCurPoint = GameManager.instance.killsThisRun;
    //    if (Globals.gameType == GameType.Survival)
    //    {

    //        if (GameManager.instance.killsThisRun == (Globals.totalEnemiesInCurrentWave + Globals.enemiesTillLastWave))
    //        {

    //            Globals.enemiesTillLastWave += Globals.totalEnemiesInCurrentWave;
    //            DOTween.Sequence().AppendInterval(1f).AppendCallback(
    //                () =>
    //                {
    //                    Observer.DispatchCustomEvent("newWave");
    //                }).Play();
    //        }
    //    }
    //}
    private void Shoot()
    {
        //shootRandom = Random.Range(0, 10000);
        //if (shootRandom > skillShootProbability)
        //{
        //    return;
        //}
        //Bomb newBomb = null;
        //bool didFindBomb = false;
        //foreach (Bomb b in GameSharedData.Instance.enemyBombPool)
        //{
        //    if (!b.isInUse)
        //    {
        //        newBomb = b;
        //        newBomb.isInUse = true;
        //        didFindBomb = true;
        //        break;
        //    }

        //}
        //if (!didFindBomb)
        //{
        //    return;
        //}
        //newBomb.Init();
        //newBomb.duration = 20;
        //if (player.Mode ==  PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        //{

        //    AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spShoot,1,transform.position);
        //}
        //if (isBoss)
        //{
        //    newBomb.radiusSQ = 0.7f;
        //    newBomb._doExplodeOnWater = true;
        //}
        //else
        //{
        //    newBomb.radiusSQ = 0.2f;
        //    newBomb.missileSprite.transform.localScale = new Vector2(newBomb.missileSprite.transform.localScale.x * 0.75f, newBomb.missileSprite.transform.localScale.y * 0.75f);
        //}
        //newBomb.transform.position = transform.position;
        //newBomb.SetDamage(missileDamage);

        //if (movingLeft)
        //{
        //    newBomb.SetHorizontalVelocity(-FightProp.移动速度 / 500);
        //    newBomb.missileSprite.transform.SetScaleX(-newBomb.transform.localScale.x);
        //    newBomb.missileSprite.transform.SetRotation(20);
        //}
        //else
        //{
        //    newBomb.SetHorizontalVelocity(FightProp.移动速度 / 500);

        //}

        //GameSharedData.Instance.enemyMissilesInUse.Add(newBomb);
        //newBomb.RemoveAfterDuration();

    }

    private void ChangeAnimation()
    {
        if (animationState == 0)
        {
            animationState = 1;
            //enemySprite.state.SetAnimation(0, "flying2", true);
        }

        else
        {

            animationState = 0;
            //enemySprite.state.SetAnimation(0, "flying", true);

        }
    }

    private void Boss()
    {
        isBoss = true;
        int bossNumber = 2;
        allowRelocate = (false);

        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
            prizeID = 0;
        //enemySprite.transform.SetScale(0.35f);
        ScaleFactor = 0.35f;
        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
        FightProp.Radius = 2;
        originalSpeed = FightProp.移动速度.Value;
        //transform.SetWorldPosition(player.transform.position.x - Globals.CocosToUnity(1400), bossInitialPositionY);
        //enemySprite.state.SetAnimation(0, "flying", true);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.35f).AppendCallback(() =>
        {
            DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(1.5f).SetLoops(-1).Play();
        }).Play();
        DOTween.Sequence().SetId(schedulerId).AppendCallback(ChangeAnimation).AppendInterval(4).SetLoops(-1).Play();
    }
}
