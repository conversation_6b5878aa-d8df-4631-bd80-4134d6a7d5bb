﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading.Tasks;
using DG.Tweening;
public class WaterMissile : EnemyMissile
{
    private float gravity;
    private float horizontalVelocity;
    private bool _particles = false;
    [HideInInspector] public bool attackMode;

    [SerializeField] private SpriteRenderer[] trail;
    [SerializeField] private GameObject boost;
    [SerializeField] private Sprite defaultSprite;
    [SerializeField] private Sprite activatedSprite;


    private void Awake()
    {
        tweenId = "WaterM" + GetInstanceID().ToString();
        schedulerId = "WaterMS" + GetInstanceID().ToString();
    }

    public override void Init()
    {
        CancelInvoke();
        transform.DOKill();
        gravity = 6.5f;
        duration = 12;
       SetDamage(10);
        radiusSQ = Globals.CocosToUnity(90);
        attackMode = false;
        boost.SetActive(false);
        missileSprite.sprite = defaultSprite;
        base.Init();
        RemoveAfterDuration();
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (attackMode)
            {
                gravity = gravity + 0.2f * Time.deltaTime * 60f;

            }
            else
            {
                gravity = gravity - 0.075f * Time.deltaTime * 60f;


                if (transform.position.y < Globals.LOWERBOUNDARY - Globals.CocosToUnity(50))
                {
                    if (gravity < 3)
                    {
                        gravity = 3;
                    }
                    horizontalVelocity /= 2;
                    gravity = gravity + 0.1f * Time.deltaTime * 60f;
                }
            }

            transform.position = new Vector2(transform.position.x + horizontalVelocity * Time.deltaTime * Globals.CocosToUnity(60f), transform.position.y + gravity * Time.deltaTime * Globals.CocosToUnity(60f));
            if (transform.position.y > Globals.UPPERBOUNDARY + 3)
            {
                createExplosion = false;
                RemoveMissile();
            }
        }
    }

    public void SetHorizontalVelocity(float val)
    {
        horizontalVelocity = val;
    }

    public void TurnOnAttackMode()
    {
        missileSprite.sprite = activatedSprite;
        boost.SetActive(true);
        attackMode = true;

        if (_particles)
        {
            MakeTrail(0.034f);
        }
    }

    public void TurnOnParticles(bool val)
    {
        _particles = val;
    }

    public void MakeTrail(float waitTime)
    {
        //while (attackMode)
        //{
        //    bool foundTrail = false;
        //    SpriteRenderer t;
        //    while (!foundTrail)
        //    {
        //        for (int i = 0; i < trail.Length; i++)
        //        {
        //            if (trail[i].gameObject.activeInHierarchy)
        //            {
        //                t = trail[i];
        //                foundTrail = true;
        //                break;
        //            }
        //        }
        //    }

        //    trail->setPosition(missileSprite->getPosition().x - 10 + CCRANDOM_0_1() * 20 + (50 * sinf(CC_DEGREES_TO_RADIANS(missileSprite->getRotation() + 90))), missileSprite->getPosition().y - 10 + CCRANDOM_0_1() * 20 + (50 * cosf(CC_DEGREES_TO_RADIANS(missileSprite->getRotation() + 90))));
        //    trail->setScale(0.6 + CCRANDOM_0_1() * 0.4);
        //    trail->setCameraMask(GAMECAMERA);
        //    trail->setRotation(missileSprite->getRotation());
        //    trail->runAction(Sequence::create(ScaleTo::create(0.2 + CCRANDOM_0_1() * 0.3, 0), RemoveSelf::create(), NULL));
        //    yield return new WaitForEndOfFrame();
        //}
        
    }

    private void DisableTrailObject()
    {

    }

    

    public override void RemoveMissile()
    {
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        scheduleUpdate = false;
        boost.gameObject.SetActive(false);
        missileSprite.sprite = defaultSprite;
        isInUse = false;
        hasHit = false;
        base.RemoveMissile();
    }


    public void PlayRotateAnim(float rotateBy,float duration)
    {
        Invoke(nameof(TurnOnAttackMode), 5f);
        transform.DORotate(new Vector3(0, 0, rotateBy), duration);
    }

    
}
