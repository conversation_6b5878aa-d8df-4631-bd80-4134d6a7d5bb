﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using Newtonsoft.Json;

using UnityEngine;

using X.PB;

/// <summary>
/// 技能调度器
/// </summary>
[DisallowMultipleComponent]
public class SkillScheduler : MonoBehaviour
{
	/// <summary>
	/// 属于哪个生物
	/// </summary>
	public CreatureBase Creature { get; set; }

	/// <summary>
	/// 调度中的技能列表
	/// </summary>
	public List<SkillProps> Skills { get; } = new();

	/// <summary>
	/// 技能升级后(或新获得)
	/// </summary>
	public event System.Action<SkillScheduler, SkillProps> AfterSkillUp;

	protected void OnAfterSkillUp(SkillProps skill)
	{
		FireAfterSkillUp(skill);
	}

	public void FireAfterSkillUp(SkillProps skill)
	{
		AfterSkillUp?.Invoke(this, skill);
	}

	/// <summary>
	/// 加入技能
	/// </summary>
	public SkillProps AddSkill(int skillID)
	{
		var skill = Skills.FirstOrDefault(x => x.SkillID == skillID);
		// 如果已拥有此技能,则跳过
		if (skill != null)
		{
			return skill;
		}

		var csvRow_CatSkill = CatSkillScheme.Instance.GetItem(skillID);
		skill = new()
		{
			SkillScheduler = this,
		};

		// 非怪物加入技能时，提取装备给技能的提升
		if (Creature.FightProp.CreatureType is not CreatureType.CreatureTypeMonster)
		{
			if (LuaToCshapeManager.Instance.PlayerSkills != null)
			{
				skill.ActorSkill = LuaToCshapeManager.Instance.PlayerSkills.FirstOrDefault(x => x.SkillID == skillID);
			}
		}

		// 重要:最后设置技能配置,这样可以自动计算各属性的最终值
		skill.CsvRow_CatSkill = csvRow_CatSkill;

		// 加入时,保证立即可用
		skill.LastDoTime = Time.time - 2 * skill.CD.Value;
		Skills.Add(skill);

		//if (useScheduler)
		//{
		//    // 使用调度器的技能在加入后，立即发起一次
		//    DoSkill(skill).Forget();
		//}

		AfterSkillUp?.Invoke(this, skill);

		return skill;
	}

	/// <summary>
	/// 给技能添加提升(升级,指定效果)
	/// </summary>
	/// <param name="sillEffectID">&lt;0表示随机一个效果</param>
	public SkillProps AddSkillEffect(int skillID, int sillEffectID)
	{
		var skill = AddSkill(skillID);

		if (sillEffectID < 0)
		{
			skill.Upgrade_RandomEffect();
		}
		else
		{
			skill.AddEffect(sillEffectID);
		}

		AfterSkillUp?.Invoke(this, skill);

		return skill;
	}

	/// <summary>
	/// 获取技能列表的Json串 [{ SkillID:0, MaxLevel:6, SkillLvl: 0, Type: 0, CD: 0.0, MinCD: 0.0, Icon: "", SkillEffectIDs: [1,2...] }, ...]
	/// </summary>
	public string GetJson_Skills()
	{
		var rtn = Skills.Select(x => x.ToJson_Lua()).ToList();
		return JsonConvert.SerializeObject(rtn);
	}

	/// <summary>
	/// 启动调度器
	/// </summary>
	public void StartSchedule(float interval = 0.04f)
	{
		var token = this.GetCancellationTokenOnDestroy();
		Task_StartSchedule(token).Forget();

		//ScheduleTask ??= Observable.Interval(System.TimeSpan.FromSeconds(interval))
		//    .Where(_ => Time.deltaTime > 0 && Skills.Count > 0)
		//    .Subscribe(t =>
		//    {
		//        // 找出冷却好了的技能,使用它
		//        foreach (var skill in Skills.Where(x => x.LastDoTime + x.CD.Value <= Time.time))
		//        {
		//            DoSkill(skill).Forget();
		//        }
		//    })
		//    .AddTo(this);
	}

	private async UniTaskVoid Task_StartSchedule(CancellationToken token)
	{
		try
		{
			for (; ; await UniTask.NextFrame())
			{
				if (token.IsCancellationRequested) break;
				if (Time.deltaTime <= 0) continue;

				// 找出冷却好了的技能,使用它
				foreach (var skill in Skills.Where(x => x.LastDoTime + x.CD.Value <= Time.time))
				{
					DoSkill(skill).Forget();
				}
			}
		}
		catch (System.OperationCanceledException) { throw; }
		catch (MissingReferenceException) { }
		catch (System.Exception ex)
		{
			Debug.LogException(ex);
		}
	}

	/// <summary>
	/// 使用技能
	/// </summary>
	protected async UniTaskVoid DoSkill(SkillProps skill)
	{
		try
		{
			await UniTask.SwitchToMainThread(this.GetCancellationTokenOnDestroy());

			if (Creature.ShouldDoSkill(skill))
			{
				skill.LastDoTime = Time.time;
				Creature.DoSkill(skill).Forget();
			}
		}
		catch (System.OperationCanceledException) { throw; }
		catch (MissingReferenceException) { }
		catch (System.Exception ex)
		{
			Debug.LogException(ex);
		}
		//catch { }
	}

	/// <summary>
	/// 当前所有技能升至顶级(或不能再升)，随机效果
	/// </summary>
	/// <param name="unique"></param>
	public void Upgrade_MaxByRandom(bool unique = false)
	{
		Skills.ForEach(x => x.Upgrade_MaxByRandom(unique));
	}
}
