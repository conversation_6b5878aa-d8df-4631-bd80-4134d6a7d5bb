using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(AudioSource)) ]
public class SoundEffect3D : MonoBehaviour
{
    [SerializeField] private AudioSource source;
    [SerializeField] private AudioClip clip;
    [HideInInspector] public bool isInUse = false;

    bool flip = true;
    bool flip2 = true;

    public void PlayAudio(Vector2 position, AudioClip clip = null, float volume = 1, bool isLooping=false)
    {
        transform.position = position;
        source.volume = volume;
        source.clip = clip;
        source.Play();
        isInUse = true;
        if (!isLooping)
        {
            StartCoroutine(WaitForAudioClipToEnd());
        }
        else
        {
            source.loop = true;
        }
    }

    public void PlayAudio(AudioClip clip, Vector2 position,  float volume)
    {
        AudioSource.PlayClipAtPoint(clip, position, volume);
    }

    private IEnumerator WaitForAudioClipToEnd()
    {
        while (source.isPlaying)
        {
            yield return null;
        }
        isInUse = false;
    }

    public void StopLooping()
    {
        source.loop = false;
        isInUse = false;
        source.Stop();
    }

    public void TestAudioLoop()
    {
        if (flip)
        {
            source.Play();
            flip = false;
        }
        else
        {
            source.Stop();
            flip = true;
        }
    }

    public void TestAudioLoopAtPoint()
    {
        if (flip2)
        {
            AudioSource.PlayClipAtPoint(clip,Vector3.zero);
            flip2 = false;
        }
        else
        {
            source.Stop();
            flip2 = true;
        }
    }

}