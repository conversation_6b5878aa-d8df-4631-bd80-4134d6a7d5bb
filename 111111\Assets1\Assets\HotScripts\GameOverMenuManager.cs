using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using Spine;
using Spine.Unity;
using DG.Tweening;
using UnityEngine.UI;
public class GameOverMenuManager : MonoBehaviour
{
    private List<MainMenuButton> menuArray;
    private int selectedButton;
    //[SerializeField] private Image blackOverlay;
    [SerializeField] private GameOverWinDesktop gameOverWinDesktop;
    [SerializeField] private GameOverLoseDesktop gameOverLoseDesktop;
    [SerializeField] private GameOverMobile gameOverMobile;

    //MenuItemImage* bossButton;
    [HideInInspector] public bool isEndScreenShown = false;
    [HideInInspector] public bool missionSequencePlayed = false;


    [HideInInspector] public int totalKillsCount;
    [HideInInspector] public int totalCoinsCount;
    [HideInInspector] public int currentMissionPlayed = 0;

    [HideInInspector] public bool disableMenu = false;
    [HideInInspector] public bool hasChestDropped = false;

    [HideInInspector] public int xpReceivedFromMission = 0;
    [HideInInspector] public int rewardCoins = 0;
    [HideInInspector] public bool isBossDefeated;

    public void Init()
    {

        Globals.backFromGamePlay = true;
        disableMenu = false;
        //Director::getInstance().getOpenGLView().setCursorVisible(false);

        DOTween.Sequence().AppendInterval(0.45f).AppendCallback(() => {
            //TODO Ask Bilal Bhai Doesnt Seem Imp
            //Shared::pauseRecursive(Director::getInstance().getRunningScene(), true);
            //Shared::pauseRecursive(this, false);
            //if (this.getParent())
            //{
            //    if (this.getParent().getChildByTag(987))
            //    {
            //        this.getParent().getChildByTag(987).resume();
            //    }
            //}
        }).Play();
        
        isBossDefeated = false;
        totalKillsCount = 0;


        //TODO
        //fadeInLayer = Layer::create();
        //this.addChild(fadeInLayer, 11);
        //fadeInLayer.setOpacity(0);

        //DOTween.Sequence().Append(blackOverlay.DOFade(1, 1));


        //bg = SkeletonAnimation::createWithJsonFile("res/GameEndMenu/gameOver.json", "res/GameEndMenu/gameOver.atlas");
        //this.addChild(bg, 5);
        //bg.setPosition(winSize.width / 2, winSize.height / 2);
        //bg.setOpacity(0);
        //float bgScale = MAX(1 / this.getScaleX(), 1 / this.getScaleY());
        //bg.setScale(bgScale);
        //Shared::rescale(bg, 1);

        selectedButton = 0;




        //auto listener = EventListenerTouchAllAtOnce::create();
        //listener.onTouchesBegan = CC_CALLBACK_2(GameOverMenu::onTouchesBegan, this);
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);

        //keylistener = EventListenerKeyboard::create();
        //keylistener.onKeyPressed = CC_CALLBACK_2(GameOverMenu::onKeyPressed, this);
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

        //GamePad_Apple* ga = GamePad_Apple::create();
        //this.addChild(ga);
        NewSelection();

        currentMissionPlayed = GameData.instance.fileHandler.currentMission;
    }

    public void OnExit()
    {

    }

    public void NewSelection()
    {

    }

    //private void onTouchesBegan(const std::vector<Touch*>& touches, Event* event);
    private void Update()
    {
        OnKeyPressed();
    }
    private void OnKeyPressed()
    {
        
    }

    public void CleanUpVectors(List<Enemy> vToClean)
    {

    }

    public void ResetValues()
    {

    }



    public void BossButtonCallback() { }

    public void InitMissionDisplayOnEndScreen() { }
    public void InitNewMissionDisplay() { }
    public void InitTrainingGameEnd() { }
    public void InitTrainingMissionCompleteMenu() { }
    public void InitTrainingMissionIncompleteMenu() { }


    public void InitMissionMenu() { }
    public void ChestButtonCallback()
    {

    }


    public void BossGameOver()
    {

//暂时屏蔽手机，后面以手机
// #if UNITY_STANDALONE
        //Globals.StopAllSounds();
        AudioManager.instance.StopAllSoundEffects();
        string bossString;
        bossString = "Gameplay Arena Boss " + GameData.instance.fileHandler.currentEvent;
    // #if UNITY_STANDALONE
        
    // #endif
        //gameOverLoseDesktop.Show();
        //return;
// #else

         gameOverMobile.Show(totalKillsCount, currentMissionPlayed, true);
        

// #endif
    }

    private void PickEndScreen()
    {
        DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() => {
            isEndScreenShown = true;

        }).Play();
        
        if (Globals.gameType == GameType.Arena)
        {
            if (isBossDefeated)
            {
                if (Globals.isMissionComplete)
                {
                    BossWinWithMission();
                    //log("boss win with mission");

                }
            }
            else
            {
                BossGameOver();
            }
        }
        else if (Globals.gameType == GameType.Training)
        {
            TrainingGameOver();  
        }
        else if (Globals.gameType == GameType.Survival)
        {
            
            Globals.enemiesTillLastWave = 0;
            //        survivalModeWaveCounter = 1;
            Globals.enemiesKilledInCurrentSession = totalKillsCount;

            if (Globals.gameType == GameType.Survival)
            {

                if (GameData.instance.fileHandler.totalKillsSurvivalMode < totalKillsCount)
                    GameData.instance.fileHandler.totalKillsSurvivalMode = totalKillsCount;
                PlayerPrefs.SetInt("totalKillsSurvivalMode", GameData.instance.fileHandler.totalKillsSurvivalMode);



            }

            if (GameData.instance.fileHandler.totalWavesSurvivalMode < Globals.survivalModeWaveCounter)
            {
                GameData.instance.fileHandler.totalWavesSurvivalMode = Globals.survivalModeWaveCounter;
                PlayerPrefs.SetInt("totalWavesSurvivalMode", GameData.instance.fileHandler.totalWavesSurvivalMode);

            }
            if (GameData.instance.fileHandler.maxTimeSurvivalMode < Globals.timerForSurvival)
            {
                GameData.instance.fileHandler.maxTimeSurvivalMode = (int)Globals.timerForSurvival;

            }

            SurvivalGameOver();
        }

    }
    void TrainingGameOver()
    {

        if (!Globals.isMissionComplete)
        {
            AudioManager.instance.StopAllSoundEffects();

//#if UNITY_STANDALONE
//            //char str[64];
//            //sprintf(str, "Gameplay Training Mission %d",FileHandler::getInstance().currentMission);
//#if UNITY_STANDALONE
//            //        gameanalytics::cocos2d::GameAnalytics::addProgressionEvent(gameanalytics::cocos2d::EGAProgressionStatus::Fail, str);
//#endif
//            gameOverLoseDesktop.Show();
        
//        return;
//#else

            //Globals.PlaySound("res/Sounds/BGM/bossLoose.mp3");
            // AudioManager.instance.PlayMusic(Track.bossLoose);
            AudioManager.instance.PlayMusic(7004);

//#endif
        }
        //暂时屏蔽手机，后面以手机
        // #if UNITY_STANDALONE
        else
        {
            //gameOverWinDesktop.Show();


            if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
            {
                    // GameData.instance.fileHandler.missionsCompleted = GameData.instance.fileHandler.currentMission;
                    GameData.instance.fileHandler.missionUnlock = 1;
            }
            // GameData.instance.fileHandler.currentMission++;

            GameData.instance.fileHandler.SaveData();
        
        
        //return;
        }
        // #else


         gameOverMobile.Show(totalKillsCount, currentMissionPlayed);
// #endif


    }


    public void BossWinWithMission()
    {
        //暂时屏蔽手机，后面以手机
// #if UNITY_STANDALONE
        string bossString = "Gameplay Arena Boss " + GameData.instance.fileHandler.currentEvent;

        //gameOverWinDesktop.Show();
    if(GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
    {
        // GameData.instance.fileHandler.missionsCompleted = GameData.instance.fileHandler.currentMission;
        GameData.instance.fileHandler.missionUnlock = 1;
    }
    //TODO Fix
    // GameData.instance.fileHandler.currentMission++;
    
    GameData.instance.fileHandler.SaveData();

        // #else
        gameOverMobile.Show(totalKillsCount, currentMissionPlayed, true);
// #endif
    }

    public void GameOverSequence()
    {
        PickEndScreen();
    }

    void SurvivalGameOver()
    {
        if (!Globals.isMissionComplete)
        {
            AudioManager.instance.StopAllSoundEffects();
            AudioManager.instance.PlayMusic(7004);
        }
        else
        {
            GameData.instance.fileHandler.SaveData();
        }
        gameOverMobile.Show(totalKillsCount, currentMissionPlayed);
    }

}
