using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Spine.Unity;
using TMPro;
using DG.Tweening;
public class UtilityMenu : MonoBehaviour
{
    [SerializeField] private Button exitButton;
    //[SerializeField] private SkeletonGraphic rays;
    [SerializeField] private Sprite<PERSON><PERSON><PERSON> blackOverlay;
    [SerializeField] private GameObject connectJoystickPrompt;
    [SerializeField] private TextMeshP<PERSON>UG<PERSON> connectJoystickLabel;
    [SerializeField] private GameObject tutorialKeyboard;
    [SerializeField] private CustomButton upButton;
    [SerializeField] private TextMeshProUGUI upLabel;
    [SerializeField] private CustomButton downButton;
    [SerializeField] private Text<PERSON><PERSON><PERSON><PERSON><PERSON>GUI downLabel;
    [SerializeField] private CustomButton leftButton;
    [SerializeField] private TextMeshProUGUI leftLabel;
    [SerializeField] private CustomButton rightButton;
    [SerializeField] private TextMeshPro<PERSON><PERSON><PERSON> rightLabel;
    [SerializeField] private TextMeshProUGUI aimLabel;
    [SerializeField] private Text<PERSON>eshP<PERSON>UGUI shootLabel;
    [SerializeField] private GameObject tutorialJoyStick;
    [SerializeField] private CustomButton joyStickShootButton;
    [SerializeField] private TextMeshProUGUI joyStickshootLabel;
    [SerializeField] private TextMeshProUGUI joyStickMoveLabel;
    [SerializeField] private TextMeshProUGUI joyStickRotateLabel;
    [SerializeField] private GameObject difficultyButtonsContainer;
    [SerializeField] private CustomButton easyButton;
    [SerializeField] private CustomButton mediumButton;
    [SerializeField] private CustomButton hardButton;
    //[SerializeField] private DifficultyRewardStats difficultyRewardEasy;
    [SerializeField] private DifficultyRewardStats difficultyRewardMedium;
    [SerializeField] private DifficultyRewardStats difficultyRewardHard;
    [SerializeField] private TextMeshProUGUI playerBuffTMP, enemyBuffTMP, tipBuffTMP;

    private bool isExit = false;
    private bool swallowTouchNow;
    private int selectedButton = 0;
    private List<CustomButton> buttonList = new List<CustomButton>();
    private List<CustomButton> startingButtons = new List<CustomButton>();
    private int selected = 0;
    private string tweenId;
    private string schedulerId;

    public void Init()
    {
        schedulerId = "UMS" + GetInstanceID();
        tweenId = "UM" + GetInstanceID();
        exitButton.onClick.AddListener(OnExitButtonCallBack);
#if UNITY_STANDALONE
        Globals.allowSidekickShoot = true;
#endif
        blackOverlay.gameObject.SetActive(true);
        blackOverlay.SetOpacity(100);
        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(-200));
        GameManager.instance.timeManager.SetTimescale(0.1f);
        selected = 0;//PlayerPrefs.GetInt("GamePlayModeType", (int)Globals.gameModeType);
        //rays.gameObject.SetActive(false);
        if (Globals.CsvRow_CatMainStage != null && (Globals.CsvRow_CatMainStage.FrontType != 3 && Globals.CsvRow_CatMainStage.FrontType != 4) &&  GameData.instance.fileHandler.currentMission != 0)
        {


#if UNITY_STANDALONE
            if (Globals.g_showDifficultyInUtilityMenu)
            {
                //rays.gameObject.SetActive(true);
                CreateDifficultyButtons();
                Globals.g_showDifficultyInUtilityMenu = false;
                //rays.AnimationState.SetAnimation(0, "idle", true);
            }
#else
            //if (Globals.isJoystickConnected)
            //{
            //    rays.gameObject.SetActive(true);
            //}
            //else
            //{
            //    rays.gameObject.SetActive(false);
            //}
            Globals.g_showDifficultyInUtilityMenu = false;
            CreateDifficultyButtons();

#endif

        }
        else
        {

            Globals.g_showDifficultyInUtilityMenu = false;
#if UNITY_TVOS
            //if (!Globals.isJoystickConnected)
            //{
            //    connectJoystickPrompt.gameObject.SetActive(true);
            //    connectJoystickLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["required"] as string;
            //    //TODO
            //    //aimLabel.runAction(RepeatForever::create(Blink::create(1.0, 10)));
            //}
#endif

        }

#if UNITY_STANDALONE
        if (startingButtons.Count > 0)
        {
            //rays.transform.position = startingButtons[selected].transform.position;
        }

        InitGamePad();

        if (GameData.instance.fileHandler.currentMission < 1)
        {
            //tutorialKeyboard.gameObject.SetActive(true);
        }
        //TODO MapKeyBoardButtons
        //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(GameData.instance.fileHandler.keyCodeBoost);

        upButton.SetButtonColor(upButton.BLUE);
        upButton.defaultLabel.text = "W";
        upLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAIN_MENU)["up"] as string;
        upButton.SetInteractable(false);
        downButton.defaultLabel.text = "S";
        downButton.SetButtonColor(downButton.BLUE);
        downLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAIN_MENU)["down"] as string;
        downButton.SetInteractable(false);
        rightButton.defaultLabel.text = "D";
        rightButton.SetButtonColor(downButton.BLUE);
        rightLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAIN_MENU)["right"] as string;
        rightButton.SetInteractable(false);
        leftButton.defaultLabel.text = "D";
        leftButton.SetButtonColor(downButton.BLUE);
        leftLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAIN_MENU)["left"] as string;
        leftButton.SetInteractable(false);

        aimLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["aim"] as string;
        shootLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["shoot"] as string;

        //TODO Create Mouse
        //UNITY_STANDALONEPointer *dp = UNITY_STANDALONEPointer::create(false);
        //this.addChild(dp);

        //if (Globals.isJoystickConnected)
        //{
        //    tutorialJoyStick.SetActive(true);
        //    joyStickMoveLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerMove"] as string;
        //    joyStickRotateLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerRotateShoot"] as string;
        //    joyStickshootLabel.text = "Shoot";
        //    joyStickShootButton.defaultLabel.text = "R1";
        //    joyStickShootButton.SetButtonColor(joyStickShootButton.YELLOW);
        //    joyStickShootButton.SetInteractable(false);
        //}
#endif
        //TODO MultipleInputs
        //    keylistener = EventListenerKeyboard::create();
        //    keylistener.onKeyPressed = CC_CALLBACK_2(UtilityMenu::onKeyPressed, this);
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

        //    auto listener = EventListenerTouchAllAtOnce::create();

        //    listener.onTouchesBegan = CC_CALLBACK_2(UtilityMenu::onTouchesBegan, this);

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);

        //    GamePad_Apple* ga = GamePad_Apple::create();
        //    this.addChild(ga);


        //    if (GameData.instance.fileHandler.currentMission == 0)
        //    {

        //        auto _listener = EventListenerController::create();

        //        _listener.onKeyDown = [=](cocos2d::Controller * controller, int keyCode, cocos2d::Event *event){

        //    if (Input.GetKeyUp(GameData.instance.fileHandler.gamePadCodeShoot || Input.GetKeyUp(GameData.instance.fileHandler.gamePadCodeShoot2 || Input.GetKeyUp(GameData.instance.fileHandler.gamePadCodeDash || Input.GetKeyUp(GameData.instance.fileHandler.gamePadCodeDash2 || Input.GetKeyUp(Controller::Key::BUTTON_RIGHT_SHOULDER)
        //    {
        //        exitButtonCallback();
        //    }

        //};


        //_eventDispatcher.addEventListenerWithSceneGraphPriority(_listener, this);

        //}
        //return true;

        //BattleSkillManager.Instance._weapon.UpdateToMax(false);
        //if (GameData.instance.fileHandler.currentMission == 99999)
        //{
        //    CreateRandomBuff();
        //}
        //else
        //{
        //    playerBuffTMP.text = "";
        //    enemyBuffTMP.text = "";
        //    tipBuffTMP.text = "";
        //}


        gameObject.SetActive(true);
    }

    //private void CreateRandomBuff()
    //{
    //    string allBuffStr = Globals.g_currentStageData.FightSkill;
    //    if(allBuffStr.Length == 0)
    //    {
    //        playerBuffTMP.text = "";
    //        enemyBuffTMP.text = "";
    //        tipBuffTMP.text = "";
    //        return;
    //    }
    //    string[] playerAndEnemyBuff = allBuffStr.Split('|');
    //    string[] playerBuffArray = playerAndEnemyBuff[0].Split(';');
    //    string[] enemyBuffArray = playerAndEnemyBuff[1].Split(';');
    //    //try
    //    //{
    //        int playerBuff = System.Convert.ToInt32(playerBuffArray[(int)Mathf.Floor(Random.Range(0, playerBuffArray.Length))]);
    //        int enemyBuff = System.Convert.ToInt32(enemyBuffArray[(int)Mathf.Floor(Random.Range(0, enemyBuffArray.Length))]);
    //        var pBI = BuffScheme.Instance.GetItem(playerBuff);
    //        var eBI = BuffScheme.Instance.GetItem(enemyBuff);
    //        playerBuffTMP.text = "玩家随机获得:" + pBI.Name;
    //        enemyBuffTMP.text = "敌人随机获得:" + eBI.Name;
    //        tipBuffTMP.text = "点击屏幕开始挑战";
    //        LuaToCshapeManager.Instance.UpgradeBuff(playerBuff, true);
    //        var be = BuffEffectScheme.Instance.GetItem(eBI.EffectID1);
    //        LuaToCshapeManager.Instance.AddEnemyBuff(be);
    //    //}
    //    //catch
    //    //{
    //        //Debug.LogError("！！！检查关卡的随机buff");
    //    //}
    //}

    private void CreateDifficultyButtons()
    {
        int rewardInCoins = GameManager.instance.missionManager.rewardInCoins;
        int rewardInXp = GameManager.instance.missionManager.rewardInXp;
        Observer.DispatchCustomEvent("MOUSE_POINTER_HIDE");
        Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        Globals.gameModeType = GamePlayMode.Easy;
        PlayerPrefs.SetInt("GamePlayModeType", (int)(Globals.gameModeType));
        OnExitButtonCallBack();
        //startingButtons.Add(easyButton);
        //difficultyRewardEasy.Create(1, rewardInCoins / 2, rewardInXp / 2);

//        {
//            easyButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["easy"] as string;
//            easyButton.defaultAction = () =>
//            {
//                Observer.DispatchCustomEvent("MOUSE_POINTER_HIDE");
//                Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
//                Globals.gameModeType = GamePlayMode.Easy;
//                PlayerPrefs.SetInt("GamePlayModeType", (int)(Globals.gameModeType));
//                OnExitButtonCallBack();

//            };
//            startingButtons.Add(easyButton);
//            difficultyRewardEasy.Create(1, rewardInCoins / 2, rewardInXp / 2);
//        }
//        {
//            mediumButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["medium"] as string;
//            mediumButton.defaultAction = () =>
//            {
//                Observer.DispatchCustomEvent("MOUSE_POINTER_HIDE");
//                Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
//                Globals.gameModeType = GamePlayMode.Medium;
//                PlayerPrefs.SetInt("GamePlayModeType", (int)(Globals.gameModeType));
//                OnExitButtonCallBack();

//            };
//            startingButtons.Add(mediumButton);
//            difficultyRewardMedium.Create(1, rewardInCoins, rewardInXp);
//        }

//        {
//            hardButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["hard"] as string;
//            hardButton.defaultAction = () =>
//            {
//                Observer.DispatchCustomEvent("MOUSE_POINTER_HIDE");
//                Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
//                Globals.gameModeType = GamePlayMode.Hard;
//                PlayerPrefs.SetInt("GamePlayModeType", (int)(Globals.gameModeType));
//                OnExitButtonCallBack();

//            };
//            startingButtons.Add(hardButton);
//            difficultyRewardHard.Create(1, rewardInCoins * 2, rewardInXp * 2);

//        }
//        difficultyButtonsContainer.gameObject.SetActive(true);
//#if UNITY_ANDROID || UNITY_IOS
//        if (!Globals.isJoystickConnected)
//        {
//            easyButton.GetSpineObject().AnimationState.SetAnimation(0, "animation", true);
//            mediumButton.GetSpineObject().AnimationState.SetAnimation(0, "animation", true);
//            hardButton.GetSpineObject().AnimationState.SetAnimation(0, "animation", true);
//        }
//#endif

//        float animationSpeed = 1.0f / 0.15f;
//        if (rays.gameObject.activeInHierarchy)
//        {
//            rays.AnimationState.TimeScale = (animationSpeed);
//            rays.transform.position = startingButtons[(int)Globals.gameModeType].transform.position;
//        }
//#if UNITY_ANDROID || UNITY_IOS
//        if (Globals.isJoystickConnected)
//        {
//            rays.gameObject.SetActive(true);
//        }
//        else
//        {
//            rays.gameObject.SetActive(false);
//        }
//#else
//            rays.gameObject.SetActive(true);
//#endif


    }

    private void OnExitButtonCallBack()
    {
        if (isExit)
        {
            return;
        }

        isExit = true;
        Globals.canGenerateLightening = true;
#if UNITY_STANDALONE
        //TODO Anim
        //keylistener.setEnabled(false);
        //this.getChildByTag(30).stopAllActions();
        //this.getChildByTag(30).runAction(EaseExponentialIn::create(MoveBy::create(0.35f, cocos2d::Point(0,-1040))));

#endif

        Globals.zoomInForSec = 0.001f;
        DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(0, 0.35f)).SetUpdate(true).Play();
        DOTween.Sequence().SetId(tweenId).Append(GetComponent<RectTransform>().DOAnchorPos(new Vector2(0, -500), 0.15f).SetEase(Ease.OutExpo)).AppendCallback(() => {

            //GameManager.instance.player.GetSkeletonAnimation().state.TimeScale = System.Convert.ToSingle(((GameData.instance.GetPlayerData()["Stats"] as PList)["timeScale"] as PList)["value"]);
            gameObject.SetActive(false);
        }).SetUpdate(true).Play();

        //GameController* gc = static_cast<GameController*>(GETGAMECONTROLLER);
        //if (gc)
        //{
        //    if (gc.getbackground().getRain())
        //    {
        //        gc.getbackground().getRain().resume();
        //        gc.getbackground().getRain().stopAllActions();
        //        gc.getbackground().getRain().runAction(Shared::createAnimation("Rain_0000%d.png", 0, 8, true, 0.04));
        //    }
        //}
        Observer.DispatchCustomEvent("GAMESTART");
        GameManager.instance.StartGame();
    }

    private void InitGamePad()
    {
        //TODO
        //        this.runAction(Sequence::create(DelayTime::create(0.25f), CallFunc::create([=](){

        //            auto _listener = EventListenerController::create();

        //            _listener.onKeyDown = [=](cocos2d::Controller * controller, int keyCode, cocos2d::Event *event){
        //        this.exitButtonCallback();

        //    };


        //    //Activate the listener into the event dispatcher
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(_listener, this);


        //    //This function should be called for iOS platform
        //#if (CC_TARGET_PLATFORM != CC_PLATFORM_WIN32 && CC_TARGET_PLATFORM != CC_PLATFORM_WINRT)

        //        Controller::startDiscoveryController();
        //#endif
        //    //code here


        //    }), NULL));
    }

    private void Update()
    {
        OnKeyPressed();
    }

    private void OnKeyPressed()
    {

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            if (UnityEngine.Input.GetKeyUp(KeyCode.Escape) || UnityEngine.Input.GetKeyUp(KeyCode.Space) || UnityEngine.Input.GetKeyUp(KeyCode.Return)
       || UnityEngine.Input.GetKeyUp(KeyCode.LeftArrow)
       || UnityEngine.Input.GetKeyUp(KeyCode.RightArrow)
       || UnityEngine.Input.GetKeyUp(KeyCode.UpArrow)
       || UnityEngine.Input.GetKeyUp(KeyCode.DownArrow))
            {
                OnExitButtonCallBack();
            }
            //TODO Custom Inputs
            //int keyCodeInt = static_cast<int>(keyCode);
            //if (keyCodeInt == GameData.instance.fileHandler.keyCodeBoost || keyCodeInt == GameData.instance.fileHandler.keyCodeSpecial || keyCodeInt == GameData.instance.fileHandler.keyCodeReverse || keyCodeInt == GameData.instance.fileHandler.keyCodeForward || keyCodeInt == GameData.instance.fileHandler.keyCodeBackward)
            //{
            //    ExitButtonCallback();

            //}
            return;
        }


        if (startingButtons.Count > 0)
        {
            if (UnityEngine.Input.GetKeyUp(KeyCode.LeftArrow))
            {

                if (selected != 0)
                {
                    selected--;
                    selected = Mathf.Clamp(selected, 0, startingButtons.Count - 1);
                    //rays.transform.position = startingButtons[selected].transform.position;
                    foreach (CustomButton c in startingButtons)
                    {
                        c.OnMouseExit();
                    }
                    startingButtons[selected].OnMouseEnter();
                    AudioManager.instance.PlaySound(AudioType.Enemy, Constants.AudioClips.SOUND_HOVER);
                }


            }

            if (UnityEngine.Input.GetKeyUp(KeyCode.RightArrow))
            {


                if (selected < startingButtons.Count - 1)
                {
                    selected++;
                    selected = Mathf.Clamp(selected, 0, startingButtons.Count - 1);
                    //rays.transform.position = startingButtons[selected].transform.position;
                    foreach (CustomButton c in startingButtons)
                    {
                        c.OnMouseExit();
                    }
                    startingButtons[selected].OnMouseEnter();
                    AudioManager.instance.PlaySound(AudioType.Enemy, Constants.AudioClips.SOUND_HOVER);
                }

            }
        }
        if (UnityEngine.Input.GetKeyUp(KeyCode.Return))
        {
            if (startingButtons.Count > 0)
                startingButtons[selected].defaultAction?.Invoke();
            else
            {
                // save value here
                Globals.gameModeType = (GamePlayMode)selected;
                PlayerPrefs.SetInt("GamePlayModeType", (int)Globals.gameModeType);
                OnExitButtonCallBack();
            }
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants.AudioClips.SOUND_BUTTON_TAP);
        }
    }


    private void AddButtonitems(Button parent, int price)
    {

    }
}
