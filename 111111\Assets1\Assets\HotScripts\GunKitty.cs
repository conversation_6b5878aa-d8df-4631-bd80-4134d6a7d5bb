using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;

public class GunKitty : Sidekick
{
    [SerializeField] Sprite bulletSprite;
    [HideInInspector] public Vector2 direction;
    [HideInInspector] public bool shootLeft;
    float shootAngle = 0;
    Bone gun1;
    Bone gun2;

    private float distance;
    private float maxDistance = 11;
    private void Start()
    {
        Init();
       
    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        scheduleUpdate = false;
        shootLeft = true;
        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);
        transform.localScale = new Vector3(scale, scale, 1);
        SetStartingPosition();
    }

    public override void StartSidekick()
    {
        sidekickSkeleton.state.Data.SetMix("select", "idle", 0.4f);
        sidekickSkeleton.state.SetAnimation(0, "select", true);
        sidekickSkeleton.state.SetAnimation(0, "idle", true);
        gun1 = sidekickSkeleton.skeleton.FindBone("gun1");
        gun2 = sidekickSkeleton.skeleton.FindBone("gun2");
        sidekickSkeleton.AnimationState.Event += ShootEvent;
    }

    private void OnDisable()
    {
        sidekickSkeleton.AnimationState.Event -= ShootEvent;
    }

    void ShootEvent(TrackEntry trackEntry, Spine.Event e)
    {
        if (e.Data.Name == "shoot")
            Shoot();
    }

    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;

        if (transform.localScale.x < 0)
        {
            {
                Bullet bulletLayer = null;
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bulletLayer = b;
                //         bulletLayer.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.setDamage(GameData.instance.fileHandler.currentMission != 0 ? damage : (3 + (player.Stats.attack * 0.5f)));//(3 + (player.Stats.attack * 0.5f));


                bulletLayer.SetSpriteFrame(bulletSprite);
                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun1Pos = (Vector2)gun1.GetWorldPosition(sidekickSkeleton.transform);

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun1Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun1Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = Vector2.SignedAngle(direction - gun1Pos, transform.right) - 95 + 180;
                float angle = shootAngle - 95;
                angle = angle < 0 ? 360 + angle : angle;
                gun1.Rotation = -angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);

                //bullet->setRotation(spBone_getWorldRotationX(gun1));

                Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.PlayBulletAnim(duration, dest);

                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }


            {
                Bullet bulletLayer = null;
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bulletLayer = b;
                //         bulletLayer.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.setDamage(5 + player.Stats.attack * 0.5f);

                bulletLayer.SetSpriteFrame(bulletSprite);
                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun2Pos = (Vector2)gun2.GetWorldPosition(sidekickSkeleton.transform);

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun2Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun2Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = Vector2.SignedAngle(direction - gun2Pos, transform.right) - 70 + 180;
                float angle = shootAngle - 120;
                angle = angle < 0 ? 360 + angle : angle;
                gun2.Rotation = -angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);

                //bullet->setRotation(spBone_getWorldRotationX(gun2));
                Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.PlayBulletAnim(duration, dest, false);

                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }
        }
        else
        {
            {
                Bullet bulletLayer = null;
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bulletLayer = b;
                //         bulletLayer.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.setDamage(3 + player.Stats.attack * 0.5f);


                bulletLayer.SetSpriteFrame(bulletSprite);
                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun1Pos = (Vector2)gun1.GetWorldPosition(sidekickSkeleton.transform);

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun1Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun1Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = -95 - Vector2.SignedAngle(direction - gun1Pos, transform.right);
                float angle = shootAngle - 95; //-95 - shootAngle;
                angle = angle < 0 ? 360 + angle : angle;
                gun1.Rotation = angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);

                Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                
                bulletLayer.PlayBulletAnim(duration, dest, false);

                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }


            {
                Bullet bulletLayer = null;
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                bulletLayer = go.GetComponent<Bullet>();
                // bool didFindBullet = false;
                // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
                // {
                //     if (!b.isInUse)
                //     {
                //         bulletLayer = b;
                //         bulletLayer.isInUse = true;
                //         didFindBullet = true;
                //         break;
                //     }
                // }
                // if (!didFindBullet)
                // {
                //     return;
                // }

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.setDamage(3 + player.Stats.attack * 0.5f);


                bulletLayer.SetSpriteFrame(bulletSprite);
                bulletLayer.transform.localScale = new Vector3(0.5f, 0.5f, 1);
                //bullet->setFlippedX(true);

                var gun2Pos = (Vector2)gun2.GetWorldPosition(sidekickSkeleton.transform);

                bulletLayer.transform.position =
                    new Vector3(transform.position.x + (gun2Pos.x - transform.position.x) * transform.localScale.x,
                    transform.position.y + (gun2Pos.y - transform.position.y) * transform.localScale.y);
                //float angle = -70 - Vector2.SignedAngle(direction - gun2Pos, transform.right);
                float angle = shootAngle - 70;// -70 - shootAngle;
                angle = angle < 0 ? 360 + angle : angle;
                gun2.Rotation = angle;
                float duration = 4;

                bulletLayer.transform.rotation = Quaternion.Euler(0, 0, shootAngle);
                //bullet->setRotation(-spBone_getWorldRotationX(gun2) - 180);

                Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Cos(shootAngle * Mathf.Deg2Rad),
                    Globals.CocosToUnity(5000) * Mathf.Sin(shootAngle * Mathf.Deg2Rad));
                bulletLayer.gameObject.SetActive(true);
                
                bulletLayer.PlayBulletAnim(duration, dest, false);

                GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);
            }

        }
    }


    void Update()
    {
        if (!scheduleUpdate)
            return;

        if (!sidekickSkeleton)
        {
            return;
        }

        direction = Globals.nearestAliveEnemy;
        distance = Vector2.Distance(transform.position, Globals.nearestAliveEnemy);
        if (distance < maxDistance)
        {
            float dir = Vector2.SignedAngle(Vector2.right, direction - (Vector2)transform.position);
            shootAngle = Globals.mobileControls
                ? dir < 0 ? 360 + dir : dir
                : player.RotationInDegrees;
        }
        if (shootAngle > 90 && shootAngle < 270)
        {
            transform.localScale = new Vector3(-scale, scale, 1);
        }
        else
        {
            transform.localScale = new Vector3(scale, scale, 1);
        }

        SidekicksUpdate();
    }
}
