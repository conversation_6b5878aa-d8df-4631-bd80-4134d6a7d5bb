using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class Constants_Audio
{
    public static class Audio
    {

        public const string spikyIntro = "spikyIntro";
        public const string spikyShoot = "spikyShoot";
        public const string spIntro = "spIntro";
        public const string spShoot = "spShoot";
        public const string cutinIntro = "cutinIntro";
        public const string explosion = "explosion";
        public const string flying = "flying";
        public const string trigger = "trigger";
        public const string barkMeowIntro = "barkMeowIntro";
        public const string backMeowLaserOff = "backMeowLaserOff";
        public const string barkMeowLaserLoop = "barkMeowLaserLoop";
        public const string barkMeowLaserOn = "barkMeowLaserOn";
        public const string mafiaIntro = "mafiaIntro";
        public const string SpiderCatBullet1Loop = "Spider Cat - Bullet 1 (loop)";
        public const string SpiderCatBullet1TriggerOFF = "Spider Cat - Bullet 1 (Trigger OFF)";
        public const string SpiderCatBullet1TriggerON = "Spider Cat - Bullet 1 (Trigger ON)";
        public const string spiderCatBullet2 = "spiderCatBullet2";
        public const string SpiderCatFall = "SpiderCatFall";
        public const string SpiderCatIntro = "SpiderCatIntro";
        public const string SpiderCatJump = "SpiderCatJump";
        public const string GreekEgyptianBullet = "Greek Egyptian - Bullet";
        public const string GreekEgyptianIntro = "Greek Egyptian - Intro";
        public const string sentinelAttack = "sentinelAttack";
        public const string sentinelLaser = "sentinelLaser";
        public const string laserLooped = "laserLooped";
        public const string SentinelIntro = "SentinelIntro";
        public const string dragonAttack = "dragonAttack";
        public const string dragonSistersIntro = "dragonSistersIntro";
        public const string startFire = "startFire";
        public const string TinyBotBGloop = "Tiny Bot - BG (loop)";
        public const string ComplexInterface = "Complex Interface 5"; //OKTO SPINE
        public const string creaturedemonic = "creature demonic (1)"; //OKTO SPINE
        public const string Device2Stop = "Device 2 Stop";
        public const string Door1Close1 = "Door 1 Close 1";
        public const string Door3Close1 = "Door 3 Close 1";
        public const string Door8Close2 = "Door 8 Close 2";
        public const string Door9Open = "Door 9 Open";
        public const string Entry = "Entry";
        public const string footstep9 = "footstep 9 (2)";
        public const string gunAttack = "gunAttack";
        public const string gunAttack1 = "gunAttack1";
        public const string MediumExplosion15 = "MediumExplosion15";
        public const string MonsterBigWalkingDistant = "Monster Big Walking Distant 1_02";
        public const string phase3Attack = "phase3Attack";
        public const string tranisiton = "tranisiton";
        public const string UnderwaterLoop1 = "Underwater Loop 1";
        public const string UnderwaterLoop2 = "Underwater Loop 2";
        public const string UnderwaterLoop3 = "Underwater Loop 3";
        public const string UnderwaterLoop4 = "Underwater Loop 4";
        public const string UnderwaterLoop5 = "Underwater Loop 5";
        public const string UnderwaterMovement1 = "Underwater Movement 1";
        public const string UnderwaterMovement2 = "Underwater Movement 2";
        public const string UnderwaterMovement3 = "Underwater Movement 3";
        public const string UnderwaterMovement4 = "Underwater Movement 4";
        public const string UnderwaterMovement5 = "Underwater Movement 5";
        public const string UnderwaterMovement6 = "Underwater Movement 6";
        public const string UnderwaterMovement7 = "Underwater Movement 7";
        public const string UnderwaterMovement8 = "Underwater Movement 8";
        public const string UnderwaterMovement9 = "Underwater Movement 9";
        public const string UnderwaterMovement10 = "Underwater Movement 10";
        public const string UnderwaterMovement11 = "Underwater Movement 11";
        public const string UnderwaterMovement12 = "Underwater Movement 12";
        public const string UnderwaterMovement13 = "Underwater Movement 13";
        public const string UnderwaterMovement14 = "Underwater Movement 14";
        public const string UnderwaterMovement15 = "Underwater Movement 15";
        public const string Weapon10Shoot1 = "Weapon 10 Shoot 1";
        public const string Weapon14Shoot3Flamethrower = "Weapon 14 Shoot 3 (Flamethrower)";
        public const string backFireSound = "backFireSound";
        public const string boss_unlock = "boss_unlock";
        public const string buttonTap = "buttonTap";
        public const string buyButton = "buyButton";
        public const string catsWalkerAttack = "catsWalkerAttack";
        public const string chest_drop = "chest_drop";
        public const string chest_unlock = "chest_unlock";
        public const string chestIdle = "chestIdle";
        public const string chestOpen = "chestOpen";
        public const string click = "click";
        public const string coinCollected = "coinCollected";
        public const string cursorClick = "cursorClick";
        public const string deatomizerSound = "deatomizerSound";
        public const string empty = "empty";
        public const string empty2 = "empty2";
        public const string enemyBeingHit = "enemyBeingHit";
        public const string enemyBuildingDestroy = "enemyBuildingDestroy";
        public const string enemyHit = "enemyHit";
        public const string explosionEnemyPlane = "explosionEnemyPlane";
        public const string explosionEnemyPlane2 = "explosionEnemyPlane2";
        public const string flameThrowerShoot = "flameThrowerShoot";
        public const string hairball = "hairball";
        public const string kerBlasterFire = "kerBlasterFire";
        public const string laserEnd = "laserEnd";
        public const string laserLoop = "laserLoop";
        public const string laserStart = "laserStart";
        public const string levelUp = "levelUp";
        public const string lightning = "lightning";
        public const string LightningSpell1 = "LightningSpell1";
        public const string LightningSpell2 = "LightningSpell2";
        public const string LightningSpell3 = "LightningSpell3";
        public const string LightningSpell4 = "LightningSpell4";
        public const string LightningSpell5 = "LightningSpell5";
        public const string LightningSpell6 = "LightningSpell6";
        public const string LightningSpell7 = "LightningSpell7";
        public const string LightningSpell8 = "LightningSpell8";
        public const string LightningSpell9 = "LightningSpell9";
        public const string LightningSpell10 = "LightningSpell10";
        public const string LightningSpell11 = "LightningSpell11";
        public const string LightningSpell12 = "LightningSpell12";
        public const string LightningSpell13 = "LightningSpell13";
        public const string LightningSpell14 = "LightningSpell14";
        public const string LightningSpell15 = "LightningSpell15";
        public const string looseCannon = "looseCannon";
        public const string magicFire = "magicFire";
        public const string mapButtonActivate = "mapButtonActivate";
        public const string mapTrailTick = "mapTrailTick";
        public const string menuBg = "menuBg";
        public const string menuClick = "menuClick";
        public const string menuToMap = "menuToMap";
        public const string openShopItem = "openShopItem";
        public const string planeBoost1 = "planeBoost1";
        public const string planeBoost2 = "planeBoost2";
        public const string planeBoost3 = "planeBoost3";
        public const string planeBoost4 = "planeBoost4";
        public const string planeBoostLoop = "planeBoostLoop";
        public const string playerBoostStart = "playerBoostStart";
        public const string playerBoostSustain = "playerBoostSustain";
        public const string playerDeath = "playerDeath";
        public const string playerHit = "playerHit";
        public const string powerUpCollected = "powerUpCollected";
        public const string powerUpCollected2 = "powerUpCollected2";
        public const string rocketeerShoot = "rocketeerShoot";
        public const string ScoreCounterEnd = "ScoreCounterEnd";
        public const string selectBoss = "selectBoss";
        public const string sidekickUnlock = "sidekickUnlock";
        public const string Spark = "Spark";
        public const string SpecialClick5 = "Special Click 05";
        public const string upcomingWave = "upcomingWave";
        public const string victory = "victory";
        public const string warMachineCopy = "warMachine copy";
        public const string warMachine = "warMachine";
        public const string waveCompleted = "waveCompleted";
        public const string Whoosh = "Whoosh 8_1";
        public const string whoosh1 = "whoosh1";
        public const string whoosh2 = "whoosh2";
        public const string wings = "wings";
        public const string popUp = "popUp";
        public const string uniqueThunderstorm = "unique-thunderstorm";
        public const string worldWhoosh = "worldWhoosh";
        public const string splashFast = "splashFast";
        public const string splashMedium = "splashMedium";
        public const string splashSlow = "splashSlow";
        public const string soundEffectForPistalPaws = "11. Sound effect for PistalPaws";
        public const string soundEffectForFixitFeline = "13. Sound effect for Fixit-Feline";
        public const string dailyBonusClaimButton = "17. Dailybonus claim button";
        public const string collectingMissionRewards = "21. Collecting mission reward sfx";
        public const string inGamePlaneFlyingShorter = "23. In-game plane flying(shorter)";
        public const string Rain = "32. Rain";
        public const string fanfareVictory = "40533_fanfare-victory-4-mw_by_symphony-of-specters_preview";
        public const string rocknrollVictory2 = "7489860_rock-n-roll-victory-2_by_benridgemusic_preview";
        public const string rocknrollVictory = "7497246_rock-n-roll-victory-logo-3_by_benridgemusic_preview";
        public const string energyTransition = "energyTransition";
        public const string equip = "equip";
        public const string ftuxTransition = "ftuxTransition";
        public const string gameOverExplosionTransition = "gameOverExplosionTransition";
        public const string energy15DigitalSwoosh = "SFX - Energy 15 Digital Swoosh";
        public const string energy23Blaster = "SFX - Energy 23 Blaster";
        public const string energy28Pulse = "SFX - Energy 28 Pulse";
        public const string energy29ElectricityChargeHit = "SFX - Energy 29 Electricity Charge Hit";
        public const string explosion10DistantUnderwater = "SFX - Explosion 10 Distant Underwater";
        public const string explosion15Energy = "SFX - Explosion 15 Energy";
        public const string explosion22Swoosh = "SFX - Explosion 22 Swoosh";
        public const string explosion25 = "SFX - Explosion 25";
        public const string logo12noCT = "SFX - Logo 12 noCT";
        public const string logo18A = "SFX - Logo 18-A";
        public const string logo20Start = "SFX - Logo 20 (Start)";
        public const string logo29A = "SFX - Logo 29-A";
        public const string logo29B = "SFX - Logo 29-B";
        public const string logo30A = "SFX - Logo 30-A";
        public const string logo30B = "SFX - Logo 30-B";
        public const string swoosh06 = "SFX - Swoosh 06";
        public const string karBlaster = "SS - Kar Blaster";
        public const string a1 = "a1";
        public const string a2 = "a2";
        public const string a3 = "a3";
        public const string a4 = "a4";
        public const string a5 = "a5";
        public const string a6 = "a6";
        public const string a7 = "a7";
        public const string a8 = "a8";
        public const string a9 = "a9";
        public const string a10 = "a10";
        public const string a11 = "a11";
        public const string a12 = "a12";
        public const string a13 = "a13";
        public const string a14 = "a14";
        public const string a15 = "a15";
        public const string a16 = "a16";
        public const string a17 = "a17";
        public const string a18 = "a18";
        public const string a19 = "a19";
        public const string a20 = "a20";
        public const string a21 = "a21";
        public const string a22 = "a22";
    }
}

