﻿using System.Collections.Generic;

using UnityEngine;

using X.PB;

namespace Assets.HotScripts.CsvTables
{
    internal class FightBuffCsv : Singleton<FightBuffCsv>
    {
        private readonly Dictionary<int, FightBuff.Types.CSVRow> dic = new();

        protected override void InitializeSingleton()
        {
            DontDestroyOnLoad(this);

            var pbFileName = nameof(FightBuff);
            var _data = HotResManager.ReadPb<FightBuff>(nameof(FightBuff));
            foreach (var item in _data.CSVTable)
            {
                dic.Add(item.BuffID, item);
            }
            //Debug.LogWarning(pbFileName + "pb succes");

            base.InitializeSingleton();
        }
    }
}
