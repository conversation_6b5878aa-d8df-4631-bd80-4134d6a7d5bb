using UnityEngine;
using Spine.Unity;
using <PERSON><PERSON>;
using DG.Tweening;
public class BalloonEnemy : Enemy
{
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite bulletSprite;
    private float balloonOffset;

    private Bone _bone;
    private Bone _arm;
    private Bone _head;
    private Bone _gun;
    private Bullet bullet;

    private PlayerPing missionPing;
    
    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "BE" + GetInstanceID();
        schedulerId = "BES" + GetInstanceID();
        InitStats();
        allowRelocate = true;
        enemyCollisionRadius = 2f;
        balloonOffset = Globals.CocosToUnity(-500) + Random.value * Globals.CocosToUnity(250);

        transform.SetWorldPositionY(Globals.CocosToUnity(300));
        enemySprite.state.SetAnimation(0, "gunShoot", true);
        if (GameData.instance.fileHandler.currentMission == 0)
        {
           transform.SetWorldPositionX(player.transform.position.x + Globals.CocosToUnity(700));

        }
        else
        {
            transform.SetWorldPositionX(player.transform.position.x + Globals.CocosToUnity(-2000));
            //transform.SetWorldPositionX(Globals.CocosToUnity(-10000));
        }

        _bone = enemySprite.skeleton.FindBone("gunShoot");
        _arm = enemySprite.skeleton.FindBone("hand");
        _head = enemySprite.skeleton.FindBone("head");
        _gun = enemySprite.skeleton.FindBone("gun");

        enemySprite.state.Event += HandleSpineEvent;
        scheduleUpdate = true;

        if (GameData.instance.fileHandler.currentMission == 0)
        {

            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);


            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);
        }



        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKillBalloons && Globals.gameType == GameType.Training)
        {
            explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
            if (GameManager.instance.missionManager.totalPointsRequired != GameManager.instance.missionManager.totalPoints)
            {

                DOTween.Sequence().SetId(schedulerId).AppendInterval(1f).AppendCallback(() =>
                {
                    missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    missionPing.Init(transform, true);


                    missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    missionPing.Init(transform, false);
                }).Play();

                allowRelocate = false;
                stats.bulletDamage = stats.bulletDamage * 5.0f;
            }
        }
        else
        {
            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);
        }


        additionalOnDestroy += () =>
        {
            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKillBalloons && Globals.gameType == GameType.Training)
            {
                GameManager.instance.missionManager.MissionComplete();
            }
        };
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            Shoot();
        }
    }

    private void Shoot()
    {
        for (int i = 0; i < 5; i++)
        {
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.setDamage(stats.bulletDamage);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.transform.SetScale(1.5f);
            bullet.setDamage(stats.bulletDamage);
            bullet.transform.position = _bone.GetWorldPosition(enemySprite.transform);
            bullet.transform.SetRotation(_gun.WorldRotationX-120 +(i*15));
            Vector2 dest = new Vector2(Globals.CocosToUnity(2500) * Mathf.Sin(Mathf.Deg2Rad*bullet.transform.eulerAngles.z)*-1, Globals.CocosToUnity(2500) * Mathf.Cos(Mathf.Deg2Rad* bullet.transform.eulerAngles.z));
            bullet.duration = 0.25f + Random.value * 0.25f;
            bullet.setRadiusEffectSquared(1);
            bullet.PlayBulletAnim(2, dest);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }
    }

    private void Update()
    {
        float dir =90+ Vector2.SignedAngle(player.transform.position - _arm.GetWorldPosition(enemySprite.transform), transform.right);
        float rotDir = dir < 0 ? 360 + dir : dir;
        _arm.Rotation = -rotDir;

        if (player.transform.position.x + balloonOffset - Globals.CocosToUnity(400) > transform.position.x)
        {
            transform.SetWorldPositionX(transform.position.x + stats.speed *Time.deltaTime);


        }

        if (player.transform.position.x + balloonOffset + Globals.CocosToUnity(400) < transform.position.x)
        {
            transform.SetWorldPositionX(transform.position.x - stats.speed*Time.deltaTime);

        }

        if (player.transform.position.x > transform.position.x)
        {
            _head.ScaleY = 1;
            _arm.ScaleY = 1;
        }
        else
        {
            _head.ScaleY = -1;
            _arm.ScaleY = -1;

        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = 2 + Random.value;//bhut taiz
        stats.turnSpeed = 0.5f + Random.value;
        stats.health = GameData.instance.fileHandler.TrainingLevel * 500;
        stats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        stats.bulletSpeed = 7;
        stats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = stats.health;
        stats.xp = stats.maxHealth.Value;
        stats.coinAwarded = 15;

        baseStats.health = GameData.instance.fileHandler.TrainingLevel * 500;
        baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        baseStats.bulletSpeed = 7;
        baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        baseStats.maxHealth.Value = baseStats.health;
        baseStats.xp = stats.maxHealth.Value;
        baseStats.coinAwarded = 15;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        return false;
    }

    public override void Destroy()
    {
        base.Destroy();
    }
}
