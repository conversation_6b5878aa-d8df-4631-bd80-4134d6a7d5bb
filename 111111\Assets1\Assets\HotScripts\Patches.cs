using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
using System.Collections;
using System;

public class Patches : Enemy
{
    [SerializeField] private new Collider2D collider;
    [SerializeField] private SkeletonAnimation boost;
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] private Sprite missileSprite;
    [HideInInspector] public Vector2 direction;

    private Bone gunBone;
    private Bone missileBone;
    private Bone mineBone;
    private Bone cannonBone;
    private Bounds bounds;

    private bool _bossPhase2 = false;
    private float _missileSpeed = 20.0f;
    private float _missileTurnSpeed = 3.5f;
    private int fireCount = 0;
    [SerializeField] private bool movingLeft =true;
    private bool allowCannonRotation = false;
    private Bullet bullet = null;
    private Mine missile = null;
    private float targetAngle;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        InitValues();
    }
    private IEnumerator NextFrameGetBounds()
    {
        yield return null;
        bounds = collider.bounds;
    }

    private void InitValues()
    {
        enemySprite.state.SetAnimation(0, "entry", false);
        enemySprite.state.AddAnimation(0, "shoot", true, 0);
        transform.position =
            new Vector3(player.transform.position.x + Globals.CocosToUnity(1000) + UnityEngine.Random.value * Globals.CocosToUnity(200),
            Globals.LOWERBOUNDARY);
        explosionType = Explosions.ExplosionType.ExplosionTypeShip;
        cannonBone = enemySprite.skeleton.FindBone("cannon");
        collider.enabled = true;
        StartCoroutine(NextFrameGetBounds());
        enemySprite.state.Event += HandleSpineEvent;
        targetAngle = 270;
        scheduleUpdate = true;
        if (GameData.instance.fileHandler.currentMission == 23)
        {

            PlayerPing missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);

        }
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            ShootGun();
        }
        if (spineEvent.Data.Name == "missile")
        {
            ShootMissile();
        }
        if (spineEvent.Data.Name == "mine")
        {
            ShootMine();
        }
    }
    
    //Shoot Events

    private void ShootGun()
    {

        fireCount++;
        if (fireCount % 2 == 0)
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.trigger,1 ,enemySprite.transform.position);
            //TODO Distance Audio
            //Globals.PlaySound("res/Sounds/Bosses/Boss3/trigger.mp3", enemySprite.transform.position);
        }
        {
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.setDamage(stats.bulletDamage * 2);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.transform.localScale =  new Vector2(2.0f,2.0f);
            gunBone = enemySprite.skeleton.FindBone ("gun");
            bullet.transform.position = gunBone.GetWorldPosition(enemySprite.transform); // bullet poisition to
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(125));
            Vector2 dest;
            //Debug.Break();
            bullet.transform.SetPositionAndRotation(gunBone.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0, 0, gunBone.WorldRotationX - 90));
            dest = new Vector2(Globals.CocosToUnity(2000) * Mathf.Cos(Mathf.Deg2Rad * gunBone.WorldRotationX), Globals.CocosToUnity(2000) * Mathf.Sin(Mathf.Deg2Rad * gunBone.WorldRotationX));

            bullet.duration = 1;
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            bullet.PlayBulletAnim(2, dest);
        }

        if (_bossPhase2 == false)
        {
            return;
        }
        {
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.setDamage(stats.bulletDamage * 1.5f);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.transform.localScale = new Vector2(1, 1);
            gunBone = enemySprite.skeleton.FindBone("gun2");
            bullet.transform.SetPositionAndRotation(gunBone.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0, 0, gunBone.WorldRotationX - 90));
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(90));
            bullet.duration = 3;
            Vector2 dest = new Vector2(Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * gunBone.WorldRotationX), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * gunBone.WorldRotationX));
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            bullet.PlayBulletAnim(3, dest);
        }
    }
    private void ShootMissile()
    {
        //if (Player::getStats().mode != Player::PLAYER_MODE_FLYING) TODO
        //{
        //    return;
        //}
        //spSkeletonBounds_update(bounds, enemySprite.getSkeleton(), true);
        //bounds = collider.bounds;

        allowCannonRotation = true;

        {
            HomingMissile missile = null;
            bool didFindMissile = false;
            foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
            {
                if (!m.isInUse)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindMissile = true;
                    break;
                }

            }
            if (!didFindMissile)
            {
                return;
            }
            missile.Init();
            missile.SetDamage(stats.missileDamage);
            missile.SetSpeed(_missileSpeed);
            missile.homingMultiplier = _missileTurnSpeed;
            missile.missileSprite.sprite = missileSprite;
            missileBone = enemySprite.skeleton.FindBone("missile");
            missile.radiusSQ = Globals.CocosToUnity(90);
            //    missile.missileSprite.setScale(0.4f);

            float dir = Vector2.SignedAngle(Vector2.right, player.transform.position - transform.position);
            float rotationDir = dir < 0 ? 360 + dir : dir;
            if (movingLeft && rotationDir < 90)
            {
                rotationDir = 90;
            }
            if (!movingLeft && rotationDir > 90)
            {
                rotationDir = 90;
            }
            rotationDir = Mathf.Clamp(rotationDir, 0, 180);

            missile.transform.SetPositionAndRotation(missileBone.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0, 0, rotationDir));
            GameSharedData.Instance.enemyMissilesInUse.Add(missile); //getInstance().g_enemyMissileArrayToAdd.pushBack(missile);
            _missileTurnSpeed += 0.05f;
            _missileSpeed += 0.1f;
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.explosion);
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.flying,1, missile.missileSprite.transform.position);
            //TODO Distance Audio
            //Globals.PlaySound("res/Sounds/Bosses/Boss3/flying.mp3", missile.missileSprite.transform.position);
            missile.RemoveAfterDuration();
        }
    }

    private void ShootMine()
    {
        Mine missile = null;
        bool didFindMissile = false;
        foreach (Mine m in GameSharedData.Instance.enemyMinePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            return;
        }
        missile.Init();
        missileBone = enemySprite.skeleton.FindBone("mine");
        missile.transform.position = missileBone.GetWorldPosition(enemySprite.transform);
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.SetDamage(stats.missileDamage / 5);
        missile.RemoveAfterDuration();
    }

    private void Update()
    {
        //if (healthBar)
        //{
        //    healthBar.setPosition(enemySprite.getPosition().x, enemySprite.getPosition().y - 100);
        //}
        if (scheduleUpdate)
        {
            bounds = collider.bounds;
            if (transform.position.x - player.transform.position.x < Globals.CocosToUnity(-1400) || transform.position.x - player.transform.position.x > Globals.CocosToUnity(1400))
            {
                stats.speed += 0.3f;
                stats.speed = Mathf.Clamp(stats.speed, 4, Globals.SPEEDLIMIT);
                boost.gameObject.SetActive(true);
            }
            else
            {

                stats.speed -= 0.5f;

                PList bossStats = GameData.instance.GetBoss(2)["Stats"]as PList;
                stats.speed = Mathf.Clamp(stats.speed, Convert.ToSingle((bossStats["moveSpeed"]as PList)["value"]), 20);

                boost.gameObject.SetActive(false);
            }
            if (movingLeft)
            {
                transform.SetWorldPositionX(transform.position.x - stats.speed * Time.deltaTime * Globals.CocosToUnity(60));

                if (allowCannonRotation)
                {
                    float dir = Vector2.SignedAngle(player.transform.position - cannonBone.GetWorldPosition(enemySprite.transform), transform.right) + 90;
                    float rotDir = dir < 0 ? 360 + dir : dir;
                    targetAngle = Globals.MoveAngleTowards(targetAngle, rotDir, Time.deltaTime * 40);
                    if (targetAngle > 270)
                    {
                        targetAngle = Mathf.Clamp(targetAngle, 270, 360);
                        cannonBone.Rotation = -targetAngle;
                    }
                    
                    //cannonBone.Rotation = Mathf.Clamp(cannonBone.Rotation, -360, -240);
                }
                if (transform.position.x - player.transform.position.x < Globals.CocosToUnity(-1500) || transform.position.x < Globals.LEFTBOUNDARY)
                {
                    movingLeft = false;
                    enemySprite.skeleton.FindBone("root").ScaleX = -1;
                    boost.transform.SetScaleX(1);
                    //boost.transform.SetWorldPositionY(-boost.transform.position.x);
                    cannonBone.Rotation = 0;
                    targetAngle = 0;
                }
            }
            else
            {
                if (allowCannonRotation)
                {

                    transform.SetWorldPositionX(transform.position.x + stats.speed * Time.deltaTime * Globals.CocosToUnity(60));
                    float dir = 90 + Vector2.SignedAngle(player.transform.position - cannonBone.GetWorldPosition(enemySprite.transform), transform.right);
                    float rotDir = dir < 0 ? 360 + dir : dir;
                    targetAngle = Globals.MoveAngleTowards(targetAngle, rotDir, Time.deltaTime * 40);
                    if(targetAngle<90)
                    {
                        targetAngle = Mathf.Clamp(targetAngle, 0, 90);
                        cannonBone.Rotation = targetAngle;
                    }
                    //cannonBone.Rotation = angle;
                    //cannonBone.Rotation = cannonBone.Rotation + (-cannonBone.Rotation + Globals.CalcAngle(player.transform.position, transform.position) - 90) * Time.deltaTime * Globals.CocosToUnity(2);

                }

                if (transform.position.x - player.transform.position.x > Globals.CocosToUnity(1500) || transform.position.x > Globals.RIGHTBOUNDARY)
                {

                    movingLeft = true;
                    enemySprite.skeleton.FindBone("root").ScaleX = 1;
                    boost.transform.SetScaleX(-1);
                    //boost.transform.SetWorldPositionX(300);
                    cannonBone.Rotation = 0;
                    targetAngle = 0;
                }

            }

            if (isBoss)
            {
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    Globals.bossPosition = new Vector3(transform.position.x, transform.position.y + Globals.CocosToUnity(125));
                }
                if (_bossPhase2 == false && stats.health < stats.maxHealth.Value * 0.5f)
                {
                    //Director::getInstance().getEventDispatcher().dispatchCustomEvent("ChangeBossState");
                    Observer.DispatchCustomEvent("ChangeBossState");
                    _bossPhase2 = true;

                    enemySprite.state.SetAnimation(0, "level2", false);
                    enemySprite.state.AddAnimation(0, "shoot2", true, 0);
                }
            }
        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        isBoss = true;

        int bossNumber = 2;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            string bn = System.Convert.ToString(plist["Boss Number"]);
            int missionBossNumber = System.Convert.ToInt32(bn);
            bossNumber = missionBossNumber;
        }

        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.missileDamage = baseStats.missileDamage = stats.bulletDamage * 5;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (bounds == null) return false;
        return bounds.Contains(P1);
        //if (Vector2.SqrMagnitude((Vector2)transform.position- P1) < Globals.CocosToUnity(1600))
        //{
        //}
        //else
        //{
        //    return false;
        //}
    }
    
    public override void Destroy()
    {
        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKillShips && (Globals.gameType == GameType.Training || Globals.gameType == GameType.Survival) && player.Mode ==  PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            GameManager.instance.missionManager.AddPoint();
        }

        healthBar.gameObject.SetActive(false);
        boost.gameObject.SetActive(false);
        scheduleUpdate = false;
        
        //if (this.getReferenceCount() != 2)
        //{
        //    CCASSERT(0, "ref count must = to 2");
        //}
        {
        }
        enemySprite.state.SetAnimation(0, "deathStart", false);
        enemySprite.state.AddAnimation(0, "death", false,0);
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, transform.position, false, 1, 0.5f, Globals.CocosToUnity(150));
        //transform.DOBlendableMoveBy(new Vector2(0, Globals.CocosToUnity(-100)), 15).OnComplete(base.Destroy);
        //if(gameObject != null && gameObject.activeInHierarchy)
        //    StartCoroutine(DestroyCoroutine());
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }

    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        GameSharedData.Instance.enemyList.Remove(this);
        yield return new WaitForSeconds(5);
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + Globals.CocosToUnity(25), transform.position.y + Globals.CocosToUnity(25)), false, 1, 2, 0);
    }
}
