﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

public class CustomRectButton : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerExitHandler, IPointerEnterHandler,
    IPointerDownHandler, IPointerUpHandler
{
    [System.Serializable]
    public class MyEventType : UnityEvent { }

    [SerializeField] MyEventType onClick;
    [SerializeField] Animator buttonAnim;
    bool isPressed, isTriggered;

    private void Start()
    {
        isPressed = false;
        isTriggered = false;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        buttonAnim.SetBool("Highlighted", false);
        buttonAnim.SetBool("Normal", false);
        buttonAnim.SetBool("Pressed", true);
        isPressed = true;
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        buttonAnim.SetBool("Highlighted", true);
        buttonAnim.SetBool("Normal", false);
        buttonAnim.SetBool("Pressed", false);
        isTriggered = false;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (isTriggered)
            return;
        
        var ped = new PointerEventData(EventSystem.current);
        ExecuteEvents.Execute(gameObject, ped, ExecuteEvents.pointerUpHandler);

        buttonAnim.SetBool("Highlighted", false);
        buttonAnim.SetBool("Normal", false);
        buttonAnim.SetBool("Pressed", false);

        buttonAnim.SetBool("Normal", true);
        isPressed = false;
        isTriggered = false;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        buttonAnim.SetBool("Highlighted", false);
        buttonAnim.SetBool("Normal", false);
        buttonAnim.SetBool("Pressed", false);

        if (isPressed)
        {
            isPressed = false;
            isTriggered = true;
            buttonAnim.SetBool("Highlighted", true);
        }
    }

    public void CheckOnClick()
    {
        if (isTriggered)
        {
            isTriggered = false;
            onClick?.Invoke();
        }
    }
}
