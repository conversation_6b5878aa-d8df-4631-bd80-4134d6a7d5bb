﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HealthBar : MonoBehaviour
{
     public SpriteRenderer health;
     public GameObject bar;
    string tweenID = "";

    public string TweenID { get { return tweenID; } }

    [HideInInspector] public float ScaleRatio;

    private void Awake()
    {
        tweenID = "HB" + gameObject.GetInstanceID().ToString();
    }


    public void SetDisplayHealth(float percentage)
    {
        if (percentage < 0)
        {
            percentage = 0;
        }
        if (percentage > 1)
        {
            percentage = 1;
        }
        percentage -= 0.01f;
        bar.transform.localScale= new Vector2(percentage, 1);
        //bar.transform.position = new Vector2(transform.position.x + health.bounds.size.x, transform.position.y);
        //bar->setPosition(health->getPosition().x + health->getBoundingBox().size.width, health->getPosition().y);
        
    }
}
