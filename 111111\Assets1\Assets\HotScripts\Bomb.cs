﻿using UnityEngine;
using System.Collections;
using System;
public class Bomb : EnemyMissile
{

    [SerializeField] private Sprite defaultSprite;
    private float verticleVelocity;
    private float horizontalVelocity;
    private float gravity;

    

    public override void Init()
    {
        if (hasHit)
        {
            return;
        }
        isCollidable = false;
        gravity = 0.005f;
        verticleVelocity = -0.03f;
        SetDamage(10);
        duration = 5;
        transform.rotation = Quaternion.Euler(0, 0, 20);
        transform.localScale = new Vector2(-1, 1);
        base.Init();
        //    missileSprite = SkeletonAnimation::createWithJsonFile("res/Arsenal/missile.json", "res/Arsenal/missile.atlas");
        //missileSprite = Sprite::createWithSpriteFrameName("bomb.png");
        //this.addChild(missileSprite);
        ////    missileSprite.setAnimation(0, "bomberBomb", true);
        //missileSprite.setScale(-1, 1);
        //missileSprite.setRotation(-20);
        //isCollidable = false;
        //this.runAction(Sequence::create(DelayTime::create(0.2f), CallFunc::create(CC_CALLBACK_0(Bomb::changeCollisionMode, this)), NULL));
        //this.scheduleUpdate();

        Invoke(nameof(ChangeCollisionMode), 0.2f);
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            verticleVelocity = verticleVelocity - gravity * Time.deltaTime * 0.6f;
            transform.position = new Vector2(transform.position.x + horizontalVelocity, transform.position.y + verticleVelocity);
            float dir = Vector2.SignedAngle(Vector2.right, transform.right);
            var rotationDir = dir < 0 ? 360 + dir : dir;
            
            //print(rotationDir);
            if (transform.localScale.x < 0)
            {
                transform.rotation = Quaternion.Euler(0, 0, dir + verticleVelocity * 7.5f);// setRotation(missileSprite.getRotation() - verticleVelocity * 0.075);
            }
            else
            {
                transform.rotation = Quaternion.Euler(0, 0, dir - verticleVelocity * 7.5f);// missileSprite.setRotation(missileSprite.getRotation() + verticleVelocity * 0.075);

            }
            
            if (transform.localScale.x<0&&dir < -50)
            {
                transform.rotation = Quaternion.Euler(0, 0, -50); //missileSprite.setRotation(70);
            }
            else if (transform.localScale.x > 0 && dir > 70)
            {
                transform.rotation = Quaternion.Euler(0, 0, 70); //missileSprite.setRotation(70);
            }
        }
    }

    public void CreateSmoke()
    {

    }

    public void RemoveObject(GameObject obj)
    {

    }

    public void SetVerticleVelocity(float velocity)
    {
        verticleVelocity = velocity;
    }

    public void SetHorizontalVelocity(float velocity)
    {
        horizontalVelocity = velocity;
    }

    public void ChangeCollisionMode()
    {
        isCollidable = true;
    }

    public void SetGravity(float val)
    {
        gravity = val;
    }

    public override void RemoveMissile()
    {
        missileSprite.sprite = defaultSprite;
        missileSprite.transform.rotation = Quaternion.identity;
        base.RemoveMissile();
    }
}
