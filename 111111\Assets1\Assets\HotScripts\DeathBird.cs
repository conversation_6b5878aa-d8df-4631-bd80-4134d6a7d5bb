using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;

public class DeathBird : Enemy
{
    #region Variables
    Vector2 enemyAcceleration;
    bool boostOn;
    bool allowShooting;
    bool chargingLaser;
    bool isLaserActivated;
    float speedLimit;
   
    #endregion

    #region UI
    [SerializeField] private GameObject boost;
    [SerializeField] private Sprite laser;
    [SerializeField] SkeletonAnimation laserImpact;
    [SerializeField] SkeletonAnimation laserImpactStart;
    [SerializeField] Sprite bulletSprite;
    #endregion

    #region CONSTANTS
    private const float ENEMYSCALE = 0.23f;
    private const float GRAVITY = 0.1f;
    private const float DRAG = 0.99f;
    #endregion

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        InitVariables();
        InitializeEnemyPosition();
        HandleBoost();
    }

    private void InitVariables()
    {
        scheduleUpdate = true;
        enemyAcceleration = Vector2.zero;
        speedLimit = 6 * Globals.enemySpeedMultiplier;
        boostOn = false;
        allowShooting = false;
        allowStack = false;
        chargingLaser = false;
        enemyCollisionRadius = Globals.CocosToUnity(100);
        _jsonScale = 1f;
        allowPushBack = true;

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Red);
        //healthBar.gameObject.SetActive(false);
        //Globals.bossPosition = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y - Globals.CocosToUnity(100));
    }

    private void InitializeEnemyPosition()
    {
        if (Random.value < 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1500), Random.value * Globals.CocosToUnity(100) + Globals.CocosToUnity(50));
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1500), Random.value * Globals.CocosToUnity(100) + Globals.CocosToUnity(50));
        }
        enemySprite.state.Event += HandleSpineAnimation;
    }

    private void HandleSpineAnimation(TrackEntry entry, Spine.Event spineEvent)
    {
        //if(player.Stats.mode!=GameManager.instance.player.Mode)
        if (spineEvent.Data.Name == "shootleft")
        {
            Shoot();
        }
    }

    private void HandleBoost()
    {
        boost.SetActive(true);
        //boost.transform.position = new Vector2(Globals.CocosToUnity(-10) * Mathf.Sin(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 90), Globals.CocosToUnity(-5) + Globals.CocosToUnity(-10) * Mathf.Cos(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 90));
        boost.transform.SetRotation(enemySprite.transform.eulerAngles.z);
    }

    void Update()
    {
       
        //if (healthBar)
        //{
        //    healthBar.transform.position = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y - Globals.CocosToUnity(75));
        //}
        
        BoostLogic();
        RotationLogic();
        
        if (enemySprite.transform.eulerAngles.z < 90 || enemySprite.transform.eulerAngles.z > 90 + 180)
        {
            enemySprite.transform.SetScaleY(ENEMYSCALE);
        }
        else
        {
            enemySprite.transform.SetScaleY(-ENEMYSCALE);
        }
    }

    private void RotationLogic()
    {
        Globals.rotateNodeToPointLerp(stats.turnSpeed, enemySprite.gameObject, player.transform.position);

        float angleNodeToRotateTo = Globals.getAngleOfTwoVectors(enemySprite.transform.position, player.transform.position);
        float nodeCurrentAngle = Globals.getCurrentAngle(enemySprite.gameObject);
        float diffAngle = Globals.getAngleDifference(angleNodeToRotateTo, nodeCurrentAngle);
        

        if (diffAngle > -30 && diffAngle < 30)
        {
            if (!allowShooting)
            {
                enemySprite.state.SetAnimation(0, "shootEnemyPlane8", true);
            }
            allowShooting = true;
        }
        else
        {
            if (allowShooting)
            {
                enemySprite.state.SetAnimation(0, "idle", true);
            }
            allowShooting = false;
        }
    }

    void BoostLogic()
    {
        

        if (Vector2.SqrMagnitude(enemySprite.transform.position-player.transform.position) > Globals.CocosToUnity(10000))
        {
            boostOn = true;
            boost.SetActive(true);
        }
        else
        {

            boostOn = false;
            boost.SetActive(false);

        }
        
        if (!isLaserActivated && !chargingLaser)
        {
            transform.position = new Vector2(transform.position.x + enemyAcceleration.x * Time.deltaTime * Globals.CocosToUnity(60), transform.position.y + enemyAcceleration.y * Time.deltaTime * Globals.CocosToUnity(60)*0.25f);
        }
        if (boostOn)
        {
            if (Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position) > stats.distanceToShoot)
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed / 4;
                enemyAcceleration.x = Mathf.Clamp(enemyAcceleration.x, -speedLimit - 3, speedLimit + 3);
                
                enemyAcceleration.y = enemyAcceleration.y + Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)) * stats.speed / 4;
                enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit - 3, speedLimit + 3);
            }

            else
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed;
                if (enemyAcceleration.x > speedLimit)
                {
                    enemyAcceleration.x -= 0.4f;
                }

                if (enemyAcceleration.x < -speedLimit)
                {
                    enemyAcceleration.x += 0.4f;

                }

                enemyAcceleration.y = enemyAcceleration.y + Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * stats.speed;
                if (enemyAcceleration.y > speedLimit)
                {
                    enemyAcceleration.y -= 0.4f;
                }

                if (enemyAcceleration.y < -speedLimit)
                {
                    enemyAcceleration.y += 0.4f;
                }

                enemyAcceleration.x = Mathf.Clamp(enemyAcceleration.x, -speedLimit - 3, speedLimit + 3);
                enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit - 3, speedLimit + 3);
            }
        }
        else
        {
            enemyAcceleration = new Vector2(enemyAcceleration.x, enemyAcceleration.y - GRAVITY);
            enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -speedLimit * 1.5f, speedLimit * 1.5f);
            enemyAcceleration.x = enemyAcceleration.x * DRAG;
        }

        if (transform.position.y < Globals.CocosToUnity(1))
        {
            transform.position = new Vector2(transform.position.x, Globals.CocosToUnity(1));
        }
    }

    private void Shoot()
    {
        if (!allowShooting)
        {
            return;
        }
        shootRandom = Random.Range(0, 10000);
        if (shootRandom > skillShootProbability)
        {
            return;
        }

        bool didFindBullet = false;
        Bullet bullet = null;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);
        bullet.PushBack = 0.25f;
        if (enemySprite.transform.localScale.y < 0)
        {
            bullet.transform.position = new Vector2(transform.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 45), transform.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z - 45));
        }
        else
        {
            bullet.transform.position = new Vector2(transform.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 45), transform.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z - 45));
        }
        bullet.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z - 90);
        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));
        
        bullet.PlayBulletAnim(2.5f, dest, false);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
        //if (!allowShooting)
        //{
        //    return;
        //}
        //bool didFindBullet = false;
        //Bullet bullet = new Bullet();
        //foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        //{
        //    if (!b.isInUse)
        //    {
        //        bullet = b;
        //        bullet.isInUse = true;
        //        didFindBullet = true;
        //        break;
        //    }
        //}
        //if (!didFindBullet)
        //{
        //    return;
        //}


        //enemySprite.timeScale = stats.attackSpeed;

        //bullet.setDamage(stats.bulletDamage);
        //bullet.transform.SetScale(1.5f);
        //bullet.spriteRenderer.sprite = bulletSprite;
        //bullet.duration = 2.5f;

        //if (enemySprite.transform.localScale.y < 0)
        //{
        //    bullet.transform.position = new Vector2(transform.position.x + Globals.CocosToUnity(100) * Mathf.Sin(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 75), transform.position.y + Globals.CocosToUnity(100) * Mathf.Cos(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 75));
        //}
        //else
        //{
        //    bullet.transform.position = new Vector2(transform.position.x + Globals.CocosToUnity(100) * Mathf.Sin(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 105), transform.position.y + Globals.CocosToUnity(100) * Mathf.Cos(Mathf.Deg2Rad * enemySprite.transform.eulerAngles.z + 105));
        //}
        //bullet.gameObject.SetActive(true);
        //bullet.PushBack = false;
        //bullet.transform.SetRotation(enemySprite.transform.eulerAngles.z + 90f);
        //Vector2 dest = new Vector2(Globals.CocosToUnity(2500) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.eulerAngles.z), Globals.CocosToUnity(2500) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.eulerAngles.z));
        //bullet.PlayBulletAnim(2, dest,false);
        //GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 2f;//0.35f;
        stats.health = baseStats.health = 100;//(player.Stats.maxHealth + 20) + Globals.difficulty * 10;
        stats.turnSpeed = baseStats.turnSpeed = 100;
        stats.bulletDamage = baseStats.bulletDamage = 7;
        stats.attackSpeed = baseStats.attackSpeed = 1;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50; ;
        stats.coinAwarded = baseStats.coinAwarded = 10;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
    }
}
