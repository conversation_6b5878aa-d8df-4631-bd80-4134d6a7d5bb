using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CatnobiSidekick : Sidekick
{
    [SerializeField] TrailRenderer trail;

    enum CatnobiState
    {
        Idle,
        Attack1,
        Attack2,
        Attack3,
        Attack4,
        Attack5,
        Attack6,
        Attack7,
        Attack8,
        Rest
    };
    bool allowFollow;
    bool waitCheckBetweenAttack;
    bool scheduleMyUpdate;
    int followCounter;
    int restCounter;
    CatnobiState currentState;
    const float timeBetweenAttacks = 0.1f;

    private void Start()
    {
        Init();
        
    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        allowFollow = true;
        scheduleMyUpdate = true;
        currentState = CatnobiState.Idle;
        followCounter = 0;
        waitCheckBetweenAttack = false;
        trail.emitting = false;

        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);
        transform.localScale = new Vector3(scale, scale, 1);
        //MotionStreak* streak = MotionStreak::create(0.4f, 10, 45, Color3B::WHITE, "res/Arsenal/catnobiStreak.png");

        SetStartingPosition();

        //if (gameType == GameType::Training)
        //{
        //    this->scheduleUpdate();
        //}
        //else
        //{
        Invoke("UnscheduleUpdate", 1.2f);
        //}
    }

    IEnumerator UnscheduleUpdate()
    {
        yield return new WaitForSeconds(1.2f);

        scheduleMyUpdate = false;
    }


    void Update()
    {
        FollowPlayer();

        if (!sidekickSkeleton || !scheduleMyUpdate || Globals.resetControls)
        {
            return;
        }


        if (currentState == CatnobiState.Rest)
        {
            SetSideKickScale();
            FollowPlayer();
            restCounter++;

            if (restCounter > 250)
            {
                restCounter = 0;
                currentState = CatnobiState.Idle;

            }

        }
        else
        {
            if (waitCheckBetweenAttack == false)
            {
                FindBullets();
            }
        }


        if (currentState == CatnobiState.Idle)
        {
            SetSideKickScale();
            FollowPlayer();
        }

    }

    void FindBullets()
    {
        followCounter++;
        foreach (Bullet bullet in GameSharedData.Instance.enemyBulletInUse)
        {
            //if (bullet->bulletType != g_AllELEMENTS::EnemyBulletBlueExplosion && bullet->bulletType != g_AllELEMENTS::EnemyBulletFireball && bullet->bulletType != g_AllELEMENTS::EnemyBulletExplosive)
            //{
            if (Vector3.Distance(player.transform.position, bullet.transform.position) < Globals.CocosToUnity(304))
            {
                trail.emitting = true;
                transform.position = (Vector2)bullet.transform.position;
                StopCoroutine(nameof(TurnOffTrail));
                StartCoroutine(nameof(TurnOffTrail));
                ManageState();


                SpawnBullet(bullet);
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.catsWalkerAttack);
                //Globals.PlaySound("res/Sounds/SFX/catsWalkerAttack.mp3");
                bullet.HasHit();
                //FIXME: here
                return;
            }
            //}
        }
        if (followCounter > 60)
        {
            currentState = CatnobiState.Idle;
            followCounter = 0;
        }
    }

    IEnumerator TurnOffTrail()
    {
        yield return new WaitForSeconds(0.2f);

        trail.emitting = false;
    }


    void SpawnBullet(Bullet enemyBullet)
    {
        //if (FileHandler::getInstance()->currentEvent > kBossSpiderCat && gameType == GameType::Arena)
        //{
        //    Bullets* bullet = (Bullets*)enemyBullet;
        //    GameSharedData::getInstance()->g_bulletsArray.eraseObject(bullet);


        //    bullet->bulletSprite->stopAllActions();

        //    bullet->bulletSprite->runAction(Shared::createAnimation("bulletHitA00%d.png", 1, 5, false));

        //    return;
        //}
        Bullet bulletLayer = null;
        GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
        bulletLayer = go.GetComponent<Bullet>();
        // bool didFindBullet = false;
        // foreach (Bullet b in GameSharedData.Instance.playerBulletPool)
        // {
        //     if (!b.isInUse)
        //     {
        //         bulletLayer = b;
        //         didFindBullet = true;
        //         break;
        //     }
        // }
        // if (!didFindBullet)
        // {
        //     return;
        // }

        bulletLayer.setDamage((GameData.instance.fileHandler.currentMission != 0) ? damage : (enemyBullet.getDamage() * 2));//(enemyBullet.getDamage() * 2);

        bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);
        bulletLayer.SetSpriteFrame(enemyBullet.spriteRenderer.sprite);
        bulletLayer.transform.localScale = enemyBullet.transform.localScale;


        bulletLayer.transform.position = enemyBullet.transform.position;
        bulletLayer.isRemovable = true;
        bulletLayer.isInUse = true;
        bulletLayer.gameObject.SetActive(true);
        float duration = 2;
        float dir = Vector2.SignedAngle(Vector2.right, enemyBullet.transform.right);
        dir = dir < 0 ? 360 + dir : dir;
        dir = (dir + 180) % 360;
        //float dir = enemyBullet.transform.eulerAngles.z + 180;
        bulletLayer.transform.rotation = Quaternion.Euler(0, 0, dir);
        Vector2 dest = new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos((dir + 90) * Mathf.Deg2Rad),
            Globals.CocosToUnity(1250) * Mathf.Sin((dir + 90) * Mathf.Deg2Rad));
        bulletLayer.PlayBulletAnim(duration, dest, false, null, null);
        GameSharedData.Instance.playerBulletInUse.Add(bulletLayer);


    }

    void ManageState()
    {
        //sideKickSprite->stopAllActions();
        if (currentState == CatnobiState.Idle)
        {
            currentState = CatnobiState.Attack1;
            sidekickSkeleton.state.SetAnimation(0, "attack1", false);
            waitCheckBetweenAttack = true;

        }
        else if (currentState == CatnobiState.Attack1)
        {
            currentState = CatnobiState.Attack2;
            sidekickSkeleton.state.SetAnimation(0, "attack2", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack2)
        {
            currentState = CatnobiState.Attack3;
            sidekickSkeleton.state.SetAnimation(0, "attack3", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack3)
        {
            currentState = CatnobiState.Attack4;
            sidekickSkeleton.state.SetAnimation(0, "attack4", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack4)
        {
            currentState = CatnobiState.Attack5;
            sidekickSkeleton.state.SetAnimation(0, "attack1", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack5)
        {
            currentState = CatnobiState.Attack6;
            sidekickSkeleton.state.SetAnimation(0, "attack2", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack6)
        {
            currentState = CatnobiState.Attack7;
            sidekickSkeleton.state.SetAnimation(0, "attack3", false);
            waitCheckBetweenAttack = true;
        }
        else if (currentState == CatnobiState.Attack7)
        {
            currentState = CatnobiState.Attack1;
            sidekickSkeleton.state.SetAnimation(0, "attack4", false);
            waitCheckBetweenAttack = true;
        }

        StopCoroutine(nameof(RevertToIdle));
        StartCoroutine(nameof(RevertToIdle));
    }

    IEnumerator RevertToIdle()
    {
        yield return new WaitForSeconds(timeBetweenAttacks);

        IdleAnimation();
    }

    void IdleAnimation()
    {
        waitCheckBetweenAttack = false;
        sidekickSkeleton.state.SetAnimation(0, "idle", true);
    }

    void FollowPlayer()
    {
        SidekicksUpdate();
    }

    void SetSideKickScale()
    {

        if (player.transform.position.x > transform.position.x)
        {
            transform.localScale = new Vector3(scale, scale, 1);
        }
        else
        {
            transform.localScale = new Vector3(-scale, scale, 1);
        }

    }

}
