using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;
using UnityEngine.UI;
using Spine;
using Spine.Unity;
using TMPro;
using DG.Tweening;
using System.Linq;
using UnityEngine.SceneManagement;
public class SideMenuMapMobileController : MonoBehaviour
{
    [SerializeField] private GameObject mainWindow;
    [SerializeField] private RectTransform rect;
    [SerializeField] private TextMeshProUGUI missionName;
    [SerializeField] private TextMeshProUGUI missionDescription;
    [SerializeField] private TextMeshProUGUI missionNumber;
    [SerializeField] private SkeletonGraphic[] missionImageSpineData = null;
    [SerializeField] private Sprite[] abilitySprites = null;
    [SerializeField] private Image[] abilityRenderer = null;
    [SerializeField] private Sprite[] sidekickSprites;
    [SerializeField] private Image blackBgSprite;
    [SerializeField] private CustomButton mapButton;
    [SerializeField] private CustomButton playButton;
    [SerializeField] private CustomButton closeButton;
    [SerializeField] private Image fadeImage;

    private SkeletonGraphic missionImageSpineObject = null;
    public Action addedCloseFunction;
    private List<CustomButton> menuArray = new List<CustomButton>();
    private Sequence seq;

    private void OnEnable()
    {
        Init();
    }

    private void Init()
    {

        Globals.isMouseOverUI = true;
        mapButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["mapButton"] as string;
        playButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["playButton"] as string;
        playButton.SetButtonColor(playButton.GREEN);
        playButton.defaultAction =() =>
        {
            OnPlayButtonCall();
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

            //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        };

        mapButton.SetButtonColor(playButton.BLUE);
        mapButton.defaultAction = () =>
        {
            CloseButtonCall();
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        };
        closeButton.defaultAction = () =>
        {
            CloseButtonCall();
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        };
        menuArray.Add(playButton);
        menuArray.Add(mapButton);
        //unlockLayer = Node::create();
        //bgSprite.addChild(unlockLayer);
        //unlockLayer.setPosition(bgSprite.getContentSize().width - 70, 430);
        //

        //menuSelected = 2;
        //TODO
        //        auto listener = EventListenerTouchOneByOne::create();

        //        listener.onTouchBegan = [=](Touch * touch, Event * event){
        //        return true;
        //    };
        //    listener.onTouchMoved = [=](Touch* touch, Event* event){



        //        return true;
        //    };
        //    listener.onTouchEnded = [=](Touch* touch, Event* event){
        //        if (isOpen())
        //        {
        //            if (!bgSprite.getBoundingBox().containsPoint(rootNode.convertToNodeSpace(touch.getLocation())) &&
        //               rootNode.convertToNodeSpace(touch.getLocation()).distance(customCloseButton.getPosition()) > 60)
        //            {
        //                CloseButtonCall();
        //            }
        //            event.stopPropagation();
        //        }


        //    };
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);
        //allLabels.clear();

        //currentSelectedGamePlayMode = static_cast<int>(gameModeType);

        //auto keyListener = EventListenerKeyboard::create();
        //keyListener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)
        //    {


        //    if (this.getPosition().x < Director::getInstance().getWinSize().width - this.getBoundingBox().size.width / 2)
        //    {

        //        if (this.isOpen())
        //        {
        //                event.stopPropagation();

        //            if (keyCode == EventKeyboard::KeyCode::KEY_ESCAPE)
        //            {
        //                CloseButtonCall();
        //            }

        //            if (keyCode == EventKeyboard::KeyCode::KEY_ENTER)
        //            {


        //                if (menuArray.size() > 0 && menuSelected == 2)
        //                {
        //                    _selectedButton = clampf(_selectedButton, 0, menuArray.size());
        //                    menuArray.at(_selectedButton).activate();
        //                    Shared::playSound(SOUND_BUTTON_TAP);

        //                }
        //            }

        //            if (keyCode == EventKeyboard::KeyCode::KEY_LEFT_ARROW)
        //            {

        //                if (menuSelected == 2)
        //                {
        //                    if (menuArray.size() > 0)
        //                    {
        //                        _selectedButton++;
        //                        _selectedButton = clampf(_selectedButton, 0, menuArray.size() - 1);
        //                        for (auto node : menuArray)
        //                        {
        //                            node.stopAttention();
        //                        }
        //                        menuArray.at(_selectedButton).callForAttention(1.0f);
        //                        Shared::playSound(SOUND_HOVER);

        //                    }
        //                }
        //                else if (menuSelected == 1)
        //                {
        //                    if (currentSelectedGamePlayMode > 0)
        //                    {
        //                        currentSelectedGamePlayMode--;
        //                        //                            radioGroup.setSelectedButton(currentSelectedGamePlayMode);
        //                        Shared::playSound(SOUND_HOVER);

        //                    }
        //                }
        //            }

        //            else if (keyCode == EventKeyboard::KeyCode::KEY_RIGHT_ARROW)
        //            {
        //                if (menuSelected == 2)
        //                {
        //                    if (menuArray.size() > 0)
        //                    {
        //                        _selectedButton--;
        //                        _selectedButton = clampf(_selectedButton, 0, menuArray.size() - 1);
        //                        for (auto node : menuArray)
        //                        {
        //                            node.stopAttention();
        //                        }
        //                        menuArray.at(_selectedButton).callForAttention(1.0f);
        //                        Shared::playSound(SOUND_HOVER);

        //                    }
        //                }
        //                else if (menuSelected == 1)
        //                {
        //                    if (currentSelectedGamePlayMode >= 0 && currentSelectedGamePlayMode < 2)
        //                    {
        //                        currentSelectedGamePlayMode++;
        //                        //                            radioGroup.setSelectedButton(currentSelectedGamePlayMode);
        //                        Shared::playSound(SOUND_HOVER);


        //                    }
        //                }

        //            }
        //            else if (keyCode == EventKeyboard::KeyCode::KEY_DOWN_ARROW)
        //            {
        //                if (menuSelected == 1)
        //                {
        //                    menuSelected = 2;
        //                    menuArray.at(_selectedButton).callForAttention(1.0f);
        //                    Shared::playSound(SOUND_HOVER);


        //                }
        //            }
        //            if (keyCode == EventKeyboard::KeyCode::KEY_UP_ARROW)
        //            {
        //                if (menuSelected == 2)
        //                {
        //                    menuSelected = 1;
        //                    Shared::playSound(SOUND_HOVER);

        //                    for (auto node : menuArray)
        //                    {
        //                        node.stopAttention();
        //                    }
        //                }
        //            }


        //        }
        //    }

        int rewardInCoins = 0;
        int rewardInXp = 0;
        rewardInCoins = (int)(GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Rewards"] as PList)["Coins"];
        rewardInXp = (int)(GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Rewards"] as PList)["Xp"];
    }

    private void OnExit()
    {

    }

    public void OnPlayButtonCall()
    {

        Globals.isMouseOverUI = false;
        mapButton.SetInteractable(false);
        closeButton.SetInteractable(false);
        playButton.SetInteractable(false);
        fadeImage.raycastTarget = true;
        fadeImage.DOFade(1, 0.25f).OnComplete(() => { SceneManager.LoadScene("GameScene"); });
    }

    private void ShowUnlocks()
    {
        foreach (Image i in abilityRenderer)
        {
            i.gameObject.SetActive(false);
        }
        int index = 0;
        if (GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission).ContainsKey("Unlock"))
        {
            PList MainMap = GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Unlock"] as PList;
            int abilityCount = MainMap.Count;
            string abilityName;
            for (int i = 0; i < abilityCount; i++)
            {
                index = i;
                //ValueMap MainMap = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission)["Unlock"].asValueMap(); TODO normal abilities
                //string _imageString = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission)["Unlock"].asValueMap()[pair.first].asValueMap()["imageName"].asString();

                abilityRenderer[i].material.SetFloat("_GrayscaleAmount", 0);
                SkeletonGraphic lockAnim = abilityRenderer[i].transform.GetChild(0).GetComponent<SkeletonGraphic>();
                lockAnim.gameObject.SetActive(false);
                abilityName = (MainMap[MainMap.ElementAt(i).Key] as PList)["imageName"] as string;
                foreach (Sprite s in abilitySprites)
                {
                    string[] str = abilityName.Split('.');
                    if (s.name == str[0])
                    {
                        abilityRenderer[i].sprite = s;
                    }
                }

                abilityRenderer[i].gameObject.SetActive(true);
                if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
                {
                    lockAnim.transform.SetScale(0.0f);
                    lockAnim.gameObject.SetActive(true);
                    lockAnim.AnimationState.SetAnimation(0, "idle", true);
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.25f).AppendCallback(() => lockAnim.gameObject.SetActive(true)).Append(lockAnim.transform.DOScale(new Vector3(0.22f, 0.22f, 0.22f), 1.5f).SetEase(Ease.OutElastic));
                    seq.Play();
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.35f).AppendCallback(() =>
                    {
                        abilityRenderer[i].material.SetFloat("_GrayscaleAmount", 1);
                    });
                    seq.Play();
                }
            }
        }
        PList sideKickMap;
        int sideKickCount = (int)(GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Rewards"] as PList)["SideKick"];
        string sideKickName;
        if (sideKickCount > 0)
        {
            index++;

            abilityRenderer[index].material.SetFloat("_GrayscaleAmount", 0);
            SkeletonGraphic lockAnim = abilityRenderer[index].transform.GetChild(0).GetComponent<SkeletonGraphic>();
            lockAnim.gameObject.SetActive(false);
            sideKickMap = (GameData.instance.GetShop()["Category5"] as PList)["Gun" + sideKickCount.ToString()] as PList;
            sideKickName = sideKickMap["ImagePath"]  as string;
            foreach (Sprite s in sidekickSprites)
            {
                if (s.name == sideKickName)
                {
                    abilityRenderer[index].sprite = s;
                }
            }

            abilityRenderer[index].gameObject.SetActive(true);
            if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
            {

                lockAnim.transform.SetScale(0.0f);
                lockAnim.AnimationState.SetAnimation(0, "idle", true);
                seq = DOTween.Sequence();
                seq.AppendInterval(1.25f).AppendCallback(() => lockAnim.gameObject.SetActive(true)).Append(lockAnim.transform.DOScale(new Vector3(0.22f, 0.22f, 0.22f), 1.5f).SetEase(Ease.OutElastic));
                seq.Play();
                seq = DOTween.Sequence();
                seq.AppendInterval(1.35f).AppendCallback(() =>
                {
                    abilityRenderer[index].material.SetFloat("_GrayscaleAmount", 1);
                });
                seq.Play();
            }

        }
    }


    private void UpdateSpineImage()
    {
        foreach (SkeletonGraphic s in missionImageSpineData)
        {
            s.gameObject.SetActive(false);
        }

        missionImageSpineObject = missionImageSpineData[GameData.instance.fileHandler.currentMission - 1];
        //ValueMap missionMap = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission);

        //missionImageSpineObject = SkeletonAnimation::createWithJsonFile(missionMap.at("Image").asString() + ".json", missionMap.at("Image").asString() + ".atlas");TODO

        //_clipNode.addChild(missionImageSpineObject, 1);
        missionImageSpineObject.gameObject.SetActive(true);
        //        missionImageSpineObject.AnimationState.SetAnimation(0, "mapAnim", true);

        if (GameData.instance.fileHandler.currentMission == 2)//FileHandler::getInstance().currentMission == 2)
        {
            //missionImageSpineObject.Skeleton.SetSkin("enemyPlaneLevel6");
        }
        if (GameData.instance.fileHandler.currentMission == 29)//FileHandler::getInstance().currentMission == 29)
        {
            // missionImageSpineObject.Skeleton.SetSkin("dragonGreen");
        }
        if (GameData.instance.fileHandler.currentMission == 24)// FileHandler::getInstance().currentMission == 24)
        {
            //missionImageSpineObject.transform.SetScale(0.185f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 21)// FileHandler::getInstance().currentMission == 21)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 27)// FileHandler::getInstance().currentMission == 27)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "arenaIdle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.42f);
        }
        if (GameData.instance.fileHandler.currentMission == 29)// FileHandler::getInstance().currentMission == 29)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "idle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 50);
        }
        if (GameData.instance.fileHandler.currentMission == 30)// FileHandler::getInstance().currentMission == 30)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.3f);
        }
        Globals.Rescale(missionImageSpineObject.gameObject, missionImageSpineObject.transform.localScale.x);
    }

    public void ShowMissionInfo()
    {
        gameObject.SetActive(true);
        PList vMap = GameData.instance.GetMissions();
        string missionVal = "Mission"+GameData.instance.fileHandler.currentMission;

        string c = GameData.instance.fileHandler.currentMission.ToString();
        string missionNum = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["mission"] as string;
        missionNum+=c;

        string missionTxt = (GameData.instance.GetTextData(Constants.GAME_TEXT.MISSION_DATA)[missionVal] as PList)["Text"] as string;
        string missionType = (vMap[missionVal]as PList)["Type"] as string;

        if (missionType == "Boss")
        {
     
            int missionBossNumber = (int)(vMap[missionVal]as PList)["Boss Number"];
            GameData.instance.fileHandler.currentEvent = missionBossNumber;
            PlayerPrefs.SetInt("currentEvent", GameData.instance.fileHandler.currentEvent);

        }


        string missionDescriptionValue = (GameData.instance.GetTextData(Constants.GAME_TEXT.MISSION_DATA)[missionVal]as PList)["Description"] as string;
        missionNumber.text = missionNum;
        //missionNumber.text = setString(Shared::convertStringNumbersToArabic(missionNumber.getString())); TODO

        missionName.text = missionTxt;
        //if (missionName.getLabelType() == Label::LabelType::TTF)
        //{
        //    TTFConfig config = missionName.getTTFConfig();
        //    config.fontSize = 100;
        //    missionName.setTTFConfig(config);
        //}
        //else
        //{
        //    //        missionName.setSystemFontSize(80);
        //}

        missionDescription.text = missionDescriptionValue;

        //this.stopAllActions();\

        DOTween.Sequence().AppendInterval(0.02f).Append(mainWindow.transform.DOScale(Vector2.one, 0.15f)).Play();
        DOTween.Sequence().AppendInterval(0.02f).Append(blackBgSprite.DOFade(0.58f,0.15f)).Play();
        if (rect.anchoredPosition.x < 50)// this.getPosition().x > Director::getInstance().getWinSize().width - 5)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

            //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
        }

        if (missionType == "Boss")
            Globals.gameType = GameType.Arena;
        else
            Globals.gameType = GameType.Training;

        //GameData.instance.fileHandler.saveData(); TODO
        //FixMe Might remove this
        //if (DIFFICULTYMENU)
        //{
        //    int rewardInCoins = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Coins").asInt();
        //    int rewardInXp = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Xp").asInt(); ;

        //    df1.changeStats(rewardInCoins / 2, rewardInXp / 2);
        //    df2.changeStats(rewardInCoins, rewardInXp);
        //    df3.changeStats(rewardInCoins * 2, rewardInXp * 2);
        //}
        UpdateSpineImage();
        ShowUnlocks();
    }

    public void CloseButtonCall()
    {
        if (!IsOpen())
        {
            return;
        }

        Globals.isMouseOverUI = false;
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

        //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);

        addedCloseFunction();
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        DOTween.Sequence().AppendInterval(0.175f).Append(mainWindow.transform.DOScale(Vector2.zero, 0.05f)).AppendCallback(()=>{gameObject.SetActive(false); }).Play();
        DOTween.Sequence().Append(blackBgSprite.DOFade(0, 0.125f)).Play();
    }

    public bool IsOpen()
    {
        if (mainWindow.transform.localScale.x >0.9f)
        {
            return true;
        }
        return false;
    }

}

