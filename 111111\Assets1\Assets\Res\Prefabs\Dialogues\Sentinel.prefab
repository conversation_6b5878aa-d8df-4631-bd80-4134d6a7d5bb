%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3197049358744646659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7503467632706360410}
  m_Layer: 0
  m_Name: Sentinel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7503467632706360410
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3197049358744646659}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2435854767984876838}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -736, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &5945573263222071390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2435854767984876838}
  - component: {fileID: 4553747120877126857}
  - component: {fileID: 9114663594757206011}
  - component: {fileID: 6551458368724992948}
  m_Layer: 0
  m_Name: SkeletonGraphic (sentinel)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2435854767984876838
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945573263222071390}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7503467632706360410}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -884.8092, y: 0}
  m_SizeDelta: {x: 1876.866, y: 665.46606}
  m_Pivot: {x: 0, y: 0.2423678}
--- !u!222 &4553747120877126857
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945573263222071390}
  m_CullTransparentMesh: 1
--- !u!114 &9114663594757206011
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945573263222071390}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: b66cf7a186d13054989b33a5c90044e4, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: 0f49cfb666c7c468197c303bb21dd6f6, type: 2}
  initialSkinName: default
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: talk
  startingLoop: 1
  timeScale: 1
  freeze: 0
  unscaledTime: 1
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      pmaVertexColors: 1
      tintBlack: 0
      calculateTangents: 0
      addNormals: 0
      immutableTriangles: 0
--- !u!114 &6551458368724992948
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5945573263222071390}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 11cd9de1340b642ed96ebef70d3158c8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonGraphic: {fileID: 9114663594757206011}
  m_preserveAspectRatio: 0
  m_SetToSetupPoseWhenScaling: 0
  m_isEditorUpdateStopped: 0
  m_initialRectWithBounds:
    serializedVersion: 2
    x: -0
    y: -161.28755
    width: 1876.866
    height: 665.46606
  m_initialSpineSkeletonScale: {x: 1, y: 1}
