﻿using System.Collections;
using System.Collections.Generic;

namespace Apq.Extension
{
    /// <summary>
    /// List类基本扩展
    /// </summary>
    public static class Ext_IList
    {
        /// <summary>
        /// 将某项添加到 System.Collections.Generic.IList 中，该项唯一。
        /// </summary>
        /// <returns>新项的插入位置。</returns>
        public static void AddUnique(this IList list, object value)
        {
            if (!list.Contains(value))
            {
                list.Add(value);
            }
        }

        /// <summary>
        /// 按循环索引获取子项
        /// </summary>
        public static T IndexOf_ByCycle<T>(this IList<T> list, int index)
        {
            if (list == null) return default;

            if (list.Count > 0 && index > -1)
            {
                return list[index % list.Count];
            }

            return default;
        }
    }
}
