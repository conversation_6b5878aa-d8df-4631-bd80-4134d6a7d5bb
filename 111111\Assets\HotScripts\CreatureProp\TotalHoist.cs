﻿using System.Collections.Generic;
using System.Linq;

using Apq.NotifyChange;

/// <summary>
/// 总提升
/// </summary>
public class TotalHoist : NotifyPropertyChange
{
    public NotifyChangeProperty<double> 最大生命 { get; }
    public NotifyChangeProperty<float> 最大生命Pct { get; }
    public NotifyChangeProperty<double> 回血 { get; }
    public NotifyChangeProperty<float> 回血Pct { get; }
    public NotifyChangeProperty<double> 攻击 { get; }
    public NotifyChangeProperty<float> 攻击Pct { get; }
    public NotifyChangeProperty<double> 防御 { get; }
    public NotifyChangeProperty<float> 防御Pct { get; }
    public NotifyChangeProperty<double> 半血攻击 { get; }
    public NotifyChangeProperty<float> 半血攻击Pct { get; }
    public NotifyChangeProperty<float> 暴击率 { get; }
    public NotifyChangeProperty<float> 暴击率Pct { get; }
    public NotifyChangeProperty<double> 加伤 { get; }
    public NotifyChangeProperty<float> 加伤Pct { get; }
    public NotifyChangeProperty<double> 减伤 { get; }
    public NotifyChangeProperty<float> 减伤Pct { get; }
    public NotifyChangeProperty<float> 移动速度 { get; }
    public NotifyChangeProperty<float> 移动速度Pct { get; }
    public NotifyChangeProperty<float> 转向速度 { get; }
    public NotifyChangeProperty<float> 转向速度Pct { get; }
    public NotifyChangeProperty<float> 攻击速度 { get; }
    public NotifyChangeProperty<float> 攻击速度Pct { get; }
    public NotifyChangeProperty<float> 子弹速度 { get; }
    public NotifyChangeProperty<float> 子弹速度Pct { get; }
    public NotifyChangeProperty<float> 攻击距离 { get; }
    public NotifyChangeProperty<float> 攻击距离Pct { get; }
    public NotifyChangeProperty<int> 子弹数量 { get; }
    public NotifyChangeProperty<float> 子弹数量Pct { get; }
    public NotifyChangeProperty<double> 暴击伤害 { get; }
    public NotifyChangeProperty<float> 暴击伤害Pct { get; }
    public NotifyChangeProperty<double> 加伤Boss { get; }
    public NotifyChangeProperty<float> 加伤BossPct { get; }
    public NotifyChangeProperty<long> 获取金币 { get; }
    public NotifyChangeProperty<float> 获取金币Pct { get; }
    public NotifyChangeProperty<double> 获取经验 { get; }
    public NotifyChangeProperty<float> 获取经验Pct { get; }
    public NotifyChangeProperty<double> 吸血 { get; }
    public NotifyChangeProperty<float> 吸血Pct { get; }
    public NotifyChangeProperty<double> 反伤 { get; }
    public NotifyChangeProperty<float> 反伤Pct { get; }

    public TotalHoist()
    {
        最大生命 = new(nameof(最大生命), this);
        最大生命Pct = new(nameof(最大生命Pct), this);
        回血 = new(nameof(回血), this);
        回血Pct = new(nameof(回血Pct), this);
        攻击 = new(nameof(攻击), this);
        攻击Pct = new(nameof(攻击Pct), this);
        防御 = new(nameof(防御), this);
        防御Pct = new(nameof(防御Pct), this);
        半血攻击 = new(nameof(半血攻击), this);
        半血攻击Pct = new(nameof(半血攻击Pct), this);
        暴击率 = new(nameof(暴击率), this);
        暴击率Pct = new(nameof(暴击率Pct), this);
        加伤 = new(nameof(加伤), this);
        加伤Pct = new(nameof(加伤Pct), this);
        减伤 = new(nameof(减伤), this);
        减伤Pct = new(nameof(减伤Pct), this);
        移动速度 = new(nameof(移动速度), this);
        移动速度Pct = new(nameof(移动速度Pct), this);
        转向速度 = new(nameof(转向速度), this);
        转向速度Pct = new(nameof(转向速度Pct), this);
        攻击速度 = new(nameof(攻击速度), this);
        攻击速度Pct = new(nameof(攻击速度Pct), this);
        子弹速度 = new(nameof(子弹速度), this);
        子弹速度Pct = new(nameof(子弹速度Pct), this);
        攻击距离 = new(nameof(攻击距离), this);
        攻击距离Pct = new(nameof(攻击距离Pct), this);
        子弹数量 = new(nameof(子弹数量), this);
        子弹数量Pct = new(nameof(子弹数量Pct), this);
        暴击伤害 = new(nameof(暴击伤害), this);
        暴击伤害Pct = new(nameof(暴击伤害Pct), this);
        加伤Boss = new(nameof(加伤Boss), this);
        加伤BossPct = new(nameof(加伤BossPct), this);
        获取金币 = new(nameof(获取金币), this);
        获取金币Pct = new(nameof(获取金币Pct), this);
        获取经验 = new(nameof(获取经验), this);
        获取经验Pct = new(nameof(获取经验Pct), this);
        吸血 = new(nameof(吸血), this);
        吸血Pct = new(nameof(吸血Pct), this);
        反伤 = new(nameof(反伤), this);
        反伤Pct = new(nameof(反伤Pct), this);
    }




    #region ICloneable
    public object Clone()
    {
        var clone = new TotalHoist();
        CopyTo(clone);
        return clone;
    }

    public void CopyTo(TotalHoist other)
    {
        other.最大生命.Value = 最大生命.Value;
        other.最大生命Pct.Value = 最大生命Pct.Value;
        other.回血.Value = 回血.Value;
        other.回血Pct.Value = 回血Pct.Value;
        other.攻击.Value = 攻击.Value;
        other.攻击Pct.Value = 攻击Pct.Value;
        other.防御.Value = 防御.Value;
        other.防御Pct.Value = 防御Pct.Value;
        other.半血攻击.Value = 半血攻击.Value;
        other.半血攻击Pct.Value = 半血攻击Pct.Value;
        other.暴击率.Value = 暴击率.Value;
        other.暴击率Pct.Value = 暴击率Pct.Value;
        other.加伤.Value = 加伤.Value;
        other.加伤Pct.Value = 加伤Pct.Value;
        other.减伤.Value = 减伤.Value;
        other.减伤Pct.Value = 减伤Pct.Value;
        other.移动速度.Value = 移动速度.Value;
        other.移动速度Pct.Value = 移动速度Pct.Value;
        other.转向速度.Value = 转向速度.Value;
        other.转向速度Pct.Value = 转向速度Pct.Value;
        other.攻击速度.Value = 攻击速度.Value;
        other.攻击速度Pct.Value = 攻击速度Pct.Value;
        other.子弹速度.Value = 子弹速度.Value;
        other.子弹速度Pct.Value = 子弹速度Pct.Value;
        other.攻击距离.Value = 攻击距离.Value;
        other.攻击距离Pct.Value = 攻击距离Pct.Value;
        other.子弹数量.Value = 子弹数量.Value;
        other.子弹数量Pct.Value = 子弹数量Pct.Value;
        other.暴击伤害.Value = 暴击伤害.Value;
        other.暴击伤害Pct.Value = 暴击伤害Pct.Value;
        other.加伤Boss.Value = 加伤Boss.Value;
        other.加伤BossPct.Value = 加伤BossPct.Value;
        other.获取金币.Value = 获取金币.Value;
        other.获取金币Pct.Value = 获取金币Pct.Value;
        other.获取经验.Value = 获取经验.Value;
        other.获取经验Pct.Value = 获取经验Pct.Value;
        other.吸血.Value = 吸血.Value;
        other.吸血Pct.Value = 吸血Pct.Value;
        other.反伤.Value = 反伤.Value;
        other.反伤Pct.Value = 反伤Pct.Value;
    }
    #endregion

    /// <summary>
    /// 从提升列表汇总提升
    /// </summary>
    public void ReCalc(IEnumerable<FightHoist> hoists)
    {
        最大生命.Value = hoists.Where(x => x.HoistType == HoistType.最大生命 && !x.IsPct).Sum(x=>(double)x.HoistValue);
        最大生命Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.最大生命 && x.IsPct).Sum(x=>(double)x.HoistValue);
        回血.Value = hoists.Where(x => x.HoistType == HoistType.回血 && !x.IsPct).Sum(x => (double)x.HoistValue);
        回血Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.回血 && x.IsPct).Sum(x => (double)x.HoistValue);
        攻击.Value = hoists.Where(x => x.HoistType == HoistType.攻击 && !x.IsPct).Sum(x => (double)x.HoistValue);
        攻击Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.攻击 && x.IsPct).Sum(x => (double)x.HoistValue);
        防御.Value = hoists.Where(x => x.HoistType == HoistType.防御 && !x.IsPct).Sum(x => (double)x.HoistValue);
        防御Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.防御 && x.IsPct).Sum(x => (double)x.HoistValue);
        半血攻击.Value = hoists.Where(x => x.HoistType == HoistType.半血攻击 && !x.IsPct).Sum(x => (double)x.HoistValue);
        半血攻击Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.半血攻击 && x.IsPct).Sum(x => (double)x.HoistValue);
        暴击率.Value = (float)hoists.Where(x => x.HoistType == HoistType.暴击率 && !x.IsPct).Sum(x => (double)x.HoistValue);
        暴击率Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.暴击率 && x.IsPct).Sum(x => (double)x.HoistValue);
        加伤.Value = hoists.Where(x => x.HoistType == HoistType.加伤 && !x.IsPct).Sum(x => (double)x.HoistValue);
        加伤Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.加伤 && x.IsPct).Sum(x => (double)x.HoistValue);
        减伤.Value = hoists.Where(x => x.HoistType == HoistType.减伤 && !x.IsPct).Sum(x => (double)x.HoistValue);
        减伤Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.减伤 && x.IsPct).Sum(x => (double)x.HoistValue);
        移动速度.Value = hoists.Where(x => x.HoistType == HoistType.移动速度 && !x.IsPct).Sum(x => (float)x.HoistValue);
        移动速度Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.移动速度 && x.IsPct).Sum(x => (double)x.HoistValue);
        转向速度.Value = hoists.Where(x => x.HoistType == HoistType.转向速度 && !x.IsPct).Sum(x => (float)x.HoistValue);
        转向速度Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.转向速度 && x.IsPct).Sum(x => (double)x.HoistValue);
        攻击速度.Value = hoists.Where(x => x.HoistType == HoistType.攻击速度 && !x.IsPct).Sum(x => (float)x.HoistValue);
        攻击速度Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.攻击速度 && x.IsPct).Sum(x => (double)x.HoistValue);
        子弹速度.Value = hoists.Where(x => x.HoistType == HoistType.子弹速度 && !x.IsPct).Sum(x => (float)x.HoistValue);
        子弹速度Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.子弹速度 && x.IsPct).Sum(x => (double)x.HoistValue);
        攻击距离.Value = (float)hoists.Where(x => x.HoistType == HoistType.攻击距离 && !x.IsPct).Sum(x => (double)x.HoistValue);
        攻击距离Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.攻击距离 && x.IsPct).Sum(x => (double)x.HoistValue);
        子弹数量.Value = (int)hoists.Where(x => x.HoistType == HoistType.子弹数量 && !x.IsPct).Sum(x => (long)x.HoistValue);
        子弹数量Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.子弹数量 && x.IsPct).Sum(x => (double)x.HoistValue);
        暴击伤害.Value = hoists.Where(x => x.HoistType == HoistType.暴击伤害 && !x.IsPct).Sum(x => (double)x.HoistValue);
        暴击伤害Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.暴击伤害 && x.IsPct).Sum(x => (double)x.HoistValue);
        加伤Boss.Value = hoists.Where(x => x.HoistType == HoistType.加伤_Boss && !x.IsPct).Sum(x => (double)x.HoistValue);
        加伤BossPct.Value = (float)hoists.Where(x => x.HoistType == HoistType.加伤_Boss && x.IsPct).Sum(x => (double)x.HoistValue);
        获取金币.Value = hoists.Where(x => x.HoistType == HoistType.获取金币 && !x.IsPct).Sum(x => (long)x.HoistValue);
        获取金币Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.获取金币 && x.IsPct).Sum(x => (double)x.HoistValue);
        获取经验.Value = hoists.Where(x => x.HoistType == HoistType.获取经验 && !x.IsPct).Sum(x => (double)x.HoistValue);
        获取经验Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.获取经验 && x.IsPct).Sum(x => (double)x.HoistValue);
        吸血.Value = hoists.Where(x => x.HoistType == HoistType.吸血 && !x.IsPct).Sum(x => (double)x.HoistValue);
        吸血Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.吸血 && x.IsPct).Sum(x => (double)x.HoistValue);
        反伤.Value = hoists.Where(x => x.HoistType == HoistType.反伤 && !x.IsPct).Sum(x => (double)x.HoistValue);
        反伤Pct.Value = (float)hoists.Where(x => x.HoistType == HoistType.反伤 && x.IsPct).Sum(x => (double)x.HoistValue);
    }
}
