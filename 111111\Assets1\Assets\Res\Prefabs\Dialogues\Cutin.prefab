%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3112520721020531869
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5164837570317447847}
  - component: {fileID: 8782517504076696286}
  - component: {fileID: 5630731766585584156}
  - component: {fileID: 4367835203180678636}
  m_Layer: 0
  m_Name: SkeletonGraphic (ship2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5164837570317447847
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3112520721020531869}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 855763014617313803}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -698, y: 406}
  m_SizeDelta: {x: 988.6185, y: 955.1292}
  m_Pivot: {x: -0.7210902, y: 0.6043161}
--- !u!222 &8782517504076696286
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3112520721020531869}
  m_CullTransparentMesh: 1
--- !u!114 &5630731766585584156
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3112520721020531869}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 5ab41df1a624146d1add17f8f165a49a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: 739bb6ae0c211426ba0fb921fa36ad41, type: 2}
  initialSkinName: default
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: talk
  startingLoop: 1
  timeScale: 1
  freeze: 0
  unscaledTime: 1
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      pmaVertexColors: 1
      tintBlack: 0
      calculateTangents: 0
      addNormals: 0
      immutableTriangles: 0
--- !u!114 &4367835203180678636
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3112520721020531869}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 11cd9de1340b642ed96ebef70d3158c8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonGraphic: {fileID: 5630731766585584156}
  m_preserveAspectRatio: 1
  m_SetToSetupPoseWhenScaling: 0
  m_isEditorUpdateStopped: 0
  m_initialRectWithBounds:
    serializedVersion: 2
    x: 712.8831
    y: -577.19995
    width: 988.6185
    height: 955.1292
  m_initialSpineSkeletonScale: {x: 1, y: 1}
--- !u!1 &5740163534979085002
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 855763014617313803}
  m_Layer: 0
  m_Name: Cutin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &855763014617313803
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5740163534979085002}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5164837570317447847}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -437, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
