﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
public class Laser : MonoBehaviour
{
    const string BONE_LASER = "laser";
    const string BONE_IMPACT = "laserImpact";
    const float OUTPUT_INTERVAL = 1;
    const string SOUND_LOOP = "laserLooped";

    [SerializeField] private BoundingBoxFollower boundingBoxfollower;
    [SerializeField] private SkeletonAnimation laser;
    private PlayerController player;
    private Bounds bounds;
    private double _damage = 1;
    private bool _allowSound = false;
    private bool isActive = true;
    private bool _allowShrink = false;
    private int sound_id = 0;
    private Vector2 _scaleSizes;
    private int _shrinkCount = 0;
    private bool scheduleUpdate = false;
    [HideInInspector] public bool isInUse = false;
    public double setDamage { set { _damage = value; } }
    public bool setAllowSound { set { _allowSound = value; } }
    private Bounds initBounds;
    bool once;
    private void Start()
    {
        //Init();
    }

    public void Init()
    {
        if (laser.state == null) print("NULL");
        laser.state.SetAnimation(0, "idle", true);
        player = GameManager.instance.player;
        scheduleUpdate = true;
        //this->schedule(schedule_selector(Laser::update), 0.017 * OUTPUT_INTERVAL);TODO
    }

    private void Update()
    {
        if (scheduleUpdate && Time.deltaTime > 0)
        {
            if (boundingBoxfollower.CurrentCollider.OverlapPoint(player.skeletonAnimationTran.position))
            {
                if (Globals.allowPlayerHit && player.canHit)
                {
                    player.GotHit(_damage * Time.deltaTime, false);
                }
                else
                {
                    //                GETPLAYERCONTROLLER->gotHit(_damage/3,false);
                }
                if (_allowShrink)
                {
                    _shrinkCount = 0;
                    float distance = Vector2.Distance(player.skeletonAnimationTran.position, laser.transform.position);
                    GetLaserBone().ScaleX = 1.5f * distance - 0.8f;
                }
            }
            else if (_allowShrink)
            {
                //            _shrinkCount++;
                //            if(_shrinkCount > 5){
                GetLaserBone().ScaleX = _scaleSizes.x;
                //            }
            }
        }

    }

    public void SetIsActive(bool val)
    {
        if (isActive == val)
        {
            return;
        }
        isActive = val;
        if (isActive && _allowSound)
        {
            gameObject.SetActive(isActive);
            scheduleUpdate = true;
            //this->schedule(schedule_selector(Laser::update), 0.017 * OUTPUT_INTERVAL);TODO
            AudioManager.instance.PlaySound(AudioType.Loop, Constants_Audio.Audio.laserLooped, 1.0f);
            //sound_id = Shared::playSound(SOUND_LOOP, true, 1.0f);TODO
        }
        if (!isActive && _allowSound)
        {
            scheduleUpdate = false;
            gameObject.SetActive(false);
            AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.laserLooped);
            //Shared::stopSound(sound_id);TODO
        }
    }

    public void SetLaserScale(float x, float y)
    {
        GetLaserBone().ScaleX = x;
        GetLaserBone().ScaleY = y;

        _scaleSizes = new Vector2(x, y);
        {
            GetImpactBone().ScaleX = y;  // this is y on purpose
            GetImpactBone().ScaleY = y;
            laser.skeleton.FindBone("laserImpact2").ScaleX = y;
            laser.skeleton.FindBone("laserImpact2").ScaleY = y;
        }
    }

    public void SetLaserRotation(float rotation)
    {
        GetLaserBone().Rotation = rotation;
        GetImpactBone().Rotation = rotation;
    }

    public Vector2 GetLaserScale()
    {
        return new Vector2(GetLaserBone().ScaleX, GetLaserBone().ScaleY);
    }

    public SkeletonAnimation GetLaser()
    {
        return laser;
    }

    public void SetAllowShrink(bool shrink)
    {
        if (shrink)
        {
            //GetLaser().setGlobalZOrder(30);

        }
        _allowShrink = shrink;
    }

    public Bone GetLaserBone()
    {
        return laser.skeleton.FindBone(BONE_LASER);
    }

    public Bone GetImpactBone()
    {
        return laser.skeleton.FindBone(BONE_IMPACT);
    }

    //public void SetDamage(float dmg)
    //{
    //    _damage = dmg * OUTPUT_INTERVAL;
    //}

}
