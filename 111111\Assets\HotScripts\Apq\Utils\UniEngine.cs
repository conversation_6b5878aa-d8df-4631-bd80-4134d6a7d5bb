﻿using UnityEngine;

using X.PB;

public static class UniEngine
{
    #region Destroy
    /// <summary>
    /// 比较安全的Destroy
    /// </summary>
    public static void Destroy(Object obj)
    {
        if (obj)
        {
            Object.Destroy(obj);
        }
    }

    /// <summary>
    /// 比较安全的Destroy
    /// </summary>
    public static void Destroy(Object obj, float t)
    {
        if (obj)
        {
            Object.Destroy(obj, t);
        }
    }
    #endregion


    #region 控制台日志
    /// <summary>
    /// 控制台日志(只显示比配置值大的日志)
    /// </summary>
    /// <remarks>能调用此方法则不要直接使用Debug下的方法(如Log方法)</remarks>
    public static void Log(LogLevel level, string msg)
    {
        var nLevel = (int)level;

        if (nLevel >= (int)LogLevel.Error)
        {
            Debug.LogError($"{msg}");
        }
        else if (nLevel < (int)LogLevel.Notice)
        {
            Debug.Log($"{msg}");
        }
        else
        {
            Debug.LogWarning($"{msg}");
        }
    }
    #endregion

}
