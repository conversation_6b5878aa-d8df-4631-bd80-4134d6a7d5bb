using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;

public class ConstValueScheme : Singleton<ConstValueScheme>
{
    private ConstValue _data;
    private Dictionary<int, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load();
        }
    }
    public bool Load()
    {
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.ConstValue;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            MemoryStream ms = new MemoryStream(HotResManager.ReadPb(pbFileName));
            _data = Serializer.Deserialize<ConstValue>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }    
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].Id] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;
        
    }
    public ConstValue.Item GetItem(int id)
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load();
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            throw new Exception("id dont exist");
        }
            
    }
}

