﻿using System.Collections.Generic;
using System.Text;

namespace Apq
{
    public class Cast
    {
        #region BytesToHexString
        /// <summary>
        /// 将字节串转换为16进制字符串
        /// </summary>
        public static string BytesToHexString(IEnumerable<byte> input)
        {
            var sb = new StringBuilder();
            if (input == null) return sb.ToString();
            var tor = input.GetEnumerator();
            while (tor.MoveNext())
            {
                sb.Append(tor.Current.ToString("X2"));
            }
            return sb.ToString();
        }
        #endregion
    }
}
