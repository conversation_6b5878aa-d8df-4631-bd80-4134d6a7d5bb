using UnityEngine;
using Spine.Unity;
using Spine;
using System.Collections;
using DG.Tweening;
using System;

public class BarkMeow : Enemy
{
    const float THRUST_D = 0.35f;
    const float SPEEDLIMIT_D = 6.0f;
    const float ENEMYSCALE = 1.2f;
    const float GRAVITY_D = 0.1f;
    const float DRAG_D = 0.99f;
    const float TURN_SPEED_D = 130;
    const float TURN_SPEED_WHILE_BOOST_D = 130;

    [SerializeField] private Laser laser;
    [SerializeField] private Transform laserPosLeft;
    [SerializeField] private Transform laserPosRight;
    [SerializeField] private GameObject boost;
    [SerializeField] private GameObject flame;
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] private Sprite missileSprite;
    [SerializeField] private ParticleSystem trail;

    private Vector2 enemyAcceleration;
    private bool BoostOn;
    private bool allowShooting = true;
    private float targetAngle;
    private Bone gun;
    private Bone gun1;
    private Bone gun2;
    private bool chargingLaser;
    private bool isLaserActivated;
    private int laserLoop;
    private int crazyRocketState = 0;
    private bool isDead = false;
    int dist = 300;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        schedulerId = "BM" + GetInstanceID();
        tweenId = "BMS" + GetInstanceID();
        enemyCollisionRadius = 1.5f;
        enemyAcceleration = Vector2.zero;
        BoostOn = false;
        allowShooting = true;
        allowRelocate = false;
        explosionType = Explosions.ExplosionType.ExplosionTypeAir2;
        _jsonScale = 0.22f;

        //    enemySprite.setPosition(Player::getInstance().getPosition());
        enemySprite.state.SetAnimation(0, "shoot", true);

        //boost.transform.SetScale(0.75f / ENEMYSCALE);
        //boost.transform.SetLocalPosition(Globals.CocosToUnity(-80) * Mathf.Sin(Mathf.Deg2Rad * (transform.eulerAngles.z - 90)), Globals.CocosToUnity(-20 + -80) * Mathf.Cos(Mathf.Deg2Rad * (transform.eulerAngles.z - 90)));


        InitLaser();
        enemySprite.state.Event += HandleSpineEvent;


        scheduleUpdate = true;
        //StartCoroutine(ChangeAnimation(0));
        //    this.schedule(schedule_selector(DeathBot::update),0,CC_REPEAT_FOREVER, );
        float dir = Vector2.SignedAngle(Vector2.right, player.skeletonAnimationTran.position - transform.position);
        float rotationDir = dir < 0 ? 360 + dir : dir;
        var rot = Quaternion.AngleAxis(rotationDir, Vector3.forward);
        enemySprite.transform.SetRotation(rot.eulerAngles.z + 180);
        targetAngle = enemySprite.transform.eulerAngles.z;
        Globals.bossPosition = new Vector2(transform.position.x, transform.position.y - Globals.CocosToUnity(100));
        gun1 = enemySprite.skeleton.FindBone("gun1");
        gun2 = enemySprite.skeleton.FindBone("gun2");
        //laser.gameObject.SetActive(false);
       DOTween.Sequence().SetId(schedulerId).AppendCallback(ChangeAnimation).AppendInterval(8).SetLoops(-1).Play();
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            Boss();
        }
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.barkMeowIntro);
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (isDead)
            {
                if (transform.position.y < Globals.LOWERBOUNDARY + 0.1f)
                {
                    GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBuildingMission, transform.position, false, 1, 0.5f, 0);

                    enemySprite.gameObject.SetActive(false);
                    scheduleUpdate = false;
                }

                return;
            }
            if (isLaserActivated)
            {
                LaserLogic();
            }

            if (isLaserActivated)
            {
                Mathf.Lerp(dist, 1900, Time.deltaTime * 10);
                MovementLogic(dist);
            }
            else
            {
                Mathf.Lerp(dist, 1900, Time.deltaTime * 10);
                MovementLogic(dist);
            }


            gun = enemySprite.skeleton.FindBone("root");

            RotationLogic();
            BoostLogic();
            if (isBoss)
            {
                Globals.bossPosition = new Vector2(transform.position.x, transform.position.y - Globals.CocosToUnity(30));
            }

            if (enemySprite.transform.eulerAngles.z < 90 || enemySprite.transform.eulerAngles.z > 90 + 180)
            {
                //        enemySprite.setScaleY(ENEMYSCALE);
                gun.ScaleY = 1.0f;

            }
            else
            {
                //        enemySprite.setScaleY(-ENEMYSCALE);
                gun.ScaleY = -1.0f;

            }
            CheckUpdate();

            //if (transform.position.x > Globals.RIGHTBOUNDARY)
            //{
            //    transform.SetWorldPositionX(Globals.RIGHTBOUNDARY);
            //}
            //if (transform.position.y > Globals.UPPERBOUNDARY)
            //{
            //    transform.SetWorldPositionY(Globals.UPPERBOUNDARY);
            //}
            //if (transform.position.x < Globals.LEFTBOUNDARY)
            //{
            //    transform.SetWorldPositionX(Globals.LEFTBOUNDARY);
            //}

            //if (transform.position.y < Globals.LOWERBOUNDARY)
            //{
            //    transform.SetWorldPositionY(Globals.LOWERBOUNDARY);
            //}
        }
    }

    private void MovementLogic(int distance)
    {

        if (transform.position.x < player.skeletonAnimationTran.position.x - Globals.CocosToUnity(distance))
        {
            //transform.SetWorldPositionX(player.transform.position.x - Globals.CocosToUnity(distance));
            trail.transform.SetLocalPosition(Globals.CocosToUnity(90) + UnityEngine.Random.value * Globals.CocosToUnity(45), Globals.CocosToUnity(-10) + UnityEngine.Random.value * Globals.CocosToUnity(20));
            if (!trail.isPlaying)
            {
                trail.Play();
            }
        }
        else if (transform.position.x > player.skeletonAnimationTran.position.x + Globals.CocosToUnity(distance))
        {
            //transform.SetWorldPositionX(player.transform.position.x + Globals.CocosToUnity(distance));
            trail.transform.SetLocalPosition(Globals.CocosToUnity(90) + UnityEngine.Random.value * Globals.CocosToUnity(45), Globals.CocosToUnity(-10) + UnityEngine.Random.value * Globals.CocosToUnity(20));
            if (!trail.isPlaying)
            {
                trail.Play();
            }
        }
        else
        {
            trail.Stop();
        }
    }

    private void LaserLogic()
    {
        if (gun.ScaleY < 0)
        {
            laser.transform.position = laserPosLeft.position;
        }
        else
        {
            laser.transform.position = laserPosRight.position;
        }

        //laser.SetLaserRotation(enemySprite.transform.eulerAngles.z + 180);
    }

    private void BoostLogic()
    {
        if (Vector2.SqrMagnitude(player.skeletonAnimationTran.position - transform.position) > Globals.CocosToUnity(1444))
        {

            if (!BoostOn)
            {
                //            enemySprite.setAnimation(0, "boost", true);

            }
            BoostOn = true;
            boost.SetActive(true);
        }
        else
        {

            if (BoostOn)
            {
                //            enemySprite.setAnimation(0, "idle", true);

            }
            BoostOn = false;
            boost.SetActive(false);

        }
        //if (!isLaserActivated && !chargingLaser)
        //{
        //    transform.SetWorldPosition(transform.position.x + enemyAcceleration.x * Time.deltaTime * Globals.CocosToUnity(60),
        //        transform.position.y + enemyAcceleration.y * Time.deltaTime * Globals.CocosToUnity(60));

        //}
        if (BoostOn)
        {
            if (Vector2.SqrMagnitude(player.skeletonAnimationTran.position - transform.position) > 3.5f)
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) *   THRUST_D / 4;
                enemyAcceleration.x = Mathf.Clamp(enemyAcceleration.x, -SPEEDLIMIT_D - 8, SPEEDLIMIT_D + 8);

                enemyAcceleration.y = enemyAcceleration.y + (Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) * THRUST_D / 4)*-1;
                enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -SPEEDLIMIT_D - 8, SPEEDLIMIT_D + 8);

            }
            else
            {
                enemyAcceleration.x = enemyAcceleration.x + Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) * THRUST_D;
                if (enemyAcceleration.x > SPEEDLIMIT_D)
                {
                    enemyAcceleration.x -= 0.4f;

                }

                if (enemyAcceleration.x < -SPEEDLIMIT_D)
                {
                    enemyAcceleration.x += 0.4f;

                }

                enemyAcceleration.y = enemyAcceleration.y + (Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) * THRUST_D)*-1;
                if (enemyAcceleration.y > SPEEDLIMIT_D)
                {
                    enemyAcceleration.y -= 0.4f;

                }

                if (enemyAcceleration.y < -SPEEDLIMIT_D)
                {
                    enemyAcceleration.y += 0.4f;

                }

            }

        }
        else
        {
            enemyAcceleration = new Vector2(enemyAcceleration.x, enemyAcceleration.y - GRAVITY_D);


            enemyAcceleration.y = Mathf.Clamp(enemyAcceleration.y, -SPEEDLIMIT_D * 1.5f, SPEEDLIMIT_D * 1.5f);


            enemyAcceleration.x = enemyAcceleration.x * DRAG_D;

        }

        //if (Player::getStats().mode == Player::PLAYER_MODE_DEAD)TODO
        //{
        //    allowShooting = false;
        //}



        if (transform.position.y < -1)
        {
            transform.SetWorldPositionY(-1);
        }
    }

    private void RotationLogic()
    {
        
        float dir = Vector2.SignedAngle(Vector2.right, player.skeletonAnimationTran.position - transform.position);
        float rotationDir = dir < 0 ? 360 + dir : dir;
        float rotationSpeed;
        if (BoostOn == false)
        {
            //enemySprite.setRotation(enemySprite.getRotation() + (-enemySprite.getRotation()+ playerRotation)* dt *1);
            rotationSpeed = TURN_SPEED_D;


            if (Globals.controls == 1)
            {
                rotationSpeed = TURN_SPEED_D;
            }
            else if (Globals.controls == 2)
            {
                rotationSpeed = 130;
                if (isLaserActivated)
                {
                    rotationSpeed = rotationSpeed * 0.35f;
                }
                if (chargingLaser)
                {
                    rotationSpeed = 5;
                }
            }
        }
        else
        {
            //            enemySprite.setRotation(enemySprite.getRotation() + (-enemySprite.getRotation()+ playerRotation)* dt *2);

            rotationSpeed = TURN_SPEED_WHILE_BOOST_D;



            if (Globals.controls == 1)
            {
                rotationSpeed = TURN_SPEED_WHILE_BOOST_D;
            }
            else if (Globals.controls == 2)
            {
                rotationSpeed = TURN_SPEED_WHILE_BOOST_D;
                if (isLaserActivated)
                {
                    rotationSpeed = rotationSpeed * 0.5f;
                }
                if (chargingLaser)
                {
                    rotationSpeed = 5;
                }
            }

        }
        targetAngle = Globals.MoveAngleTowards(targetAngle, rotationDir, rotationSpeed * Time.deltaTime);
        var rot = Quaternion.AngleAxis(targetAngle, Vector3.forward);
        enemySprite.transform.SetRotation(rot.eulerAngles.z+180);
        //enemySprite.transform.SetRotation(enemySprite.transform.GetRotation() + (-enemySprite.transform.GetRotation()+targetAngle)* dt *5);
        if (!isLaserActivated && chargingLaser == false)
        {
            if (enemySprite.transform.GetRotation() - rotationDir > -180-30 && enemySprite.transform.GetRotation() - rotationDir < 180+30)
            {
                if (!allowShooting)
                {
                    enemySprite.state.SetAnimation(0, "shoot", true);
                }
                allowShooting = true;
            }
            else
            {

                if (allowShooting)
                {
                    enemySprite.state.SetAnimation(0, "idle", true);
                }
                allowShooting = false;
            }
        }
    }

    private void InitLaser()
    {
        laser.Init();
        laser.setDamage = 2.0f;
        laser.SetLaserScale(55, 2.5f);
        laser.setAllowSound = true;
        laser.SetAllowShrink(true);
        laser.SetIsActive(false);
    }

    private void ChangeAnimation()
    {
        if (isDead)
        {
            return;
        }
        if (!gameObject.activeSelf)
        {
            return;
        }
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_DEATHDROP || player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE || player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_DEAD || Vector2.Distance(player.skeletonAnimationTran.position, transform.position) > Globals.CocosToUnity(1200))
        {
            isLaserActivated = true;
        }

        if (!isLaserActivated)
        {
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.barkMeowLaserOn);
            //Globals.PlaySound("res/Sounds/Bosses/Boss4/Bullet/barkMeowLaserOn.mp3");
            enemySprite.state.SetAnimation(0, "laserShoot", false);
            enemySprite.state.AddAnimation(0, "LaserOn", true, 0);
            chargingLaser = true;
            enemyAcceleration = Vector2.zero;
        }
        else
        {
            isLaserActivated = false;
            laser.SetIsActive(false);
            if (crazyRocketState == 0 && stats.health / stats.maxHealth.Value < 0.75)
            {
                crazyRocketState = 1;
                scheduleUpdate = false;
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.barkMeowIntro);
                //Globals.PlaySound("res/Sounds/Bosses/Boss4/barkMeowIntro.mp3");
                Observer.DispatchCustomEvent("ChangeBossState");
                for (int i = 0; i < 30; i++)
                {
                    StartCoroutine(ShootCrazyRockets(1 + 0.1f * i));
                }
                DOTween.Sequence().SetId(schedulerId).AppendInterval(3.5f).AppendCallback(ReturnToNormalStates).Play();
                return;
            }

            if (crazyRocketState == 1 && stats.health / stats.maxHealth.Value < 0.25)
            {
                crazyRocketState = 2;
                scheduleUpdate = false;
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.barkMeowIntro);
                //Globals.PlaySound("res/Sounds/Bosses/Boss4/barkMeowIntro.mp3");
                Observer.DispatchCustomEvent("ChangeBossState");
                for (int i = 0; i < 30; i++)
                {
                    StartCoroutine(ShootCrazyRockets(1 + 0.1f * i));
                }
                DOTween.Sequence().SetId(schedulerId).AppendInterval(3.5f).AppendCallback(ReturnToNormalStates).Play();
                return;
            }
            //experimental::AudioEngine::stop(laserLoop); TODO
            AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.barkMeowLaserLoop);
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.backMeowLaserOff);

            //Globals.PlaySound("res/Sounds/Bosses/Boss4/Bullet/barkMeowLaserOff.mp3");
            enemySprite.state.SetAnimation(0, "idle", false);
            enemySprite.state.AddAnimation(0, "shoot", true);

        }
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        //if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)
        //{
        if (spineEvent.Data.Name == "gunshoot1")
        {
            Shoot(true);
        }
        if (spineEvent.Data.Name == "gunshoot2")
        {
            Shoot(false);
        }
        //if (spineEvent.Data.Name == "rocketshoot")
        //{
        //    ShootMissile();
        //}

        if (spineEvent.Data.Name == "laserShoot")
        {
            isLaserActivated = true;
            laser.SetIsActive(true);


            if (Globals.AllowMusic)
            {
                AudioManager.instance.PlaySound(AudioType.Loop, Constants_Audio.Audio.barkMeowLaserLoop);
                //laserLoop = experimental::AudioEngine::play2d("res/Sounds/Bosses/Boss4/Bullet/barkMeowLaserLoop.mp3", true, 1.0f);
            }
            //                laser.setScaleY(0);
            //                laser.runAction(ScaleTo::create(0.2, 1,1));
            //                laser.setVisible(true);
            //                laserImpact.setVisible(true);
            //                laserImpactStart.setVisible(true);

            chargingLaser = false;
        }
        //}
    }

    private void Shoot(bool shootLeft)
    {
        if (allowShooting == false)
        {
            return;
        }
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spikyShoot);
        //Globals.PlaySound("res/Sounds/Bosses/Boss1/spikyShoot.mp3", false, 1.0f);
        bullet.hasHit = false;
        bullet.setDamage(stats.bulletDamage);
        bullet.SetSpriteFrame(bulletSprite);

        bullet.transform.SetScale(1);
        float angleOffset = 92f;
        if (shootLeft)
        {

            //        gun = spSkeleton_findBone(enemySprite.getSkeleton(), "gun1");
            //bullet.transform.SetWorldPosition(enemySprite.getPosition().x + (78 * sinf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation() + 180 + 20 * gun.ScaleY))), enemySprite.getPosition().y + (gun.ScaleY * 78 * cosf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation() + 180 + 20 * gun.ScaleY))));
            bullet.transform.position = gun1.GetWorldPosition(enemySprite.transform);

        }
        else
        {
            //        gun = spSkeleton_findBone(enemySprite.getSkeleton(), "gun2");
            //bullet.setPosition(enemySprite.getPosition().x + (gun.ScaleY * 116 * sinf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation() + 180 + 60 * gun.ScaleY))), enemySprite.getPosition().y + (gun.ScaleY * 116 * cosf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation() + 180 + 60 * gun.ScaleY))));
            bullet.transform.position = gun2.GetWorldPosition(enemySprite.transform);
        }


        //     bullet.setPosition(enemySprite.getPosition().x+ enemySprite.getScaleX()*gun.WorldX*sinf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation())), enemySprite.getPosition().y+ enemySprite.getScaleY()*gun.WorldY*cosf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation())));

        //    bullet.setPosition(enemySprite.getPosition().x + 100*sinf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation()-90)) , enemySprite.getPosition().y + 100*cosf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation()-90)));
        //    bullet.runAction(Sequence::create(MoveBy::create(stats.bulletSpeed,cocos2d::Point( 5000*sinf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation()-90)),5000*cosf(CC_DEGREES_TO_RADIANS(enemySprite.getRotation()-90)))),CallFuncN::create(CC_CALLBACK_1(Seversky::removeNode, this)), NULL));
        bullet.transform.SetRotation(enemySprite.transform.GetRotation() + angleOffset);

        Vector2 dest = new Vector2(Globals.CocosToUnity(2500) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation())*-1f, Globals.CocosToUnity(2500) * Mathf.Cos(Mathf.Deg2Rad * (bullet.transform.GetRotation())));

        bullet.gameObject.SetActive(true);
        bullet.duration = 2;
        bullet.PlayBulletAnim(2, dest, false);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(50f);
    }

    private void ShootMissile()
    {
        if (!allowShooting||isDead)
            return;
        HomingMissile missile = null;
        bool didFindMissile = false;
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            return;
        }
        missile.Init();
        missile.missileSprite.sprite = missileSprite;
        missile.SetDamage(stats.bulletDamage * 2.5f);
        missile.SetSpeed(18);
        missile.homingMultiplier = 3.0f;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.deatomizerSound,0.85f);
        //Globals.PlaySound("res/Sounds/SFX/deatomizerSound.mp3", false, 0.85f);

        if (gun.ScaleY < 0)
        {
            missile.transform.SetRotation(enemySprite.transform.eulerAngles.z - 90);

        }
        else
        {
            missile.transform.SetRotation(enemySprite.transform.eulerAngles.z + 90);

        }
        missile.transform.position = transform.position;
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.radiusSQ = Globals.CocosToUnity(90);
        missile.EnableTrail();
        missile.RemoveAfterDuration();
    }
    
    private IEnumerator ShootCrazyRockets(float interval)
    {
        if (isDead)
            yield break;
        yield return new WaitForSeconds(interval);
        HomingMissile missile = null;
        bool didFindMissile = false;
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            yield break;
        }
        missile.Init();
        missile.missileSprite.sprite = missileSprite; ;
        missile.SetDamage(stats.bulletDamage * 3f);
        missile.SetSpeed(12);
        missile.homingMultiplier = 3.0f;
        missile.duration = 15;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.deatomizerSound , 0.85f);

        //Globals.PlaySound("res/Sounds/SFX/deatomizerSound.mp3", false, 0.85f);
        missile.transform.SetRotation(UnityEngine.Random.value * 360);
        missile.transform.position = transform.position;
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.radiusSQ = Globals.CocosToUnity(90);
        missile.EnableTrail();
        missile.RemoveAfterDuration();
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        isBoss = true;
        int bossNumber = 4;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }

        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }
        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"]as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"]as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"]as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"]as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"]as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"]as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"]as PList)["value"]); ;
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"]as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }

    private void Boss()
    {
        isBoss = true;
        int bossNumber = 4;

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"]as PList;

        stats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = Convert.ToInt32((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }


    private void ReturnToNormalStates()
    {
        scheduleUpdate = true;
    }

    public void Create()
    {

    }

    public void CreateAsBoss()
    {

    }

    public override bool CheckCollision(Vector2 P1)
    {
        return base.CheckCollision(P1);
    }
    
    public override void Destroy()
    {
        healthBar.gameObject.SetActive(false);
        laser.SetIsActive(false);
        boost.SetActive(false);
        AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.barkMeowLaserLoop);
        //experimental::AudioEngine::stop(laserLoop); TODO

        //if (this.getReferenceCount() != 2)
        //{
        //    CCASSERT(0, "ref count must = to 2");
        //}
        //{
        //}
        scheduleUpdate = true;
        isDead = true;
        StopAllCoroutines();
        //    this.unscheduleAllCallbacks();
        //enemySprite.stopAllActions();
        //enemySprite.setColor(Color3B::WHITE);
        enemySprite.transform.SetRotation(0);

        flame.SetActive(true);

        //if (player.transform.position.x < transform.position.x)
        //{
        //    enemySprite.skeleton.FindBone("root").ScaleX = 1;
        //    transform.DOBlendableMoveBy(new Vector2(-8, -13), 15);
        //    flame.transform.SetRotation(20);
        //    enemySprite.transform.SetRotation(-10);

        //    for (int i = 0; i < 20; i++)
        //    {
        //        StartCoroutine(ParticlesCoroutine(i * 0.35f));
        //    }
        //}


        //else {

        //    enemySprite.skeleton.FindBone("root").ScaleX = -1;
        //    transform.DOBlendableMoveBy(new Vector2(8, -13), 15);
        //    flame.transform.SetRotation(-20);
        //    enemySprite.transform.SetRotation(10);
        //    enemySprite.skeleton.FindBone("root").ScaleY = 1;
        //    for (int i = 0; i < 20; i++)
        //    {
        //        StartCoroutine(ParticlesCoroutine(i * 0.35f));
        //    }

        //}
        //enemySprite.state.SetAnimation(0, "deathStart2", true);
        //StartCoroutine(DeathExplosion(0.1f));
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }

    private IEnumerator ParticlesCoroutine(float interval)
    {
        yield return new WaitForSeconds(interval);
        if (player.skeletonAnimationTran.position.x < transform.position.x)
        {
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x - 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
            //if (rand_0_1() < 0.05f)TODO
            //{
            //    explosion.setLocalZOrder(2);
            //}
        }
        else
        {
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
            //if (rand_0_1() < 0.05f)TODO
            //{
            //    explosion.setLocalZOrder(2);
            //}
        }
    }

    private IEnumerator DeathExplosion(float interval)
    {
        yield return new WaitForEndOfFrame();
        GameSharedData.Instance.enemyList.Remove(this);
        yield return new WaitForSeconds(interval);
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
    }
}
