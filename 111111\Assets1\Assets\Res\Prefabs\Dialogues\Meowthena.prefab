%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3667261721682953298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7802478659976016732}
  - component: {fileID: 7513380531146839239}
  - component: {fileID: 5083333163082321482}
  - component: {fileID: 822460706291182561}
  m_Layer: 0
  m_Name: SkeletonGraphic (MeowthenaSkeleton)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7802478659976016732
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3667261721682953298}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 806015114424499368}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -1052.0983, y: 139}
  m_SizeDelta: {x: 494.1632, y: 527.5056}
  m_Pivot: {x: -2.3228176, y: 0.30427465}
--- !u!222 &7513380531146839239
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3667261721682953298}
  m_CullTransparentMesh: 1
--- !u!114 &5083333163082321482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3667261721682953298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: b66cf7a186d13054989b33a5c90044e4, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: 0b115af32e8794046b6c133419f1472e, type: 2}
  initialSkinName: default
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: talk
  startingLoop: 1
  timeScale: 1
  freeze: 0
  unscaledTime: 1
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      pmaVertexColors: 1
      tintBlack: 0
      calculateTangents: 0
      addNormals: 0
      immutableTriangles: 0
--- !u!114 &822460706291182561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3667261721682953298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 11cd9de1340b642ed96ebef70d3158c8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonGraphic: {fileID: 5083333163082321482}
  m_preserveAspectRatio: 1
  m_SetToSetupPoseWhenScaling: 0
  m_isEditorUpdateStopped: 0
  m_initialRectWithBounds:
    serializedVersion: 2
    x: 1147.851
    y: -160.50659
    width: 494.1632
    height: 527.5056
  m_initialSpineSkeletonScale: {x: 1, y: 1}
--- !u!1 &7288717114213830893
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 806015114424499368}
  m_Layer: 0
  m_Name: Meowthena
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &806015114424499368
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288717114213830893}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7802478659976016732}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -519, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
