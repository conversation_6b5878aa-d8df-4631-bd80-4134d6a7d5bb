using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
public enum Track
{
    arenaMusic,
    BarkMeow,
    berserk_mode_1,
    berserk_mode_2,
    bossLoose,
    creditLoop,
    Cutin,
    endMusic,
    gamePlayMusic,
    mainMenuMusic,
    menuBg,
    menuIntro,
    shopMusic,
    SugarPuff,
    trainingMusic
}

public enum SoundEffectType
{
    PlayOnce,
    Loop,
    StartLoopEnd,
    Random,
    PitchShift,
}

public enum AudioType
{
    Player,
    Enemy,
    Explosion,
    Menu,
    Loop,
    ThreeD
}

public class AudioManager : MonoBehaviour
{

    const float PITCH_SHIFT_THRESHOLD = 1.5f;
    const float PITCH_SHIFT = 0.05f;
    public static AudioManager instance;
    [SerializeField] AudioSource musicSource;
    [SerializeField] AudioSource oneShotMusicSource;
    [SerializeField] AudioSource playerSource;
    [SerializeField] AudioSource normalAudioSource;
    [SerializeField] List<AudioSource> loopAudioSource = new List<AudioSource>();
    [SerializeField] AudioSource muteAudioSource;
    [SerializeField] AudioSource explosionsAudioSource;
    [SerializeField] AudioSource enemiesAudioSource;
    [SerializeField] AudioSource audioSource3D;
    [SerializeField,Tooltip("Must have the same order as Track enum in AudioManager")] AudioClip[] tracks;

	Track? lastPlayedTrack = null;
    float lastTrackPlaybackTime;
    float lastTrackVol;
    [SerializeField] SoundEffect[] soundEffects;
    [SerializeField] SoundEffect3D[] soundEffects3D;
    void Awake()
    {
        ManageSingletonInstance();
    }

    private void ManageSingletonInstance()
    {
        if (instance == null)
        {
            instance = this;
            foreach (SoundEffect s in soundEffects)
            {
                //s.source = normalAudioSource;
                //s.source = gameObject.AddComponent<AudioSource>();
                //s.source.clip = s.clips[0];
                //s.source.volume = s.volume;
                //s.source.pitch = s.pitch;
                //if (s.type == SoundEffectType.Loop)
                //    s.source.loop = true;
                //else
                //    s.source.loop = false;
                //s.source.playOnAwake = false;
            }
        }
        else
        {
            Destroy(gameObject);
        }

        DontDestroyOnLoad(gameObject);
    }
    
    void Start()
    {
        CheckSoundAndMusic();

        SceneManager.activeSceneChanged += ResetMusicVolume;
        
    }
    /// <summary>
    /// 检查声音和音效的开关
    /// </summary>
    
    public void CheckSoundAndMusic()
    {
        if (PlayerPrefs.HasKey("xmMusic"))
        {
            if (PlayerPrefs.GetInt("xmMusic") == 0)
            {
                SetMusicActive(true);
            }
            else
            {
                SetMusicActive(false);
            }
        }

        if (PlayerPrefs.HasKey("xmSound"))
        {
            if (PlayerPrefs.GetInt("xmSound") == 0)
            {
                SetSoundEffectsActive(true);
            }
            else
            {
                SetSoundEffectsActive(false);
            }
        }
    }

    private void Update()
    {
        //SetSoundEffectsActive(GameStateManager.Instance.GetSoundStatus());
        //SetMusicActive(GameStateManager.Instance.GetSoundStatus());
        //if (Input.GetKeyDown(KeyCode.S))
        //    StopSoundEffect("SportsCar");
        //if (Input.GetKeyDown(KeyCode.P))
        //    PlaySoundEffect("Iap");
    }

    public void PlayMusic(Track track, bool resumeLastTrack = false, float volume = 1.0f)
    {
        if (resumeLastTrack)
        {
            PlayOneShotMusic(track,volume);
            return;
        }
        musicSource.Stop();
        musicSource.clip = null;
        musicSource.volume = volume;
        musicSource.clip = tracks[(int)track];
        musicSource.Play();

    }
    public void PlayMusic(int soundID)
    {
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlayMusic", soundID);
    }

    private void PlayOneShotMusic(Track track, float volume)
    {
        musicSource.Pause();
        oneShotMusicSource.clip = tracks[(int)track];
        oneShotMusicSource.Play();
        StartCoroutine(ResumeLastPlayedMusic(tracks[(int)track].length));
    }

    IEnumerator ResumeLastPlayedMusic(float delay)
    {
        yield return new WaitForSeconds(delay);
        musicSource.Play();
        //musicSource.loop = true;
        //musicSource.volume = lastTrackVol;
        //musicSource.clip = tracks[(int)lastPlayedTrack];
        //musicSource.Play();
        //musicSource.time = lastTrackPlaybackTime;
    }

	public bool IsMusicPlaying( Track musicTrack )
	{
		//DEBUG:
		print( "DEBUG: <color=white>AudioManager.IsMusicPlaying( " + musicTrack.ToString() + " ), last: " + lastPlayedTrack.ToString() + ", isPlaying? " + musicSource.isPlaying + "</color>" );

		return ( musicSource.isPlaying && ( musicTrack == lastPlayedTrack ) );
	}

    public void PlaySound(AudioType audioType, string name, float volume=1.0f, Vector2? position = null)
    {
        //print("Sound::::::" + name);
        SoundEffect s = System.Array.Find(soundEffects, sound => sound.name == name);
        if (s == null)
        {
            print("Sound : " + name + " not found.");
            return;
        }
        if(!Globals.AllowMusic)
        {
            return;
        }

        if (audioType == AudioType.ThreeD)
        {
            soundEffects3D[0].PlayAudio(s.clips[0], (Vector2)position, volume);
            return;
            if (position != null)
                foreach (SoundEffect3D s3d in soundEffects3D)
                {
                    if (!s3d.isInUse)
                    {
                        s3d.PlayAudio((Vector2)position, s.clips[0], volume);
                        return;
                    }
                }
            return;
        }

        s.source = SetSourceType(audioType);
        //s.source.volume = volume;
        if (audioType == AudioType.Loop)
        {
            s.source.clip = s.clips[0];
            s.source.Play();
        }
        
        else
        {
            s.source.PlayOneShot(s.clips[0], volume);
        }
    }

    private AudioSource SetSourceType(AudioType audioType)
    {
        if (audioType == AudioType.Enemy)
        {
            return enemiesAudioSource;
        }
        else if (audioType == AudioType.Explosion)
        {
            return explosionsAudioSource;
        }
        else if (audioType == AudioType.Player)
        {
            return playerSource;
        }
        else if (audioType == AudioType.Menu)
        {
            return normalAudioSource;
        }
        else if (audioType == AudioType.Loop)
        {
            foreach (AudioSource a in loopAudioSource)
            {
                if (a.clip == null)
                {
                    return a;
                }
                else
                {
                    
                    return AddNewSource();
                }
            }
            return null;
        }
        else if (audioType == AudioType.ThreeD)
        {
            return audioSource3D;
        }
        else
        {
            return null;
        }
    }

    private AudioSource AddNewSource()
    {
        AudioSource audioS = loopAudioSource[0].gameObject.AddComponent<AudioSource>();
        loopAudioSource.Add(audioS);
        return audioS;
    }

    public void SetMusicVolume(float volume)
    {
        musicSource.volume = volume;
    }

    private void ResetMusicVolume(Scene currentScene, Scene nextScene)
    {
        if (musicSource != null)
        {
            musicSource.volume = 1;
        }
    }

    public void PauseMusic()
    {
        musicSource.Pause();
    }

  //  IEnumerator StartLoopStopCoroutine(SoundEffect s)
  //  {
		//yield return null;
  //      s.source.clip = s.clips[0];
  //      s.source.loop = false;
  //      s.source.Play();
  //      yield return new WaitWhile(() => s.source.isPlaying);
  //      s.source.loop = true;
  //      s.source.clip = s.clips[1];
  //      s.source.Play();
  //  }

  //  public void StopAllSounds()
  //  {
		////DEBUG:
		//print( "DEBUG: <color=magenta>AudioManager.StopAllSounds.</color>" );

  //      //StopSoundEffect("CopSiren");
  //      //StopSoundEffect("SportsCar");
  //      //StopSoundEffect("Headstart");
  //      StopAllCoroutines();
  //      SetSoundEffectsActive(false);
		//trackPlaybackTime = musicSource.time;
  //      SetMusicActive(false);
  //  }

    public void StopSoundEffectByName(string name)
    {
        if (GetSoundEffect(name).source)
        {
            GetSoundEffect(name).source.Stop();
        }
    }

    public void Stop3DSoundEffectByName(string name)
    {
        if (GetSoundEffect(name).source)
        {
            GetSoundEffect(name).source.Stop();
        }
    }

    public void ResumeAllSounds()
    {
		//DEBUG:
		print( "DEBUG: <color=magenta>AudioManager.ResumeAllSounds.</color>" );

        //SetSoundEffectsActive(!GameManager.instance.profileManager.GetIsAudioMute());    
        musicSource.time = lastTrackPlaybackTime;
		//SetMusicActive(!GameManager.instance.profileManager.GetIsAudioMute());
    }

    //public void StopSoundEffect(string name)
    //{
    //    SoundEffect s = System.Array.Find(soundEffects, sound => sound.name == name);
    //    if (s == null)
    //    {
    //        printWarning("Sound : " + name + " not found.");
    //        return;
    //    }
    //    if (s.type == SoundEffectType.StartLoopEnd)
    //    {
    //        if (s.source.loop)
    //        {
    //            s.source.clip = s.clips[2];
    //            s.source.loop = false;
    //            s.source.Play();
    //        }
    //        else
    //        {
    //            printWarning("Failed to Stop SoundEffect");
    //        }
    //    }
    //    else
    //        s.source.Stop();
    //}

  //  public AudioSource GetSoundEffectSource(string name)
  //  {
		//return null;
  //      SoundEffect s = System.Array.Find(soundEffects, sound => sound.name == name);
  //      if (s == null)
  //      {
  //          printWarning("Sound : " + name + " not found.");
  //          return null;
  //      }
  //      return s.source;
  //  }

    public SoundEffect GetSoundEffect(string name)
    {
        SoundEffect s = System.Array.Find(soundEffects, sound => sound.name == name);
        if (s == null)
        {
            print("Sound : " + name + " not found.");
            return null;
        }
        return s;
    }

    public SoundEffect [] GetAllSoundEffects()
    {
        return soundEffects;
    }

    public void SetMusicActive(bool isActive)
    {
        musicSource.mute = isActive;
    }

    public void SetSoundEffectsActive(bool isActive)
    {
        playerSource.mute = isActive;
        print(playerSource.mute);
        normalAudioSource.mute = isActive;
        print(normalAudioSource.mute);
        foreach (AudioSource a in loopAudioSource)
        {
            a.mute = isActive;
            print(a.mute);
        }
        explosionsAudioSource.mute = isActive;
        print(explosionsAudioSource.mute);
        enemiesAudioSource.mute = isActive;
        print(enemiesAudioSource.mute);
    }

    public void StopMusic()
    {
        if (Globals.AllowBgMusic)
        {
            musicSource.Stop();
        }
        else
            return;
    }

    public void StopAllSoundEffects()
    {
        if (Globals.AllowMusic)
        {
            playerSource.Stop();
            normalAudioSource.Stop();
            foreach (AudioSource a in loopAudioSource)
            {
                a.Stop();
            }
            explosionsAudioSource.Stop();
            enemiesAudioSource.Stop();
        }
        else
            return;
    }

    [System.Serializable]
    public class SoundEffect
    {
        public string name;
        public SoundEffectType type;
        public AudioClip[] clips;
        
        [Range(0f, 2f)]
        public float volume;
        [Range(0.1f, 3f)]
        public float pitch;

        [HideInInspector]
        public AudioSource source;

        [HideInInspector] public float lastPitchShiftTime;
    }


}
