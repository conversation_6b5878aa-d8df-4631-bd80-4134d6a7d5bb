﻿using System;

using Apq.ApqMath;

/// <summary>
/// 一个战内提升
/// </summary>
public class FightHoist : ICloneable
{
    /// <summary>
    /// 提升类型(与BuffEffect.Param1相同)
    /// </summary>
    public HoistType HoistType { get; set; }
    /// <summary>
    /// 提升量
    /// </summary>
    public Bytes8 HoistValue { get; set; }
    /// <summary>
    /// 是否为百分比提升
    /// </summary>
    public bool IsPct { get; set; }

    #region ICloneable
    public object Clone()
    {
        var clone = new FightHoist();
        CopyTo(clone);
        return clone;
    }

    public void CopyTo(FightHoist other)
    {
        other.HoistType = HoistType;
        other.HoistValue = HoistValue;
        other.IsPct = IsPct;
    }
    #endregion
}
