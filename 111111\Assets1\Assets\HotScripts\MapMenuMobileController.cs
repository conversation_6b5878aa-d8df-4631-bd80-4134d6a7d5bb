using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
using UnityEngine.UI;
using TMPro;
using UnityEngine.SceneManagement;
public class MapMenuMobileController : MonoBehaviour
{
    //public MobileControlsMenu controlsMenuObj;
    [SerializeField] private GameObject mainMenuObject;
    [SerializeField] private MainSettingsMenu settingsMenuObj;
    public bool isOpen = false;
    public bool sceneExit = false;
    public CustomButton MapButton;
    [SerializeField] private CustomButton focusMissionButton;
    [SerializeField] private SkeletonGraphic rays = null;

    [SerializeField] private CustomButton settingsButton;
    [SerializeField] private CustomButton shopButton;
    [SerializeField] private CustomButton sidekicksButton;
    [SerializeField] private CustomButton survivalMissionButton;
    [SerializeField] private CustomButton backToMainHQButton;
    [SerializeField] private CustomButton homeButton;

    List<CustomButton> buttonArray = new List<CustomButton>();
    List<CustomButton> activebuttonArray = new List<CustomButton>();


    int currentSelected = 0;
    But<PERSON> controlsButton;
    Sequence seq;

    private void Start()
    {
        Init();
    }

    private void Init()
    {


        Observer.RegisterCustomEvent(gameObject, "SHOW_SETTINGS_ACCESSIBILITY_MENU", () =>
        {
            //PlayerPrefs.SetInt("isAssistMode", 1);
            Globals.isAssistMode = PlayerPrefs.GetInt("isAssistMode", 1) == 1 ? true : false;
            PlayerPrefs.SetInt("screenShakeEnabled", 1);
            Globals.screenShakeEnabled = true;
            //UserDefault::getInstance().flush(); TODO Ask Bilal bhai


            //UICustom::Popup* pop = UICustom::Popup::create(Size(850, 720), GameData::getInstance().getMenuData(GAME_DATA_SCENE::MAP_MENU).at("settingsButton").asString());
            //Director::getInstance().getRunningScene().addChild(pop, INT_MAX);
            //pop.getDisplayArea().addChild(settingsMenuObj);
            //settingsMenuObj.setPosition(Vec2(5,5));
            PlayerPrefs.SetInt("firstTimeOnUpdate", 0);
        });

        sceneExit = false;
        settingsButton.defaultAction=() =>
        {
            Globals.isMouseOverUI = true;
            settingsMenuObj.Init();
        }; 
        //Globals.Rescale(settingsButton.gameObject, 1.0f);
        settingsButton.SetInteractable(true);
        buttonArray.Add(settingsButton);
        homeButton.defaultAction = () =>
        {
            SceneManager.LoadScene(1);
            //mainMenuObject.SetActive(true);
            homeButton.SetInteractable(false);
        };
        //Globals.Rescale(homeButton.gameObject, 1.0f);
        homeButton.SetInteractable(true);

        buttonArray.Add(homeButton);
        backToMainHQButton.defaultAction = () =>
        {
            BackToHQCall();
        };
        backToMainHQButton.mainImage.DOFade(0, 0);
        backToMainHQButton.GetComponentInChildren<TextMeshProUGUI>().DOFade(0, 0);
        backToMainHQButton.SetInteractable(false);
        buttonArray.Add(backToMainHQButton);

        focusMissionButton.defaultAction=() =>
        {

            FocusButtonCall();

        };
        //Globals.Rescale(focusMissionButton.gameObject, 0.55f);

        buttonArray.Add(focusMissionButton);


        //Globals.Rescale(MapButton.gameObject, 1.0f);

        buttonArray.Add(MapButton);
        rays.gameObject.SetActive(false);


        currentSelected = 0;

        //TODO
        //auto keyListener = EventListenerKeyboard::create();
        //keyListener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)
        //    {
        //        if (hudMenuEnabledController)
        //        {

        //                        event.stopPropagation();
        //                    if (keyCode == EventKeyboard::KeyCode::KEY_TAB)
        //                    {
        //                        Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);


        //                        if (onHQController)
        //                        {
        //                            // disable this menu and enable headquarter menu
        //                            hudMenuEnabledController = false;

        //                            mapMenuEnabledController = false;
        //                            hqMenuEnabledController = true;
        //                            Observer.DispatchCustomEvent("activate_controller_support_hq");
        //    rays.gameObject.SetActive(false);
        //                            for (auto a : activebuttonArray)
        //                            {
        //                                a.stopAnimation();
        //                            }
        //                        }
        //                        if (onMapController)
        //{
        //    // disable this menu and enable main menu map
        //    hudMenuEnabledController = false;
        //    mapMenuEnabledController = true;
        //    rays.gameObject.SetActive(false);
        //    Observer.DispatchCustomEvent("entered_missionselect");

        //    for (auto a : activebuttonArray)
        //    {
        //        a.stopAnimation();
        //    }

        //}


        //                    }
        //                    //            event.stopPropagation();
        //                    if (keyCode == EventKeyboard::KeyCode::KEY_RIGHT_ARROW)
        //{

        //    if (currentSelected < 2)
        //    {
        //        for (auto a : activebuttonArray)
        //        {
        //            a.stopAnimation();
        //        }
        //        currentSelected++;
        //        rays.gameObject.SetActive(true);
        //        rays.setPosition(activebuttonArray.at(currentSelected).getPosition());
        //        Globals.PlaySound(SOUND_HOVER);
        //        activebuttonArray.at(currentSelected).playAnimation();
        //    }

        //}
        //if (keyCode == EventKeyboard::KeyCode::KEY_LEFT_ARROW)
        //{

        //    if (currentSelected > 0)
        //    {
        //        for (auto a : activebuttonArray)
        //        {
        //            a.stopAnimation();
        //        }
        //        currentSelected--;
        //        rays.gameObject.SetActive(true);
        //        rays.setPosition(activebuttonArray.at(currentSelected).getPosition());
        //        Globals.PlaySound(SOUND_HOVER);
        //        activebuttonArray.at(currentSelected).playAnimation();


        //    }
        //}

        //if (keyCode == EventKeyboard::KeyCode::KEY_UP_ARROW)
        //{

        //    if (currentSelected > 1)
        //    {
        //        for (auto a : activebuttonArray)
        //        {
        //            a.stopAnimation();
        //        }
        //        currentSelected--;
        //        rays.gameObject.SetActive(true);
        //        rays.setPosition(activebuttonArray.at(currentSelected).getPosition());
        //        Globals.PlaySound(SOUND_HOVER);
        //        activebuttonArray.at(currentSelected).playAnimation();


        //    }




        //}
        //if (keyCode == EventKeyboard::KeyCode::KEY_DOWN_ARROW)
        //{

        //    if (currentSelected == 0 || currentSelected == 1)
        //    {
        //        for (auto a : activebuttonArray)
        //        {
        //            a.stopAnimation();
        //        }
        //        currentSelected = 2;
        //        rays.setPosition(activebuttonArray.at(currentSelected).getPosition());
        //        Globals.PlaySound(SOUND_HOVER);
        //        activebuttonArray.at(currentSelected).playAnimation();


        //    }




        //}

        //if (keyCode == EventKeyboard::KeyCode::KEY_ENTER)
        //{
        //    activebuttonArray.at(currentSelected).MainFunc(NULL);
        //    Globals.PlaySound(SOUND_BUTTON_TAP);
        //    if (currentSelected != 0)
        //    {
        //        rays.gameObject.SetActive(false);
        //    }



        //}

        //if (keyCode == EventKeyboard::KeyCode::KEY_ESCAPE)
        //{
        //    if (isOnIsland)
        //    {
        //        Observer.DispatchCustomEvent("Exit_New_Island");
        //    }
        //    else
        //    {
        //        Director::getInstance().replaceScene(MainMenuScene::createScene());
        //    }

        //}

        //                }




        //            };
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);

        CreateEvents();
        UpdateActiveButtons();
    }

    private void OnExit()
    {

    }

    public void CreateIndicatorsForItems()
    {
        //TODO Ask Zaeem To Look Into It
        //if (!shopButton)
        //    return;

        //int getShopItemsAvailable = GameData::getInstance().calculateShopItemsToBeUnlocked();
        //if (getShopItemsAvailable > 0)
        //{
        //    Sprite* circle = Sprite::create("res/CustomButton/Circle2.png");
        //    shopButton.addChild(circle, INT_MAX);
        //    circle.setTag(5);
        //    circle.setPosition(50, 60);
        //    circle.setColor(Color3B::RED);
        //    circle.runAction(RepeatForever::create(Sequence::create(EaseSineInOut::create(ScaleTo::create(0.75f, 1.15f)), EaseSineInOut::create(ScaleTo::create(0.75f, 1.0f)), NULL)));

        //    Label* numberOfItems = Label::createWithTTF(Shared::convertStringNumbersToArabic(to_string(getShopItemsAvailable)), GAME_FONT, 20);
        //    circle.addChild(numberOfItems);
        //    numberOfItems.setPosition(circle.getContentSize() / 2);
        //    Shared::fontToCustom(numberOfItems);
        //}


        //getShopItemsAvailable = GameData::getInstance().getItemsAvailableInSideKickShop();

        //if (getShopItemsAvailable > 0)
        //{
        //    Sprite* circle = Sprite::create("res/CustomButton/Circle2.png");
        //    sidekicksButton.addChild(circle, INT_MAX);
        //    circle.setTag(5);
        //    circle.setPosition(50, 60);
        //    circle.setColor(Color3B::RED);
        //    circle.runAction(RepeatForever::create(Sequence::create(EaseSineInOut::create(ScaleTo::create(0.75f, 1.15f)), EaseSineInOut::create(ScaleTo::create(0.75f, 1.0f)), NULL)));

        //    Label* numberOfItems = Label::createWithTTF(Shared::convertStringNumbersToArabic(to_string(getShopItemsAvailable)), GAME_FONT, 20);
        //    circle.addChild(numberOfItems);
        //    numberOfItems.setPosition(circle.getContentSize() / 2);
        //    Shared::fontToCustom(numberOfItems);
        //}

    }

    public void OnEnter()
    {

    }

    public void BackToHQCall()
    {
        {

            Observer.DispatchCustomEvent("ON_ENABLE_HQ");
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.Whoosh);

            //Globals.PlaySound("res/Sounds/SFX/Whoosh 8_1.mp3");
            if (!Globals.disableScroll)
            {

                Globals.disableScroll = true;


                Globals.hudMenuEnabledController = false;
                Globals.hqMenuEnabledController = true;
                Globals.onHQController = true;
                rays.gameObject.SetActive(false);
                Observer.DispatchCustomEvent("activate_controller_support_hq");


//#if UNITY_STANDALONE
                Globals.onMap = false;
                Observer.DispatchCustomEvent("BACK_TO_MAP");
                Observer.DispatchCustomEvent("show_take_off_button");
                Observer.DispatchCustomEvent("hide_back_to_HQ_button");
                Observer.DispatchCustomEvent("HIDE_CURRENT_MISSION_KIT");

                if (Globals.isJoystickConnected)
                {
                    Globals.onMapController = false;
                    Globals.onHQController = true;
                }
//#else
//                Globals.onMap = false;
//                Observer.DispatchCustomEvent("BACK_TO_MAP");
//                Observer.DispatchCustomEvent("show_take_off_button");
//                Observer.DispatchCustomEvent("hide_back_to_HQ_button");
//                Observer.DispatchCustomEvent("HIDE_CURRENT_MISSION_KIT");
//                if (Globals.isJoystickConnected)
//                {
//                    Globals.onMapController = false;
//                    Globals.onHQController = true;
//                }

////#endif
            }
            seq = DOTween.Sequence().AppendInterval(1).AppendCallback(() => { Globals.disableScroll = false; }).Play();
        }
    }

    public void UpdateActiveButtons()
    {
        //Change to New Button
        activebuttonArray.Clear();
        foreach (CustomButton a in buttonArray)
        {
            if (a.GetInteractable())
            {
                activebuttonArray.Add(a);
            }
        }
        //log("activebuttonArray.size:%lu", activebuttonArray.size());
    }

    public void FocusButtonCall()
    {
        if (!Globals.disableScroll)
        {
            Globals.disableScroll = true;


            Observer.DispatchCustomEvent("ON_DISABLE_HQ");
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.Whoosh);

            //Globals.PlaySound("res/Sounds/SFX/Whoosh 8_1.mp3");



//#if UNITY_STANDALONE
            Observer.DispatchCustomEvent("show_back_to_HQ_button");
            Observer.DispatchCustomEvent("hide_take_off_button");
            Observer.DispatchCustomEvent("FOCUS_MISSION");
            Observer.DispatchCustomEvent("SHOW_CURRENT_MISSION_KIT");

            Globals.onMap = true;

//#if UNITY_STANDALONE
            Globals.onMapController = true;
            Globals.onHQController = false;
//#endif

            if (Globals.isJoystickConnected)
            {
                Globals.onMapController = true;
                Globals.onHQController = false;
            }

//#else
//            Observer.DispatchCustomEvent("show_back_to_HQ_button");

//            Observer.DispatchCustomEvent("FOCUS_MISSION");
//            Observer.DispatchCustomEvent("hide_take_off_button");
//            Observer.DispatchCustomEvent("SHOW_CURRENT_MISSION_KIT");
//            Globals.onMap = true;
//            if (Globals.isJoystickConnected)
//            {
//                Globals.onMapController = true;
//                Globals.onHQController = false;
//            }
//#if UNITY_STANDALONE
//    Globals.onMapController = true;
//    Globals.onHQController = false;
//#endif
//#endif
            seq = DOTween.Sequence().AppendInterval(1f).AppendCallback(() =>
            {
                Globals.disableScroll = false;
            });


            Globals.hudMenuEnabledController = false;
            Globals.mapMenuEnabledController = true;
            Globals.hqMenuEnabledController = false;
            rays.gameObject.SetActive(false);
            Observer.DispatchCustomEvent("entered_missionselect");

        }


    }

    private void Update()
    {

    }
    private void CreateEvents()
    {
        Observer.RegisterCustomEvent(gameObject, "Enable_Map_Menu_On_Controller", () =>
         {
             rays.transform.position = settingsButton.transform.position;
             currentSelected = 0;
             //activebuttonArray[currentSelected].playAnimation(); TODO
             rays.gameObject.SetActive(true);

         });
        Observer.RegisterCustomEvent(gameObject, "call_focus_button", () =>
        {
            rays.gameObject.SetActive(false);
            FocusButtonCall();
        });
        Observer.RegisterCustomEvent(gameObject, "on_focus_button", () =>
        {
            //rays.gameObject.SetActive(true);
            //rays.transform.position = focusMissionButton.transform.position;
        });
        Observer.RegisterCustomEvent(gameObject, "not_on_focus_button", () =>
        {
            rays.gameObject.SetActive(false);
        });
        Observer.RegisterCustomEvent(gameObject, "show_back_to_HQ_button", () =>
        {
            if (!backToMainHQButton.GetInteractable())
            {
                backToMainHQButton.mainImage.DOFade(1, 0.25f);
                backToMainHQButton.defaultLabel.DOFade(1, 0.25f);
                backToMainHQButton.transform.DOScale(1, 0.25f);

                backToMainHQButton.SetInteractable(true);
                //backToMainHQButton.getBaseButton().setEnabled(true);
                backToMainHQButton.gameObject.SetActive(true);

                UpdateActiveButtons();
            }
        });
        Observer.RegisterCustomEvent(gameObject, "hide_back_to_HQ_button", () =>
        {
            if (backToMainHQButton.GetInteractable())
            {
                backToMainHQButton.mainImage.DOFade(0, 0.25f);
                backToMainHQButton.defaultLabel.DOFade(0, 0.25f);
                backToMainHQButton.transform.DOScale(0, 0.25f);

                backToMainHQButton.SetInteractable(false);
                //seq = DOTween.Sequence();
                //seq.AppendInterval(0.25f).AppendCallback(() =>
                //{
                //    backToMainHQButton.gameObject.SetActive(false);
                //});
                //seq.Play();
                UpdateActiveButtons();
            }

        });
        Observer.RegisterCustomEvent(gameObject, "show_take_off_button", () =>
        {

            if (!focusMissionButton.GetInteractable())
            {
                focusMissionButton.mainImage.DOFade(1, 0.25f);
                focusMissionButton.transform.DOScale(1, 0.25f);
                focusMissionButton.SetInteractable(true);
                UpdateActiveButtons();
            }
        });

        Observer.RegisterCustomEvent(gameObject, "hide_take_off_button", () =>
        {
            //
            if (focusMissionButton.GetInteractable())
            {
                focusMissionButton.GetComponentInChildren<Image>().DOFade(0, 0.25f);
                focusMissionButton.transform.DOScale(0, 0.25f);
                focusMissionButton.SetInteractable(false);
                UpdateActiveButtons();
            }

        });
    }
}
        
