using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
using Spine;
public class Turret : Enemy
{
    [SerializeField] private Sprite bulletSprite;
    private enum State
    {
        idle,
        shoot
    };
    //private Bone bone;
    private State currentState;

    private void Awake()
    {
        tweenId = "Turret" + GetInstanceID().ToString();
        schedulerId = "TurretS" + GetInstanceID().ToString();
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        isDestructable = false;
        allowRelocate = false;
        enemyCollisionRadius = Globals.CocosToUnity(100.0f);
        enemySchedulerSpeed = 0;
        InitStats();
        //enemySprite.state.SetAnimation(0, "idle", false);
        currentState = State.idle;
        //bone = enemySprite.skeleton.FindBone("gun");

        //enemySprite.state.Event += TurretEventHandler;
        Globals.numberOfEnemies++;
        //Observer.RegisterCustomEvent(gameObject, "GeneratorDestroyed", () =>
        //{
        //    enemySprite.state.SetAnimation(0, "end", false);
        //    DOTween.Sequence().SetId(tweenId).Append(enemySprite.GetComponent<Renderer>().material.DOColor(new Color(200 / 255, 200 / 255, 200 / 255), 0.5f)).Play();// enemySprite.runAction(TintTo::create(0.5, 200, 200, 200));
        //    scheduleUpdate = false;
        //});
        scheduleUpdate = true;
    }

    private void OnDisable()
    {
        //enemySprite.state.Event -= TurretEventHandler;
    }

    void TurretEventHandler(TrackEntry entry, Spine.Event spineEvent)
    {
        //if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)
        //{
        if (spineEvent.Data.Name == "shoot")
        {
            Shoot();
        }

        if (spineEvent.Data.Name == "changeAnim")
        {
            ChangeAnimation();
        }
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        //if (currentState == State.idle)
        //{
        //    float angle = 90 + Globals.CalcAngle(player.skeletonAnimationTran.position, enemyPoint.position) * -1;
        //    bone = enemySprite.skeleton.FindBone("gun");

        //    if (bone.Rotation - angle > 180)
        //    {
        //        bone.Rotation += -360;
        //    }
        //    if (bone.Rotation - angle < -180)
        //    {
        //        bone.Rotation += 360;
        //    }
        //    bone.Rotation = Mathf.Lerp(bone.Rotation, angle, Time.deltaTime * 2.5f);
        //}
        CheckUpdate();
    }

    public void Disable()
    {
        scheduleUpdate = false;
        //enemySprite.GetComponent<Renderer>().enabled = false;
        //enemySprite.state.SetAnimation(0, "end", false);
        transform.SetWorldPositionY(Globals.CocosToUnity(-4000));
        Observer.RegisterCustomEvent(gameObject, Globals.ACTIVATE_TURRET_EVENT, () =>
         {

             Activate();


         });
    }

    public void Activate()
    {
        scheduleUpdate = true;
        //enemySprite.GetComponent<Renderer>().enabled = true;
        //enemySprite.state.SetAnimation(0, "idle", false);
        //DOTween.Sequence().SetId(tweenId).Append(transform.DOBlendableMoveBy(new Vector2(0, -2.36f), 0.35f)).Play();

        transform.DOMove(new Vector2(transform.position.x, Globals.LOWERBOUNDARY),0.35f);
    }

    private void Shoot()
    {

        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        
        bullet.setDamage(stats.bulletDamage);
        bullet.duration = stats.bulletSpeed;
        //bullet.transform.SetRotation(90);
        bullet.setRadiusEffectSquared(1.2f);
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireballTrail);
        bullet.SetSpriteFrame(bulletSprite);
        //    SkeletonAnimation *bulletOverlay = SkeletonAnimation::createWithJsonFile("res/Missions/turretBullet.json", "res/Missions/turretBullet.atlas");
        //    bullet.addChild(bulletOverlay);
        //    bulletOverlay.setCameraMask(GAMECAMERA);
        //    bulletOverlay.setScale(5);
        bullet.transform.SetRotation(enemyPoint.GetRotation());
        //float angle = bone.Rotation * -1;

        //bone = enemySprite.skeleton.FindBone("gunShoot");
        bullet.transform.position = enemyPoint.position;
        //bullet.transform.SetRotation(angle);
        Vector2 dest = new Vector2(Globals.CocosToUnity(2600) * Mathf.Sin(Mathf.Deg2Rad* enemyPoint.GetRotation()), Globals.CocosToUnity(2600) * Mathf.Cos(Mathf.Deg2Rad* enemyPoint.GetRotation()));
        bullet.PlayBulletAnim(stats.bulletSpeed, dest);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    private void InitStats()
    {

        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 1.5f + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = (14 + 10) * 100;
        stats.bulletDamage = baseStats.bulletDamage = 55;
        stats.missileDamage = baseStats.missileDamage = (14) * 10;
        stats.bulletSpeed = baseStats.bulletSpeed = 2.5f;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 15;
        stats.xp = baseStats.xp = stats.maxHealth.Value;
    }

    private void ChangeAnimation()
    {

        if (currentState == State.idle)
        {
            //enemySprite.state.SetAnimation(0, "shoot", false);
            currentState = State.shoot;
        }
        else
        {
            currentState = State.idle;
            //enemySprite.state.SetAnimation(0, "idle", false);

        }


    }

}
