
using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections;
using DG.Tweening;
public class SwipeScrollerWorldSpace : MonoBehaviour
{
    public float xMin;
    public float xMax;
    public float yMin;
    public float yMax;
    private bool touchEnabled = false;

    [SerializeField] private Camera Camera;

    private bool touchStarted;
    private bool movingCamera = false;
    private bool startLerp = false;
    float elapsedTouchTime = 0;
    float elapsedTime = 0;
    float duration;

    private Vector3 camStartPos;
    private Vector3 touchStartPos;
    private Vector3 touchEndPos;
    private Vector3 camLerpPos = Vector3.zero;
    private float velocity;
    private float stationaryTime;

    private int pointsMoved = 0;
    private void Start()
    {
        touchEnabled = true;
    }
    //Perform Movement After Swipe
    private void Update()
    {

        Vector3 delta1 = Vector3.zero;
        Vector3 clampVec = Vector3.zero;
        //Scroll
        if (!touchEnabled||Globals.isOnIsland||EventSystem.current.IsPointerOverGameObject())
            return;

        if (!movingCamera)
        {
            if (Input.touchCount >= 1)
            {
                if (Input.GetTouch(0).phase == TouchPhase.Began)
                {
                    startLerp = false;
                    //touchStartPos  = Camera.ScreenToWorldPoint(Input.GetTouch(0).position);
                    camStartPos = Camera.transform.position;
                    elapsedTouchTime = 0;
                    stationaryTime = 0;
                    Globals.isTouchDragging = false;
                    pointsMoved = 0;
                }
                else if (Input.GetTouch(0).phase == TouchPhase.Moved)
                {
                    //touchEndPos = Camera.transform.position;
                    pointsMoved++;
                    if (pointsMoved >= 5)
                    {
                        velocity = GetSwipeVelocity(Input.GetTouch(0));
                        delta1 = HandleDragLogic(Input.GetTouch(0));
                        Camera.transform.Translate(delta1, Space.World);
                        camLerpPos = Camera.transform.position;
                        stationaryTime = 0;
                        Globals.isTouchDragging = true;
                    }
                }
                else if (Input.GetTouch(0).phase == TouchPhase.Ended)
                {
                    //touchEndPos= Camera.transform.position;
                    //velocity = GetSwipeVelocity(Input.GetTouch(0));
                    pointsMoved = 0;
                    camLerpPos = -(camStartPos- Camera.transform.position).normalized;
                    camLerpPos *= velocity*0.2f;
                    camLerpPos += Camera.transform.position;
                    startLerp = true;
                    DOTween.Sequence().AppendInterval(0.05f).AppendCallback(() => {
                        Globals.isTouchDragging = false; }).Play();
                }
                else if (Input.GetTouch(0).phase == TouchPhase.Stationary)
                {
                    stationaryTime += Time.deltaTime;
                    if (stationaryTime > 0.1f)
                    {
                        //touchEndPos= Camera.transform.position;
                        velocity = 0;
                        camLerpPos = Camera.transform.position;
                        startLerp = false;
                    }
                }
                elapsedTouchTime += Time.deltaTime;
            }
            if (startLerp)
            {
                Camera.transform.position = Vector3.Lerp(Camera.transform.position, camLerpPos, Time.deltaTime * velocity);
                if (Camera.transform.position == camLerpPos)
                {
                    startLerp = false;
                }
            }

            clampVec = Camera.transform.position;
            clampVec.x = Mathf.Clamp(clampVec.x, xMin, xMax);
            clampVec.y = Mathf.Clamp(clampVec.y, yMin, yMax);
            clampVec.z = Mathf.Clamp(clampVec.z, -10, -10);
            Camera.transform.position = clampVec;
        }

    }

    protected Vector3 HandleDragLogic(Touch touch)
    {
        //not moved
        if (touch.phase != TouchPhase.Moved)
            return Vector3.zero;

        var posBefore = Camera.ScreenToWorldPoint(touch.position - touch.deltaPosition);
        var posNow = Camera.ScreenToWorldPoint(touch.position);

        return posBefore - posNow;
    }

    private float GetSwipeVelocity(Touch touch)
    {
        float velocity = Vector3.Distance(touch.position,touch.deltaPosition) / elapsedTouchTime;
        return velocity*0.003f;
    }


    protected Vector3 HandleReleaseLogic(Vector3 v)
    {
        Vector3 dir = v * Time.deltaTime;
        return dir;
    }



    public void SetTouchEnabled(bool val)
    {
        touchEnabled = val;
    }

    public void ScrollToTarget(Vector3 targetPos, float time, bool attuned)
    {
        movingCamera = true;
        Camera.transform.DOMove(targetPos, 0.35f).SetEase(DG.Tweening.Ease.OutQuint).OnComplete(()=> { movingCamera = false; });
    }

    public void JumpToTarget(Vector3 targetPos, float mapZoomValue = 6f)
    {
        movingCamera = true;
        Camera.transform.DOMove(targetPos, 0.1f).SetEase(DG.Tweening.Ease.OutQuint).OnComplete(() => { movingCamera = false; });
        //Camera.orthographicSize = mapZoomValue;
    }

    public Vector2 GetContentPos()
    {
        return Camera.transform.position;
    }

    public void SlideToTarget(Vector3 TargetPos)
    {
        StopAllCoroutines();
        StartCoroutine(SlideAnim(TargetPos));
    }
   

    private IEnumerator SlideAnim(Vector3 target)
    {
        while (Camera.main.transform.position != target)
        {
            Camera.main.transform.position = Vector3.Lerp(Camera.main.transform.position, target, Time.deltaTime * 8f);
            yield return new WaitForEndOfFrame();
        }
    }
}
