using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Spine;
using Spine.Unity;
using DG.Tweening;
using UnityEngine.SceneManagement;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;

public class WorldMapController : MonoBehaviour
{ 
    private enum WORLD
    {
        KATMANDU,
        KATLANTIS
    };
    private WORLD currentWorld = WORLD.KATMANDU;

    private Vector3 mapCameraPoint;
    private Vector3 mousePosition;
    private Vector2 initialPosition1;
    private Vector2 initialPosition2;
    private Vector2 value;


    private List<MissionButtonController> buttonArray = new List<MissionButtonController>();
    private Vector3 newIslandPosition;

    private Bone _bone;
    [SerializeField] private Image blackScreen;
    [SerializeField] private SkeletonAnimation islandSpine = null;
    [SerializeField] private MapTrailController mapTrail = null;
    [SerializeField] private SkeletonAnimation pointerObject = null;
    [SerializeField] private WorldScrollLayerController worldScrollLayer = null;
    [SerializeField] private Button backFromIslandButton = null;
    [SerializeField] private MissionButtonController missionButton = null;
    [SerializeField] private MissionButtonController missionButtonBoss = null;

    [SerializeField] private SideMenuController sideMenuObj;
    [SerializeField] private SideMenuMapMobileController sideMenuMobileObj;
    [SerializeField] private MapMenuMobileController mobileMenu = null;
    [SerializeField] private GameObject kitCurrentMission;
    public SkeletonGraphic cloudTransition;
    [SerializeField] private Image cloudBL = null;
    [SerializeField] private Image cloudBR = null;
    [SerializeField] private Image cloudTL = null;
    [SerializeField] private Image cloudTR = null;
    [SerializeField] private Camera mapCamera = null;

    [SerializeField] private Material worldMapMat;
    [SerializeField] private Material[] islandMat;
    [SerializeField] private Material waveMaterial;

    private float waterOpacityCurrent;
    private float waterOpacityTarget;
    private float saturateValueTarget;
    private float saturateValueCurrent;
    private Vector2 wave1TilingCurrent;
    private Vector2 wave1TilingTarget;
    private Vector2 wave2TilingCurrent;
    private Vector2 wave2TilingTarget;
    private Vector2 noiseTilingCurrent;
    private Vector2 noiseTilingTarget;

    private bool _islandTransition = false;
    private bool isCurrentMissionKitVisible = false;
    private bool isTouchActive = false;

    private int _numberOfDots = 0;
    private int TotalMissions = 33;
    private int currentSelected = 1;

    private float zoomToFit = 350;
    private float timeDelta = 0;
    private float mapCameraToZ = 3.2f;
    private float tempCameraHeight;
    private float transitionDelay;
    private float initialTouchDistance;
    private float previousDistance;
    private float mapZoomLerpValue;
    private bool scheduleUpdate = false;

    private DG.Tweening.Sequence seq;

    private static int currentMission =32;
    private static int missionsCompleted = 32;


    private DefaultInputActions inputActions;
    [SerializeField] private WorldMapNavController buttonSelected;
    [SerializeField] private CustomButton homeButton;

    private void Awake()
    {
        inputActions = new DefaultInputActions();

#if UNITY_WEBGL || UNITY_IOS
        Globals.mobileControls = true;
        if (Input.GetJoystickNames().Length > 0)
        {
            Globals.isJoystickConnected = true;
            Globals.mobileControls = false;
        }
#else
        Globals.mobileControls = false;
        if (Input.GetJoystickNames().Length > 0)
        {
            Globals.isJoystickConnected = true;
        }
#endif
    }

    private void Start()
    {
    //    GameData.instance.fileHandler.currentMission = 32;
    //    GameData.instance.fileHandler.missionsCompleted = 32;
    //    GameData.instance.fileHandler.missionUnlock = 32;
    //    Globals.showNextMission = false;
        SetMatValues();
        Init();
    }

    public void SetDebugValues(InputField input)
    {
        currentMission = missionsCompleted = int.Parse(input.text);
        SceneManager.LoadScene(2);
    }

    private void SetMatValues()
    {
        waterOpacityCurrent = waterOpacityTarget = 0.3f;
        saturateValueCurrent = saturateValueTarget = 0.6f;
        wave1TilingCurrent = wave1TilingTarget = new Vector2(0.25f, 0.5f);
        wave2TilingCurrent = wave2TilingTarget = new Vector2(0.5f, 1f);
        noiseTilingCurrent = noiseTilingTarget = new Vector2(1f, 1f);
        worldMapMat.color = Color.white;
        foreach (Material m in islandMat)
        {
            m.color = new Color(1, 1, 1, 0);
        }
    }


    private void Init()
    {
        ReportAchievement();
        //ThirdPartyInterface::setGKAccessPoint(false, false);TODO

        Globals.disableScroll = false;
        Globals.g_showDifficultyInUtilityMenu = true;
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
        mapZoomLerpValue = 11f;

        //this.setAnchorPoint(cocos2d::Point::ANCHOR_BOTTOM_LEFT);
        //mapCameraToZ = (Constants.GameConstants.CAMERA_MAX_SCROLL_HEIGHT + Constants.GameConstants.CAMERA_MIN_SCROLL_HEIGHT) / 2;
        tempCameraHeight = mapCameraToZ;
        //mapCameraPoint = winSize / 2;
        mapCameraPoint = new Vector3(-22/2, -4.4f/2f, -20);
        zoomToFit = Constants.GameConstants.CAMERA_BUTTON_PRESS_HEIGHT;
        Globals.allowDialogueFromBoss = true;
        GameData.instance.fileHandler.currentMission = Mathf.Clamp(GameData.instance.fileHandler.currentMission, 0, TotalMissions);
        if (GameData.instance.fileHandler.currentMission < 1)
        {
            GameData.instance.fileHandler.currentMission = 1;
        }
//#if UNITY_STANDALONE
//        UNITY_STANDALONEPointer* dp = UNITY_STANDALONEPointer::create(false);
//        this.addChild(dp, INT_MAX);
//        mapCameraToZ = (CAMERA_MAX_SCROLL_HEIGHT);
//        tempCameraHeight = mapCameraToZ;
//#endif

//        auto glView = Director::getInstance().getOpenGLView();
//        auto frameSize = glView.getFrameSize();
//#if UNITY_STANDALONE
//        frameSize = frameSize * glView.getFrameZoomFactor() * glView.getRetinaFactor();
//#endif

        //{
        //    mapCamera = Camera::createPerspective(60, (GLfloat)frameSize.width / frameSize.height, 1, 3000);
        //    mapCamera.lookAt(Vec3(0, 0, 0), Vec3(0, 0, 0));
        //    mapCamera.setPositionZ(CAMERA_HEIGHT * 3);
        //    mapCamera.setPosition(mapCameraPoint);
        //    this.addChild(mapCamera);
        //    mapCamera.setCameraFlag(CameraFlag::USER3);
        //}
        //Director::getInstance().getOpenGLView().setCursorVisible(false);

        float startDelayTime = 0.85f;
        bool showBlackScreen = true;

        if (!Globals.backFromGame)
        {
            Vector2 winSize;
            winSize.x = 1334;
            winSize.y = 750;

            Vector2 actualWinSize = Globals.GetScreenSize();
            float factorX = actualWinSize.x / winSize.x;
            float factorY = actualWinSize.y / winSize.y;
            cloudTransition.AnimationState.SetAnimation(0, "MenuToMap2", false);
            //cloudTransition.GetComponent<RectTransform>().localScale=new Vector2(0.27f * factorX, 0.27f * factorY);
            seq = DOTween.Sequence();
            seq.AppendInterval(3 + startDelayTime).AppendCallback(()=> { cloudTransition.gameObject.SetActive(false); });
            seq.Play();
            Globals.backFromGame = true;
            showBlackScreen = false;
        }

        seq = DOTween.Sequence();
        seq.AppendInterval(0.22f).AppendCallback(() => { scheduleUpdate = true; });
        seq.Play();
        bool attuned = true;

        if (Globals.backFromGame)
        {
            transitionDelay = 0.0f;
            //mapCamera.setPositionZ(CAMERA_MAX_SCROLL_HEIGHT * 1.5f); TODO

            if (showBlackScreen)
            {
                blackScreen.gameObject.SetActive(true);
                blackScreen.DOFade(0, 1);
            }
            attuned = false;
            Globals.backFromGame = false;

        }
        else
        {
            transitionDelay = Constants.GameConstants.cloudTransitionDelay;
        }

        // if next mission button is pressed, show mission info






        if (GameData.instance.fileHandler.missionsCompleted == 30)
        {
            GameData.instance.fileHandler.missionUnlock = 0;
            mapTrail.TrailSetTo(GameData.instance.fileHandler.missionsCompleted, islandSpine.transform); 
        }

        if (GameData.instance.fileHandler.missionsCompleted >= 30)
        {
            seq = DOTween.Sequence();
            seq.AppendInterval(0.5f).AppendCallback(() => {
                PlayerPrefs.SetInt("IsIslandUnlocked", 1);
                PlayerPrefs.SetInt("newIslandAnimationPlayed", 1);
            });
            seq.Play();
            
        }

        if (Globals.showNextMission)
        {
            LandOnNextMission();

        }
        else
        {
            LandOnHQ();

        }

//#if UNITY_STANDALONETODO
//    CreateMouseSupport();
//    worldScrollLayer.getScrollView().setInertiaScrollEnabled(false);
//    worldScrollLayer.getScrollView().setBounceEnabled(false);
//    worldScrollLayer.getScrollView().setTouchEnabled(false);
    
//    worldScrollLayer.getScrollView().addEventListener([=](Ref *sender, ui::ScrollView::EventType type){
//        if(type == ui::ScrollView::EventType::SCROLLING)
//        {
//            if(mapCameraToZ  < CAMERA_BUTTON_PRESS_HEIGHT + 20)
//            {
//                sideMapMenuObj.CloseButtonCall();
//            }
//        }
//    });
//#endif






        CreateMissionPopupObject();
        CreateNavigationMenu();
        CreateAccelerometerSupport();
        CreateCurrentMissionKitIcon();
        CreateEvents();
        NewUpdateCall();

        CreateNewIsland();


        Globals.onMapMenu = true;

        kitCurrentMission.transform.parent = mapTrail.transform;
        //    islandtransitionExit(newIslandPosition);

#if UNITY_WEBGL ||UNITY_IOS
        Destroy(sideMenuObj.gameObject);
#else
        Destroy(sideMenuMobileObj.gameObject);
#endif
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
#if !UNITY_STANDALONE

            if (GameData.instance.fileHandler.missionsCompleted < 31 &&!Globals.disableScroll) 

            {
                //        mobileMenu.focusMissionButton.setPosition(mapCamera.getPosition())

                MissionButtonController button = buttonArray[GameData.instance.fileHandler.missionsCompleted];
                //        log("MISSIONCOMPLETED:%d", GameData.instance.fileHandler.missionsCompleted);

                //        float angle = Shared::calcAngle(Point(mobileMenu.focusMissionButton.getBaseButton().getWorldPosition()), button.getWorldPosition() );
                //        mobileMenu.focusMissionButton.setRotation(angle);

                //AABB a = AABB(Vec3(button.getWorldPosition().x - 0, button.getWorldPosition().y - 0, 0), Vec3(button.getWorldPosition().x + 0, button.getWorldPosition().y + 0, 0));
                //mapCamera.
                //        a.transform(getNodeToWorldTransform());
                //        Camera *camera = Director::getInstance().getRunningScene().getCameras().at(1);
                if (button.GetComponent<Renderer>().isVisible)// mapCamera.isVisibleInFrustum(&a))
                {
                    if (GameData.instance.fileHandler.currentMission <= 30)
                    {
                        Observer.DispatchCustomEvent("SHOW_CURRENT_MISSION_KIT");
                        Observer.DispatchCustomEvent("hide_take_off_button");
                    }

                }
                else
                {
                    {
                        if (GameData.instance.fileHandler.currentMission <= 30)
                        {
                            Observer.DispatchCustomEvent("HIDE_CURRENT_MISSION_KIT");
                            Observer.DispatchCustomEvent("show_take_off_button");
                        }

                    }
                }

            }
#endif
            timeDelta += Time.deltaTime;
            //worldScrollLayer.GetMap().(WorldScrollLayerController. * 2);

            if (timeDelta > transitionDelay)
            {
#if UNITY_STANDALONE
                mousePosition = sideMenuObj._mousePosition;
#endif
                //mapCamera.transform.SetWorldPositionZ(mapCamera.transform.GetWorldPositionZ() + ((-mapCamera.transform.GetWorldPositionZ() + mapCameraToZ) * Time.deltaTime * mapZoomLerpValue));
                //mapCamera.orthographicSize = mapCamera.orthographicSize + ((-mapCamera.orthographicSize+mapCameraToZ) * Time.deltaTime * mapZoomLerpValue);

#if UNITY_STANDALONE
                mapCamera.transform.position = Vector3.Lerp(mapCamera.transform.position, -mapCamera.transform.position+ mapCameraPoint + (Camera.main.ScreenToWorldPoint(UnityEngine.Input.mousePosition) * 10f / Globals.CocosToUnity(18000)), Time.deltaTime * 8.0f);
                
                mapCamera.orthographicSize = Mathf.Lerp(mapCamera.orthographicSize, mapCameraToZ, Time.deltaTime * 8.0f);
                mapCamera.orthographicSize = Mathf.Clamp(mapCamera.orthographicSize,1.5f, 6);
                waterOpacityCurrent = waveMaterial.GetFloat("_Water_Opacity");
                waterOpacityCurrent = Mathf.Lerp(waterOpacityCurrent, waterOpacityTarget, Time.deltaTime * 14f);
                waveMaterial.SetFloat("_Water_Opacity", waterOpacityCurrent);

                saturateValueCurrent = waveMaterial.GetFloat("_Saturate_Value");
                saturateValueCurrent = Mathf.Lerp(saturateValueCurrent, saturateValueTarget, Time.deltaTime * 14f);
                waveMaterial.SetFloat("_Saturate_Value", saturateValueCurrent);

                wave1TilingCurrent = waveMaterial.GetVector("_Wave_1_Tiling");
                wave1TilingCurrent = Vector2.Lerp(wave1TilingCurrent, wave1TilingTarget, Time.deltaTime * 14f);
                waveMaterial.SetVector("_Wave_1_Tiling", wave1TilingCurrent);

                wave2TilingCurrent = waveMaterial.GetVector("_Wave_2_Tiling");
                wave2TilingCurrent = Vector2.Lerp(wave2TilingCurrent, wave2TilingTarget, Time.deltaTime * 14f);
                waveMaterial.SetVector("_Wave_2_Tiling", wave2TilingCurrent);


                noiseTilingCurrent = waveMaterial.GetVector("_Noise_Tiling");
                noiseTilingCurrent = Vector2.Lerp(noiseTilingCurrent, noiseTilingTarget, Time.deltaTime * 14f);
                waveMaterial.SetVector("_Noise_Tiling", noiseTilingCurrent);

                //        MouseScroll(_mousePosition);
#else
                //if (Input.GetMouseButtonDown(0))
                //{
                //    mousePosition = Camera.main.ScreenToWorldPoint(Input.mousePosition);
                //    mapCamera.transform.position = mousePosition;//  mapCamera.transform.position + ((-mapCamera.transform.position) + mapCameraPoint + (mousePosition * mapCamera.transform.GetWorldPositionZ() / Globals.CocosToUnity(18000)) * Time.deltaTime * mapZoomLerpValue);
                //    mapCamera.transform.position = new Vector3(mapCamera.transform.position.x, mapCamera.transform.position.y, -10);

                //}
                mapCamera.orthographicSize = Mathf.Lerp(mapCamera.orthographicSize, mapCameraToZ, Time.deltaTime * 8.0f);
                mapCamera.orthographicSize = Mathf.Clamp(mapCamera.orthographicSize, 1.5f, 6);
#endif

            }

            //worldScrollLayer.getScrollView().setContentSize(cocos2d::Size(Director::getInstance().getWinSize().width, Director::getInstance().getWinSize().height));
            //TODO Find rwhat get content size does
        }
    }

    private void OnEnable()
    {
        seq = DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() =>
        {
            //
            Globals.isOnIsland = false;
            GameData.instance.CalculateItemsAvailableInShop(GameData.instance.fileHandler.coins);
            GameData.instance.CalculateItemsAvailebleInSideKickShop(GameData.instance.fileHandler.coins);
            

            if (Globals.AllowBgMusic)
            {
                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.worldWhoosh, 0.25f);
                // AudioManager.instance.PlayMusic(Track.mainMenuMusic);
                AudioManager.instance.PlayMusic(7009);
            }
            DG.Tweening.Sequence seq2 = DOTween.Sequence().AppendInterval(0.75f).AppendCallback(() =>
            {
                //GamePadInstructions::create(GamePadInstructions::GPI_TYPE::ADVANCED, GamePadInstructions::GPI_POSITION::BOTTOM_RIGHT);
            }).Play();
        }).Play();

        inputActions.UI.Navigate.performed += OnNavigationButtonInput;
        inputActions.UI.Navigate.Enable();

        inputActions.UI.Submit.performed += OnSubmitButtonInput;
        inputActions.UI.Submit.Enable();

        inputActions.UI.Cancel.performed += OnBackButtonInput;
        inputActions.UI.Cancel.Enable();
    }

    private void OnEnterTransitionDidFinish()
    {

    }

    private void OnExit()
    {

    }

    private void OnDisable()
    {

        inputActions.UI.Navigate.performed -= OnNavigationButtonInput;
        inputActions.UI.Navigate.Enable();

        inputActions.UI.Submit.performed -= OnSubmitButtonInput;
        inputActions.UI.Submit.Enable();

        inputActions.UI.Cancel.performed -= OnBackButtonInput;
        inputActions.UI.Cancel.Enable();
    }

    private void CreateButtons()
    {

        pointerObject.state.SetAnimation(0, "idle", true);
        ///Mission Buttons///

        mapTrail.GetTrail().skeleton.UpdateWorldTransform();


        _bone = worldScrollLayer.GetMap().skeleton.FindBone("Atlantis");
        worldScrollLayer.GetMap().skeleton.UpdateWorldTransform();
        //newIslandPosition.setScale(420 / newIslandPosition.getContentSize().width, 400 / newIslandPosition.getContentSize().height);

        newIslandPosition = _bone.GetWorldPosition(worldScrollLayer.GetMap().transform);


        islandSpine.GetComponent<EventHandler>().action = () => { IslandTransitionEnter(newIslandPosition); };


        //backFromIslandButton = ui::Button::create("res/World/shopItemArrow.png");
        //    _backFromIslandButton.setVisible(false);
        //backFromIslandButton.setPosition(Point(winSize.width / 2, _backFromIslandButton.getContentSize().height / 2));

        backFromIslandButton.onClick.AddListener(() => { IslandTransitionExit(newIslandPosition); });
        MissionButtonController.canBePressed = true;
        for (int i = 1; i < TotalMissions+1; i++)
        {

            _bone = mapTrail.GetTrail().skeleton.FindBone("mission_" + i);
            float scaleVal = _bone.ScaleX;
            float flagScale = 1;
            //button = ui::Button::create("res/World/MarkX.png", "res/World/MarkX.png");
            //button.setCascadeOpacityEnabled(true);
            MissionButtonController flagButton;
            flagButton = Instantiate(missionButton);
            flagButton.missionNumber = i;
            buttonArray.Add(flagButton);
            flagButton.name = i.ToString();
            flagButton.skeletonAnimation.state.TimeScale = 0.5f + Random.value * 0.5f;
            if (i > 30)
            {
                flagButton.transform.parent = islandSpine.transform;
                //flagButton.gameObject.SetActive(false);
            }
            else
            {
                flagButton.transform.parent = mapTrail.GetTrail().transform;//-1

            }
            flagButton.skeletonAnimation.state.AddAnimation(0, "flag_idle", true);
            flagButton.flagObj.transform.localPosition = new Vector3(0, -0.43f, 0);
            flagButton.SetMissionStars();

            //TODO Replace missionsCompleted and missionUnlocked with filehandler values
            if (i > GameData.instance.fileHandler.missionsCompleted + 1)
            {
                flagButton.flagObj.SetActive(false);
                flagButton.GetComponent<Collider2D>().enabled = false;
            }
            else
            {
                if (i < GameData.instance.fileHandler.missionsCompleted + 1)
                {
                    flagButton.skeletonAnimation.state.SetAnimation(0, "flag_idle", true);
                    flagButton.flagObj.SetActive(true);
                    flagButton.GetComponent<Collider2D>().enabled = true;
                }
            }
            Vector2 bonePos = _bone.GetWorldPosition(mapTrail.GetTrail().transform);
            StartCoroutine(FlagCoroutine());
            IEnumerator FlagCoroutine()
            {
                if (GameData.instance.fileHandler.missionUnlock == 1 && Globals.showNextMission)
                {
                    if (i == GameData.instance.fileHandler.missionsCompleted + 1)
                    {
                        pointerObject.gameObject.SetActive(false);
                        flagButton.flagObj.SetActive(false);
                        //if (i > 30)
                        //{
                        //    flagButton.transform.position = smallIslandFlagLocation[33 - i].position;
                        //}
                        //else
                        //{
                        //    flagButton.transform.position = new Vector2(bonePos.x, bonePos.y + Globals.CocosToUnity(27));
                        //}
                        flagButton.transform.position = new Vector2(bonePos.x, bonePos.y + Globals.CocosToUnity(27));

                        pointerObject.transform.position = new Vector2(flagButton.transform.position.x, flagButton.transform.position.y - 0.33f);
                        pointerObject.transform.parent = flagButton.transform;
                        pointerObject.transform.SetScale(2.2f);
                        Vector3 flagInitPos = flagButton.flagObj.transform.position;
                        flagButton.flagObj.transform.position = new Vector2(flagButton.flagObj.transform.position.x, flagButton.flagObj.transform.position.y + 10);
                        yield return new WaitForSeconds(1.5f + transitionDelay + 0.2f * (_numberOfDots + 1));
                        flagButton.skeletonAnimation.state.SetAnimation(0, "flag", false);
                        flagButton.skeletonAnimation.state.AddAnimation(0, "flag_idle", false);
                        flagButton.skeletonAnimation.state.TimeScale = 1;
                        flagButton.flagObj.transform.DOMove(flagInitPos, 0.2f);
                        flagButton.skeletonAnimation.state.SetAnimation(0, "flag_idle", false);
                        flagButton.skeletonAnimation.state.AddAnimation(0, "flag_idle", true);
                        flagButton.flagObj.SetActive(true);

                        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.mapButtonActivate);

                        //Globals.PlaySound("res/Sounds/SFX/mapButtonActivate.mp3"); 
                        GameData.instance.fileHandler.missionUnlock = 0;
                        //GameData.instance.fileHandler.SaveData();
                        yield return new WaitForSeconds(0.1f);

                        yield return new WaitForSeconds(0.15f);
                        Camera.main.transform.DOShakePosition(0.5f, 0.1f, 10);
                        flagButton.GetComponent<Collider2D>().enabled = true;
                        pointerObject.gameObject.SetActive(true);
                    }
                    else
                    {
                        if (i == GameData.instance.fileHandler.missionsCompleted + 1)
                        {
                            flagButton.flagObj.SetActive(true);
                            flagButton.skeletonAnimation.state.SetAnimation(0, "flag_idle", true);
                            flagButton.GetComponent<Collider2D>().enabled = true;
                        }
                    }
                }
            }

            flagButton.transform.SetScale(flagScale);
            //if (i > 30)
            //{
            //    flagButton.transform.position = smallIslandFlagLocation[33 - i].position;
            //}
            //else
            //{
            //    flagButton.transform.position = new Vector2(bonePos.x, bonePos.y + Globals.CocosToUnity(27));
            //}
            flagButton.transform.position = new Vector2(bonePos.x, bonePos.y + Globals.CocosToUnity(27));
            flagButton.buttonListener = () =>
            {
#if UNITY_STANDALONE
                if (flagButton.missionNumber == GameData.instance.fileHandler.currentMission && sideMenuObj.IsOpen())
                {
                    return;
                }
#endif

                currentSelected = flagButton.missionNumber - 1;

                GameData.instance.fileHandler.currentMission = flagButton.missionNumber;
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                Globals.gameType = GameType.Training;
                //                    GameData.instance.fileHandler.saveData();
                if (flagButton.missionNumber > 30)
                {
                    pointerObject.transform.position = new Vector2(flagButton.transform.position.x, flagButton.transform.position.y - 0.33f / 4.16f);
                    pointerObject.transform.parent = flagButton.transform;
                    pointerObject.transform.SetScale(2.2f);
                }
                else
                {

                    pointerObject.transform.position = new Vector2(flagButton.transform.position.x, flagButton.transform.position.y - 0.33f);
                    pointerObject.transform.parent = flagButton.transform;
                    pointerObject.transform.SetScale(2.2f);
                }

#if UNITY_STANDALONE
                sideMenuObj.ShowMissionInfo();
                if (!Globals.isOnIsland)
                {
                    mapCameraToZ = 3.2f;
                    mapCameraPoint = new Vector3(flagButton.transform.position.x*2+4f, flagButton.transform.position.y*2,mapCameraPoint.z);
                }
#else
                pointerObject.skeleton.UpdateWorldTransform();
                sideMenuMobileObj.ShowMissionInfo();
#endif



            };



        }

        //SpineReader::createButtonsFromSpineData(_mapTrail.getTrail(), worldScrollLayer.getMap().getSkeleton());

    }

    private void FocusMission(bool attuned)
    {
        MissionButtonController button = buttonArray[GameData.instance.fileHandler.currentMission - 1];


        //    if(GameData.instance.fileHandler.missionsCompleted < TotalMissions)
        {
            Vector3 pos = button.transform.position;


            worldScrollLayer.ScrollToPosition(new Vector3(pos.x, pos.y,-10), attuned);

        }
        //    else
        //    {
        //        mapCameraToZ  = tempCameraHeight;
        //
        //    }
        return;
    }

    private void FocusButton(bool attuned)
    {
#if UNITY_STANDALONE
    return;
#endif
        if (currentSelected < 30)
        {
            MissionButtonController button = buttonArray[currentSelected];
            if (GameData.instance.fileHandler.missionsCompleted <= TotalMissions)//GameData.instance.fileHandler.missionsCompleted <= TotalMissions)
            {
                Vector2 pos = button.transform.position;
                //        worldScrollLayer.scrollToPosition(pos,attuned);
                worldScrollLayer.ScrollToPosition(new Vector3((pos.x-3<-4)?pos.x+3:pos.x-3, pos.y,-10), attuned);

            }
        }

        //    else
        //    {
        //        mapCameraToZ  = tempCameraHeight;
        //
        //    }


        return;
    }

    

    private void MouseScroll(Vector2 pos)
    {
        //if (mapCameraToZ < Constants.GameConstants.CAMERA_BUTTON_PRESS_HEIGHT + 20)
        //    return;


        //pos = pos + new Vector2(winSize.x / 2, winSize.y / 2);

        //if (pos.x < Constants.GameConstants.MAC_MOUSE_SCROLL_HEIGHT && worldScrollLayer.GetScrollView().getScrolledPercentBothDirection().x > 5)
        //{
        //    worldScrollLayer.getScrollView().jumpToPercentBothDirection(cocos2d::Point(worldScrollLayer.getScrollView().getScrolledPercentBothDirection().x - MAC_MOUSE_SCROLL_SPEED + pos.x / MAC_MOUSE_SCROLL_HEIGHT, worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y));
        //}

        //if (pos.y < MAC_MOUSE_SCROLL_HEIGHT && worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y < 95)
        //{
        //    worldScrollLayer.getScrollView().jumpToPercentBothDirection(cocos2d::Point(worldScrollLayer.getScrollView().getScrolledPercentBothDirection().x, worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y + MAC_MOUSE_SCROLL_SPEED - pos.y / MAC_MOUSE_SCROLL_HEIGHT));
        //}

        //if (pos.y > Director::getInstance().getWinSizeInPixels().height - MAC_MOUSE_SCROLL_HEIGHT && worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y > 5)
        //{
        //    worldScrollLayer.getScrollView().jumpToPercentBothDirection(cocos2d::Point(worldScrollLayer.getScrollView().getScrolledPercentBothDirection().x, worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y - MAC_MOUSE_SCROLL_SPEED + (winSize.height - pos.y) / MAC_MOUSE_SCROLL_HEIGHT));
        //}

        //if (pos.x > Director::getInstance().getWinSizeInPixels().width - MAC_MOUSE_SCROLL_HEIGHT && worldScrollLayer.getScrollView().getScrolledPercentBothDirection().x < 95)
        //{
        //    worldScrollLayer.getScrollView().jumpToPercentBothDirection(cocos2d::Point(worldScrollLayer.getScrollView().getScrolledPercentBothDirection().x + MAC_MOUSE_SCROLL_SPEED - (winSize.width - pos.x) / MAC_MOUSE_SCROLL_HEIGHT, worldScrollLayer.getScrollView().getScrolledPercentBothDirection().y));
        //}
    }

    private void ShowNewUpdatePopup()
    {

    }

    private void CreateEvents()
    {
        Observer.RegisterCustomEvent(gameObject, "entered_missionselect", () =>
        {
            pointerObject.gameObject.SetActive(true);
            pointerObject.transform.SetScale(5.0f);
            pointerObject.transform.DOScale(new Vector3(2.2f, 2.2f, 2.2f), 0.1f).SetEase(Ease.InOutBack);

        });
        Observer.RegisterCustomEvent(gameObject, "left_missionselect", () =>
        {

            pointerObject.gameObject.SetActive(false);

        });



        Observer.RegisterCustomEvent(gameObject, "SHOW_MISSION_INFO", () =>
        {
            seq = DOTween.Sequence();
            seq.AppendInterval(1f).AppendCallback(() => { sideMenuMobileObj.ShowMissionInfo(); });
            seq.Play();
        });

        Observer.RegisterCustomEvent(gameObject, "Exit_New_Island", () =>
        {

            IslandTransitionEnter(newIslandPosition);
            MissionButtonController button = buttonArray[29];

            pointerObject.transform.position = new Vector2(button.transform.position.x, button.transform.position.y - 0.33f);
            pointerObject.transform.parent = button.transform;
            pointerObject.transform.SetScale(2.2f);;
            FocusButton(true);
        });

        Observer.RegisterCustomEvent(gameObject, "FOCUS_MISSION", () =>
        {

            Globals.onMapMenu = false;
            if (GameData.instance.fileHandler.missionsCompleted > 29)
            {
                IslandTransitionEnter(newIslandPosition);
                Observer.DispatchCustomEvent("hide_back_to_HQ_button");
            }
            else
            {
#if UNITY_STANDALONE
                mapCameraToZ = 6;//tempCameraHeight + 20;
                mapCameraPoint.x = 0;//winSize.x / 2 + 270;
                mapCameraPoint.y = 0.3f;
                //worldScrollLayer.SlideToPos( mapCameraPoint);


#else

                FocusMission(true);

#endif
            }



            // if currenat unlocked greater thn 30, call island transition

        });

        Observer.RegisterCustomEvent(gameObject, "BACK_TO_MAP", () =>
        {
            if (Globals.isJoystickConnected)
            {
                if (buttonSelected)
                {
                    buttonSelected = buttonSelected.objectDefault;
                    if (buttonSelected.GetComponent<CustomButton>())
                    {
                        buttonSelected.GetComponent<CustomButton>().OnMouseEnter();
                    }
                    else if (buttonSelected.GetComponent<HQCustomButton>())
                    {
                        buttonSelected.GetComponent<HQCustomButton>().OnMouseEnter();
                    }
                }
            }
            Globals.onMapMenu = true;
#if UNITY_STANDALONE
            //mapCameraPoint = new Vector2(Globals.CocosToUnity(-200), Globals.CocosToUnity(500));
            sideMenuObj.CloseButtonCall();
            mapCameraPoint.x = -22;
            mapCameraPoint.y = -4.4f;
            mapCameraPoint.z = -20f;
            mapCameraToZ = 3.2f;// Constants.GameConstants.CAMERA_BUTTON_PRESS_HEIGHT + Globals.CocosToUnity(100);
            //worldScrollLayer.ScrollToPosition(new Vector2(Globals.CocosToUnity(-1100), Globals.CocosToUnity(500)), true);
            //worldScrollLayer.SlideToPos(mapCameraPoint);

#else
            //if (Globals.isJoystickConnected)//TODO Look into the Cocos COde
            //    {
            //        //mapCameraPoint = new Vector2(Globals.CocosToUnity(-200), Globals.CocosToUnity(500));
            //        sideMenuMobileObj.CloseButtonCall();
            //        mapCameraToZ = 4;
            //    }
            //else
            //    {
            //        //mapCameraPoint = new Vector2(Globals.CocosToUnity(-200), Globals.CocosToUnity(500));
            //        worldScrollLayer.ScrollToPosition(new Vector3(0, 0,-10), true);
            //    }
            mapCameraToZ = 4;
            worldScrollLayer.ScrollToPosition(new Vector3(-12, -2, -10), true);

            sideMenuMobileObj.CloseButtonCall();
#endif

        });

        Observer.RegisterCustomEvent(gameObject, "SHOW_CURRENT_MISSION_KIT", () =>
        {
            if (GameData.instance.fileHandler.missionsCompleted < 33)//GameData.instance.fileHandler.missionsCompleted < 33)
            {
                if (!isCurrentMissionKitVisible)
                {
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.mapButtonActivate);

                    //Globals.PlaySound("res/Sounds/SFX/mapButtonActivate.mp3", false, 1);

                    isCurrentMissionKitVisible = true;
                    MissionButtonController b = buttonArray[GameData.instance.fileHandler.missionsCompleted];//GameData.instance.fileHandler.missionsCompleted
                    float kitPos;
                    float pointerPos;
                    if (!Globals.isOnIsland)
                    {
                        kitPos = 1;
                        pointerPos = 0.33f;
                    }
                    else
                    {
                        kitPos = 1f / 4.16f;
                        pointerPos = 0.33f / 4.16f;
                    }
                    pointerObject.transform.parent = b.transform;
                    pointerObject.transform.SetScale(2.2f);
                    kitCurrentMission.transform.position = new Vector3(b.transform.position.x, b.transform.position.y + kitPos);
                    pointerObject.transform.position = new Vector3(b.transform.position.x, b.transform.position.y - pointerPos);
                    seq = DOTween.Sequence();
                    seq.AppendInterval(0.3f).Append(kitCurrentMission.transform.DOScale(new Vector3(0.7f, 0.7f, 0.7f), 0.25f).SetEase(Ease.OutElastic))
                    .Append(kitCurrentMission.GetComponentInChildren<SpriteRenderer>().DOFade(1, 0)).AppendCallback(() =>
                     {
                         isCurrentMissionKitVisible = true;
                     });
                    seq.Play();
                }

            }
        });
        Observer.RegisterCustomEvent(gameObject, "HIDE_CURRENT_MISSION_KIT", () =>
        {

            if (isCurrentMissionKitVisible)
            {

                isCurrentMissionKitVisible = false;
                seq = DOTween.Sequence();
                seq.AppendInterval(0.3f).Append(kitCurrentMission.transform.DOScale(Vector3.zero, 0).SetEase(Ease.OutElastic))
                .Append(kitCurrentMission.GetComponentInChildren<SpriteRenderer>().DOFade(0, 0.5f)).AppendCallback(() =>
                {
                });
                seq.Play();
            }

            
        });
    }

    private void CreateCustomEvents()
    {

    }

    private void CreateCurrentMissionKitIcon()
    {
        //Done in inspector
    }

    private void CreateNewIsland()
    {
        islandSpine.transform.SetScale(1 / 4.16f);
        islandSpine.SetOpacity(0);
        islandSpine.state.SetAnimation(5, "barrel", true);
        islandSpine.state.SetAnimation(6, "flag", true);
        islandSpine.state.SetAnimation(7, "water", true);
        islandSpine.state.SetAnimation(8, "waterfall", true);
        islandSpine.transform.position = newIslandPosition;
        //islandSpine.setCascadeOpacityEnabled(true);
    }

    private void CreateNavigationMenu()
    {
        
        mobileMenu.MapButton.defaultAction=() => {
            IslandTransitionExit(newIslandPosition);
            MissionButtonController button = buttonArray[29];
            Globals.mapMenuEnabledController = true;
            Globals.hudMenuEnabledController = false;

            pointerObject.transform.SetWorldPosition(button.transform.position.x, button.transform.position.y - 0.33f);// - button.getContentSize().height / 3));
            this.FocusButton(true);
        };
        mobileMenu.MapButton.gameObject.SetActive(false);
        //mobileMenu.MapButton.SetButtonActive(false);
    }

    private void CreateMissionPopupObject()
    {

#if UNITY_STANDALONE
    
    Vector2 pos = new Vector2(2421.930176f, 1560.670044f);
    sideMenuObj.addedCloseFunction = ()=>{
        
        if(!Globals.isOnIsland){
            mapCameraToZ = 6;//tempCameraHeight + 20;
            mapCameraPoint.x = 0;//winSize.x / 2 + 270;
            mapCameraPoint.y = 0.3f;
        }
        else{
            mapCameraToZ = 2f;// Constants.GameConstants.CAMERA_HEIGHT_ISLAND;
            
        }
        
        
    };
    //GamePad_Apple *ga = GamePad_Apple::create();TODO
    
    
#else
        //GamePad_Apple* ga = GamePad_Apple::create();TODO
        //sideMapMenuMobileObj.addChild(ga);
        sideMenuMobileObj.addedCloseFunction = ()=>
    {

            if (!Globals.isOnIsland)
            {
                mapCameraToZ = 4;
            }
            else
            {
                mapCameraToZ = 1.4f;
            }


        };

#endif
    }

    private void CreateAccelerometerSupport()
    {//TODO
//        Device::setAccelerometerEnabled(true);
//        auto accListener = EventListenerAcceleration::create([=](Acceleration * acceleration, Event *event){
//        float multiplier = 5;
//        if (isOnIsland)
//        {
//            multiplier = 0.5f;
//        }
//        value.x = acceleration.x * 670 * multiplier;
//        value.y = -acceleration.y * 375 * multiplier;


//        event.stopPropagation();
//    });
//getEventDispatcher().addEventListenerWithSceneGraphPriority(accListener, this);TODO
    }

    private void CreateMapCamera()
    {

    }

    private void ShowNextMissionOnMap()
    {
        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS || CC_TARGET_PLATFORM == CC_PLATFORM_TVOS)TODO
        //        mapCameraPoint = Point(-200, 500);
        //#endif



        if (GameData.instance.fileHandler.missionsCompleted < TotalMissions)
        {

            if (GameData.instance.fileHandler.missionUnlock == 1)
            {
                _numberOfDots = mapTrail.TrailRunFromTo(GameData.instance.fileHandler.missionsCompleted, GameData.instance.fileHandler.missionsCompleted + 1, true, islandSpine.transform, transitionDelay);//missionsCompleted, GameData.instance.fileHandler.missionsCompleted + 1, true, islandSpine, transitionDelay);
            }
            else
            {
                _numberOfDots = mapTrail.TrailRunFromTo(GameData.instance.fileHandler.missionsCompleted + 1, GameData.instance.fileHandler.missionsCompleted + 1, false, islandSpine.transform, transitionDelay);//GameData.instance.fileHandler.missionsCompleted + 1, GameData.instance.fileHandler.missionsCompleted + 1, false, islandSpine, transitionDelay);
            }
        }
        else
        {
            _numberOfDots = mapTrail.TrailRunFromTo(GameData.instance.fileHandler.missionsCompleted, GameData.instance.fileHandler.missionsCompleted, false, islandSpine.transform, transitionDelay);//GameData.instance.fileHandler.missionsCompleted, GameData.instance.fileHandler.missionsCompleted, false, islandSpine, transitionDelay);
        }


        if (GameData.instance.fileHandler.currentMission > 30)//GameData.instance.fileHandler.currentMission > 30)
        {
            IslandTransitionEnter(newIslandPosition);
        }
#if UNITY_WEBGL || UNITY_IOS

        mapCameraToZ = 4;
        worldScrollLayer.GetScrollView().JumpToTarget(new Vector3(0, 0, 0), 4f);
#else

        mapCameraPoint.x = 0;
        mapCameraPoint.y = 0.3f;
        mapCameraPoint.z = -20f;
        mapCameraToZ = 6;
#endif
        FocusButton(true);

        currentSelected = GameData.instance.fileHandler.currentMission;//GameData.instance.fileHandler.currentMission;
        seq = DOTween.Sequence();
        DOTween.Sequence().AppendInterval(5).AppendCallback(() =>
        {

            ShowNextMissionInfo();

        }).Play();
        Globals.showNextMission = false;
    }

    private void ShowHeadQuarters()
    {
        {
            if (GameData.instance.fileHandler.missionsCompleted < TotalMissions)  //GameData.instance.fileHandler.missionsCompleted < TotalMissions)
            {

                if (GameData.instance.fileHandler.missionUnlock == 1&& GameData.instance.fileHandler.missionsCompleted != 0) 
                {

                    mapTrail.TrailSetTo(GameData.instance.fileHandler.missionsCompleted + 1, islandSpine.transform);

                    seq = DOTween.Sequence();
                    seq.AppendInterval(transitionDelay).AppendCallback(() =>
                    {
                        #if UNITY_WEBGL || UNITY_IOS
                        mapCameraToZ = 4f;
#else
                        mapCameraToZ = 3.2f;
#endif
                        MissionButtonController button = buttonArray[GameData.instance.fileHandler.missionsCompleted - 1];
                        MissionButtonController button2 = buttonArray[GameData.instance.fileHandler.missionsCompleted];
                        if (GameData.instance.fileHandler.missionsCompleted < TotalMissions)
                        {
#if UNITY_STANDALONE
                            if (GameData.instance.fileHandler.currentMission > 30 && PlayerPrefs.GetInt("newIslandAnimationPlayed", 0) == 1)
                            {
                                worldScrollLayer.GetMap().skeleton.SetAttachment("Atlantis", null);
                                islandSpine.SetOpacity(1);
                            }


#endif


                            pointerObject.transform.SetWorldPosition(buttonArray[GameData.instance.fileHandler.missionsCompleted].transform.position.x,
                                buttonArray[GameData.instance.fileHandler.missionsCompleted].transform.position.y-0.33f);//buttonArray.at(GameData.instance.fileHandler.missionsCompleted).getPosition().x, buttonArray.at(GameData.instance.fileHandler.missionsCompleted).getPosition().y - button.getContentSize().height / 3));
                            currentSelected = button2.missionNumber - 1;

                        }


                    }).AppendInterval(0.35f).AppendCallback(() =>
                    {
                        if (GameData.instance.fileHandler.missionsCompleted < TotalMissions)
                        {
                            MissionButtonController button = buttonArray[GameData.instance.fileHandler.missionsCompleted];
                            if (button == null)
                                return;
                            GameData.instance.fileHandler.currentMission = button.missionNumber; 

                            Globals.gameType = GameType.Training;
                            //GameData.instance.fileHandler.saveData();TODO
                            pointerObject.transform.position = new Vector2(button.transform.position.x, button.transform.position.y-0.33f);// - button.getContentSize().height / 3));



                            currentSelected = button.missionNumber - 1;


                            //if (GameData.instance.fileHandler.currentMission > 30 && PlayerPrefs.GetInt("newIslandAnimationPlayed", 0) == 1)
                            //{
                            //    IslandTransitionEnter(newIslandPosition);
                            //}
                        }
                    });
                    seq.Play();


                }
                else
                {
                    if (GameData.instance.fileHandler.missionsCompleted < TotalMissions)
                    {

                        mapTrail.TrailSetTo(GameData.instance.fileHandler.missionsCompleted + 1, islandSpine.transform);


                        seq = DOTween.Sequence();
                        seq.AppendInterval(transitionDelay).AppendCallback(()=>
                        {
#if UNITY_WEBGL || UNITY_IOS
                            mapCameraToZ = 4f;
#else
                        mapCameraToZ = 3.2f;
#endif
                            MissionButtonController button = buttonArray[GameData.instance.fileHandler.missionsCompleted];
#if UNITY_STANDALONE
                            if (GameData.instance.fileHandler.currentMission > 30 && PlayerPrefs.GetInt("newIslandAnimationPlayed", 0) == 1) 
                            {
                                worldScrollLayer.GetMap().skeleton.SetAttachment("Atlantis", null);
                                islandSpine.SetOpacity(1);
                            }
#endif


                            pointerObject.transform.SetWorldPosition(button.transform.position.x, button.transform.position.y-0.33f);// - button.getContentSize().height / 3))TODO;
                            currentSelected = button.missionNumber - 1;//button.getTag() - 1;


                        }).AppendInterval(0.35f).AppendCallback(()=>
                        {

                            MissionButtonController button = buttonArray[GameData.instance.fileHandler.missionsCompleted];//buttonArray.at(GameData.instance.fileHandler.missionsCompleted);
                            GameData.instance.fileHandler.currentMission = button.missionNumber;//GameData.instance.fileHandler.currentMission = button.getTag();

                            Globals.gameType = GameType.Training;
                            //GameData.instance.fileHandler.saveData();TODO
                            pointerObject.transform.SetWorldPosition(button.transform.position.x, button.transform.position.y-0.33f);//- button.getContentSize().height / 3));TODO



                            currentSelected = button.missionNumber - 1;//button.getTag() - 1;



                        });
                        seq.Play();
                    }
                    else
                    {
                        mapCameraToZ = tempCameraHeight;
                    }
                }
            }
        }
    }

    private void NewUpdateCall()
    {

        seq = DOTween.Sequence();

        seq.AppendInterval(2).AppendCallback(() =>
        {

            if (PlayerPrefs.GetInt("firstTimeOnUpdate", 1) == 1)
            {
                Observer.DispatchCustomEvent("SHOW_SETTINGS_ACCESSIBILITY_MENU");
            }
        });
        Globals.isAssistMode =  PlayerPrefs.GetInt("isAssistMode") == 1;
        Globals.screenShakeEnabled = PlayerPrefs.GetInt("screenShakeEnabled") == 1;
    }

    private void SetCameraPoints()
    {
#if UNITY_STANDALONE


        tempCameraHeight = mapCameraToZ;
        mapCameraPoint = new Vector3(-22, -4.4f, -20);
        //mapCameraPoint = new Vector2(Globals.CocosToUnity(-200), Globals.CocosToUnity(450));
        mapCameraToZ = tempCameraHeight + 600;
        seq = DOTween.Sequence();
        seq.AppendInterval(0.5f).AppendCallback(() => {
            Observer.DispatchCustomEvent("on_focus_button");
        });
        seq.Play();
#else
        mapCameraToZ = 4;
        worldScrollLayer.GetScrollView().JumpToTarget(new Vector3(-12, -1.8f, -10));
        seq = DOTween.Sequence();
        seq.AppendInterval(0.5f).AppendCallback(() => {
            Observer.DispatchCustomEvent("on_focus_button");
        });
        seq.Play();
#endif
    }

    private void ReportAchievement()
    {
        //if (GameData.instance.fileHandler.missionsCompleted == 0)TODO
        //{
        //    GameData.instance.fileHandler.unlockAchievement("com.werplay.explottens.arcadeFTUX");
        //    GameData.instance.fileHandler.unlockAchievement("grp.com.werplay.explottens.arcadeFTUX");
        //}
    }

    private void LandOnMap()
    {
        seq = DOTween.Sequence();
        seq.AppendInterval(0.15f).AppendCallback(()=> {
            Observer.DispatchCustomEvent("show_back_to_HQ_button");
            Observer.DispatchCustomEvent("hide_take_off_button");
            Observer.DispatchCustomEvent("FOCUS_MISSION");
            Globals.onMapController = true;
            Globals.onHQController = false;
            Globals.onMap = true;
        });
        seq.Play();
    }

    private void LandOnHQ()
    {
        CreateButtons();
        if (Globals.isJoystickConnected)
        {
            buttonSelected = buttonSelected.objectDefault;
            if (buttonSelected.GetComponent<CustomButton>())
            {
                buttonSelected.GetComponent<CustomButton>().OnMouseEnter();
            }
            else if (buttonSelected.GetComponent<HQCustomButton>())
            {
                buttonSelected.GetComponent<HQCustomButton>().OnMouseEnter();
            }
        }
        Globals.hqMenuEnabledController = true;
        Globals.hudMenuEnabledController = false;
        Globals.onHQController = true;
        Globals.onMapController = false;
        SetCameraPoints();
        ShowHeadQuarters();
    }

    private void LandOnNextMission()
    {
        CreateButtons();
        ShowNextMissionOnMap();
    }

    private void IslandTransitionEnter(Vector2 island)
    {
        if (PlayerPrefs.GetInt("IsIslandUnlocked", 0)==0)
        {
            return;
        }

        if (Globals.isOnIsland || _islandTransition)
        {
            return;
        }
        Globals.disableScroll = true;
        Globals.isOnIsland = true;

        Observer.DispatchCustomEvent("hide_take_off_button");
        Observer.DispatchCustomEvent("hide_back_to_HQ_button");
        kitCurrentMission.transform.parent = islandSpine.transform;
            Observer.DispatchCustomEvent("SHOW_CURRENT_MISSION_KIT");


        // hide back to hq button
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        //Globals.PlaySound(Constants.AudioClips. SOUND_BUTTON_TAP);
        currentSelected = GameData.instance.fileHandler.missionsCompleted;
        currentSelected = Mathf.Clamp(currentSelected, 0, TotalMissions - 1);
        MissionButtonController button = buttonArray[currentSelected];
        pointerObject.transform.position = new Vector2(button.transform.position.x, button.transform.position.y -0.33f/4.16f);// button.GetComponent<SpriteRenderer>().size.y / 3);
        FocusButton(true);
        _islandTransition = true;
        islandSpine.timeScale = 1;
        if (worldScrollLayer)
        {
            worldScrollLayer.GetScrollView().SetTouchEnabled(false);
        }
        //worldScrollLayer.getScrollView().stopScroll();
        //worldScrollLayer.getScrollView().stopOverallScroll();

#if UNITY_STANDALONE
    sideMenuObj.CloseButtonCall();
#else
        sideMenuMobileObj.CloseButtonCall();
#endif
        mobileMenu.MapButton.gameObject.SetActive(true);
        mobileMenu.MapButton.SetInteractable(true);
        mobileMenu.UpdateActiveButtons();
#if UNITY_WEBGL || UNITY_IOS

        mapCameraToZ = 1.4f;
        worldScrollLayer.GetScrollView().JumpToTarget(new Vector3(1f, -2.5f, -10), 1.7f);
#else
        mapCameraToZ = 1.4f;
        mapCameraPoint = new Vector3(island.x+1f , island.y - Globals.CocosToUnity(250),-20);
#endif
        seq = DOTween.Sequence();

        waterOpacityTarget = 0.8f;
        saturateValueTarget = 0.8f;
        wave1TilingTarget = new Vector2(0.5f, 1f);
        wave2TilingTarget = new Vector2(1f, 2f);
        noiseTilingTarget = new Vector2(2f, 2f);
        seq.AppendInterval(0.05f).AppendCallback(() =>
        {
            worldScrollLayer.GetMap().skeleton.SetAttachment("Atlantis", null);
        });
        seq.Play();
        //newIslandPosition.SetSwallowTouches(false); TODO
        if (cloudTransition)
        {
            cloudTransition.gameObject.SetActive(true);
            cloudTransition.AnimationState.SetAnimation(0, "MenuToMap4", false);
            seq = DOTween.Sequence();
            seq.AppendInterval(1.0f).AppendCallback(() =>
            {
                cloudTransition.gameObject.SetActive(false);
            });
        }
        //Debug.Log("SCROLL POSITION x:%f y:%f", worldScrollLayer.getScrollView().getScrolledPercentVertical(), worldScrollLayer.getScrollView().getScrolledPercentHorizontal());

        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.worldWhoosh,0.25f);

        //Globals.PlaySound("res/Sounds/SFX/test/worldWhoosh.mp3", false, 0.25f);

        worldScrollLayer.mapSeq.Kill();
        worldScrollLayer.mapSeq = DOTween.Sequence();
        worldScrollLayer.mapSeq.AppendInterval(0.1f).AppendCallback(()=> { worldMapMat.DOFade(0, 0.15f).SetEase(Ease.Linear, 0.25f,1); });
        worldScrollLayer.mapSeq.Play();
        worldScrollLayer.waterSeq.Kill();
        worldScrollLayer.waterSeq = DOTween.Sequence();
        worldScrollLayer.waterSeq.Append(worldScrollLayer.GetBackgroundWater().transform.DOScale(Vector3.one * 0.4f, 0.25f)).AppendCallback(() =>
        {
            _islandTransition = false;
            DOTween.Sequence().Append(worldScrollLayer.GetBackgroundWater().transform.DOScale(new Vector3(0.42f, 0.42f, 0.42f), 10).SetEase(Ease.InOutSine)).Append(worldScrollLayer.GetBackgroundWater().transform.DOScale(new Vector3(0.4f, 0.4f, 0.4f), 10).SetEase(Ease.InOutSine)).SetLoops(-1).Play();

        }).Play();
        


        foreach (MissionButtonController node in buttonArray)
        {
            if (node.missionNumber <= 30)
            {
               node.GetComponent<SpriteRenderer>().DOFade(0, 0.15f).SetEase(Ease.Linear, 0.25f);
                node.skeletonAnimation.DOFade(0, 0.15f, 0.25f);
                //              node.setEnabled(false);
            }
            else
            {
                node.GetComponent<SpriteRenderer>().DOFade(1, 0.5f).SetEase(Ease.Linear, 0.25f);
                node.skeletonAnimation.DOFade(1, 0.5f, 0.25f);
            }

        }
        mapTrail.FadeBigIslandTrail(0, 0.15f, 0.25f);
        mapTrail.FadeSmallIslandTrail(1, 0.5f, 0.25f);
        foreach (Material m in islandMat)
        {
            m.DOFade(1, 0.15f).SetEase(Ease.Linear,0.25f,1f);
        }
        //mapTrail.GetComponent<>runAction(EaseIn::create(FadeOut::create(0.5f), 0.25f));  TODO

        cloudBR.rectTransform.DOKill();
        cloudTR.rectTransform.DOKill();
        cloudBL.rectTransform.DOKill();
        cloudTL.rectTransform.DOKill();

        cloudBR.rectTransform.DOScale(new Vector3(1.5f,1.5f,1.5f),0.5f).SetEase(Ease.Linear,0.25f);
        cloudTR.rectTransform.DOScale(new Vector3(1.5f, 1.5f, 1.5f), 0.5f).SetEase(Ease.Linear, 0.25f);
        cloudBL.rectTransform.DOScale(new Vector3(1.5f, 1.5f, 1.5f), 0.5f).SetEase(Ease.Linear, 0.25f);
        cloudTL.rectTransform.DOScale(new Vector3(1.5f, 1.5f, 1.5f), 0.5f).SetEase(Ease.Linear, 0.25f);
        islandSpine.state.SetAnimation(0, "entry", false);
    }

    private void IslandTransitionExit(Vector3 island)
    {
        if (PlayerPrefs.GetInt("IsIslandUnlocked", 0)==0)
        {
            return;
        }

        if (!Globals.isOnIsland || _islandTransition)
        {
            return;
        }
        Globals.disableScroll = false;
        if (!Globals.isJoystickConnected)
        {
            Observer.DispatchCustomEvent("show_take_off_button");
        }
        Observer.DispatchCustomEvent("show_back_to_HQ_button");
        Observer.DispatchCustomEvent("HIDE_CURRENT_MISSION_KIT");

        currentSelected = 29;
        MissionButtonController button = buttonArray[currentSelected];//.at(currentSelected);
        pointerObject.transform.position = new Vector2(button.transform.position.x, button.transform.position.y - 0.33f);// - button.GetComponent<SpriteRenderer>().size.y / 3);

        _islandTransition = true;
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.worldWhoosh,0.25f);
        //Globals.PlaySound("res/Sounds/SFX/test/worldWhoosh.mp3", false, 0.25f);

        kitCurrentMission.transform.parent = mapTrail.transform;
        worldScrollLayer.waterSeq.Kill();
        if (worldScrollLayer)
        {
#if UNITY_IOS || UNITY_WEBGL
            worldScrollLayer.GetScrollView().SetTouchEnabled(true);
#endif
        }
        foreach (Material m in islandMat)
        {
            m.DOFade(0, 0.05f);
        }
        //islandSpine.DOFade(0, 0.05f);
        seq = DOTween.Sequence();
        seq.AppendInterval(0.5f).AppendCallback(() =>
        {
            Globals.isOnIsland = false;
            _islandTransition = false;

            islandSpine.timeScale = 0;
            worldScrollLayer.PlayWaterAnim();

        });

        foreach (MissionButtonController node in buttonArray)
        {
            if (node.missionNumber <= 30)
            {
                node.GetComponent<SpriteRenderer>().DOFade(1, 0.5f).SetEase(Ease.Linear, 0.25f);
                node.skeletonAnimation.DOFade(1, 0.5f, 0.25f);
                //              node.setEnabled(false);
            }
            else
            {
                node.GetComponent<SpriteRenderer>().DOFade(0, 0.15f).SetEase(Ease.Linear, 0.25f);
                node.skeletonAnimation.DOFade(0, 0.15f, 0.25f);
            }
        }
//#if UNITY_STANDALONE
//        mobileMenu.MapButton.gameObject.SetActive(false);
//    mobileMenu.MapButton.SetInteractable(false);
//    mobileMenu.UpdateActiveButtons();
//#else
//        mobileMenu.MapButton.gameObject.SetActive(false);
//        mobileMenu.MapButton.interactable = false;
//        mobileMenu.UpdateActiveButtons();

//#endif
        mobileMenu.MapButton.gameObject.SetActive(false);
        mobileMenu.MapButton.SetInteractable(false);
        mobileMenu.UpdateActiveButtons();
        // worldScrollLayer.GetMap().DOFade(1, 0.15f, 0.25f);

        worldMapMat.DOFade(1, 0.15f).SetEase(Ease.Linear, 0.25f, 1);
        worldScrollLayer.GetBackgroundWater().transform.DOScale(Vector3.one,0.15f);
        //mapTrail.GetTrail().DOFade(1, 0.15f, 0.25f);
        mapTrail.FadeBigIslandTrail(1, 0.15f, 0.25f);
        mapTrail.FadeSmallIslandTrail(0, 0.15f, 0.25f);
        //newIslandPosition.setSwallowTouches(true); TODO
        worldScrollLayer.GetMap().skeleton.SetAttachment( "Atlantis", "New_island4");
        islandSpine.state.SetAnimation(0, "exit", false);


        cloudBR.rectTransform.DOKill();
        cloudTR.rectTransform.DOKill();
        cloudBL.rectTransform.DOKill();
        cloudTL.rectTransform.DOKill();
        cloudBR.rectTransform.DOScale(Vector3.one, 0.15f).SetEase(Ease.Linear, 0.25f);
        cloudTR.rectTransform.DOScale(Vector3.one, 0.15f).SetEase(Ease.Linear, 0.25f);
        cloudBL.rectTransform.DOScale(Vector3.one, 0.15f).SetEase(Ease.Linear, 0.25f);
        cloudTL.rectTransform.DOScale(Vector3.one, 0.15f).SetEase(Ease.Linear, 0.25f);


#if UNITY_STANDALONE
    mapCameraToZ = 6f;
    mapCameraPoint.x = 0 ;
    mapCameraPoint.y = 0.3f;
        mapCameraPoint.z = -20;
        waterOpacityTarget = 0.3f;
        saturateValueTarget = 0.6f;
        wave1TilingTarget = new Vector2(0.25f, 0.5f);
        wave2TilingTarget = new Vector2(0.5f, 1f);
        noiseTilingTarget = new Vector2(1f, 1f);
#else

        mapCameraToZ = 4f;
        worldScrollLayer.GetScrollView().JumpToTarget(new Vector3(0, 0, -10), 4f);
#endif

    }

    private void ShowNextMissionInfo()
    {
        {

            MissionButtonController button = buttonArray[GameData.instance.fileHandler.currentMission - 1];
#if UNITY_STANDALONE
        
        if(button.name == GameData.instance.fileHandler.currentMission.ToString() && sideMenuObj.IsOpen())
        {
            return;
        }
#endif
            //if (touchMovedCounter > 10)
            //{
            //    touchMovedCounter = 0;
            //    return;
            //}
            //touchMovedCounter = 0;

            GameData.instance.fileHandler.currentMission = int.Parse(button.name);
            Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            Globals.gameType = GameType.Training;
            button.buttonListener?.Invoke();
        }
    }

    private void OnDestroy()
    {
        DOTween.KillAll();
    }

    private void OnNavigationButtonInput(InputAction.CallbackContext obj)
    {

            Vector2 val = obj.ReadValue<Vector2>();


        if (sideMenuObj)
        {

        }
        if (sideMenuMobileObj)
        {

        }

        if (Globals.onHQController)
        {
            if (val.y > 0)
                MoveToObjTop();
            else if (val.y < 0)
                MoveToObjBottom();
            if (val.x > 0)
                MoveToObjRight();
            else if (val.x < 0)
                MoveToObjLeft();
        }
        else
        {
            //if (val.y > 0)
            //    MoveUp();
            //else if (val.y < 0)
            //    MoveDown();
            if (val.x > 0)
                MoveToNextFlag();
            else if (val.x < 0)
                MoveToPreviousFlag();
        }
    }

    private void MoveToObjLeft()
    {
        if (buttonSelected)
        {
            if (buttonSelected.objectLeft)
            {
                ResetButtonState();
                buttonSelected = buttonSelected.objectLeft;
                SetButtonState();
                
            }
        }
    }

    private void MoveToObjRight()
    {
        if (buttonSelected)
        {
            if (buttonSelected.objectRight)
            {
                ResetButtonState();
                buttonSelected = buttonSelected.objectRight;
                SetButtonState();
            }
        }
    }

    private void MoveToObjTop()
    {
        if (buttonSelected)
        {
            if (buttonSelected.objectTop)
            {
                ResetButtonState();
                buttonSelected = buttonSelected.objectTop;
                SetButtonState();
            }
        }
    }

    private void MoveToObjBottom()
    {
        if (buttonSelected)
        {
            if (buttonSelected.objectBottom)
            {
                ResetButtonState();
                buttonSelected = buttonSelected.objectBottom;
                SetButtonState();
            }
        }
    }

    private void ResetButtonState()
    {
        if (buttonSelected.GetComponent<HQCustomButton>())
        {
            buttonSelected.GetComponent<HQCustomButton>().OnMouseExit();
        }
        else if (buttonSelected.GetComponent<CustomButton>())
        {
            buttonSelected.GetComponent<CustomButton>().OnMouseExit();
        }
    }

    private void SetButtonState()
    {
        if (buttonSelected.GetComponent<CustomButton>())
        {
            buttonSelected.GetComponent<CustomButton>().OnMouseEnter();
        }
        else if (buttonSelected.GetComponent<HQCustomButton>())
        {
            buttonSelected.GetComponent<HQCustomButton>().OnMouseEnter();
        }
    }

    private void MoveToNextFlag()
    {
        if (currentSelected < GameData.instance.fileHandler.missionsCompleted)
        {

            buttonArray[currentSelected].transform.SetScale(1f);
            currentSelected++;
            if (currentSelected > 29)
            {
                IslandTransitionEnter(newIslandPosition);
                //Observer.DispatchCustomEvent("hide_back_to_HQ_button");
            }
            buttonArray[currentSelected].transform.SetScale(1.1f);


            pointerObject.transform.position = new Vector2(buttonArray[currentSelected].transform.position.x, buttonArray[currentSelected].transform.position.y - 0.33f);
            pointerObject.transform.parent = buttonArray[currentSelected].transform;
            pointerObject.transform.SetScale(2.2f);
            pointerObject.gameObject.SetActive(true);
        }
    }

    private void MoveToPreviousFlag()
    {
        if (currentSelected > 0)
        {

            buttonArray[currentSelected].transform.SetScale(1f);
            currentSelected--;
            if (currentSelected < 30)
            {
                mobileMenu.MapButton.defaultAction?.Invoke();
            }
            //buttonArray[currentSelected].buttonListener?.Invoke();

            buttonArray[currentSelected].transform.SetScale(1.1f);


            pointerObject.transform.position = new Vector2(buttonArray[currentSelected].transform.position.x, buttonArray[currentSelected].transform.position.y - 0.33f);
            pointerObject.transform.parent = buttonArray[currentSelected].transform;
            pointerObject.transform.SetScale(2.2f);
            pointerObject.gameObject.SetActive(true);
        }
    }

    private void OnBackButtonInput(InputAction.CallbackContext obj)
    {
        if (Globals.onHQController)
        {
            buttonSelected = homeButton.GetComponent<WorldMapNavController>();
            homeButton.OnMouseEnter();
        }
        else
        {
#if UNITY_WEBGL || UNITY_IOS
            if (sideMenuMobileObj.IsOpen())
            {
                sideMenuMobileObj.CloseButtonCall();
            }
            else
            {
                if (currentSelected > 29)
                {
                    mobileMenu.MapButton.defaultAction?.Invoke();
                }
                else
                {
                    mobileMenu.BackToHQCall();
                }
            }
#else
            if (sideMenuObj.IsOpen())
            {
                sideMenuObj.CloseButtonCall();
            }
            else
            {
                if (currentSelected > 29)
                {
                    mobileMenu.MapButton.defaultAction?.Invoke();
                }
                else
                {
                    mobileMenu.BackToHQCall();
                }
            }
#endif
        }
    }

    private void OnSubmitButtonInput(InputAction.CallbackContext obj)
    {
        if (Globals.onHQController)
        {
            if (buttonSelected)
            {
                if (buttonSelected.GetComponent<CustomButton>())
                {
                    buttonSelected.GetComponent<CustomButton>().Submit();
                }
                else if (buttonSelected.GetComponent<HQCustomButton>())
                {
                    buttonSelected.GetComponent<HQCustomButton>().OnMouseUp();
                }
            }
        }
        else
        {
#if UNITY_WEBGL || UNITY_IOS
            if (sideMenuMobileObj.IsOpen())
            {
                sideMenuMobileObj.OnPlayButtonCall();
            }
            else
            {
                buttonArray[currentSelected].buttonListener?.Invoke();
            }
#else
            if (sideMenuObj.IsOpen())
            {
                sideMenuObj.OnPlayButtonCall();
            }
            else
            {
                buttonArray[currentSelected].buttonListener?.Invoke();
            }
#endif
        }
    }

}
