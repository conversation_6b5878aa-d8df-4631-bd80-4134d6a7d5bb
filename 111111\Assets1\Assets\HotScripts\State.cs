﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public abstract class State
{
    /**
     *  Called when entering state
     *
     *  @param previousState previous state or null if this is the first state
     */
    public virtual void DidEnterWithPreviousState(State previousState) { }
    /**
     *  Called every frame by state machine
     *
     *  @param delta time
     */
    public virtual void UpdateState() { }
    /**
     *  Checks if next state is valid for transition
     *
     *  @param state next state
     *
     *  @return true if valid, false otherwise
     */
    public virtual bool IsValidNextState(State state) { return false; }
    /**
     *  Called when exiting current state
     *
     *  @param nextState next state
     */
    public virtual void WillExitWithNextState(State nextState) { }

    public virtual string GetStateType() { return ""; }

    /**
     *  Get state machine
     *
     *  @return state machine
     */
    public StateMachine GetStateMachine()
    {
        return stateMachine;
    }

    /**
     *  Set State machine, this will be set when state has been added to state machine
     *
     *  @param stateMachine parent state machine
     */
    public void SetStateMachine(StateMachine sm)
    {
        stateMachine = sm;
    }

    protected StateMachine stateMachine;
}
