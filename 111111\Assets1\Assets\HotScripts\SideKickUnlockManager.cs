using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using Spine.Unity;
using TMPro;
using UnityEngine.SceneManagement;
public class SideKickUnlockManager : MonoBehaviour
{

    [SerializeField] private Image unlockImage;
    [SerializeField] private Sprite[] sideKickSprite;
    [SerializeField] private SkeletonGraphic lockImage;
    [SerializeField] private SkeletonGraphic lightsEffect;
    [SerializeField] private TextMeshProUGUI heading;
    [SerializeField] private TextMeshProUGUI description;
    [SerializeField] private Image blackOverlay;
    [SerializeField] private Image overLay;
    int sideKickToUnlock =0;
    bool allowTouch;

    private string tweenId;
    private string schedulerId;
    private void Awake()
    {
        tweenId = "SKU" + GetInstanceID();
        schedulerId = "SKU" + GetInstanceID();
    }


    public void Show()
    {

        sideKickToUnlock = GameData.instance.fileHandler.sideKickUnlocked;
        if (sideKickToUnlock >= GameManager.instance.missionManager.isUnlockSideKick)
        {
            return;
        }
        gameObject.SetActive(true);
        Init();
    }
    private void Init()
    {

#if UNITY_STANDALONE
{
            //TODO
    //GamePad * node = GamePad::create();
    //this.addChild(node);
}
#endif
        allowTouch = false;

        


        sideKickToUnlock = GameManager.instance.missionManager.isUnlockSideKick;
        GameData.instance.fileHandler.sideKickUnlocked = sideKickToUnlock;

        //TODO Achievements
        //GameData.instance.fileHandler.unlockAchievement("grp.com.werplay.explottens.UnlockSK" + to_string(sideKickToUnlock));
        //GameData.instance.fileHandler.unlockAchievement("com.werplay.explottens.UnlockSK" + to_string(sideKickToUnlock));


        lockImage.transform.SetScale(0);
        lockImage.AnimationState.SetAnimation(0, "idle", true);
        DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).Append(lockImage.transform.DOScale(Vector3.one, 0.15f)).AppendCallback(() => { lockImage.AnimationState.SetAnimation(0, "unlock", false); }).Play();
        
       string imageString = ((GameData.instance.GetShop()["Category5"]as PList)["Gun" + sideKickToUnlock] as PList)["ImagePath"] as string;
        foreach (Sprite s in sideKickSprite)
        {
            if (s.name == imageString)
            {
                unlockImage.sprite = s;
            }
        }
        unlockImage.gameObject.SetActive(false);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(2f).AppendCallback(() => { unlockImage.gameObject.SetActive(true); allowTouch = true; }).Play();
        DOTween.Sequence().SetId(tweenId).Append(unlockImage.transform.DOScale(Vector3.one * 1.05f, 1f).SetEase(Ease.InOutSine)).Append(unlockImage.transform.DOScale(Vector3.one, 1f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();


        lightsEffect.AnimationState.TimeScale = 0.5f;
        lightsEffect.transform.SetScale(1);
        lightsEffect.AnimationState.SetAnimation(0, "levelUp3", true);
        DOTween.Sequence().SetId(tweenId).Append(lightsEffect.transform.DOScale(Vector3.one * 2.5f, 1f)).Play();
        DOTween.Sequence().SetId(tweenId).Append(heading.DOFade(1, 0.5f)).Play();
        DOTween.Sequence().SetId(tweenId).Append(description.DOFade(1, 0.5f)).Play();

        string sk = "Gun"+sideKickToUnlock;
        //  GameLabel *sideKickName  = GameLabel::create(GameData::getInstance().getShop().at("Category5").asValueMap().at(sk).asValueMap().at("Label").asString(), 55);
        description.text = ((GameData.instance.GetTextData(Constants.GAME_TEXT.SHOP_DATA)["Category5"]as PList)[sk] as PList)["Label"]  as string;
        heading.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.UNLOCK_SCENE)["sideKickUnlockedTitle"] as string;
        
       Globals.StopAllSounds();
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.sidekickUnlock);
        //Globals.PlaySound("res/Sounds/SFX/sidekickUnlock.mp3");

        GameData.instance.fileHandler.showSkUnlockAnimation = 1;

        if (GameData.instance.fileHandler.currentMission != 30)
        {

            DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(1, 0.5f)).AppendCallback(() =>
            {
#if !UNITY_STANDALONE
//TODO Ask Bilal Bhai
                //Shared::pauseRecursive(Director::getInstance()->getRunningScene(), true);
                //Shared::pauseRecursive(this, false);
#endif
            }).Play();

        }

    }

    private void Update()
    {
        CheckInput();
    }

    private void CheckInput()
    {

        if (UnityEngine.Input.GetKeyDown(KeyCode.Escape) || UnityEngine.Input.GetKeyDown(KeyCode.Return) || UnityEngine.Input.GetMouseButtonUp(0))
        {
            if (allowTouch)
                ExitCallback();
        }
        //};
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);

        //GamePad_Apple* ga = GamePad_Apple::create();
        //this.addChild(ga);
        //#if Desktop

        //    auto mouseListener = EventListenerMouse::create();
        //    mouseListener.onMouseMove = [=](EventMouse * event){

        //        event.stopPropagation();
        //    };

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
        //#endif
    }
    private void ExitCallback()
    {

        if (sideKickToUnlock == 1)
        {
            //这里是跳转到商店，临时改成回到主场景
            //SceneManager.LoadScene(3);
            int xpReceivedFromMission = 0;
            if (Globals.gameModeType == GamePlayMode.Easy)
            {

                xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp/2;
            }
            if (Globals.gameModeType == GamePlayMode.Medium)
            {
                xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp;

            }
            if (Globals.gameModeType == GamePlayMode.Hard)
            {
                xpReceivedFromMission = GameManager.instance.missionManager.rewardInXp * 2;

            }
            string coinAndExp = LuaToCshapeManager.Instance.BattleAddCoin.ToString() + "|" + xpReceivedFromMission.ToString() + "|" + ((int)Globals.gameModeType + 1).ToString();
            LuaManager.Instance.RunLuaFunction<int, string>("BattleManager.BattleEnd", 1, coinAndExp);
        }
        else
        {
#if UNITY_STANDALONE
            //Shared::pauseRecursive(this.getParent().getParent(), false);
#else
        //Shared::pauseRecursive(this.getParent(), false);

#endif
            DOTween.Sequence().SetId(tweenId).Append(overLay.DOFade(1, 0.5f)).AppendCallback(() =>
            {
                gameObject.SetActive(false);
            }).Play();
        }

    }




}
