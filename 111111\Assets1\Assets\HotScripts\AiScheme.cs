using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;

public class AiScheme : Singleton<AiScheme>
{
    private Ai _data;
    private Dictionary<int, int> _idIndexMap;

    public bool Load()
    {
        int schemeIndex = (int)SchemeType.Ai;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            MemoryStream ms = new MemoryStream(HotResManager.ReadPb(pbFileName));
            _data = Serializer.Deserialize<Ai>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].AiId] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;

    }
    public Ai.Item GetItem(int id)
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load();
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            throw new Exception("id dont exist");
        }

    }
}

