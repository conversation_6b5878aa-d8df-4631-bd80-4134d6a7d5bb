using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class JellyFish : Enemy
{
    [SerializeField] private GameObject blast;
    [SerializeField] private Sprite bulletSprite;

    private const float detonationDistance = 2.5f;


    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "JF"+GetInstanceID().ToString();
        schedulerId = "SchJF" + GetInstanceID().ToString();
        InitStats();
        allowLessFrames = false;

        float angle = -90 + Random.value * 180.0f;
        //if (Director::getInstance().getRunningScene().getCameras().size() > 2)
        //{
        //    node = Director::getInstance().getRunningScene().getCameras().at(1);
        //}
        Vector2 position = new Vector2(transform.position.x + Globals.CocosToUnity(1800) * Mathf.Sin(Mathf.Deg2Rad*(angle)), Globals.LOWERBOUNDARY + Globals.CocosToUnity(1800) * Mathf.Cos(Mathf.Deg2Rad*(angle))); ;
       
        transform.position = position;
        enemySprite.state.SetAnimation(0, "idle2", true);
        //healthBar.gameObject.SetActive(false);
        scheduleUpdate = true;
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        OnCollision();
        float dir = Vector2.SignedAngle(player.transform.position - transform.position, transform.right)+90;
        float rotDir = dir < 0 ? 360 + dir : dir;
        enemySprite.transform.SetRotation(-rotDir);
        transform.position= new Vector2(transform.position.x + stats.speed * Mathf.Sin(Mathf.Deg2Rad*rotDir) * Time.deltaTime * 0.60f, transform.position.y + stats.speed * Mathf.Cos(Mathf.Deg2Rad*rotDir) * Time.deltaTime * 0.60f);
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        stats.speed = baseStats.speed = 5.5f;

        stats.health = baseStats.health = 650;
        stats.turnSpeed = baseStats.turnSpeed = 2;
        stats.bulletDamage = baseStats.bulletDamage = 150;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50;
        stats.coinAwarded = baseStats.coinAwarded = 0;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;

    }

    private void OnCollision()
    {
        if (Vector2.SqrMagnitude(player.transform.position-transform.position) < detonationDistance && player.canHit) //Player::getStats().mode == Player::PLAYER_MODE_FLYING)
        {
            scheduleUpdate = false;
            enemySprite.GetComponent<Renderer>().enabled = false;
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, transform.position, false, 1, 2.5f,Globals.CocosToUnity(100));
            DOTween.Kill(tweenId);
            blast.SetActive(true);
            DOTween.Sequence().SetId(tweenId).Append(blast.transform.DOScale(new Vector2(2.5f,2.5f), 0.2f)).AppendInterval(0.1f).Play();
            DOTween.Sequence().SetId(tweenId).Append(blast.GetComponent<Renderer>().material.DOFade(0, 0.3f)).AppendCallback(() => { Destroy(blast); }).Play();
            blast.transform.parent = null;
            blast.transform.position = transform.position;

            DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(Destroy).Play();
            player.GotHit(stats.bulletDamage);
        }

    }

    public override void Destroy()
    {
        float random = Random.value * 4000;
        for (int i = 0; i < 6; i++)
        {
            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            
            bullet.setDamage(stats.bulletDamage / 2);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.duration = 6.5f;
            bullet.transform.SetScale(1.0f);

            bullet.setRadiusEffectSquared(Globals.CocosToUnity(100));
            bullet.setDamage(30);

            bullet.transform.position = transform.position;
            bullet.transform.SetRotation(random + (i * 60));
            Vector2 dest = new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), Globals.CocosToUnity(5000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation())*-1);
            bullet.PlayBulletAnim(8, dest);
            bullet.duration = 6.5f;
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            bullet.PushBack = 0.3f;

            GameSharedData.Instance.enemyBulletInUse.Add(bullet);

            for (int j = 0; j < 2; j++)
            {
                bullet.SetBulletChild(BulletChild.SonicBullet);
                //SkeletonAnimation* sonic = SkeletonAnimation::createWithJsonFile("res/Enemy/Sonic_Bullet.json", "res/Enemy/Sonic_Bullet.atlas");
                //bullet.addChild(sonic);
                //sonic.setPosition(bulletLayer.getContentSize() / 2);
                //sonic.setAnimation(0, "Sonic_Bullet", true);
                //sonic.setCameraMask(GAMECAMERA);
                bullet.spriteRenderer.color = new Color(1,1,1,0.1f);
                //sonic.setRotation(90);
                //sonic.setScale(0.75f);

            }
        }


        AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.spiderCatBullet2,1,transform.position);
        //TODO Distance Sound
        //Globals.PlaySound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3",transform.position);
        base.Destroy();
    }
}
