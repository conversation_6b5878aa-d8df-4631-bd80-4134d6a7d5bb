using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class BubbleCreator : MonoBehaviour
{
    [SerializeField] private GameObject bubbleContainer;
    [SerializeField] private GameObject[] bubbles;

    [HideInInspector] public float scaleMultiplier = 1.0f;
    [HideInInspector] public float durationMultiplier = 1.0f;
    [HideInInspector] public float distanceMultiplier = 1.0f;

    private Transform followObj = null;

    private string schedulerId = "ScheduleBubble";
    private string tweenid = "Bubble";



    private void CreateBubble()
    {
        bool didFindBubble = false;
        GameObject bubble = null;
        foreach (GameObject g in bubbles)
        {
            if (!g.activeSelf)
            {
                bubble = g;
                didFindBubble = true;
                bubble.transform.SetScale(1);
                break;
            }
        }
        if (!didFindBubble)
            return;

        if (followObj)
        {
            bubble.transform.position = followObj.position;
        }
        bubble.gameObject.SetActive(true);
        bubble.transform.GetChild(0).transform.localPosition = new Vector2(Globals.CocosToUnity(-0.5f + (-0.75f * Random.value * scaleMultiplier)), Globals.CocosToUnity(0.25f));
        bubble.transform.SetRotation(360 * Random.value);
        bubble.transform.position = new Vector2(bubble.transform.position.x+Random.Range(-0.5f,0.5f), bubble.transform.position.y + Random.Range(-0.2f, 0.2f));
        DOTween.Sequence().SetId(bubble.name + tweenid).Append(bubble.transform.DORotate(new Vector3(0, 0, 30 + (30 * Random.value)), 1f)).SetLoops(-1).Play();
        DOTween.Sequence().SetId(bubble.name + tweenid).Append(bubble.transform.DOBlendableMoveBy(new Vector2(0, 4f*distanceMultiplier), 5 * durationMultiplier)).Play();
        DOTween.Sequence().SetId(bubble.name + tweenid).Append(bubble.transform.DOScale(Vector2.zero, 4.5f + (2 * Random.value * durationMultiplier))).AppendCallback(()=>
        {
            DOTween.Kill(bubble.name + tweenid);
            bubble.gameObject.SetActive(false);
        }).Play();
    }

    public void SetFollowObject(Transform obj)
    {
        followObj = obj;
    }
    
    public void SpawnBubble()
    {
        DOTween.Sequence().SetId(schedulerId).AppendCallback(CreateBubble).AppendInterval(0.3f).SetLoops(-1).Play();
    }

    public void CancelSpawn()
    {
        DOTween.Kill(schedulerId);
    }
}
