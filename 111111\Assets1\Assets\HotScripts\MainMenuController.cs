using System.Collections;
using System.Collections.Generic;
using UnityEngine.SceneManagement;
using UnityEngine;
using TMPro;

public class MainMenuController : MonoBehaviour
{
    public enum MainScreenType { Shop = 0, Sidekick = 1 }
    public static MainScreenType startScreen;

    public static MainMenuController instance;
    public Camera Mycamera;
    public PlayerShop player;
    [SerializeField] RectTransform canvasRect;
    [SerializeField] GameObject cam1GameObject, cam2GameObject;
    [SerializeField] private Animator fadeScreenAnim;
    public Shop shopMenu;
    [SerializeField] SidekicksPanel sidekicksMenu;
    public PopUp popup;
    [SerializeField] private MainMenuScreen currentScreen;
    [SerializeField] private MainMenuScreen[] mainScreens;
    public TextMeshProUGUI planeStat1TMP, planeStat2TMP, planeStat3TMP, planeStat4TMP;

    private MainMenuScreen nextScreen;

    private void Awake()
    {
        instance = this;

        //PlayerPrefs.SetInt(((SidekickType)1).ToString(), 0);
        //PlayerPrefs.SetInt(((SidekickType)2).ToString(), 0);
        //PlayerPrefs.SetInt(((SidekickType)3).ToString(), 0);
        //PlayerPrefs.SetInt(((SidekickType)4).ToString(), 0);
        //PlayerPrefs.SetInt(((SidekickType)5).ToString(), 0);
    }

    private void Start()
    {
        Mycamera.cullingMask = -1;
        PlayerPrefs.SetInt("lastLevelVisitingShop", PlayerPrefs.GetInt("missionsCompleted"));
        currentScreen = mainScreens[(int)MainScreenType.Sidekick];

        currentScreen.OpenScreen();

        //if (startScreen == MainScreenType.Shop)
        //{
        //    shopMenu.Init();
        //    sidekicksMenu.SetSelectedSidekick();
        //    sidekicksMenu.ShowSidekickWithPlayer(shopMenu.GetPlayerPosition);
        //    return;
        //}
        //else if (startScreen == MainScreenType.Sidekick)
        //{
            sidekicksMenu.Init();
        //}
        
        shopMenu.StopPlayerShoot();
    }

    public void SetScreenSize(RectTransform screenRect)
    {
        float scale = Mathf.Min(canvasRect.sizeDelta.x / 960f, canvasRect.sizeDelta.y / 600f);
        screenRect.localScale = new Vector3(scale, scale, 1);
    }

    public void SetSize(RectTransform imgRect, Vector2 imgSizeInPixels)
    {
        float scale = Mathf.Max(canvasRect.sizeDelta.x / imgSizeInPixels.x, canvasRect.sizeDelta.y / imgSizeInPixels.y);
        scale = (scale * 10) % (int)(scale * 10) > 0.5f ? Mathf.Round(scale * 10) / 10f : scale;

        imgRect.localScale = new Vector3(scale, scale, 1);

        float bgPosOffsetX = 0;
        float bgPosOffsetY = 0;
        float aspectRatio = canvasRect.sizeDelta.x / canvasRect.sizeDelta.y;

        bgPosOffsetX = (scale * imgSizeInPixels.x - canvasRect.sizeDelta.x) / 3f;
        //bgPosOffsetY = (scale * 750 - 750) / 2;

        imgRect.anchoredPosition = new Vector2(bgPosOffsetX, bgPosOffsetY);
    }

    public void ChangeScreen(MainMenuScreen screen)
    {
        nextScreen = screen;

        float speed = 3;
        fadeScreenAnim.SetFloat("Speed", speed);
        fadeScreenAnim.Play("Fade", 0, 0);
        StopCoroutine(nameof(PlayFadeAnimation));
        StartCoroutine(nameof(PlayFadeAnimation), 2 / speed); // 2 Seconds is the duration of Fade animation
    }

    public void CreatePopup(string title, string msg, System.Action callback)
    {
        popup.CreateAsConfirmDialogue(title, msg, callback);
    }

    public void CreateMessagePopup(string title, string msg, bool isOkButton = true)
    {
        popup.CreateAsMessage(title, msg, isOkButton);
    }

    IEnumerator PlayFadeAnimation(float delay)
    {
        yield return new WaitForSeconds(delay / 2);

        {
            var shop = currentScreen.GetComponent<Shop>();
            var sidekicksPanel = currentScreen.GetComponent<SidekicksPanel>();

            if (sidekicksPanel)
            {
                sidekicksPanel.HideSidekick();
            }
            else if (shop)
            {
                shop.StopPlayerShoot();
                sidekicksMenu.HideSidekickWithPlayer();
            }
        }
        
        currentScreen.CloseScreen();
        currentScreen = nextScreen;
        currentScreen.OpenScreen();

        {
            var sidekicksPanel = currentScreen.GetComponent<SidekicksPanel>();
            var shop = currentScreen.GetComponent<Shop>();

            if (sidekicksPanel)
            {
                sidekicksPanel.Init();
            }
            else if (shop)
            {
                shop.Init();
                sidekicksMenu.ShowSidekickWithPlayer(shopMenu.GetPlayerPosition);
            }
        }
    }

    private void OnDestroy()
    {
        GameData.instance.fileHandler.lastLevelVisitingShop = PlayerPrefs.GetInt("lastLevelVisitingShop");
        GameData.instance.fileHandler.SaveData();
    }

    public void LoadMainMenu()
    {
        NavigationButton.currentlySelected = null;
        //SceneManager.LoadScene("MainMenu");
        LuaManager.Instance.RunLuaFunction<int>("BattleManager.goToMain", 0);
    }
}
