using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using Spine;
public class ElectricTower : Enemy
{
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite missileSprite;
    [SerializeField] private SuperGeyser geyserLeft;
    [SerializeField] private SuperGeyser geyserRight;
    [SerializeField] private GameObject[] blueBlast;
    [SerializeField] private GameObject[] plasmaBlast;
    [SerializeField] private GameObject[] orbsDefault;
    [SerializeField] private GameObject[] orbsAdditive;
    [SerializeField] private GameObject[] electricMine;
    [SerializeField] private ThunderAttack[] thunderAttack;
    private float posX;
    private float posY;
    public static int TowerType = 1;
    PlayerPing missionPing;
    private int thunderAttackCount = 0;
    public override void Init()
    {

        if (initialized)
            return;
        base.Init();
        tweenId = "ElectricTower" + GetInstanceID().ToString();
        schedulerId = "ElectricTowerS" + GetInstanceID().ToString();
        allowRelocate = false;

        isBoss = false;
        InitStats();
        Globals.numberOfEnemies++;
        healthBar.gameObject.SetActive(false);
        transform.position = new Vector2(Globals.CocosToUnity(5000), Globals.LOWERBOUNDARY);
        enemySprite.state.SetAnimation(0, "Idle", true);
        healthBar.ScaleRatio = 2.5f;

        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, true);


        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, false);

        explosionType = Explosions.ExplosionType.ExplosionTypeBuildingMission;
        DOTween.Sequence().SetId(schedulerId).AppendCallback(UpdateBounds).SetLoops(-1).Play();
        scheduleUpdate = true;

        posX = transform.position.x;
        posY = transform.position.y;
        DOTween.Sequence().SetId(schedulerId).AppendCallback(() =>
        {
            posX = transform.position.x;
            posY = transform.position.y;

            if (TowerType == 1)
            {
                DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot).AppendInterval(8).SetLoops(-1).Play();
            }
            if (TowerType == 2)
            {
                DOTween.Sequence().SetId(schedulerId).AppendCallback(Shoot3).AppendInterval(6).SetLoops(-1).Play();
                //        TimerAttack * timerAtt = TimerAttack::createWithTower(this);
                //        this.addChild(timerAtt);


                DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).AppendCallback(() =>
                {
                    geyserLeft.gameObject.SetActive(true);
                    geyserLeft.transform.parent = null;
                    geyserLeft.Create();
                    geyserLeft.transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(-600));
                    geyserRight.gameObject.SetActive(true);
                    geyserRight.transform.parent = null;
                    geyserRight.Create();
                    geyserRight.transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(600));
                });


            }
        }).Play();

    }

    public void Create()
    {
        Init();
    }

    public void CreateAsType1()
    {
        TowerType = 1;
        Init();
    }

    public void CreateAsType2()
    {
        TowerType = 2;
        Init();
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        stats.speed = baseStats.speed = 3.5f + Random.value;

        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        if (TowerType == 1)
        {
            stats.health = baseStats.health = 25000;

        }
        if (TowerType == 2)
        {
            stats.health = baseStats.health = 35000;


        }
        stats.bulletDamage = baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 25;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;

    }

    private void Update()
    {
        
    }

    public override void Destroy()
    {
        GameManager.instance.ShakeCamera(1.25f, 15);
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);

        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        base.Destroy();
    }

    private void UpdateBounds()
    {

    }

    private void Shoot()
    {
        enemySprite.state.SetAnimation(0, "Attack", false);
        enemySprite.state.AddAnimation(0, "Idle", true);
        if (thunderAttackCount <= 6)
        {
            thunderAttack[thunderAttackCount].gameObject.SetActive(true);
            thunderAttack[thunderAttackCount].CreateWithTime(3);
            thunderAttack[thunderAttackCount]._lightningNumber = 0;
            thunderAttack[thunderAttackCount].SetLaserRotation(90);
            thunderAttack[thunderAttackCount].SetLaserPosition(new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(2500)));
            //thunderAttack[0].GetLaser().SetIsActive(true);
            thunderAttackCount++;
            print(thunderAttackCount);
        }
        //for (int i = 1; i < 6; i++)
        //{
        {
            int i = 1;
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.75f).AppendCallback(() =>
            {

                float offset = 0;

                if (i % 2 == 0)
                {
                    offset = 225;
                }
                GenerateElectricBall(Globals.CocosToUnity(-675 + offset), Globals.CocosToUnity(4800));
                GenerateElectricBall(Globals.CocosToUnity(-225 + offset), Globals.CocosToUnity(4800));
                GenerateElectricBall(Globals.CocosToUnity(225 + offset), Globals.CocosToUnity(4800));
                GenerateElectricBall(Globals.CocosToUnity(675 + offset), Globals.CocosToUnity(4800));
                GenerateElectricBall(Globals.CocosToUnity(1125 + offset), Globals.CocosToUnity(4800));
                GenerateElectricBall(Globals.CocosToUnity(1575 + offset), Globals.CocosToUnity(4800));
                i++;

            }).SetLoops(5).Play();
        }
        

        {

            int i = 1;
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.75f).AppendCallback(() =>
            {

                float offset = 0;

                if (i % 2 == 0)
                {
                    offset = 225;
                }
                GenerateElectricBall(Globals.CocosToUnity(-675 + offset), Globals.CocosToUnity(-4800));
                GenerateElectricBall(Globals.CocosToUnity(-225 + offset), Globals.CocosToUnity(-4800));
                GenerateElectricBall(Globals.CocosToUnity(225 + offset), Globals.CocosToUnity(-4800));
                GenerateElectricBall(Globals.CocosToUnity(675 + offset), Globals.CocosToUnity(-4800));
                GenerateElectricBall(Globals.CocosToUnity(1125 + offset), Globals.CocosToUnity(-4800));
                GenerateElectricBall(Globals.CocosToUnity(1575 + offset), Globals.CocosToUnity(-4800));
                i++;
            }).SetLoops(5).Play();
        }
    }

    private void Shoot2()
    {
        //enemySprite.setAnimation(0, "Attack", false);
        //enemySprite.addAnimation(0, "Idle", true);

        //ThunderAttack* thunderAttack = ThunderAttack::create();
        //thunderAttack._lightningNumber = 0;
        //thunderAttack.SetLaserRotation(90);
        //thunderAttack.setCameraMask(GAMECAMERA);
        //thunderAttack.SetLaserPosition(Vec2(posX, posY + 2400));
        //this.addChild(thunderAttack);
        ////thunderAttack._lightning.setScale(3);
        ////       this.runAction(Sequence::create( DelayTime::create(1), CallFunc::create([=]{
        ////
        ////
        ////
        ////           if(Player::getInstance().getPosition().x > enemySprite.getPosition().x - 200)
        ////               GETPLAYERCONTROLLER.knockback(thunderAttack._laserObj.getLaser().getRotation(), 300, 100);
        ////
        ////       }) ,   NULL));


        //float timer = 0.5;

        //for (int i = -5; i < 4; i++)
        //{


        //    this.runAction(Sequence::create(DelayTime::create(timer), CallFunc::create([=]{
        //        ThunderAttack* thunderAttack = ThunderAttack::createWithTime(3);
        //        thunderAttack._lightningNumber = 0;
        //        thunderAttack.SetLaserRotation(360 - 90);
        //        thunderAttack.setCameraMask(GAMECAMERA);
        //        thunderAttack.SetLaserPosition(Vec2(i * 250 + posX, posY - 350));
        //        this.addChild(thunderAttack);


        //    }),  NULL));

        //timer = timer + 0.5;


    }

    private void Shoot3()
    {

        enemySprite.state.SetAnimation(0, "Attack", false);
        enemySprite.state.AddAnimation(0, "Idle", true);
        HomingMissile missile = null;
        bool didFindMissile = false;
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            return;
        }
        missile.CreateWithHomingDuration(100);
        missile.DisableBoost();
        missile.duration = 100;
        missile.missileSprite.sprite = missileSprite;
        missile.SetDamage(300);
        missile.SetSpeed(5.5f);
        missile.homingMultiplier = 3.5f;
        missile._doExplodeOnWater = false;
        missile.transform.position = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y + Globals.CocosToUnity(200));
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.radiusSQ = Globals.CocosToUnity(150);
        missile.RemoveAfterDuration();
        missile.isDestructable = false;
        missile.transform.SetScale(2.0f);
        missile.endFunc = () => {
            GameObject blast = null;
            bool didFindBlast = false;
            foreach (GameObject g in blueBlast)
            {
                if (!g.activeSelf)
                {
                    blast = g;
                    didFindBlast = true;
                    break;
                }

            }
            if (!didFindBlast)
            {
                return;
            }
            //Sprite* blast = Sprite::createWithSpriteFrameName("blueBlast_00000.png");
            //blast.runAction(Sequence::create(Shared::createAnimation("blueBlast_0000%d.png", 0, 6, false), RemoveSelf::create(), NULL));
            //this.addChild(blast);
            //blast.setPosition(missile.missileSprite.getPosition());
            //blast.setCameraMask(GAMECAMERA);
            //blast.setScale(missile.missileSprite.getScale() * 25);
            blast.transform.position = missile.transform.position;
            blast.gameObject.SetActive(true);
            blast.GetComponent<Animator>().Play("BlueBlastAnim");
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.27f).AppendCallback(() => { blast.gameObject.SetActive(false); }).Play();



            GameObject blast2 = null;
            bool didFindPlasmaBlast = false;
            foreach (GameObject g in plasmaBlast)
            {
                if (!g.activeSelf)
                {
                    blast2 = g;
                    didFindPlasmaBlast = true;
                    break;
                }

            }
            if (!didFindPlasmaBlast)
            {
                return;
            }
            blast2.GetComponent<Renderer>().material.color = Color.white;
            blast2.transform.position = missile.transform.position;
            blast2.transform.SetScale(15);
            blast2.gameObject.SetActive(true);
            blast2.GetComponent<SkeletonAnimation>().state.SetAnimation(0, "plasmaBlast", true);
            DOTween.Sequence().SetId(tweenId).Append(blast2.GetComponent<Renderer>().material.DOFade(0, 0.3f)).Play();
            blast2.transform.position = missile.transform.position;
            DOTween.Sequence().SetId(tweenId).Append(blast2.transform.DOScale(2,0.2f)).AppendInterval(0.1f).AppendCallback(()=>{ blast2.gameObject.SetActive(false); }).Play();
        };

        {
            if (missile.transform.childCount < 3)
            {
                GameObject orb = null;
                bool didFindOrb = false;
                foreach (GameObject o in orbsDefault)
                {
                    if (!o.activeSelf)
                    {
                        orb = o;
                        didFindOrb = true;
                        break;
                    }

                }
                if (!didFindOrb)
                {
                    return;
                }
                orb.gameObject.SetActive(true);
                orb.transform.parent = missile.transform;
                orb.transform.SetScale(1);
                orb.transform.localPosition = Vector3.zero;
                DOTween.Sequence().SetId(tweenId).Append(orb.transform.DOBlendableLocalRotateBy(Vector3.forward * 800, 1)).SetLoops(-1).Play();
                //Sprite* spriteOverlay = Sprite::create("res/Enemy/Meowthena_Orb_Base.png");
                //missile.missileSprite.addChild(spriteOverlay);
                //spriteOverlay.setCameraMask(GAMECAMERA);
                //spriteOverlay.setPosition(missile.missileSprite.getContentSize() / 2);
                //spriteOverlay.setOpacity(200);
                //spriteOverlay.runAction(RepeatForever::create(RotateBy::create(1, 800)));
            }
        }

        {

            if (missile.transform.childCount < 4)
            {
                GameObject orb = null;
                bool didFindOrb = false;
                foreach (GameObject o in orbsAdditive)
                {
                    if (!o.activeSelf)
                    {
                        orb = o;
                        didFindOrb = true;
                        break;
                    }

                }
                if (!didFindOrb)
                {
                    return;
                }
                orb.SetActive(true);
                orb.transform.parent = missile.transform;
                orb.transform.localPosition = Vector3.zero;
                DOTween.Sequence().SetId(tweenId).Append(orb.transform.DOBlendableLocalRotateBy(Vector3.forward * -800, 1)).SetLoops(-1).Play();
                orb.transform.SetScale(1);
                //Sprite* spriteOverlay = Sprite::create("res/Enemy/Meowthena_Orb_Base.png");
                //missile.missileSprite.addChild(spriteOverlay);
                //spriteOverlay.setCameraMask(GAMECAMERA);
                //spriteOverlay.setPosition(missile.missileSprite.getContentSize() / 2);
                //spriteOverlay.setOpacity(200);
                //spriteOverlay.runAction(RepeatForever::create(RotateBy::create(1, 800)));
            }
            //Sprite* spriteOverlay = Sprite::create("res/Enemy/Meowthena_Orb_Base.png");
            //missile.missileSprite.addChild(spriteOverlay);
            //spriteOverlay.setCameraMask(GAMECAMERA);
            //spriteOverlay.setPosition(missile.missileSprite.getContentSize() / 2);
            //spriteOverlay.setOpacity(200);
            //spriteOverlay.runAction(RepeatForever::create(RotateBy::create(1, -800)));
            //spriteOverlay.setBlendFunc(BlendFunc::ADDITIVE);
        }
        if (missile.transform.childCount < 5)
        {
            GameObject mine = null;
            bool didFindMine = false;
            foreach (GameObject m in electricMine)
            {
                if (!m.activeSelf)
                {
                    mine = m;
                    didFindMine = true;
                    break;
                }

            }
            if (!didFindMine)
            {
                return;
            }
            mine.SetActive(true);
            mine.transform.SetScale(1.5f);
            mine.transform.parent = missile.transform;
            mine.transform.localPosition = Vector3.zero;
            //Sprite* spriteOverlay = Sprite::create("res/Enemy/Meowthena_Orb_Base.png");
            //missile.missileSprite.addChild(spriteOverlay);
            //spriteOverlay.setCameraMask(GAMECAMERA);
            //spriteOverlay.setPosition(missile.missileSprite.getContentSize() / 2);
            //spriteOverlay.setOpacity(200);
            //spriteOverlay.runAction(RepeatForever::create(RotateBy::create(1, 800)));
        }
        
        //Sprite* lightning = Sprite::createWithSpriteFrameName("ElectricMineNew1.png");
        //lightning.setCameraMask(GAMECAMERA);
        //missile.missileSprite.addChild(lightning);
        //lightning.setPosition(Vec2(missile.missileSprite.getContentSize().width / 2, missile.missileSprite.getContentSize().height / 2));
        //lightning.setScale(1.5f);
        //lightning.runAction(Shared::createAnimation("ElectricMineNew%d.png", 1, 22, true, 0.04));

    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        else
            return false;
    }

    private void GenerateElectricBall(float ySpawnPosition, float xFinalPosition)
    {
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        //Bullets* bulletLayer = Bullets::create();
        //Sprite* electricBall = bulletLayer->bulletSprite;
        //electricBall->setTexture("res/Enemy/Meowthena_Orb_Base.png");
        bullet.SetSpriteFrame(missileSprite);
        bullet.setDamage(100);
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
        bullet.duration = 5.5f;
        //    electricBall->setGlobalZOrder(1);

        bullet.setRadiusEffectSquared(Globals.CocosToUnity(180));
        bullet.transform.SetScale(0.8f);

        bullet.transform.position =new Vector3(posX, posY + ySpawnPosition);
        Vector2 dest = new Vector2(xFinalPosition, bullet.transform.position.y);
        bullet.PlayBulletAnim(4 * 2, dest, false);//FIXME
        DOTween.Sequence().SetId(bullet.tweenId).Append(bullet.transform.DOScale(1.7f, 4f)).Play();
        //electricBall->runAction(Sequence::create(Spawn::create(MoveTo::create(4 * 2, Vec2(posX - xFinalPosition, electricBall->getPositionY() + 0)), ScaleTo::create(4, 0.8), NULL), CallFunc::create([=]{


        //}),  NULL));
        DOTween.Sequence().SetId(bullet.tweenId).Append(bullet.transform.DOBlendableRotateBy(Vector3.forward * 180, 1)).Append(bullet.transform.DOBlendableRotateBy(Vector3.forward * -180, 1)).SetLoops(-1).Play();
        //electricBall->runAction(RepeatForever::create(Sequence::create(RotateBy::create(1, 180), RotateBy::create(1, -180), NULL)));

        //GameSharedData::getInstance()->g_bulletsToAddArray.pushBack(bulletLayer);

        //Sprite* lightning = Sprite::createWithSpriteFrameName("ElectricMineNew1.png");
        //lightning->setCameraMask(GAMECAMERA);
        //electricBall->addChild(lightning);
        //lightning->setPosition(Vec2(electricBall->getContentSize().width / 2, electricBall->getContentSize().height / 2));
        //lightning->setScale(1.75);
        //lightning->runAction(Shared::createAnimation("ElectricMineNew%d.png", 1, 22, true, 0.04));
        bullet.SetBulletChild(BulletChild.LightningBall);
        bullet.GetBulletChild(BulletChild.LightningBall).transform.SetScale(1f);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);



    }

}
