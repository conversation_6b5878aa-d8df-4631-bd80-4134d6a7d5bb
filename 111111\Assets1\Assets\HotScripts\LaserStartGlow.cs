﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LaserStartGlow : MonoBehaviour
{
    [SerializeField] float minScale, maxScale, animationDuration;
    float currentScale = 0;

    public void AnimateGlow(bool doScaleUp)
    {
        StopCoroutine(nameof(ScaleUp));
        StopCoroutine(nameof(ScaleDown));
        if (doScaleUp)
        {
            StartCoroutine(nameof(ScaleUp));
        }
        else
        {
            StartCoroutine(nameof(ScaleDown));
        }
    } 

    IEnumerator ScaleUp()
    {
        currentScale = transform.localScale.x;

        if (currentScale >= maxScale)
            yield break;

        float duration = currentScale <= 0 ? animationDuration : currentScale / maxScale * animationDuration;

        while(duration > 0)
        {
            float step = (maxScale - currentScale) * (Time.deltaTime / duration);

            transform.localScale += (Vector3)(Vector2.one * step);

            if (transform.localScale.x > maxScale)
                transform.localScale = Vector2.one * maxScale;

            duration -= Time.deltaTime;

            yield return null;
        }
    }

    IEnumerator ScaleDown()
    {
        currentScale = transform.localScale.x;

        if (currentScale <= minScale)
            yield break;

        float duration = currentScale / maxScale * animationDuration;

        while (duration > 0)
        {
            float step = (currentScale - minScale) * (Time.deltaTime / duration);

            transform.localScale -= (Vector3)(Vector2.one * step);

            if (transform.localScale.x < minScale)
                transform.localScale = Vector2.one * minScale;

            duration -= Time.deltaTime;

            yield return null;
        }
    }

    public void SetScaleZero()
    {
        StopCoroutine(nameof(ScaleUp));
        StopCoroutine(nameof(ScaleDown));
        transform.localScale = new Vector3(minScale, minScale, 1);
    }
}
