﻿using System.Collections.Generic;
using System.IO;

using ProtoBuf;
using X.PB;

namespace CsvTables
{
    public partial class EquipSmeltCsv : Singleton<EquipSmeltCsv>
    {
        private readonly Dictionary<int, EquipSmelt.Item> dic = new();

        protected override void InitializeSingleton()
        {
            DontDestroyOnLoad(this);
            int schemeIndex = (int)SchemeType.EquipEffect;
            string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
            MemoryStream ms = new(HotResManager.ReadPb(pbFileName));
            var _data = Serializer.Deserialize<EquipSmelt>(ms);
            foreach (var item in _data.Items)
            {
                dic.Add(item.Id, item);
            }
            //Debug.LogWarning(pbFileName + "pb succes");

            base.InitializeSingleton();
        }
    }
}