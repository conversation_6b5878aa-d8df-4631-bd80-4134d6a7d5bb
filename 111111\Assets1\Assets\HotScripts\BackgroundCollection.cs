﻿using UnityEngine;





[CreateAssetMenu(fileName = "BackgroundCollection", menuName = "ScriptableObjects/BackgroundCollection", order = 1)]
public class BackgroundCollection : ScriptableObject
{
    public Prop[] props;
    public Prop[] clouds;
    public BackgroundPropLayer[] backgroundPropLayers; // When using layers of background props, make sure 'isFarProp' boolean is set to false for all Prop structs to avoid overlapping.
    public GameObject bgSkyPrefab, topCloudPrefab, godrayPrefab, horizonPrefab, lightningPrefab, moonPrefab,
        bg2Prefab, smokePrefab, catheadPrefab, waterTopPrefab, blueNormalPrefab, darkEffectPrefab;
    public Sprite waterTopSprite;
    public Color propColor = Color.white, cloudColor = Color.white, farCloudColor = Color.white;
    public float minZ, maxZ, xDistanceFromCam, yNearPropPosition, yFarPropPosition, zFarPosition, skyZPosition;
    public int numberOfNearProps, numberOfFarProps, numberOfNearClouds, numberOfFarClouds, numberOfGodrays;
    public bool allowTopCloud, allowSmoke;
}

[System.Serializable]
public struct Prop
{
    public GameObject prefab;
    public float minScale, maxScale, farScale, minNearY, minFarY, maxNearY, maxFarY;
    public bool isFarProp, isNearProp; // Check whether this prop can be spawned as a far prop and a near prop or not
}

// Far props are the ones spawned at the back; props are spawned over water-top otherwise.

[System.Serializable]
public struct BackgroundPropLayer
{
    public int orderInLayerA, orderInLayerB, numberOfProps;
    public float layerYPosition, layerZPosition;
    public BackgroundProp[] layerProps;

    [System.Serializable]
    public struct BackgroundProp
    {
        public GameObject prefab;
        public float scale;
    }
}