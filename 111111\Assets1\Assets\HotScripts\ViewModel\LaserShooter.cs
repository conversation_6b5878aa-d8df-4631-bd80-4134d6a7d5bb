﻿using System.Collections.Generic;
using System.Threading;

using Cysharp.Threading.Tasks;

using UnityEngine;

namespace ViewModel
{
    /// <summary>
    /// 激光发射器
    /// </summary>
    public class LaserShooter
    {
        /// <summary>
        /// 激光光源
        /// </summary>
        public GameObject LaserLight { get; set; }
        /// <summary>
        /// 发光特效
        /// </summary>
        public GameObject LaserGlow { get; set; }
        ///// <summary>
        ///// 不知道是什么
        ///// </summary>
        //public GameObject LaserStart { get; set; }
        /// <summary>
        /// 发出的激光
        /// </summary>
        public List<Laser> Lasers { get; } = new();

        /// <summary>
        /// 激光的取消令牌
        /// </summary>
        public CancellationTokenSource LaserCancel { get; set; } = new();
        /// <summary>
        /// 属于哪次激光技能(编号)
        /// </summary>
        public long LaserSkillNo { get; } = NextLaserSkillNo();

        /// <summary>
        /// 最后一次激光技能的编号
        /// </summary>
        public static long LastLaserSkillNo { get; private set; }
        /// <summary>
        /// 获取下一个激光技能编号(自增)
        /// </summary>
        private static long NextLaserSkillNo()
        {
            return LastLaserSkillNo++;
        }

        /// <summary>
        /// 准备发动技能:创建光源、发光特效、音效
        /// </summary>
        /// <param name="weapon">激光的预制体现在是放在武器上的</param>
        /// <param name="bulletScale">发光点的缩放(可能没啥用)</param>
        public async UniTask CreateLightGlow(Weapon weapon, int bulletScale)
        {
            //LaserStart = Object.Instantiate(weapon.laserStartPrefab, weapon.player.transform);
            LaserLight = Object.Instantiate(weapon.laserStartGlowPrefab, weapon.player.transform);
            LaserGlow = Object.Instantiate(weapon.laserStartParticlePrefab, weapon.player.transform);
            LaserGlow.transform.localScale = Vector3.one * (GameData.instance.fileHandler.currentMission != 0 ? Globals.UnityValueTransform(bulletScale) : 1);

            StartAnimate();
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.laserStart, 0.75f);
            await UniTask.Delay(750, cancellationToken: LaserCancel.Token);

            //var laserStartSkeleton = LaserStart.GetComponent<SkeletonAnimation>();
            //var trackEntry = laserStartSkeleton.state.SetAnimation(0, "animation", false);
            //laserStartSkeleton.gameObject.SetActive(true);

            //await UniTask.Delay(System.TimeSpan.FromSeconds(trackEntry.Animation.Duration));
            //laserStartSkeleton.gameObject.SetActive(false);

            //GameManager.instance.ShakeCamera(1);
            GameManager.instance.ToggleDarkEffect(true);
            //player.transform.position = player.transform.position - (Vector3)new Vector2(Globals.CocosToUnity(50) * Mathf.Cos(player.RotationInDegrees * Mathf.Deg2Rad), Globals.CocosToUnity(50) * Mathf.Sin(player.RotationInDegrees * Mathf.Deg2Rad));
        }

        /// <summary>
        /// 射出一条激光(但并不显示)
        /// </summary>
        /// <param name="weapon">激光的预制体现在是放在武器上的</param>
        /// <param name="skillLevel">技能等级,计算起点的缩放(可能没啥用)</param>
        /// <param name="pEnd">终点位置</param>
        public Laser EjectLaser(Weapon weapon, Vector2 pEnd, int skillLevel)
        {
            var laser = new Laser
            {
                LaserBegin = Object.Instantiate(weapon.laserImpactPrefab, weapon.player.transform),
                LaserMiddle = Object.Instantiate(weapon.laserPrefab, weapon.player.transform),
                LaserEnd = Object.Instantiate(weapon.laserImpactPrefab, weapon.player.transform),
            };

            var dir = (Vector3)pEnd - weapon.player.transform.position;
            var dir_1 = dir.normalized;

            laser.LaserBegin.transform.position = weapon.player.transform.position + dir_1 * 0.1f;
            laser.LaserMiddle.transform.position = weapon.player.transform.position;
            laser.LaserEnd.transform.position = pEnd;

            laser.LaserBegin.transform.right = dir_1;
            laser.LaserMiddle.transform.right = dir_1;

            laser.LaserMiddle.GetComponent<SpriteRenderer>().drawMode = SpriteDrawMode.Sliced;

            //laser.LaserGlow.GetComponent<SpriteRenderer>().sprite.texture = xxx;
            //laser.LaserMiddle.GetComponent<SpriteRenderer>().sprite.texture = xxx;
            //laser.LaserEnd.GetComponent<SpriteRenderer>().sprite.texture = xxx;

            laser.LaserBegin.transform.localScale = new Vector3(1f, 1.25f + (skillLevel - 1) * 0.45f, 1);
            laser.LaserEnd.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));

            laser.SetActive(false);

            Lasers.Add(laser);
            return laser;
        }



        /// <summary>
        /// 射出一条范围激光(但并不显示)
        /// </summary>
        /// <param name="weapon">激光的预制体现在是放在武器上的</param>
        /// <param name="skillLevel">技能等级,计算起点的缩放(可能没啥用)</param>
        /// <param name="pEnd">终点位置</param>
        public Laser EjectSectorLaser(Weapon weapon, Vector2 pEnd, int skillLevel)
        {
            var laser = new SectorLaser
            {
                LaserBegin = Object.Instantiate(weapon.laserImpactPrefab, weapon.player.transform.parent),
                LaserMiddle = Object.Instantiate(weapon.SectorLaserPrefab, weapon.player.transform.parent),
                LaserEnd = Object.Instantiate(weapon.laserImpactPrefab, weapon.player.transform.parent),
            };

            var dir = (Vector3)pEnd - weapon.player.transform.position;
            var dir_1 = dir.normalized;

            laser.LaserBegin.transform.position = weapon.player.transform.position + dir_1 * 0.1f;
            laser.LaserMiddle.transform.position = weapon.player.transform.position;
            laser.LaserEnd.transform.position = pEnd + (Vector2)dir_1 * 100f;

            laser.LaserBegin.transform.right = dir_1;
            laser.LaserMiddle.transform.right = dir_1;

            //laser.LaserMiddle.GetComponent<SpriteRenderer>().drawMode = SpriteDrawMode.Sliced;

            //laser.LaserGlow.GetComponent<SpriteRenderer>().sprite.texture = xxx;
            //laser.LaserMiddle.GetComponent<SpriteRenderer>().sprite.texture = xxx;
            //laser.LaserEnd.GetComponent<SpriteRenderer>().sprite.texture = xxx;

            laser.LaserBegin.transform.localScale = new Vector3(1f, 1.25f + (skillLevel - 1) * 0.45f, 1);
            laser.LaserEnd.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));

            laser.SetActive(false);

            Lasers.Add(laser);
            return laser;
        }

        /// <summary>
        /// 启动动画和特效
        /// </summary>
        public void StartAnimate()
        {
            if (LaserLight)
            {
                LaserLight.SetActive(true);
                LaserLight.GetComponent<LaserStartGlow>().AnimateGlow(true);
            }
            if (LaserGlow)
            {
                LaserGlow.GetComponent<ParticleSystem>().Play();
            }
        }

        /// <summary>
        /// 停止动画和特效
        /// </summary>
        public void StopAnimate()
        {
            if (LaserGlow)
            {
                LaserGlow.GetComponent<ParticleSystem>().Stop();
            }
            if (LaserLight)
            {
                LaserLight.GetComponent<LaserStartGlow>().AnimateGlow(false);
                LaserLight.SetActive(false);
            }
        }

        /// <summary>
        /// 销毁
        /// </summary>
        public void Destroy()
        {
            StopAnimate();

            if (LaserLight)
            {
                Object.Destroy(LaserLight);
            }
            if (LaserGlow)
            {
                Object.Destroy(LaserGlow);
            }

            Lasers.ForEach(x => x.Destroy());
            Lasers.Clear();
        }
    }
}
