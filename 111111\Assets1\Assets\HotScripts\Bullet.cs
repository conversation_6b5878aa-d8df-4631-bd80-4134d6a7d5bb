using System;
using System.Collections;
using System.Collections.Generic;

using DG.Tweening;

using Spine.Unity;

using UnityEngine;

public enum BulletChild
{
    Splash = 0,
    RearBullet = 1,
    Trail = 2,
    Lightning = 3,
    flame = 4,
    SonicBullet = 5,
    FireBall = 6,
    LightningBall = 7,
    ElectricBall = 8,
    TinyBot = 9,
    PlasmaBullet = 10,
    Missile = 11,
    FireballParticle = 12
}

[System.Serializable]
public struct BulletChildStruct
{
    public BulletChild childType;
    public GameObject child;
}

public class Bullet : MonoBehaviour
{
    public enum BulletType
    {
        FrontMachineGun = 0,
        FrontMultiCanon = 1,
        FrontPlasma = 2,
        FrontRocket = 3,
        RearFlameThrower = 4,
        RearHomingRockets = 5,
        ProtonCanon = 6,
        EnemyBulletFireball = 7,
        EnemyBulletBlueExplosion = 8,
        EnemyBulletFireballTrail,
        ReflectButtle,
        Boomerang,
        Skyfire,
        ProtectiveShield,
        IceBall,
        IceBullet,
        HammerBullet,
        Skyfire_new01,
    }

    #region Public Variables

    public SpriteRenderer spriteRenderer;

    #endregion

    #region Private Serialized Variables


    #endregion

    #region Public Hidden Variables

    [HideInInspector] public bool hasHit = false;
    [HideInInspector] public bool isInUse = false;
    public float bulletRotation;
    [HideInInspector] public bool isRemovable = true, isFlipped = false, canCollionWithNormalEnemyBullet = false;
    [HideInInspector] public float duration = 3;
    [HideInInspector] public bool isMenuBullet = false;
    [HideInInspector] public string layerName;
    [HideInInspector] public int orderInLayer;
    public GameObject raflectTrail;

    #endregion

    #region Private Variables
    [SerializeField] Animator splashAnim, flameThrower, leishenzhichuiAnimator;
    [SerializeField] TrailRenderer trail;
    [SerializeField] SpriteRenderer lightning;
    [SerializeField] Sprite bulletSprite;
    [SerializeField] SkeletonAnimation fireBall;
    [SerializeField] ParticleSystem smokeParticle;
    [SerializeField] private double _damage;
    [SerializeField] private GameObject[] childObject;
    [SerializeField] private BulletChildStruct[] bulletChildren;

    private bool _reactToWater = true;
    private float defaultRadius = 1;
    /// <summary>
    /// 爆炸特效半径
    /// </summary>
    private float _radiusEffect = 0.5f;
    /// <summary>
    /// 爆炸特效半径的平方
    /// </summary>
    private float _radiusEffectSquared = 2f;
    private float radius;
    private Sequence animationSequence;
    [HideInInspector] public BulletType bulletType;
    private Action<Bullet> onHit, bulletActionComplete;
    private float _pushBack = 0;
    private bool _playSound = false;

    private bool _startProtonTrun = false;
    private float _protonMaxDistance;
    private float _protonCurrentDistance;
    private bool _isProtonDistanceAdd;
    private float _protectiveShieldturnSpeed;
    private bool _isProtectiveShieldMaxLevel;

    public float PushBack { get { return _pushBack; } set { _pushBack = value; } }
    public bool PlaySound { get { return _playSound; } set { _playSound = value; } }
    public BulletType Type { get { return bulletType; } }
    public bool isFireBallActive = false;
    public bool moveFireballLeft = true;

    public string tweenId;
    public string schedulerId;

    private GameObject _boomPrefab;
    private GameObject _skyBoomParent;
    private Vector3 preBulletPosition;
    //粒子炮相关的
    [HideInInspector] public CatSkill.Item skillData;
    [HideInInspector] public int canSplitNum;
    /// <summary>
    /// 只有主角的主武器能够去检测是否暴击
    /// </summary>
    [HideInInspector] public bool canCheckIsCriticalHit;

    private bool isReflectRota = false;
    #endregion


    private void Start()
    {
        //for (int i = 0; i < transform.childCount; i++)
        //{
        //    transform.GetChild(i).GetComponent<Renderer>().sortingLayerName = spriteRenderer.sortingLayerName;
        //    transform.GetChild(i).GetComponent<Renderer>().sortingOrder = spriteRenderer.sortingOrder + 250;
        //}

        tweenId = "bullet" + GetInstanceID().ToString();
        schedulerId = "bulletS" + GetInstanceID().ToString();
        if (spriteRenderer != null)
        {
            spriteRenderer.sortingLayerName = layerName;
            spriteRenderer.sortingOrder = orderInLayer;
        }
    }

    private void Init()
    {

    }

    private void Update()
    {
        if (isFireBallActive)
        {
            transform.SetRotation(Globals.Modulus(transform.GetRotation(), 360));
            if (moveFireballLeft)
            {
                //bullet.transform.DOMoveX(bullet.transform.position.x - 5, bullet.duration);
                transform.SetWorldPositionX(transform.position.x - Globals.CocosToUnity(5));
                //bulletSprite->setPositionX(bulletSprite->getPosition().x - _speed);
            }
            else
            {
                //bullet.transform.DOMoveX(bullet.transform.position.x + 5, bullet.duration);
                transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(5));
                //bulletSprite->setPositionX(bulletSprite->getPosition().x + _speed);
            }

            //bullet.transform.DOMoveY(bullet.transform.position.y + Globals.CocosToUnity(15) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), bullet.duration);
            transform.SetWorldPositionY(transform.position.y + Globals.CocosToUnity(15) * Mathf.Sin(Mathf.Deg2Rad * transform.GetRotation()));
            transform.SetRotation(transform.GetRotation() + 1.5f);

            //GetRedFireballTrail().transform.position = transform.position;

            if (transform.GetRotation() > 180)
            {
                GetRedFireballTrail().transform.SetRotation(GetRedFireballTrail().transform.GetRotation() + (transform.GetRotation() + 180 - GetRedFireballTrail().transform.GetRotation()) * Time.deltaTime * 10);
            }
            else
            {
                GetRedFireballTrail().transform.SetRotation(GetRedFireballTrail().transform.GetRotation() + ((-transform.GetRotation() + 180 + 360) - GetRedFireballTrail().transform.GetRotation()) * Time.deltaTime * 10);
            }
        }

        //if (_reactToWater)
        //{
        //    if(transform.position.y < Globals.LOWERBOUNDARY)
        //    {
        //        setReactToWater(false);
        //        if (GameManager.instance)
        //        {
        //            GameManager.instance.HitWater(transform.position, 0.3f);
        //        }
        //    }
        //}

        #region 新加技能相关的
        if (bulletType == BulletType.ReflectButtle)
        {
            ReflectMove();
        }
        else if (bulletType == BulletType.Boomerang)
        {
            BoomerangMove();
        }
        else if (bulletType == BulletType.Skyfire)
        {
            SkyFireMove();
        }
        else if (bulletType == BulletType.ProtectiveShield && _startProtonTrun)
        {
            ProtonUpdate();
        }
        else if (bulletType == BulletType.IceBall)
        {
            //IceBallUpdate();
        }
        else if (bulletType == BulletType.HammerBullet)
        {
            HammerMove();
        }
        else if (bulletType == BulletType.Skyfire_new01)//新毒爆弹
        {
            SkyFireMove();
        }

        #endregion
    }

    private IEnumerator BulletAnim(MovementParameters movementParameters)
    {
        Vector2 distanceVector, dest = movementParameters.destination;
        float distanceToTravel, dt, duration = movementParameters.duration;


        while (duration > 0)
        {
            dt = Time.deltaTime < duration ? Time.deltaTime : duration;
            distanceVector = dest - (Vector2)transform.position;
            distanceToTravel = distanceVector.magnitude * (dt / duration);

            transform.position += (Vector3)(distanceVector.normalized * distanceToTravel);

            duration -= dt;

            yield return null;
        }
        StopCoroutine(nameof(ResetBulletProperties));
        StartCoroutine(nameof(ResetBulletProperties));
    }

    #region Public Functions


    public void InitProtonBullet(Action<Bullet> onComplete, Action<Bullet> onBulletHit)
    {
        bulletActionComplete = onComplete;
        setReactToWater(false);
        setDamage(50);
        onHit = onBulletHit;

        spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g,
                spriteRenderer.color.b, 0);
        spriteRenderer.DOFade(1, 0.5f);


        //MotionStreak* streak = MotionStreak::create(0.1f, 1, 45, Color3B::WHITE, "res/Arsenal/blueStreak.png");
    }

    public void InitFireBallDestructionEffect()
    {
        //for (int i = 0; i < 10; i++)
        //{
        //ParticleSystem temp = Instantiate(smokeParticle);
        smokeParticle.gameObject.SetActive(true);
        var main = smokeParticle.main;
        smokeParticle.transform.SetParent(transform);
        main.simulationSpace = ParticleSystemSimulationSpace.Local;
        smokeParticle.transform.position = transform.position;
        main.startSize = new ParticleSystem.MinMaxCurve(3.5f * transform.localScale.x, 3.5f * transform.localScale.x + UnityEngine.Random.value * 1.4f);
        //main.startSize = 3.5f * transform.localScale.x + UnityEngine.Random.value * 1.4f;
        //temp.transform.SetScale(3.5f * transform.localScale.x + UnityEngine.Random.value * 1.4f);
        main.emitterVelocity = new Vector2(-Globals.CocosToUnity(400) + UnityEngine.Random.value * Globals.CocosToUnity(600), -Globals.CocosToUnity(400) + UnityEngine.Random.value * Globals.CocosToUnity(600));
        //temp.transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(400) + UnityEngine.Random.value * Globals.CocosToUnity(600), -Globals.CocosToUnity(400) + UnityEngine.Random.value * Globals.CocosToUnity(600)), 0.5f);
        main.startRotation = new ParticleSystem.MinMaxCurve(UnityEngine.Random.value * 360, UnityEngine.Random.value * 360);
        //temp.transform.SetRotation(UnityEngine.Random.value * 360);
        //temp.transform.DOScale(0, 0.5f + UnityEngine.Random.value * 0.3f).OnComplete(() =>
        //{
        //    Destroy(temp.gameObject);
        //});
        //var main = temp.main;
        //temp.GetComponent<SpriteRenderer>().color = new Color(255, 192, 0);
        //temp.GetComponent<SpriteRenderer>().DOColor(new Color(255, 12, 0), 0.25f);
        //temp.GetComponent<SpriteRenderer>().DOColor(new Color(60, 60, 60), 0.5f);
        //main.startColor = new Color(255, 192, 0);

        //trail->runAction(Sequence::create(TintTo::create(0.25, 255, 12, 0), TintTo::create(0.5, 60, 60, 60), NULL));
        //}
    }

    /// <summary>
    /// 重置子弹属性,相当于子弹刚创建(隐藏、无位置、没开始移动)
    /// </summary>
    IEnumerator ResetBulletProperties()
    {
        yield return new WaitForEndOfFrame();
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        if (raflectTrail != null)
        {
            raflectTrail.SetActive(false);
        }
        isDestroyAfterCollision = true;
        bulletActionComplete?.Invoke(this);
        transform.DOKill();
        spriteRenderer.DOKill();
        //StopCoroutine(nameof(ProtonBulletUpdate));
        if (GetComponent<Animator>() != null)
        {
            GetComponent<Animator>().runtimeAnimatorController = null;
        }
        canCheckIsCriticalHit = false;
        PushBack = 0;
        SetSpriteFrame(bulletSprite);
        spriteRenderer.enabled = true;
        spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g,
                spriteRenderer.color.b, 1);

        transform.position = Vector3.zero;
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        //spriteRenderer.sortingLayerName = "Default";
        //spriteRenderer.sortingOrder = 1;
        hasHit = false;
        isInUse = false;
        isFlipped = false;
        _reactToWater = true;
        canCollionWithNormalEnemyBullet = false;
        setRadiusEffectSquared(defaultRadius);
        if (!isMenuBullet)
            GameSharedData.Instance.RemoveFromList(this);
        gameObject.SetActive(false);

        if (bulletType == BulletType.ProtonCanon)
        {
            bulletType = BulletType.FrontMachineGun;
        }
        if (bulletType == BulletType.EnemyBulletBlueExplosion)
        {
            if (Vector2.SqrMagnitude(GameManager.instance.player.transform.position - transform.position) < Globals.CocosToUnity(225))
            {
                //  SkeletonAnimation *blast = SkeletonAnimation::createWithJsonFile("res/Arsenal/blueBlast.json", "res/Arsenal/blueBlast.atlas");

                //blast->setAnimation(0, "blueBlast", true);
                //Sprite* blast = Sprite::createWithSpriteFrameName("blueBlast_00000.png");
                //blast->runAction(Sequence::create(Shared::createAnimation("blueBlast_0000%d.png", 0, 6, false), RemoveSelf::create(), NULL));
                //this->addChild(blast);
                //blast->setPosition(bullet->bulletSprite->getPosition());
                //blast->setCameraMask(GAMECAMERA);
                //blast->setScale(bullet->bulletSprite->getScaleX() * 2);
                //bullet->bulletSprite->setVisible(false);
                //SkeletonAnimation* blast2 = SkeletonAnimation::createWithJsonFile("res/Arsenal/blast.json", "res/Arsenal/blast.atlas");
                //blast2->setAnimation(0, "plasmaBlast", true);
                //blast2->runAction(Sequence::create(ScaleTo::create(0.2f, 2), DelayTime::create(0.1f), RemoveSelf::create(), NULL));
                //blast2->runAction(FadeOut::create(0.3f));
                //this->addChild(blast2, 5);
                //blast2->setPosition(bullet->bulletSprite->getPosition().x, bullet->bulletSprite->getPosition().y);
                //blast2->setCameraMask(GAMECAMERA);
                //blast2->setScale(bullet->bulletSprite->getScaleX());


            }
        }
        skillData = null;
        penetrationEnemy = new();
        spriteRenderer.sortingLayerName = layerName;
        spriteRenderer.sortingOrder = orderInLayer;
        ResetChildObjects();
        StopAllCoroutines();
    }

    /// <summary>
    /// 设置子弹的不透明度
    /// </summary>
    public void SetOpacity(float opacity)
    {
        opacity = Mathf.Clamp(opacity, 0, 1);
        spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g,
                spriteRenderer.color.b, opacity);
    }

    public void ResetBullet()
    {
        StopCoroutine(nameof(ResetBulletProperties));
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(nameof(ResetBulletProperties));
        }
    }

    public void SetSortingLayer(string layerName, int orderInLayer)
    {
        spriteRenderer.sortingLayerName = layerName;
        spriteRenderer.sortingOrder = orderInLayer;
    }

    //public void PlayBulletAnim(float duration, int speed, Vector2 destination) => StartCoroutine(BulletAnim(duration,speed,destination));
    public void PlayStoneTowerBulletAnim(float duration, Vector2 destination, Sprite sprite, RuntimeAnimatorController runtimeAnimatorController, bool doScaleDown = false,
        Action<Bullet> onComplete = null, Action<Bullet> hitAction = null)
    {
        onHit = hitAction;
        bulletActionComplete = onComplete;
        gameObject.SetActive(true);
        ResetWithDelay();
        transform.DOBlendableMoveBy(destination, duration).SetEase(Ease.OutQuint).OnComplete(() =>
        {
            if (doScaleDown)
                ScaleDown();
            else
            {
                //StopCoroutine(nameof(ResetBulletProperties));
                //StartCoroutine(nameof(ResetBulletProperties));
                RemoveStoneTowerWithSplash(sprite, runtimeAnimatorController);
            }
        });
    }

    public void PlayBulletAnim(float duration, Vector2 destination, bool doScaleDown = false,
        Action<Bullet> onComplete = null, Action<Bullet> hitAction = null, Ease ease = Ease.Linear)
    {
        onHit = hitAction;
        bulletActionComplete = onComplete;
        gameObject.SetActive(true);
        ResetWithDelay();
        if (transform == null)
        {
            return;
        }
        transform.DOBlendableMoveBy(destination, duration).SetEase(ease).OnComplete(() =>
        {
            if (doScaleDown)
                ScaleDown();
            else
            {
                //StopCoroutine(nameof(ResetBulletProperties));
                //StartCoroutine(nameof(ResetBulletProperties));
                RemoveWithSplash();
            }
        });
    }




    public void InitializeBullet()
    {
        gameObject.SetActive(true);
        //StartCoroutine(ResetAfterDuration());
    }

    /// <summary>
    /// 最大存在时长 之后 重置
    /// </summary>
    public void ResetWithDelay()
    {
        StartCoroutine(ResetAfterDuration());
    }

    private IEnumerator ResetAfterDuration()
    {
        yield return new WaitForSeconds(duration);
        RemoveWithSplash();
    }

    public void PlayBulletAnimation(float duration, Vector2 destination, bool doScaleDown = false,
        Action<Bullet> onComplete = null, Action<Bullet> hitAction = null)
    {
        isInUse = true;

        gameObject.SetActive(true);
        var movementParameters = new MovementParameters
        {
            destination = destination,
            duration = duration,
            doScaleDown = doScaleDown,
            onComplete = onComplete
        };
        onHit = hitAction;

        StopCoroutine(nameof(BulletAnim));
        StartCoroutine(nameof(BulletAnim), movementParameters);
    }

    public void ScaleDown()
    {
        transform.DOScale(Vector2.zero, 0.1f).OnComplete(() =>
        {
            StopCoroutine(nameof(ResetBulletProperties));
            if (gameObject != null && gameObject.activeInHierarchy)
            {
                StartCoroutine(nameof(ResetBulletProperties));
            }
        }
        );
    }

    //Stops the bullet and resets its properties. 
    public void HasHit()
    {
        if (!isRemovable)
            return;
        //if(isFireBallActive)
        //{
        //    transform.DOKill();
        //    spriteRenderer.enabled = false;
        //    fireBall.GetComponent<MeshRenderer>().enabled = false;
        //    InitFireBallDestructionEffect();
        //    //var main = fireBall.transform.GetChild(1).GetComponent<ParticleSystem>().main;
        //    //main.loop = false;
        //    isFireBallActive = false;
        //    onHit?.Invoke(this);
        //    DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() =>
        //    {
        //        StopCoroutine(nameof(ResetBulletProperties));
        //        StartCoroutine(nameof(ResetBulletProperties));
        //    });
        //}
        //else
        //{
        transform.DOKill();
        spriteRenderer.enabled = false;
        onHit?.Invoke(this);
        StopCoroutine(nameof(ResetBulletProperties));
        if (gameObject.activeSelf)
        {
            StartCoroutine(nameof(ResetBulletProperties));
        }
        //}
    }
    //粒子炮分裂相关的
    public void RemovePlasmaBullet()
    {
        transform.DOKill();
        spriteRenderer.enabled = false;
        for (int i = 0; i < transform.childCount; i++)
        {
            transform.GetChild(i).gameObject.SetActive(false);
        }
        if (canSplitNum > 0)
        {
            float realSkillSpeed = skillData.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate;
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.deatomizerSound);
            float distance = (Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(skillData.AttackTime));
            int attackCount = skillData.AttackCount;
            float angle = skillData.AttackAngle;
            for (int i = 0; i < attackCount; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                Bullet itemBullet = go.GetComponent<Bullet>();
                itemBullet.SetBulletType(Bullet.BulletType.FrontPlasma);

                //Debug.LogWarning("主技能的id" + skillData.Id.ToString());
                double defaultDamege = skillData.AttackValue;
                defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(skillData.DamageCoe));


                itemBullet.setDamage(defaultDamege);
                itemBullet.isDestroyAfterCollision = false;
                itemBullet.SetSpriteFrame(HotResManager.ReadAtlas("Bullet").GetSprite("jn" + skillData.Id));
                //itemBullet.GetComponent<Animator>().runtimeAnimatorController = gameObject.GetComponent<Animator>().runtimeAnimatorController;
                itemBullet.transform.localScale = (0.8f + (skillData.Level * 0.1f)) * (GameData.instance.fileHandler.currentMission != 0 ? Globals.UnityValueTransform(skillData.BulletScale) : 1) * Vector2.one;
                float duration = GameData.instance.fileHandler.currentMission != 0 ? Globals.UnityValueTransform(skillData.AttackTime) : 0.6f;



                itemBullet.skillData = skillData;
                itemBullet.canSplitNum = canSplitNum - 1;
                Vector3 rotation1 = transform.eulerAngles;
                //发射角度
                Vector3 dest = new Vector2(Mathf.Cos(Mathf.Deg2Rad * rotation1.z), Mathf.Sin(Mathf.Deg2Rad * rotation1.z));
                itemBullet.transform.position = transform.position + dest;

                if (attackCount > 1)
                {
                    //平分120角
                    if (i % 2 == 0)
                    {
                        rotation1.z += Mathf.Floor((i + 1) / 2) * angle;
                    }
                    else
                    {
                        rotation1.z -= Mathf.Floor((i + 1) / 2) * angle;
                    }
                }

                itemBullet.transform.rotation = Quaternion.Euler(rotation1);
                dest = new Vector2(Mathf.Cos(Mathf.Deg2Rad * rotation1.z), Mathf.Sin(Mathf.Deg2Rad * rotation1.z));

                itemBullet.gameObject.SetActive(true);
                itemBullet.PlayBulletAnim(duration, dest * distance, true);
                itemBullet.isInUse = true;
                GameSharedData.Instance.tempPlayerBulletInUse.Add(itemBullet);
            }


        }
        StopCoroutine(nameof(DisplayProtonBulletDestruction));
        StartCoroutine(nameof(DisplayProtonBulletDestruction));
    }

    public void RemoveProtonBullet()
    {
        transform.DOKill();
        spriteRenderer.enabled = false;
        onHit?.Invoke(this);

        StopCoroutine(nameof(DisplayProtonBulletDestruction));
        StartCoroutine(nameof(DisplayProtonBulletDestruction));
    }

    IEnumerator DisplayProtonBulletDestruction()
    {
        yield return new WaitForSeconds(0);
        StopCoroutine(nameof(ResetBulletProperties));
        StartCoroutine(nameof(ResetBulletProperties));
    }

    public void RemoveWithSplash()
    {
        transform.DOKill();
        spriteRenderer.enabled = false;
        for (int i = 0; i < transform.childCount; i++)
        {
            transform.GetChild(i).gameObject.SetActive(false);
        }
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StopCoroutine(nameof(DisplaySplash));
            StartCoroutine(nameof(DisplaySplash));
        }
    }
    public void RemoveStoneTowerWithSplash(Sprite splash, RuntimeAnimatorController animatorController)
    {
        transform.DOKill();
        spriteRenderer.enabled = false;
        for (int i = 0; i < transform.childCount; i++)
        {
            transform.GetChild(i).gameObject.SetActive(false);
        }

        StopCoroutine(nameof(DisplayStoneTowerSplash));
        StartCoroutine(DisplayStoneTowerSplash(splash, animatorController));
    }

    IEnumerator DisplayStoneTowerSplash(Sprite splash, RuntimeAnimatorController animatorController)
    {
        splashAnim.gameObject.GetComponent<SpriteRenderer>().sprite = splash;
        splashAnim.gameObject.SetActive(true);
        splashAnim.runtimeAnimatorController = animatorController;
        splashAnim.Play("On", 0, 0);
        yield return new WaitForSeconds(0.133f);
        HasHit();
    }

    IEnumerator DisplaySplash()
    {
        if (bulletType == BulletType.EnemyBulletFireball)
        {
            SetBulletChild(BulletChild.FireBall);
            yield return new WaitForSeconds(0.8f);
        }
        else if (bulletType == BulletType.EnemyBulletFireballTrail)
        {
            SetBulletChild(BulletChild.FireballParticle);
            yield return new WaitForSeconds(0.8f);
        }
        else
        {
            splashAnim.gameObject.SetActive(true);
            splashAnim.Play("On", 0, 0);
            yield return new WaitForSeconds(0.133f);
        }
        HasHit();
    }

    public void SetBulletType(BulletType type)
    {
        bulletType = type;
    }

    public void setDamage(double dmg)
    {
        _damage = dmg;
    }

    public double getDamage()
    {
        return _damage;
    }

    public void setReactToWater(bool val)
    {
        _reactToWater = val;
    }

    public bool getReactToWater()
    {
        return _reactToWater;
    }

    public void setRadiusEffectSquared(float r)
    {
        _radiusEffect = r;
        _radiusEffectSquared = r * r;
    }

    public float getRadiusEffect()
    {
        return _radiusEffect;
    }

    public float getRadiusEffectSquared()
    {
        return _radiusEffectSquared;
    }

    public void SetSpriteFrame(Sprite bulletSprite)
    {
        spriteRenderer.sprite = bulletSprite;
    }

    public TrailRenderer GetTrailRenderer()
    {
        return trail;
    }

    public SpriteRenderer GetSpriteRendererComponent()
    {
        return spriteRenderer;
    }

    public SpriteRenderer GetLightning()
    {
        return lightning;
    }

    public Animator GetFlameThrower()
    {
        return flameThrower;
    }

    public SkeletonAnimation GetRedFireballTrail()
    {
        fireBall.transform.GetChild(1).gameObject.SetActive(true);
        return fireBall;
    }

    public SkeletonAnimation GetBlueFireBallTrail()
    {
        fireBall.transform.GetChild(0).gameObject.SetActive(true);
        return fireBall;
    }
    #endregion


    struct MovementParameters
    {
        public float duration;
        public Vector2 destination;
        public bool doScaleDown;
        public Action<Bullet> onComplete;
    }


    /// <summary>
    /// 激活子弹的一个下级
    /// </summary>
    public void SetBulletChild(BulletChild child)
    {
        //if ((int)child <= childObject.Length)
        //{
        //    childObject[(int)child].SetActive(true);
        //}

        //if ((int)child == childObject.Length)
        //{
        //}

        foreach (BulletChildStruct b in bulletChildren)
        {
            if (b.childType == child)
            {
                b.child.SetActive(true);
            }
        }
    }

    public GameObject GetBulletChild(BulletChild child)
    {
        if ((int)child <= childObject.Length)
        {
            return childObject[(int)child];
        }
        else
            return null;
    }

    /// <summary>
    /// 隐藏所有下级(仅一层,非递归)
    /// </summary>
    private void ResetChildObjects()
    {
        foreach (GameObject obj in childObject)
        {
            if (obj == null) continue;
            if (obj.activeSelf)
            {
                obj.SetActive(false);
            }
        }

        foreach (BulletChildStruct b in bulletChildren)
        {
            if (b.child == null) continue;
            if (b.child.activeSelf)
            {
                b.child.SetActive(false);
            }
        }
    }





    #region 新技能相关的模块
    [HideInInspector] public float reflectBulletDuration;
    private Vector3 _reflectDirction;
    private float _leftBorder;
    private float _rightBorder;
    private float _topBorder;
    private float _downBorder;
    private float _speed;
    [HideInInspector] public bool isDestroyAfterCollision;

    private bool _isMove2Destination;
    private Vector2 _boomerangDestination;

    private float _skyFireMoveTotalTime;
    private float _skyFireMoveCurrentTime;
    private Vector3 _skyFireMoveStart;
    private Vector3 _skyFireMoveEnd;
    private Vector3 _skyFireMoveCenter;
    private double _skyFireDamage;
    private float _skyFireRadius;
    private float _skyFireTime;

    private float _iceBallShootInterval;
    private float _iceBulletTime;
    private int _iceBulletNum;
    private float _iceBulletBulletAngle;
    private float _iceBulletScale;
    private double _iceBulletDameage;
    private float _intervalTime = 0.1f;

    private ProtonBulletSlot[] protonBulletSlots;

    public void SetIceBallParam(float duration, Vector2 destination, float speed, int bulletNum, float angle, float scale, double damage, float intervalTime)
    {
        _speed = speed;
        _iceBulletTime = duration;
        transform.DOBlendableMoveBy(destination, 3).SetEase(Ease.Linear).OnComplete(() =>
        {
            ResetBullet();
        });
        _iceBallShootInterval = 0.5f;
        _iceBulletNum = bulletNum;
        _iceBulletBulletAngle = angle;
        _iceBulletScale = scale;
        isDestroyAfterCollision = false;
        _iceBulletDameage = damage;
        _intervalTime = intervalTime / 1000;

        StartCoroutine(nameof(ShootIceBullet2));
    }
    private void IceBallUpdate()
    {
        transform.Rotate(0, 0, 15f * Time.deltaTime);
        _iceBallShootInterval -= Time.deltaTime;
        if (_iceBallShootInterval <= 0)
        {
            ShootIceBullet();
            _iceBallShootInterval = 0.5f;
        }
    }

    private void ShootIceBullet()
    {
        //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6020);
        for (int i = 0; i < _iceBulletNum; i++)
        {
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.IceBullet);
            Bullet bullet = go.GetComponent<Bullet>();
            bullet.skillData = skillData;
            bullet.SetBulletType(Bullet.BulletType.IceBullet);

            bullet.setDamage(_iceBulletDameage);
            bullet.isRemovable = true;
            bullet.isDestroyAfterCollision = true;
            bullet.transform.localScale = Vector3.one * 0.5f * _iceBulletScale;
            float distance = _speed * _iceBulletTime;
            bullet.setReactToWater(true);
            float duration = _iceBulletTime;
            bullet.transform.position = transform.position;
            Vector3 rotation1 = transform.eulerAngles;
            rotation1.z += i * _iceBulletBulletAngle;
            bullet.transform.rotation = Quaternion.Euler(rotation1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
            Vector2 dest = new Vector2(distance
                * Mathf.Cos(Mathf.Deg2Rad * rotation1.z),
                distance * Mathf.Sin(Mathf.Deg2Rad * rotation1.z));
            bullet.gameObject.SetActive(true);
            bullet.SetIceBulletParam(duration, dest);

            bullet.isInUse = true;
            GameSharedData.Instance.playerBulletInUse.Add(bullet);
        }
    }

    /// <summary>
    /// GN冰晶
    /// </summary>
    /// <returns></returns>
    private IEnumerator ShootIceBullet2()
    {
        //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6020);
        //yield return new WaitForSeconds(1f);
        bool isMaxLevel = skillData.NextLv == 0;
        for (int i = 0; i < _iceBulletNum; i++)
        {
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.IceBullet);
            Bullet bullet = go.GetComponent<Bullet>();
            bullet.skillData = skillData;
            bullet.SetBulletType(Bullet.BulletType.IceBullet);
            bullet.SetSpriteFrame(HotResManager.ReadAtlas("Bullet").GetSprite("jn" + skillData.Id));
            bullet.setDamage(_iceBulletDameage);
            bullet.isRemovable = true;
            bullet.isDestroyAfterCollision = true;
            bullet.transform.localScale = Vector3.one * 0.5f * _iceBulletScale;
            float distance = _speed * _iceBulletTime;
            bullet.setReactToWater(true);
            bullet.transform.position = transform.position;
            Vector3 rotation1 = transform.eulerAngles;
            rotation1.z += i * _iceBulletBulletAngle;
            bullet.transform.rotation = Quaternion.Euler(rotation1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
            Vector2 dest = new Vector2(Mathf.Cos(Mathf.Deg2Rad * rotation1.z), Mathf.Sin(Mathf.Deg2Rad * rotation1.z)) * distance;
            bullet.gameObject.SetActive(true);
            bullet.SetIceBulletParam(_iceBulletTime, dest);

            bullet.isInUse = true;
            GameSharedData.Instance.playerBulletInUse.Add(bullet);

            if (isMaxLevel)
            {
                go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.IceBullet);
                bullet = go.GetComponent<Bullet>();
                bullet.skillData = skillData;
                bullet.SetBulletType(Bullet.BulletType.IceBullet);
                bullet.SetSpriteFrame(HotResManager.ReadAtlas("Bullet").GetSprite("jn" + skillData.Id));
                bullet.setDamage(_iceBulletDameage);
                bullet.isRemovable = true;
                bullet.isDestroyAfterCollision = true;
                bullet.transform.localScale = Vector3.one * 0.5f * _iceBulletScale;
                distance = _speed * _iceBulletTime;
                bullet.setReactToWater(true);
                bullet.transform.position = transform.position;
                rotation1 = transform.eulerAngles;
                rotation1.z += 180 + i * _iceBulletBulletAngle;
                bullet.transform.rotation = Quaternion.Euler(rotation1);
                bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
                dest = new Vector2(Mathf.Cos(Mathf.Deg2Rad * rotation1.z), Mathf.Sin(Mathf.Deg2Rad * rotation1.z)) * distance;
                bullet.gameObject.SetActive(true);
                bullet.SetIceBulletParam(_iceBulletTime, dest);

                bullet.isInUse = true;
                GameSharedData.Instance.playerBulletInUse.Add(bullet);
            }
            yield return new WaitForSeconds(_intervalTime);
        }
    }

    public void SetIceBulletParam(float duration, Vector2 dest)
    {
        transform.DOBlendableMoveBy(dest, duration).SetEase(Ease.Linear).OnComplete(() =>
        {
            ResetBullet();
        });
    }

    /// <summary>
    /// 反弹子弹
    /// </summary>
    public void SetReflectBulletParam(float duration, float speed)
    {
        //Debug.Log("SetReflectBulletParam--duration=" + duration + "   speed=" + speed);
        reflectBulletDuration = duration;
        _speed = speed;
        _reflectDirction = (Quaternion.Euler(transform.rotation.eulerAngles) * new Vector3(1, 0, 0)).normalized;
        //GetRedFireballTrail();
        //raflectTrail.SetActive(true);
        isDestroyAfterCollision = false;
    }
    public void SetRefectRota(bool isRota)
    {
        isReflectRota = isRota;
    }

    public void ReflectMove()
    {
        if (!isInUse)
        {
            return;
        }
        reflectBulletDuration -= Time.deltaTime;
        if (reflectBulletDuration <= 0f)
        {
            ResetBullet();
        }
        //屏幕边缘转向
        float[] border = Globals.GetScreenBorder();
        _topBorder = border[0];
        _downBorder = border[1] + 15.5f;
        _leftBorder = border[2];
        _rightBorder = border[3];
        //Debug.Log(_topBorder+"   "+ _downBorder + "   "+ _leftBorder+"   " + _rightBorder);
        if (transform.position.y >= _topBorder || transform.position.y <= _downBorder || transform.position.x >= _rightBorder || transform.position.x <= _leftBorder)
        {
            DoReflection();
        }
        transform.Translate(_reflectDirction.normalized * Time.deltaTime * _speed, Space.World);
        if (isReflectRota)
        {
            transform.RotateAround(transform.localPosition, Vector3.forward, 30f);
        }
    }

    public void DoReflection()
    {
        _reflectDirction = GameManager.instance.player.transform.position - transform.position;

        Vector3 i = _reflectDirction.normalized;
        Vector3 n = -_reflectDirction.normalized;

        i.x = UnityEngine.Random.Range(-10f, 10f);
        i.y = UnityEngine.Random.Range(-10f, 10f);

        float a = Vector3.Dot(i, n);
        float h = 0.5f;
        float b = 1.0f - h * h * (1.0f - a * a);
        Vector3 t = h * i - (h * a + Mathf.Sqrt(b)) * n;
        if (b > 0)
        {
            _reflectDirction = t;
        }
        else
        {
            _reflectDirction = Vector3.zero;
        }
        transform.right = _reflectDirction.normalized;
    }




    [HideInInspector] public float angularVelocity, angularAcceleration, rotationSmoothness;
    private bool startRevolution = false;

    public Sequence BoomerangMoveSeq;

    /// <summary>
    /// 回旋镖子弹
    /// </summary>
    public void SetBoomerangParam(float duration, Vector2 destination, Vector3 scale, int level, float distance)
    {
        isDestroyAfterCollision = false;
        angularVelocity = -720;
        angularAcceleration = -100;
        rotationSmoothness = 0.8f;
        startRevolution = true;
        BoomerangMoveSeq?.Kill();
        BoomerangMoveSeq = DOTween.Sequence();
        BoomerangMoveSeq.Append(transform.DOBlendableMoveBy(destination, duration).SetEase(Ease.OutQuad));
        Sequence seq = DOTween.Sequence();
        seq.AppendInterval(duration);
        seq.AppendCallback(() =>
        {
            startRevolution = false;
            var dest = new Vector2(distance * Mathf.Cos(Mathf.Deg2Rad * transform.eulerAngles.z), distance * Mathf.Sin(Mathf.Deg2Rad * transform.eulerAngles.z));
            transform.DOBlendableMoveBy(dest, duration).SetEase(Ease.OutQuad).OnComplete(() =>
            {
                ResetBullet();
            });
        });

        var go = new GameObject();
        go.transform.SetParent(transform, false);
        go.transform.position = transform.position;
        GameSharedData.Instance.skyFireBoomPool.Add(new SkyFireStruct(go, _damage, duration * 2, Globals.UnityValueTransform(skillData.DamageRadius)));
    }
    public void SetBoomerangParamMax(Enemy near, float rot, float distance, float time, float showTime, float scale)
    {
        float dir = 0;
        if (near != null)
        {
            dir = Vector2.SignedAngle(Vector2.right, (Vector2)near.transform.position - (Vector2)transform.position);
        }
        else
        {
            dir = rot;
        }
        //raflectTrail.SetActive(true);

        Sequence seq = DOTween.Sequence();
        seq.AppendInterval(showTime);
        seq.AppendCallback(() =>
        {
            transform.DOScale(new Vector3(scale, scale, scale), 0.15f).SetEase(Ease.InOutQuad);
        });
        seq.AppendInterval(1f);
        seq.AppendCallback(() =>
        {
            isInUse = true;
            isDestroyAfterCollision = false;
            GameSharedData.Instance.playerBulletInUse.Add(this);
            transform.rotation = Quaternion.AngleAxis(dir, Vector3.forward);
            Vector3 newRot = transform.eulerAngles;
            Vector2 dest = (new Vector2(distance * Mathf.Cos(Mathf.Deg2Rad * newRot.z), distance * Mathf.Sin(Mathf.Deg2Rad * newRot.z))) * (1 + UnityEngine.Random.value);

            transform.DOBlendableMoveBy(dest, time).SetEase(Ease.OutQuad).OnComplete(() =>
            {
                ResetBullet();
            });

        });
    }


    private void BoomerangMove()
    {
        if (!isInUse || !startRevolution)
        {
            return;
        }
        //transform.Rotate(0, 0, 15f * Time.deltaTime);
        angularVelocity += angularAcceleration * Time.deltaTime;
        var currentAngle = transform.eulerAngles.z;
        var targetAngle = currentAngle + angularVelocity * Time.deltaTime;
        var smoothAngle = Mathf.LerpAngle(currentAngle, targetAngle, rotationSmoothness);
        transform.eulerAngles = new Vector3(0, 0, smoothAngle);

    }

    /// <summary>
    /// 雷神之锤
    /// </summary>
    public void SetHammerParam(float duration, Vector2 destination, Vector3 scale, int level)
    {
        //GetComponent<Animator>().runtimeAnimatorController = leishenzhichuiAnimator.runtimeAnimatorController;
        isDestroyAfterCollision = false;
        angularVelocity = 2700;
        angularAcceleration = -500;
        rotationSmoothness = 0.8f;

        //_isMove2Destination = true;   
        Sequence seq = DOTween.Sequence();
        seq.Append(transform.DOMove(destination, duration).SetEase(Ease.InCubic));
        seq.Join(transform.DOScale(scale, duration).SetEase(Ease.Linear));
        seq.AppendInterval(1f);
        seq.AppendCallback(() =>
        {
            GameManager.instance.ShakeCamera(0.5f, 1);
            ResetBullet();
        });


    }

    private void HammerMove()
    {
        if (!isInUse)
        {
            return;
        }
        //angularVelocity += angularAcceleration * Time.deltaTime;
        //var currentAngle = transform.eulerAngles.z;
        //var targetAngle = currentAngle + angularVelocity * Time.deltaTime;
        //var smoothAngle = Mathf.LerpAngle(currentAngle, targetAngle, rotationSmoothness);
        //transform.eulerAngles = new Vector3(0, 0, smoothAngle);

    }


    /// <summary>
    /// 天火子弹
    /// </summary>
    public void SetSkyfireParam(float t, int index, int total, GameObject prefab, double boomDamage, float boomRadius, float skyTime, Vector3 moveEnd,GameObject skyBoomParent)
    {
        _skyFireMoveStart = GameManager.instance.player.transform.position;
        _skyFireMoveEnd = moveEnd;
        _skyFireMoveTotalTime = t;
        _skyFireMoveCurrentTime = 0f;
        _skyFireMoveCenter = (_skyFireMoveStart + _skyFireMoveEnd) * 0.5f + (Vector3.up * 10);
        isDestroyAfterCollision = false;
        _boomPrefab = prefab;
        _skyFireDamage = boomDamage;
        _skyFireRadius = boomRadius;
        _skyFireTime = skyTime;
        _skyBoomParent = skyBoomParent;
        preBulletPosition = transform.position;
    }

    private Vector3 GetRandomPosFormScreen()
    {
        float[] borders = Globals.GetScreenBorder();
        return new Vector3(UnityEngine.Random.Range(borders[2], borders[3]), UnityEngine.Random.Range(borders[1], borders[0]), 0);
    }





    /// <summary>
    /// 获取一个圆圈上平均的点
    /// </summary>
    /// <param name="i">第几个点[0,x]</param>
    /// <param name="count">总共多少点</param>
    private Vector3 GetCirclePos(Vector3 center_pos, int i, int count, float radius)
    {
        float offset = 360f / count;
        float x = center_pos.x + radius * Mathf.Cos(i * offset * Mathf.PI / 180f + 90f);
        float y = center_pos.y + radius * Mathf.Sin(i * offset * Mathf.PI / 180f + 90f);
        return new Vector3(x, y, 0);
    }

    private void SkyFireMove()
    {
        _skyFireMoveCurrentTime += Time.deltaTime;
        if (_skyFireMoveCurrentTime >= _skyFireMoveTotalTime)
        {
            CreateSkyBoom();
            ResetBullet();
        }
        else
        {
            float t = _skyFireMoveCurrentTime / _skyFireMoveTotalTime;
            Vector3 newPos = (1 - t) * (1 - t) * _skyFireMoveStart + 2 * t * (1 - t) * _skyFireMoveCenter + t * t * _skyFireMoveEnd;
            Vector3 dir = newPos - preBulletPosition;
            transform.right = -dir.normalized;
            preBulletPosition = newPos;
            transform.position = newPos;
        }


    }

    public void SetProtectiveShield(ProtonBulletSlot[] ProtonBulletSlots, bool isMax, float maxRadius, float turnSpeed)
    {
        protonBulletSlots = ProtonBulletSlots;
        isDestroyAfterCollision = false;
        _protonCurrentDistance = 0f;
        _protonMaxDistance = maxRadius;
        _startProtonTrun = true;
        _isProtonDistanceAdd = true;
        _isProtectiveShieldMaxLevel = isMax;
        _protectiveShieldturnSpeed = turnSpeed;
    }

    /// <summary>
    /// 离子副武器旋转
    /// </summary>
    private void ProtonUpdate()
    {
        if (!isInUse)
        {
            return;
        }
        if (_isProtectiveShieldMaxLevel)
        {
            if (_isProtonDistanceAdd)
            {
                _protonCurrentDistance += Time.deltaTime * _protectiveShieldturnSpeed;
            }
            else
            {
                _protonCurrentDistance -= Time.deltaTime * _protectiveShieldturnSpeed;
            }
            if (_protonCurrentDistance >= _protonMaxDistance)
            {
                _isProtonDistanceAdd = false;
            }
            else if (_protonCurrentDistance <= 0)
            {
                _isProtonDistanceAdd = true;
            }
        }

        if (protonBulletSlots != null)
        {

            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                if (protonBulletSlots[i].isOccupied)
                {
                    protonBulletSlots[i].bullet.transform.position = GameManager.instance.player.transform.position * Vector2.one
                + new Vector2(_protonMaxDistance * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                _protonMaxDistance * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                }

                protonBulletSlots[i].angle -= _protectiveShieldturnSpeed * Time.deltaTime * 60;
                protonBulletSlots[i].angle = protonBulletSlots[i].angle < 0 ? 360 + protonBulletSlots[i].angle
                    : protonBulletSlots[i].angle;
            }
        }

    }

    private void CreateSkyBoom()
    {
        GameObject go = Instantiate<GameObject>(_boomPrefab);
        go.transform.SetParent(_skyBoomParent.transform);
        go.transform.position = transform.position;
        GameSharedData.Instance.skyFireBoomPool.Add(new SkyFireStruct(go, _skyFireDamage, _skyFireTime, _skyFireRadius));

    }


    #endregion

    /// <summary>
    /// 子弹穿透伤害间隔时间
    /// </summary>
    public int penetrationIntervalTime;

    /// <summary>
    /// 穿透过的敌人集合
    /// </summary>
    Dictionary<Enemy, uint> penetrationEnemy = new();

    /// <summary>
    /// 检测敌人是否被子弹穿透过
    /// </summary>
    /// <param name="enemy"></param>
    /// <returns></returns>
    public bool PenetrationDetection(Enemy enemy)
    {
        if (enemy)
        {
            var now = Premier.Instance.GetServerTime();
            //有子弹穿透记录
            if (penetrationEnemy.ContainsKey(enemy))
            {
                float time = 3f;
                if (penetrationIntervalTime == 0 && skillData != null)
                {
                    time = Globals.UnityValueTransform(skillData.AttackTime);
                }
                //判断穿透间隔时间
                if ((now - penetrationEnemy[enemy]) >= time)
                {
                    penetrationEnemy[enemy] = now;
                    return true;
                }
            }
            else
            {
                penetrationEnemy.Add(enemy, now);
                return true;
            }
        }
        return false;
    }
}
