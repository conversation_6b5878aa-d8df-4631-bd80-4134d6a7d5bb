using Spine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WXR_Katlando : Sidekick
{
    [HideInInspector] public Vector2 direction;
    [HideInInspector] public bool shootLeft;
    private void Start()
    {
        Init();

    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        scheduleUpdate = false;
        shootLeft = true;
        //transform.localScale = new Vector3(scale, scale, 1);
        SetStartingPosition();
    }

    public override void StartSidekick()
    {
        InvokeRepeating("Shoot", 0.08f, 0.08f);
    }


    void ShootEvent(TrackEntry trackEntry, Spine.Event e)
    {
        if (e.Data.Name == "shoot")
            Shoot();
    }

    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;

        if (!Globals.beginSidekickShoot)
            return;

        PlayerMissile missile = null;
        bool didFindBullet = false;
        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                didFindBullet = true;
                break;
            }
        }
        Debug.Log("didFindBullet=" + didFindBullet);

        if (!didFindBullet)
        {
            return;
        }
        missile.SurvivalTime = 2f;
        missile.initSpeed = 100f;
        missile.Init();
        if (spriteFrame) missile.SetSpriteFrame(spriteFrame);
        missile.isInUse = true;
        missile.damage = Helper.GetPlayerBulletRealDamage(damage);
        missile.transform.position = (Vector2)transform.position;
        missile.transform.localScale = Vector3.one * 2.5f;
        GameSharedData.Instance.playerMissilesInUse.Add(missile);
    }

}
