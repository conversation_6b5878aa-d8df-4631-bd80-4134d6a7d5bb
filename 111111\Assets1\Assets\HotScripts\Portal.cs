using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Spine;
using Spine.Unity;
using UnityEngine;

public class Portal : Enemy
{
    public enum PortalStates
    {
        CUTUES_FORM_L,
        CUTUES_FORM_F,
        SPIDER_LASER,
        SPIDER_BULLET,
        SENTINEL_BOTS,
        FIREBALL_1,
        FIREBALL_2,
        LASER,
        DEAD
    }

    [SerializeField] Collider2D boundsCollider;
    [SerializeField] Sprite bulletEnemySprite, creepyBulletSprite;
    [SerializeField] private Transform bladesContainer, lasersContainer;
    [SerializeField] private List<Blade> bladeList = new List<Blade>();
    [SerializeField] private LightningAttack[] lightning;
    [SerializeField] private List<Laser> lasers;
    [SerializeField] private List<SpriteRenderer> strobes;
    [SerializeField] private GameObject lightningContainer;

    [HideInInspector] public Vector2 PortalMid;
    Bounds bounds;
    Bone _bone;
    PortalStates _currentState;
    List<SkeletonAnimation> bladeArray;
    int _bombersCount;
    private int lightningCount;
    bool isInvulnerable = true;

    Sequence checkForSpiderLaser, spawnBombers, checkToActivateBoundary;

    const int TOTAL_NUMBER_OF_BOMBERS = 20;
    const float ATTACK_DELAY = 2.0f;
    const string tweenID = "Portal", schedulerID = "PortalSchedules";

    void OnExit()
    {
        bladeArray.Clear();
        //Node.onExit();
    }

    private IEnumerator NextFrameGetBounds()
    {
        yield return null;
        bounds = boundsCollider.bounds;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        //SetAllowRelocate(false); TODO
        _bombersCount = TOTAL_NUMBER_OF_BOMBERS;
        explosionType = Explosions.ExplosionType.ExplosionTypeBuilding;
        bladeArray = new List<SkeletonAnimation>();
        bladesContainer.parent = null;
        lasersContainer.parent = null;
        lightningContainer.transform.parent = null;
        InitLasers();
        enemySchedulerSpeed = 0;
        scheduleUpdate = true;
        explosionType = (Explosions.ExplosionType)15; // TODO 

        enemySprite.state.SetAnimation(0, "emerge", false);
        enemySprite.state.AddAnimation(0, "idle", true);
        transform.position = new Vector3(player.transform.position.x + Globals.CocosToUnity(500),
            Globals.LOWERBOUNDARY - Globals.CocosToUnity(50), transform.position.z);

        _bone = enemySprite.skeleton.FindBone("lights");
        var bonePos = _bone.GetWorldPosition(enemySprite.transform);

        PortalMid = new Vector2(bonePos.x, bonePos.y);
        enemySprite.state.Event += PortalEventHandler;

        GameSharedData.Instance.enemyList.Remove(this);

        //healthBar.setPositionY(-50);
        healthBar.gameObject.SetActive(false);
        //healthBar.setScaleRatio(2);
        //healthBar.setPosition(enemySprite.getPosition().x - 100, -50);
    }

    private void OnDisable()
    {
        enemySprite.state.Event -= PortalEventHandler;
    }

    void PortalEventHandler(TrackEntry entry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shootBlades")
        {
            CreateBlades();
        }
        if (spineEvent.Data.Name == "shootLightning")
        {
            if (_currentState == PortalStates.CUTUES_FORM_L)
            {
                DOTween.Sequence().SetId(schedulerID).AppendCallback(CreateLightning).AppendInterval(0.15f).SetLoops(7);
            }
            else if (_currentState == PortalStates.FIREBALL_1)
            {
                ShootFireballAllDirections();
            }
        }
        if (spineEvent.Data.Name == "shootFireballs")
        {
            ShootFireballUp();
        }
        if (spineEvent.Data.Name == "shootLaser")
        {
            CreateLaser();
        }

        if (spineEvent.Data.Name == "shake")
        {
            GameManager.instance.ShakeCamera(0.8f, 8);
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
            //Shared.playSound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        }
    }

    public void StartPortal()
    {
        GameSharedData.Instance.enemyList.Add(this);
        StartCoroutine(NextFrameGetBounds());
        enemySprite.state.SetAnimation(0, "talk", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        enemySprite.state.AddAnimation(0, "idle", false);
        DOTween.Sequence().SetId(schedulerID).AppendInterval(2).AppendCallback(() =>
        {
            checkToActivateBoundary = DOTween.Sequence().SetId(schedulerID)
            .AppendCallback(CheckToActivateBoundary).AppendInterval(0.45f).SetLoops(-1);
        });
        _currentState = PortalStates.CUTUES_FORM_F;
        ChangeState();
        isBoss = true;
        isInvulnerable = false;

        //BossHud hud = BossHud.createWithEnemy(this);
        //this.addChild(hud);
        //hud.setSkin("Blu");
    }

    void CreateLightning()
    {
        lightning[lightningCount].PlayLightningAnim();
        lightningCount++;
        if (lightningCount == 7)
        {
            lightningCount = 0;
        }
    }

    void CheckToActivateBoundary()
    {
        if (Mathf.Abs(player.transform.position.x - transform.position.x) < Globals.CocosToUnity(1500))
        {
            checkToActivateBoundary.Kill();

            {
                var node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                node.Init();
                node.boundaryPosX = transform.position.x - Globals.CocosToUnity(3000);
                node._isLeft = 1;
                //node.stats.health = 100000; TODO
                //node.stats.maxHealth = node.stats.health; TODO
                Globals.LEFTBOUNDARY = transform.position.x - Globals.CocosToUnity(2500);
                node.AllowBoundaryChange = true;

                DOTween.Sequence().SetId("BoundarySpiders").AppendInterval(20).AppendCallback(() =>
                {
                    node._isLeft = 3;
                    node.StopAllActions();
                    node.enemySprite.state.SetAnimation(0, "littleLaserOn", true);
                    node.AllowBoundaryChange = false;
                });
            }

            {
                var node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                node.Init();
                node.boundaryPosX = transform.position.x + Globals.CocosToUnity(3000);
                node._isLeft = 0;
                //node.stats.health = 100000; TODO
                //node.stats.maxHealth = node.stats.health; TODO
                Globals.RIGHTBOUNDARY = transform.position.x + Globals.CocosToUnity(2500);
                node.AllowBoundaryChange = true;

                DOTween.Sequence().SetId("BoundarySpiders").AppendInterval(20).AppendCallback(() =>
                {
                    node._isLeft = 3;
                    node.StopAllActions();
                    node.enemySprite.state.SetAnimation(0, "littleLaserOn", true);
                    node.AllowBoundaryChange = false;
                });
            }
        }
    }

    void CreateBlades()
    {
        int bladeCount = 0;
        for (int i = 0; i < bladeList.Count; i++)
        {
            if (!bladeList[i].isInUse)
            {
                bladeList[i].isInUse = true;
                //bone = bladeList[i].skeletonAnimation.skeleton.FindBone("root");
                //float originalRotation = bone.rotation;
                bladeList[i].transform.SetRotation(-85 + (i * 85));
                bladeList[i].transform.position = PortalMid;
                bladeList[i].meshRenderer.enabled = true;
                bladeList[i].gameObject.SetActive(true);
                bladeList[i].skeletonAnimation.state.SetAnimation(0, "idle", false);
                PlayBladeAnim(bladeList[i]);

                bladeCount++;
            }
            if (bladeCount == 3)
            {
                break;
            }
        }
    }

    private void PlayBladeAnim(Blade obj)
    {
        Sequence seq = DOTween.Sequence().SetId(obj.GetTweenID())
                    .Append(obj.transform.DOBlendableMoveBy(new Vector3(Globals.CocosToUnity(-3000) * Mathf.Sin(Mathf.Deg2Rad * obj.transform.eulerAngles.z),
                    Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * obj.transform.eulerAngles.z)), 4))
                    .OnComplete(() =>
                    {
                        obj.skeletonAnimation.state.SetEmptyAnimation(0, 0);
                        obj.transform.eulerAngles = Vector3.zero;
                        obj.transform.localPosition = Vector3.zero;
                        obj.isInUse = false;
                        obj.meshRenderer.enabled = false;
                        obj.gameObject.SetActive(false);
                        //bone.rotation = rotation;

                    }).Play();
    }

    void ChangeState()
    {
        switch (_currentState)
        {
            case PortalStates.CUTUES_FORM_F:
                _currentState = PortalStates.CUTUES_FORM_L;
                enemySprite.state.SetAnimation(0, "shootLightning", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                DOTween.Sequence().SetId(tweenID).AppendInterval(3.5f).AppendCallback(ChangeState);
                break;
            case PortalStates.CUTUES_FORM_L:
                _currentState = PortalStates.CUTUES_FORM_F;
                enemySprite.state.SetAnimation(0, "shootBlades", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                DOTween.Sequence().SetId(tweenID).AppendInterval(2.5f).AppendCallback(ChangeState);
                break;
            case PortalStates.SENTINEL_BOTS:
                spawnBombers = DOTween.Sequence().SetId(schedulerID).AppendInterval(0.2f).AppendCallback(SpawnBombers).SetLoops(-1);
                break;
            case PortalStates.FIREBALL_1:
                _currentState = PortalStates.FIREBALL_2;
                enemySprite.state.SetAnimation(0, "shootFireballs", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                DOTween.Sequence().SetId(tweenID).AppendInterval(6).AppendCallback(ChangeState);
                DOTween.Sequence().SetId(tweenID).AppendInterval(5).AppendCallback(() =>
                {
                    DOTween.Sequence().SetId(tweenID).AppendCallback(CreateSkyFireballs).AppendInterval(0.2f).SetLoops(25);
                });
                break;

            case PortalStates.FIREBALL_2:
                _currentState = PortalStates.FIREBALL_1;
                enemySprite.state.SetAnimation(0, "shootLightning", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                DOTween.Sequence().SetId(tweenID).AppendInterval(3.5f).AppendCallback(ChangeState);
                break;

            case PortalStates.LASER:
                enemySprite.state.SetAnimation(0, "shootLaser", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                DOTween.Sequence().SetId(tweenID).AppendInterval(3.5f).AppendCallback(ChangeState);
                break;
            case PortalStates.SPIDER_LASER:
                {
                    LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS",null,true) as LittleSpiders;
                    node.CreateWithType(SpiderType.LASER);
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                }

                {
                    LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS",null,true) as LittleSpiders;
                    node.CreateWithType(SpiderType.LASER);
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                }

                {
                    LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                    node.CreateWithType(SpiderType.GUN);
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                }

                {
                    LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                    node.CreateWithType(SpiderType.GUN);
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                }
                break;
            default:
                break;
        }
    }

    void CreateSkyFireballs()
    {
        Bullet bullet = null;

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.gameObject.SetActive(true);

        bullet.setDamage(175);
        bullet.SetSpriteFrame(null);

        bullet.SetOpacity(1);

        bullet.transform.position =
                new Vector3(player.transform.position.x - Globals.CocosToUnity(1000) + Random.value * Globals.CocosToUnity(2000),
                Globals.CocosToUnity(1850), bullet.transform.position.z);
        bullet.PlayBulletAnim(4, new Vector2(0, -Globals.CocosToUnity(6500)));

        var fireball = bullet.transform.GetChild(4);
        fireball.gameObject.SetActive(true);
        var fireballSkeleton = fireball.GetComponent<SkeletonAnimation>();
        fireballSkeleton.state.SetAnimation(0, "blueFlame", true);
        bullet.transform.localScale = Vector3.one * 0.75f;

        bullet.setRadiusEffectSquared(Globals.CocosToUnity(150));
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    void CreateLaser()
    {
        DOTween.Sequence().SetId(tweenID).AppendInterval(ATTACK_DELAY - 0.5f).AppendCallback(() =>
        {
            if (stats.health / stats.maxHealth.Value < 0.1f)
            {
                CreateBlades();
            }
        });

        DOTween.Sequence().SetId(tweenID).AppendInterval(ATTACK_DELAY + 0.5f).AppendCallback(() =>
        {
            if (stats.health / stats.maxHealth.Value < 0.1f)
            {
                CreateBlades();
            }
        });

        for (int i = 0; i < 10; i++)
        {
            float rotation = Random.value * 360;
            _bone = enemySprite.Skeleton.FindBone("fire");
            var bonePos = _bone.GetWorldPosition(enemySprite.transform);

            SpriteRenderer alert = null;
            foreach (var strobe in strobes)
            {
                if (!strobe.transform.parent.gameObject.activeInHierarchy)
                {
                    alert = strobe;
                    alert.transform.parent.gameObject.SetActive(true);
                    break;
                }
            }

            if (alert)
            {
                var laserAlert = alert.transform.parent;

                laserAlert.localScale = new Vector3(40, 1, 1);
                alert.color = new Color(1, 0, 0, 0);
                laserAlert.rotation = Quaternion.Euler(0, 0, rotation);
                laserAlert.position = bonePos;
                DOTween.Sequence().SetId(tweenID).Append(alert.DOBlendableColor(new Color(1, 0, 0, 1), ATTACK_DELAY))
                    .AppendCallback(() => laserAlert.gameObject.SetActive(false));
            }


            Laser laser = null;
            foreach (var l in lasers)
            {
                if (!l.isInUse)
                {
                    laser = l;
                    laser.isInUse = true;
                    break;
                }
            }

            if (laser)
            {
                var laserTransform = laser.transform;

                laserTransform.rotation = Quaternion.Euler(0, 0, rotation + 180);
                laser.SetLaserScale(40, 1);
                laser.setDamage = GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 20;
                laserTransform.position = bonePos;
                DOTween.Sequence().SetId(tweenID).AppendInterval(ATTACK_DELAY)
                    .AppendCallback(() => laser.gameObject.SetActive(true))
                    .AppendInterval(1.5f).Append
                    (
                        DOTween.Sequence().SetId(tweenID)
                        .AppendCallback(() => laser.SetLaserScale(40, laser.GetLaserScale().y - 0.01f))
                        .AppendInterval(0.01f).SetLoops(100)
                    )
                    .OnComplete(() =>
                    {
                        laser.isInUse = false;
                        laser.gameObject.SetActive(false);
                    });
            }
        }
    }

    private void InitLasers()
    {
        foreach(var l in lasers)
        {
            l.Init();
            l.setDamage = GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1f :  5.0f;
            l.SetLaserScale(40, 1f);
            l.setAllowSound = true;
            l.SetAllowShrink(false);
            l.SetIsActive(false);
        }
    }


    void SpawnBombers()
    {
        if (GameSharedData.Instance.enemyList.Count > 20)
            return;

        if (_bombersCount > 0)
        {
            BugBot node = GameManager.instance.SpawnEnemy("BUGBOT", PortalMid) as BugBot;
            node.SetBulletDamage(stats.bulletDamage);
            //node.InitAsBomber();

            // TODO ASK
            //node.runAction(Sequence.create(DelayTime.create(0.1), CallFunc.create([=](){ node.setLocalZOrder(2); }), NULL));

            _bombersCount--;
            node.stats.turnSpeed /= 2;
            node.stats.speed *= 1.35f;
        }

        if (_bombersCount == 0 && GameSharedData.Instance.enemyList.Count < 5)
        {
            _currentState = PortalStates.FIREBALL_1;
            spawnBombers.Kill();
            DOTween.Sequence().SetId(tweenID).AppendInterval(3).AppendCallback(ChangeState);
        }
    }

    void CheckPlayerCollisions()
    {
        foreach (Blade blade in bladeList)
        {
            if (Vector2.SqrMagnitude(player.transform.position - blade.transform.position) < Globals.CocosToUnity(1000)
                && player.canHit)
            {
                if (blade.boundingBox.bounds.Contains(player.transform.position))
                {
                    player.GotHit(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1f : 15.0f, false);
                }
            }
        }
    }

    public override bool TakeHit(double damage)
    {
        if (healthBar && !isInvulnerable)
        {
            stats.health = stats.health - damage * 12; // TODO REMOVE '* 12'
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            healthBar.gameObject.SetActive(true);
            DOTween.Kill(healthBar.TweenID);
            DOTween.Sequence().SetId(healthBar.TweenID).AppendInterval(3).AppendCallback(() =>
            {
                healthBar.gameObject.SetActive(false);
            });
        }


        if (_currentState == PortalStates.CUTUES_FORM_L || _currentState == PortalStates.CUTUES_FORM_F)
        {
            if (stats.health < stats.maxHealth.Value * 0.85f)
            {
                _currentState = PortalStates.SENTINEL_BOTS;
                DOTween.Kill(tweenID); 
                DOTween.Sequence().SetId(tweenID).AppendInterval(5).AppendCallback(ChangeState);
                enemySprite.state.SetAnimation(0, "talk", false);
                enemySprite.state.AddAnimation(0, "idle", true);
            }
        }

        if (_currentState == PortalStates.FIREBALL_1 || _currentState == PortalStates.FIREBALL_2)
        {
            if (stats.health < stats.maxHealth.Value * 0.3f)
            {
                _currentState = PortalStates.SPIDER_LASER;
                DOTween.Kill(tweenID); 
                enemySprite.state.SetAnimation(0, "talk", false);
                enemySprite.state.AddAnimation(0, "idle", true);
                isInvulnerable = true;
                DOTween.Sequence().SetId(tweenID).AppendInterval(2.5f).AppendCallback(ChangeState);
                DOTween.Sequence().SetId(tweenID).AppendInterval(5).AppendCallback(() =>
                {
                    checkForSpiderLaser = DOTween.Sequence().SetId(schedulerID).AppendCallback(CheckForSpiderLaser).AppendInterval(0.5f).SetLoops(-1);
                });
            }
        }

        if (stats.health < 0)
        {
            if (isBoss)
            {
                Globals.isBossMode = false;
                scheduleUpdate = false;
                Globals.bossPosition = Vector2.zero;
            }

            return true;
        }

        return false;
    }

    void CheckForSpiderLaser()
    {
        if (_currentState == PortalStates.SPIDER_LASER && GameSharedData.Instance.enemyList.Count < 4)
        {
            _currentState = PortalStates.LASER;
            DOTween.Sequence().SetId(tweenID).AppendInterval(3).AppendCallback(ChangeState);
            checkForSpiderLaser.Kill();
            isInvulnerable = false;
        }
    }
    
    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        int bossNumber = 9;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn);
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }

        isBoss = true;
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = System.Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = System.Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)System.Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;





        //stats.speed = baseStats.speed = 2;
        //stats.health = baseStats.health = 100000;

        //stats.turnSpeed = baseStats.turnSpeed = 2;
        //stats.bulletDamage = baseStats.bulletDamage = 4;
        //stats.regen = baseStats.regen = 0;
        //stats.xp = baseStats.xp = 50;
        //stats.coinAwarded = baseStats.coinAwarded = 10;
        //stats.missileDamage = baseStats.missileDamage = 4;
        //stats.maxHealth = baseStats.maxHealth = stats.health;
    }

    void ShootFireballAllDirections()
    {
        _bone = enemySprite.Skeleton.FindBone("fire");
        var bonePos = _bone.GetWorldPosition(enemySprite.transform);

        for (int i = 0; i < 7; i++)
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.gameObject.SetActive(true);
            bullet.setDamage(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 200);
            bullet.SetSpriteFrame(null);
            //bullet.setAnchorPoint(cocos2d.Point(0.75, 0.75));


            bullet.transform.position = (Vector2)bonePos;
            bullet.transform.rotation = Quaternion.Euler(0, 0, -90 + (i * 30));
            bullet.PlayBulletAnim(6.5f,
                new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(bullet.transform.eulerAngles.z * Mathf.Deg2Rad),
                Globals.CocosToUnity(5000) * Mathf.Cos(bullet.transform.eulerAngles.z * Mathf.Deg2Rad)));

            var fireball = bullet.transform.GetChild(4);
            fireball.gameObject.SetActive(true);
            var fireballSkeleton = fireball.GetComponent<SkeletonAnimation>();
            fireballSkeleton.state.SetAnimation(0, "blueFlame", true);
            bullet.transform.localScale = Vector3.one * 0.75f;

            bullet.setRadiusEffectSquared(Globals.CocosToUnity(170));
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }

        for (int i = 0; i < 2; i++)
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.gameObject.SetActive(true);

            bullet.setDamage(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 200);
            bullet.SetSpriteFrame(null);
            //bullet.setAnchorPoint(cocos2d.Point(0.75, 0.75));

            bullet.transform.position = new Vector3(bonePos.x, bonePos.y - Globals.CocosToUnity(30), bullet.transform.position.z);
            bullet.transform.rotation = Quaternion.Euler(0, 0, -100 + (i * 200));
            bullet.PlayBulletAnim(6.5f,
                new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(bullet.transform.eulerAngles.z * Mathf.Deg2Rad),
                Globals.CocosToUnity(5000) * Mathf.Cos(bullet.transform.eulerAngles.z * Mathf.Deg2Rad)));
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);

            var fireball = bullet.transform.GetChild(4);
            fireball.gameObject.SetActive(true);
            var fireballSkeleton = fireball.GetComponent<SkeletonAnimation>();
            fireballSkeleton.state.SetAnimation(0, "blueFlame", true);
            bullet.transform.localScale = Vector3.one * 0.75f;

            bullet.setRadiusEffectSquared(Globals.CocosToUnity(170));
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }
    }


    void ShootFireballUp()
    {
        Bullet bullet = null;

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.gameObject.SetActive(true);

        bullet.setDamage(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 100);
        bullet.SetSpriteFrame(bulletEnemySprite);
        bullet.SetOpacity(1);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(150));
        _bone = enemySprite.skeleton.FindBone("fire");
        var bonePos = _bone.GetWorldPosition(enemySprite.transform);

        bullet.transform.position = bonePos;

        bullet.PlayBulletAnim(1.5f, new Vector2(0, Globals.CocosToUnity(2500)));


        var fireball = bullet.transform.GetChild(4);
        fireball.gameObject.SetActive(true);
        var fireballSkeleton = fireball.GetComponent<SkeletonAnimation>();
        fireballSkeleton.state.SetAnimation(0, "blueFlame", true);
        bullet.transform.localScale = Vector3.one * 0.75f;
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }


    public override bool CheckCollision(Vector2 P1)
    {
        if (isDestroyed || bounds == null)
            return false;
        
        return bounds.Contains(P1);
    }

    void Update()
    {
        if (!scheduleUpdate)
            return;

        bounds = boundsCollider.bounds;
        CheckPlayerCollisions();
    }
    
    public override void Destroy() 
    {
        Globals.allDamage = 7000;
        _currentState = PortalStates.DEAD;
        healthBar.gameObject.SetActive(false);
        lasersContainer.gameObject.SetActive(false);
        bladesContainer.gameObject.SetActive(false);

        isDestroyed = true;
        scheduleUpdate = false; 

        enemySprite.state.SetAnimation(0, "talk", false);
        enemySprite.state.AddAnimation(0, "death", false);
        DOTween.Kill(schedulerID);
        DOTween.Kill(tweenID);
        Globals.ResetBoundary();
        //    Shared.playSound("res/Sounds/Bosses/Boss6/SpiderCatFall.mp3");
    }
}
