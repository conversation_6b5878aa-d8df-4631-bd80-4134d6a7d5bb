using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class CameraMovement : MonoBehaviour
{
    [SerializeField] PlayerController player;
    Vector2 movementValues = Vector2.zero;
    Camera mainCam;
    Transform mainCameraTransform;
    int numberOfShakes;
    float fustrumIncreaseVal;
    bool isZoomToBoss, zoomIn;

    public string tweenID, schedulerID;

    const float zoomDivision = 2.0f;
    float countZ;

    private void Awake()
    {
        mainCameraTransform = transform.GetChild(0);
        mainCam = mainCameraTransform.GetComponent<Camera>();
        isZoomToBoss = false;
        zoomIn = false;
        Globals.slowDownForBoss = false;
        tweenID = "CameraTween";
        schedulerID = "CameraSchedules";
    }
    
    public Camera GetCamera()
    {
        return mainCam;
    }

    // Start is called before the first frame update
    void Start()
    {
        //transform.position = new Vector3(transform.position.x, transform.position.y, -Globals.CocosToUnity(850));
    }

    // Update is called once per frame
    void LateUpdate()
    {
        //CameraFollowNew();
        //CheckBoundaries();

        //if (Director::getInstance()->getScheduler()->getTimeScale() > 0.8f) TODO ASK
        //{
        //    timeCount += dt;
        //}

        //if (Globals.zoomInForSec != 0)
        //{
        //    ZoomInFunc(Globals.zoomInForSec);
        //}

        //if (Globals.zoomToBossForSec != 0)
        //{
        //    ZoomToBoss(Globals.zoomToBossForSec);
        //}
        //if (Globals.zoomOutCamera)
        //{
        //    Globals.zoomOutCamera = false;
        //    ZoomOut();
        //    isZoomToBoss = false;
        //}
    }
    
    void CameraFollowNew()
    {
        LimitMovementValues();

        Vector2 focusOnPosition = player.transform.position;
        //float focusOnRotation = player.RotationInDegrees;
        float deltaMultiplier = 1;

        if (isZoomToBoss)
        {
            focusOnPosition = Globals.bossPosition;
        }
        //if (player.Mode == Player::PLAYER_MODE_DEATHDROP || Player::getStats()->mode == Player::PLAYER_MODE_BEFORECHUTE)
        //{
        //    movementValues.x = 0;
        //    movementValues.y = 0;
        //}

        ////////////////////Camera position/////////////////////////
        if (Globals.lockPlayerMovementForTutorial)
        {
            Globals.averageBulletDistance = new Vector2(0, 100);
            movementValues = Vector2.zero;
        }

        if (Globals.focusOnPlayer)
        {
            movementValues = Vector2.zero;
            Globals.averageBulletDistance = Vector2.zero;
        }

        if (zoomIn)
        {
            Globals.averageBulletDistance = Vector2.zero;
            //focusOnRotation = 90;
            movementValues.x = Globals.CocosToUnity(100);
            movementValues.y = 0;
            deltaMultiplier = 18; // ASK TODO
        }

        //print(Globals.zoomValueWhileGame * 103.63296195f);
        float x = transform.position.x + (-transform.position.x + focusOnPosition.x + Globals.averageBulletDistance.x + Globals.CocosToUnity(movementValues.x) * 1.35f ) * Time.unscaledDeltaTime * 3 * deltaMultiplier;

        //Debug.Log("deltaMultiplier" + deltaMultiplier.ToString());
        //Debug.Log("focusOnPosition.x" + focusOnPosition.x.ToString());
        //Debug.Log("Globals.averageBulletDistance.x" + Globals.averageBulletDistance.x.ToString());
        //Debug.Log("movementValues.x" + movementValues.x.ToString());
        //Debug.Log("Time.unscaledDeltaTime" + Time.unscaledDeltaTime.ToString());
        
        //Debug.LogWarning("摄像机的最终X------------------------" + x.ToString());
        transform.SetWorldPositionX(x);
        transform.SetWorldPositionY(transform.position.y
            + (-transform.position.y + focusOnPosition.y + Globals.averageBulletDistance.y + Globals.CocosToUnity(movementValues.y) * 0.25f ) * Time.unscaledDeltaTime * 3 * deltaMultiplier);
        //bgCamera->setRotation(camera->getRotation());

        ////////////////////Camera position/////////////////////////

        float zAxisValueAddition = Mathf.Clamp(Mathf.Max(Mathf.Abs(player.Acceleration.x),
            Mathf.Abs(player.Acceleration.y)), 0, Globals.CocosToUnity(13));

         zAxisValueAddition = -Globals.CocosToUnity(650)
            - Globals.zoomValueWhileGame - (zAxisValueAddition * Globals.CocosToUnity(26.5f));

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            zAxisValueAddition = Mathf.Clamp(zAxisValueAddition, -14, 200);
        }

        float dtMul = 1;

        if (zoomIn)
        {
            zAxisValueAddition = -Globals.CocosToUnity(200);
            if (isZoomToBoss)
            {
                zAxisValueAddition = -Globals.zoomValueOnBoss;
            }
            dtMul = 10;
            if (Globals.updateCameraZValueForSidekicks || Globals.isTutorial)
            {
                zAxisValueAddition = -Globals.CocosToUnity(350);
            }
        }

        if (Globals.bossShouldStayOnScreen)
        {
            if (GameSharedData.Instance.enemyList.Count == 0)
            {
                return;
            }
            
            if (!zoomIn)
            {
                foreach (Enemy enemy in GameSharedData.Instance.enemyList)
                {
                    if (enemy.isBoss)
                    {
                        // TODO
                        float distance = Vector2.Distance(enemy.transform.position, player.transform.position);
                        distance = -Mathf.Clamp(distance, Globals.CocosToUnity(650) + Globals.zoomValueWhileGame,
                            Globals.maxCameraZoom);
                        countZ = transform.position.z + (-transform.position.z + distance) * Time.unscaledDeltaTime * dtMul;
                        if (float.IsNaN(countZ)) countZ = 0f;
                        transform.SetWorldPositionZ(countZ);

                        var midPoint = new Vector3((enemy.transform.position.x + player.transform.position.x) / 2,
                            (enemy.transform.position.y + player.transform.position.y) / 2, transform.position.z);
                        transform.position = midPoint + (-midPoint + transform.position) * Time.unscaledDeltaTime * 0.5f;
                    }
                }
            }
            else
            {
                countZ = transform.position.z
                    + (-transform.position.z + zAxisValueAddition + fustrumIncreaseVal) * Time.unscaledDeltaTime * dtMul;
                if (float.IsNaN(countZ)) countZ = 0f;
                transform.SetWorldPositionZ(countZ);
            }
        }
        else
        {
            zAxisValueAddition = Mathf.Clamp(zAxisValueAddition, -20, 100);
            countZ = transform.position.z
                + (-transform.position.z + zAxisValueAddition + fustrumIncreaseVal) * Time.unscaledDeltaTime * dtMul;
            if (float.IsNaN(countZ)) countZ = 0f;
            transform.SetWorldPositionZ(countZ);
        }
        Globals.ResetShootCheckDistance();
        //bgCamera->setPosition3D(camera->getPosition3D());
    }

    void CameraFollow()
    {
        LimitMovementValues();

        Vector2 positionFocus = player.transform.position;
        float rotationFocus = player.RotationInDegrees;

        float deltaMultiplier = 1;

        //float nextPosition_X = transform.position.x + (positionFocus.x - transform.position.x) * Time.unscaledDeltaTime * deltaMultiplier;
        //float nextPosition_Y = transform.position.y + (positionFocus.y - transform.position.y) * Time.unscaledDeltaTime * deltaMultiplier;

        float nextPosition_X = transform.position.x + (positionFocus.x - transform.position.x
            + Globals.CocosToUnity(movementValues.x) * 1.35f * Mathf.Cos(Mathf.Deg2Rad * rotationFocus)) * Time.unscaledDeltaTime * deltaMultiplier * 3;
        float nextPosition_Y = transform.position.y + (positionFocus.y - transform.position.y
            + Globals.CocosToUnity(movementValues.y) * 0.25f * Mathf.Sin(Mathf.Deg2Rad * rotationFocus)) * Time.unscaledDeltaTime * deltaMultiplier * 3;
        float zAxisValueAddition = Mathf.Clamp(Mathf.Max(Mathf.Abs(player.Acceleration.x), Mathf.Abs(player.Acceleration.y)), 0, Globals.CocosToUnity(13));
        zAxisValueAddition = -Globals.CocosToUnity(650) - Globals.zoomValueWhileGame - (zAxisValueAddition * Globals.CocosToUnity(26.5f));

        float nextPosition_Z = transform.position.z + (zAxisValueAddition - transform.position.z) * Time.unscaledDeltaTime;
        transform.position = new Vector3(nextPosition_X, nextPosition_Y, nextPosition_Z);

        //transform.position = new Vector3(nextPosition_X, nextPosition_Y, transform.position.z);
    }

    void CheckBoundariesNew()
    {
        if (Globals.lockPlayerMovementForTutorial)
            return;


        if (!zoomIn && !isZoomToBoss)
        {
            float additional = 0;
            if (Globals.bossShouldStayOnScreen)
            {
                additional = Globals.bossShouldStayOnScreenAdditionalBoundary;
                var a = new Bounds(player.transform.position, new Vector3(0.02f, 0.02f, 0));
                //if (camera->isVisibleInFrustum(a)) TODO
                {
                    Globals.bossShouldStayOnScreenAdditionalBoundary += Globals.CocosToUnity(3);
                    Globals.bossShouldStayOnScreenAdditionalBoundary =
                        Mathf.Clamp(Globals.bossShouldStayOnScreenAdditionalBoundary, 0, Globals.CocosToUnity(250));
                }
            }

            if (transform.position.y < Globals.LOWERBOUNDARY - Globals.CocosToUnity(100)
                + Screen.height / 2 - Globals.zoomValueWhileGame / zoomDivision) // Ask sign of zoomValueWhileGame TODO
            {
                transform.SetWorldPositionY(Globals.LOWERBOUNDARY - Globals.CocosToUnity(100)
                    + Screen.height / 2 + Globals.zoomValueWhileGame / zoomDivision);
            }
            if (transform.position.y > Globals.UPPERBOUNDARY + transform.position.z + Globals.CocosToUnity(950)) // TODO ASK
            {
                transform.SetWorldPositionY(Globals.UPPERBOUNDARY + transform.position.z + Globals.CocosToUnity(950));
            }


            //  boundaries x


            if (transform.position.y < Globals.LEFTBOUNDARY - transform.position.z - Globals.CocosToUnity(450) - additional)// + zoomValueWhileGame/zoomDivision) )
            {
                transform.SetWorldPositionX(Globals.LEFTBOUNDARY - transform.position.z - Globals.CocosToUnity(450) - additional);// + zoomValueWhileGame/zoomDivision) );
                return;
            }
            else if (transform.position.x > Globals.RIGHTBOUNDARY + transform.position.z + Globals.CocosToUnity(450) + additional)//  - zoomValueWhileGame/ zoomDivision)
            {
                transform.SetWorldPositionX(Globals.RIGHTBOUNDARY + transform.position.z + Globals.CocosToUnity(450) + additional);// - zoomValueWhileGame/ zoomDivision);
                return;
            }
        }
    }

    void CheckBoundaries()
    {
        float positionY = transform.position.y;
        float positionX = transform.position.x;
        var frustumHeight = 2.0f * -transform.position.z * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad);
        var frustumWidth = frustumHeight * mainCam.aspect;
        //float lowerPosMax = Globals.LOWERBOUNDARY - 1.67f + Globals.CocosToUnity(Screen.height / 2) + Globals.CocosToUnity(Globals.zoomValueWhileGame/2);
        //float upperPosMax = Globals.UPPERBOUNDARY + transform.position.z + Globals.CocosToUnity(950);
        float lowerPosMax = Globals.LOWERBOUNDARY - 1.67f + frustumHeight / 2;
        float upperPosMax = Globals.UPPERBOUNDARY - frustumHeight / 2 + Globals.CocosToUnity(450);
        //float leftPosMax = Globals.LEFTBOUNDARY + transform.position.z - Globals.CocosToUnity(20);
        //float rightPosMax = Globals.RIGHTBOUNDARY - transform.position.z + Globals.CocosToUnity(20);
        float leftPosMax = Globals.LEFTBOUNDARY + frustumWidth / 2 - Globals.CocosToUnity(450);
        float rightPosMax = Globals.RIGHTBOUNDARY - frustumWidth / 2 + Globals.CocosToUnity(450);

        if (positionY < lowerPosMax)
        {
            transform.position = new Vector3(transform.position.x, lowerPosMax, transform.position.z);
        }
        if (positionY > upperPosMax)
        {
           transform.position = new Vector3(transform.position.x, upperPosMax, transform.position.z);
        }

        if (positionX < leftPosMax)
        {
            transform.position = new Vector3(leftPosMax, transform.position.y, transform.position.z);
        }
        if (positionX > rightPosMax)
        {
            transform.position = new Vector3(rightPosMax, transform.position.y, transform.position.z);
        }
    }

    public void ShakeCamera(float intensity, int shakes)
    {
        mainCameraTransform.DOKill();
        numberOfShakes = shakes;
        ShakeScreen(intensity);
    }

    void ShakeScreen(float intensity)
    {
        //if (!screenShakeEnabled)
        //    return;
        intensity = intensity / 1.5f;
        float randomShakeX = (Random.value * intensity) - intensity / 2;
        float randomShakeY = (Random.value * intensity) - intensity / 2;

        mainCameraTransform.DOLocalMove(new Vector3(randomShakeX, -randomShakeY, 0), 0.03f)
            .OnComplete(() =>
            {
                mainCameraTransform.DOLocalMove(new Vector3(-randomShakeX, randomShakeY, 0), 0.03f)
                .OnComplete(() =>
                {
                    mainCameraTransform.DOLocalMove(Vector3.zero, 0.01f)
                    .OnComplete(() =>
                    {
                        numberOfShakes--;
                        if(numberOfShakes > 0)
                            ShakeScreen(intensity);
                    });
                });
            });
    }

    void LimitMovementValues()
    {
        int xValue = 200; // For PC
        float multiplier = 10;
        float mValueX = movementValues.x, mValueY = movementValues.y;

        if (player.playerMovement.GotMovementInput)
        {
            mValueX = mValueX - 3 * multiplier;
            mValueY = mValueY + 1.0f * multiplier;
            //if (mValueX > xValue)
            //{
            //    mValueX = xValue;
            //}
            if (mValueX < 200)
            {
                mValueX = 200;
            }
            if (mValueY > xValue)
            {
                mValueY = xValue;
            }
        }
        else
        {

            mValueX += multiplier;
            mValueY -= 0.5f * multiplier;
            //if (mValueX < 200)
            //{
            //    mValueX = 200;
            //}
            if (mValueX > xValue)
            {
                mValueX = xValue;
            }
            if (mValueY < 200)
            {
                mValueY = 200;
            }
        }

        movementValues = new Vector2(mValueX, mValueY);
    }

    void ZoomOut()
    {
        //Director::getInstance()->getScheduler()->setTimeScale(GAMESPEED);
        Globals.slowDownForBoss = true;
        zoomIn = false;
    }

    void ZoomInFunc(float time)
    {
        ZoomOut();

        Globals.zoomInForSec = 0;

        //if (player.Mode == PlayerController.PlayerAnimation.Flying)
            //Director::getInstance()->getScheduler()->setTimeScale(0.15f);

        zoomIn = true;
        DOTween.Kill(tweenID);
        //#if Desktop
        //    Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("show_cursor");

        //#else
        //        if (FileHandler::getInstance()->currentMission != 0)
        //            Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("show_dpads");


        //#endif

        DOTween.Sequence().SetId(tweenID).AppendInterval(time).AppendCallback(ZoomOut).AppendInterval(0.5f)
            .AppendCallback(() =>
            {
                //GameManager.instance.EnableControls();
                // Enable controls here
            });
    }

    void ZoomOutFromBoss()
    {
        isZoomToBoss = false;
        //#if Desktop
        //    if(!isJoystickConnected){
        //        if(skipLabel)
        //        skipLabel->setVisible(false);
        //    }
        //#endif
        //        gameControllerObj->_hudLayer->isZoomed = false;
    }

    void ZoomToBoss(float time)
    {
        Globals.zoomToBossForSec = 0;
        //gameControllerObj->_hudLayer->isZoomed = true;

        if (Globals.slowDownForBoss)
        {
            //Director::getInstance()->getScheduler()->setTimeScale(zoomToBossTimeScale);
        }
        Globals.zoomToBossTimeScale = 0.1f;
        zoomIn = true;
        isZoomToBoss = true;
#if UNITY_STANDALONE
    if(!Globals.isJoystickConnected){
            //TODO
        //if(Globals.skipLabel)
        //skipLabel->setVisible(true);
    }
#endif

        DOTween.Kill(tweenID);
        DOTween.Sequence().SetId(tweenID).AppendInterval(time).AppendCallback(ZoomOutFromBoss)
            .AppendCallback(ZoomOut);
        //this->runAction(Sequence::create(DelayTime::create(time),
        //    CallFunc::create(CC_CALLBACK_0(GameScene::zoomOutFromBoss, this)),
        //    CallFunc::create(CC_CALLBACK_0(GameScene::zoomOut, this)), NULL));
        
    }
}
