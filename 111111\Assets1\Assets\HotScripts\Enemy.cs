﻿
using DG.Tweening;
using Spine.Unity;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
public enum PINGTYPE
{
    ENEMY_ALERT,
    MISSION_AHEAD
};

public class Enemy : MonoBehaviour
{
    public enum MonsterType
    {
        normal = 0,//普通
        select = 1,//三选一
        elit = 2,//精英
        boss = 3,//boss
    }

    const float ENEMY_RELOCATE_DISTANCE = 80;

    /// <summary>
    /// 碰撞检测半径
    /// </summary>
    public float enemyCollisionRadius = 1f;
    public SkeletonAnimation enemySprite;
    public Transform enemyPoint;
    public HealthBar healthBar;
    public Vector2 offset = Vector2.zero;
    public Vector2 tmpOffset = Vector2.zero;//飘字偏移
    public Explosions.ExplosionType explosionType = Explosions.ExplosionType.ExplosionTypeWXREnemy1;
    [HideInInspector] public bool isDestroyed = false;
    [HideInInspector] public bool takeDamage = true;
    [HideInInspector] public bool isBoss = false;
    [HideInInspector] public bool allowStack = true;
    [HideInInspector] public bool allowLessFrames = true;
    [HideInInspector] public bool _allowKillPoint = true;
    [HideInInspector] public bool _isFiller = false;
    [HideInInspector] public float enemySchedulerSpeed = 0.041f; // 30fps
    [HideInInspector] public bool _isInteractable = true;
    [HideInInspector] public PlayerController player;
    [HideInInspector] public float _jsonScale = 1.0f;
    [HideInInspector] public bool isDestructable = true;
    [HideInInspector] public bool isBossChild = false;
    [HideInInspector] public bool isTripodBoss = false;
    [HideInInspector] public bool isTutorialTower = false;
    [HideInInspector] public bool isOktoBoss = false;
    [HideInInspector] public bool isSentinelBoss = false;
    [HideInInspector] public bool isCurrentlyTarget = false;
    [HideInInspector] public bool isLaserBase = false;
    [HideInInspector] public bool isLittleSpider = false;
    [HideInInspector] public bool scheduleUpdate = false;
    public MonsterType monsterType = MonsterType.normal;//怪物类型
    public int dieathSoundID = 7100;
    private int _enemyLevel = 1;

    [HideInInspector] public bool initialized = false;
    [HideInInspector] public Attributes stats;
    [HideInInspector] public Attributes baseStats;

    private bool _allowRelocate;
    private bool _allowFollow;
    private bool _allowDeathParticles;
    private bool _allowPushBack;
    //奖励ID
    [HideInInspector] public int prizeID;
    [HideInInspector] public BattleBrushEnemy.Item monsterData;
    //射击概率相关的
    [HideInInspector] public double skillShootProbability = 10000;
    [HideInInspector] public float angleToShoot = 20;
    [HideInInspector] public Vector2 posDiff2;
    [HideInInspector] public int shootRandom;
    [HideInInspector] public float checkStateTime = 0.5f;
    [HideInInspector] public float curCheckStateTime = 0;

    /// <summary>
    /// 是否正在被激光伤害中
    /// </summary>
    [HideInInspector] public bool laserDamaging = false;
    /// <summary>
    /// 正被哪条激光伤害
    /// </summary>
    [HideInInspector] public ViewModel.Laser laser;

    Sequence _buffSeq;
    Dictionary<int, float> _buffTime;
    Dictionary<int, float> _buffTimeTemp;
    List<int> _needRemoveBuff;
    bool _hadDestroy = false;
    //定时怪，在战斗中某波刷出来，到达目标点后，如果在规定的时间内没有被击杀，就加速撤离，不计入到击杀数里面
    [HideInInspector] public float timeOfStopMode = 0;
    [HideInInspector] public bool startMove;
    [HideInInspector] public bool isBorthRight;
    //是否需要重置相机的zooom
    [HideInInspector] public bool needResetZoom;
    //镭射暴击显示的延迟
    [HideInInspector] public float lasterTime;
    //默认是可以检测和玩家的距离的（为了区分蜘蛛猫的问题）


    public bool canCheckDistance = true;


    public bool allowRelocate { get { return _allowRelocate; } set { _allowRelocate = value; } }
    public bool allowFollow { get { return _allowFollow; } set { _allowFollow = value; } }
    public bool allowDeathParticles { get { return _allowDeathParticles; } set { _allowDeathParticles = value; } }
    public bool allowPushBack { get { return _allowPushBack; } set { _allowPushBack = value; } }

    public delegate void AdditionalOnDestroy();
    public event AdditionalOnDestroy additionalOnDestroy;

    [HideInInspector] public string tweenId;
    [HideInInspector] public string schedulerId;
    Sequence seq;

    private int _tag;
    public int Tag { get { return _tag; } set { _tag = value; } }
    [HideInInspector] public Rigidbody2D rigid;
    //怪物进入玩家攻击圈减速减掉X%;
    private float reducePercent = 75f;
    private bool isReduceSpeed = false;

    public int ZumaQueneIndex = 0;
    public int ZumaPathPosIndex = 0;
    private int ZumaMoveTargetPosIndex;
    public int zumaPathIndex = 0;
    private void Start()
    {
        //Init();
    }

    public virtual void Init()
    {
        if (initialized)
            return;
        initialized = true;
        allowRelocate = false;
        allowDeathParticles = false;
        allowPushBack = false;
        isReduceSpeed = false;
        isDestroyed = false;
        rigid = gameObject.AddComponent<Rigidbody2D>();
        rigid.gravityScale = 0;
        //rigid.angularDrag = 50000f;
        rigid.constraints = RigidbodyConstraints2D.FreezeRotation;
        //rigid.freezeRotation = false;
        var collider = gameObject.AddComponent<CircleCollider2D>();
        // 碰撞器半径，阻止怪物重叠
        collider.radius = 0.7f;
        player = GameManager.instance.player;
        GameSharedData.Instance.allBattleScript.Add(this);
        Relocate(1, 3); //FIXME dont relocate for others
        //StartCoroutine(Reclocate(3, 1));
        GameSharedData.Instance.enemyList.Add(this);
        //scheduleUpdate = true;
        seq = DOTween.Sequence().SetId(schedulerId).AppendInterval(0.25f).AppendCallback(() =>
        {

            if (!isBoss)
            {
                if (allowLessFrames)
                {
                    //TODO
                    //enemySprite.skeleton.upda::unscheduleUpdate();
                    //enemySprite.SkeletonRenderer::schedule(schedule_selector(SkeletonRenderer::update), enemySchedulerSpeed, kRepeatForever, 0);
                }
            }

        });
        Observer.RegisterCustomEvent(gameObject, "SET_DIFFICULTY", () =>
          {
              SetEnemyDifficulty();
          });
        if (healthBar)
        {
            healthBar.gameObject.SetActive(false);
        }
        _hadDestroy = false;
        lasterTime = 0;
        if (enemyPoint == null) enemyPoint = enemySprite.transform;
    }

    public void InitWithPosition(Vector2? position = null)
    {
        if (position == null)
        {
            position = Vector2.zero;
        }
        Init();
        transform.position = (Vector3)position;
    }

    public void SetEnemyLevel(int level, string enemyType, int newID)
    {
        if (level < 0)
        {
            return;
        }

        if (Globals.gameType == GameType.Survival)
        {
            level += (30 * Globals.survivalModeEnemyLevel);
        }

        if (stats != null)
        {

            if (LuaToCshapeManager.Instance.EquipmentID != 0)
            {
                //读pb
                //Debug.Log(" enemy new id:"+newID);
                monsterData = BattleBrushEnemyScheme.Instance.GetItem(newID);
                //Debug.Log("monsterData.PrizeID=" + monsterData.PrizeID);
                prizeID = monsterData.PrizeID;
                stats.health = baseStats.health = monsterData.Hp * (0.9f + (Random.value * 0.2f));  //System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH]as PList)[Constants.STATS_DATA.RANDOM]));
                //Debug.LogWarning("敌人的血量(难度前)" + stats.health.ToString());
                stats.bulletDamage = baseStats.bulletDamage = monsterData.PhysicsAttack * (0.9f + (Random.value * 0.2f)); //System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK]as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK]as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK]as PList)[Constants.STATS_DATA.RANDOM]));
                stats.speed = baseStats.speed = monsterData.MoveSpeed / 10000f; //System.Convert.ToSingle((statsMap[Constants.STATS.SPEED]as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED]as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED]as PList)[Constants.STATS_DATA.RANDOM]) * Globals.enemySpeedMultiplier);
                stats.turnSpeed = baseStats.turnSpeed = monsterData.TurnSpeed / 10000f * (0.9f + (Random.value * 0.2f)); //System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED]as PList)[Constants.STATS_DATA.VALUE])+ (level * System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED]as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED]as PList)[Constants.STATS_DATA.RANDOM]) * Globals.enemyTurnSpeedMultiplier);
                stats.distanceToShoot = monsterData.PhysicsDefense;
                stats.missileDamage = monsterData.Skill;
                skillShootProbability = monsterData.CriticalStrike;
                checkStateTime = monsterData.AttackRate / 1000f;
                //Debug.Log("checkStateTime=" + checkStateTime);
                //Debug.Log("ID:" + newID + "   monsterData.MoveSpeed=" + monsterData.MoveSpeed + "   stats.distanceToShoot=" + stats.distanceToShoot);
            }
            else
            {
                PList statsMap = GameData.instance.GetEnemy(enemyType)["Stats"] as PList;
                stats.health = baseStats.health = System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.HEALTH] as PList)[Constants.STATS_DATA.RANDOM]));
                // //Debug.LogWarning("敌人的血量(难度前)" + stats.health.ToString());
                stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.ATTACK] as PList)[Constants.STATS_DATA.RANDOM]));
                stats.speed = baseStats.speed = System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.RANDOM]) * Globals.enemySpeedMultiplier);
                stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.TURN_SPEED] as PList)[Constants.STATS_DATA.RANDOM]) * Globals.enemyTurnSpeedMultiplier);
                stats.distanceToShoot = Globals.CocosToUnity(9500);
                //Debug.Log("ID:" + newID + "   monsterData.MoveSpeed=" + monsterData.MoveSpeed + "   stats.distanceToShoot=" + stats.distanceToShoot);
                //Debug.LogWarning("原版的速度" + (System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.VALUE]) + (level * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.MULTIPLIER])) + (Random.value * System.Convert.ToSingle((statsMap[Constants.STATS.SPEED] as PList)[Constants.STATS_DATA.RANDOM]) * Globals.enemySpeedMultiplier)).ToString());
            }
           //Debug.LogWarning("我们算出的速度" + stats.speed.ToString());




            stats.missileDamage = baseStats.missileDamage = stats.missileDamage * ((level + 1) * 0.5f);
            stats.attackSpeed = baseStats.attackSpeed = stats.attackSpeed + (level * 0.05f);


            stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
            SetEnemySpecialAttributes();
            SetEnemyDifficulty();

            //FIXME: fix this
            if (GameData.instance.fileHandler.currentMission == 25)
            {
                SetEnemyDifficulty(0.075f, 2.0f);
            }
        }
    }

    /// <summary>
    /// 初始化怪物技能相关的东西
    /// </summary>
    public virtual void InitSkill()
    {

    }

    public virtual void ApplyBuff(int buffID = 0)
    {
        int keepTime = -1;
        BuffEffect.Item buffEffectData;
        Buff.Item buff;
        if (buffID == 0)
        {
            buffEffectData = LuaToCshapeManager.Instance.curEnemyBuffEffect;
        }
        else
        {
            buff = BuffScheme.Instance.GetItem(buffID);
            buffEffectData = BuffEffectScheme.Instance.GetItem(buff.EffectID1);
            keepTime = buff.KeepTime;
        }
        if (_buffTime == null)
        {
            _buffTime = new Dictionary<int, float>();
        }
        if (buffEffectData != null)
        {
            double newValue;
            if (buffEffectData.Param1 == 2)//攻击力
            {
                //Debug.LogWarning("敌人攻击力的加之前:" + stats.bulletDamage.ToString());

                newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * stats.bulletDamage);

                stats.bulletDamage = newValue;
                //Debug.LogWarning("敌人攻击力:" + newValue.ToString() + "," + stats.bulletDamage.ToString());
            }
            else if (buffEffectData.Param1 == 1)//血量
            {
                //Debug.LogWarning("敌人血量的加之前:" + stats.maxHealth.ToString());

                newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * stats.maxHealth.Value);
                stats.maxHealth.Value = newValue;
                stats.health = newValue;
                //Debug.LogWarning("敌人血量:" + newValue.ToString() + "," + stats.maxHealth.ToString());
            }
            else if (buffEffectData.Param1 == 18)//移动速度
            {
                //Debug.LogWarning("敌人移动速度的加之前:" + stats.speed.ToString());



                //冰封球的buff
                if (buffEffectData.Param3 < 0)
                {
                    if (_buffTime.ContainsKey(buffEffectData.Id))
                    {
                        _buffTime[buffEffectData.Id] = keepTime / 1000;
                    }
                    else
                    {
                        _buffTime.Add(buffEffectData.Id, keepTime / 1000);
                    }
                    if (enemySprite)
                    {
                        enemySprite.GetComponent<Renderer>().material.DOKill();
                        enemySprite.GetComponent<Renderer>().material.color = Color.blue;
                    }

                    newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * baseStats.speed);
                    //SetBuffSeq();
                }
                else
                {
                    newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * stats.speed);
                }
                stats.speed = (float)newValue;
                //Debug.LogWarning("敌人移动速度:" + newValue.ToString() + "," + stats.speed.ToString());
            }
            else if (buffEffectData.Param1 == 25)//子弹速度
            {
                //Debug.LogWarning("敌人子弹速度的加之前:" + stats.bulletSpeed.ToString());

                newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * stats.bulletSpeed);

                stats.bulletSpeed = (float)newValue;



                //Debug.LogWarning("敌人子弹速度:" + newValue.ToString() + "," + stats.bulletSpeed.ToString());
            }
            else if (buffEffectData.Param1 == 34)//攻击距离
            {
                //Debug.LogWarning("敌人攻击距离的加之前:" + stats.attackDistance.ToString());

                newValue = ((1 + Globals.UnityValueTransform(buffEffectData.Param3)) * stats.attackDistance);

                stats.attackDistance = (float)newValue;

                //Debug.LogWarning("敌人攻击距离:" + newValue.ToString() + "," + stats.attackDistance.ToString());
            }
            //燃烧
            else if (buffEffectData.Param1 == 19)
            {
                _combustionTotalTime = BuffScheme.Instance.GetItem(buffID).MaxTime / 1000;
                _combustionEveryTime = buffEffectData.Param7 / 1000;
                _combustionTempTime = _combustionEveryTime;
                _combustionDamage = buffEffectData.Param3;
                _buffTime[buffEffectData.Id] = _combustionTotalTime;
                _combustionBuffID = buffEffectData.Id;

                if (_flameGameObject == null)
                {
                    _flameGameObject = GameObject.Instantiate<GameObject>(GameSharedData.Instance.prefabConfig[16].prefab);// GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.FlamePrefab);
                    _flameGameObject.transform.SetParent(transform);
                    _flameGameObject.transform.localPosition = Vector2.zero + offset;
                }
                _flameGameObject.SetActive(true);
            }
        }

    }

    /// <summary>
    /// 是否碰到某个点
    /// </summary>
    public virtual bool CheckCollision(Vector2 P1)
    {
        if (!enemyPoint)
        {
            return false;
        }
        return Vector2.Distance(P1, enemyPoint.position) < enemyCollisionRadius;
    }

    /// <summary>
    /// 是否碰到某个圆形区域
    /// </summary>
    public virtual bool CheckCollision(Vector2 P1, float radius)
    {
        if (!enemyPoint)
        {
            return false;
        }
        return Vector2.Distance(P1, enemyPoint.position) < enemyCollisionRadius + radius;
    }

    /// <summary>
    /// 是否碰到玩家了
    /// </summary>
    /// <returns></returns>
    public virtual bool CheckCollidePlayer()
    {
        if (!enemyPoint)
        {
            return false;
        }
        return Vector2.Distance(player.skeletonAnimationTran.position, enemyPoint.transform.position) < enemyCollisionRadius + player.CollisionRadius;
    }

    /// <summary>
    /// 受击(显示血条)
    /// </summary>
    /// <param name="damage">应减去的血量</param>
    /// <returns>是否死亡</returns>
    public virtual bool TakeHit(double damage)
    {
        if (isDestroyed) { return false; }
        if (healthBar)
        {
            if (!healthBar.gameObject.activeInHierarchy)
            {
                healthBar.gameObject.SetActive(true);
            }
            DOTween.Kill("HealthBar" + GetInstanceID());
            DOTween.Sequence().SetId("HealthBar" + GetInstanceID()).AppendInterval(3).AppendCallback(() =>
              {
                  healthBar.gameObject.SetActive(false);
              }).Play();



            damage = System.Math.Max(0, damage);
            //玩家吸血
            if (GameManager.instance.player.Stats.AbsorbHP > 0)
            {
                var suck = System.Math.Floor(GameManager.instance.player.Stats.AbsorbHP * damage);
                GameManager.instance.player.Stats.health += suck;
                GameManager.instance.player.Stats.health = System.Math.Min(GameManager.instance.player.Stats.health, GameManager.instance.player.Stats.maxHealth.Value);
                GameManager.instance.player.UpdateHealthBar(true);
            }
            //Debug.Log(monsterData.Id+"敌人生命-------------》" + stats.health.ToString());
            //Debug.Log(monsterData.Id+"敌人收到了伤害-------》" + damage.ToString());
            stats.health -= damage; // REMOVE TODO
            //Debug.LogWarning("敌人剩余生命" + stats.health.ToString());
            //Debug.LogWarning("敌人默认最大生命" + stats.maxHealth.ToString());
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            if (enemySprite)
            {
                enemySprite.GetComponent<Renderer>().material.DOKill();
                if (_buffTime == null || _buffTime.Count == 0)
                {
                    enemySprite.GetComponent<Renderer>().material.color = Color.red;
                    enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
                }
            }

        }

        if (stats.health < 0)
        {
            if (isBoss)
            {
                Globals.isBossMode = false;
                scheduleUpdate = false;
                Globals.bossPosition = Vector3.zero;
            }

            return true;
        }
        return false;
    }

    public virtual void Destroy()
    {
        isDestroyed = true;
        //std::this_thread::sleep_for(std::chrono::milliseconds(15));
        //Thread.Sleep(15);
        //GenerateExplosion();
        PlayDestroySound();
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StopCoroutine(RemoveEnemy());
            StartCoroutine(RemoveEnemy());
        }
    }

    private void GenerateExplosion()
    {
        if (explosionType == Explosions.ExplosionType.ExplosionTypeBuildingMission)
        {
            //_bg.runAction(Sequence::create(TintTo::create(0.14, 50, 50, 50), DelayTime::create(0.3), TintTo::create(1.5f, 255, 255, 255), NULL));TODO

            GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1 + Random.Range(0, 3), _jsonScale / 2, 1);

        }
        else if (explosionType == Explosions.ExplosionType.ExplosionTypeBoss)
        {
            //_bg.runAction(Sequence::create(TintTo::create(0.14, 50, 50, 50), DelayTime::create(0.5), TintTo::create(1.5f, 255, 255, 255), NULL)); TODO
            GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1 + Random.Range(0, 3), 1f, 1);

        }
        else
        {
            GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1 + Random.Range(0, 3), 1f, 1);
        }

    }

    private IEnumerator RemoveEnemy()
    {
        yield return new WaitForEndOfFrame();
        if (additionalOnDestroy != null)
        {
            //Debug.Log("Additional On Destroy called");
            additionalOnDestroy();
        }

        if (_isFiller)
        {
            Globals.numberOfEnemies--;
        }
        GameSharedData.Instance.enemyList.Remove(this);

        if (monsterData.MoveType == 1 || monsterData.MoveType == 2) GameSharedData.Instance.ZumaEnemies[zumaPathIndex].Remove(this);

        //if (Globals.gameType == GameType.Survival)
        //{
        //    Observer.DispatchCustomEvent("newWave");
        //}
        if (healthBar)
        {
            DOTween.Kill("HealthBar" + GetInstanceID());
        }
        lasterTime = 0;
        DOTween.Kill(_buffSeq);
        _hadDestroy = true;
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        if (needResetZoom)
        {
            Globals.ResetZoomValue();
            needResetZoom = false;
        }
        Destroy(gameObject);
    }

    public void isAFillerEnemy()
    {
        if (!_isFiller)
        {
            _isFiller = true;
            Globals.numberOfEnemies++;
        }
    }

    public virtual void SetEnemySpecialAttributes()
    {

    }

    public void SetEnemyDifficulty(float easy = 0.275f, float hard = 2f)
    {
        easy = GameData.instance.fileHandler.currentMission != 0 ? Globals.g_currentStageData.DiffLevels[0] : easy;
        hard = GameData.instance.fileHandler.currentMission != 0 ? Globals.g_currentStageData.DiffLevels[2] : hard;
        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            if (stats != null)
            {
                if (Globals.g_currentStageData != null && (Globals.g_currentStageData.FrontType == 3 || Globals.g_currentStageData.FrontType == 4))
                {
                    easy *= player.baseStats.level;
                }
                stats.missileDamage *= easy;
                stats.bulletDamage *= easy;
                stats.health *= easy;
                stats.maxHealth.Value = stats.health;
            }

        }
        else if (Globals.gameModeType == GamePlayMode.Hard)
        {
            if (stats != null)
            {
                stats.missileDamage *= hard;
                stats.bulletDamage *= hard;
                stats.health *= hard;
                stats.maxHealth.Value = stats.health;
            }
        }
        ApplyBuff();
    }

    public void Relocate(float initialWait, float interval) => StartCoroutine(IRelocate(initialWait, interval));

    protected IEnumerator IRelocate(float initialWiat, float interval)
    {
        yield return new WaitForSeconds(initialWiat);
        while (_allowRelocate)
        {
            if (transform.position.x - player.transform.position.x < -ENEMY_RELOCATE_DISTANCE || transform.position.x - player.transform.position.x > ENEMY_RELOCATE_DISTANCE)
            {

                if (Random.value < 0.5f)
                {
                    transform.position = new Vector2(player.transform.position.x - ENEMY_RELOCATE_DISTANCE, transform.position.y);
                }
                else
                {
                    transform.position = new Vector2(player.transform.position.x + ENEMY_RELOCATE_DISTANCE, transform.position.y);
                }
            }
            yield return new WaitForSeconds(interval);
        }
    }

    public void CancelRelocate() => StopCoroutine(nameof(IRelocate));

    public void SpawnSpiderBulletOnButton() //will be removed in future
    {
        //LittleSpiders.currentSpiderType = SpiderType.GUN;
    }

    public void SetAllowPing(bool val, PINGTYPE pType)
    {
        if (val)
        {
            //if (!_ping)
            //{
            //    if (pType == PINGTYPE.ENEMY_ALERT)
            //    {

            //        _ping = Sprite::create("res/GameHud/missileAlert.png");
            //        Shared::rescale(_ping, 1);
            //    }
            //    else
            //    {
            //        _ping = Sprite::create("res/GameHud/missionAlert.png");
            //        Sprite* pingChild = Sprite::create("res/GameHud/missionAlertBg.png");
            //        _ping->addChild(pingChild);
            //        _ping->setScale(0.65f);
            //        Shared::rescale(_ping, 0.65);
            //        pingChild->setTag(1);
            //        pingChild->setPosition(_ping->getContentSize() / 2);
            //    }
            //    _pType = pType;
            //    this->addChild(_ping);
            //    _ping->setCameraMask(HUDCAMERA);
            //    // _ping->setGlobalZOrder(100);
            //    _ping->setVisible(false);
            //    this->enemySprite->updateWorldTransform();
            //    this->enemySprite->setContentSize(this->enemySprite->getBoundingBox().size);
            //}

            //this->schedule(schedule_selector(Enemy::showPing), 0.0f, CC_REPEAT_FOREVER, 1.0f);
        }

        else
        {

            //if (_ping)
            //{
            //    _ping->setVisible(false);
            //    this->unschedule(schedule_selector(Enemy::showPing));
            //}
        }
    }

    void SetBuffSeq()
    {
        _buffSeq?.Kill();
        _buffSeq = DOTween.Sequence().AppendInterval(1f).AppendCallback(() =>
        {
            if (_buffTime.Count > 0)
            {
                _buffTimeTemp = new Dictionary<int, float>(_buffTime);
                foreach (var v in _buffTimeTemp)
                {
                    var key = v.Key;
                    var time = v.Value;
                    time -= 1;
                    _buffTime[key] = time;
                    if (time == 0)
                    {
                        if (_needRemoveBuff == null)
                        {
                            _needRemoveBuff = new List<int>();
                        }
                        _needRemoveBuff.Add(key);
                    }

                }

                CheckRemoveBuff();
            }
        }).SetLoops(-1).Play();
    }

    void CheckRemoveBuff()
    {
        if (_needRemoveBuff != null && _needRemoveBuff.Count > 0)
        {
            foreach (var key in _needRemoveBuff)
            {
                RemoveBuff(key);
                _buffTime.Remove(key);
            }
            _needRemoveBuff.Clear();
        }
    }

    void RemoveBuff(int effectId)
    {
        if (!_hadDestroy)
        {
            var buffEffectData = BuffEffectScheme.Instance.GetItem(effectId);
            if (buffEffectData == null)
            {
                Debug.LogWarning("移除buff的时候有问题，effectID：" + effectId.ToString());
                return;
            }
            if (buffEffectData.Param1 == 18)//移动速度
            {
                stats.speed = baseStats.speed;
                if (enemySprite != null && enemySprite.gameObject.activeInHierarchy)
                {
                    Renderer renderer = enemySprite.GetComponent<Renderer>();
                    if (renderer != null)
                    {
                        renderer.material.DOKill();
                        renderer.material.color = Color.white;
                    }
                }

            }
            else if (buffEffectData.Param1 == 19)
            {
                if (_flameGameObject != null)
                {
                    _flameGameObject.SetActive(false);
                }
            }
        }
    }
    //燃烧相关的
    private float _combustionTotalTime;
    private float _combustionEveryTime;
    private float _combustionDamage;
    private float _combustionTempTime;
    private int _combustionBuffID;
    private GameObject _flameGameObject;

    protected virtual void LateUpdate()
    {
        if (_buffTime != null && _buffTime.Count > 0)
        {
            _buffTimeTemp = new Dictionary<int, float>(_buffTime);
            foreach (var v in _buffTimeTemp)
            {
                var key = v.Key;
                var time = v.Value;
                time -= Time.deltaTime;
                _buffTime[key] = time;
                if (time <= 0)
                {
                    if (_needRemoveBuff == null)
                    {
                        _needRemoveBuff = new List<int>();
                    }
                    _needRemoveBuff.Add(key);
                }
                //燃烧的
                if (key == _combustionBuffID)
                {
                    _combustionTempTime -= Time.deltaTime;
                    if (_combustionTempTime <= 0)
                    {
                        if (TakeHit(_combustionDamage))
                        {
                            if (!isDestroyed)
                            {
                                isDestroyed = true;
                                GameManager.instance.physicsManager.DestroyEnemy(this);
                            }
                        }
                        else
                        {
                            _combustionTempTime = _combustionEveryTime;
                        }

                    }
                }
            }

            CheckRemoveBuff();
        }
    }

    public void StartTimeOfStopMode()
    {
        if (timeOfStopMode > 0)
        {
            isBorthRight = (Random.value < 0.5);
            transform.SetWorldPositionX(player.transform.position.x + (isBorthRight ? Globals.CocosToUnity(2000) : Globals.CocosToUnity(-2000)));

            startMove = true;
        }
    }

    public virtual void CheckUpdate()
    {
        if (Globals.resetControls) return;
        Vector2 playerPosition = player.skeletonAnimationTran.position;

        if (rigid)
        {
            Vector2 direct = player.transform.position - transform.position;
            //祖玛移动
            if (monsterData.MoveType == 1 || monsterData.MoveType == 2) 
            {
                if (ZumaQueneIndex <= CheckZumaMoveBackIndex())
                {
                    ZumaMoveTargetPosIndex = ZumaPathPosIndex - 1;
                }
                else
                {
                    ZumaMoveTargetPosIndex = ZumaPathPosIndex + 1;
                }
                direct = GameSharedData.Instance.ZumaPathsPosList[zumaPathIndex][ZumaMoveTargetPosIndex] - (Vector2)transform.position;
                transform.Translate(direct.normalized * stats.speed * Time.deltaTime);
                if (Vector2.Distance(transform.position, GameSharedData.Instance.ZumaPathsPosList[zumaPathIndex][ZumaMoveTargetPosIndex]) <= 0.2f)
                {
                    ZumaPathPosIndex = ZumaMoveTargetPosIndex;
                }
            }
            else
            {
                rigid.velocity = stats.speed * direct.normalized;
            }

            //Debug.Log("stats.speed = " + stats.speed + "   rigid.velocity=" + rigid.velocity);
        }
        else
        {
            rigid = gameObject.GetComponentInChildren<Rigidbody2D>();
            //rigid.bodyType = RigidbodyType2D.Kinematic;
        }
        //transform.Translate(direct.normalized * stats.speed * Time.deltaTime * 0.60f);
        if(monsterData.MoveType != 1 && monsterData.MoveType != 2) CheckSpeed(playerPosition);
        if (CheckCollidePlayer())
        {
            if (isDestroyed) return;
            //_allowKillPoint = false;
            isDestroyed = true;
            //怪物撞击玩家爆炸伤害是怪物攻击伤害的11倍
            player.GotHit(stats.bulletDamage * 11f);
            GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1, 1, 0);
            //Destroy();
            GameManager.instance.physicsManager.DestroyEnemy(this);
            GameManager.instance.missionManager.autoKillPoints++;
            //KillCount();
        }
    }
    private void CheckSpeed(Vector2 playerPos)
    {
        if (isReduceSpeed) return;
        float count = LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.攻击距离);
        float percent = LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.攻击距离);
        float closestDistance = (Globals.playerAssistDirection + count) * (1 + percent / 10000f);
        var distance = Vector2.Distance(playerPos, transform.position);
        if (distance < closestDistance)
        {
            isReduceSpeed = true;

            // 当前速度
            var sp = stats.speed;
            // 目标速度
            var speed = sp * (1f - reducePercent / 100f);
            // 改速度需要的时间(秒)，数值越小变的越快
            var duration = 2 * closestDistance * 0.6f / (sp + speed);

            // 使速度线性渐变
            DOTween.To(() => stats.speed, x => stats.speed = x, speed, duration).OnComplete(() =>
            {
                //Debug.Log($"完成改速度: 从 {sp} 到 {stats.speed}");
            });
        }
    }

    //不走physicsManager的计数，这里要加计数以及刷波数
    public void KillCount()
    {
        if (_allowKillPoint) return;
        Debug.Log("0");
        //GameManager.instance.missionManager.AddPoint();
        GameData.instance.fileHandler.totalKills++;
        GameManager.instance.killsThisRun++;
        GameManager.instance.playerHud.survivalCurPoint = GameManager.instance.killsThisRun;
        if (Globals.gameType == GameType.Survival)
        {

            if (GameManager.instance.killsThisRun == (Globals.totalEnemiesInCurrentWave + Globals.enemiesTillLastWave))
            {

                Globals.enemiesTillLastWave += Globals.totalEnemiesInCurrentWave;
                DOTween.Sequence().AppendInterval(1f).AppendCallback(
                    () =>
                    {
                        Observer.DispatchCustomEvent("newWave");
                    }).Play();
            }
        }
    }

    public virtual void PlayDestroySound()
    {
        LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", dieathSoundID);
    }

    /// <summary>
    /// 检测祖玛队列需要回退的第一个对象下标
    /// </summary>
    /// <returns></returns>
    private int CheckZumaMoveBackIndex()
    {
        if (GameSharedData.Instance.ZumaEnemies[zumaPathIndex].Count < 2) return -1;
        for (int i = GameSharedData.Instance.ZumaEnemies[zumaPathIndex].Count - 2; i >= 0; i--)
        {
            if (GameSharedData.Instance.ZumaEnemies[zumaPathIndex][i].ZumaPathPosIndex > GameSharedData.Instance.ZumaEnemies[zumaPathIndex][i + 1].ZumaPathPosIndex
                && GameSharedData.Instance.ZumaEnemies[zumaPathIndex][i].ZumaQueneIndex < GameSharedData.Instance.ZumaEnemies[zumaPathIndex][i + 1].ZumaQueneIndex - 1)
            {
                return GameSharedData.Instance.ZumaEnemies[zumaPathIndex][i].ZumaQueneIndex;
            }
        }
        return -1;
    }
}
