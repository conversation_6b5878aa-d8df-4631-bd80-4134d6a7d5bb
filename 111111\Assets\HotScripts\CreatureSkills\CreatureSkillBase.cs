﻿using System.Collections.Generic;
using System.Threading;

using Cysharp.Threading.Tasks;

using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 生物使用一轮技能(技能结束后应销毁)
    /// </summary>
    public class CreatureSkillBase : System.IDisposable
    {
        /// <summary>
        /// 使用技能的生物
        /// </summary>
        public CreatureBase Creature { get; set; }

        /// <summary>
        /// 使用的技能
        /// </summary>
        public SkillProps Skill { get; set; }
        /// <summary>
        /// 本轮技能预设的结束时间
        /// </summary>
        public float Preset_FinshTime { get; set; }

        /// <summary>
        /// 本次技能的取消令牌
        /// </summary>
        public CancellationTokenSource CTS_Skill { get; } = new();

        /// <summary>
        /// 每轮射击的取消令牌
        /// </summary>
        public List<CancellationTokenSource> CTS_Shoots { get; } = new();

        #region IDisposable
        protected bool disposedValue;
        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (!CTS_Skill.IsCancellationRequested)
                    {
                        CTS_Skill.Cancel();
                    }
                    ClearSkill();
                }

                // TODO: 释放未托管的资源(未托管的对象)并重写终结器
                // TODO: 将大型字段设置为 null
                disposedValue = true;
            }
        }

		// // TODO: 仅当“Dispose(bool disposing)”拥有用于释放未托管资源的代码时才替代终结器
		// ~CreatureSkillBase()
		// {
		//     // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
		//     Dispose(false);
		// }

		public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(true);
            System.GC.SuppressFinalize(this);
        }
        #endregion

        /// <summary>
        /// 使用技能
        /// </summary>
        /// <remarks>按一轮攻击的持续时长预设结束时间</remarks>
        public virtual async UniTaskVoid DoSkill()
        {
            var attackDuration = Skill.CsvRow_CatSkill.AttackDuration;
            Preset_FinshTime = Time.time + attackDuration;

            Creature.PlayAnimation("attack01");

            await UniTask.SwitchToMainThread();
        }

        /// <summary>
        /// 立即停止本轮技能
        /// </summary>
        public virtual void StopSkill()
        {
            Dispose();
        }

        /// <summary>
        /// 清除该技能对场景的所有影响(比如,销毁或还回对象池等)
        /// </summary>
        public virtual void ClearSkill()
        {
            // 如果不是怪物的技能，则从所有怪物的 被锁列表 中移除本技能
            if (Skill.CreatureProps.CreatureType is not X.PB.CreatureType.CreatureTypeMonster)
            {
                GameSharedData.Instance.enemyList.ForEach(enemy =>
                {
                    if (enemy)
                    {
                        enemy.LockedBySkills.Remove(this);
                    }
                });
            }
        }
    }
}
