﻿using System;
using UnityEngine;
using Spine.Unity;
using System.Collections;
using DG.Tweening;

public class Generator : Enemy
{
    [HideInInspector] public bool _isDead = false;
    public static Action GeneratorDestroyed;
    [HideInInspector] public string tweenID;

    private PlayerPing missionPing;

    private void Awake()
    {
        tweenID = "Generator" + GetInstanceID();
        schedulerId = "GeneratorS" + GetInstanceID();
    }

    public override void Init()
    {
        if (initialized)
            return;
        isBoss = false;
        InitStats();
        Globals.numberOfEnemies++;

        DOTween.Sequence().SetId(schedulerId).AppendInterval(1f).AppendCallback(() =>
        {
            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);


            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);
        }).Play();

        explosionType = Explosions.ExplosionType.ExplosionTypeBuilding;
        enemyCollisionRadius = 1.5f * 1.5f;
        base.Init();
        CancelRelocate();
    }

    private void Update()
    {

    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 3.5f + UnityEngine.Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + UnityEngine.Random.value;
        stats.health = baseStats.health = 1000;//5500;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 5;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        return base.CheckCollision(P1);
    }

    public override void Destroy()
    {
        Globals.numberOfEnemies--;
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        _isDead = true;
        Invoke(nameof(DelayedDestroy), 0.2f);
        //this.runAction(Sequence::create(DelayTime::create(0.2f), RemoveSelf::create(), NULL));
        //GeneratorDestroyed?.Invoke();
        Observer.DispatchCustomEvent("GeneratorDestroyed");
    }

    public void DelayedDestroy()
    {
        base.Destroy();
    }
}
