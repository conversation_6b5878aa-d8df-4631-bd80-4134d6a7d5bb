using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using Spine.Unity;
using DG.Tweening;

public class PlayerShop : MonoBehaviour
{
    SkeletonAnimation skeletonAnimation;
    [SerializeField] Transform bulletsContainer, plasmaEffectParent, laserParent;
    [SerializeField] private RuntimeAnimatorController plasmaAnimatorController;
    [SerializeField] private SkeletonAnimation upgradeEffectSkeleton;
    [SerializeField] Bullet playerBulletPrefab;
    [SerializeField] Sprite[] bulletSprites;
    [SerializeField] int maxBullets;
    [SerializeField] private GameObject bulletFlashParent, multiCanonEffectPrefab, plasmaEffectPrefab,
        rocketEffectPrefab, laserPrefab, laserImpactPrefab, flameThrowerEffectPrefab, flameThrowerPrefab,
        backfireEffectPrefab;

    private GameObject[] bulletFlash;
    private GameObject laserStartImpact, laserEndImpact;
    private SpriteRenderer laserRenderer;
    private Animator multiCanonEffectAnim, plasmaEffectAnim, rocketEffectAnim, flameThrowerEffectAnim, flameThrowerAnim,
        backfireAnim;
    private ProtonBulletSlot[] protonBulletSlots;
    FrontGunType currentFrontGun, selectedFrontGun;
    PlaneType currentPlane, selectedPlane;
    RearGunType currentRearGun, selectedRearGun;
    int frontGunLevel, rearGunLevel, backFireLevel = 3;
    bool incineratorUsable, isInitialized;
    List<Bullet> bulletPoolList;

    private void Awake()
    {
        skeletonAnimation = GetComponent<SkeletonAnimation>();

        selectedFrontGun = (FrontGunType)PlayerPrefs.GetInt("Category1");
        currentFrontGun = selectedFrontGun;

        selectedRearGun = (RearGunType)PlayerPrefs.GetInt("Category2");
        currentRearGun = selectedRearGun;

        selectedPlane = (PlaneType)PlayerPrefs.GetInt("Category3");
        currentPlane = selectedPlane;
    }

    // Start is called before the first frame update
    public void Init()
    {
        if (isInitialized)
            return;

        isInitialized = true;
        InitBulletFlash();
        InstantiateEffects();
        InitLaser();

        UpdateFrontGunLevel();
        UpdateRearGunLevel();

        bulletPoolList = new List<Bullet>();

        for(int i = 0; i < maxBullets; i++)
        {
            Bullet bullet = Instantiate(playerBulletPrefab, bulletsContainer);

            bullet.GetSpriteRendererComponent().sortingLayerName = "Default";
            bullet.GetSpriteRendererComponent().sortingOrder = 6;
            bulletPoolList.Add(bullet);
        }

        protonBulletSlots = new ProtonBulletSlot[3];

        for (int i = 0; i < protonBulletSlots.Length; i++)
        {
            protonBulletSlots[i].isOccupied = false;
            protonBulletSlots[i].angle = 120 * i;
        }

        upgradeEffectSkeleton.skeletonDataAsset.GetAnimationStateData().SetMix("upgradeGlow", "upgradeGlow", 0);
        upgradeEffectSkeleton.gameObject.SetActive(false);
    }

    public void PlayUpgradeEffect()
    {

        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.powerUpCollected);
        DOTween.Kill("UpgradeEffect");
        upgradeEffectSkeleton.gameObject.SetActive(true);
        upgradeEffectSkeleton.state.SetAnimation(0, "upgradeGlow", false);

        DOTween.Sequence().SetId("UpgradeEffect").AppendInterval(0.6f).AppendCallback(() =>
        {
            upgradeEffectSkeleton.gameObject.SetActive(false);
        });
    }

    private void Update()
    {
        if (currentRearGun != RearGunType.ProtonCanon)
            return;

        float radius = Globals.CocosToUnity(250);
        float turnSpeed = (rearGunLevel + 1) * 1.5f * 3;

        for (int i = 0; i < protonBulletSlots.Length; i++)
        {
            if (protonBulletSlots[i].isOccupied)
            {
                protonBulletSlots[i].bullet.transform.position = transform.position * Vector2.one
            + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
            radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
            }

            protonBulletSlots[i].angle -= turnSpeed * Time.deltaTime * 60;
            protonBulletSlots[i].angle = protonBulletSlots[i].angle < 0 ? 360 + protonBulletSlots[i].angle
                : protonBulletSlots[i].angle;
        }
    }

    public bool CheckEquippedItem(int tabNum, int itemNum)
    {
        if (tabNum == 0)
            return itemNum == (int)selectedFrontGun;
        else if(tabNum == 1)
            return itemNum == (int)selectedRearGun;
        else
            return itemNum == (int)selectedPlane;
    }

    public void SelectFrontGun(int index)
    {
        selectedFrontGun = (FrontGunType)index;
        Globals.frontGun = selectedFrontGun;
    }
    public void SelectRearGun(int index)
    {
        selectedRearGun = (RearGunType)index;
        Globals.rearGun = selectedRearGun;
    }
    public void SelectPlane(int index)
    {
        selectedPlane = (PlaneType)index;
        Globals.playerPlane = selectedPlane;
    }

    public void UpdateFrontGunLevel()
    {
        string gunName = ((GameData.instance.GetShop()["Category1"] as PList)["Gun" + ((int)currentFrontGun+1)] as PList)["Name"] as string;
        frontGunLevel = PlayerPrefs.GetInt(gunName);

        if (laserStartImpact.activeSelf)
            StartLaser();
    }

    public void UpdateRearGunLevel()
    {
        string gunName = ((GameData.instance.GetShop()["Category2"] as PList)["Gun" + ((int)currentRearGun + 1)] as PList)["Name"] as string;
        rearGunLevel = PlayerPrefs.GetInt(gunName);
    }

    public void ResetFrontGun() => SetFrontGun((int)selectedFrontGun);
    public void SetFrontGun(int gunType)
    {
        currentFrontGun = (FrontGunType)gunType;

        string gunName = ((GameData.instance.GetShop()["Category1"] as PList)["Gun" + ((int)currentFrontGun + 1)] as PList)["Name"] as string;
        frontGunLevel = PlayerPrefs.GetInt(gunName);

        skeletonAnimation.skeleton.SetAttachment("frontgun4", null);
        skeletonAnimation.skeleton.SetAttachment("frontgun3", null);
        skeletonAnimation.skeleton.SetAttachment("frontgun2", null);
        skeletonAnimation.skeleton.SetAttachment("frontgun", null);
        skeletonAnimation.skeleton.SetAttachment("machinegun", null);
        skeletonAnimation.skeleton.SetAttachment("gunFront", null);
        skeletonAnimation.skeleton.SetAttachment("plasmacannon", null);
        skeletonAnimation.skeleton.SetAttachment("rocketlauncher", null);
        skeletonAnimation.skeleton.SetAttachment("laserGun", null);
        if(laserRenderer) StopLaser();

        if (currentFrontGun == FrontGunType.MachineGun)
        {
            skeletonAnimation.skeleton.SetAttachment("frontgun4", "frontgun");
            skeletonAnimation.skeleton.SetAttachment("frontgun3", "frontgun");
            skeletonAnimation.skeleton.SetAttachment("frontgun2", "frontgun");
            skeletonAnimation.skeleton.SetAttachment("frontgun", "frontgun");
        }
        else if (currentFrontGun == FrontGunType.MultiCanon)
        {
            skeletonAnimation.skeleton.SetAttachment("machinegun", "machinegun");
            skeletonAnimation.skeleton.SetAttachment("gunFront", "gunFront");
        }
        else if (currentFrontGun == FrontGunType.Plasma)
        {
            skeletonAnimation.skeleton.SetAttachment("plasmacannon", "plasmacannon");
        }
        else if (currentFrontGun == FrontGunType.Rocket)
        {
            skeletonAnimation.skeleton.SetAttachment("rocketlauncher", "rocketlauncher");
        }
        else if (currentFrontGun == FrontGunType.Laser)
        {
            skeletonAnimation.skeleton.SetAttachment("laserGun", "laserGun");
            if (laserRenderer) StartLaser();
        }
    }

    public void ResetPlane() => SetPlane((int)selectedPlane);
    public void SetPlane(int planeType)
    {
        currentPlane = (PlaneType)planeType;

        skeletonAnimation.skeleton.SetSkin("playerPlane" + (planeType+1).ToString());
        skeletonAnimation.state.SetAnimation(0, "flying" + (planeType + 1).ToString(), true);

        string ch = "Gun" + (planeType+1);
        var planeStats = ((GameData.instance.GetShop()["Category3"] as PList)[ch] as PList)["Stats"] as PList;

        int stat1 = (int)((planeStats["stat1"] as PList)["Level"] as PList)["L1"];
        int stat2 = (int)((planeStats["stat2"] as PList)["Level"] as PList)["L1"];
        int stat3 = (int)((planeStats["stat3"] as PList)["Level"] as PList)["L1"];
        int stat4 = (int)((planeStats["stat4"] as PList)["Level"] as PList)["L1"];

        MainMenuController.instance.planeStat1TMP.text = GameData.instance.fileHandler.playerLevel.ToString();
        MainMenuController.instance.planeStat2TMP.text = GameData.instance.fileHandler.PlayerAttack.ToString();
        MainMenuController.instance.planeStat3TMP.text = GameData.instance.fileHandler.PlayerHealth.ToString();
        MainMenuController.instance.planeStat4TMP.text = GameData.instance.fileHandler.PlayerEnergy.ToString();
    }

    public void ResetRearGun()
    {
        string gunName = ((GameData.instance.GetShop()["Category2"] as PList)["Gun1"] as PList)["Name"] as string;

        SetRearGun((int)selectedRearGun, PlayerPrefs.GetInt(gunName) > 0);
    }

    public void SetRearGun(int gunType, bool canUseIncinerator = true)
    {
        currentRearGun = (RearGunType)gunType;

        skeletonAnimation.skeleton.SetAttachment("flameGlow", null);
        skeletonAnimation.skeleton.SetAttachment("flame", null);
        skeletonAnimation.skeleton.SetAttachment("flameThrower", null);
        skeletonAnimation.skeleton.SetAttachment("backGlow", null);
        skeletonAnimation.skeleton.SetAttachment("backGun", null);
        skeletonAnimation.skeleton.SetAttachment("protonBar", null);
        skeletonAnimation.skeleton.SetAttachment("protonCannon", null);
        skeletonAnimation.skeleton.SetAttachment("protonLight", null);

        if (currentRearGun == RearGunType.FlameThrower)
        {
            incineratorUsable = canUseIncinerator;

            if (!canUseIncinerator)
                return;

            skeletonAnimation.skeleton.SetAttachment("flameGlow", "flameGlow");
            skeletonAnimation.skeleton.SetAttachment("flame", "flame");
            skeletonAnimation.skeleton.SetAttachment("flameThrower", "flameThrower");

            if(protonBulletSlots != null)
            {
                for (int i = 0; i < protonBulletSlots.Length; i++)
                {
                    if (protonBulletSlots[i].bullet != null)
                    {
                        protonBulletSlots[i].bullet.HasHit();
                        protonBulletSlots[i].isOccupied = false;
                        protonBulletSlots[i].bullet = null;
                    }
                }
            }
        }

        else if (currentRearGun == RearGunType.RearBackFire)
        {
            skeletonAnimation.skeleton.SetAttachment("backGlow", "backGlow");
            skeletonAnimation.skeleton.SetAttachment("backGun", "backGun");

            if (protonBulletSlots != null)
            {
                for (int i = 0; i < protonBulletSlots.Length; i++)
                {
                    if (protonBulletSlots[i].bullet != null)
                    {
                        protonBulletSlots[i].bullet.HasHit();
                        protonBulletSlots[i].isOccupied = false;
                        protonBulletSlots[i].bullet = null;
                    }
                }
            }
        }
        else if (currentRearGun == RearGunType.ProtonCanon)
        {
            skeletonAnimation.skeleton.SetAttachment("protonBar", "protonBar");
            skeletonAnimation.skeleton.SetAttachment("protonCannon", "protonCannon");
            skeletonAnimation.skeleton.SetAttachment("protonLight", "protonLight");
        }
    }

    private void InstantiateEffects()
    {
        {
            GameObject obj = Instantiate(multiCanonEffectPrefab);

            var bone = skeletonAnimation.skeleton.FindBone("LOOSECANNON");
            var pos = bone.GetWorldPosition(transform);

            obj.transform.parent = transform;
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(2, 2, 1);

            multiCanonEffectAnim = obj.GetComponent<Animator>();
            multiCanonEffectAnim.Play("Main", 0, 1);
        }
        
        {
            GameObject obj = Instantiate(plasmaEffectPrefab);
            obj.transform.GetChild(0).GetComponent<SpriteRenderer>().sortingLayerName = "Default";
            obj.transform.GetChild(0).GetComponent<SpriteRenderer>().sortingOrder = 6;
            obj.transform.parent = plasmaEffectParent;
            obj.transform.localPosition = new Vector3(0.42f, 0, 0);
            obj.transform.localScale = new Vector3(3.5f, 3.5f, 1);

            plasmaEffectAnim = obj.transform.GetChild(0).GetComponent<Animator>();
            plasmaEffectAnim.Play("Main", 0, 1);
        }

        {
            GameObject obj = Instantiate(rocketEffectPrefab);

            Spine.Bone bone = skeletonAnimation.skeleton.FindBone("ROCKET");
            obj.transform.position = bone.GetWorldPosition(transform);
            obj.transform.position -= Vector3.right * 1.5f;

            obj.transform.parent = transform;
            obj.transform.localScale = new Vector3(4, 4, 1);

            rocketEffectAnim = obj.GetComponent<Animator>();
            rocketEffectAnim.Play("Main", 0, 1);
        }

        {
            GameObject obj = Instantiate(flameThrowerEffectPrefab);

            var bone = skeletonAnimation.skeleton.FindBone("flame");
            var pos = bone.GetWorldPosition(transform);

            obj.transform.parent = transform;
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(1.5f, 1.5f, 1);
            obj.GetComponent<MeshRenderer>().sortingLayerName = "Default";
            obj.GetComponent<MeshRenderer>().sortingOrder = 4;

            flameThrowerEffectAnim = obj.GetComponent<Animator>();
            obj.SetActive(false);


            GameObject obj1 = Instantiate(flameThrowerPrefab);

            obj1.transform.parent = transform;
            obj1.transform.position = pos - Vector3.right;
            obj1.transform.localScale = new Vector3(1, 1, 1);
            obj1.transform.GetChild(0).GetComponent<MeshRenderer>().sortingLayerName = "Default";
            obj1.transform.GetChild(0).GetComponent<MeshRenderer>().sortingOrder = 4;

            flameThrowerAnim = obj1.transform.GetChild(0).GetComponent<Animator>();
            float flameLevel = 1;
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 1);
        }

        {
            GameObject obj = Instantiate(backfireEffectPrefab);

            Vector3 spawnPoint = transform.position * Vector2.one
                     + new Vector2(-Globals.CocosToUnity(80), 0);

            obj.transform.position = spawnPoint;
            obj.transform.localScale = new Vector3(2f, 2f, 1);
            obj.transform.parent = transform;
            backfireAnim = obj.GetComponent<Animator>();
            backfireAnim.Play("Main", 0, 1);
        }
    }

    private void InitBulletFlash()
    {
        SpriteRenderer[] t = bulletFlashParent.GetComponentsInChildren<SpriteRenderer>();
        bulletFlash = new GameObject[t.Length];
        for (int i = 0; i < t.Length; i++)
        {
            bulletFlash[i] = t[i].gameObject;
            bulletFlash[i].SetActive(false);
        }
    }

    public void StartShooting()
    {
        StartCoroutine(nameof(ShootCouroutine), 0.17f);
        InvokeRepeating("InitRearWeapons", 2, 1);
    }
    public void StopShooting()
    {
        if (this == null)
            return;

        if (protonBulletSlots != null)
        {
            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                if (protonBulletSlots[i].bullet != null)
                {
                    protonBulletSlots[i].isOccupied = false;
                    protonBulletSlots[i].bullet = null;
                }
            }
        }

        if (bulletPoolList != null)
        {
            foreach (Bullet bullet in bulletPoolList)
            {
                if (bullet.isInUse && bullet != null) bullet.HasHit();
            }
        }
        
        StopAllCoroutines();
        CancelInvoke();
    }

    private IEnumerator ShootCouroutine(float waitTime = 0.17f)
    {
        while (true)
        {
            if (currentFrontGun == FrontGunType.Plasma || currentFrontGun == FrontGunType.Rocket)
            {
                ShootSlowGuns();
                waitTime = 1.5f;
            }
            else
            {
                Shoot();
                waitTime = 0.17f;
            }
            yield return new WaitForSeconds(waitTime);
        }
    }

    private void Shoot()
    {
        if (bulletPoolList == null)
            return;

        if (currentFrontGun == FrontGunType.MachineGun)
        {

            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 0.2f);
            for (int i = 0; i < frontGunLevel; i++)
            {
                Bullet bullet = null;

                bool didFindBullet = false;
                foreach (Bullet b in bulletPoolList)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }
                bullet.SetBulletType(Bullet.BulletType.FrontMachineGun);
                bullet.setDamage(5);
                bullet.isRemovable = true;
                bullet.SetSpriteFrame(bulletSprites[0]);
                bullet.transform.localScale *= 3f;


                float distance;
                float rotation;
                bullet.setReactToWater(false);

                float duration = 0.8f;
                bullet.transform.position = transform.position;
                bullet.transform.rotation = transform.rotation;
                bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));

                if (i < 4)
                {
                    string boneName = "mg" + (i + 1).ToString();
                    Spine.Bone bone = skeletonAnimation.skeleton.FindBone(boneName);
                    Vector3 pos = bone.GetWorldPosition(transform);
                    bullet.transform.position = (Vector2)pos + Vector2.right * 0.5f;

                    if (i < 2)
                        bullet.spriteRenderer.sortingOrder = 4;
                    else
                        bullet.spriteRenderer.sortingOrder = 6;
                    for (int j = 0; j < bulletFlash.Length; j++)
                    {
                        if (!bulletFlash[j].activeSelf)
                        {
                            bulletFlash[j].transform.position = pos;
                            bulletFlash[j].SetActive(true);

                            if (i + 1 == 3 || i + 1 == 4)
                            {
                                bulletFlash[j].GetComponent<SpriteRenderer>().sortingOrder = 6;
                            }
                            else
                            {
                                bulletFlash[j].GetComponent<SpriteRenderer>().sortingOrder = 4;
                            }
                            bulletFlash[j].transform.localScale = new Vector2(0.75f, 0.75f);
                            break;
                        }
                    }

                }
                if (i == 4)
                {
                    bullet.spriteRenderer.sortingOrder = 4;
                }

                Vector2 dest = new Vector2(Globals.CocosToUnity(1300), 0);
                bullet.gameObject.SetActive(true);
                bullet.isMenuBullet = true;
                bullet.PlayBulletAnim(duration, dest, true);
                bullet.isInUse = true;
            }
        }
        else if (currentFrontGun == FrontGunType.MultiCanon)
        {
            int numberOfbullets = 5;
            if (frontGunLevel == 1)
            {
                numberOfbullets = 3;
            }

            float rangeDistance = Globals.CocosToUnity(550) + Globals.CocosToUnity(frontGunLevel * 100);

            string boneName = "LOOSECANNON";
            Spine.Bone bone = skeletonAnimation.skeleton.FindBone(boneName);
            Vector3 bonePos = bone.GetWorldPosition(transform);

            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.looseCannon,0.3f);
            for (int i = 0; i < numberOfbullets; i++)
            {
                Bullet bullet = null;
                bool didFindBullet = false;
                foreach (Bullet b in bulletPoolList)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                bullet.SetBulletType(Bullet.BulletType.FrontMultiCanon);
                bullet.SetSpriteFrame(bulletSprites[0]);

                float rotation = 0;
                Vector3 pos = transform.position;

                if (i == 0)
                {
                    pos = bonePos;

                    bullet.setDamage(8 + (FileHandler.PlayerLevel * 2 * 0.25f));

                    if (frontGunLevel > 2)
                    {
                        bullet.transform.localScale = Vector2.one * 1.5f;
                        bullet.setDamage(20 + (FileHandler.PlayerLevel * 2 * 0.25f));

                    }

                    if (frontGunLevel > 4)
                    {
                        bullet.transform.localScale = Vector2.one * 3f;
                        bullet.setDamage(25 + (FileHandler.PlayerLevel * 2 * 0.25f));
                    }
                    rotation = 0;
                    bullet.spriteRenderer.sortingOrder = 1;
                }
                else if (i == 1)
                {
                    pos = bonePos;

                    if (frontGunLevel == 1 || frontGunLevel == 2)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.localScale = Vector2.one * 0.5f;
                    }
                    if (frontGunLevel == 3)
                    {
                        bullet.setDamage(8 + (frontGunLevel * 2 * 0.25f));
                    }

                    if (frontGunLevel > 3)
                    {
                        bullet.transform.localScale = Vector2.one * 1.5f;
                        bullet.setDamage(15 + (FileHandler.PlayerLevel * 2 * 0.25f));
                    }
                    rotation = -15;
                    bullet.spriteRenderer.sortingOrder = 3;
                }
                else if (i == 2)
                {
                    pos = bonePos;

                    if (frontGunLevel == 1 || frontGunLevel == 2)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.localScale = Vector2.one * 0.5f;
                    }
                    if (frontGunLevel == 3)
                    {
                        bullet.setDamage(8 + (FileHandler.PlayerLevel * 2 * 0.25f));

                    }

                    if (frontGunLevel > 3)
                    {
                        bullet.transform.localScale = Vector2.one * 1.5f;
                        bullet.setDamage(15 + (FileHandler.PlayerLevel * 2 * 0.25f));

                    }
                    rotation = 15;
                    bullet.spriteRenderer.sortingOrder = 2;
                }
                else if (i == 3)
                {
                    pos = bonePos;

                    if (frontGunLevel == 2 || frontGunLevel == 3)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 3);
                        bullet.transform.localScale = Vector2.one * 0.3f;
                    }

                    if (frontGunLevel == 4)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.localScale = Vector2.one * 1.3f;
                    }

                    if (frontGunLevel == 5)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)));
                        bullet.transform.localScale = Vector2.one * 1.5f;
                    }
                    rotation = -30;
                    bullet.spriteRenderer.sortingOrder = 3;
                }
                else if (i == 4)
                {
                    pos = bonePos;

                    if (frontGunLevel == 2 || frontGunLevel == 3)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 3);
                        bullet.transform.localScale = Vector2.one * 0.3f;
                    }

                    if (frontGunLevel == 4)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)) / 2);
                        bullet.transform.localScale = Vector2.one * 0.65f;
                    }

                    if (frontGunLevel == 5)
                    {
                        bullet.setDamage((8 + (FileHandler.PlayerLevel * 2 * 0.25f)));
                        bullet.transform.localScale = Vector2.one * 1.5f;
                    }
                    rotation = 30;
                    bullet.spriteRenderer.sortingOrder = 2;
                }

                float duration = 0.6f;
                bullet.transform.position = pos;
                bullet.transform.localScale *= 1.5f;

                float dir = rotation;
                dir = dir < 0 ? 360 + dir : dir;
                bullet.transform.rotation = Quaternion.Euler(bullet.transform.eulerAngles.x, bullet.transform.eulerAngles.y,
                    dir);

                Vector2 dest = new Vector2(rangeDistance * Mathf.Cos(Mathf.Deg2Rad * dir), rangeDistance * Mathf.Sin(Mathf.Deg2Rad * dir));

                bullet.gameObject.SetActive(true);
                bullet.PlayBulletAnim(duration, dest, true);
                bullet.isInUse = true;
                bullet.isMenuBullet = true;
                bullet.spriteRenderer.sortingLayerName = "Default";
                bullet.spriteRenderer.sortingOrder = 4;
            }
            multiCanonEffectAnim.transform.position = (Vector2)bonePos - Vector2.right * 0.25f;
            multiCanonEffectAnim.Play("Main", 0, 0);
            skeletonAnimation.state.SetAnimation(1, "gunShoot", false);
        }
    }

    private void ShootSlowGuns()
    {
        if (currentFrontGun == FrontGunType.Plasma)
        {
            Bullet bullet = null;

            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.deatomizerSound);
            bool didFindBullet = false;
            foreach (Bullet b in bulletPoolList)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.SetBulletType(Bullet.BulletType.FrontPlasma);

            bullet.SetSpriteFrame(bulletSprites[1]);
            Animator anim = bullet.GetComponent<Animator>();
            anim.runtimeAnimatorController = plasmaAnimatorController;
            bullet.transform.localScale = Vector2.one * 1.75f;
            float duration = 1.0f;

            Spine.Bone bone = skeletonAnimation.skeleton.FindBone("DEATOMIZER");
            var pos = bone.GetWorldPosition(transform);

            bullet.transform.position = pos;
            bullet.transform.rotation = transform.rotation;

            Vector2 dest = new Vector2(Globals.CocosToUnity(1250), 0);

            bullet.gameObject.SetActive(true);
            bullet.isMenuBullet = true;
            bullet.PlayBulletAnim(duration, dest, true);
            bullet.isInUse = true;
            bullet.spriteRenderer.sortingLayerName = "Default";
            bullet.spriteRenderer.sortingOrder = 4;

            plasmaEffectAnim.Play("Main", 0, 0);
            skeletonAnimation.state.SetAnimation(1, "plasmaShoot", false);
        }
        else if (currentFrontGun == FrontGunType.Rocket)
        {

            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.rocketeerShoot,0.25f);
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in bulletPoolList)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.SetBulletType(Bullet.BulletType.FrontRocket);

            bullet.SetSpriteFrame(bulletSprites[2]);
            bullet.transform.localScale = Vector2.one * 1.75f;
            Spine.Bone bone = skeletonAnimation.skeleton.FindBone("ROCKET");


            float duration = 1f;
            var pos = bone.GetWorldPosition(transform);
            bullet.transform.position = pos;
            bullet.transform.right = -transform.right;
            bullet.spriteRenderer.sortingLayerName = "Default";
            bullet.spriteRenderer.sortingOrder = 6;

            Vector2 dest = new Vector2(Globals.CocosToUnity(2000), 0);

            bullet.gameObject.SetActive(true);
            bullet.isMenuBullet = true;
            bullet.PlayBulletAnim(duration, dest, false);
            bullet.isInUse = true;

            rocketEffectAnim.Play("Main", 0, 0);
            skeletonAnimation.state.SetAnimation(1, "rocketShoot", false);
        }
    }

    void InitRearWeapons()
    {
        if (currentRearGun == RearGunType.FlameThrower)
        {
            if (!incineratorUsable)
                return;
            StopCoroutine(nameof(PlayFlameThrowerEffectAnim));
            StartCoroutine(nameof(PlayFlameThrowerEffectAnim));

            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.flameThrowerShoot);
            float flameLevel = 2;
            flameThrowerAnim.Play("flame" + flameLevel.ToString(), 0, 0);
        }
        else if (currentRearGun == RearGunType.ProtonCanon)
        {
            for (int i = 0; i < protonBulletSlots.Length; i++)
            {
                if (!protonBulletSlots[i].isOccupied)
                {
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.magicFire);
                    Bullet bulletLayer = null;

                    bool didFindBullet = false;
                    foreach (Bullet b in bulletPoolList)
                    {
                        if (!b.isInUse)
                        {
                            bulletLayer = b;
                            didFindBullet = true;
                            break;
                        }
                    }
                    if (!didFindBullet)
                    {
                        return;
                    }

                    bulletLayer.InitProtonBullet(
                        (Bullet b) =>
                        {

                        },
                        (Bullet b) =>
                        {

                        }
                    );

                    bulletLayer.gameObject.SetActive(true);
                    bulletLayer.SetBulletType(Bullet.BulletType.ProtonCanon);
                    bulletLayer.isRemovable = true;
                    bulletLayer.isInUse = true;
                    bulletLayer.isMenuBullet = true;
                    bulletLayer.SetSpriteFrame(bulletSprites[3]);
                    bulletLayer.spriteRenderer.sortingOrder = 4;
                    bulletLayer.transform.GetChild(2).gameObject.SetActive(true);
                    TrailRenderer trail = bulletLayer.transform.GetChild(2).GetComponent<TrailRenderer>();
                    trail.startWidth = 1.2f;
                    trail.time = 0.2f;
                    trail.sortingLayerName = "Default";
                    trail.sortingOrder = 3;
                    float radius = Globals.CocosToUnity(250);

                    bulletLayer.transform.position = transform.position * Vector2.one
                        + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                        radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                    bulletLayer.transform.localScale = new Vector3(7.5f, 7.5f, 1);
                    protonBulletSlots[i].bullet = bulletLayer;
                    protonBulletSlots[i].isOccupied = true;

                    break;
                }
            }
        }
        else if (currentRearGun == RearGunType.RearBackFire)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.backFireSound);
            for (int i = 0; i < backFireLevel; i++)
            {
                Bullet bulletLayer = null;

                bool didFindBullet = false;
                foreach (Bullet b in bulletPoolList)
                {
                    if (!b.isInUse)
                    {
                        bulletLayer = b;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                bulletLayer.SetBulletType(Bullet.BulletType.FrontMachineGun);

                bulletLayer.transform.localScale = new Vector3(5, 5, 1);
                bulletLayer.SetSpriteFrame(null);
                bulletLayer.transform.GetChild(1).gameObject.SetActive(true);
                bulletLayer.transform.GetChild(1).GetComponent<SpriteRenderer>().sortingLayerName = "Default";
                bulletLayer.transform.GetChild(1).GetComponent<SpriteRenderer>().sortingOrder = 4;
                float duration = 2;


                float distance;
                float rotation;
                if (i == 0)
                {
                    distance = Globals.CocosToUnity(80 * 2.5f);
                    rotation = 0;
                }
                else if (i == 1)
                {
                    distance = Globals.CocosToUnity(60 * 2.5f);
                    rotation = -10;
                }
                else if (i == 2)
                {
                    distance = Globals.CocosToUnity(60 * 2.5f);
                    rotation = 10;
                }
                else if (i == 3)
                {
                    distance = Globals.CocosToUnity(60 * 2.5f);
                    rotation = -30;
                }
                else if (i == 4)
                {
                    distance = Globals.CocosToUnity(60 * 2.5f);
                    rotation = 30;
                }
                else
                {
                    distance = Globals.CocosToUnity(80 * 2.5f);
                    rotation = 0;
                }

                float rot = 180 + rotation;
                rot = rot < 0 ? 360 + rot : rot % 360;

                Vector3 spawnPoint = transform.position * Vector2.one
                     + new Vector2(distance * Mathf.Cos(rot * Mathf.Deg2Rad), distance * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.transform.position = spawnPoint;

                rot = 180;

                Vector2 dest = new Vector2(Globals.CocosToUnity(1250) * Mathf.Cos(rot * Mathf.Deg2Rad),
                        Globals.CocosToUnity(1250) * Mathf.Sin(rot * Mathf.Deg2Rad));

                bulletLayer.PlayBulletAnim(duration, dest, true);
                bulletLayer.transform.rotation = transform.rotation;
                bulletLayer.gameObject.SetActive(true);
                bulletLayer.isInUse = true;
                bulletLayer.isRemovable = true;
                bulletLayer.isMenuBullet = true;
                bulletLayer.isFlipped = true;

                backfireAnim.Play("Main", 0, 0);
            }
        }
    }

    IEnumerator PlayFlameThrowerEffectAnim()
    {
        flameThrowerEffectAnim.gameObject.SetActive(true);
        flameThrowerEffectAnim.Play("Main", 0, 0);

        yield return new WaitForSeconds(0.367f);

        flameThrowerEffectAnim.gameObject.SetActive(false);
    }

    void InitLaser()
    {
        // Laser Begin
        GameObject laser = Instantiate(laserPrefab);
        laser.transform.parent = laserParent;
        laser.transform.localPosition = Vector3.zero;

        laserRenderer = laser.GetComponent<SpriteRenderer>();
        laserRenderer.sortingLayerName = "Default";
        laserRenderer.sortingOrder = 7;
        laser.SetActive(false);
        //Laser End

        // LaserImpact Begin
        for (int i = 0; i < 2; i++)
        {
            GameObject li = Instantiate(laserImpactPrefab, laserParent);
            li.transform.localScale = new Vector3(1f, 3.5f, 1);
            li.transform.localPosition = Vector3.zero;
            li.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));
            li.SetActive(false);

            if (i == 0)
            {
                li.transform.localRotation = Quaternion.Euler(new Vector3(0, 0, 0));
                laserStartImpact = li;
                var meshR = li.GetComponent<MeshRenderer>();
                meshR.sortingLayerName = "Default";
                meshR.sortingOrder = 6;
            }
            else
            {
                laserEndImpact = li;
                var meshR = li.GetComponent<MeshRenderer>();
                meshR.sortingLayerName = "Default";
                meshR.sortingOrder = 3;
            }
        }
        // LaserImpact End
    }


    void StartLaser()
    {
        if (!gameObject.activeSelf)
            gameObject.SetActive(true);

        StopCoroutine(nameof(ActivateLaser));
        StartCoroutine(nameof(ActivateLaser));
    }
    void StopLaser()
    {

        AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.laserLooped);
        StopCoroutine(nameof(ActivateLaser));
        laserRenderer.gameObject.SetActive(false);
        laserStartImpact.SetActive(false);
        laserEndImpact.SetActive(false);
    }

    IEnumerator ActivateLaser()
    {
        laserRenderer.gameObject.SetActive(true);
        laserStartImpact.SetActive(true);
        AudioManager.instance.PlaySound(AudioType.Loop, Constants_Audio.Audio.laserLooped);
        laserStartImpact.transform.localScale = new Vector3(1f, 1.25f + (frontGunLevel - 1) * 0.45f, 1);
        laserEndImpact.SetActive(true);
        laserEndImpact.transform.localScale = new Vector3(1f, 1.25f + (frontGunLevel - 1) * 0.45f, 1);

        Transform laserTransform = laserRenderer.transform;
        Transform impactStartTransform = laserStartImpact.transform;
        Transform impactEndTransform = laserEndImpact.transform;

        var laserMaterial = laserTransform.GetComponent<MaterialMovement>();
        laserMaterial.ResetOffset();

        float targetScaleX, targetScaleY, xWorldUnits, yWorldUnits;

        targetScaleX = Globals.CocosToUnity(800);
        targetScaleY = 1f + (frontGunLevel - 1) * 0.5f;
        xWorldUnits = laserTransform.localScale.x * laserTransform.root.localScale.x
            * (laserRenderer.sprite.rect.width / laserRenderer.sprite.pixelsPerUnit);
        yWorldUnits = laserTransform.localScale.y * laserTransform.root.localScale.y
            * (laserRenderer.sprite.rect.height / laserRenderer.sprite.pixelsPerUnit);

        laserTransform.localScale = new Vector3(
             laserTransform.localScale.x,
             laserTransform.localScale.y / yWorldUnits * targetScaleY,
             laserTransform.localScale.z
            );

        laserRenderer.size = new Vector2(
            targetScaleX,
            laserRenderer.size.y);

        impactEndTransform.position = impactStartTransform.position - impactEndTransform.right * targetScaleX;

        while (true)
        {
            laserMaterial.OffsetUpdate();

            yield return null;
        }
    }
}
