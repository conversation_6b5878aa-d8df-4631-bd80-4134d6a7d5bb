﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MainMenuScreen : MonoBehaviour
{
    GraphicRaycaster[] graphicRaycasters;
    CanvasGroup canvasGroup;

    private void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
        graphicRaycasters = GetComponentsInChildren<GraphicRaycaster>();
        CloseScreen();
    }

    public void OpenScreen()
    {
        
        canvasGroup.alpha = 1;
        canvasGroup.interactable = true;
        canvasGroup.blocksRaycasts = true;

        foreach (GraphicRaycaster raycaster in graphicRaycasters)
            raycaster.enabled = true;
    }

    public void CloseScreen()
    {
        canvasGroup.alpha = 0;
        canvasGroup.interactable = false;
        canvasGroup.blocksRaycasts = false;

        foreach (GraphicRaycaster raycaster in graphicRaycasters)
            raycaster.enabled = false;
    }
}
