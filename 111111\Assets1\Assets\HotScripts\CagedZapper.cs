using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
public class CagedZapper : MonoBehaviour
{
    private enum CageStates
    {
        Shielded,
        Unshielded

    };

    [SerializeField] private Generator generator;
    [SerializeField] private Zapper sideKick;
    [SerializeField] private PlayerPing ping;
    [SerializeField] private SkeletonAnimation cage;
    [SerializeField] private SkeletonAnimation powerUpEffect;

    private CageStates currentState;
    private bool isSideKickSaved;
    private bool scheduleUpdate = false;

    public void Init()
    {
        isSideKickSaved = false;
        currentState = CageStates.Shielded;
        transform.position= new Vector2 (Globals.CocosToUnity(4000), -0.5f);
        sideKick.SideKickSkeleton.state.SetAnimation(0, "locked", true);
        sideKick.Init();
        sideKick.transform.localPosition = new Vector2(0, 7.6f);
        generator.Init();
        generator.stats.health = 25000;
        generator.stats.maxHealth.Value = 25000;
        generator.transform.SetLocalPositionX(Globals.CocosToUnity(1000));
        generator.transform.parent = null;
        if (Globals.gameModeType == GamePlayMode.Easy)
        {
            generator.stats.maxHealth.Value = generator.stats.maxHealth.Value * 0.3f;
            generator.stats.health = generator.stats.maxHealth.Value;

        }
        if (Globals.gameModeType == GamePlayMode.Hard)
        {
            generator.stats.maxHealth.Value = generator.stats.maxHealth.Value * 1.5f;
            generator.stats.health = generator.stats.maxHealth.Value;

        }

        sideKick.scheduleUpdate = false;
        scheduleUpdate = true;
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        if (currentState == CageStates.Shielded)
        {
            if (generator._isDead == true)
            {
                //        enemySprite->setAnimation(0, "idle", true);
                currentState = CageStates.Unshielded;
                sideKick.SideKickSkeleton.state.SetAnimation(0, "select", false);
                sideKick.SideKickSkeleton.state.AddAnimation(0, "idle", true);
                cage.state.SetAnimation(0, "end", true);
                powerUpEffect.gameObject.SetActive(true);
                powerUpEffect.state.SetAnimation(0, "powerUp", false);
                sideKick.transform.parent = null;
                DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() => { powerUpEffect.gameObject.SetActive(false); }).Play();
                scheduleUpdate = false;
                GameManager.instance.missionManager.MissionComplete();
                DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() =>
                {
                    sideKick.enabled = true;
                }).AppendInterval(0.1f).AppendCallback(() =>
                {
                    sideKick.scheduleUpdate = true;
                }).Play();
                Observer.DispatchCustomEvent(Globals.DEACTIVE_LASER_EVENT);
            }
        }
    }

}
