using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using TMPro;
using Spine.Unity;
public enum TutorialState
{
    none,
    Movement,
    Movement_Start,
    LockPlayerMovement,
    Shooting,
    Aim_Assist_Rotation,
    Aim_Assist_Settings,
    Health_Regen,
    Health_Regen_Complete,
    SpawnSidekicks,
    Start_Fight,
    Enter_Special_Ability_Mode,
    Activate_Special_Ability_Mode,
    Exit_Special_Ability_Mode,
    Pre_Berserk_Mode,
    Enter_Berserk_Mode,
    Exit_Berserk_Mode,
    Spawn_Mafia_Kitty,
    Spawn_Spider_Kat,
    EndTutorial,
    SkipTutorial


};

public class ControlsTutorialManager : MonoBehaviour
{
    [SerializeField] private Image specialAbilityButtonUp;
    [SerializeField] private SpriteRenderer blackOverlay;
    [SerializeField] private TutorialDialogue td;
    [SerializeField] private GameObject KeyBoardControlsObject;
    [SerializeField] private TextMeshProUGUI KeyBoardControlsLabel;
    [SerializeField] private GameObject mouseControlObjects;
    [SerializeField] private TextMesh<PERSON><PERSON>UGUI mouseAimLabel;
    [SerializeField] private TextM<PERSON><PERSON><PERSON>UGUI mouseShootLabel;
    [SerializeField] private GameObject JoyStickObject;
    [SerializeField] private TextMeshProUGUI JoyStickObjectLabel;
    [SerializeField] private GameObject speacialAbilityButtonMobile;
    [SerializeField] private GameObject speacialAbilityButton;
    [SerializeField] private GameObject JoyStickFireButton;
    [SerializeField] private TextMeshProUGUI JoyStickFireButtonLabel;
    [SerializeField] private CustomButton fireButton;
    [SerializeField] private TextMeshProUGUI fireButtonLabel;
    [SerializeField] private CustomButton FTUESkipButton;
    [SerializeField] private CustomButton skipButton;
    [SerializeField] private SkeletonGraphic leftDpadTutorialHand;
    [SerializeField] private SkeletonGraphic rightDpadTutorialHand1;
    [SerializeField] private Image rightShoulderButton;
    [SerializeField] private CustomButton r1Button;
    [SerializeField] private SkeletonAnimation[] sidekickSpawnEffect;
    [HideInInspector] public bool rightShoulderGlyph = false;
    [HideInInspector] public bool isLaserTowerDestroyed = false;

    public TutorialState currentState = TutorialState.none;
    private bool mafiaKittySpawned = false;
    private double originalPlayerRegen;
    private string tweenId;
    private string schedulerId;


    public void Create()
    {

    }

    public void CreateMovementControls()
    {

#if UNITY_ANDROID || UNITY_IOS

        Observer.DispatchCustomEvent("enable_movement");
        Observer.DispatchCustomEvent("hide_shoot_button");
        InputController.instance.SetDash(false);
        InputController.instance.SetSpecialAbility(false);
        if (!Globals.isJoystickConnected)
        {
            Observer.DispatchCustomEvent("show_movement_dpad");
        }
#else
if (Globals.isJoystickConnected)
        {
            JoyStickObject.transform.parent.gameObject.SetActive(true);
            JoyStickObject.DoBlink(3, 3);
            SetMovementControls(false, "");
        }
        else
        {
            KeyBoardControlsObject.gameObject.SetActive(true);
        }

#endif
    }

    public void SetMovementControls(bool enable, string textToDisplay)
    {
#if UNITY_ANDROID || UNITY_IOS

#else
        if (Globals.isJoystickConnected && JoyStickObject)
        {
            JoyStickObject.gameObject.SetActive(enable);
            JoyStickObjectLabel.gameObject.SetActive(enable);
            JoyStickObjectLabel.text = textToDisplay;
        }
        else
        {
            KeyBoardControlsObject.gameObject.SetActive(enable);
            KeyBoardControlsLabel.text = textToDisplay;
        }
#endif

    }
    public void SetJoystickFirebutton(bool enable, string textToDisplay)
    {
#if UNITY_ANDROID || UNITY_IOS

#else
        if (Globals.isJoystickConnected && fireButton)
        {
            fireButton.gameObject.SetActive(enable);
            fireButtonLabel.gameObject.SetActive(enable);
            fireButtonLabel.text = textToDisplay;
        }
        else
        {
            mouseControlObjects.gameObject.SetActive(enable);
            mouseAimLabel.text = "Aim";
            mouseShootLabel.text = "Shoot";
        }
#endif
    }
    public void CreateJoystickFirebutton()
    {
        if (Globals.isJoystickConnected)
        {
            if (rightShoulderGlyph)
            {
                fireButton.transform.GetChild(1).gameObject.SetActive(true);
            }
            else
            {
                fireButton.defaultLabel.text = "R1";
            }
            fireButton.CallForAttention(1.0f);
            fireButton.SetButtonColor(fireButton.YELLOW);
            SetJoystickFirebutton(false, "");
        }
        else
        {
#if UNITY_ANDROID || UNITY_IOS
#else
            mouseControlObjects.gameObject.SetActive(true);
            mouseAimLabel.text = "Aim";
            mouseShootLabel.text = "Shoot";
#endif
        }
    }

    private void CreateFTUESkipButton()
    {

        {
            Observer.RegisterCustomEvent(gameObject, "Move_Skip_Button_Down", () =>
             {
                 //skipButton.runAction(MoveTo::create(0.5, Vec2(winMinBounds.x + 200, winMaxBounds.y - 180)));

             });
        }
        {
            Observer.RegisterCustomEvent(gameObject, "Move_Skip_Button_Up", () =>
            {
                //skipButton.runAction(MoveTo::create(0.5, Vec2(winMinBounds.x + 200, winMaxBounds.y - 80)));

            });
        }
        skipButton.gameObject.SetActive(true);
        skipButton.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["skipButton"] as string;
        skipButton.SetButtonColor(skipButton.GREEN);
        skipButton.defaultAction = () =>
        {

            GameManager.instance.SkipButtonCall();

        };
        {
            float a = (GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["skipButton"] as string).Length;
            a = a * 15;

            //skipButton.setContentSize(cocos2d::Size(280 + a, 115));

        }

        //skipButton.setScale(0.3);

        //skipButton.addToButton(skipLabel, false);

        //this.addChild(skipButton, 6);
        //skipButton.setPosition(winMinBounds.x + 200, winMaxBounds.y - 80);
    }


    public void ChangeState(TutorialState amts)
    {
        if (amts == currentState)
            return;
        if (Globals.isGameInTutorialState)
        {
            currentState = amts;

            print(amts);
            switch (amts)
            {

                case TutorialState.Movement:
                    Globals.canBerserk = false;
                    Globals.isFTUETutorial = true;
                    Globals.allowSidekickShoot = false;
                    Globals.allowShoot = false;
                    Globals.disableShooting = true;
                    Globals.canDropOrbs = false;

                    GameManager.instance.player.playerMovement.SetSeeMouse(false);
                    // tint BG to black to focus on HUD




                    break;
                case TutorialState.Movement_Start:
                    {
                        blackOverlay.DOFade(0.2745098f, 0.2f).SetUpdate(true);
                        CreateMovementControls();

                        SetMovementControls(true, GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerMove"] as string);
                        if (!Globals.isJoystickConnected)
                        {
                            CreateFTUESkipButton();
                        }
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                        {
                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Movement_Start);
                        });

                    }
                    break;
                case TutorialState.LockPlayerMovement:



                    td.HideDialogue();
                    DOTween.Sequence().SetId(schedulerId).AppendInterval(2).AppendCallback(() =>
                    {
                        td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.LockPlayerMovement);

                    }).Play();


                    Globals.SetZoomValueWhileGame(Globals.zoomValueWhileGame + Globals.CocosToUnity(600));
                    //Globals.lockPlayerMovementForTutorial = true;
                    Globals.disableShooting = false;
                    //Observer.DispatchCustomEvent("disable_movement");
                    GameManager.instance.player.playerMovement.SetAcceleration(Vector2.zero);

                    if (!Globals.isJoystickConnected)
                    {
                        leftDpadTutorialHand.gameObject.SetActive(false);
                        //TODO Remove Comment
                        //rightDpadTutorialHand1.gameObject.SetActive(true);
                    }



                    break;

                case TutorialState.Shooting:
                    {

                        GameManager.instance.player.playerMovement.SetSeeMouse(true);
                        Globals.lockTapAndHoldShoot = true;

                        Globals.unlockTapShoot = true;
                        GameManager.instance.player.playerMovement.SetAcceleration(Vector2.zero);

#if UNITY_ANDROID || UNITY_IOS
                        //Observer.DispatchCustomEvent("hide_movement_dpad");
#endif
                        //Observer.DispatchCustomEvent("disable_movement");

                        //SetMovementControls(false, "");
                        CreateJoystickFirebutton();
                        if (Globals.isJoystickConnected)
                        {
                        }
#if UNITY_ANDROID || UNITY_IOS
                        //if (!Globals.isJoystickConnected)
                        //{

                        //    InputController.instance.ResetMovementDpad();
                        //    InputController.instance.SetJoyStickEnabled(false);
                        //}
#endif

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                        {
#if UNITY_ANDROID || UNITY_IOS
                            if (!Globals.isJoystickConnected)
                            {
                                Observer.DispatchCustomEvent("show_shoot_button");
                            }
#endif
                            //if (Globals.isJoystickConnected)
                            //{
                            //    SetJoystickFirebutton(true, GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["controllerShoot"] as string);
                            //}

                        }).Play();
                        break;
                    }
                case TutorialState.Aim_Assist_Rotation:
                    {
                        SetJoystickFirebutton(false, "");
                        if (!Globals.isJoystickConnected)
                        {
                            td.HideDialogue();

                        }
                        Globals.SetZoomValueWhileGame(Globals.zoomValueWhileGame + Globals.CocosToUnity(200));

                        if (!Globals.isJoystickConnected)
                        {
                            InputController.instance.rightDpadTutorialHand1.gameObject.SetActive(false);
                            InputController.instance.enemyTarget.gameObject.SetActive(false);
                        }

                        //Observer.DispatchCustomEvent("disable_rotation");
                        //Observer.DispatchCustomEvent("hide_shoot_button");


                        GameManager.instance.player.secondaryWeapon.SetShootMode(false);
                        GameManager.instance.player.SetPlaneIdle();
                        if (Globals.isJoystickConnected)
                        {
                            SetMovementControls(false, "");
                            SetJoystickFirebutton(false, "");
                            InputController.instance.enemyTarget.gameObject.SetActive(false);

                        }
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(2).AppendCallback(() =>
                        {
                            GameManager.instance.player.TurnOnBoost(true);
                            float rot = GameManager.instance.player.RotationInDegrees;
                            float target = rot > 180 ? 360 : 0;
                            DOTween.To(() => rot, x => rot = x, target, 1).OnUpdate(() =>
                            {
                                GameManager.instance.player.playerMovement.RotatePlayer(rot);
                            });
                            //DOTween.Sequence().SetId(GameManager.instance.player.tweenId).Append(GameManager.instance.player.GetSkeletonAnimation().transform.DORotate(Vector3.forward * 0, 1f)).Play();

                            DOTween.Sequence().SetId(GameManager.instance.player.tweenId).Append(GameManager.instance.player.transform.DOBlendableMoveBy(new Vector3(0.3f, 0.2f), 1).SetEase(Ease.InOutSine)).AppendCallback(() =>
                            {

                                GameManager.instance.player.TurnOnBoost(false);

                            }).Play(); ;

                        }).Play();



                        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                        {
                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Building_Destroyed);
                        }).AppendInterval(6).AppendCallback(() =>
                        {
                            td.HideDialogue();

                        }).Play();




                        DOTween.Sequence().SetId(schedulerId).AppendInterval(8.5f).AppendCallback(() =>
                        {


                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Aim_Assist_Rotation);

                            Globals.canRegenerateHealth = false;


                            DOTween.Sequence().SetId(schedulerId).AppendInterval(3).AppendCallback(() =>
                            {

                                // call event here
                                Observer.DispatchCustomEvent("Move_Skip_Button_Down");
                                Observer.DispatchCustomEvent("show_player_hud");
                                if (!Globals.isJoystickConnected)
                                {
                                    Observer.DispatchCustomEvent("show_shoot_button");
                                }
                                for (int i = 0; i < 10; i++)
                                {
                                    DOTween.Sequence().SetId(schedulerId).AppendInterval(0.75f * i).AppendCallback(() =>
                                    {
                                        Tracker s = GameManager.instance.SpawnEnemy("SEVERSKY2") as Tracker;
                                        s.Init();
                                        s.stats.health = s.stats.health * 0.25f;
                                        s.stats.maxHealth.Value = s.stats.health;
                                        s.stats.attack = s.stats.attack * 2;
                                        s.stats.bulletDamage = s.stats.bulletDamage * 2.5f;

                                    }).Play();
                                }
                            }).Play();



                            if (Globals.isJoystickConnected)
                            {
                                SetJoystickFirebutton(true, "");
                            }
                            Observer.DispatchCustomEvent("enable_rotation");


                        }).Play();
                        break;
                    }
                case TutorialState.Aim_Assist_Settings:
                    {
                        td.HideDialogue();
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(2).AppendCallback(() =>
                        {
                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Aim_Assist_Settings);
                        }).AppendInterval(5).AppendCallback(() =>
                        {
                            ChangeState(TutorialState.Health_Regen);
                        }).Play();

                        break;
                    }
                case TutorialState.Health_Regen:
                    {
                        td.HideDialogue();

                        float rot = GameManager.instance.player.RotationInDegrees;
                        float target = rot > 180 ? 360 : 0;
                        DOTween.To(() => rot, x => rot = x, target, 0.5f).OnUpdate(() =>
                        {
                            GameManager.instance.player.playerMovement.RotatePlayer(rot);
                        });
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(1.5f).AppendCallback(() =>
                        {
                            blackOverlay.DOFade(0.2745098f, 0.2f).SetUpdate(true);
                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Health_Regen);
                            DOTween.Sequence().SetId(schedulerId).AppendInterval(2f).AppendCallback(() =>
                            {
                                if (Globals.isTutorial)
                                {
                                    Observer.DispatchCustomEvent("Health_Regen_Complete");
                                }
                            }).Play();
                        }).Play();


                        Globals.canRegenerateHealth = true;
                        Observer.DispatchCustomEvent("hide_shoot_button");
                        Observer.DispatchCustomEvent("disable_shooting");


                        originalPlayerRegen = GameManager.instance.player.Stats.regen;

                        {
                            var healthPer = GameManager.instance.player.Stats.health / GameManager.instance.player.Stats.maxHealth.Value;
                            healthPer = System.Math.Clamp(healthPer, 0.5, 0.9);
                            GameManager.instance.player.Stats.regen = GameManager.instance.player.Stats.regen * 5;
                        }
                        GameManager.instance.player.weapon.SetShootMode(false);
                        GameManager.instance.player.SetPlaneIdle();



                    }
                    break;
                case TutorialState.Health_Regen_Complete:
                    {
                        blackOverlay.DOFade(0, 0.5f).SetUpdate(true);
                        td.HideDialogue();
                        GameManager.instance.player.Stats.regen = originalPlayerRegen;
                        ChangeState(TutorialState.SpawnSidekicks);

                        break;
                    }

                case TutorialState.SpawnSidekicks:

                    {
                        Globals.canRegenerateHealth = false;
                        Globals.isTutorial = false;
                        Globals.updateCameraZValueForSidekicks = true;
                        Observer.DispatchCustomEvent("enable_rotation");
                        Observer.DispatchCustomEvent("enable_movement");
                        Observer.DispatchCustomEvent("TutorialComplete");

                        GameManager.instance.player.weapon.SetShootMode(false);
                        GameManager.instance.player.SetPlaneIdle();
                        GunKitty gk = null;
                        Rocketeer rk = null;
                        Bowie bk = null;
                        Globals.unlockTapShoot = false;
                        Globals.lockTapAndHoldShoot = true;
                        GameManager.instance.timeManager.SetTimescale(0.3f);
                        gk = GameManager.instance.SpawnSidekick(0) as GunKitty;
                        gk.gameObject.SetActive(false);
                        rk = GameManager.instance.SpawnSidekick(1) as Rocketeer;
                        rk.gameObject.SetActive(false);
                        bk = GameManager.instance.SpawnSidekick(3) as Bowie;
                        bk.gameObject.SetActive(false);
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.3f).AppendCallback(() =>
                        {

                            float rot = GameManager.instance.player.RotationInDegrees;
                            float target = rot > 180 ? 360 : 0;
                            DOTween.To(() => rot, x => rot = x, target, 0.5f).OnUpdate(() =>
                            {
                                GameManager.instance.player.playerMovement.RotatePlayer(rot);
                            });

                        }).AppendInterval(0.15f).AppendCallback(() =>
                        {


                            {
                                GameManager.instance.player.Stats.consumption = 1;
                                Globals.zoomInForSec = 1.5f;
                                InitSideKick(gk, 0);
                                //DOTween.Sequence().SetId("SpawnEffect").AppendInterval(0.75f*1.7f).AppendCallback(() => { sidekickSpawnEffect[0].gameObject.SetActive(false); }).Play();

                                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.15f).AppendCallback(() =>
                                {

                                    InitSideKick(rk, 1);
                                    //DOTween.Sequence().SetId("SpawnEffect").AppendInterval(0.75f * 1.7f).AppendCallback(() => { sidekickSpawnEffect[1].gameObject.SetActive(false); }).Play();

                                    // pAnimation.setPosition(rt.sideKickSprite.getPosition());
                                }).AppendInterval(0.15f).AppendCallback(() =>
                                {
                                    InitSideKick(bk, 2);
                                    //DOTween.Sequence().SetId("SpawnEffect").AppendInterval(0.75f * 1.7f).AppendCallback(() => { sidekickSpawnEffect[2].gameObject.SetActive(false); }).Play();

                                    Globals.ftuxSidekicksSpawned = true;
                                    Globals.LEFTBOUNDARY = GameManager.instance.player.transform.GetWorldPositionX() - Globals.CocosToUnity(1300);
                                    Globals.RIGHTBOUNDARY = GameManager.instance.player.transform.GetWorldPositionX() + Globals.CocosToUnity(1300);
                                }).Play();

                                Globals.allowSidekickShoot = true;
                                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.75f).AppendCallback(() =>
                                {
                                    Globals.canRegenerateHealth = true;
                                    GameManager.instance.timeManager.SetTimescale(1);
                                    Globals.LEFTBOUNDARY = GameManager.instance.player.transform.GetWorldPositionX() - Globals.CocosToUnity(1300);
                                    Globals.RIGHTBOUNDARY = GameManager.instance.player.transform.GetWorldPositionX() + Globals.CocosToUnity(1300);

                                    //
                                    td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.SpawnSidekicks);


                                    DOTween.Sequence().SetId(schedulerId).AppendInterval(3f).AppendCallback(() =>
                                    {
                                        rk.StartSidekick();
                                        gk.StartSidekick();
                                        bk.StartSidekick();
                                        // introComplete = true;
                                        Globals.updateCameraZValueForSidekicks = false;
                                        Globals.lockTapAndHoldShoot = false;
                                        Globals.unlockTapShoot = true;
#if UNITY_ANDROID || UNITY_IOS
                                        if (!Globals.isJoystickConnected)
                                        {
                                            InputController.instance.SetJoyStickEnabled(true);// EnableJoystickCall();
                                            InputController.instance.DisableMovementJoystickHandTutorial();
                                        }
#endif
                                        Observer.DispatchCustomEvent("enable_rotation");
                                        Observer.DispatchCustomEvent("enable_movement");
                                        blackOverlay.DOFade(0, 0.5f).SetUpdate(true);
                                        //TODO
                                        //static_cast<GameController*>(GETGAMECONTROLLER)._hudLayer.setVisible(true);
                                        //static_cast<GameController*>(GETGAMECONTROLLER).enableEfactory();
                                        Globals.lockPlayerMovementForTutorial = false;
                                        GameManager.instance.eFactory.SetEnabled(true);
#if UNITY_ANDROID || UNITY_IOS
                                        if (!Globals.isJoystickConnected)
                                        {
                                            InputController.instance.ShowMobileControlsHud();
                                        }
#endif

                                    }).Play();

                                }).Play();



                            }
                        }).Play();

                        if (Globals.isJoystickConnected)
                        {
                            SetJoystickFirebutton(false, "");
                            SetMovementControls(false, "");
                        }


                        InputController.instance.DisableFireButtonHandTutorial();
                        InputController.instance.DisableMovementJoystickHandTutorial();
                        Observer.DispatchCustomEvent("enable_shooting");

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(12).AppendCallback(() =>
                        {
                            td.HideDialogue();
                        }).AppendInterval(1).AppendCallback(() =>
                        {
                            ChangeState(TutorialState.Start_Fight);

                        }).AppendInterval(5).Play();

                        break;
                    }

                case TutorialState.Start_Fight:
                    {
                        td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Start_Fight);

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(8).AppendCallback(() =>
                        {
                            td.HideDialogue();
                        }).AppendInterval(3).AppendCallback(() =>
                        {
                            ChangeState(TutorialState.Enter_Special_Ability_Mode);

                        }).Play();


                        break;
                    }
                case TutorialState.Enter_Special_Ability_Mode:
                    {
                        Globals.canDropCoins = false;
                        if (!Globals.isJoystickConnected)
                        {
                            Observer.DispatchCustomEvent("Show_SpecialAbility_Hud");
                        }
                        Observer.DispatchCustomEvent("start_rear_shoot");
                        //


                        Globals.canProceedInEnemyFactory = false;
                        break;
                    }
                case TutorialState.Activate_Special_Ability_Mode:
                    {
                        {
                            Globals.canActivateSpecialOnControllerTutorial = true;
                            blackOverlay.DOFade(0.2745098f, 0.2f).SetUpdate(true);
                            // Observer.DispatchCustomEvent("hide_dpads");
                            Observer.DispatchCustomEvent("disable_movement");
                            Observer.DispatchCustomEvent("disable_shooting");
                            Observer.DispatchCustomEvent("disable_rotation");

                            if (Globals.isJoystickConnected)
                            {
                                if (Globals.g_ControllerName == "DUALSHOCK 4 Wireless Controller" || Globals.g_ControllerName == "Wireless Controller")
                                {
                                    //TODO
                                    //specialAbilityButtonUp = SFSymbol.getGlyphForButton("Button Y", 60, "PS");
                                    //Shared.SpriteOutliner(specialAbilityButtonUp, Vec4(0.0f, 0.0f, 0.0f, 1.0f), 4.0 / specialAbilityButtonUp.getContentSize().width);

                                }
                                else
                                {
                                    //TODO
                                    //specialAbilityButtonUp = SFSymbol.getGlyphForButton("Button Y", 60, "Xbox");
                                    //Shared.SpriteOutliner(specialAbilityButtonUp, Vec4(0.0f, 0.0f, 0.0f, 1.0f), 4.0 / specialAbilityButtonUp.getContentSize().width);

                                }
                                specialAbilityButtonUp.gameObject.DoBlink(1, 3);
                            }

                            td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Activate_Special_Ability_Mode);
                            DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                            {
                                GameManager.instance.timeManager.SetTimescale(0.6f);
                            }).Play();
                            break;
                        }
                    }
                case TutorialState.Exit_Special_Ability_Mode:
                    {
                        td.HideDialogue();
                        GameManager.instance.timeManager.SetTimescale(1);
                        blackOverlay.DOFade(0, 0.5f).SetUpdate(true);
                        GameManager.instance.eFactory.SetEnabled(false);
                        if (!Globals.isJoystickConnected)
                        {
                            InputController.instance.ShowMobileControlsHud();
                        }
                        Observer.DispatchCustomEvent("enable_movement");
                        Observer.DispatchCustomEvent("enable_shooting");
                        Observer.DispatchCustomEvent("enable_rotation");

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(6).AppendCallback(() =>
                        {
                            ChangeState(TutorialState.Pre_Berserk_Mode);

                        }).Play();

                        if (Globals.isJoystickConnected)
                        {
                            if (specialAbilityButtonUp)
                            {
                                specialAbilityButtonUp.gameObject.SetActive(false);
                            }
                        }

                        break;
                    }

                case TutorialState.Pre_Berserk_Mode:
                    {
                        Globals.allowShoot = false;
                        GameManager.instance.eFactory.SetEnabled(false);
                        Globals.canDropOrbs = true;
                        Globals.tutorialOrbsDropState = true;
                        if (!Globals.isJoystickConnected)
                        {
                            Observer.DispatchCustomEvent("Hide_SpecialAbility_Hud");
                        }

                        td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Pre_Berserk_Mode);
                        for (int i = 0; i < 1; i++)
                        {
                            DOTween.Sequence().SetId(schedulerId).AppendCallback(() =>
                            {
                                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(700));
                                BalloonEnemy e = GameManager.instance.SpawnEnemy("BALLOONENEMY") as BalloonEnemy;
                                e.Init();
                                e.stats.maxHealth.Value = e.stats.maxHealth.Value * 0.4f;
                                e.stats.health = e.stats.maxHealth.Value;
                                e.stats.coinAwarded = 13;
                            }).Play();
                        }

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(5).AppendCallback(() =>
                        {
                            td.HideDialogue();
                        }).Play();

                        break;
                    }

                case TutorialState.Enter_Berserk_Mode:
                    {
                        if (Globals.canBerserk)
                            return;
                        td.ShowDialogueWithState(TutorialDialogue.TutorialDialogueState.Enter_Berserk_Mode);

                        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                        {
                            GameManager.instance.timeManager.SetTimescale(0.7f);

                        }).Play();

                        DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(0.2745098f, 0.2f)).Play();


                        Globals.canBerserk = true;
                        Globals.canProceedInEnemyFactory = false;

                    }

                    break;
                case TutorialState.Exit_Berserk_Mode:
                    {
                        blackOverlay.DOFade(0, 0.5f).SetUpdate(true);

                        td.HideDialogue();
                        Globals.tutorialOrbsDropState = false;
                        Globals.canDropOrbs = false;
                        Globals.isFTUETutorial = false;
                        Globals.canProceedInEnemyFactory = true;
                        GameManager.instance.eFactory.SetEnabled(true);
                        GameManager.instance.timeManager.SetTimescale(1);
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(3).AppendCallback(() =>
                        {
                            GameManager.instance.timeManager.SetTimescale(1);
                        }).Play();
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(10).AppendCallback(() =>
                        {
                            if (!Globals.isJoystickConnected)
                            {
                                Observer.DispatchCustomEvent("Show_SpecialAbility_Hud");
                            }

                        }).Play();
                        Globals.isGameInTutorialState = false;

                        //ChangeState(TutorialState.Spawn_Spider_Kat);

                        break;
                    }

                case TutorialState.Spawn_Spider_Kat:
                    {
                        //DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(0, 0.5f)).Play();

                        td.HideDialogue();
                        //Globals.tutorialOrbsDropState = false;
                        //Globals.canDropOrbs = false;
                        //Globals.isFTUETutorial = false;
                        //Globals.canProceedInEnemyFactory = true;
                        //GameManager.instance.eFactory.SetEnabled(true);
                        //GameManager.instance.timeManager.SetTimescale(1);

                        //DOTween.Sequence().SetId(schedulerId).AppendInterval(3).AppendCallback(() =>
                        //{
                        //    GameManager.instance.timeManager.SetTimescale(1);
                        //}).Play();
                        //DOTween.Sequence().SetId(schedulerId).AppendInterval(10).AppendCallback(() =>
                        //{
                        //    if (!Globals.isJoystickConnected)
                        //    {
                        //        Observer.DispatchCustomEvent("Show_SpecialAbility_Hud");
                        //    }

                        //}).Play();

                        //Globals.isGameInTutorialState = false;
                        break;
                    }
                case TutorialState.Spawn_Mafia_Kitty:

                    {

                        if (mafiaKittySpawned == false)
                        {
                            mafiaKittySpawned = true;
                            Observer.DispatchCustomEvent("Spawn_MafiaKitty_FTUE");



                            DOTween.Sequence().SetId(schedulerId).AppendInterval(7).AppendCallback(() =>
                            {
                                //Hide_SpecialAbility_Hud
                                Observer.DispatchCustomEvent("Hide_SpecialAbility_Hud");

                                Globals.introComplete = true;
                            }).Play();

                        }

                        break;

                    }

                case TutorialState.SkipTutorial:


                    {
                        Globals.isGameInTutorialState = false;
                        if (!isLaserTowerDestroyed)
                        {
                            Observer.DispatchCustomEvent("Destroy_Tower_On_Skip_Tutorial");
                        }
                        blackOverlay.DOFade(0, 0.5f).SetUpdate(true);

                        td.HideDialogue();
                        Globals.popupDestroyedOnSkip = false;


                        DOTween.Sequence().SetId(schedulerId).AppendInterval(7).AppendCallback(() =>
                        {
                            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(900));

                        }).Play();


                        Globals.tutorialOrbsDropState = false;
                        Globals.canDropOrbs = false;
                        Globals.isFTUETutorial = false;
                        Globals.canBerserk = true;

                        Globals.canProceedInEnemyFactory = true;
                        if (GameManager.instance.eFactory != null)
                            GameManager.instance.eFactory.SetEnabled(true);
                        if (GameManager.instance.timeManager != null)
                            GameManager.instance.timeManager.SetTimescale(1);
                        if (!Globals.isJoystickConnected)
                        {
                            Observer.DispatchCustomEvent("Show_SpecialAbility_Hud");
                        }

                        Observer.DispatchCustomEvent("show_player_hud");
                        Observer.DispatchCustomEvent("start_rear_shoot");

                        InputController.instance.SetJoyStickEnabled(false);


                    }
                    Globals.allowSidekickShoot = true;
                    Globals.disableShooting = false;
                    Globals.lockTapAndHoldShoot = false;
                    Globals.isTutorial = false;
                    if (GameManager.instance.player != null)
                        GameManager.instance.player.Stats.consumption = 1;

#if UNITY_ANDROID || UNITY_IOS
                    if (!Globals.isJoystickConnected)
                    {

                        // enable movement

                        Observer.DispatchCustomEvent("enable_movement");
                        Observer.DispatchCustomEvent("enable_rotation");
                        Observer.DispatchCustomEvent("Move_Skip_Button_Down");


                    }
#endif
                    SetJoystickFirebutton(false, "");
                    SetMovementControls(false, "");
                    Observer.DispatchCustomEvent("enable_rotation");
                    Observer.DispatchCustomEvent("enable_movement");
                    //TODO HudLayer
                    //static_cast<GameController*>(GETGAMECONTROLLER)._hudLayer.setVisible(true);
                    Globals.lockPlayerMovementForTutorial = false;

#if UNITY_ANDROID || UNITY_IOS
                    if (!Globals.isJoystickConnected)
                    {
                        InputController.instance.ShowMobileControlsHud();

                        InputController.instance.DisableFireButtonHandTutorial();
                        InputController.instance.DisableMovementJoystickHandTutorial();
                    }

#endif
                    if (!Globals.ftuxSidekicksSpawned)
                    {
                        Observer.DispatchCustomEvent("TutorialComplete");
                        Globals.unlockTapShoot = true;
                        GameManager.instance.player.playerMovement.SetSeeMouse(true);
                        GunKitty gk = GameManager.instance.SpawnSidekick(0) as GunKitty;
                        gk.Init();
                        gk.StartSidekick();
                        Bowie bk = GameManager.instance.SpawnSidekick(3) as Bowie;
                        bk.Init();
                        bk.StartSidekick();
                        Rocketeer rk = GameManager.instance.SpawnSidekick(1) as Rocketeer;
                        rk.Init();
                        rk.StartSidekick();
                        Globals.LEFTBOUNDARY = GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1300);
                        Globals.RIGHTBOUNDARY = GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1300);

                    }
                    break;

                default:
                    break;
            }
        }
    }

    private void InitSideKick(Sidekick sk, int effectIndex)
    {
        sk.gameObject.SetActive(true);
        sk.Init();
        sk.transform.localScale = Vector3.zero;
        DOTween.Sequence().Append(sk.transform.DOScale(Vector2.one * 0.22f, 0.5f)).Play();
        Globals.sideKickList.Add(sk);
        sk.SideKickSkeleton.state.SetAnimation(0, "select", false);
        sk.SideKickSkeleton.state.AddAnimation(0, "idle", true);
        sidekickSpawnEffect[effectIndex].GetComponent<Renderer>().sortingLayerID = sk.SideKickSkeleton.GetComponent<Renderer>().sortingLayerID;
        sidekickSpawnEffect[effectIndex].GetComponent<Renderer>().sortingOrder = sk.SideKickSkeleton.GetComponent<Renderer>().sortingOrder - 1;
        sidekickSpawnEffect[effectIndex].transform.parent = sk.transform;

        sidekickSpawnEffect[effectIndex].transform.localPosition = Vector3.zero;
        sidekickSpawnEffect[effectIndex].transform.SetScale(3);
        sidekickSpawnEffect[effectIndex].gameObject.SetActive(true);
        sidekickSpawnEffect[effectIndex].state.TimeScale = 3f;
        sidekickSpawnEffect[effectIndex].state.SetAnimation(0, "powerUp", false);
        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.levelUp);
    }

    public void Init()
    {

        if (!td)
            return;


        schedulerId = "CTMS" + GetInstanceID();
        tweenId = "CTM" + GetInstanceID();
        td.HideDialogue();
        {
            Observer.RegisterCustomEvent(gameObject, "Gun_Cool_Down", () =>
            {
                if (td.currentState == TutorialDialogue.TutorialDialogueState.none)
                {
                    Observer.DispatchCustomEvent("Gun_Cool_Down_Second");
                }
                else
                {
                    if (Globals.isGameInTutorialState)
                    {
                        TutorialDialogue.TutorialDialogueState a = td.currentState;
                        td.HideDialogue();
                        DOTween.Sequence().SetId(schedulerId).AppendInterval(3.25f).AppendCallback(() =>
                        {
                            if (td != null)
                            {
                                td.ShowDialogueWithState(a);
                            }
                        });
                    }
                    DOTween.Sequence().SetId(schedulerId).AppendInterval(0.5f).AppendCallback(() =>
                    {
                        Observer.DispatchCustomEvent("Gun_Cool_Down_Second");

                    }).Play();
                }

            });
            Observer.RegisterCustomEvent(gameObject, "Health_Regen_Complete", () =>
             {


                 ChangeState(TutorialState.Health_Regen_Complete);

             });

        }
        SpawnEvent();
    }

    private void SpawnEvent()
    {
        Observer.RegisterCustomEvent(gameObject, "Gun_Cool_Down_Second", () =>
        {

            //TODO Later Fix Pause
            //Shared::pauseRecursive(GETGAMECONTROLLER, true);
            //Shared::pauseRecursive(_hudLayer, true);
            //Shared::pauseRecursive(_playerController, true);
            //Shared::pauseRecursive(_hudLayer->getPlayerHud(), false);

            GameManager.instance.timeManager.SetTimescale(0);
            GameManager.instance.player.SetTutorialCooldownUpdate(true);
            Globals.unlockTapShoot = false;
            GameManager.instance.player.weapon.SetShootMode(false);
            td.canCallAttention = false;
            DOTween.Sequence().SetId(tweenId).AppendInterval(0.25f).AppendCallback(() =>
            {
                td.ShowDialogueWithString(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.New_FTUE)["shootingEnergy"] as string);
            }).SetUpdate(true).Play();
            DOTween.Sequence().SetId(tweenId).AppendInterval(3).AppendCallback(() =>
            {
                td.HideDialogue();


                GameManager.instance.timeManager.SetTimescale(1);
                blackOverlay.DOFade(0, 0.5f).SetUpdate(true).OnComplete(() =>
                {
                    Globals.unlockTapShoot = true;
                    GameManager.instance.player.weapon.SetShootMode(true);
                });
            }).SetUpdate(true).Play();
        });

        Observer.RegisterCustomEvent(gameObject, "Spawn_SpiderCat_FTUE_Dialogues", () =>
        {


            Observer.DispatchCustomEvent("hide_player_hud");
            Observer.DispatchCustomEvent("Move_Skip_Button_Up");
            Observer.DispatchCustomEvent("SpawnMidDialogues");


        });

        Observer.RegisterCustomEvent(gameObject, "Spawn_SpiderCat_FTUE", () =>
        {
            BossFactory bs = BossFactory.Create();
            bs.SpawnFtueSpiderCat();
        });

    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.M))
        {
            skipButton.defaultAction?.Invoke();
        }
    }

}
