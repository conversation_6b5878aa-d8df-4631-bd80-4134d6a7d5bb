﻿using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Apq.NotifyChange
{
    /// <summary>
    /// 列表事件的参数
    /// </summary>
    public class NotifyChangeListEventArgs<T> : EventArgs
    {
        public NotifyChangeListEventArgs(NotifyChangeList<T> lst)
        {
            List = lst;
        }

        /// <summary>
        /// 列表实例
        /// </summary>
        public NotifyChangeList<T> List { get; }
        /// <summary>
        /// 更改类型
        /// </summary>
        public ListChangedType ChangeType { get; set; }
        /// <summary>
        /// 指示是否取消(中断后续流程)
        /// </summary>
        public bool Cancel { get; set; }

        /// <summary>
        /// 删除的项
        /// </summary>
        public List<ListItemChanged<T>> Deleted { get; } = new();

        /// <summary>
        /// 插入的项
        /// </summary>
        public List<ListItemChanged<T>> Inserted { get; } = new();
    }
}
