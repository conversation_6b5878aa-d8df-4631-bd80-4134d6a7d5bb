using UnityEngine;
public class Singleton<T> : MonoBehaviour where T : Component
{
    private static T _instance;
    private static object lockMe = new();
    public static T Instance
    {
        get
        {
            if (_instance != null) return _instance;
            lock (lockMe)
            {
                if (_instance != null) return _instance;
                
                _instance = FindObjectOfType<T>();
                if (_instance != null) return _instance;
                
                GameObject obj = new(typeof(T).Name + "_AutoCreated");
                _instance = obj.AddComponent<T>();
            }
            
            return _instance;
        }
    }
    protected virtual void Awake()
    {
        InitializeSingleton();
    }
    protected virtual void InitializeSingleton()
    {
        if (!Application.isPlaying)
        {
            return;
        }

        // lock (lockMe)
        // {
        //     _instance = this as T;
        // }
    }
}

