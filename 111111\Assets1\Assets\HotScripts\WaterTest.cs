using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WaterTest : MonoBehaviour
{
    [SerializeField] private Water water;

    bool wait;

    // Start is called before the first frame update
    void Start()
    {
        water.Color1 = new Color(66 / 255f, 210 / 255f, 255 / 255f, 200 / 255f);
        water.Color2 = new Color(9 / 255f, 171 / 255f, 229 / 255f, 255 / 255f);

        wait = false;
    }

    private void Update()
    {
        if(UnityEngine.Input.GetMouseButtonUp(0) && !wait)
        {
            wait = true;
            water.HitWater(new Vector3(0.2860361f, -3.13f, -0.09399614f));
            Invoke("EndWait", 3);
        }
    }

    void EndWait()
    {
        wait = false;
    }
}
