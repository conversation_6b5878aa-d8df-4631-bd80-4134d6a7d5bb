using System.Diagnostics;
using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;
using UnityEngine.SceneManagement;
using DG.Tweening;
using Spine;

public class BackgroundController : MonoBehaviour
{
    public enum BackgroundType { None = 0, Day = 1, Rain = 2, Dusk = 3,
        Snow = 4, Twilight = 5, Volcano = 6, Night = 7, Darkness = 8, DarkClouds = 9, Waterfall = 10,
        UnderWater = 11, AtlantisDay = 12, AtlantisUnderwater = 13,FTUE =14}

    public static BackgroundType backgroundType;

    [SerializeField] Transform playerTransform;
    [SerializeField] Transform waterTransform;
    [SerializeField] Water water, waterBG;
    [SerializeField] BackgroundCollection[] backgroundCollections;
    [SerializeField] GameObject waterfallPrefab, rainPrefab, darkEffectPrefab, bottomFoamPrefab, liquidEffectPrefab,
        floorGlowPrefab;
    [SerializeField] SkeletonAnimation rocksPrefab;
    [SerializeField] SkeletonAnimation katStar;
    [SerializeField] SkeletonAnimation[] explosions;
    [SerializeField] GameObject ftueTransition;
    [SerializeField] GameObject horizonPrefab;
    [SerializeField] float horizonTopAlpha = 1, horizonBottomAlpha = 1;
    [SerializeField] Color horizonColor = Color.white;
    [SerializeField] GameObject[] chargeRadial;
    [SerializeField] Vector3 waterTopStart, waterTopEnd;
    [SerializeField] string layerName;

    BackgroundCollection currentCollection;
    SkeletonAnimation lightningSkeletonAnimation;
    List<PropObj> bgPropsList, frontPropsList, propsList, repeatableObjList, cloudsList, farCloudsList,
        backgroundPropLayersList;
    GameObject darkEffect, liquidEffect;
    List<Transform> smokeList; 
    Camera mainCam;
    string tweenID, schedulerID;
    float cameraPosition;
    bool hasWater;

    private bool scheduleUpdate = false;
    private List<PropObj> topClouds = new List<PropObj>();

    private void Awake()
    {
        mainCam = Camera.main;
        topClouds = new List<PropObj>();
    }


    public void Init(int backgroundNumber)
    {
        if (!scheduleUpdate)
            return;
        tweenID = "BGTween";
        schedulerID = "BGScheduler";

        backgroundType = (BackgroundType)backgroundNumber;
        currentCollection = backgroundCollections[(int)backgroundType];

        if (backgroundType == BackgroundType.UnderWater)
            horizonColor = new Color(17f / 255f, 94f / 255f, 134f / 255f);
        else if (backgroundType == BackgroundType.DarkClouds)
            horizonColor = new Color(135f / 255f, 135f / 255f, 120f / 255f);
        else if (backgroundType == BackgroundType.Dusk)
            horizonColor = new Color(255f / 255f, 64f / 255f, 31f / 255f);
        else if (backgroundType == BackgroundType.FTUE)
            horizonColor = new Color(247f / 255f, 116f / 255f, 108f / 255f);

        hasWater = backgroundType != BackgroundType.AtlantisUnderwater;
        Globals.GROUND_ENABLED = backgroundType == BackgroundType.AtlantisUnderwater;

        SpawnBackground();
        SelectWaterColor();
        SetLayerRecursively(transform);

        InvokeRepeating("CheckPropsDistanceFromCamera", 2, 2);
        InvokeRepeating("CheckRepeatableObjectDist", 2, 2);
    }

    void SetLayerRecursively(Transform trans)
    {
        trans.gameObject.layer = LayerMask.NameToLayer(layerName);

        foreach(Transform child in trans)
        {
            SetLayerRecursively(child);
        }
    }

    void LateUpdate()
    {
        if (!scheduleUpdate)
            return;

        var xPos = waterTransform.position.x;
        xPos += (-xPos + playerTransform.position.x) * Time.deltaTime * 5;

        waterTransform.position = new Vector3(xPos, waterTransform.position.y, waterTransform.position.z);
        //if (liquidEffect.activeSelf)
        //{

        //    liquidEffect.transform.SetWorldPositionX(
        //        liquidEffect.transform.position.x + (-liquidEffect.transform.position.x + playerTransform.position.x) * Time.deltaTime * 60);
        //}

        if (GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING
            && playerTransform.position.y > Globals.LOWERBOUNDARY && hasWater)
        {

            float distance = playerTransform.position.y - Globals.LOWERBOUNDARY;
            if (distance > Globals.CocosToUnity(300))
            {
                //liquidEffect.SetActive(false);
                return;

            }

            float scale = GameManager.instance.player.Acceleration.magnitude / 0.12f;

            scale = scale / ((playerTransform.position.y - Globals.LOWERBOUNDARY) / Globals.CocosToUnity(100));

            //if (!allowPlayerHit)
            //{
            //    scale = clampf(scale, 0, 0.75f);

            //}

            //else
            //{
            scale = Mathf.Clamp(scale, 0, 0.75f);
            //}

            //liquidEffect.transform.SetScale(scale);

            if (scale < 0.25f)
            {
                //liquidEffect.SetActive(false);
                return;
            }
            if (GameManager.instance.player.Acceleration.x > 0)
            {
                //liquidEffect.transform.SetScaleX(-scale);
            }
            //liquidEffect.SetActive(true);
        }
        else
        {
            //liquidEffect.SetActive(false);
        }
    }

    void SpawnBackground()
    {
        bgPropsList = new List<PropObj>();
        frontPropsList = new List<PropObj>();
        float xDistanceFromCam = currentCollection.xDistanceFromCam;
        float xPos = -xDistanceFromCam;
        float frustumHeight;
        float frustumWidth;
        cameraPosition = -Globals.CocosToUnity(650) - Globals.zoomValueWhileGame;

        List<Prop> nearProps = new List<Prop>();
        List<Prop> farProps = new List<Prop>();

        // Separating near and far props
        for(int i = 0; i < currentCollection.props.Length; i++)
        {
            Prop p = currentCollection.props[i];

            if (p.isFarProp)
                farProps.Add(p);
            if (p.isNearProp)
                nearProps.Add(p);
        }


        int orderInLayer = 0;
        // Far Props
        for(int i = 0; i < currentCollection.numberOfFarProps; i++)
        {
            if (farProps.Count == 0)
                break;

            int propIndex = Random.Range(0, farProps.Count);
            GameObject prop = Instantiate(farProps[propIndex].prefab);

            prop.transform.position = new Vector3(xPos, waterTopEnd.y, waterTopEnd.z);

            xPos += Random.Range((xDistanceFromCam * 2) / (currentCollection.numberOfFarProps * 4),
                (xDistanceFromCam * 2) / (currentCollection.numberOfFarProps * 0.5f));

            int[] ranArr = { -1, 1 };
            prop.transform.localScale = new Vector3(farProps[propIndex].farScale * ranArr[Random.Range(0, 2)],
                farProps[propIndex].farScale, prop.transform.localScale.z);

            PropObj p = new PropObj();
            p.prop = prop;
            p.spriteRenderer = p.prop.transform.GetChild(0).GetComponent<SpriteRenderer>();
            p.spriteRenderer.color = currentCollection.propColor;
            p.spriteRenderer.sortingLayerName = "BgMountains";
            p.spriteRenderer.sortingOrder = orderInLayer;
            orderInLayer = orderInLayer == 0 ? 2 : 0; // 1 and 3 for far clouds
            bgPropsList.Add(p);
            prop.transform.parent = transform;
        }

        xPos = -xDistanceFromCam;

        // Near Props
        for (int i = 0; i < currentCollection.numberOfNearProps; i++)
        {
            if (nearProps.Count == 0)
                break;

            int propIndex = Random.Range(0, nearProps.Count);
            GameObject prop = Instantiate(nearProps[propIndex].prefab);

            var zPos = Random.Range(currentCollection.minZ, currentCollection.maxZ);
            prop.transform.position = new Vector3(xPos,
                waterTopStart.y - ((zPos - waterTopStart.z) / (waterTopEnd.z - waterTopStart.z) * (waterTopStart.y - waterTopEnd.y)),
                zPos);

            xPos += Random.Range((xDistanceFromCam * 2) / (currentCollection.numberOfNearProps * 4),
                (xDistanceFromCam * 2) / (currentCollection.numberOfNearProps * 0.5f));

            int[] ranArr = { -1, 1 };
            var scale = ((zPos - currentCollection.minZ) / (currentCollection.maxZ - currentCollection.minZ)
                * (nearProps[propIndex].maxScale - nearProps[propIndex].minScale)) + nearProps[propIndex].minScale;
            prop.transform.localScale = new Vector3(scale * ranArr[Random.Range(0, 2)],
                scale, prop.transform.localScale.z);

            prop.transform.GetChild(0).GetComponent<SpriteRenderer>().sortingLayerName = "Props";

            PropObj p = new PropObj();
            p.prop = prop;
            p.spriteRenderer = p.prop.transform.GetChild(0).GetComponent<SpriteRenderer>();
            p.spriteRenderer.color = currentCollection.propColor;
            frontPropsList.Add(p);
            prop.transform.parent = transform;
        }



        repeatableObjList = new List<PropObj>();

        // Repeated objects
        for (int i = 0; i < 3; i++)
        {
            frustumHeight = 2.0f * (currentCollection.zFarPosition - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad);
            frustumWidth = frustumHeight * mainCam.aspect;
            float xWorldUnits, yWorldUnits;

            if (backgroundType == BackgroundType.Waterfall)
            {
                var zPos = Globals.CocosToUnity(1000);
                var fHeight = 2.0f * (zPos - cameraPosition) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad);
                var fWidth = fHeight * mainCam.aspect;

                GameObject go = Instantiate(waterfallPrefab);

                PropObj waterFall = new PropObj();
                waterFall.prop = go;
                waterFall.spriteRenderer = go.GetComponent<SpriteRenderer>();
                waterFall.spriteRenderer.color = currentCollection.cloudColor;

                waterFall.prop.transform.position = new Vector3(-fWidth + i * fWidth,
                    0, Globals.CocosToUnity(1000));

                xWorldUnits = waterFall.spriteRenderer.bounds.size.x;
                yWorldUnits = waterFall.spriteRenderer.bounds.size.y;
                waterFall.prop.transform.localScale =
                    new Vector3(waterFall.prop.transform.localScale.x / xWorldUnits * fWidth,
                    waterFall.prop.transform.localScale.y / yWorldUnits * fHeight * 2,
                    waterFall.prop.transform.localScale.z);

                if (backgroundType == BackgroundType.Waterfall)
                    InitWaterFallProps(go.transform, i, fWidth);

                go.transform.parent = transform;
                repeatableObjList.Add(waterFall);
            }

            if (currentCollection.allowTopCloud)
            {
                GameObject go = Instantiate(currentCollection.topCloudPrefab);

                PropObj topCloud = new PropObj();
                topCloud.prop = go;
                topCloud.spriteRenderer = go.GetComponent<SpriteRenderer>();
                topCloud.spriteRenderer.color = currentCollection.cloudColor;

                var cloudWidth = topCloud.spriteRenderer.bounds.size.x;

                topCloud.prop.transform.position = new Vector3(-cloudWidth + i * cloudWidth,
                    Globals.UPPERBOUNDARY + Globals.CocosToUnity(450) - topCloud.spriteRenderer.bounds.size.y / 2,
                    topCloud.prop.transform.position.z);

                go.transform.parent = transform;
                repeatableObjList.Add(topCloud);
                topClouds.Add(topCloud);
            }

            GameObject wt = Instantiate(currentCollection.waterTopPrefab);

            PropObj waterTop = new PropObj();
            waterTop.prop = wt;
            waterTop.spriteRenderer = wt.GetComponent<SpriteRenderer>();
            waterTop.spriteRenderer.sprite = currentCollection.waterTopSprite;

            waterTop.prop.transform.position = new Vector3(-frustumWidth + i * frustumWidth,
                waterTop.prop.transform.position.y, waterTop.prop.transform.position.z);

            xWorldUnits = waterTop.spriteRenderer.bounds.size.x;
            waterTop.prop.transform.localScale =
                new Vector3(waterTop.prop.transform.localScale.x / xWorldUnits * frustumWidth,
                waterTop.prop.transform.localScale.y, waterTop.prop.transform.localScale.z);

            if (backgroundType == BackgroundType.Waterfall)
            {
                yWorldUnits = waterTop.spriteRenderer.bounds.size.y;
                waterTop.prop.transform.localScale = new Vector3(waterTop.prop.transform.localScale.x,
                    waterTop.prop.transform.localScale.y / 3.5f, waterTop.prop.transform.localScale.z);

                waterTop.prop.transform.position += waterTop.prop.transform.up * (yWorldUnits * 2);
            }

            wt.transform.parent = transform;
            repeatableObjList.Add(waterTop);
        }

        CreateCameraFollowObjects();
        CreateClouds();
        CreateGodRays();
        CreateBackgroundPropLayers();
        CreateSmoke();
        CreateRain();

        if(currentCollection.lightningPrefab != null)
        {
            GameObject lightning = Instantiate(currentCollection.lightningPrefab);
            lightningSkeletonAnimation = lightning.GetComponent<SkeletonAnimation>();
            StartCoroutine(GenerateLightning());
        }

        if(currentCollection.moonPrefab != null)
        {
            GameObject moon = Instantiate(currentCollection.moonPrefab);
            var moonF = moon.AddComponent<FollowCameraObject>();
            moonF.Init(17.4f, 12.8f, currentCollection.zFarPosition, 10); 
            moon.transform.parent = transform;
        }

        if(currentCollection.catheadPrefab != null)
        {
            Transform cathead = Instantiate(currentCollection.catheadPrefab).transform;

            cathead.parent = transform;
        }

        propsList = new List<PropObj>(); // List for checking distance of props from the camera view
        propsList.AddRange(frontPropsList);
        propsList.AddRange(bgPropsList);
        propsList.AddRange(cloudsList);
        propsList.AddRange(farCloudsList);

        List<PropObj> props = new List<PropObj>();
        props.AddRange(frontPropsList);
        props.AddRange(cloudsList);
        SortOrderInLayer(props);

        //liquidEffect = Instantiate(liquidEffectPrefab, transform);
        //liquidEffect.transform.position = new Vector3(0, Globals.LOWERBOUNDARY - 0.5f);
        //liquidEffect.GetComponent<SpriteRenderer>().color = new Color(165f/255f, 1, 1);
        //liquidEffect.transform.SetScale(1);

        //transform.SetWorldPositionY(-5.16f);
    }

    void CreateCameraFollowObjects()
    {
        GameObject sky = Instantiate(currentCollection.bgSkyPrefab);

        var frustumHeight = 2.0f * (currentCollection.skyZPosition - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad);
        var frustumWidth = frustumHeight * mainCam.aspect;

        sky.transform.position = new Vector3(0, 0, currentCollection.skyZPosition);

        var skySpriteRenderer = sky.GetComponent<SpriteRenderer>();
        var xWorldUnits = skySpriteRenderer.bounds.size.x;
        var yWorldUnits = skySpriteRenderer.bounds.size.y;
        sky.transform.localScale = new Vector3(sky.transform.localScale.x / xWorldUnits * frustumWidth * 3f,
            sky.transform.localScale.y / yWorldUnits * frustumHeight * 3f, sky.transform.localScale.z);

        var skyF = sky.AddComponent<FollowCameraObject>();
        skyF.Init(0, 0, currentCollection.skyZPosition);
        sky.transform.parent = transform;

        if (currentCollection.bg2Prefab)
        {
            GameObject bg2 = Instantiate(currentCollection.bg2Prefab);

            bg2.transform.position = new Vector3(0, 0, currentCollection.skyZPosition);

            var bg2SpriteRenderer = bg2.GetComponent<SpriteRenderer>();
            xWorldUnits = bg2SpriteRenderer.bounds.size.x;
            yWorldUnits = bg2SpriteRenderer.bounds.size.y;
            bg2.transform.localScale = new Vector3(bg2.transform.localScale.x / xWorldUnits * frustumWidth * 3f,
                bg2.transform.localScale.y / yWorldUnits * frustumHeight * 3f, bg2.transform.localScale.z);

            var bg2F = bg2.AddComponent<FollowCameraObject>();
            bg2F.Init(0, 0, currentCollection.skyZPosition);
            bg2.transform.parent = transform;
        }

        //frustumWidth = 2.0f * (currentCollection.zFarPosition - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;

        if (currentCollection.horizonPrefab != null)
        {
            GameObject go = Instantiate(horizonPrefab);

            var horizonTopSpriteRenderer = go.transform.GetChild(0).GetChild(0).GetComponentInChildren<SpriteRenderer>();
            var horizonBottomSpriteRenderer = go.transform.GetChild(1).GetChild(0).GetComponentInChildren<SpriteRenderer>();

            horizonTopSpriteRenderer.color = new Color(horizonColor.r, horizonColor.g, horizonColor.b, horizonTopAlpha);
            horizonBottomSpriteRenderer.color = new Color(horizonColor.r, horizonColor.g, horizonColor.b, horizonBottomAlpha);

            go.transform.position = new Vector3(0, waterTopEnd.y, waterTopEnd.z);

            // ===========================< Don't uncomment >=====================================================
            //xWorldUnits = horizonSpriteRenderer.bounds.size.x;
            //go.transform.localScale =
            //    new Vector3(go.transform.localScale.x / xWorldUnits * frustumWidth,
            //    go.transform.localScale.y, go.transform.localScale.z);
            // ===========================< Don't uncomment >=====================================================

            var horizonF = go.AddComponent<FollowCameraObject>();
            horizonF.Init(0, 0, waterTopEnd.z, 0, true, false);
            go.transform.parent = transform;
        }

        if(backgroundType == BackgroundType.AtlantisUnderwater)
        {
            GameObject go = Instantiate(floorGlowPrefab);

            var floorGlowF = go.AddComponent<FollowCameraObject>();
            floorGlowF.Init(0, 0, go.transform.position.z, 0, true, false);
            go.transform.parent = transform;
        }

        if (currentCollection.blueNormalPrefab)
        {
            for(int i = 0; i < 2; i++)
            {
                GameObject bn = Instantiate(currentCollection.blueNormalPrefab);

                bn.transform.position = new Vector3(0, 0, currentCollection.skyZPosition);

                var bnSpriteRenderer = bn.GetComponent<SpriteRenderer>();
                bnSpriteRenderer.sortingLayerName = i == 0 ? "Default" : "Foreground";
                bnSpriteRenderer.sortingOrder = i == 0 ? 25 : 1;
                if(i == 1)
                {
                    bnSpriteRenderer.color = new Color(bnSpriteRenderer.color.r, bnSpriteRenderer.color.g,
                    bnSpriteRenderer.color.b, 0.47f);
                }
                xWorldUnits = bnSpriteRenderer.bounds.size.x;
                yWorldUnits = bnSpriteRenderer.bounds.size.y;
                bn.transform.localScale = new Vector3(bn.transform.localScale.x / xWorldUnits * frustumWidth * 3f,
                    bn.transform.localScale.y / yWorldUnits * frustumHeight * 3f, bn.transform.localScale.z);

                var bnF = bn.AddComponent<FollowCameraObject>();
                bnF.Init(0, 0, currentCollection.skyZPosition);
                bn.transform.parent = transform;
            }
        }

        if (darkEffectPrefab)
        {
            GameObject de = Instantiate(darkEffectPrefab);

            de.transform.position = new Vector3(0, 0, currentCollection.skyZPosition);

            var deSpriteRenderer = de.GetComponent<SpriteRenderer>();
            deSpriteRenderer.sortingLayerName = "Props";
            deSpriteRenderer.sortingOrder = 100;

            xWorldUnits = deSpriteRenderer.bounds.size.x;
            yWorldUnits = deSpriteRenderer.bounds.size.y;
            de.transform.localScale = new Vector3(de.transform.localScale.x / xWorldUnits * frustumWidth * 3f,
                de.transform.localScale.y / yWorldUnits * frustumHeight * 3f, de.transform.localScale.z);

            var deF = de.AddComponent<FollowCameraObject>();
            deF.Init(0, 0, currentCollection.skyZPosition);
            de.transform.parent = transform;
            darkEffect = de;
            darkEffect.SetActive(false);
        }
    }

    void CreateRain()
    {
        if (backgroundType != BackgroundType.Rain)
            return;

        GameObject rain = Instantiate(rainPrefab);
        float distanceFromCamera = 1;

        var frustumHeight = 2.0f * distanceFromCamera * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad);
        var frustumWidth = frustumHeight * mainCam.aspect;

        rain.transform.position = new Vector3(mainCam.transform.position.x, mainCam.transform.position.y,
            mainCam.transform.position.z + distanceFromCamera);

        var xWorldUnits = rain.GetComponent<SpriteRenderer>().bounds.size.x;
        var yWorldUnits = rain.GetComponent<SpriteRenderer>().bounds.size.y;

        rain.transform.localScale = new Vector3(rain.transform.localScale.x / xWorldUnits * frustumWidth,
                    rain.transform.localScale.y / yWorldUnits * frustumHeight, rain.transform.localScale.z);

        rain.transform.parent = mainCam.transform;
    }

    void CreateBackgroundPropLayers()
    {
        backgroundPropLayersList = new List<PropObj>();
        int orderInLayer = 0;
        float xPos, frustumWidth;

        for (int j = 0; j < currentCollection.backgroundPropLayers.Length; j++)
        {
            BackgroundPropLayer backgroundPropLayer = currentCollection.backgroundPropLayers[j];
            frustumWidth = 2.0f * (backgroundPropLayer.layerZPosition - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;
            orderInLayer = backgroundPropLayer.orderInLayerA;
            xPos = -frustumWidth * 1.5f;

            for (int i = 0; i < backgroundPropLayer.numberOfProps; i++)
            {
                int propIndex = Random.Range(0, backgroundPropLayer.layerProps.Length);
                GameObject prop = Instantiate(backgroundPropLayer.layerProps[propIndex].prefab);

                prop.transform.position = new Vector3(xPos, backgroundPropLayer.layerYPosition,
                    backgroundPropLayer.layerZPosition);

                int remaining = backgroundPropLayer.numberOfProps - i - 1;

                xPos += Random.Range((frustumWidth * 1.5f - xPos) / (remaining * 3),
                    (frustumWidth * 1.5f - xPos) / remaining);

                int[] ranArr = { -1, 1 };
                float scale = backgroundPropLayer.layerProps[propIndex].scale
                    + backgroundPropLayer.layerProps[propIndex].scale * Random.Range(0, 0.25f);
                prop.transform.localScale = new Vector3(scale * ranArr[Random.Range(0, 2)], scale,
                    prop.transform.localScale.z);

                PropObj p = new PropObj();
                p.prop = prop;
                p.spriteRenderer = p.prop.transform.GetChild(0).GetComponent<SpriteRenderer>();
                //p.spriteRenderer.color = currentCollection.propColor;
                p.spriteRenderer.sortingLayerName = "BgMountains";
                p.spriteRenderer.sortingOrder = orderInLayer;
                orderInLayer = orderInLayer == backgroundPropLayer.orderInLayerA
                    ? backgroundPropLayer.orderInLayerB : backgroundPropLayer.orderInLayerA; 
                backgroundPropLayersList.Add(p);
                prop.transform.parent = transform;
            }
        }

        StartCoroutine(CheckBgPropLayerDistance());
    }

    IEnumerator CheckBgPropLayerDistance()
    {
        yield return new WaitForSeconds(2);

        foreach(PropObj obj in backgroundPropLayersList)
        {
            var frustumWidth = 2.0f * (obj.prop.transform.position.z - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;

            var rightDistance = mainCam.transform.position.x + frustumWidth / 2 - obj.prop.transform.position.x;
            var leftDistance = mainCam.transform.position.x - frustumWidth / 2 - obj.prop.transform.position.x;
            var maxDistance = frustumWidth * 3;

            if (rightDistance >= maxDistance)
            {
                obj.prop.transform.position =
                    new Vector3(mainCam.transform.position.x + frustumWidth / 2 + obj.spriteRenderer.bounds.size.x,
                    obj.prop.transform.position.y, obj.prop.transform.position.z);
            }
            else if (leftDistance <= -maxDistance)
            {
                obj.prop.transform.position =
                    new Vector3(mainCam.transform.position.x - frustumWidth / 2 - obj.spriteRenderer.bounds.size.x,
                    obj.prop.transform.position.y, obj.prop.transform.position.z);
            }
        }

        StartCoroutine(CheckBgPropLayerDistance());
    }

    void CreateClouds()
    {
        cloudsList = new List<PropObj>();
        float xPos = -currentCollection.xDistanceFromCam;
        float rightBound = currentCollection.xDistanceFromCam;

        List<Prop> nearClouds = new List<Prop>();
        List<Prop> farClouds = new List<Prop>();

        // Separating near and far clouds
        for (int i = 0; i < currentCollection.clouds.Length; i++)
        {
            Prop p = currentCollection.clouds[i];

            if (p.isFarProp)
                farClouds.Add(p);
            if (p.isNearProp)
                nearClouds.Add(p);
        }

        for (int i = 0; i < currentCollection.numberOfNearClouds; i++)
        {
            if (nearClouds.Count == 0)
                break;

            int cloudIndex = Random.Range(0, nearClouds.Count);
            GameObject cloud = Instantiate(nearClouds[cloudIndex].prefab);

            var zPos = Random.Range(currentCollection.minZ, currentCollection.maxZ);
            cloud.transform.position = new Vector3(xPos,
                Random.Range(nearClouds[cloudIndex].minNearY, nearClouds[cloudIndex].maxNearY), zPos);

            int remaining = currentCollection.numberOfNearClouds - i - 1;

            xPos += Random.Range((rightBound - xPos) / (remaining * 2),
                (rightBound - xPos) / remaining);

            int[] ranArr = { -1, 1 };
            var scale =
                Random.Range(nearClouds[cloudIndex].minScale, nearClouds[cloudIndex].maxScale);
            cloud.transform.localScale = new Vector3(scale * ranArr[Random.Range(0, 2)],
                scale, cloud.transform.localScale.z);

            cloud.GetComponent<SpriteRenderer>().sortingLayerName = "Props";

            PropObj p = new PropObj();
            p.prop = cloud;
            p.spriteRenderer = p.prop.GetComponent<SpriteRenderer>();
            p.spriteRenderer.color = currentCollection.cloudColor;
            cloudsList.Add(p);
            cloud.transform.parent = transform;
        }

        farCloudsList = new List<PropObj>();
        xPos = -currentCollection.xDistanceFromCam;
        int orderInLayer = 1;

        for (int i = 0; i < currentCollection.numberOfFarClouds; i++)
        {
            if (farClouds.Count == 0)
                break;

            int cloudIndex = Random.Range(0, farClouds.Count);
            GameObject cloud = Instantiate(farClouds[cloudIndex].prefab);

            var zPos = currentCollection.zFarPosition;
            cloud.transform.position = new Vector3(xPos,
                Random.Range(farClouds[cloudIndex].minFarY, farClouds[cloudIndex].maxFarY), zPos);

            int remaining = currentCollection.numberOfNearClouds - i - 1;

            xPos += Random.Range((rightBound - xPos) / (remaining * 2),
                (rightBound - xPos) * 2 / remaining);

            int[] ranArr = { -1, 1 };
            var scale = farClouds[cloudIndex].farScale;
            cloud.transform.localScale = new Vector3(scale * ranArr[Random.Range(0, 2)],
                scale, cloud.transform.localScale.z);

            PropObj p = new PropObj();
            p.prop = cloud;
            p.spriteRenderer = p.prop.GetComponent<SpriteRenderer>();
            p.spriteRenderer.color = currentCollection.farCloudColor;
            p.spriteRenderer.sortingLayerName = "BgMountains";
            p.spriteRenderer.sortingOrder = orderInLayer;
            orderInLayer = orderInLayer == 1 ? 3 : 1;
            farCloudsList.Add(p);
            cloud.transform.parent = transform;
        }
    }

    void CreateGodRays()
    {
        var xPos = Globals.LEFTBOUNDARY
            + Random.Range(0, (Globals.RIGHTBOUNDARY - Globals.LEFTBOUNDARY) / (currentCollection.numberOfGodrays * 2));

        for (int i = 0; i < currentCollection.numberOfGodrays; i++)
        {
            GameObject godray = Instantiate(currentCollection.godrayPrefab);

            godray.transform.position = new Vector3(xPos, 0, 0);
            godray.transform.localScale = new Vector3(Random.Range(1.5f, 2.5f), 30, 1);
            godray.transform.localEulerAngles = new Vector3(0, 0, Random.Range(30, 60));

            int remaining = currentCollection.numberOfGodrays - i - 1;

            xPos += Random.Range((Globals.RIGHTBOUNDARY - xPos) / (remaining * 2),
                (Globals.RIGHTBOUNDARY - xPos) / remaining);

            godray.transform.parent = transform;
        }
    }

    void CreateSmoke()
    {
        if (!currentCollection.allowSmoke)
            return;

        smokeList = new List<Transform>();
        int orderInLayer = 12;
        float xPos, yPos = -11.6f, zPos = 41.25f;

        float frustumWidth = 2.0f * (zPos - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;
        xPos = -frustumWidth;

        for (int i = 0; i < 2; i++)
        {
            GameObject smoke = Instantiate(currentCollection.smokePrefab);

            float maxPosX = xPos + (frustumWidth - xPos) / (2 - i);
            xPos = Random.Range(xPos, maxPosX);
            float scale = 6 + 6 * Random.Range(0, 0.5f);

            smoke.transform.position = new Vector3(xPos, yPos, zPos);
            xPos += 20;
            smoke.transform.localScale = new Vector3(scale, scale, 1);
            smoke.GetComponent<SkeletonAnimation>().timeScale = 0.06f + Random.Range(0f, 1f) * 0.04f;
            smoke.GetComponent<MeshRenderer>().sortingOrder = orderInLayer;
            orderInLayer = 13;
            smokeList.Add(smoke.transform);
            smoke.transform.parent = transform;
        }

        orderInLayer = 3;
        yPos = -24f;
        zPos = 87.5f;
        frustumWidth = 2.0f * (zPos - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;
        xPos = -frustumWidth;

        for (int i = 0; i < 3; i++)
        {
            GameObject smoke = Instantiate(currentCollection.smokePrefab);

            float maxPosX = xPos + (frustumWidth - xPos) / (3 - i);
            xPos = Random.Range(xPos, maxPosX);
            float scale = 17 + 17 * Random.Range(0, 0.5f);

            smoke.transform.position = new Vector3(xPos, yPos, zPos);
            xPos += 60;
            smoke.transform.localScale = new Vector3(scale, scale, 1);
            smoke.GetComponent<SkeletonAnimation>().timeScale = 0.01f + Random.Range(0f, 1f) * 0.01f;
            smoke.GetComponent<MeshRenderer>().sortingOrder = orderInLayer;
            orderInLayer = orderInLayer == 3 ? 4 : 3;
            smokeList.Add(smoke.transform);
            smoke.transform.parent = transform;
        }

        StartCoroutine(CheckSmokeDistance());
    }

    IEnumerator CheckSmokeDistance()
    {
        yield return new WaitForSeconds(2);

        foreach (Transform trans in smokeList)
        {
            var frustumWidth = 2.0f * (trans.position.z - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;

            var rightDistance = mainCam.transform.position.x + frustumWidth / 2 - trans.position.x;
            var leftDistance = mainCam.transform.position.x - frustumWidth / 2 - trans.position.x;
            var maxDistance = frustumWidth * 2;

            if (rightDistance >= maxDistance)
            {
                trans.position =
                    new Vector3(mainCam.transform.position.x + frustumWidth * 0.75f,
                    trans.position.y, trans.position.z);
            }
            else if (leftDistance <= -maxDistance)
            {
                trans.position =
                    new Vector3(mainCam.transform.position.x - frustumWidth * 0.75f,
                    trans.position.y, trans.position.z);
            }
        }

        StartCoroutine(CheckSmokeDistance());
    }

    void InitWaterFallProps(Transform parent, int iteration, float frustumWidth)
    {
        if (iteration == 0)
        {
            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1000), Globals.CocosToUnity(300), -3);
                rocks.state.SetAnimation(0, "fog", true);
                rocks.state.SetAnimation(1, "water", true);
                rocks.Skeleton.SetSkin("1");
                rocks.skeleton.FindBone("water").ScaleY = 2.5f;
                rocks.skeleton.FindBone("water2").ScaleY = 2.5f;
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(200), Globals.CocosToUnity(100), -3);

                rocks.Skeleton.SetSkin("4");
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1400), Globals.CocosToUnity(200), -3);

                rocks.Skeleton.SetSkin("5");
            }


            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(2000), Globals.CocosToUnity(400), -3);
                rocks.state.SetAnimation(0, "fog", true);

                rocks.Skeleton.SetSkin("2");
            }

            {

                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(12300), Globals.CocosToUnity(150), -3);
                rocks.state.SetAnimation(0, "fog", true);
                rocks.state.SetAnimation(1, "water", true);
                rocks.Skeleton.SetSkin("3");
                rocks.skeleton.FindBone("water").ScaleY = 1.9f;
                rocks.skeleton.FindBone("water2").ScaleY = 1.9f;
            }
        }


        if (iteration == 1)
        {
            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1000), Globals.CocosToUnity(300), -3);
                rocks.Skeleton.SetSkin("5");
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(200), Globals.CocosToUnity(100), -3);

                rocks.Skeleton.SetSkin("4");

            }
            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1400), Globals.CocosToUnity(50), -3);

                rocks.state.SetAnimation(0, "fog", true);
                rocks.state.SetAnimation(1, "water", true);
                rocks.skeleton.FindBone("water").ScaleY = 1.4f;
                rocks.skeleton.FindBone("water2").ScaleY = 1.4f;
                rocks.Skeleton.SetSkin("1");
            }


            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(2000), Globals.CocosToUnity(400), -3);
                rocks.state.SetAnimation(0, "fog", true);

                rocks.Skeleton.SetSkin("2");
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(2300), Globals.CocosToUnity(150), -3);
                rocks.state.SetAnimation(0, "fog", true);
                rocks.Skeleton.SetSkin("3");
            }
        }

        if (iteration == 2)
        {
            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1000), Globals.CocosToUnity(300), -3);
                rocks.state.SetAnimation(0, "fog", true);
                rocks.state.SetAnimation(1, "water", true);
                rocks.Skeleton.SetSkin("1");
                rocks.skeleton.FindBone("water").ScaleY = 2.5f;
                rocks.skeleton.FindBone("water2").ScaleY = 2.5f;
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(200), Globals.CocosToUnity(100), -3);

                rocks.Skeleton.SetSkin("4");
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(1400), Globals.CocosToUnity(200), -3);

                rocks.Skeleton.SetSkin("5");
            }


            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(2000), Globals.CocosToUnity(400), -3);
                rocks.state.SetAnimation(0, "fog", true);

                rocks.Skeleton.SetSkin("2");
            }

            {
                SkeletonAnimation rocks = Instantiate(rocksPrefab, parent);
                rocks.transform.localScale = new Vector3(0.45f, 0.35f, 1);
                rocks.transform.localPosition = new Vector3(Globals.CocosToUnity(2300), Globals.CocosToUnity(150), -3);
                rocks.state.SetAnimation(0, "fog", true);
                rocks.state.SetAnimation(1, "water", true);
                rocks.Skeleton.SetSkin("3");
                rocks.skeleton.FindBone("water").ScaleY = 1.9f;
                rocks.skeleton.FindBone("water2").ScaleY = 1.9f;
            }
        }

        {
            var bottomFoam = Instantiate(bottomFoamPrefab);
            var bfSpriteRenderer = bottomFoam.GetComponent<SpriteRenderer>();

            var xWorldUnits = bfSpriteRenderer.bounds.size.x;
            var yWorldUnits = bfSpriteRenderer.bounds.size.y;
            var newWidth = bottomFoam.transform.localScale.x / xWorldUnits * frustumWidth;

            bottomFoam.transform.localScale = new Vector3(iteration == 1 ? newWidth * -1 : newWidth,
                newWidth + newWidth * 0.3f, 1);
            bottomFoam.transform.position = new Vector3(bottomFoam.transform.position.x, -4.5f, bottomFoam.transform.position.z);

            bottomFoam.transform.parent = parent;
            bottomFoam.transform.localPosition = new Vector3(0, bottomFoam.transform.localPosition.y, 0);

            DOTween.Sequence().SetId(tweenID).Append(
                    bottomFoam.transform.DOBlendableMoveBy(new Vector2(0, 0.25f), 2).SetEase(Ease.InOutSine)
                ).Append(
                    bottomFoam.transform.DOBlendableMoveBy(new Vector2(0, -0.25f), 2).SetEase(Ease.InOutSine)
                ).SetLoops(-1);
        }
    }

    void CheckPropsDistanceFromCamera()
    {
        foreach(PropObj obj in propsList)
        {
            CheckPropsDistanceFromGameView(obj);
        }
    }

    void CheckRepeatableObjectDist()
    {
        foreach (PropObj obj in repeatableObjList)
        {
            CheckRepeatableObjectsDistance(obj);
        }
    }

    void CheckPropsDistanceFromGameView(PropObj obj)
    {
        var frustumWidth = 2.0f * (obj.prop.transform.position.z - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;

        var rightDistance = mainCam.transform.position.x + frustumWidth / 2 - obj.prop.transform.position.x;
        var leftDistance = mainCam.transform.position.x - frustumWidth / 2 - obj.prop.transform.position.x;
        var maxDistance = currentCollection.xDistanceFromCam * 2;

        if (rightDistance >= maxDistance)
        {
            obj.prop.transform.position =
                new Vector3(mainCam.transform.position.x + frustumWidth / 2 + obj.spriteRenderer.bounds.size.x,
                obj.prop.transform.position.y, obj.prop.transform.position.z);
        }
        else if(leftDistance <= -maxDistance)
        {
            obj.prop.transform.position =
                new Vector3(mainCam.transform.position.x - frustumWidth / 2 - obj.spriteRenderer.bounds.size.x,
                obj.prop.transform.position.y, obj.prop.transform.position.z);
        }
    }

    void CheckRepeatableObjectsDistance(PropObj obj)
    {
        var frustumWidth = 2.0f * (obj.prop.transform.position.z - mainCam.transform.position.z) * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;

        var rightDistance = mainCam.transform.position.x + frustumWidth / 2 - obj.prop.transform.position.x;
        var leftDistance = mainCam.transform.position.x - frustumWidth / 2 - obj.prop.transform.position.x;
        var maxDistance = obj.spriteRenderer.bounds.size.x * 2;

        if (rightDistance >= maxDistance)
        {
            obj.prop.transform.position =
                new Vector3(obj.prop.transform.position.x + obj.spriteRenderer.bounds.size.x * 3,
                obj.prop.transform.position.y, obj.prop.transform.position.z);
        }
        else if (leftDistance <= -maxDistance)
        {
            obj.prop.transform.position =
                new Vector3(obj.prop.transform.position.x - obj.spriteRenderer.bounds.size.x * 3,
                obj.prop.transform.position.y, obj.prop.transform.position.z);
        }
    }

    IEnumerator GenerateLightning()
    {
        yield return new WaitForSeconds(Random.Range(5, 10));

        var frustumWidth = 2.0f * -mainCam.transform.position.z * Mathf.Tan(mainCam.fieldOfView * 0.5f * Mathf.Deg2Rad) * mainCam.aspect;
        Transform lightningTransform = lightningSkeletonAnimation.transform;

        var xOffset = Random.Range(-frustumWidth / 2, frustumWidth / 2);
        lightningTransform.position = new Vector3(mainCam.transform.position.x + xOffset, -Random.Range(4f, 5f), 0);
        lightningTransform.eulerAngles = new Vector3(0, 0, Random.Range(0, 45) * Mathf.Sign(xOffset));
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.lightning);
        lightningSkeletonAnimation.state.SetAnimation(0, "bgBolt1", false);

        StartCoroutine(GenerateLightning());
    }

    void SortOrderInLayer(List<PropObj> objs)
    {
        for(int i = 0; i < objs.Count - 1; i++)
        {
            for(int j = 0; j < objs.Count - i - 1; j++)
            {
                if (objs[j].prop.transform.position.z < objs[j + 1].prop.transform.position.z)
                {
                    PropObj temp = objs[j];
                    objs[j] = objs[j + 1];
                    objs[j + 1] = temp;
                }
            }
        }

        for (int i = 0; i < objs.Count; i++)
        {
            objs[i].spriteRenderer.sortingOrder = i;
        }
    }

    void SelectWaterColor()
    {
        switch (backgroundType)
        {
            case BackgroundType.AtlantisDay:
                water.Color1 = new Color(66 / 255f, 210 / 255f, 255 / 255f, 200 / 255f);
                water.Color2 = new Color(9 / 255f, 171 / 255f, 229 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(132 / 255f, 243 / 255f, 255 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(66 / 255f, 210 / 255f, 255 / 255f, 255 / 255f);
                break;
            case BackgroundType.AtlantisUnderwater:
                //water.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 250 / 255f);
                //water.Color2 = new Color(0 / 255f, 20 / 255f, 39 / 255f, 255 / 255f);

                //waterBG.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 255 / 255f);
                //waterBG.Color2 = new Color(0 / 255f, 32 / 255f, 44 / 255f, 255 / 255f);

                water.gameObject.SetActive(false);
                waterBG.gameObject.SetActive(false);
                break;
            case BackgroundType.DarkClouds:
                water.Color1 = new Color(95 / 255f, 109 / 255f, 94 / 255f, 200 / 255f);
                water.Color2 = new Color(80 / 255f, 111 / 255f, 111 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(214 / 255f, 155 / 255f, 99 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(214 / 255f, 155 / 255f, 99 / 255f, 255 / 255f);
                break;
            case BackgroundType.Darkness:
                water.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 250 / 255f);
                water.Color2 = new Color(0 / 255f, 20 / 255f, 39 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(0 / 255f, 32 / 255f, 44 / 255f, 255 / 255f);
                break;
            case BackgroundType.Day:
                water.Color1 = new Color(70 / 255f, 235 / 255f, 221 / 255f, 200 / 255f);
                water.Color2 = new Color(14 / 255f, 75 / 255f, 134 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(165 / 255f, 255 / 255f, 255 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(14 / 255f, 75 / 255f, 134 / 255f, 255 / 255f);
                break;
            case BackgroundType.Dusk:
                water.Color1 = new Color(246 / 255f, 33 / 255f, 29 / 255f, 200 / 255f);
                water.Color2 = new Color(70 / 255f, 0, 2 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(91 / 255f, 16 / 255f, 17 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(181 / 255f, 34 / 255f, 27 / 255f, 255 / 255f);
                break;
            case BackgroundType.Night:
                water.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 200 / 255f);
                water.Color2 = new Color(0 / 255f, 20 / 255f, 39 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(1 / 255f, 31 / 255f, 43 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(0 / 255f, 32 / 255f, 44 / 255f, 255 / 255f);
                break;
            case BackgroundType.Rain:
                water.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 200 / 255f);
                water.Color2 = new Color(0 / 255f, 20 / 255f, 39 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(1 / 255f, 31 / 255f, 43 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(0 / 255f, 32 / 255f, 44 / 255f, 255 / 255f);
                break;
            case BackgroundType.Snow:
                water.Color1 = new Color(1 / 255f, 63 / 255f, 86 / 255f, 200 / 255f);
                water.Color2 = new Color(0 / 255f, 20 / 255f, 39 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(1 / 255f, 31 / 255f, 43 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(0 / 255f, 32 / 255f, 44 / 255f, 255 / 255f);
                break;
            case BackgroundType.Twilight:
                water.Color1 = new Color(197 / 255f, 86 / 255f, 195 / 255f, 200 / 255f);
                water.Color2 = new Color(146 / 255f, 37 / 255f, 145 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(208 / 255f, 109 / 255f, 213 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(208 / 255f, 109 / 255f, 213 / 255f, 255 / 255f);
                break;
            case BackgroundType.Volcano:
                water.Color1 = new Color(246 / 255f, 33 / 255f, 29 / 255f, 200 / 255f);
                water.Color2 = new Color(70 / 255f, 0, 2 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(91 / 255f, 16 / 255f, 17 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(181 / 255f, 34 / 255f, 27 / 255f, 255 / 255f);
                break;
            case BackgroundType.Waterfall:
                water.Color1 = new Color(90 / 255f, 150 / 255f, 199 / 255f, 200 / 255f);
                water.Color2 = new Color(37 / 255f, 97 / 255f, 147 / 255f, 255 / 255f);

                waterBG.Color1 = new Color(109 / 255f, 171 / 255f, 215 / 255f, 255 / 255f);
                waterBG.Color2 = new Color(109 / 255f, 171 / 255f, 215 / 255f, 255 / 255f);
                break;
            case BackgroundType.FTUE:

                if (GameData.instance.fileHandler.currentMission == 0)
                {
                    water.Color1 = new Color(91 / 255f, 87 / 255f, 172 / 255f, 200 / 255f);
                    water.Color2 = new Color(52 / 255f, 52 / 255f, 85 / 255f, 255 / 255f);

                    waterBG.Color1 = new Color(91 / 255f, 87 / 255f, 172 / 255f, 255 / 255f);
                    waterBG.Color2 = new Color(52 / 255f, 51 / 255f, 85 / 255f, 255 / 255f);

                    katStar.gameObject.SetActive(false);
                    Observer.RegisterCustomEvent(gameObject, "TutorialComplete", () => {

                        katStar.transform.position = new Vector3(GameManager.instance.player.transform.GetWorldPositionX() + Globals.CocosToUnity(300), Globals.CocosToUnity(800), Globals.CocosToUnity(4500));
                        const float duration = 80;
                        katStar.gameObject.SetActive(true);
                        katStar.state.SetAnimation(0, "animation2", true);
                        katStar.state.SetAnimation(1, "Distance", false);
                        katStar.state.Data.SetMix("animation2", "animation4", 0.1f);
                        katStar.transform.SetScale(2);
                        katStar.state.TimeScale = 0.2f;
                        DOTween.Sequence().SetId("KatStar").Append(katStar.transform.DOScale(Vector3.one * 8, duration)).Append(katStar.transform.DOMove(new Vector3(GameManager.instance.player.transform.GetWorldPositionX() + Globals.CocosToUnity(900), Globals.CocosToUnity(2000), Globals.CocosToUnity(2500)), duration)).Play();
                        //bgOverlay2.runAction(FadeTo::create(duration, 0));TODO

                        katStar.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
                        {
                            //_bgConfig._propName3._scale = 6; TODO

                            if (spineEvent.Data.Name == "explosion")
                            {
                                SkeletonAnimation explosion = null;
                                bool didFindExp = false;
                                foreach (SkeletonAnimation exp in explosions)
                                {
                                    if (!exp.gameObject.activeInHierarchy)
                                    {
                                        explosion = exp;
                                        didFindExp = true;
                                        break;
                                    }
                                }
                                if (!didFindExp)
                                    return;
                                explosion.gameObject.SetActive(true);
                                explosion.transform.SetScale( 2.5f * katStar.transform.localScale.x);
                                explosion.state.TimeScale = 0.5f;
                                //if (katStar.getPositionZ() > -2500)
                                //{
                                //    this.addChild(explosion, -13);

                                //}
                                //else
                                //{
                                //    this.addChild(explosion, -14);

                                //}
                                explosion.state.SetAnimation(0, "explosion3", false);
                                Bone bone = katStar.skeleton.FindBone("explosion");
                                Vector3 pos = bone.GetWorldPosition(katStar.transform); //(katStar.transform.position.x + (katStar.transform.localScale.x * bone.WorldX), katStar.getPositionZ() / 3, katStar.getPositionZ()));
                                explosion.transform.position = new Vector3(pos.x ,-4.4f,pos.z);
                                DOTween.Sequence().SetId("ExpDelay").AppendInterval(0.15f).AppendCallback(() =>
                                {
                                    explosion.gameObject.GetComponent<Renderer>().enabled = true;
                                }).AppendInterval(3f).AppendCallback(() =>
                                {
                                    explosion.gameObject.GetComponent<Renderer>().enabled = false;
                                    explosion.gameObject.SetActive(false);
                                }).Play();
                                explosion.transform.DOScale( 6.5f, 1);
                                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.explosion10DistantUnderwater, 0.15f * explosion.transform.localScale.x);

                            }
                            if (spineEvent.Data.Name == "WhiteScreen")
                            {
                                Observer.DispatchCustomEvent("WhiteScreen");
                            }
                            if (spineEvent.Data.Name == "ShakeScreen")
                            {
                                GameManager.instance.ShakeCamera(Globals.CocosToUnity(150), 15);
                                GameManager.instance.backgroundFadeLayer.SetOpacity(0);
                                GameManager.instance.backgroundFadeLayer.DOFade(0.2745098f, 0.04f);
                            }
                            if (spineEvent.Data.Name == "Transition")
                            {
                                ftueTransition.SetActive(true);
                                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.explosion25);
                                    GameManager.instance.backgroundFadeLayer.SetOpacity(0);
                                //GameManager.instance.backgroundFadeLayer.sortingLayerName = "Player";
                                //GameManager.instance.backgroundFadeLayer.sortingOrder = -100001;
                                GameManager.instance.backgroundFadeLayer.DOFade(1, 0.001f);

                            }
                        };

                        Observer.RegisterCustomEvent(gameObject, "KatStarFocus", () => {
                            Observer.DispatchCustomEvent("hide_cursor");
                            katStar.state.TimeScale = 0.6f;
                            katStar.state.SetAnimation(0, "animation4", false);
                            for (int i = 0; i < 10; i++)
                            {
                                EnableObject(i);
                                chargeRadial[i].transform.SetScale(3 + Random.Range(0, 5));
                                chargeRadial[i].transform.localPosition = new Vector2(0, Globals.CocosToUnity(-50));
                                chargeRadial[i].transform.SetRotation(Random.Range(0, 360));
                            }
                            //katStar.setGlobalZOrder(10);
                            Globals.bossPosition = new Vector3(katStar.transform.position.x, katStar.transform.position.y,0);
                            Globals.zoomToBossForSec = 2.5f;
                            Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
                            GameManager.instance.backgroundFadeLayer.DOFade(0, 0.1f);
                            foreach (PropObj c in cloudsList)
                            {
                                c.spriteRenderer.DOFade(0, 0.02f);
                            }


                        });



                        Observer.RegisterCustomEvent(gameObject, "DeathControlsInit", () => {
                            DOTween.Kill("KatStar");
                            DOTween.Sequence().SetId("KatStar").Append(katStar.transform.DOScale(Vector3.one * 8, 1.5f)).Play();
                            DOTween.Sequence().SetId("KatStar").Append(katStar.transform.DOMove(new Vector3(katStar.transform.position.x,Globals.CocosToUnity(2000),Globals.CocosToUnity(2500)), 1.5f)).OnComplete(()=> {
                                Globals.bossPosition = new Vector3(katStar.transform.position.x, katStar.transform.position.y, 0);
                                foreach(PropObj cloud in topClouds)
                                    cloud.spriteRenderer.color = new Color(0, 0, 0, 0);
                                currentCollection.cloudColor = new Color(0, 0, 0, 0);
                                katStar.GetComponent<Renderer>().sortingLayerName = "Player";
                                katStar.GetComponent<Renderer>().sortingOrder = -1000;
                            }).Play();
                            GameManager.instance.backgroundFadeLayer.DOFade(0, 1.5f);
                        });


                    });



//#if UNITY_STANDALONE
//                this.runAction(Sequence::create(DelayTime::create(0.2f),CallFunc::create([=](){
//                    Director::getInstance().getEventDispatcher().dispatchCustomEvent("TutorialComplete");
//                }), NULL));
//#endif

                    break;
                }
                break;
            default:
                break;
        }
    }

    private void EnableObject(int index)
    {
        DOTween.Sequence().SetId("Charge").AppendInterval(index * 0.005f).AppendCallback(() => { chargeRadial[index].SetActive(true); }).Play();
    }

    public void ToggleDarkEffect(bool on) => darkEffect.SetActive(on);

    public void BackToBackgroundSelection()
    {
        SceneManager.LoadScene("MainMenu");
    }

    public void DisableBackground()
    {
        StopAllCoroutines();
        scheduleUpdate = false;
        Camera.main.cullingMask &= ~(1 << LayerMask.NameToLayer("BackgroundLayer"));
    }

    struct PropObj
    {
        public GameObject prop;
        public SpriteRenderer spriteRenderer;
    }
}
