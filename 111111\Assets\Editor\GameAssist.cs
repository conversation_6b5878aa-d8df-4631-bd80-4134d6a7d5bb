﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

using UnityEditor;
using UnityEditor.Animations;

using UnityEngine;
using UnityEngine.UI;

public class GameAssist
{
    [MenuItem("工具/生成图集表格UIAtlasConfig")]
    public static void PackSprite()
    {
        StringBuilder csvSB = new StringBuilder();
        csvSB.AppendLine("ID,Pack,Name");
        csvSB.AppendLine("ID,包名,图名");
        csvSB.AppendLine("1:int,2:string,3:string");

        string rootPath = "Assets/Temp/uisprite";
        string[] dirList = Directory.GetDirectories(rootPath);
        if (dirList == null)
        {
            Debug.LogError("PackSprite 找不到uisprite目录。");
            return;
        }

        float progress = 0.0f;
        EditorUtility.DisplayProgressBar("生成图集", "生成中", progress);
        int nProgressCount = 0;
        int nProgressTotal = dirList.Length;
        Dictionary<string, bool> nameSet = new Dictionary<string, bool>();
        int nCount = 0;
        foreach (var dir in dirList)
        {
            ++nProgressCount;
            EditorUtility.DisplayProgressBar("生成图集", "生成中", (float)nProgressCount / nProgressTotal);
            if (dir != rootPath)
            {
                string dirName = Path.GetFileNameWithoutExtension(dir);
                
                string[] fileList = Directory.GetFiles(dir);
                foreach (var fileName in fileList)
                {
                    if (fileName.EndsWith(".png") || fileName.EndsWith(".tga"))
                    {
                        TextureImporter texImporter = TextureImporter.GetAtPath(fileName) as TextureImporter;
                        if (texImporter != null)
                        {
                            bool bChanged = false;
                            if (texImporter.textureType == TextureImporterType.Default)
                            {
                                texImporter.textureType = TextureImporterType.Sprite;
                                bChanged = true;
                            }

                            //if (texImporter.spritePackingTag != dirName)
                            //{
                            //    texImporter.spritePackingTag = dirName;
                            //    bChanged = true;
                            //}

                            if (bChanged)
                                texImporter.SaveAndReimport();
                        }
                        else
                        {
                            Debug.LogError("PackSprite texImporter == null " + fileName);
                        }
                        
                        string fileSpName = Path.GetFileNameWithoutExtension(fileName);
                        Sprite sp = AssetDatabase.LoadAssetAtPath<Sprite>(fileName);
                        if (sp)
                        {
                            ++nCount;
                            csvSB.AppendLine(nCount + "," + dirName + "," + fileSpName);
                            if (!nameSet.ContainsKey(fileSpName))
                            {
                                nameSet.Add(fileSpName, true);
                            }
                            else
                            {
                                Debug.LogError("PackSprite 图片名称重复！ " + fileSpName);
                            }
                        }
                    }
                }
            }
        }

        AssetDatabase.SaveAssets();

        try
        {
            FileStream fs = File.Open(Path.Combine(Application.dataPath, "Temp/scp/UIAtlasConfig.csv"), FileMode.Create);
            if (fs != null)
            {
                StreamWriter sw = new StreamWriter(fs);
                sw.Write(csvSB.ToString());
                sw.Close();
                fs.Close();
            }
            else
            {
                Debug.LogError("PackSprite 写入UIAtlasConfig文件失败。");
            }
        }
        catch(Exception e)
        {
            if (e != null)
            {
                Debug.LogError("PackSprite 写入UIAtlasConfig文件失败2 " + e.ToString());
            }
            else
            {
                Debug.LogError("PackSprite 写入UIAtlasConfig文件失败3 ");
            }
        }

        EditorUtility.ClearProgressBar();

        // 表格生成后自动转pb
        string csvName = "UIAtlasConfig";
        string pathName = Path.GetFullPath(@"..\..\CsvTool", Application.dataPath);
        Debug.Log("csv2pb.exe 目录=" + pathName);
        string csv2pb = Path.Combine(pathName, "csv2pb.exe");
        if (!File.Exists(csv2pb))
        {
            Debug.LogError("PackSprite 表格转pb失败，csv2pb.exe路径不对");
            return;
        }

        string dirBak = Directory.GetCurrentDirectory();
        Directory.SetCurrentDirectory(pathName);
        try
        {
            System.Diagnostics.Process process = new();
            System.Diagnostics.ProcessStartInfo startInfo = new(csv2pb, $"trans {csvName}");
            process.StartInfo = startInfo;
            process.Start();
        }
        catch (Exception e)
        {
            if (e != null)
            {
                Debug.LogError("PackSprite 运行lua失败 " + e.ToString());
            }
            else
            {
                Debug.LogError("PackSprite 运行lua失败2");
            }
        }

        Directory.SetCurrentDirectory(dirBak);
    }

    #region 动画控制器
    [MenuItem("工具/生成model目录下所有模型的动画控制器")]
    static void AnimationProcessAllModles()
    {
        string dir = "Assets/model";
        string[] ds = Directory.GetDirectories(dir);
        for (int i = 0; i < ds.Length; ++i)
        {
            string d = ds[i];
            int index = d.LastIndexOf("\\");

            AnimationProcessModel(dir, d.Substring(index + 1, d.Length - index - 1));
        }

        AssetDatabase.SaveAssets();
    }
    [MenuItem("Assets/生成选中模型的动画控制器")]
    static void AnimationProcessSelectedModle()
    {
        if (Selection.objects == null || Selection.objects.Length == 0)
        {
            return;
        }
        foreach (var o in Selection.objects)
        {
            Debug.Log(o.name);

            AnimationProcessModel("Assets/model", o.name);
        }

        AssetDatabase.SaveAssets();
    }
    public static bool HasFBXAnimation(string path, string model_name, string animation_name)
    {
        return File.Exists(path + "/" + model_name + "/fbx/" + model_name + "@" + animation_name + ".FBX");
    }

    // 例如:Assets/model, Boss001
    public static AnimatorController GetAnimatorcontroller(string path, string model_name)
    {
        string animator_folder = path + "/" + model_name + "/animator";
        if (!Directory.Exists(animator_folder))
        {
            // 不存在，创建之 
            AssetDatabase.CreateFolder(path + "/" + model_name, "animator");
        }

        string controller_path = animator_folder + "/" + model_name + ".controller";
        Debug.Log(controller_path);
        AnimatorController animatorController;
        if (File.Exists(controller_path))
        {
            AssetDatabase.DeleteAsset(controller_path);
        }
        // 创建之
        animatorController = AnimatorController.CreateAnimatorControllerAtPath(controller_path);
        return animatorController;
    }
    // 例如:Assets/model, Boss001， stand， stand
    private static AnimatorState AnimationAddState(string path, string model_name, string animation, string animation_alternative, AnimatorStateMachine asm, Dictionary<string, AnimatorState> dict, string tarAnimation)
    {
        //
        string fbx_path = path + "/" + model_name + "/fbx/" + model_name + "@" + animation + ".FBX";
        if (!File.Exists(fbx_path))
        {
            fbx_path = path + "/" + model_name + "/fbx/" + model_name + "@" + animation_alternative + ".FBX";
            if (!File.Exists(fbx_path))
            {
                return null;
            }
        }
        AnimatorState state = asm.AddState(animation);
        //根据动画文件读取它的AnimationClip对象
        AnimationClip newClip = AssetDatabase.LoadAssetAtPath(fbx_path, typeof(AnimationClip)) as AnimationClip;
        state.motion = newClip;

        dict.Add(animation, state);
        if (tarAnimation != null)
            AddAniTransition(dict, animation, tarAnimation, model_name);
        return state;
    }

    private static void AddAniTransition(Dictionary<string, AnimatorState> aniDict, string srcAni, string tarAni, string modelName)
    {
        if (!aniDict.ContainsKey(srcAni) || !aniDict.ContainsKey(tarAni) || srcAni == tarAni)
        {
            Debug.LogErrorFormat("AddAniTransition 找不到目标动画 {0} {1} {2}", srcAni, tarAni, modelName);
            return;
        }

        AnimatorState srcState = aniDict[srcAni];
        AnimatorState tarState = aniDict[tarAni];
        AnimatorStateTransition ast = srcState.AddTransition(tarState);
        if (ast)
        {
            ast.duration = 0.0f;
            ast.hasExitTime = true;
            ast.exitTime = 1;
        }
    }

    // 处理某个模型，例如 Assets/model, Boss001
    static void AnimationProcessModel(string path, string model_name)
    {
        if (!HasFBXAnimation(path, model_name, "stand"))
        {
            return;
        }

        Dictionary<string, AnimatorState> aniDict = new Dictionary<string, AnimatorState>();
        // 获取动画控制器        
        AnimatorController animatorController = GetAnimatorcontroller(path, model_name);
        // 加入节点
        AnimatorStateMachine asm = animatorController.layers[0].stateMachine;
        AnimationAddState(path, model_name, "stand", "stand", asm, aniDict, null);
        AnimationAddState(path, model_name, "born", "stand", asm, aniDict, "stand");
        AnimationAddState(path, model_name, "die", "die", asm, aniDict, null);
        AnimationAddState(path, model_name, "idle", "stand", asm, aniDict, "stand");
        AnimationAddState(path, model_name, "run", "run", asm, aniDict, null);

        StringBuilder sb = new StringBuilder();
        // 攻击 5 个
        for (int w = 1; w <= 5; ++w)
        {
            sb.Length = 0;
            sb.Append("pg");
            sb.Append(w);
            AnimationAddState(path, model_name, sb.ToString(), "pg1", asm, aniDict, "stand");
        }
        // 受击 3 个
        for (int w = 1; w <= 3; ++w)
        {
            sb.Length = 0;
            sb.Append("behit");
            sb.Append(w);
            AnimationAddState(path, model_name, sb.ToString(), "behit1", asm, aniDict, "stand");
        }
    }

    private static Dictionary<string, int> constAnimationList = new Dictionary<string, int>();
    private static int nCreatureMotionConfigMaxID = 0;
    [MenuItem("工具/导出CreatureMotionConfig动画时长")]
    static void CalcCreatureMotionConfig()
    {
        Debug.Log("导出动画时长开始");
        float process = 0;
        EditorUtility.DisplayProgressBar("导出动画时长", "读取CreatureMotionConfig", process);

        FileStream fs = null;
        StreamReader sr = null;
        List<List<string>> finalData = new List<List<string>>();
        try
        {
            fs = File.Open(Application.dataPath + "/Temp/scp/CreatureMotionConfig.csv", FileMode.OpenOrCreate);
            if (fs == null)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("CreatureMotionConfig 文件加载失败。");
                return;
            }
            sr = new StreamReader(fs, Encoding.UTF8);
            if (sr == null)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("CreatureMotionConfig 文件加载失败。");
                fs.Close();
                return;
            }

            int nColCount = 0;
            for (int i = 0; i < 10000000; ++i)
            {
                string line = sr.ReadLine();
                if (line == null)
                    break;
                string[] strSplit = line.Split(',');
                List<string> strCol = new List<string>(strSplit);
                if (i == 0)
                {
                    nColCount = strCol.Count;
                }
                else
                {
                    if (strCol.Count < nColCount)
                    {
                        Debug.LogError("CreatureMotionConfig 第" + (i + 1) + "行列数不对");
                        int nDelta = nColCount - strCol.Count;
                        for (int n = 0; n < nDelta; ++n)
                        {
                            strCol.Add("-1");
                        }
                    }
                }
                finalData.Add(strCol);
            }
        }
        catch (IOException)
        {
            if (sr != null)
            {
                sr.Close();
                sr = null;
            }
            if (fs != null)
            {
                fs.Close();
                fs = null;
            }
            EditorUtility.ClearProgressBar();
            Debug.LogError("CreatureMotionConfig 读取文件失败。");
            return;
        }

        if (sr != null)
        {
            sr.Close();
            sr = null;
        }
        if (fs != null)
        {
            fs.Close();
            fs = null;
        }

        if (finalData.Count < 3)
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError("CreatureMotionConfig 文件格式不对。");
            return;
        }

        process = 0.05f;
        EditorUtility.DisplayProgressBar("导出动画时长", "解析CreatureMotionConfig", process);

        Dictionary<string, int> rowIndex = new Dictionary<string, int>();
        for (int i = 0; i < finalData[0].Count; ++i)
        {
            rowIndex[finalData[0][i]] = i;
        }

        if (!rowIndex.ContainsKey("ID") || !rowIndex.ContainsKey("AniTime") || !rowIndex.ContainsKey("ModelName") || !rowIndex.ContainsKey("AnimationName"))
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError("CreatureMotionConfig 格式不对");
            return;
        }

        constAnimationList.Clear();
        constAnimationList.Add("die", 1);
        StringBuilder sb = new StringBuilder();
        // 怪物 5 个
        for (int w = 1; w <= 5; ++w)
        {
            sb.Length = 0;
            sb.Append("pg");
            sb.Append(w);
            constAnimationList.Add(sb.ToString(), 1);
        }

        int nIDIndex = rowIndex["ID"];
        int nModelIndex = rowIndex["ModelName"];
        int nAniIndex = rowIndex["AnimationName"];
        Dictionary<string, Dictionary<string, List<int>>> modelAniIndex = new Dictionary<string, Dictionary<string, List<int>>>();
        for (int i = 0; i < finalData.Count; ++i)
        {
            List<string> rowData = finalData[i];
            if (i >= 3)
            {
                string modelName = rowData[nModelIndex];
                string animationName = rowData[nAniIndex];
                if (!modelAniIndex.ContainsKey(modelName))
                {
                    Dictionary<string, List<int>> newModel = new Dictionary<string, List<int>>();
                    modelAniIndex.Add(modelName, newModel);
                }

                if (modelAniIndex[modelName].ContainsKey(animationName))
                {
                    // Debug.LogError("CreatureMotionConfig 模型动作重复配置 " + modelName + "@" + animationName);
                    modelAniIndex[modelName][animationName].Add(i);
                }
                else
                {
                    List<int> newList = new List<int>();
                    newList.Add(i);
                    modelAniIndex[modelName].Add(animationName, newList);
                }

                int id = int.Parse(rowData[nIDIndex]);
                if (nCreatureMotionConfigMaxID < id)
                    nCreatureMotionConfigMaxID = id;
            }
        }

        //动画事件
        string[] ds = Directory.GetDirectories("Assets/model");
        for (int i = 0; i < ds.Length; ++i)
        {
            string d = ds[i];

            int index = d.LastIndexOf("\\");
            ProcessCreatureMotionModel("Assets/model", d.Substring(index + 1, d.Length - index - 1), finalData, modelAniIndex, rowIndex);

            process += 0.01f;
            if (process > 0.9)
                process = 0.05f;
            EditorUtility.DisplayProgressBar("导出动画时长", "导出中", process);
        }

        process = 0.99f;
        EditorUtility.DisplayProgressBar("导出动画时长", "写入文件", process);
        StreamWriter sw = null;
        try
        {
            fs = File.Open(Application.dataPath + "/Temp/scp/CreatureMotionConfig.csv", FileMode.Create);
            if (fs == null)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("CreatureMotionConfig 写入文件失败。");
                return;
            }

            sw = new StreamWriter(fs);
            foreach (var row in finalData)
            {
                sb.Length = 0;
                bool isFirst = true;
                foreach (string col in row)
                {
                    if (isFirst)
                        isFirst = false;
                    else
                        sb.Append(',');
                    sb.Append(col);
                }
                sw.WriteLine(sb.ToString());
            }
        }
        catch (IOException)
        {
            if (sw != null)
            {
                sw.Close();
                sw = null;
            }
            if (fs != null)
            {
                fs.Close();
                fs = null;
            }
            EditorUtility.ClearProgressBar();
            Debug.LogError("CreatureMotionConfig 写入文件失败。");
            return;
        }

        if (sw != null)
        {
            sw.Close();
            sw = null;
        }
        if (fs != null)
        {
            fs.Close();
            fs = null;
        }
        EditorUtility.ClearProgressBar();
        Debug.Log("导出动画时长结束");
    }
    // 处理某个模型，例如 Assets/model, Boss001
    static void ProcessCreatureMotionModel(string path, string model_name, List<List<string>> finalData, Dictionary<string, Dictionary<string, List<int>>> modelAniIndex, Dictionary<string, int> rowIndex)
    {
        if (!HasFBXAnimation(path, model_name, "stand"))
        {
            return;
        }
        // 获取动画控制器
        string animator_folder = path + "/" + model_name + "/animator";
        string controller_path = animator_folder + "/" + model_name + ".controller";
        AnimatorController animatorController = AssetDatabase.LoadAssetAtPath<AnimatorController>(controller_path);
        if (animatorController == null)
        {
            return;
        }
        if (animatorController.layers.Length == 0)
        {
            Debug.LogError(string.Format("{0}动画控制器没有layer", model_name));
            return;
        }

        // 加入节点
        UnityEditor.Animations.AnimatorStateMachine asm = animatorController.layers[0].stateMachine;
        if (modelAniIndex.ContainsKey(model_name))
        {
            foreach (var curAni in modelAniIndex[model_name])
            {
                if (constAnimationList.ContainsKey(curAni.Key))
                    continue;
                WriteModelAniTime(path, model_name, curAni.Key, asm, finalData, modelAniIndex, rowIndex);
            }
        }

        foreach (var curAni in constAnimationList)
        {
            WriteModelAniTime(path, model_name, curAni.Key, asm, finalData, modelAniIndex, rowIndex);
        }
    }
    private static void WriteModelAniTime(string path, string model_name, string animation_name, UnityEditor.Animations.AnimatorStateMachine asm, List<List<string>> finalData, Dictionary<string, Dictionary<string, List<int>>> modelAniIndex, Dictionary<string, int> rowIndex)
    {
        string name = animation_name;
        if (!HasFBXAnimation(path, model_name, name))
        {
            return;
        }

        float aniSpeed = GetAnimationSpeed(animation_name, asm);
        if (aniSpeed == 0)
        {
            Debug.LogError("GetModelAniTime 动画速度为0？ " + model_name + " " + animation_name);
            return;
        }

        float fAniTime = GetAnimationLength(animation_name, asm, model_name) / aniSpeed;
        if (modelAniIndex.ContainsKey(model_name) && modelAniIndex[model_name].ContainsKey(animation_name))
        {
            List<int> indexList = modelAniIndex[model_name][animation_name];
            foreach (int nIndex in indexList)
            {
                finalData[nIndex][rowIndex["AniTime"]] = fAniTime.ToString();
            }
        }
        else
        {
            // 新增一行
            List<string> rowData = new List<string>();
            for (int i = 0; i < finalData[0].Count; ++i)
            {
                string colName = finalData[0][i];
                if (colName == "ID")
                {
                    ++nCreatureMotionConfigMaxID;
                    rowData.Add(nCreatureMotionConfigMaxID.ToString());
                }
                else if (colName == "ModelName")
                {
                    rowData.Add(model_name);
                }
                else if (colName == "AnimationName")
                {
                    rowData.Add(animation_name);
                }
                else if (colName == "AniTime")
                {
                    rowData.Add(fAniTime.ToString());
                }
                else if (colName == "LogicTime")
                {
                    rowData.Add(fAniTime.ToString());
                }
                else
                {
                    rowData.Add("-1");
                }
            }

            finalData.Add(rowData);
        }
    }
    private static float GetAnimationSpeed(string animation_name, UnityEditor.Animations.AnimatorStateMachine asm)
    {
        foreach (var e in asm.states)
        {
            if (string.Equals(animation_name, e.state.name, StringComparison.CurrentCultureIgnoreCase))
            {
                return e.state.speed;
            }
        }
        return 1.0f;
    }
    private static float GetAnimationLength(string animation_name, UnityEditor.Animations.AnimatorStateMachine asm, string model_name)
    {
        foreach (var e in asm.states)
        {
            if (string.Equals(animation_name, e.state.name, StringComparison.CurrentCultureIgnoreCase))
            {
                AnimationClip newClip = (AnimationClip)e.state.motion;
                if (newClip == null)
                {
                    // throw (new Exception("new clip 为null"));
                    Debug.LogError("模型 " + model_name + " 动画AnimationClip为空 " + animation_name);
                    return 0;
                }
                return newClip.length;
            }
        }
        return 1.0f;
    }
    #endregion

    [CustomEditor(typeof(TextAssist))]
    public class TextAssistEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            if (target == null)
                return;
            base.OnInspectorGUI();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("套上字体预览"))
            {
                // 套上字体
                TextAssist ta = target as TextAssist;
                if (ta != null)
                {
                    Text txt = ta.GetComponent<Text>();
                    if (txt != null)
                    {
                        Font font = Helper.LoadResourceFont(ta.fontName);
                        if (font)
                        {
                            txt.font = font;
                        }
                    }
                }
            }
            if (GUILayout.Button("用完别忘还原"))
            {
                // 还原字体
                TextAssist ta = target as TextAssist;
                if (ta != null)
                {
                    Text txt = ta.GetComponent<Text>();
                    if (txt != null)
                    {
                        txt.text = "";
                        txt.font = null;
                        txt.enabled = false;
                        txt.enabled = true;
                    }
                }
            }
            EditorGUILayout.EndHorizontal();
        }
    }
}