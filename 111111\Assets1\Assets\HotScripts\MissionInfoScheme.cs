using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;


public class MissionInfoScheme : Singleton<MissionInfoScheme>
{
    private MissionInfo _data;
    private Dictionary<string, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load(false);
        }
    }
    public bool Load(bool isReadResources)
    {
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.MissionInfo;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            byte[] by;
            if (isReadResources)
            {
                by = HotResManager.ReadPbFromResources(pbFileName);
            }
            else
            {
                by = HotResManager.ReadPb(pbFileName);
            }
            MemoryStream ms = new MemoryStream(by);
            _data = Serializer.Deserialize<MissionInfo>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].MissionID] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;

    }
    public MissionInfo.Item GetItem(string id, bool isReadResources = false)
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load(isReadResources);
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            throw new Exception("id dont exist");
        }

    }
}


