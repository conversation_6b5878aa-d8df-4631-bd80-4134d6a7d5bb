using System.Collections.Generic;
using UnityEngine;
using System.Linq;

[CreateAssetMenu(fileName = "PrefabsContainer", menuName = "ScriptableObjects/PrefabsContainer")]
public class PrefabsContainer : ScriptableObject
{
    [SerializeField] List<PrefabInfo> prefabs;

    public Object GetObject(string name) => prefabs.FirstOrDefault(p => p.name == name).prefabObject; 
}

[System.Serializable]
public struct PrefabInfo
{
    public string name;
    public Object prefabObject;
}
