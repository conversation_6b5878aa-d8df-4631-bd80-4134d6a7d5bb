using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Zapper : Sidekick
{
    [SerializeField] Transform zaplineContainer;
    [SerializeField] GameObject zapLinePrefab, zapPrefab;
    [SerializeField] int numberOfZaplines;

    List<ZapperObject> zaplinesPool;
    float playerAttack = 1;
    int attackCount = 0;
    [IFix.Interpret]
    double maxAttackCount = 6;

    void Start()
    {
        Init();
        
    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        attackCount = 0;
        //sideKickSprite->setMix("select", "idle", 0.4f);
        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);
        transform.localScale = new Vector3(scale, scale, 1);
        SetStartingPosition();
        CreatePool();
    }

    public override void StartSidekick()
    {
        sidekickSkeleton.state.SetAnimation(0, "idle", true);
        InvokeRepeating("Shoot", 0.1f, 0.1f);
    }

    private void Update()
    {
        SidekicksUpdate();
    }
    [IFix.Interpret]
    public void SetMonsterData()
    {
        if (monsterNew != null)
        {
            maxAttackCount = monsterNew.DamageAdd;
        }
    }
    [IFix.Patch]
    void Attack(Vector2 startPos, bool drawLine)
    {
        attackCount++;
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (Vector2.Distance(startPos, enemy.transform.position) < Globals.CocosToUnity(500) && attackCount < maxAttackCount && enemy.tag != "TaggedEnemy")
            {
                ZapperObject zapperObject = new ZapperObject();

                bool didFindzapline = false;
                int i = 0;
                for (i = 0; i < zaplinesPool.Count; i++)
                {
                    if (!zaplinesPool[i].isInUse)
                    {
                        zapperObject = zaplinesPool[i];
                        didFindzapline = true;
                        break;
                    }
                }
                if (!didFindzapline)
                {
                    return;
                }

                if (drawLine)
                {
                    Transform zapLineTransform = zapperObject.zapline.transform;
                    zapperObject.isInUse = true;
                    zaplinesPool[i] = zapperObject;

                    SpriteRenderer zaplineRenderer = zapLineTransform.GetComponent<SpriteRenderer>();
                    //zaplineRenderer.color = new Color(zaplineRenderer.color.r, zaplineRenderer.color.g,
                    //    zaplineRenderer.color.b, 1);
                    zapLineTransform.gameObject.SetActive(true);

                    zapLineTransform.position = (startPos + (Vector2)enemy.transform.position) / 2;
                    var dir = Vector2.SignedAngle(Vector2.right, (Vector2)enemy.transform.position - startPos);
                    dir = dir < 0 ? 360 + dir : dir;
                    zapLineTransform.rotation = Quaternion.AngleAxis(dir, Vector3.forward);

                    var targetScaleX = ((Vector2)enemy.transform.position - startPos).magnitude;
                    if (targetScaleX == 0) targetScaleX = 0.1f;
                    var targetScaleY = 2;

                    var xWorldUnits = zapLineTransform.localScale.x * zapLineTransform.root.localScale.x
                        * (zaplineRenderer.sprite.rect.width / zaplineRenderer.sprite.pixelsPerUnit);
                    var yWorldUnits = zapLineTransform.localScale.y * zapLineTransform.root.localScale.y
                        * (zaplineRenderer.sprite.rect.height / zaplineRenderer.sprite.pixelsPerUnit);

                    zapLineTransform.localScale = new Vector3(
                        zapLineTransform.localScale.x / xWorldUnits * targetScaleX,
                        zapLineTransform.localScale.y / yWorldUnits * targetScaleY,
                        zapLineTransform.localScale.z);
                }
                enemy.tag = "TaggedEnemy";

                GameObject zap = zaplinesPool[i].zap;
                zap.transform.position = enemy.transform.position;
                float zapScale = 0.75f / enemy.transform.localScale.x;
                zap.transform.localScale = new Vector3(zapScale, zapScale, 1);
                zap.SetActive(true);
                //StartCoroutine(nameof(TurnOffZap), zap);

                Attack(enemy.transform.position, true);
                enemy.tag = "Untagged";

                if (enemy.TakeHit(damage))
                {
                    if (!enemy.isDestroyed)
                    {
                        enemy.isDestroyed = true;
                        //enemy.Destroy();
                        GameManager.instance.physicsManager.DestroyEnemy(enemy);
                    }
                }
                return;
            }
        }
    }

    IEnumerator TurnOffZap(GameObject obj)
    {
        yield return new WaitForSeconds(0.05f);

        obj.SetActive(false);
    }

    void CreatePool()
    {
        zaplinesPool = new List<ZapperObject>();
        for(int i = 0; i < numberOfZaplines; i++)
        {
            GameObject zapline = Instantiate(zapLinePrefab, zaplineContainer);
            zapline.transform.localPosition = Vector3.zero;
            //SpriteRenderer zaplineRenderer = zapline.GetComponent<SpriteRenderer>();
            //zaplineRenderer.color = new Color(zaplineRenderer.color.r, zaplineRenderer.color.g,
            //    zaplineRenderer.color.b, 0);
            zapline.SetActive(false);

            GameObject zap = Instantiate(zapPrefab, zaplineContainer);
            zap.transform.localPosition = Vector3.zero;
            zap.SetActive(false);

            var zapperObj = new ZapperObject();
            zapperObj.zapline = zapline;
            zapperObj.zap = zap;
            zapperObj.isInUse = false;

            zaplinesPool.Add(zapperObj);
        }
    }

    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;

        for(int i = 0; i < zaplinesPool.Count; i++)
        {
            ZapperObject zObj = new ZapperObject();

            zObj.isInUse = false;
            zObj.zapline = zaplinesPool[i].zapline;
            //SpriteRenderer zaplineRenderer = zObj.zapline.GetComponent<SpriteRenderer>();
            //zaplineRenderer.color = new Color(zaplineRenderer.color.r, zaplineRenderer.color.g,
            //    zaplineRenderer.color.b, 0);
            zObj.zapline.SetActive(false);
            zObj.zap = zaplinesPool[i].zap;
            zObj.zap.SetActive(false);

            zaplinesPool[i] = zObj;
        }

        attackCount = 0;
        StopCoroutine(nameof(TurnOffZap));
        Attack(transform.position, true);
    }

    struct ZapperObject
    {
        public bool isInUse;
        public GameObject zapline;
        public GameObject zap;
    }
}
