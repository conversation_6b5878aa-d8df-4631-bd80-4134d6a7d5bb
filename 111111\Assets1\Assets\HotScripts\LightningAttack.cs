using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
public class LightningAttack : MonoBehaviour
{
    public bool isInUse;

    [SerializeField] private SkeletonAnimation lightning = null;
    [SerializeField] private SpriteRenderer alert;
    [SerializeField] private Color lightningColor;

    private float attackDelay = 2.0f;
    bool allowDmg = false;
    float mainPosX;
    private bool scheduleUpdate;

    public void PlayLightningAnim()
    {
        gameObject.SetActive(true);
        allowDmg = false;
        mainPosX = GameManager.instance.player.transform.position.x + ( Globals.CocosToUnity(-1000) + (Random.value * Globals.CocosToUnity(2000)));
        alert.gameObject.SetActive(true);
        alert.transform.position = new Vector2(mainPosX, (Globals.LOWERBOUNDARY + Globals.UPPERBOUNDARY) / 2);
        alert.transform.SetScaleY(20);
        DOTween.Sequence().Append(alert.DOColor(lightningColor, attackDelay)).AppendCallback(() =>
        {
            alert.gameObject.SetActive(false);
        }).Play();
        lightning.gameObject.SetActive(false);
        lightning.transform.SetWorldPositionX(mainPosX);
        DOTween.Sequence().AppendInterval(attackDelay).AppendCallback(()=>{

            lightning.gameObject.SetActive(true);
            lightning.state.SetAnimation(0, "bolt", true);
            allowDmg = true;

        }).Play();

        DOTween.Sequence().AppendInterval(attackDelay + 0.5f).AppendCallback(()=>
        {
            lightning.gameObject.SetActive(false);
            mainPosX = 0;
            allowDmg = false;
            scheduleUpdate = false;
            gameObject.SetActive(false);
        });
        scheduleUpdate = true;
    }

    public void Update()
    {
        
        if (!scheduleUpdate)
            return;
        if (!allowDmg)
        {
            return;
        }
        if (GameManager.instance.player.transform.position.x < lightning.transform.position.x + Globals.CocosToUnity(120) &&
           GameManager.instance.player.transform.position.x > lightning.transform.position.x- Globals.CocosToUnity(120)
           && GameManager.instance.player.canHit)
        {
            GameManager.instance.player.GotHit(GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 15);
        }
    }
}
