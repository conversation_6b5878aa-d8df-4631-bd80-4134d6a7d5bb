using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using DG.Tweening;
using UnityEngine.SceneManagement;
using Spine.Unity;
public class OptionsMenu : BaseMenu
{
    [SerializeField] private SkeletonGraphic menuAnimation;
    [SerializeField] private TextMeshP<PERSON>UGUI optionsHeading;
    [SerializeField] private MainMenuButton musicButton;
    [SerializeField] private TextMeshProUGUI musicButtonStateLabel;
    [SerializeField] private MainMenuButton soundButton;
    [SerializeField] private TextMeshProUGUI soundButtonStateLabel;
    [SerializeField] private MainMenuButton upButton;
    [SerializeField] private TextMeshProUG<PERSON> upButtonStateLabel;
    [SerializeField] private MainMenuButton downButton;
    [SerializeField] private TextMeshProUGUI downButtonStateLabel;
    [SerializeField] private MainMenuButton leftButton;
    [SerializeField] private TextMeshProUG<PERSON> leftButtonStateLabel;
    [SerializeField] private MainMenuButton rightButton;
    [SerializeField] private TextMeshProUG<PERSON> rightButtonStateLabel;
    [SerializeField] private MainMenuButton specialButton;
    [SerializeField] private TextMeshProUGUI specialButtonStateLabel;
    [SerializeField] private MainMenuButton resetButton;
    [SerializeField] private MainMenuButton backButton;
    [SerializeField] private PopUp popUp;

    Sequence seq;
    public void InitOptionsMenu()
    {

        SetEnable(true);



        //    this.setVisible(false);
        //    this.runAction(Sequence::create(DelayTime::create(0.5f),Show::create(),NULL));

        clickSound = Constants_Audio.Audio.menuClick;

        int countNode = 0;
        float animationTime = 1.0f;
        float topMargin = 5500;

        optionsHeading.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["options"] as string;//, GAME_FONT, 130);
                                                                                                    //    optionsHeading.setPosition(_winSize.width/2, _winSize.height/2 + SPACING * 5);

        optionsHeading.GetComponent<RectTransform>().DOAnchorPos(Vector2.zero, animationTime).SetEase(Ease.OutExpo);// (EaseExponentialOut::create(MoveTo::create(animationTime, cocos2d::Point(_winSize.width/2, _winSize.height/2 + SPACING* 5))));



        {
            musicButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["music"] as string, () =>
            {



            });

            musicButtonStateLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;//, GAME_FONT, node.getMainLabel().getRenderingFontSize());


            if (PlayerPrefs.GetInt("MusicState", 1) == 1)
            {
                musicButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string);
            }
            else
            {
                musicButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string);
            }

            musicButton.SetMainFunc(() =>
            {

                if (PlayerPrefs.GetInt("MusicState", 1) == 1)
                {
                    musicButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string);


                    Globals.AllowBgMusic = false;
                    AudioManager.instance.SetMusicActive(true);
                    PlayerPrefs.SetInt("MusicState", 0);
                }
                else
                {
                    musicButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string);
                    ////child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);

                    Globals.AllowBgMusic = true;
                    AudioManager.instance.SetMusicActive(false);
                    if (Globals.AllowBgMusic)
                    {
                        //Globals.PlaySound("res/Sounds/BGM/menuBg.mp3", true, 0.5f);
                        
                        // AudioManager.instance.PlayMusic(Track.gamePlayMusic, false, 0.5f);
                        AudioManager.instance.PlayMusic(7008);
                    }
                    PlayerPrefs.SetInt("MusicState", 1);

                }


            });



            buttonsList.Add(musicButton);
            musicButton.SetChildColorEnabled(true);
            countNode++;


            musicButton.rectTransform.DOAnchorPos(new Vector2(0, -100), animationTime).SetEase(Ease.OutExpo);



            {
                soundButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["sound"] as string, () =>
                {


                });

                soundButtonStateLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;// GAME_FONT, node.getMainLabel().getRenderingFontSize()) ;
                soundButton.SetChildColorEnabled(true);

                if (PlayerPrefs.GetInt("SoundState", 1) == 1)
                {
                    soundButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string);
                }
                else
                {
                    soundButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string);
                }


                soundButton.SetMainFunc(() =>
                {

                    if (PlayerPrefs.GetInt("SoundState", 1) == 1)
                    {
                        soundButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string);

                        PlayerPrefs.SetInt("SoundState", 0);
                        Globals.AllowMusic = false;
                        AudioManager.instance.SetSoundEffectsActive(true);
                    }
                    else
                    {
                        soundButtonStateLabel.text = (GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string);
                        ////child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                        PlayerPrefs.SetInt("SoundState", 1);
                        Globals.AllowMusic = true;
                        AudioManager.instance.SetSoundEffectsActive(false);

                    }

                });


                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));
                buttonsList.Add(soundButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 + SPACING * 2 + SPACING / 2 + topMargin - (countNode * 400));

                soundButton.rectTransform.DOAnchorPos(new Vector2(0, -150), animationTime).SetEase(Ease.OutExpo);
            }

            {

                ////std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(FileHandler::getInstance().keyCodeBoost);
                ////std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                upButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["up"] as string, () =>
                {
                });
                ////Label* childNode = Label::createWithTTF(str, GAME_FONT, node.getMainLabel().getRenderingFontSize());


                upButton.SetChildColorEnabled(true);

                upButton.SetMainFunc(() =>
                {
                    //this.setEnable(false);

                    //MacMenu* mc = MacMenu::create();
                    //this.addChild(mc);
                    //mc.setOpacity(200);
                    //Label* lbl = Label::createWithTTF("ENTER KEY", GAME_FONT, 65);
                    //mc.addChild(lbl);
                    //Shared::fontToCustom(lbl);
                    //lbl.setPosition(_winSize.width / 2, _winSize.height / 2 + SPACING * 1.5f);


                    //auto keylistener = EventListenerKeyboard::create();
                    //keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){

                    //    int keyCodeInt = static_cast<int>(keyCode);
                    //    keylistener.setEnabled(false);
                    //    mc.addNewGameLabelAt(MenuOptionMac::createWithLabel("", ()=>{ }),cocos2d::Point::ZERO);

                    //    mc.runAction(Sequence::create(DelayTime::create(0.15f), CallFunc::create(()=>{

                    //        if (keyCodeInt != FileHandler::getInstance().keyCodeBoost
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeReverse
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeForward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeBackward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeSpecial)
                    //        {

                    //            g_KeycodeBoost = keyCode;

                    //            //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(keyCode);
                    //            //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                    //            childNode.setString(str);
                    //            //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);

                    //            FileHandler::getInstance().keyCodeBoost = keyCodeInt;
                    //            FileHandler::getInstance().SaveKeys();


                    //        }

                    //    }),RemoveSelf::create(), NULL));

                    //    this.setEnable(true);
                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);
                });

                //node.setPosition_winSize.width / 2, _winSize.height / 2 + SPACING - SPACING / 2);
                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));


                buttonsList.Add(upButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 + SPACING - SPACING / 2 + topMargin - (countNode * 400));


                upButton.rectTransform.DOAnchorPos(new Vector2(0, -250), animationTime).SetEase(Ease.OutExpo);

            }

            {

                //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(FileHandler::getInstance().keyCodeReverse);
                //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                downButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["down"] as string, () =>
                {
                });

                ////Label* childNode = Label::createWithTTF(str, GAME_FONT, node.getMainLabel().getRenderingFontSize());



                downButton.SetChildColorEnabled(true);
                downButton.SetMainFunc(() =>
                {
                    //this.setEnable(false);

                    //MacMenu* mc = MacMenu::create();
                    //this.addChild(mc);
                    //mc.setOpacity(200);
                    ////            GameLabel *lbl = GameLabel::create("ENTER KEY", 65);
                    //Label* lbl = Label::createWithTTF("ENTER KEY", GAME_FONT, 65);
                    //Shared::fontToCustom(lbl);
                    //mc.addChild(lbl);
                    //lbl.setPosition(_winSize.width / 2, _winSize.height / 2 + SPACING * 1.5f);

                    //mc.addNewGameLabelAt(MenuOptionMac::createWithLabel("", ()=>{ }),cocos2d::Point::ZERO);
                    //auto keylistener = EventListenerKeyboard::create();
                    //keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){

                    //    int keyCodeInt = static_cast<int>(keyCode);

                    //    keylistener.setEnabled(false);

                    //    mc.runAction(Sequence::create(DelayTime::create(0.15f), CallFunc::create(()=>{

                    //        if (keyCodeInt != FileHandler::getInstance().keyCodeBoost
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeReverse
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeForward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeBackward
                    //            && keyCodeInt != FileHandler::getInstance().keyCodeSpecial)
                    //        {
                    //            g_KeycodeReverse = keyCode;
                    //            //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(keyCode);
                    //            //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                    //            childNode.setString(str);
                    //            //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                    //            FileHandler::getInstance().keyCodeReverse = keyCodeInt;
                    //            FileHandler::getInstance().SaveKeys();

                    //        }

                    //    }),RemoveSelf::create(), NULL));

                    //    this.setEnable(true);
                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

                    //auto mouseListener = EventListenerMouse::create();
                    //mouseListener.onMouseDown = [=](EventMouse *event){

                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
                });

                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING / 2);


                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));

                buttonsList.Add(downButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 - SPACING / 2 + topMargin - (countNode * 400));

                downButton.rectTransform.DOAnchorPos(new Vector2(0, -300), animationTime).SetEase(Ease.OutExpo);

            }

            {

                //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(FileHandler::getInstance().keyCodeBackward);
                //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                leftButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["left"] as string, () =>
                {
                });

                //Label* childNode = Label::createWithTTF(str, GAME_FONT, node.getMainLabel().getRenderingFontSize());



                leftButton.SetChildColorEnabled(true);


                leftButton.SetMainFunc(() =>
                {
                    //this.setEnable(false);

                    //MacMenu* mc = MacMenu::create();
                    //this.addChild(mc);
                    //mc.setOpacity(200);
                    ////GameLabel *lbl = GameLabel::create("ENTER KEY", 65);
                    //Label* lbl = Label::createWithTTF("ENTER KEY", GAME_FONT, 65);
                    //Shared::fontToCustom(lbl);
                    //mc.addChild(lbl);
                    //lbl.setPosition(_winSize.width / 2, _winSize.height / 2 + SPACING * 1.5f);

                    //mc.addNewGameLabelAt(MenuOptionMac::createWithLabel("", ()=>{ }),cocos2d::Point::ZERO);
                    //auto keylistener = EventListenerKeyboard::create();
                    //keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){

                    //    int keyCodeInt = static_cast<int>(keyCode);

                    //    keylistener.setEnabled(false);

                    //    mc.runAction(Sequence::create(DelayTime::create(0.15f), CallFunc::create(()=>{

                    //        if (keyCodeInt != FileHandler::getInstance().keyCodeBoost
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeReverse
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeForward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeBackward
                    //            && keyCodeInt != FileHandler::getInstance().keyCodeSpecial)
                    //        {
                    //            g_KeycodeReverse = keyCode;
                    //            //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(keyCode);
                    //            //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                    //            childNode.setString(str);
                    //            //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                    //            FileHandler::getInstance().keyCodeBackward = keyCodeInt;
                    //            FileHandler::getInstance().SaveKeys();

                    //        }

                    //    }),RemoveSelf::create(), NULL));

                    //    this.setEnable(true);
                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

                    //auto mouseListener = EventListenerMouse::create();
                    //mouseListener.onMouseDown = [=](EventMouse *event){

                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
                });

                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING - SPACING / 2);


                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));

                buttonsList.Add(leftButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 - SPACING - SPACING / 2 + topMargin - (countNode * 400));

                leftButton.rectTransform.DOAnchorPos(new Vector2(0, -350), animationTime).SetEase(Ease.OutExpo);


            }


            {

                //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(FileHandler::getInstance().keyCodeForward);
                //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                rightButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["right"] as string, () =>
                {
                });

                //Label* childNode = Label::createWithTTF(str, GAME_FONT, node.getMainLabel().getRenderingFontSize());



                leftButton.SetChildColorEnabled(true);
                leftButton.SetMainFunc(() =>
                {
                    //this.setEnable(false);

                    //MacMenu* mc = MacMenu::create();
                    //this.addChild(mc);
                    //mc.setOpacity(200);
                    //// GameLabel *lbl = GameLabel::create("ENTER KEY", 65);
                    //Label* lbl = Label::createWithTTF("ENTER KEY", GAME_FONT, 65);
                    //Shared::fontToCustom(lbl);
                    //mc.addChild(lbl);
                    //lbl.setPosition(_winSize.width / 2, _winSize.height / 2 + SPACING * 1.5f);

                    //mc.addNewGameLabelAt(MenuOptionMac::createWithLabel("", ()=>{ }),cocos2d::Point::ZERO);
                    //auto keylistener = EventListenerKeyboard::create();
                    //keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){

                    //    int keyCodeInt = static_cast<int>(keyCode);

                    //    keylistener.setEnabled(false);

                    //    mc.runAction(Sequence::create(DelayTime::create(0.15f), CallFunc::create(()=>{

                    //        if (keyCodeInt != FileHandler::getInstance().keyCodeBoost
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeReverse
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeForward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeBackward
                    //            && keyCodeInt != FileHandler::getInstance().keyCodeSpecial)
                    //        {
                    //            g_KeycodeReverse = keyCode;
                    //            //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(keyCode);
                    //            //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                    //            childNode.setString(str);
                    //            //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                    //            FileHandler::getInstance().keyCodeForward = keyCodeInt;
                    //            FileHandler::getInstance().SaveKeys();

                    //        }

                    //    }),RemoveSelf::create(), NULL));

                    //    this.setEnable(true);
                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

                    //auto mouseListener = EventListenerMouse::create();
                    //mouseListener.onMouseDown = [=](EventMouse *event){

                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
                });

                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING * 2 - SPACING / 2);


                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));

                buttonsList.Add(rightButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 - SPACING * 2 - SPACING / 2 + topMargin - (countNode * 400));


                rightButton.rectTransform.DOAnchorPos(new Vector2(0, -400), animationTime).SetEase(Ease.OutExpo);

            }
            {

                //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(FileHandler::getInstance().keyCodeSpecial);
                //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                specialButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["special"] as string, () =>
                {
                });

                //Label* childNode = Label::createWithTTF(str, GAME_FONT, node.getMainLabel().getRenderingFontSize());



                specialButton.SetChildColorEnabled(true);
                specialButton.SetMainFunc(() =>
                {
                    //this.setEnable(false);

                    //MacMenu* mc = MacMenu::create();
                    //this.addChild(mc);
                    //mc.setOpacity(200);
                    ////GameLabel *lbl = GameLabel::create("ENTER KEY", 65);
                    //Label* lbl = Label::createWithTTF("ENTER KEY", GAME_FONT, 65);
                    //Shared::fontToCustom(lbl);
                    //mc.addChild(lbl);
                    //lbl.setPosition(_winSize.width / 2, _winSize.height / 2 + SPACING * 1.5f);

                    //mc.addNewGameLabelAt(MenuOptionMac::createWithLabel("", ()=>{ }),cocos2d::Point::ZERO);
                    //auto keylistener = EventListenerKeyboard::create();
                    //keylistener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event){
                    //    int keyCodeInt = static_cast<int>(keyCode);


                    //    keylistener.setEnabled(false);

                    //    mc.runAction(Sequence::create(DelayTime::create(0.15f), CallFunc::create(()=>{

                    //        if (keyCodeInt != FileHandler::getInstance().keyCodeBoost
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeReverse
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeForward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeBackward
                    //           && keyCodeInt != FileHandler::getInstance().keyCodeSpecial)
                    //        {
                    //            g_KeycodeSpecial = keyCode;

                    //            //std::string str = KeyBoardMap::getInstance().getKeyCodeInStr(keyCode);
                    //            //std::transform(str.begin(), str.end(), str.begin(), ::toupper);
                    //            childNode.setString(str);
                    //            //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                    //            FileHandler::getInstance().keyCodeSpecial = keyCodeInt;
                    //            FileHandler::getInstance().SaveKeys();

                    //        }

                    //    }),RemoveSelf::create(), NULL));

                    //    this.setEnable(true);
                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

                    //auto mouseListener = EventListenerMouse::create();
                    //mouseListener.onMouseDown = [=](EventMouse *event){

                    //};

                    //_eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
                });

                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING * 3 - SPACING / 2);

                ////node.setPositionX(_winSize.width / 2);
                //child//node.setPositionTEXT_CONTENT_SIZE - childNode.getContentSize().width / 2, node.getMainLabel().getContentSize().height / 2);
                //node.getMainLabel().setContentSize(Size(TEXT_CONTENT_SIZE, node.getMainLabel().getContentSize().height));


                buttonsList.Add(specialButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 - SPACING * 3 - SPACING / 2 + topMargin - (countNode * 400));


                specialButton.rectTransform.DOAnchorPos(new Vector2(0, -450), animationTime).SetEase(Ease.OutExpo);


            }
            {
                resetButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["reset"] as string, () =>
                {


                    popUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.MAIN_MENU)["back"] as string, " ALL PROGRESS WILL BE LOST,\n ARE YOU SURE YOU WANT TO RESET?", () =>
                    {
                        Globals.canRegenerateHealth = true;
                        Globals.introComplete = false;
                        Globals.skipFTUX = false;
                        Globals.ftuxSidekicksSpawned = false;
                        Globals.specialAbilityUsedInTutorial = false;
                        Globals.specialAbilityTutorialExecuted = false;
                        Globals.tutorialOrbsDropState = false;
                        Globals.canActivateSpecialOnControllerTutorial = false;

                        //FileHandler::getInstance().EmptyData();
                        //FileHandler::getInstance().readData();
                        //FileHandler::getInstance().saveData();
                        //#if CC_TARGET_PLATFORM == CC_PLATFORM_MAC
                        //            PlayerPrefs.SetInt("firstLaunch", true);
                        //#endif
                        GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));
                        Globals.StopAllSounds();
                        SceneManager.LoadScene("Splash");
                    });

                });



                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING * 4 - SPACING / 2);
                buttonsList.Add(resetButton);
                countNode++;
                //node.setPositionY(_winSize.height / 2 - SPACING * 4 - SPACING / 2 + topMargin - (countNode * 400));


                resetButton.rectTransform.DOAnchorPos(new Vector2(0, -500), animationTime).SetEase(Ease.OutExpo);




            }

            {
                backButton.InitVariables(GameData.instance.GetMenuData(Globals.MAIN_MENU)["back"] as string, () =>
                {

                    //                Director::getInstance().replaceScene(TransitionProgressVertical::create(0.05f,  MainMenuScene::createScene()));
                    SetEnable(false);
                    optionsHeading.GetComponent<RectTransform>().DOAnchorPosY(optionsHeading.GetComponent<RectTransform>().anchoredPosition.y+5500, animationTime/1.5f).SetEase(Ease.InExpo);
                    for (int i = 1; i <= buttonsList.Count; i++)
                    {
                       seq = DOTween.Sequence().AppendInterval(i * 0.03f).Append(buttonsList[i-1].rectTransform.DOAnchorPos(new Vector2(0, buttonsList[i-1].rectTransform.anchoredPosition.y + 5500), animationTime / 1.5f).SetEase(Ease.InExpo)).Play();
                    }


                    DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() =>
                    {
                        Observer.DispatchCustomEvent("backFromOptions");
                        menuAnimation.AnimationState.SetAnimation(0, "OptionsToMenu", false);
                        menuAnimation.AnimationState.AddAnimation(0, "idle", true);

                        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.whoosh2);

                        //Globals.PlaySound("res/Sounds/SFX/whoosh2.mp3");

                    }).AppendInterval(0.8f).AppendCallback(() => { SetEnable(false); seq.Kill(); }).Play();

                });

                AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.whoosh2);

                //Globals.PlaySound("res/Sounds/SFX/whoosh2.mp3");

                exitIndex = buttonsList.Count;

                //node.setPosition_winSize.width / 2, _winSize.height / 2 - SPACING * 5 - SPACING / 2);
                buttonsList.Add(backButton);
                countNode++;

                //node.setPositionY(_winSize.height / 2 - SPACING * 5 - SPACING / 2 + topMargin - (countNode * 400));
                backButton.rectTransform.DOAnchorPos(new Vector2(0, -550), animationTime).SetEase(Ease.OutExpo);


            }

            Init();

            UpdateSelected();
        }
    }
}
