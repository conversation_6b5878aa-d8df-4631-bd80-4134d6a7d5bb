using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Civilian : Enemy
{
    [SerializeField] Collider2D boundsCollider;
    [SerializeField] Image timerImage;
    //cocos2d::ProgressTimer* pTimer;
    PlayerPing ping;
    Bounds bounds;
    bool isComplete = false;

    private IEnumerator NextFrameGetBounds()
    {
        yield return null;
        bounds = boundsCollider.bounds;
    }
    public override void Init()
    {
        if (initialized)
            return;
        base.Init();

        StartCoroutine(NextFrameGetBounds());

        enemySprite.state.SetAnimation(0, "help", true);
        GameManager.instance.missionManager.placementCounter++;
        transform.position = new Vector3(Globals.CocosToUnity(3500)
            + GameManager.instance.missionManager.placementCounter * Globals.CocosToUnity(2000), Globals.LOWERBOUNDARY, 0);

        // TODO
        //this->setAllowPing(true, Enemy::PINGTYPE::MISSION_AHEAD);
        timerImage.color = Color.yellow;

        timerImage.fillAmount = 0;

        stats = new Attributes();
        baseStats = new Attributes();

        stats.health = GameData.instance.fileHandler.currentMission;
        stats.maxHealth.Value = stats.health;

        baseStats.health = GameData.instance.fileHandler.currentMission;
        baseStats.maxHealth.Value = baseStats.health;

        scheduleUpdate = true;


        ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true);


    }

    void Update()
    {
        if (!scheduleUpdate)
            return;

        bounds = boundsCollider.bounds;

        if (timerImage.fillAmount >= 1)
        {
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.levelUp);
            //Shared::playSound("res/Sounds/SFX/levelUp.mp3");
            timerImage.gameObject.SetActive(false);
            enemySprite.state.SetAnimation(0, "rescued", false);
            scheduleUpdate = false;
            //this->setAllowPing(false, PINGTYPE::MISSION_AHEAD); TODO
            GameManager.instance.missionManager.AddPoint();

            isComplete = true;
        }
        else
        {
            //暂时屏蔽不回血
            //timerImage.fillAmount -= 0.07f * Time.deltaTime;
            //timerImage.fillAmount = Mathf.Clamp(timerImage.fillAmount, 0, 1);
        }
    }

    public override bool TakeHit(double damage)
    {
        //damage *= 0.075f; // TODO REMOVE
        //timerImage.fillAmount += damage / (stats.maxHealth);

        return false;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (bounds == null) return false;
        return bounds.Contains(P1);
    }

    public void EnablePlayerPing()
    {
        // TODO
        //ping = PlayerPing::createWithPingNode(enemySprite);
        //ping->setPingTexture("res/GameHud/powerUpAlert.png");
        //ping->_pingBg->setVisible(false);
        //this->addChild(ping);
        //ping->setRotationEnabled(true);

    }
}
