﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class Shop : MonoBehaviour
{
    public enum TabType { FrontGuns = 0, AltGuns = 1, Planes = 2, Sidekicks = 3 }
    TabType currentTab;

    [SerializeField] PlayerShop player;
    [SerializeField] NavigationButton defaultNavButton;
    [SerializeField] RectTransform shopBG, shopScreenRect;
    [SerializeField] Menu[] shopmenus;
    [SerializeField] Button[] tabs;
    [SerializeField] Text panelText;
    [SerializeField] RectTransform planeInfoDialogue, lockInfoDialogue, canvasRect;
    [SerializeField] TextMeshProUGUI coinsTMP, planeInfoDialogueTMP, lockInfoDialogueTMP;
    [SerializeField] Button arrowButton;
    [SerializeField] GameObject[] notificationGameobjects;
    [SerializeField] TextMeshProUGUI[] notificationTMPs;
    [SerializeField] ShopPanel rearGunsPanel, planesPanel;
    [SerializeField] SidekicksPanel sidekicksPanel;
    [SerializeField] string[] panelTexts;
    [SerializeField] Vector2 bgSize;

    [HideInInspector] public bool isTutorial = false;

    public TabType GetCurrentTab { get { return currentTab; } }
    public Vector3 GetPlayerPosition { get { return player.transform.position; } }
    public PlayerShop Player { get { return player; } }

    private void OnEnable()
    {
        // AudioManager.instance.PlayMusic(Track.shopMusic);
        AudioManager.instance.PlayMusic(7012);
    }

    private void OnDisable()
    {
        if (player == null)
            return;

        player.StopShooting();
        player.gameObject.SetActive(false);
    }

    private void Start()
    {
        StartCoroutine(CallSetNotifications());
    }

    IEnumerator CallSetNotifications()
    {
        yield return new WaitForEndOfFrame();

        SetNotifications();
    }

    public void Init()
    {
        MainMenuController.instance.SetSize(shopBG, bgSize);
        MainMenuController.instance.SetScreenSize(shopScreenRect);

        player.Init();
        ChangeTab(0);
        InitShopPanels();

        player.gameObject.SetActive(true);
        player.StartShooting();
        UpdateCoins();

        NavigationButton.currentlySelected = defaultNavButton;
        NavigationButton.ChangeCurrentlySelected(defaultNavButton, true);
        if (Globals.shopTutorial)
        {
            Globals.shopTutorial = false;
            isTutorial = true;

            defaultNavButton.onUpSelect = null;
            defaultNavButton.onDownSelect = null;
            defaultNavButton.onBackSelect = null;

            arrowButton.interactable = false;
            arrowButton.GetComponent<NavigationButton>().enabled = false;

            shopmenus[0].GetComponent<ShopPanel>().DisableButtons(0);

            for (int i = 0; i < 4; i++)
            {
                tabs[i].image.raycastTarget = false;
                tabs[i].GetComponent<NavigationButton>().enabled = false;
                tabs[i].GetComponent<GraphicRaycaster>().enabled = false;
            }
        }
    }

    void SetNotifications()
    {
        int rearGunsNotifications = rearGunsPanel.CheckUnlockedItems();
        int planesNotifications = planesPanel.CheckUnlockedItems();
        int sidekickNotifications = sidekicksPanel.CheckUnlockedSidekicks();

        notificationGameobjects[0].SetActive(false);
        notificationGameobjects[1].SetActive(rearGunsNotifications > 0);
        notificationGameobjects[2].SetActive(planesNotifications > 0);
        notificationGameobjects[3].SetActive(sidekickNotifications > 0);

        notificationTMPs[0].text = 0.ToString();
        notificationTMPs[1].text = rearGunsNotifications.ToString();
        notificationTMPs[2].text = planesNotifications.ToString();
        notificationTMPs[3].text = sidekickNotifications.ToString();
    }

    public void ChangeTab(int tabNum)
    {
        if (isTutorial)
            return;

        ChangeCurrentTab((TabType)tabNum);

        ResetFrontGun();
        ResetPlane();
        ResetRearGun();

        if(tabNum > 0)
            notificationGameobjects[tabNum].SetActive(false);

        //if(tabNum == 3)
        //{
        //    player.StopShooting();
        //    player.gameObject.SetActive(false);
        //}
    }

    public void StopPlayerShoot()
    {
        player.StopShooting();
        player.gameObject.SetActive(false);
    }

    void ChangeCurrentTab(TabType tab)
    {
        tabs[(int)currentTab].interactable = true;
        tabs[(int)currentTab].GetComponent<Canvas>().sortingOrder = 9;
        shopmenus[(int)currentTab].ClosePanel();

        currentTab = tab;

        tabs[(int)currentTab].interactable = false;
        tabs[(int)currentTab].GetComponent<Canvas>().sortingOrder = 11;
        shopmenus[(int)currentTab].GetComponent<ShopPanel>().ResetShopPanel();
        shopmenus[(int)currentTab].OpenPanel();

        panelText.text = panelTexts[(int)currentTab];
    }

    void InitShopPanels()
    {
        shopmenus[0].OpenImmediately();
        shopmenus[1].CloseImmediately();
        shopmenus[2].CloseImmediately();
        shopmenus[3].CloseImmediately();
    }

    public void ResetFrontGun() => player.ResetFrontGun();
    public void SetFrontGun(int index) => player.SetFrontGun(index);

    public void ResetPlane() => player.ResetPlane();
    public void SetPlane(int index) => player.SetPlane(index);

    public void ResetRearGun() => player.ResetRearGun();
    public void SetRearGun(int index) => player.SetRearGun(index);

    public void ShowInfoDialogue(bool show, bool forLock, RectTransform itemRect = null, string info = "", Vector2? offset = null)
    {
        if (offset == null)
            offset = Vector2.zero;

        if (forLock)
        {
            lockInfoDialogueTMP.text = info;
            lockInfoDialogue.gameObject.SetActive(show);
            if(itemRect)
            {
                lockInfoDialogue.anchoredPosition = canvasRect.InverseTransformPoint(itemRect.position);
                lockInfoDialogue.anchoredPosition += (Vector2)offset;
            }
        }
        else
        {
            planeInfoDialogueTMP.text = info;
            planeInfoDialogue.gameObject.SetActive(show);
            if (itemRect)
            {
                planeInfoDialogue.anchoredPosition = canvasRect.InverseTransformPoint(itemRect.position);
                planeInfoDialogue.anchoredPosition += (Vector2)offset;
            }
        }
    }

    public void SelectItem(int itemIndex)
    {
        if ((int)currentTab == 0)
            player.SelectFrontGun(itemIndex);
        else if ((int)currentTab == 1)
            player.SelectRearGun(itemIndex);
        else if ((int)currentTab == 2)
            player.SelectPlane(itemIndex);
    }

    public void UpdateCoins()
    {
        coinsTMP.text = GameData.instance.fileHandler.coins.ToString();
    }

    public bool CheckEquippedItem(int itemIndex) => player.CheckEquippedItem((int)currentTab, itemIndex);
}
