using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
using ViewModel;
public class Explosions : MonoBehaviour
{

    bool firstTime = false;

    public enum ExplosionType
    {
        ExplosionTypeAboveWater,
        ExplosionTypeAir1,
        ExplosionTypeAir2,
        ExplosionTypePlayer,
        ExplosionTypeUnderWater,
        ExplosionTypeBuildingWithoutWater,
        ExplosionTypeBuilding,
        ExplosionTypeBuildingMission,
        ExplosionTypeBoss,
        ExplosionTypeEnemyFall,
        ExplosionTypeShip,
        ExplosionTypeAoe,
        ExplosionTypeRandom,
        ExplosionTypeWXREnemy1,
        ExplosionTypeWXREnemy2,
        ExplosionTypeWXREnemy3,
        WXRExplosionAttack,
    };

    public void GenerateParticlesAt(ExplosionType expType, Vector3 point, bool rubble, int numberOfExplosions,
        float scale, float varDistance)
    {

        if (expType == ExplosionType.ExplosionTypeAir1)
        {
            for (int i = 0; i < numberOfExplosions; i++)
            {
                Explosion explosion = null;

                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                float newScale = Random.value * 0.5f + scale;
                explosion.transform.localScale = new Vector2(newScale, newScale);

                explosion.skeletonAnimation.state.TimeScale = 1;
                explosion.transform.rotation = Quaternion.Euler(0, 0, Random.value * 360);
                explosion.transform.position = new Vector2(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
                //敌人爆炸震动的概率
                //if(GameData.instance.fileHandler.currentMission != 0)
                //{
                //    GameManager.instance.ShakeCamera(1, 3);
                //}
                StartCoroutine(PlayExplosion(explosion, "explosion" + Random.Range(1, 3).ToString(), 0.2f * i, 1.5f, expType));
            }
        }
        if (expType == ExplosionType.ExplosionTypeAir2)
        {
            for (int i = 0; i < numberOfExplosions; i++)
            {
                Explosion explosion = null;

                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }

                float eScale = Random.Range(0f, 1f) + scale / 2;
                explosion.transform.localScale = new Vector3(eScale, eScale, 1);
                explosion.transform.rotation = Quaternion.Euler(new Vector3(0, 0, Random.Range(0f, 1f) * 360));

                explosion.transform.position = new Vector2(point.x + Random.Range(0f, 1f) * varDistance - varDistance / 2,
                    point.y + Random.Range(0f, 1f) * varDistance - varDistance / 2);

                StartCoroutine(PlayExplosion(explosion, "explosion" + Random.Range(4, 6).ToString(), 0.2f * i, 1.5f, expType));
            }
        }
        if (expType == ExplosionType.ExplosionTypeBuilding)
        {
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.splashFast, 0.5f);

            //Globals.PlaySound("res/Sounds/SFX/waterSplash/splashFast.mp3", false, 0.5f);
            Explosion waterExplosion = null;

            bool didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.waterExplosionsList)
            {
                if (!exp.IsInUse)
                {
                    waterExplosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }// waterExplosion = SkeletonAnimation::createWithJsonFile("res/Explosions/waterSplash.json", "res/Explosions/waterSplash.atlas", 0.5);

            waterExplosion.skeletonAnimation.state.TimeScale = 0.8f;
            waterExplosion.transform.localScale = new Vector2(7 * scale, 7 * scale);
            waterExplosion.skeletonAnimation.GetComponent<MeshRenderer>().sortingOrder = waterExplosion.defaultOrderInLayer - 1;

            //this.addChild(waterExplosion, 2);
            waterExplosion.transform.position = new Vector2(point.x + 0.10f, -0.5f + Globals.LOWERBOUNDARY - (0.70f * scale));
            //waterExplosion.setCameraMask(GAMECAMERA);
            int var = Globals.g_bgType;
            if (Globals.g_bgType > 7)
            {
                var /= 2;
            }
            if (Globals.g_bgType == 7)
            {
                var = 2;
            }
            string exptype = "bg" + var;
            //char ch[32];
            //sprintf(ch, "bg%d", var);
            StartCoroutine(PlayExplosion(waterExplosion, exptype, 0, 0.5f, expType));
            //explosion.skeletonAnimation.state.SetAnimation(0, "bg1", false);
            //waterExplosion.runAction(Sequence::create(DelayTime::create(0.5), RemoveSelf::create(), NULL));
            Explosion explosion = null;

            didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.explosionsList)
            {
                if (!exp.IsInUse)
                {
                    explosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }
            explosion.transform.localScale = new Vector2(5f * scale, 5f * scale);
            explosion.skeletonAnimation.state.TimeScale = 0.75f;
            explosion.transform.position = new Vector2(point.x, Globals.LOWERBOUNDARY + (0.60f * scale));
            StartCoroutine(PlayExplosion(explosion, "explosion3", 0.15f, 3f, expType));
        }

        if (expType == ExplosionType.ExplosionTypeAoe)
        {
            Explosion explosion = null;

            bool didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.explosionsList)
            {
                if (!exp.IsInUse)
                {
                    explosion = exp;
                    didFindExplosion = true;
                    break;
                }
            }
            if (!didFindExplosion)
            {
                return;
            }
            explosion.skeletonAnimation.state.TimeScale = 1;
            explosion.transform.position = new Vector2(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
            explosion.transform.DOScale(new Vector2(scale, scale), 0.25f);
            StartCoroutine(PlayExplosion(explosion, "explosion8", 0f, 1.5f, expType));
        }
        if (expType == ExplosionType.ExplosionTypeBuildingMission)
        {
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.splashFast, 0.5f);

            //Globals.PlaySound("res/Sounds/SFX/waterSplash/splashFast.mp3", false, 0.5f);
            Explosion waterExplosion = null;

            bool didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.waterExplosionsList)
            {
                if (!exp.IsInUse)
                {
                    waterExplosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }
            waterExplosion.skeletonAnimation.state.TimeScale = 0.8f;
            waterExplosion.transform.localScale = new Vector2(14 * scale, 14 * scale); //Reduced from 14

            waterExplosion.transform.position = new Vector2(point.x + Globals.CocosToUnity(10), Globals.CocosToUnity(-5) + Globals.LOWERBOUNDARY - (Globals.CocosToUnity(70) * scale));

            int var = Globals.g_bgType;
            if (Globals.g_bgType > 7)
            {
                var /= 2;
            }
            if (var == 7)
            {
                var = 2;
            }
            StartCoroutine(PlayExplosion(waterExplosion, "bg" + var, 0, 0.5f, expType));

            for (int i = 0; i < 7; i++)
            {
                StartCoroutine(CreateWithExplosion(i * 0.15f, ExplosionType.ExplosionTypeAir2, new Vector3(point.x, Globals.LOWERBOUNDARY + (Globals.CocosToUnity(60) * scale * 3) + (i * Globals.CocosToUnity(200) * scale)), rubble, numberOfExplosions, scale * 5f, varDistance)); //scale loowered from 5f
            }

            Explosion explosion = null;
            didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.explosionsList)
            {
                if (!exp.IsInUse)
                {
                    explosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }
            explosion.transform.localScale = new Vector2(5f * scale, 5f * scale); //Scale lowered from 5f
            explosion.skeletonAnimation.state.TimeScale = 0.75f;
            explosion.transform.position = new Vector2(point.x, Globals.LOWERBOUNDARY + (Globals.CocosToUnity(60) * scale));
            StartCoroutine(PlayExplosion(explosion, "explosion3", 0.15f, 3f, expType));
            StartCoroutine(CreateWithExplosion(0.4f, ExplosionType.ExplosionTypeBuildingWithoutWater, point, rubble, numberOfExplosions, scale * 1.35f, varDistance,
                () =>
                {
                    for (int i = 0; i < 4; i++)
                    {

                        StartCoroutine(CreateWithExplosion(i * 0.15f, ExplosionType.ExplosionTypeAir2, new Vector2(point.x - Globals.CocosToUnity(500) * scale, Globals.LOWERBOUNDARY + (Globals.CocosToUnity(60) * scale * Globals.CocosToUnity(3)) + (i * Globals.CocosToUnity(200) * scale)), rubble, numberOfExplosions, scale * 5f, varDistance)); // scale lowered from5

                    }
                    for (int i = 0; i < 4; i++)
                    {

                        StartCoroutine(CreateWithExplosion(i * 0.15f, ExplosionType.ExplosionTypeAir2, new Vector2(point.x + Globals.CocosToUnity(500) * scale, Globals.LOWERBOUNDARY + (Globals.CocosToUnity(60) * scale * Globals.CocosToUnity(3)) + (i * Globals.CocosToUnity(200) * scale)), rubble, numberOfExplosions, scale * 5f, varDistance));// scale lowered from5

                    }
                }));
            StartCoroutine(CreateWithExplosion(0.55f, ExplosionType.ExplosionTypeBuildingWithoutWater, point, rubble, numberOfExplosions, scale < 1.0f ? scale * 2.0f : scale * 1.5f, varDistance));
            for (int i = 0; i < 5; i++)
            {
                StartCoroutine(CreateDebris(3, 600, 7, i * 0.1f, point));
            }
        }
        else if (expType == ExplosionType.ExplosionTypeBuildingWithoutWater)
        {
            AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.splashFast, 0.5f);

            //Globals.PlaySound("res/Sounds/SFX/waterSplash/splashFast.mp3", false, 0.5f);
            Explosion waterExplosion = null;

            bool didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.waterExplosionsList)
            {
                if (!exp.IsInUse)
                {
                    waterExplosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }
            waterExplosion.skeletonAnimation.state.TimeScale = 0.8f;
            waterExplosion.transform.localScale = new Vector2(5f * scale, 5f * scale);//Lowered from 5
            waterExplosion.transform.position = new Vector2(point.x + Globals.CocosToUnity(10), Globals.CocosToUnity(-5) + Globals.LOWERBOUNDARY - (Globals.CocosToUnity(100) * scale));

            int var = Globals.g_bgType; //TODO
            if (Globals.g_bgType > 7)
            {
                var /= 2;
            }
            if (Globals.g_bgType == 7)
            {
                var = 2;
            }

            StartCoroutine(PlayExplosion(waterExplosion, "bg" + var, 0, 0.5f, expType));

            Explosion explosion = null;
            didFindExplosion = false;
            foreach (Explosion exp in GameSharedData.Instance.explosionsList)
            {
                if (!exp.IsInUse)
                {
                    explosion = exp;
                    didFindExplosion = true;
                    break;
                }

            }
            if (!didFindExplosion)
            {
                return;
            }
            explosion.transform.localScale = new Vector2(3.5f * scale, 3.5f * scale); //Lowered from 3.5f
            explosion.skeletonAnimation.state.TimeScale = 0.6f;
            explosion.transform.position = new Vector2(point.x, Globals.LOWERBOUNDARY + (Globals.CocosToUnity(60) * scale));
            StartCoroutine(PlayExplosion(explosion, "explosion3", 0.15f, 3f, expType));
        }
        else if (expType == ExplosionType.ExplosionTypeBoss)
        {
            //GameSharedData::getInstance().bgColorFade = 1; TODO
            //Globals.SetZoomValueWhileGame(Globals.CocosToUnity(800));
            for (int i = 0; i < 16; i++)
            {
                Explosion explosion = null;
                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                //explosion.setVisible(false);
                explosion.transform.SetScale(Random.value + scale * 2.5f);

                explosion.skeletonAnimation.state.TimeScale = 1;
                varDistance = Globals.CocosToUnity(300);
                explosion.transform.SetWorldPosition(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
                explosion.transform.SetRotation(Random.value * 360);
                float rnd = Random.value;
                string animName;
                if (rnd < 0.33f)
                {
                    animName = "explosion2";
                }
                else if (rnd < 0.66f)
                {
                    animName = "explosion1";

                }
                else
                {
                    animName = "explosion6";

                }
                StartCoroutine(PlayExplosion(explosion, animName, i * 0.15f, 1.5f, expType));

            }



            for (int i = 0; i < 5; i++)
            {

                Explosion explosion = null;
                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.specialExplosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                explosion.transform.position = point;
                explosion.transform.SetScale(5 + Random.value * 3);
                StartCoroutine(PlayExplosion(explosion, "powerUp3", i * 0.15f, 1.5f, expType));


            }

            StartCoroutine(CreateDebris(8, 600, 7, 0.2f, point));
        }
        else if (expType == ExplosionType.ExplosionTypeShip)
        {



            for (int j = 0; j < numberOfExplosions; j++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;
                debris.CreateWithData(600, 6, true);
                debris.transform.position = new Vector2(point.x, point.y + 0.5f);
            }



            for (int i = 0; i < 1; i++)
            {
                Explosion explosion = null;
                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                explosion.transform.SetScale(Random.value + scale * 2);

                explosion.skeletonAnimation.state.TimeScale = 1;
                explosion.transform.position = point;
                StartCoroutine(PlayExplosion(explosion, "explosion7", i * 0.15f, 1.5f, expType));

            }


        }

        else if (expType == ExplosionType.ExplosionTypeEnemyFall)
        {
            for (int i = 0; i < 2; i++)
            {
                Explosion explosion = null;
                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                explosion.transform.SetScale(Random.value + scale * 2);
                explosion.transform.SetRotation(Random.value * 360);

                explosion.skeletonAnimation.state.TimeScale = 1;
                explosion.transform.SetWorldPosition(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);

                StartCoroutine(PlayExplosion(explosion, "explosion" + Random.Range(4, 6).ToString(), i * 0.4f, 1.5f, expType));

            }

            for (int i = 0; i < 1; i++)
            {
                CreateDebris(3, 400, 5, i * 0.1f, point);
            }

            for (int j = 0; j < 1 + Random.Range(0, 3); j++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;
                debris.CreateWithData(400, 4, false, true);
                debris.transform.position = point;
            }
        }
        else if (expType == ExplosionType.ExplosionTypePlayer)
        {
            for (int i = 0; i < 1; i++)
            {
                Explosion explosion = null;
                bool didFindExplosion = false;
                foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                {
                    if (!exp.IsInUse)
                    {
                        explosion = exp;
                        didFindExplosion = true;
                        break;
                    }

                }
                if (!didFindExplosion)
                {
                    return;
                }
                explosion.gameObject.SetActive(false);
                explosion.transform.SetScale(scale);

                explosion.skeletonAnimation.state.TimeScale = 1;
                explosion.transform.SetRotation(Random.value * 360);
                explosion.transform.SetWorldPosition(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
                StartCoroutine(PlayExplosion(explosion, "explosion" + Random.Range(4, 6).ToString(), i * 0.2f, 1f, expType));
            }
        }
        else if (expType == ExplosionType.ExplosionTypeWXREnemy1 || expType == ExplosionType.ExplosionTypeWXREnemy2|| expType == ExplosionType.ExplosionTypeWXREnemy3)
        {
            DefaultGameObjectType dgo = DefaultGameObjectType.ExplosionTypeWXREnemy1;
            if (expType == ExplosionType.ExplosionTypeWXREnemy2) dgo = DefaultGameObjectType.ExplosionTypeWXREnemy2;
            if (expType == ExplosionType.ExplosionTypeWXREnemy3) dgo = DefaultGameObjectType.ExplosionTypeWXREnemy3;
            for (int i = 0; i < numberOfExplosions; i++)
            {
                bool didFindExplosion = false;
                GameObject exp = GameSharedData.Instance.GetPrefabByType(dgo);
                WXR_Explosion explosion = exp.GetComponent<WXR_Explosion>();
                if (!explosion.IsInUse)
                {
                    didFindExplosion = true;
                }
                //foreach (Explosion exp in GameSharedData.Instance.explosionsList)
                //{
                //    if (!exp.IsInUse)
                //    {
                //        explosion = exp;
                //        didFindExplosion = true;
                //        break;
                //    }

                //}
                if (!didFindExplosion)
                {
                    return;
                }
                explosion.gameObject.SetActive(false);
                explosion.transform.SetScale(scale);
                explosion.transform.SetRotation(Random.value * 360);
                explosion.transform.SetWorldPosition(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
                StartCoroutine(PlayWXRExplosion(explosion, "explosion" + Random.Range(4, 6).ToString(), i * 0.2f, 1f, expType));
            }
        }
    }

    private IEnumerator CreateDebris(int loopCount, int particleCount,float force, float interval,Vector2 point)
    {
        for (int i = 0; i < loopCount; i++)
        {
            yield return new WaitForSeconds(interval);
            GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
            Gibs debris = go.GetComponent<Gibs>();
            debris.isInUse = true;
            debris.CreateWithData(particleCount, force);
            debris.transform.position = point;
        }
        
    }
    

    public IEnumerator CreateWithExplosion(float delay, ExplosionType expType, Vector3 point, bool rubble, int numberOfExplosions, float scale, float varDistance,System.Action OnComplete = null)
    {
        yield return new WaitForSeconds(delay);
        GenerateParticlesAt(expType, point, rubble, numberOfExplosions, scale, varDistance);
        OnComplete?.Invoke();
    }

    IEnumerator PlayExplosion(Explosion explosion, string animationName, float startDelay, float endDelay, ExplosionType explosionType)
    {
        explosion.IsInUse = true;

        yield return new WaitForSeconds(startDelay);
        if (Random.value < 0.5)
        {
            //TODO Distace Sound
            AudioManager.instance.PlaySound(AudioType.ThreeD,Constants_Audio.Audio.explosionEnemyPlane,1,explosion.transform.position);
        }
        else
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane2,1,explosion.transform.position);
            //Globals.PlaySound("res/Sounds/SFX/explosionEnemyPlane2.mp3", explosion.transform.position);
        }
        if (explosionType == ExplosionType.ExplosionTypeAir2 || explosionType == ExplosionType.ExplosionTypeEnemyFall)
        {
            //GameManager.instance.ShakeCamera(0.5f, 1);

        }
        else if (explosionType == ExplosionType.ExplosionTypeBoss)
        {
            //GameManager.instance.ShakeCamera(1, 8);
        }

        if(!animationName.Equals("bg0")) explosion.PlayAnimation(animationName);

        yield return new WaitForSeconds(endDelay);

        explosion.Reset();
    }

    IEnumerator PlayWXRExplosion(WXR_Explosion explosion, string animationName, float startDelay, float endDelay, ExplosionType explosionType)
    {
        explosion.IsInUse = true;

        yield return new WaitForSeconds(startDelay);
        if (Random.value < 0.5)
        {
            //TODO Distace Sound
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane, 1, explosion.transform.position);
        }
        else
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane2, 1, explosion.transform.position);
            //Globals.PlaySound("res/Sounds/SFX/explosionEnemyPlane2.mp3", explosion.transform.position);
        }

        explosion.PlayAnimation();
        yield return new WaitForSeconds(endDelay);

        explosion.Reset();
    }


    //----------------------------------------wxr explosion attack---------------------------------//
    public void GenerateParticlesAt(ExplosionType expType, Vector3 point,
        float scale, float varDistance,double damage,CatSkill.Item skillData)
    {
        WXR_ExplosionAttack expAttack = null;
        bool didFindExplosion = false;
        foreach (WXR_ExplosionAttack exp in GameSharedData.Instance.wxrExplosionsAttackList)
        {
            if (!exp.IsInUse)
            {
                expAttack = exp;
                didFindExplosion = true;
                break;
            }

        }
        if (!didFindExplosion)
        {
            return;
        }
        expAttack.gameObject.SetActive(false);
        expAttack.transform.SetScale(scale);

        expAttack.transform.SetRotation(Random.value * 360);
        //expAttack.transform.SetWorldPosition(point.x + Random.value * varDistance - varDistance / 2, point.y + Random.value * varDistance - varDistance / 2);
        expAttack.transform.position = point;
        expAttack.damage = damage;
        expAttack.skillData = skillData;
        //GameSharedData.Instance.wxrExplosionsAttackInUse.Add(expAttack);
        //StartCoroutine(PlayWXRExplosionAttack(expAttack, 0.2f, 1f, expType));
        PlayExplosionAttack(expAttack);
    }

    IEnumerator PlayWXRExplosionAttack(WXR_ExplosionAttack explosion, float startDelay, float endDelay, ExplosionType explosionType)
    {
        explosion.IsInUse = true;

        yield return new WaitForSeconds(startDelay);
        if (Random.value < 0.5)
        {
            //TODO Distace Sound
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane, 1, explosion.transform.position);
        }
        else
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane2, 1, explosion.transform.position);
            //Globals.PlaySound("res/Sounds/SFX/explosionEnemyPlane2.mp3", explosion.transform.position);
        }

        explosion.PlayAnimation();
        yield return new WaitForSeconds(endDelay);

        explosion.Reset();
    }

    private void PlayExplosionAttack(WXR_ExplosionAttack explosion)
    {
        explosion.IsInUse = true;
        if (Random.value < 0.5)
        {
            //TODO Distace Sound
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane, 1, explosion.transform.position);
        }
        else
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.explosionEnemyPlane2, 1, explosion.transform.position);
            //Globals.PlaySound("res/Sounds/SFX/explosionEnemyPlane2.mp3", explosion.transform.position);
        }
        explosion.PlayAnimation();
        
        AttackEnemy(explosion);
        explosion.Reset();
    }

    private void AttackEnemy(WXR_ExplosionAttack expAttack)
    {
        double _bulletOrMissileDamage = 0;
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.canCheckDistance && Vector2.Distance(expAttack.transform.position, (Vector2)enemy.transform.position) < expAttack.skillData.DamageRadius * 0.00004f)
            {
                if (expAttack.skillData != null)
                {
                    LuaToCshapeManager.Instance.AddSkillDamageCount(expAttack.skillData.Type, expAttack.damage);
                }
                _bulletOrMissileDamage = expAttack.damage;
                ////是否检查暴击
                //if (missile.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
                //{
                //    _bulletOrMissileDamage *= 1.5f;
                //    ShowCriticalTMP(enemy, _bulletOrMissileDamage);
                //}
                if (enemy.TakeHit(_bulletOrMissileDamage))
                {
                    if (!enemy.isDestroyed)
                    {
                        enemy.isDestroyed = true;
                         GameManager.instance.physicsManager.DestroyEnemy(enemy);
                        //enemy.Destroy();
                    }
                }
                //expAttack.Reset();
                //return;
            }
        }
    }

}
