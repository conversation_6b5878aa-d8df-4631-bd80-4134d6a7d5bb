using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using TMPro;
using UnityEngine.UI;
public class HudLayer : MonoBehaviour
{
    [SerializeField] private PlayerHud playerHud;
    [SerializeField] private ComboManager comboManagerObj;
    [SerializeField] private Sprite[] consoleSprite;
    [SerializeField] private GameObject dashButtonContainer;
    [SerializeField] private CustomButton dashButtonConsole;
    [SerializeField] private CustomButton dashButtonConsoleGlyph;
    [SerializeField] private Image dashButtonConsoleGlyphImage;
    [SerializeField] private Image dashButtonDesktop;
    [SerializeField] private Sprite[] dashButtonDesktopSprite;
    [SerializeField] private TextMeshProUGUI dashLabel;

    private string tweenId;
    private string schedulerId;


    public void Start()
    {
        tweenId = "HL" + GetInstanceID();
        schedulerId = "HLS" + GetInstanceID();
        comboManagerObj.Init();
        RegisterEvents();
    }

    public void AddKillToComboManager()
    {
        comboManagerObj.AddKill();
        //_playerHUD->updateComboBar(comboManagerObj->killComboCounter);

    }
    public int getKillCombo()
    {
        return comboManagerObj.killComboCounter;
    }

    private void Update()
    {
        if (Input.GetMouseButtonUp(1))
        {
            if (GameData.instance.fileHandler.currentMission == 2)
            {
                DOTween.Kill(tweenId);
                DOTween.Kill(schedulerId);
                dashButtonContainer.SetActive(false);
                dashButtonDesktop.gameObject.SetActive(false);
                //if (PlayerPrefs.GetInt("DashTutorial2") == 1)
                //{
                //    GameManager.instance.timeManager.SetTimescale(1.0f);
                //    PlayerPrefs.SetInt("DashTutorial2", 0);
                //}
            }
        }
    }

    private void RegisterEvents()
    {


        Observer.RegisterCustomEvent(gameObject, "show_player_hud", () => {
            //TODO
            //_playerHUD->runAction(MoveTo::create(0.5f, cocos2d::Point(winMinBounds.x + offsetInside + extraNotchDistance, winMaxBounds.y - offsetInside)));
            //_playerHUD->setInitialPosition(cocos2d::Point(winMinBounds.x + offsetInside + extraNotchDistance, winMaxBounds.y - offsetInside));


        });


        Observer.RegisterCustomEvent(gameObject, "hide_player_hud", () =>
         {
             playerHud.HideHud();
         });

        Observer.RegisterCustomEvent(gameObject, "EndControllerDashTutorial", () => {

            if (dashLabel && dashButtonContainer)
            {
                dashLabel.gameObject.SetActive(false);
                dashButtonContainer.SetActive(false);
                GameManager.instance.timeManager.SetTimescale(1.0f);

            }

        });

        Observer.RegisterCustomEvent(gameObject, "DashTutorial", () =>
         {
             dashButtonContainer.SetActive(true);
             if (Globals.isJoystickConnected)
             {

                 string dashButtonString = "L2";
                 if (Globals.controllerSetting == 2)
                     dashButtonString = "R2";

                 Sprite sp = null;
#if UNITY_PS4 || UNITY_PS5
            sp = consoleSprite[0];
            
#elif UNITY_XBOXONE
            sp = consoleSprite[0];
#endif
                if (sp)
                 {
                     dashButtonConsoleGlyph.gameObject.SetActive(true);
                     dashButtonConsoleGlyphImage.sprite = sp;
                     dashButtonConsoleGlyph.CallForAttention(0.01f);
                 }
                 else
                 {
                     dashButtonConsole.gameObject.SetActive(true);
                     dashButtonConsoleGlyph.CallForAttention(0.01f);
                 }


             }
             else
             {
#if UNITY_STANDALONE

                dashButtonDesktop.gameObject.SetActive(true);

                 DOTween.Sequence().SetId(tweenId).SetUpdate(true).AppendInterval(0.1f).AppendCallback(() => { dashButtonDesktop.sprite = dashButtonDesktopSprite[0]; }).AppendInterval(0.1f).AppendCallback(() => { dashButtonDesktop.sprite = dashButtonDesktopSprite[1]; }).SetLoops(-1).Play();
#else
            // set dash button glow visisble
            //TODO Check
            //static_cast<GameController*>(GETPLAYERCONTROLLER->getParent())->_hudLayer->pauseLayer->setScale(0);

                InputController.instance.dashButtonGlow.gameObject.SetActive(true);
                InputController.instance.dashTutorialHand.gameObject.SetActive(true);

#endif
            }
             dashLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["dash"] as string; ;
             if (Vector2.Distance(GameManager.instance.player.transform.position, GameSharedData.Instance.enemyList[0].transform.position) > Globals.CocosToUnity(600))
             {
                 float val = GameManager.instance.timeManager.TimeScale;
                 DOTween.To(() => val, x => val = x, 0.015f, 0.1f).SetUpdate(true).OnUpdate(() =>
                             {
                                 if (PlayerPrefs.GetInt("DashTutorial2") == 0)
                                 {
                                     GameManager.instance.timeManager.SetTimescale(1);
                                 }
                                 else if (GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
                                 {
                                     GameManager.instance.timeManager.SetTimescale(val);
                                 }
                             });
             }
             else
             {
                 if (GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING && GameManager.instance.player.Stats.health > 0)
                 {

                     GameManager.instance.timeManager.SetTimescale(0.015f);

                    //  add here
                }

             }
             PlayerPrefs.SetInt("DashTutorial2", 1);
             InputController.instance.SetDash(true);
         });
    }
}




