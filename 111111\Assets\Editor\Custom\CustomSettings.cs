#define USING_DOTWEENING

using System;
using System.Collections.Generic;
using Apq;
using Apq.Net;
using Cysharp.Threading.Tasks;
using GsProxy.JsonParams;
using LuaInterface;

using ProvisGames.Core.SpineSupport;

using TMPro;

using UnityEditor;

using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using UnityEngine.Video;

using BindType = ToLuaMenu.BindType;

public static class CustomSettings
{
    public static string saveDir = Application.dataPath + "/Source/Generate/";
    public static string toluaBaseType = Application.dataPath + "/ToLua/BaseType/";
    public static string baseLuaDir = Application.dataPath + "/Tolua/Lua/";
    public static string injectionFilesPath = Application.dataPath + "/ToLua/Injection/";

    //导出时强制做为静态类的类型(注意customTypeList 还要添加这个类型才能导出)
    //unity 有些类作为sealed class, 其实完全等价于静态类
    public static List<Type> staticClassTypes = new List<Type>
    {
        typeof(UnityEngine.Application),
        typeof(UnityEngine.Time),
        typeof(UnityEngine.Screen),
        typeof(UnityEngine.SleepTimeout),
        typeof(UnityEngine.Input),
        typeof(UnityEngine.Resources),
        typeof(UnityEngine.Physics),
        typeof(UnityEngine.RenderSettings),
        typeof(UnityEngine.QualitySettings),
        typeof(UnityEngine.GL),
        typeof(UnityEngine.Graphics),
        typeof(Helper),
        typeof(RectTransformUtility),
        typeof(FxManager),
        typeof(RandomNum),
        typeof(LayoutRebuilder),
    };

    //附加导出委托类型(在导出委托时, customTypeList 中牵扯的委托类型都会导出， 无需写在这里)
    public static DelegateType[] customDelegateList =
    {
        _DT(typeof(Action)),
        _DT(typeof(UnityEngine.Events.UnityAction)),
        _DT(typeof(System.Predicate<int>)),
        _DT(typeof(System.Action<int>)),
        _DT(typeof(System.Comparison<int>)),
        _DT(typeof(System.Func<int, int>)),
        _DT(typeof(UnityEngine.Events.UnityAction<UnityEngine.EventSystems.BaseEventData>)),
    };

    //在这里添加你要导出注册到lua的类型列表
    public static BindType[] customTypeList =
    {                
        //------------------------为例子导出--------------------------------
        //_GT(typeof(TestEventListener)),
        //_GT(typeof(TestProtol)),
        //_GT(typeof(TestAccount)),
        //_GT(typeof(Dictionary<int, TestAccount>)).SetLibName("AccountMap"),
        //_GT(typeof(KeyValuePair<int, TestAccount>)),
        //_GT(typeof(Dictionary<int, TestAccount>.KeyCollection)),
        //_GT(typeof(Dictionary<int, TestAccount>.ValueCollection)),
        //_GT(typeof(TestExport)),
        //_GT(typeof(TestExport.Space)),
        //-------------------------------------------------------------------        
                        
        _GT(typeof(LuaInjectionStation)),
        _GT(typeof(InjectType)),
        _GT(typeof(Debugger)).SetNameSpace(null),
        _GT(typeof(AudioSource)),
        _GT(typeof(RandomNum)),
        _GT(typeof(LoginParam)),
        _GT(typeof(UniTaskVoid)),

#if USING_DOTWEENING
        _GT(typeof(DG.Tweening.DOTween)),
        _GT(typeof(DG.Tweening.Tween)).SetBaseType(typeof(System.Object)).AddExtendType(typeof(DG.Tweening.TweenExtensions)),
        _GT(typeof(DG.Tweening.Sequence)).AddExtendType(typeof(DG.Tweening.TweenSettingsExtensions)),
        _GT(typeof(DG.Tweening.Tweener)).AddExtendType(typeof(DG.Tweening.TweenSettingsExtensions)),
        _GT(typeof(DG.Tweening.LoopType)),
        _GT(typeof(DG.Tweening.PathMode)),
        _GT(typeof(DG.Tweening.PathType)),
        _GT(typeof(DG.Tweening.RotateMode)),
        _GT(typeof(Component)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)).AddExtendType(typeof(MyShortcutExtensions)),
        _GT(typeof(Transform)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        _GT(typeof(Light)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        _GT(typeof(Material)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        _GT(typeof(Rigidbody)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        _GT(typeof(Camera)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        //_GT(typeof(LineRenderer)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),
        //_GT(typeof(TrailRenderer)).AddExtendType(typeof(DG.Tweening.ShortcutExtensions)),

        // 网上找的补充
        _GT(typeof(DG.Tweening.AutoPlay)),
        _GT(typeof(DG.Tweening.AxisConstraint)),
        _GT(typeof(DG.Tweening.Ease)),
        _GT(typeof(DG.Tweening.LogBehaviour)),
        _GT(typeof(DG.Tweening.ScrambleMode)),
        _GT(typeof(DG.Tweening.TweenType)),
        _GT(typeof(DG.Tweening.UpdateType)),

        _GT(typeof(DG.Tweening.DOVirtual)),
        _GT(typeof(DG.Tweening.EaseFactory)),
        _GT(typeof(DG.Tweening.TweenParams)),

        _GT(typeof(DG.Tweening.Core.ABSSequentiable)),
        _GT(typeof(DG.Tweening.Core.TweenerCore<Vector3, Vector3, DG.Tweening.Plugins.Options.VectorOptions>)).SetWrapName("TweenerCoreV3V3VO").SetLibName("TweenerCoreV3V3VO"),
        _GT(typeof(DG.Tweening.Core.TweenerCore<Quaternion, Vector3, DG.Tweening.Plugins.Options.QuaternionOptions>)).SetWrapName("TweenerCoreQ4V3QO").SetLibName("TweenerCoreQ4V3QO"),
        _GT(typeof(DG.Tweening.Core.TweenerCore<Color,Color,DG.Tweening.Plugins.Options.ColorOptions>)).SetWrapName("TweenerCoreClClCO").SetLibName("TweenerCoreClClCO"),
        _GT(typeof(DG.Tweening.Core.TweenerCore<Vector2,Vector2,DG.Tweening.Plugins.Options.VectorOptions>)).SetWrapName("TweenerCoreV2V2VO").SetLibName("TweenerCoreV2V2VO"),
        _GT(typeof(DG.Tweening.Core.TweenerCore<float,float,DG.Tweening.Plugins.Options.FloatOptions>)).SetWrapName("TweenerCoreFlFlFO").SetLibName("TweenerCoreFlFlFO"),
        _GT(typeof(DG.Tweening.Core.TweenerCore<string, string, DG.Tweening.Plugins.Options.StringOptions>)).SetWrapName("TweenerCoreSTSTSO").SetLibName("TweenerCoreSTSTSO"),
        
#else
                                         
        _GT(typeof(Component)),
        _GT(typeof(Transform)),
        _GT(typeof(Material)),
        _GT(typeof(Light)),
        _GT(typeof(Rigidbody)),
        _GT(typeof(Camera)),
        
        _GT(typeof(AudioListener)),
        //_GT(typeof(LineRenderer))
        _GT(typeof(TrailRenderer)),
#endif
      
        _GT(typeof(Behaviour)),
        _GT(typeof(MonoBehaviour)),
        _GT(typeof(GameObject)).AddExtendType(typeof(MyShortcutExtensions)),
        _GT(typeof(TrackedReference)),
        _GT(typeof(Application)),
        _GT(typeof(Physics)),
        _GT(typeof(Collider)),
        _GT(typeof(Time)),
        _GT(typeof(Texture)),
        _GT(typeof(Texture2D)),
        _GT(typeof(Shader)),
        _GT(typeof(Renderer)),
        _GT(typeof(WWW)),
        _GT(typeof(Screen)),
        _GT(typeof(CameraClearFlags)),
        _GT(typeof(AudioClip)),
        _GT(typeof(AssetBundle)),
        _GT(typeof(ParticleSystem)),
        //_GT(typeof(ParticleSystem.ShapeModule)),
        //_GT(typeof(ParticleSystemShapeType)),
        _GT(typeof(AsyncOperation)).SetBaseType(typeof(System.Object)),
        _GT(typeof(LightType)),
        _GT(typeof(SleepTimeout)),
#if UNITY_5_3_OR_NEWER && !UNITY_5_6_OR_NEWER
        _GT(typeof(UnityEngine.Experimental.Director.DirectorPlayer)),
#endif
        _GT(typeof(Animator)),
        _GT(typeof(AnimatorCullingMode)),
        _GT(typeof(Input)),
        _GT(typeof(KeyCode)),
        _GT(typeof(SkinnedMeshRenderer)),
        _GT(typeof(Space)),      
       
        //_GT(typeof(MeshRenderer)),
#if !UNITY_5_4_OR_NEWER
        _GT(typeof(ParticleEmitter)),
        _GT(typeof(ParticleRenderer)),
        _GT(typeof(ParticleAnimator)), 
#endif

        _GT(typeof(BoxCollider)),
        _GT(typeof(MeshCollider)),
        _GT(typeof(SphereCollider)),
        _GT(typeof(CharacterController)),
        _GT(typeof(CapsuleCollider)),

        _GT(typeof(Animation)),
        _GT(typeof(AnimationClip)).SetBaseType(typeof(UnityEngine.Object)),
        _GT(typeof(AnimationState)),
        _GT(typeof(AnimationBlendMode)),
        _GT(typeof(QueueMode)),
        _GT(typeof(PlayMode)),
        _GT(typeof(WrapMode)),
        _GT(typeof(Projector)),
        _GT(typeof(QualitySettings)),
        _GT(typeof(RenderSettings)),
        _GT(typeof(SkinWeights)),
        _GT(typeof(RenderTexture)),
        //_GT(typeof(FileSecurity)),
        //_GT(typeof(File)),
        _GT(typeof(RenderTextureFormat)),
        // ugui
        _GT(typeof(UnityEngine.EventSystems.EventSystem)),
        _GT(typeof(UnityEngine.EventSystems.BaseEventData)),
        _GT(typeof(UnityEngine.EventSystems.PointerEventData)),
        _GT(typeof(UnityEngine.EventSystems.PointerEventData.InputButton)),
        _GT(typeof(UnityEngine.EventSystems.RaycastResult)),
        _GT(typeof(UnityEngine.EventSystems.AxisEventData)),
        _GT(typeof(UnityEngine.EventSystems.MoveDirection)),
        _GT(typeof(UnityEngine.EventSystems.EventTrigger)).AddExtendType(typeof(MyShortcutExtensions)),
        _GT(typeof(UnityEngine.EventSystems.EventTriggerType)),
        _GT(typeof(Canvas)),
        _GT(typeof(RenderMode)),
        //DOTweenModuleUI类有强制编译模式，报错不影响，后面再看怎么把报错去掉
        _GT(typeof(CanvasGroup)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(CanvasScaler)),
        _GT(typeof(LayoutElement)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(GraphicRaycaster)),
        _GT(typeof(RectTransform)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(RectTransformUtility)),
        _GT(typeof(CanvasRenderer)),
        _GT(typeof(Graphic)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(Text)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(Font)),
        _GT(typeof(TextAnchor)),
        _GT(typeof(FontStyle)),
        _GT(typeof(HorizontalWrapMode)),
        _GT(typeof(VerticalWrapMode)),
        _GT(typeof(Image)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(Image.Type)),
        _GT(typeof(Image.FillMethod)),
        _GT(typeof(Sprite)),
        _GT(typeof(UnityEngine.U2D.SpriteAtlas)),
        _GT(typeof(RawImage)),
        _GT(typeof(Rect)),
        _GT(typeof(Mask)),
        _GT(typeof(RectMask2D)),
        _GT(typeof(Button)),
        _GT(typeof(Button.ButtonClickedEvent)).AddExtendType(typeof(MyShortcutExtensions)),
        _GT(typeof(InputField)),
        _GT(typeof(InputField.CharacterValidation)),
        _GT(typeof(InputField.ContentType)),
        _GT(typeof(InputField.InputType)),
        _GT(typeof(InputField.LineType)),
        _GT(typeof(InputField.OnChangeEvent)),
        _GT(typeof(InputField.SubmitEvent)),
        _GT(typeof(Toggle)),
        _GT(typeof(Toggle.ToggleTransition)),
        _GT(typeof(Toggle.ToggleEvent)),
        _GT(typeof(ToggleGroup)),
        _GT(typeof(Slider)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(Slider.Direction)),
        _GT(typeof(Slider.SliderEvent)),
        _GT(typeof(Scrollbar)),
        _GT(typeof(Scrollbar.Direction)),
        _GT(typeof(Scrollbar.ScrollEvent)),
        _GT(typeof(Dropdown)),
        _GT(typeof(Dropdown.OptionData)),
        _GT(typeof(ScrollRect)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(ScrollRect.MovementType)),
        _GT(typeof(ScrollRect.ScrollbarVisibility)),
        _GT(typeof(ScrollRect.ScrollRectEvent)),
        _GT(typeof(Selectable)),
        _GT(typeof(Selectable.Transition)),
        _GT(typeof(Shadow)),
        _GT(typeof(Outline)).AddExtendType(typeof(DG.Tweening.DOTweenModuleUI)),
        _GT(typeof(ContentSizeFitter)),
        //_GT(typeof(ContentSizeFitter.FitMode)),
        _GT(typeof(AspectRatioFitter)),
        _GT(typeof(AspectRatioFitter.AspectMode)),
        _GT(typeof(RectOffset)),
        _GT(typeof(HorizontalLayoutGroup)),
        _GT(typeof(VerticalLayoutGroup)),
        _GT(typeof(GridLayoutGroup)),
        _GT(typeof(GridLayoutGroup.Axis)),
        _GT(typeof(GridLayoutGroup.Constraint)),
        _GT(typeof(GridLayoutGroup.Corner)),
        _GT(typeof(LayoutRebuilder)),
        // lua 辅助
        _GT(typeof(UIEventListener)),
        _GT(typeof(EventListenerEx)),
        _GT(typeof(Type)),
        _GT(typeof(PlayerPrefs)),
        _GT(typeof(SceneManager)),
        _GT(typeof(Resources)),
         _GT(typeof(TextAsset)),
        _GT(typeof(LogType)),
        _GT(typeof(CsharpApiVersion)),
        _GT(typeof(Helper)),
        _GT(typeof(Premier)),
        _GT(typeof(Network)),
        _GT(typeof(Matrix4x4)),
        _GT(typeof(UnityEngine.AI.NavMeshPath)),
        _GT(typeof(DateTime)),
        _GT(typeof(TimeSpan)),
        _GT(typeof(UnityEngine.AI.NavMesh)),
        _GT(typeof(UnityEngine.AI.NavMeshHit)),
        _GT(typeof(UnityEngine.AI.NavMeshAgent)),
        _GT(typeof(LuaDataComp)),
        _GT(typeof(PersistentClient)),
        _GT(typeof(PersistentConnection)),
        _GT(typeof(Exception)),
        _GT(typeof(PickComp)),
        _GT(typeof(AnimatorStateInfo)),
        _GT(typeof(HotResManager)),
        _GT(typeof(GameGlobal)),
        _GT(typeof(AttachComp)),
        //_GT(typeof(ParticleSystemRenderer)),
        _GT(typeof(GameLuaAPI)),
        _GT(typeof(GameLuaAPI.eChannel)),
        _GT(typeof(WWWForm)),
        _GT(typeof(LuaProfiler)),
        _GT(typeof(ThreadPriority)),
        _GT(typeof(HttpRequestHelper)),
        _GT(typeof(FxComp)),
        _GT(typeof(FxManager)),
        _GT(typeof(AniComp)),
        _GT(typeof(FollowEffectComp)),
        _GT(typeof(SpriteAnimationComp)),
        _GT(typeof(TextAssist)),
        _GT(typeof(RawImageAssist)),
        _GT(typeof(RaycastBlockComp)),
        _GT(typeof(ResMgr)),

        _GT(typeof(GameData)),
        _GT(typeof(LuaToCshapeManager)),
        _GT(typeof(VideoPlayer)),
        _GT(typeof(SkeletonGraphicRespectRectTransform)),

        //spine
        _GT(typeof(Spine.AnimationState)),
        _GT(typeof(Spine.Animation)),
        _GT(typeof(Spine.Skeleton)),
        _GT(typeof(Spine.SkeletonData)),
        _GT(typeof(Spine.AnimationStateData)),
        //_GT(typeof(Spine.Unity.SkeletonGraphic)),
        _GT(typeof(Spine.Unity.SkeletonAnimation)),
        //_GT(typeof(Spine.Unity.SkeletonDataAsset)),
        _GT(typeof(Spine.Bone)),
        _GT(typeof(CFixGridRect)),
        _GT(typeof(TextMeshPro)),
        _GT(typeof(MainMenuButton)),
        _GT(typeof(RectTransform.Axis)),

        //----------自定义导出类----------
        _GT(typeof(UILongPressButton)),       
        _GT(typeof(GameManager)),
        _GT(typeof(MissionManager)),
        
    };

    public static List<Type> dynamicList = new List<Type>()
    {
        typeof(MeshRenderer),
#if !UNITY_5_4_OR_NEWER
        typeof(ParticleEmitter),
        typeof(ParticleRenderer),
        typeof(ParticleAnimator),
#endif

        typeof(BoxCollider),
        typeof(MeshCollider),
        typeof(SphereCollider),
        typeof(CharacterController),
        typeof(CapsuleCollider),

        typeof(Animation),
        typeof(AnimationClip),
        typeof(AnimationState),

        typeof(SkinWeights),
        typeof(RenderTexture),
        typeof(Rigidbody),
    };

    //重载函数，相同参数个数，相同位置out参数匹配出问题时, 需要强制匹配解决
    //使用方法参见例子14
    public static List<Type> outList = new List<Type>()
    {

    };

    //ngui优化，下面的类没有派生类，可以作为sealed class
    public static List<Type> sealedList = new List<Type>()
    {
        /*typeof(Transform),
        typeof(UIRoot),
        typeof(UICamera),
        typeof(UIViewport),
        typeof(UIPanel),
        typeof(UILabel),
        typeof(UIAnchor),
        typeof(UIAtlas),
        typeof(UIFont),
        typeof(UITexture),
        typeof(UISprite),
        typeof(UIGrid),
        typeof(UITable),
        typeof(UIWrapGrid),
        typeof(UIInput),
        typeof(UIScrollView),
        typeof(UIEventListener),
        typeof(UIScrollBar),
        typeof(UICenterOnChild),
        typeof(UIScrollView),        
        typeof(UIButton),
        typeof(UITextList),
        typeof(UIPlayTween),
        typeof(UIDragScrollView),
        typeof(UISpriteAnimation),
        typeof(UIWrapContent),
        typeof(TweenWidth),
        typeof(TweenAlpha),
        typeof(TweenColor),
        typeof(TweenRotation),
        typeof(TweenPosition),
        typeof(TweenScale),
        typeof(TweenHeight),
        typeof(TypewriterEffect),
        typeof(UIToggle),
        typeof(Localization),*/
        typeof(RectTransform),
        typeof(EventListenerEx),
    };

    public static BindType _GT(Type t)
    {
        return new BindType(t);
    }

    public static DelegateType _DT(Type t)
    {
        return new DelegateType(t);
    }


    [MenuItem("Lua/Attach Profiler", false, 151)]
    static void AttachProfiler()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请在运行时执行此功能", "确定");
            return;
        }

        LuaClient.Instance.AttachProfiler();
    }

    [MenuItem("Lua/Detach Profiler", false, 152)]
    static void DetachProfiler()
    {
        if (!Application.isPlaying)
        {
            return;
        }

        LuaClient.Instance.DetachProfiler();
    }
}
