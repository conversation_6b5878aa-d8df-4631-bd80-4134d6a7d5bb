﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using UnityEngine;
using TMPro;


public class ShopButton : MonoBehaviour
{
    public enum ButtonCategory { Category1 = 0, Category2 = 1, Category3 = 2 }

    public Action<ShopButton> onTap, onSelect;

    [SerializeField] int buttonIndex;
    [SerializeField] GameObject buttonsConatiner, damageobject, bulletsObject, mainLabel, attributes, tick, arrow;
    [SerializeField] SkeletonGraphic lockSkeleton;
    [SerializeField] CustomButton normalButton, coinButton, circleButton;
    [SerializeField] TextMeshProUGUI damageTMP, bulletsTMP, coinButtonTMP1, coinButtonTMP2, normalButtonTMP, buttonTitleTMP,
        lockTMP;

    [HideInInspector] public Shop shop;
    PList buttonData;
    Animator animator;
    string popupString;
    int buyAmount;
    int sellAmount;
    bool isExpanded, isLocked, unlockAnimationShown;

    public int Category { get; set; }

    public bool ToggleButtonsContainer {
        set {
            buttonsConatiner.SetActive(value);
            if (attributes)
            {
                attributes.SetActive(value);
                mainLabel.SetActive(!value);
            }

            SetAttributes(value);
        } }
    public bool IsExpanded { get { return isExpanded; } }
    public int ButtonIndex { get { return buttonIndex; } }

    private void Awake()
    {
        animator = GetComponent<Animator>();
    }

    private void Start()
    {
        isExpanded = false;
        buttonsConatiner.SetActive(false);
        animator.Play("Contract", 0, 1);

        circleButton.SetButtonColor(circleButton.PURPLE);
        normalButton.defaultAction = Equip;
        coinButton.defaultAction = TappedBuy;
        circleButton.defaultAction = SellButtonTapped;
    }

    public void Init()
    {
        if(buttonData == null)
        {
            string ch = "Gun" + (ButtonIndex + 1);
            buttonData = (GameData.instance.GetShop()[((ButtonCategory)Category).ToString()] as PList)[ch] as PList;
        }

        //SetAttributes(false);
    }

    public int GetUnlockLevel()
    {
        return (int)buttonData["UnlockLevel"];
    }

    public void SetAttributes(bool isOpen)
    {
        if (buttonData == null)
            return;

        isLocked = (int)buttonData["UnlockLevel"] > GameData.instance.fileHandler.missionsCompleted;

        arrow.SetActive(!isLocked);
        lockSkeleton.gameObject.SetActive(isLocked);
        lockTMP.text = GetUnlockLevel().ToString();

        int level = (Category == 0 || Category == 2) && buttonIndex == 0
            && PlayerPrefs.GetInt(buttonData["Name"] as string) == 0
            ? 1
            : PlayerPrefs.GetInt(buttonData["Name"] as string);

        tick.SetActive(PlayerPrefs.GetInt(((ButtonCategory)Category).ToString()) == buttonIndex && !isLocked && level > 0);
        circleButton.SetInteractable(isOpen);
        circleButton.gameObject.SetActive(level > 1 && level <= 5 && Category != 2);

        if (Category == 2)
            return;
                
        if (damageobject.activeSelf)
        {
            int damage = level == 0 ? 0 : (int)(((buttonData["Stats"] as PList)["stat1"] as PList)["Level"] as PList)["L" + level.ToString()];
            int nextDamage = level + 1 > 5 ? 0 : (int)(((buttonData["Stats"] as PList)["stat1"] as PList)["Level"] as PList)["L" + (level + 1).ToString()] - damage;
            string stat1Str = damage == 0 ? "" : damage.ToString();
            string stat1NextStr = nextDamage == 0 ? "" : "+" + nextDamage.ToString();
            damageTMP.text = isOpen ? stat1Str + "<color=#22FF00>" + stat1NextStr + "</color>" : stat1Str; 
        }

        if (bulletsObject.activeSelf)
        {
            int bullets = level == 0 ? 0 : (int)(((buttonData["Stats"] as PList)["stat2"] as PList)["Level"] as PList)["L" + level.ToString()];
            int nextBullets = level + 1 > 5 ? 0 : (int)(((buttonData["Stats"] as PList)["stat2"] as PList)["Level"] as PList)["L" + (level + 1).ToString()] - bullets;
            string stat2Str = bullets == 0 ? "" : bullets.ToString();
            string stat2NextStr = nextBullets == 0 ? "" : "+" + nextBullets.ToString();
            bulletsTMP.text = isOpen ? stat2Str + "<color=#22FF00>" + stat2NextStr + "</color>" : stat2Str;
        }
    }

    public void ResetButton()
    {
        NavigationButton navButton = null;

        int level = (Category == 0 || Category == 2) && buttonIndex == 0
            && PlayerPrefs.GetInt(buttonData["Name"] as string) == 0
            ? 1
            : PlayerPrefs.GetInt(buttonData["Name"] as string);
        bool isEquipped = PlayerPrefs.GetInt(((ButtonCategory)Category).ToString()) == buttonIndex && level > 0;

        popupString = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["upgradeToLevel"] as string;
        popupString += " " + (level + 1).ToString();

        if (level == 0)
        {
            normalButton.gameObject.SetActive(false);
            coinButton.gameObject.SetActive(true);
            coinButton.SetButtonColor(coinButton.YELLOW);
            coinButtonTMP1.text = "BUY";
            int price = buyAmount = (int)(buttonData["Price"] as PList)["L1"];
            coinButtonTMP2.text = price.ToString();

            popupString = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["buyPopup"] as string;

            navButton = coinButton.GetComponent<NavigationButton>();
            NavigationButton.ChangeCurrentlySelected(navButton, true);

            return;
        }

        if (Category == 2)
        {
            if(level > 0)
            {
                if (isEquipped)
                {
                    normalButton.gameObject.SetActive(true);
                    coinButton.gameObject.SetActive(false);
                    normalButton.SetButtonColor(normalButton.YELLOW);
                    normalButton.SetInteractable(false);
                    normalButtonTMP.text = "MAXED";
                }
                else
                {
                    normalButton.gameObject.SetActive(true);
                    coinButton.gameObject.SetActive(false);
                    normalButton.SetInteractable(true);
                    normalButton.SetButtonColor(normalButton.GREEN);
                    normalButtonTMP.text = "EQUIP";
                }

                navButton = normalButton.GetComponent<NavigationButton>();
                NavigationButton.ChangeCurrentlySelected(navButton, true);
            }

            return;
        }

        if(level >= 5)
        {
            if (isEquipped)
            {
                normalButton.gameObject.SetActive(true);
                coinButton.gameObject.SetActive(false);
                normalButton.SetInteractable(false);
                normalButton.SetButtonColor(normalButton.YELLOW);
                normalButtonTMP.text = "MAXED";
            }
            else
            {
                normalButton.gameObject.SetActive(true);
                coinButton.gameObject.SetActive(false);
                normalButton.SetInteractable(true);
                normalButton.SetButtonColor(normalButton.GREEN);
                normalButtonTMP.text = "EQUIP";
            }

            navButton = normalButton.GetComponent<NavigationButton>();
            NavigationButton.ChangeCurrentlySelected(navButton, true);

            return;
        }

        if (isEquipped)
        {
            normalButton.gameObject.SetActive(false);
        }
        else
        {
            normalButton.gameObject.SetActive(true);
            normalButton.SetButtonColor(normalButton.GREEN);
            normalButton.SetInteractable(true);
            normalButtonTMP.text = "EQUIP";
        }

        {
            coinButton.gameObject.SetActive(true);
            coinButton.SetButtonColor(coinButton.BLUE);
            coinButtonTMP1.text = "UPGRADE";
            int price = buyAmount = Convert.ToInt32((buttonData["Price"] as PList)["L" + (level + 1).ToString()]);
            coinButtonTMP2.text = price.ToString();
        }

        navButton = coinButton.GetComponent<NavigationButton>();
        NavigationButton.ChangeCurrentlySelected(navButton, true);
    }

    public void CheckUnlockedItems()
    {
        bool showUnlock = (int)buttonData["UnlockLevel"] <= GameData.instance.fileHandler.missionsCompleted
            && GameData.instance.fileHandler.missionsCompleted > GameData.instance.fileHandler.lastLevelVisitingShop
            && !unlockAnimationShown;

        if ((Category == 0 || Category == 2) && buttonIndex == 0)
            return;

        if (!showUnlock)
            return;

        unlockAnimationShown = true;

        arrow.SetActive(false);
        lockSkeleton.gameObject.SetActive(true);
        lockSkeleton.transform.localScale *= 0.5f;
        lockSkeleton.AnimationState.SetAnimation(0, "unlock", false);
        DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() => { AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.boss_unlock); }).Play();
        lockTMP.gameObject.SetActive(false);
        DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() => arrow.SetActive(true));
    }

    public int CheckIfUnlocked()
    {
        if(buttonData == null)
        {
            string ch = "Gun" + (ButtonIndex + 1);
            buttonData = (GameData.instance.GetShop()[((ButtonCategory)Category).ToString()] as PList)[ch] as PList;
        }

        bool showUnlock = (int)buttonData["UnlockLevel"] <= GameData.instance.fileHandler.missionsCompleted
            && GameData.instance.fileHandler.missionsCompleted > GameData.instance.fileHandler.lastLevelVisitingShop
            && !unlockAnimationShown;

        if ((Category == 0 || Category == 2) && buttonIndex == 0)
            return 0;

        if (!showUnlock)
            return 0;

        return 1;
    }

    public void ButtonTapped()
    {
        if (isLocked)
            return;

        isExpanded = !isExpanded;
        animator.SetBool("Expand", isExpanded);
        onTap?.Invoke(this);
        onSelect?.Invoke(this);

        if (!isExpanded) NavigationButton.ChangeCurrentlySelected(GetComponent<NavigationButton>(), true);
    }

    public void ExpandButton(bool expand)
    {
        isExpanded = expand;
        animator.SetBool("Expand", isExpanded);
    }

    public void TappedBuy()
    {
        if(GameData.instance.fileHandler.coins < buyAmount)
        {
            popupString = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["brokePopup"] as string;
            MainMenuController.instance.CreateMessagePopup(buttonTitleTMP.text, popupString,false);
            var returnNavButton = NavigationButton.currentlySelected;
            MainMenuController.instance.popup.addedNoCallback = () =>
            {
                NavigationButton.ChangeCurrentlySelected(returnNavButton, true);
            };
            return;
        }

        {
            MainMenuController.instance.CreatePopup(buttonTitleTMP.text, popupString, BuyItem);
            var returnNavButton = NavigationButton.currentlySelected;
            MainMenuController.instance.popup.addedNoCallback = () =>
            {
                NavigationButton.ChangeCurrentlySelected(returnNavButton, true);
            };
        }
    }

    public void BuyItem()
    {

        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chestOpen);
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.levelUp);

        GameData.instance.fileHandler.coins -= buyAmount;
        int newLevel = PlayerPrefs.GetInt(buttonData["Name"] as string) + 1;
        newLevel = Mathf.Clamp(newLevel, 0, 5);
        PlayerPrefs.SetInt(buttonData["Name"] as string, newLevel);
        shop.UpdateCoins();
        ResetButton();
        SetAttributes(true);

        MainMenuController.instance.player.UpdateFrontGunLevel();
        MainMenuController.instance.player.UpdateRearGunLevel();

        MainMenuController.instance.player.PlayUpgradeEffect();

        GameData.instance.fileHandler.SaveData();

        if (shop.isTutorial)
        {
            MainMenuController.instance.LoadMainMenu();
        }
    }

    void SellButtonTapped()
    {
        string str = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["sellUpgrade"] as string;
        int level = PlayerPrefs.GetInt(buttonData["Name"] as string);
        sellAmount = (int)(buttonData["Price"] as PList)["L" + level.ToString()];
        str += " " + sellAmount;

        MainMenuController.instance.CreatePopup(buttonTitleTMP.text, str, SellItem);
    }

    void SellItem()
    {
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.chestOpen);
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.levelUp);
        GameData.instance.fileHandler.coins += sellAmount;
        int newLevel = PlayerPrefs.GetInt(buttonData["Name"] as string) - 1;
        newLevel = Mathf.Clamp(newLevel, 0, 5);
        PlayerPrefs.SetInt(buttonData["Name"] as string, newLevel);
        shop.UpdateCoins();
        ResetButton();
        SetAttributes(true);

        MainMenuController.instance.player.UpdateFrontGunLevel();
        MainMenuController.instance.player.UpdateRearGunLevel();

        GameData.instance.fileHandler.SaveData();
    }

    public void Equip()
    {
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.equip);
        PlayerPrefs.SetInt(((ButtonCategory)Category).ToString(), buttonIndex);
        shop.SelectItem(buttonIndex);
        onTap?.Invoke(this);
        SetAttributes(true);

        GameData.instance.fileHandler.SaveData();
    }

    public void MouseOver()
    {
        onSelect?.Invoke(this);
        buttonTitleTMP.color = Color.yellow;
    }
    public void MouseExit()
    {
        if (isExpanded)
            return;

        buttonTitleTMP.color = Color.white;
    }
}
