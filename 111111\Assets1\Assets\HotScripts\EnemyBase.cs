using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using Spine;
public class EnemyBase : Enemy
{
    [SerializeField] private List<Soldier> soldierArray;
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite mineSprite;
    [SerializeField] private Transform controlTowerPos;
    [SerializeField] private ControlTower controlTowerMain;
    [SerializeField] private ControlTower controlTowerLeft;
    [SerializeField] private ControlTower controlTowerRight;
    [SerializeField] private SkeletonAnimation[] flameAnim;

    private bool isOnFire = false;


    public override bool CheckCollision(Vector2 P1)
    {
        return false;
    }

    public override void Destroy()
    {

        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeEnemyBase && Globals.gameType == GameType.Training)
        {
            GameManager.instance.missionManager.MissionComplete();
            GameManager.instance.missionManager.Location = Vector2.zero;
        }
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        Globals.numberOfEnemies--;


    }


    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "EnemyBase" + GetInstanceID().ToString();
        tweenId = "EnemyBaseS" + GetInstanceID().ToString();
        allowRelocate = false;
        //this.unschedule(schedule_selector(EnemyLaserBase::reclocate));//TODO Fix this shit
        transform.position = new Vector3(40, -2.6f);
        InitStats();
        Globals.numberOfEnemies++;
        healthBar.gameObject.SetActive(false);
        healthBar.ScaleRatio = 0;
        explosionType = Explosions.ExplosionType.ExplosionTypeBuilding;
        InitSoldiers();
        SpawnTowers();
        SpawnMines();
        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(400));
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.3f).AppendCallback(UpdateBounds).SetLoops(-1).Play();
    }

    private void UpdateBounds()
    {

        if (!isOnFire && Globals.isMissionComplete)
        {
            //const float firePositionY = 0.0f;
            isOnFire = true;
            for (int i = 0; i < flameAnim.Length; i++)
            {
                flameAnim[i].gameObject.SetActive(true);
                flameAnim[i].state.SetAnimation(0, "idle", true);
                flameAnim[i].state.TimeScale = (0.8f + Random.value * 0.4f);
            }
            for (int i = 0; i < soldierArray.Count; i++)
            {
                soldierArray[i].Run();
            }
        }
        // spSkeletonBounds_update(_bounds, enemySprite.getSkeleton(), true);

    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();



        stats.speed = baseStats.speed = 7 + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = GameData.instance.fileHandler.TrainingLevel * 700;
        stats.bulletDamage = baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.missileDamage = baseStats.missileDamage =GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 15;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;

    }

    private void InitSoldiers()
    {
        for (int i = 0; i < soldierArray.Count; i++)
        {
            soldierArray[i].gameObject.SetActive(true);
            soldierArray[i].Init();
        }
    }

    private void SpawnMines()
    {
        for (int i = 0; i < 16; i++)
        {
            Mine mine = null;
            bool didFindMine = false;
            foreach (Mine m in GameSharedData.Instance.enemyMinePool)
            {
                if (!m.isInUse)
                {
                    mine = m;
                    mine.isInUse = true;
                    didFindMine = true;
                    break;
                }

            }
            if (!didFindMine)
            {
                return;
            }
            mine.Init();
            mine.transform.position = new Vector2(transform.position.x - Globals.CocosToUnity(2500) + (i * 3), Globals.LOWERBOUNDARY + Random.value * 0.3f);
            DOTween.Kill(mine.tweenId);
            mine.missileSprite.sprite = mineSprite;
            mine.duration = 500;
            mine.SetDamage(1200);
            mine.RemoveAfterDuration();
            GameSharedData.Instance.enemyMissilesInUse.Add(mine);
        }
    }

    private void SpawnTowers()
    {
        float healtMultiplier = 0.4f;
        controlTowerLeft.Init();
        controlTowerLeft.allowRightCannon = false;
        controlTowerLeft.stats.health = controlTowerLeft.stats.health * 2 * healtMultiplier;
        controlTowerLeft.stats.maxHealth.Value = controlTowerLeft.stats.health;
        controlTowerLeft.SetEnemyDifficulty();
        controlTowerLeft.transform.localPosition = new Vector2(-8, 0.4854369f);
        controlTowerMain.Init();
        controlTowerMain.allowLeftCannon = false;
        controlTowerMain.allowRightCannon = false;
        controlTowerMain.stats.health = controlTowerMain.stats.health * 5 * healtMultiplier;
        controlTowerMain.stats.maxHealth.Value = controlTowerMain.stats.health;
        controlTowerMain.SetEnemyDifficulty();
        controlTowerMain.transform.localPosition = new Vector2(0, 0.4854369f);
        controlTowerRight.Init();
        controlTowerRight.allowLeftCannon = false;
        controlTowerRight.stats.health = controlTowerRight.stats.health * 2 * healtMultiplier;
        controlTowerRight.stats.maxHealth.Value = controlTowerRight.stats.health;
        controlTowerRight.SetEnemyDifficulty();
        controlTowerRight.transform.localPosition = new Vector2(8, 0.4854369f);

    }

}
