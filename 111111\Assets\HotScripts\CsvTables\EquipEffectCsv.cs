﻿using System.Collections.Generic;
using System.IO;

using ProtoBuf;

using UnityEngine;

using X.PB;

namespace CsvTables
{
    public partial class EquipEffectCsv : Singleton<EquipEffectCsv>
    {
        private readonly Dictionary<int, EquipEffect.Item> dic = new();

        protected override void InitializeSingleton()
        {
            DontDestroyOnLoad(this);
            int schemeIndex = (int)SchemeType.EquipEffect;
            string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
            MemoryStream ms = new(HotResManager.ReadPb(pbFileName));
            var _data = Serializer.Deserialize<EquipEffect>(ms);
            foreach (var item in _data.Items)
            {
                dic.Add(item.Id, item);
            }
            //Debug.LogWarning(pbFileName + "pb succes");

            base.InitializeSingleton();
        }
    }
}