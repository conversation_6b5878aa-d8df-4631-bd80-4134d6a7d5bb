﻿using System.Threading;
using Cysharp.Threading.Tasks;
using HotScripts;
using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 怪物默认技能
    /// </summary>
    public class CreatureSkill_1000 : CreatureSkillBase
    {
        /// <summary>
        /// 使用技能的怪物
        /// </summary>
        protected Enemy Monster => Creature as Enemy;

        public override async UniTaskVoid DoSkill()
        {
            // 子弹朝向玩家的位置
            var dir = GameManager.instance.player.skeletonAnimationTran.position - Monster.transform.position;
            // 攻击距离还够不到玩家，就不发子弹了。
            if (dir.magnitude > Skill.攻击距离.Value)
            {
                return;
            }

            // 按技能的CD时间停止本轮技能
            base.DoSkill().Forget();

            await UniTask.SwitchToMainThread();

            // 怪物已销毁，就不使用技能了
            if (!Monster) return;

            // 怪物有射击概率，不是一定射击(但技能会重新冷却)
            if (Random.Range(0, 10000) >= Monster.CsvRow_BattleBrushEnemy.CriticalStrike)
            {
                return;
            }

            // 技能可由令牌或怪物死亡的方式停止
            var cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Skill.Token,
                Monster.GetCancellationTokenOnDestroy()
            );

            // 从子弹池中取出一个空闲子弹
            var bullet = GameSharedData.Instance.GetOrAddBulletFromPool(0, false);
            if (bullet == null)
            {
                // 没子弹用了,就不使用技能了
                return;
            }

            // 玩家销毁了或已经死了,就不使用技能了
            if (!GameManager.instance.player || GameManager.instance.player.FightProp.HP.Value <= double.Epsilon)
            {
                return;
            }

            //Debug.Log($"根据射击概率,怪物{Monster.gameObject.name}进行射击");
            
            // 开火声音
            AudioPlayer.Instance.PlaySound(Skill.CsvRow_CatSkill.ShootSound).Forget();

            var dir_1 = dir.normalized;
            bullet.transform.right = dir_1;

            bullet.isInUse = true;
            bullet.BulletProp.CreatureSkill = this;

            // 设置子弹图片
            // bullet.SetSpriteFrame(bulletSprite);
            bullet.transform.localScale = 2 * Skill.子弹缩放.Value * Vector3.one;

            // 设置子弹图片
            if (!string.IsNullOrWhiteSpace(Skill.CsvRow_CatSkill.BulletImg))
            {
                bullet.SetSprite(Skill.CsvRow_CatSkill.BulletImg);
                //bullet.GetComponent<SpriteRenderer>().sprite =
                //    HotResManager.ReadAtlas("Bullet").GetSprite(Skill.CsvRow_CatSkill.BulletImg);
            }

            // 子弹初始位置
            //bullet.transform.position = Monster.transform.position + (Monster.FightProp.Radius * dir_1);
            bullet.transform.position = Monster.transform.position + (0.3f * dir_1);

            bullet.transform.SetScale(Skill.子弹缩放.Value);
            bullet.gameObject.SetActive(true);
            //bullet.PlayBulletAnim(dir, false);

            // 添加到使用中的怪物子弹列表
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            //bullet.setRadiusEffectSquared(skill.子弹缩放.Value * skill.CsvRow_CatSkill.BulletRadius);

            bullet.BulletProp.MoveDirection_Straight = dir_1;
            bullet.BulletProp.StartMove_Straight().Forget();

            var cts_BulletDestroy = CancellationTokenSource.CreateLinkedTokenSource(bullet.CTS_BulletLife.Token,
                bullet.GetCancellationTokenOnDestroy());
            // 子弹最大存在时长
            TurnToPool_Delay(bullet, Skill.CsvRow_CatSkill.BulletLife, cts_BulletDestroy.Token).Forget();
        }

        protected async UniTaskVoid TurnToPool_Delay(Bullet bullet, float seconds, CancellationToken token)
        {
            await UniTask.Delay(System.TimeSpan.FromSeconds(seconds));
            if (token.IsCancellationRequested) return;
            bullet.TurnToPool().Forget();
        }

        //public override void ClearSkill()
        //{
        //    // 回收或销毁本轮发出的所有子弹
        //    foreach (var bullet in GameSharedData.Instance.playerBulletInUse.Where(x => x.SkillProps == Skill))
        //    {
        //        if (bullet.is来自子弹池)
        //        {
        //            // 重置子弹并回收到池中
        //            bullet.ResetBullet();
        //        }
        //        else
        //        {
        //            Object.Destroy(bullet);
        //        }
        //    }
        //}
    }
}