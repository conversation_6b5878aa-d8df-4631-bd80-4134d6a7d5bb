using System.Collections.Generic;
using System.Linq;

using Apq.Extension;

using DG.Tweening;

using UnityEngine;

public enum DefaultGameObjectType
{
    Bullet,
    Boomerang,
    Coin,
    IceBall,
    IceBullet,
    FireWell,
    Zapline,
    Zapper,
    ZaplineMax,
    UAV,
    BoomerangEffect,
    WhiteCat,
    BoomerangMax,
    Hammer,
    HammerMax,
    LighthningPrefab,
    FlamePrefab,
    LaserLine,
    Debris,
    CriticalTMP,
    AddCoinTMP,
    ExplosionTypeWXREnemy1,
    ExplosionTypeWXREnemy2,
    ExplosionTypeWXREnemy3,
    WXRExplosionAttack,
}

[System.Serializable]
public class PrefabProperties
{
    public DefaultGameObjectType Type;
    public GameObject prefab;
}

public class GameSharedData : MonoBehaviour
{
    public static GameSharedData Instance { get; private set; }
    [Header("Arsenal")]
    [SerializeField] private BulletPoolCreator bulletPoolProperties;
    [SerializeField] private EnemyMissilePoolCreator enemyMissilePoolProperties;
    [SerializeField] private int playerMissileLimit;
    [SerializeField] private GameObject playerMissilePrefab;
    [SerializeField] private GameObject playerMissileContainer;
    [SerializeField] private string enemyMissileLayer;
    [SerializeField] private string playerMissileLayer;
    [SerializeField] private CoinPoolCreator coinPoolProperties;

    [HideInInspector] public List<Coin> coinsPool = new List<Coin>();
    [HideInInspector] public List<Coin> coinsInUse = new List<Coin>();
    [HideInInspector] public List<Coin> extenCoinsInUse = new List<Coin>();
    [HideInInspector] public List<Bullet> playerBulletPool = new List<Bullet>();
    [HideInInspector] public List<Bullet> playerBulletInUse = new List<Bullet>();
    [HideInInspector] public List<Bullet> tempPlayerBulletInUse = new List<Bullet>();
    [HideInInspector] public List<WaterMissile> enemyWaterMissilePool = new List<WaterMissile>();
    [HideInInspector] public List<HomingMissile> enemyHomingMissilePool = new List<HomingMissile>();
    [HideInInspector] public List<Mine> enemyMinePool = new List<Mine>();
    [HideInInspector] public List<Bomb> enemyBombPool = new List<Bomb>();
    [HideInInspector] public List<EnemyMissile> enemyMissilesInUse = new List<EnemyMissile>();
    [HideInInspector] public List<Bullet> enemyBulletPool = new List<Bullet>();
    [HideInInspector] public List<Bullet> enemyBulletInUse = new List<Bullet>();
    [HideInInspector] public List<PlayerMissile> playerMissilePool = new List<PlayerMissile>();
    [HideInInspector] public List<PlayerMissile> playerMissilesInUse = new List<PlayerMissile>();

    [HideInInspector] public List<SkyFireStruct> skyFireBoomPool = new List<SkyFireStruct>();


    [HideInInspector] public List<Sidekick> sidekicksList = new List<Sidekick>();

    [Header("Explosions")]

    public Explosions explosions;
    [SerializeField] private int explosionsLimit;
    [SerializeField] GameObject explosionPrefab;
    [SerializeField] GameObject explosionsContainer;
    [SerializeField] GameObject wxrExplosionAttackPrefab;
    [SerializeField] GameObject wxrExplosionAttackContainer;
    [SerializeField] private int waterExplosionsLimit;
    [SerializeField] private Explosion waterExplosionPrefab;
    [SerializeField] private int specialExplosionsLimit;
    [SerializeField] private Explosion specialExplosionPrefab;
    [SerializeField] private int debrisPoolLimit;
    [SerializeField] private Gibs debrisPrefab;
    [SerializeField] private Transform debrisPoolContainer;

    [HideInInspector] public List<Explosion> explosionsList = new List<Explosion>();
    [HideInInspector] public List<Explosion> waterExplosionsList = new List<Explosion>();
    [HideInInspector] public List<Explosion> specialExplosionsList = new List<Explosion>();
    [HideInInspector] public List<Gibs> debrisList = new List<Gibs>();
    [HideInInspector] public List<WXR_ExplosionAttack> wxrExplosionsAttackList = new List<WXR_ExplosionAttack>();
    [HideInInspector] public List<WXR_ExplosionAttack> wxrExplosionsAttackInUse = new List<WXR_ExplosionAttack>();

    [Header("Enemy Fall")]

    [SerializeField] private EnemyFall enemyFallPrefab;
    [SerializeField] private Transform enemyFallPoolContainer;
    [SerializeField] private int enemyFallLimit;

    [HideInInspector] public List<EnemyFall> enemyFallList = new List<EnemyFall>();

    //Enemy List
    [HideInInspector] public List<Enemy> enemyList = new List<Enemy>();
    //所有战斗中额外生成的影响战斗结束的对象
    [HideInInspector] public List<MonoBehaviour> allBattleScript = new List<MonoBehaviour>();

    //祖玛enemy list
    [HideInInspector] public List<List<Enemy>> ZumaEnemies = new List<List<Enemy>>();
    //祖玛路径坐标点list
    [HideInInspector] public List<List<Vector2>> ZumaPathsPosList = new List<List<Vector2>>();
    private void Awake()
    {
        Init();
    }

    private void Init()
    {
        Instance = this;
        coinsPool = coinPoolProperties.GetCoinPool();
        playerBulletPool = bulletPoolProperties.GetBulletPool(true);
        enemyBulletPool = bulletPoolProperties.GetBulletPool(false);
        enemyWaterMissilePool = enemyMissilePoolProperties.GetWaterMissilePool(enemyMissileLayer);
        enemyHomingMissilePool = enemyMissilePoolProperties.GetHomingMissilePool(enemyMissileLayer);
        enemyMinePool = enemyMissilePoolProperties.GetMinePool(enemyMissileLayer);
        enemyBombPool = enemyMissilePoolProperties.GetBombPool(enemyMissileLayer);
        CreateExplosionsPool();
        CreatePlayerMissilePool();
        CreateWaterExplosionsPool();
        CreateSpecialExplosionsPool();
        CreateDebrisPool();
        CreateWXRExplosionsAttackPool();
        //CreateEnemyFallPool();
    }

    /// <summary>
    /// 获取碰到指定环形区域的敌人
    /// </summary>
    /// <param name="point">圆心</param>
    /// <param name="minRadius">内径</param>
    /// <param name="maxRadius">外径</param>
    /// <returns></returns>
    public List<Enemy> GetEnemiesTouchRing(Vector2 point, float minRadius, float maxRadius)
    {
        return enemyList.Where(x =>
        {
            if (!x.isDestroyed)
            {
                var distance = Vector2.Distance(point, x.transform.position) - x.enemyCollisionRadius;
                return distance >= minRadius && distance <= maxRadius;
            }
            return false;
        }).ToList();
    }

    /// <summary>
    /// 获取碰到激光的敌人
    /// </summary>
    /// <param name="transform">激光的transform</param>
    /// <param name="lenght">激光长度(矩形宽度)</param>
    /// <param name="width">激光宽度(矩形高度)</param>
    /// <remarks>该矩形是以transform的position作为左边的中点</remarks>
    public List<Enemy> GetEnemiesTouchLaser(Transform transform, float lenght, float width)
    {
        //Debug.Log($"激光坐标:{transform.position} 长:{lenght}, 宽:{width}, 角度:{transform.GetRotation()}");

        var rtn = enemyList.Where(x => !x.isDestroyed && x.canCheckDistance
            && transform.LaserToCircleIntersect2D(lenght, width, x.transform.position, x.enemyCollisionRadius))
            .ToList();

        //rtn.ForEach(x =>
        //{
        //    Debug.Log($"敌人坐标:{x.transform.position}");
        //});

        return rtn;
    }

    /// <summary>
    /// 获取碰到激光的敌人
    /// </summary>
    /// <param name="sectorLaser">激光的transform</param>
    public List<Enemy> GetEnemiesTouchLaser(Transform sectorLaser)
    {
        List<Enemy> enemys = new List<Enemy>();
        Mesh mesh = sectorLaser.GetComponent<MeshFilter>().sharedMesh;
        if (mesh == null) return enemys;
        Vector3[] points = mesh.vertices;
        for (int i = 0; i < points.Length; i++)
        {
            Vector3 pos = points[i];
            points[i] = sectorLaser.TransformPoint(pos);
            //Debug.Log("points：" + points[i]);

        }
        for (int i=0;i < enemyList.Count;i++)
        {
            Enemy e = enemyList[i];
            if (IsINTriangle(e.transform.position, points[0], points[1], points[2]))
            {
                enemys.Add(e);
            }
            //if (mesh.bounds.Contains(e.transform.localPosition))
            //{
            //    enemys.Add(e);
            //}
        }

        return enemys;
    }

    /// <summary>
    /// 判断点是否在三角形内
    /// </summary>
    /// <param name="point">敌人坐标</param>
    /// <param name="v0">点1</param>
    /// <param name="v1">点2</param>
    /// <param name="v2">点3</param>
    /// <returns></returns>
    private bool IsINTriangle(Vector3 point, Vector3 v0, Vector3 v1, Vector3 v2)
    {
        float x = point.x;
        float y = point.y;

        float v0x = v0.x;
        float v0y = v0.y;

        float v1x = v1.x;
        float v1y = v1.y;

        float v2x = v2.x;
        float v2y = v2.y;
        if ((pointInTriangle(v0x, v0y, v1x, v1y, v2x, v2y, x, y)))
            return true;
        return false;
    }
    private bool pointInTriangle(float x1, float y1, float x2, float y2, float x3, float y3, float x, float y)
    {
        float denominator = ((y2 - y3) * (x1 - x3) + (x3 - x2) * (y1 - y3));
        float a = ((y2 - y3) * (x - x3) + (x3 - x2) * (y - y3)) / denominator;
        float b = ((y3 - y1) * (x - x3) + (x1 - x3) * (y - y3)) / denominator;
        float c = 1 - a - b;

        return 0 <= a && a <= 1 && 0 <= b && b <= 1 && 0 <= c && c <= 1;
    }

    /// <summary>
    /// 将子弹从列表中移除(分玩家或敌人两个列表)
    /// </summary>
    public void RemoveFromList(Bullet bullet)
    {
        if (playerBulletInUse.Contains(bullet))
        {
            playerBulletInUse.Remove(bullet);
        }
        else if (enemyBulletInUse.Contains(bullet))
        {
            enemyBulletInUse.Remove(bullet);
        }
    }

    public void RemoveCoinsFromList(Coin coin)
    {
        if (coinsInUse.Contains(coin))
        {
            coinsInUse.Remove(coin);
        }
        if (extenCoinsInUse.Contains(coin))
        {
            extenCoinsInUse.Remove(coin);
        }
    }
    public int CoinsFromListCount()
    {
        return coinsInUse.Count;
    }

    public void RemovePlayerBullet(Bullet playerBullet)
    {
        if (playerBullet.Type == Bullet.BulletType.FrontMachineGun || playerBullet.Type == Bullet.BulletType.FrontMultiCanon)
        {
            if (playerBullet.hasHit)
            {
                //if (playerBullet->getPlaySound())
                //{
                //    Shared::playSound("res/Sounds/SFX/enemyBeingHit.mp3", playerBullet->bulletSprite->getPosition());
                //}
                //TODO Distance Sound
                AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.enemyBeingHit, 1f, playerBullet.transform.position);
                playerBullet.RemoveWithSplash();
            }
        }

        else if (playerBullet.Type == Bullet.BulletType.ProtonCanon)
        {
            //Sprite* blast = Sprite::createWithSpriteFrameName("blueBlast_00000.png");
            //blast->runAction(Sequence::create(Shared::createAnimation("blueBlast_0000%d.png", 0, 6, false), RemoveSelf::create(), NULL));
            //this->addChild(blast);
            //blast->setPosition(playerBullet->bulletSprite->getPosition());
            //blast->setCameraMask(GAMECAMERA);
            //blast->setScale(2);

            //SkeletonAnimation* blast2 = SkeletonAnimation::createWithJsonFile("res/Arsenal/blast.json", "res/Arsenal/blast.atlas");
            //blast2->setAnimation(0, "plasmaBlast", true);
            //blast2->runAction(Sequence::create(ScaleTo::create(0.2f, 2), DelayTime::create(0.1f), RemoveSelf::create(), NULL));
            //blast2->runAction(FadeOut::create(0.3f));
            //this->addChild(blast2, 2);
            //blast2->setPosition(playerBullet->bulletSprite->getPosition().x, playerBullet->bulletSprite->getPosition().y);
            //blast2->setCameraMask(GAMECAMERA);




            //removeBulletWithSplash(false, playerBullet);
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.enemyHit, 1f, playerBullet.transform.position);
            // AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.enemyHit);
            //Globals.PlaySound("res/Sounds/SFX/enemyHit.mp3");

            playerBullet.RemoveProtonBullet();
        }

        //if (playerBullet.Type == Bullet.BulletType.FrontPlasma)
        //{
        //    GameSharedData::getInstance()->playerBullets.eraseObject(playerBullet);
        //    playerBullet->bulletSprite->runAction(ScaleTo::create(0.5, 0));
        //    playerBullet->runAction(Sequence::create(DelayTime::create(0.5), RemoveSelf::create(), NULL));
        //}

        else if (playerBullet.Type == Bullet.BulletType.FrontRocket)
        {
            //float radius = 0.5 + FileHandler::getInstance()->rocketLevel * 1.25;
            float radius = 0.5f + GameData.instance.fileHandler.rocketLevel * 1.25f;
            //if (FileHandler::getInstance()->_frontGunStr != kRocket)
            //{
            //    radius = 1.5 * 5;
            //}

            float newVal = radius / 4;

            int countEnemies = 0;
            bool endLoop = false;

            playerBullet.HasHit();

        reLoop:
            foreach (Enemy enemy in GameSharedData.Instance.enemyList)
            {
                if (Vector2.SqrMagnitude(playerBullet.transform.position - enemy.transform.position) < newVal)
                {

                    countEnemies++;
                    if (countEnemies == 5)
                    {
                        //endLoop = true;
                        break;
                    }
                    if (enemy.TakeHit(playerBullet.getDamage() / countEnemies))
                    {
                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            GameManager.instance.physicsManager.DestroyEnemy(enemy);
                            //break;
                        }
                        //if (!enemy.isDestroyed)
                        //{
                        //    enemy.isDestroyed = true;
                        //    enemy.Destroy();
                        //}
                        goto reLoop;

                    }

                }
            }


        }

        else if (playerBullet.Type == Bullet.BulletType.FrontPlasma)
        {
            AudioManager.instance.PlaySound(AudioType.ThreeD, Constants_Audio.Audio.enemyHit, 1f, playerBullet.transform.position);
            playerBullet.RemovePlasmaBullet();
        }
        else if (playerBullet.Type == Bullet.BulletType.HammerBullet)
        {
            //LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6022);
            GameObject go = GetPrefabByType(DefaultGameObjectType.BoomerangEffect);
            go.transform.localPosition = playerBullet.transform.localPosition;
            go.SetActive(true);
            DOTween.Sequence().SetId("ex").AppendInterval(0.5f).AppendCallback(() =>
            {
                go.SetActive(false);

            }).Play();
            playerBullet.ResetBullet();
        }
    }

    private void CreateExplosionsPool()
    {
        for (int i = 0; i < explosionsLimit; i++)
        {
            GameObject explosion = Instantiate(explosionPrefab);
            explosion.transform.parent = explosionsContainer.transform;
            Explosion exp = explosion.GetComponent<Explosion>();
            explosionsList.Add(exp);
            exp.Reset();
        }
    }
    private void CreateWXRExplosionsAttackPool()
    {
        for (int i = 0; i < explosionsLimit; i++)
        {
            GameObject explosion = Instantiate(wxrExplosionAttackPrefab);
            explosion.transform.parent = wxrExplosionAttackContainer.transform;
            WXR_ExplosionAttack exp = explosion.GetComponent<WXR_ExplosionAttack>();
            wxrExplosionsAttackList.Add(exp);
            exp.Reset();
        }
    }

    private void CreatePlayerMissilePool()
    {
        for (int i = 0; i < playerMissileLimit; i++)
        {
            GameObject missile = Instantiate(playerMissilePrefab, playerMissileContainer.transform);
            var miss = missile.GetComponent<PlayerMissile>();
            playerMissilePool.Add(miss);
            miss.ResetMissile();
        }
    }

    private void CreateWaterExplosionsPool()
    {
        for (int i = 0; i < waterExplosionsLimit; i++)
        {
            Explosion explosion = Instantiate(waterExplosionPrefab, Vector2.zero, Quaternion.identity, explosionsContainer.transform);
            waterExplosionsList.Add(explosion);
            explosion.Reset();
        }
    }

    private void CreateSpecialExplosionsPool()
    {
        for (int i = 0; i < specialExplosionsLimit; i++)
        {
            Explosion explosion = Instantiate(specialExplosionPrefab, Vector2.zero, Quaternion.identity, explosionsContainer.transform);
            specialExplosionsList.Add(explosion);
            explosion.Reset();
        }
    }

    private void CreateDebrisPool()
    {
        // for (int i = 0; i < debrisPoolLimit; i++)
        // {
        //     Gibs debris = Instantiate(debrisPrefab, Vector2.zero, Quaternion.identity, debrisPoolContainer);
        //     debrisList.Add(debris);
        //     debris.Reset();
        // }
    }

    private void CreateEnemyFallPool()
    {
        for (int i = 0; i < enemyFallLimit; i++)
        {
            EnemyFall enemyFall = Instantiate(enemyFallPrefab, Vector2.zero, Quaternion.identity, enemyFallPoolContainer);
            enemyFallList.Add(enemyFall);
        }
    }

    public void ClearEnemyBullets()
    {
        enemyBulletInUse.ForEach(b => b.ResetBullet());
    }

    #region 新版对象池相关的 临时的 后面用动态加载
    public PrefabProperties[] prefabConfig;
    private Dictionary<DefaultGameObjectType, GameObject> PrefabMap;
    public GameObject GetPrefabByType(DefaultGameObjectType type)
    {
        if (PrefabMap == null)
        {
            PrefabMap = new Dictionary<DefaultGameObjectType, GameObject>();
        }
        if (!PrefabMap.ContainsKey(type))
        {
            foreach (var item in prefabConfig)
            {
                if (item.Type == type)
                {
                    PrefabMap.Add(type, item.prefab);
                }
            }
        }
        return GameObjectPoolManager.Instance.GetItemFromPooler(PrefabMap[type]);
    }

    public GameObject GetBulletPrefab(DefaultGameObjectType type, bool isPlayer = true)
    {
        //Debug.Log("获取的" + type);
        GameObject go = GetPrefabByType(type);
        Bullet bullet = go.GetComponent<Bullet>();
        bullet.isRemovable = true;
        bullet.layerName = isPlayer ? "PlayerBullet" : "EnemyBullet";
        return go;
    }





    #endregion


}

[System.Serializable]
public class BulletPoolCreator
{
    [SerializeField] private Bullet bulletPrefab;
    [SerializeField] private int playerBulletPoolLimit;
    [SerializeField] private int enemyBulletPoolLimit;
    [SerializeField] private Transform playerBulletContainer;
    [SerializeField] private Transform enemyBulletContainer;
    [SerializeField] private string playerBulletLayer;
    [SerializeField] private string enemyBulletLayer;

    public List<Bullet> GetBulletPool(bool isPlayerPool = false)
    {
        List<Bullet> bulletList = new List<Bullet>();
        Bullet b;
        int limit = isPlayerPool ? playerBulletPoolLimit : enemyBulletPoolLimit;
        for (int i = 0; i < limit; i++)
        {
            b = Bullet.Instantiate(bulletPrefab, Vector2.zero, Quaternion.identity, isPlayerPool ? playerBulletContainer : enemyBulletContainer);
            b.isRemovable = true;
            b.layerName = isPlayerPool ? playerBulletLayer : enemyBulletLayer;
            b.orderInLayer = i;
            bulletList.Add(b);
        }

        return bulletList;
    }

}

[System.Serializable]
public class CoinPoolCreator
{
    [SerializeField] private Coin coinPrefab;
    [SerializeField] private int coinPoolLimit;
    [SerializeField] private Transform coinContainer;

    public List<Coin> GetCoinPool()
    {
        List<Coin> coinList = new List<Coin>();
        Coin c;
        int limit = coinPoolLimit;
        for (int i = 0; i < limit; i++)
        {
            c = Coin.Instantiate(coinPrefab, Vector2.zero, Quaternion.identity, coinContainer);
            c.isRemovable = true;

            coinList.Add(c);
        }
        return coinList;
    }
}

[System.Serializable]
public class EnemyMissilePoolCreator
{
    [SerializeField] private WaterMissile waterMissilePrefab;
    [SerializeField] private int waterMissilePoolLimit;
    [SerializeField] private Transform waterMissilePoolParent;
    [SerializeField] private HomingMissile homingMissilePrefab;
    [SerializeField] private int homingMissilePoolLimit;
    [SerializeField] private Transform homingMissilePoolParent;
    [SerializeField] private Bomb bombPrefab;
    [SerializeField] private int bombPoolLimit;
    [SerializeField] private Transform bombPoolParent;
    [SerializeField] private Mine minePrefab;
    [SerializeField] private int minePoolLimit;
    [SerializeField] private Transform minePoolParent;

    public List<WaterMissile> GetWaterMissilePool(string layer)
    {
        List<WaterMissile> waterMissileList = new List<WaterMissile>();
        WaterMissile w;

        for (int i = 0; i < waterMissilePoolLimit; i++)
        {
            w = WaterMissile.Instantiate(waterMissilePrefab, Vector2.zero, Quaternion.identity, waterMissilePoolParent);

            w.missileSprite.sortingLayerName = layer;
            w.missileSprite.sortingOrder = i;
            waterMissileList.Add(w);
        }

        return waterMissileList;
    }

    public List<HomingMissile> GetHomingMissilePool(string layer)
    {
        List<HomingMissile> homingMissileList = new List<HomingMissile>();
        HomingMissile h;

        for (int i = 0; i < homingMissilePoolLimit; i++)
        {
            h = HomingMissile.Instantiate(homingMissilePrefab, Vector2.zero, Quaternion.identity, homingMissilePoolParent);

            h.missileSprite.sortingLayerName = layer;
            h.missileSprite.sortingOrder = i;
            homingMissileList.Add(h);
        }

        return homingMissileList;
    }

    public List<Mine> GetMinePool(string layer)
    {
        List<Mine> mineList = new List<Mine>();
        Mine m;

        for (int i = 0; i < minePoolLimit; i++)
        {
            m = Mine.Instantiate(minePrefab, Vector2.zero, Quaternion.identity, minePoolParent);
            m.missileSprite.sortingLayerName = layer;
            m.missileSprite.sortingOrder = i;
            mineList.Add(m);
        }

        return mineList;
    }

    public List<Bomb> GetBombPool(string layer)
    {
        List<Bomb> bombList = new List<Bomb>();
        Bomb b;

        for (int i = 0; i < bombPoolLimit; i++)
        {
            b = Bomb.Instantiate(bombPrefab, Vector2.zero, Quaternion.identity, bombPoolParent);
            b.missileSprite.sortingLayerName = layer;
            b.missileSprite.sortingOrder = i;
            bombList.Add(b);
        }

        return bombList;
    }




}