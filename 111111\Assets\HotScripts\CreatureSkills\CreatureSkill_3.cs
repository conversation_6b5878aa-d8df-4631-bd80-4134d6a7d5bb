﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Apq;
using Apq.Extension;
using Cysharp.Threading.Tasks;
using HotScripts;
using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 激光束
    /// </summary>
    public class CreatureSkill_3 : CreatureSkillBase
    {
        ///// <summary>
        ///// 预制件:激光端点
        ///// </summary>
        //public GameObject LaserPrefab_Impact { get; set; }
        ///// <summary>
        ///// 预制件:激光线条(矩形)
        ///// </summary>
        //public GameObject LaserPrefab_Rect { get; set; }

        /// <summary>
        /// 发出的激光
        /// </summary>
        public List<BulletProps> Lasers { get; } = new();

        public override void ClearSkill()
        {
            base.ClearSkill();

            foreach (var laser in Lasers)
            {
                laser.Dispose();
            }
        }

        public override async UniTaskVoid DoSkill()
        {
            base.DoSkill().Forget();
            try
            {
                await UniTask.SwitchToMainThread();

                // 激光条数
                var laserQty = Skill.连射次数.Value;

                // 分裂深度、依次条数
                var depth = (int)Skill.子弹最大分裂次数.Value;
                var dNums = Skill.CsvRow_CatSkill.BulletSeparates.ToList();
                for (var i = 0; i < dNums.Count; i++)
                {
                    dNums[i] += (int)Skill.TotalEffect.击中敌人后子弹分裂数量.Value;
                }

                dNums.Insert(0, (int)laserQty);

                // 激光最大长度、宽度
                var maxLength = Skill.攻击距离.Value;
                var width = 2 * Skill.Get子弹半径();

                // 持续时长：秒
                var attackDuration = Skill.持续时长.Value;
                // 至少持续时长：秒
                var minAttackDuration = Skill.MinAttackDuration.Value;
                // 造成伤害的最小间隔时长:秒
                var minDamageInterval = Skill.MinDamageInterval.Value;

                // 超时令牌
                var CTS_timeout = new CancellationTokenSource();
                CTS_timeout.CancelAfterSlim(System.TimeSpan.FromSeconds(attackDuration));

                // 当持续时长够了或玩家死了，自动结束。(但对于发出的激光,至少要存在最小持续时长)
                var cts_Shoot = CancellationTokenSource.CreateLinkedTokenSource(
                    CTS_timeout.Token,
                    Creature.GetCancellationTokenOnDestroy()
                );
                CTS_Shoots.Add(cts_Shoot);

                // 开火声音
                AudioPlayer.Instance.PlaySound(Skill.CsvRow_CatSkill.ShootSound).Forget();

                // 每条激光开启一个任务
                for (var i = 0; i < laserQty; i++)
                {
                    DoLaser(cts_Shoot.Token, i, depth, dNums, maxLength, width,
                        attackDuration, minAttackDuration, minDamageInterval).Forget();
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        /// 一条激光
        /// </summary>
        /// <param name="laserNo">激光编号(0开始)</param>
        /// <param name="depth">分裂树的深度(第一层为0)</param>
        /// <param name="dNums">依次分裂的条数</param>
        /// <param name="maxLength">激光最大长度</param>
        /// <param name="width">激光宽度</param>
        /// <param name="attackDuration">持续时长：秒</param>
        /// <param name="minAttackDuration">至少持续时长</param>
        /// <param name="minDamageInterval">造成伤害的最小间隔时长:秒</param>
        private async UniTaskVoid DoLaser(CancellationToken token,
            int laserNo,
            int depth,
            IList<int> dNums,
            float maxLength,
            float width,
            float attackDuration,
            float minAttackDuration,
            float minDamageInterval)
        {
            //await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);
            await UniTask.SwitchToMainThread();

            // 代表一条激光
            var laser = new BulletProps()
            {
                CreatureSkill = this,
                LaserDamageType = 1,
                //将发出的激光保存到LaserLines中
                //LaserLines,
            };
            Lasers.Add(laser);

            // 攻击的结束时间
            var endTime = Time.time + attackDuration;

            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested) break;
                    if (Time.time >= Preset_FinshTime) break;
                    if (Time.deltaTime <= 0) continue;

                    await DoLaser_Eject(token, laser,
                        depth, dNums, maxLength, width,
                        endTime,
                        minAttackDuration,
                        minDamageInterval);
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
            {
                laser.Dispose();
                Lasers.Remove(laser);
            }
        }

        /// <summary>
        /// 锁定攻击最近一个怪,直至怪死亡或时长用完。
        /// 没有攻击对象则会立即退出。
        /// </summary>
        /// <param name="depth">分裂树的深度(第一层为0)</param>
        /// <param name="dNums">依次分裂的条数</param>
        /// <param name="maxLength">激光最大长度</param>
        /// <param name="width">激光宽度</param>
        /// <param name="endTime">攻击结束时间</param>
        /// <param name="minAttackDuration">至少持续时长(秒)</param>
        /// <param name="minDamageInterval">持续伤害间隔(秒)</param>
        private async UniTask DoLaser_Eject(CancellationToken token,
            BulletProps laser,
            int depth,
            IList<int> dNums,
            float maxLength,
            float width,
            float endTime,
            float minAttackDuration,
            float minDamageInterval
        )
        {
            // 这条激光分裂树(第0层就是根节点，但怪可以为null, 其后代节点就一定要有怪了)
            TreeNode<CreatureBase> tree_Separate = new();
            var pDepth = 0; // 分裂树的深度
            List<Enemy> enemies = new(); //分裂树中的怪
            // 实际结束时间
            var eTime = endTime;

            try
            {
                // 攻击到结束时间或技能被强停
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested) break;
                    if (Time.time >= eTime) break;
                    if (Time.deltaTime <= 0) continue;

                    // 分裂树是否为重建的
                    var isBuild = false;

                    // 如果分裂树里 没怪 或 有怪死了，就重新生成分裂树
                    if (enemies.Count == 0 || enemies.Any(x => !x))
                    {
                        enemies.Clear();
                        isBuild = true;
                        pDepth = 0;
                        BuildSeparateTree(tree_Separate, laser,
                            Creature.transform.position, maxLength, depth, dNums, (n) =>
                            {
                                if (pDepth < n.LayerNo) pDepth = n.LayerNo;
                                enemies.Add(n.Data as Enemy);
                            });
                    }

                    // 如果当前分裂树没重建，且深度没用完，就从叶节点再寻一次怪
                    if (!isBuild && pDepth < depth)
                    {
                        foreach (var leaf in tree_Separate.GetLeafs())
                        {
                            FillChildren_SeparateTree(leaf, depth, laser,
                                leaf.Data.transform.position, maxLength, dNums, (n) =>
                                {
                                    if (pDepth < n.LayerNo) pDepth = n.LayerNo;
                                    enemies.Add(n.Data as Enemy);
                                });
                        }
                    }

                    // 先隐藏所有激光,再依次显示
                    foreach (var laserLine in laser.LaserLines)
                    {
                        laserLine.SetLaserActive(false);
                    }

                    // 分裂树里没怪
                    if (enemies.Count <= 0) continue;

                    // 分裂树里有怪,将激光刷新到界面

                    // 调整实际结束时间(至少持续配置的最小时长)
                    if (eTime <= endTime) //这个判断,确保最多调整一次
                    {
                        var minEndTime = Time.time + minAttackDuration;
                        if (endTime < minEndTime)
                        {
                            eTime = minEndTime;
                        }
                    }

                    // 如果激光不够,创建激光
                    {
                        var t = enemies.Count - laser.LaserLines.Count;
                        for (var i = 0; i < t; i++)
                        {
                            var laserLine = CreatureBase.CreateLaser(
                                Creature.transform.position,
                                Vector3.one,
                                GameManager.instance.player.weapon.laserPrefab,
                                GameManager.instance.player.weapon.laserImpactPrefab);
                            laserLine.SetLaserActive(false);
                            var sr = laserLine.LaserMiddle.GetComponent<SpriteRenderer>();
                            sr.size = new Vector2(maxLength, width); // 设置激光宽度
                            laserLine.LaserMiddle.GetComponent<MaterialMovement>().ResetOffset();
                            laser.LaserLines.Add(laserLine);
                        }
                    }

                    // 显示激光分裂树
                    ShowLaser(tree_Separate, laser, Creature.transform.position, width);

                    if (laser.NextHitEnemyTime.Value > Time.time) continue;

                    // 可以造成伤害的时间到了,则造成一次伤害
                    laser.NextHitEnemyTime.Value = Time.time + minDamageInterval;
                    //Debug.Log($"激光3下次伤害时间: {laser.NextHitEnemyTime.Value}");

                    var maxRadius = laser.DamageEnemy(enemies.ConvertAll(x => x as CreatureBase));
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
            {
                // 隐藏所有用到的激光段
                foreach (var laserLine in laser.LaserLines)
                {
                    laserLine.gameObject.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 显示激光分裂树
        /// </summary>
        /// <param name="laser">哪条激光</param>
        /// <param name="pBegin">激光的起点位置</param>
        /// <param name="width">激光宽度</param>
        protected void ShowLaser(TreeNode<CreatureBase> tree_Separate,
            BulletProps laser,
            Vector3 pBegin,
            float width)
        {
            // 父节点 到 子节点 的连线
            tree_Separate.Walk_Depth((n, i) =>
            {
                var p1 = pBegin;
                if (n.Parent != null)
                {
                    p1 = n.Parent.Data.transform.position;
                }

                var p2 = n.Data.transform.position;

                if (laser.LaserLines.Count <= i) return;

                var laserLine = laser.LaserLines[i];
                // 更新 起止位置 和 激光的方向、长度
                laserLine.LaserBegin.transform.position = p1;
                laserLine.LaserMiddle.transform.position = p1;
                laserLine.LaserEnd.transform.position = p2;
                var dir = p2 - p1;
                var dir_1 = dir.normalized;
                laserLine.LaserBegin.transform.right = dir_1;
                laserLine.LaserMiddle.transform.right = dir_1;
                laserLine.LaserEnd.transform.right = -dir_1;
                var distance = dir.magnitude;
                laserLine.LaserMiddle.GetComponent<SpriteRenderer>().size = new Vector2(distance, width);
                laserLine.LaserMiddle.GetComponent<MaterialMovement>().OffsetUpdate();
                laserLine.SetLaserActive(true);
            });
        }

        /// <summary>
        /// 起点圆为中心,在攻击距离内,按分裂方式锁定没被 指定激光 锁定的最近的怪，生成depth深度的分裂树
        /// </summary>
        /// <param name="laser">一条激光</param>
        /// <param name="pos">圆心</param>
        /// <param name="distance">激光的最大长度</param>
        /// <param name="depth">分裂树的深度(第一层为0)</param>
        /// <param name="dNums">依次分裂的条数</param>
        /// <param name="onAdd">(新节点,层数) 分裂树中添加了新节点后 回调</param>
        /// <returns></returns>
        protected void BuildSeparateTree(TreeNode<CreatureBase> tree,
            BulletProps laser,
            Vector3 pos, float distance, int depth,
            IList<int> dNums,
            System.Action<TreeNode<CreatureBase>> onAdd)
        {
            tree.Children.Clear();
            tree.Data = null; // 这条激光攻击的第一只怪
            tree.LayerNo = 0;

            var enemy = GameSharedData.Instance.enemyList.Where(x => x
                                                                     // 只找未被本次技能锁定的怪
                                                                     && !x.LockedBySkills.Contains(this))
                .Select(x => new
                {
                    Enemy = x,
                    Distance = x ? (x.transform.position - pos).magnitude : float.PositiveInfinity,
                })
                //.Where(x => x.Distance <= distance)
                .Where(x => x.Distance <= Skill.攻击距离.Value)
                .OrderBy(x => x.Distance)
                .FirstOrDefault()?.Enemy;
            if (!enemy) return;

            enemy.LockedBySkills.Add(this);
            //if (!enemy.LockedByBullets.Contains(laser))
            //{
            //    enemy.LockedByBullets.Add(laser);
            //}
            tree.Data = enemy;
            onAdd?.Invoke(tree);

            FillChildren_SeparateTree(tree, depth, laser, pos, distance, dNums, onAdd);
        }

        /// <summary>
        /// [递归]填充分裂树的子级(广度优先)
        /// </summary>
        /// <param name="node">分裂树的节点</param>
        /// <param name="depth">分裂树的深度(第一层为0)</param>
        /// <param name="laser">一条激光</param>
        /// <param name="pos">圆心</param>
        /// <param name="distance">激光的最大长度</param>
        /// <param name="dNums">依次分裂的条数</param>
        /// <param name="onAdd">(新节点,层数) 分裂树中添加了新节点后 回调</param>
        private static void FillChildren_SeparateTree(TreeNode<CreatureBase> node, int depth,
            BulletProps laser,
            Vector3 pos, float distance,
            IList<int> dNums,
            System.Action<TreeNode<CreatureBase>> onAdd)
        {
            if (node.LayerNo >= depth) return;

            // 需要找多少个怪
            var mNum = dNums.IndexOf_ByCycle(node.LayerNo);

            // 找出随机的 mNum 个怪
            var enemies = GameSharedData.Instance.enemyList.Where(x => x
                                                                       // 只找未被本次技能锁定的怪
                                                                       && !x.LockedBySkills.Contains(
                                                                           laser.CreatureSkill))
                .Select(x => new
                {
                    Enemy = x,
                    Distance = x ? (x.transform.position - pos).magnitude : float.PositiveInfinity,
                })
                //.Where(x => x.Distance <= distance)  激光分裂找目标怪物大小，攻击距离的X分之一
                .Where(x => x.Distance <= distance / 2)
                //.OrderBy(x => x.Distance)
                .OrderBy(x => RandomNum.RandomInt())
                .Take(mNum)
                .Select(x => x.Enemy).ToList();

            foreach (var enemy in enemies)
            {
                enemy.LockedBySkills.Add(laser.CreatureSkill);
                //if (!enemy.LockedByBullets.Contains(laser))
                //{
                //    enemy.LockedByBullets.Add(laser);
                //}

                var cNode = new TreeNode<CreatureBase>()
                {
                    Parent = node,
                    Data = enemy,
                    LayerNo = node.LayerNo + 1,
                };

                node.Children.Add(cNode);
                onAdd?.Invoke(cNode);
            }

            foreach (var cNode in node.Children)
            {
                FillChildren_SeparateTree(cNode, depth, laser,
                    cNode.Data.transform.position, distance,
                    dNums, onAdd);
            }
        }
    }
}