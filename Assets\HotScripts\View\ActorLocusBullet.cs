// ReSharper disable ClassWithVirtualMembersNeverInherited.Global
// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using HotScripts;

using Props;

using RxEventsM2V;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     玩家阵营的子弹:直线轨迹预示
    /// </summary>
    public class ActorLocusBullet : ActorBulletBase
    {
        /// <summary>
        ///     清空轨迹(销毁)
        /// </summary>
        public void ClearLocus()
        {
            try
            {
                for (int i = LocusRoot.transform.childCount - 1; i >= 0; i--)
                {
                    try
                    {
                        Destroy(LocusRoot.transform.GetChild(i).gameObject);
                    }
                    catch
                    {
                        // ignore
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     生成并显示跟随敌人的轨迹
        /// </summary>
        public async UniTask ShowTrackEnemyLocus(Color bulletLocusColor, float bulletLocusWidth,
            double bulletLocusTrackEnemyDuration)
        {
            try
            {
                // 轨迹(跟随敌人)
                if (bulletLocusTrackEnemyDuration > 0)
                {
                    CancellationTokenSource cts_TrackEnd = new();
                    CancellationToken token_TrackEnd = cts_TrackEnd.Token;
                    cts_TrackEnd.CancelAfterSlim(TimeSpan.FromSeconds(bulletLocusTrackEnemyDuration));

                    for (;; await UniTask.NextFrame())
                    {
                        // 生成线段轨迹数据
                        await GenStraightLocus();
                        ShowLocus_One(new Color(bulletLocusColor[0], bulletLocusColor[1], bulletLocusColor[2],
                            bulletLocusColor[3]), bulletLocusWidth).Forget();
                        if (token_TrackEnd.IsCancellationRequested)
                        {
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                ClearLocus();
            }
        }

        /// <summary>
        ///     生成并显示最终轨迹(固定)
        /// </summary>
        public async UniTaskVoid ShowLocus(Color bulletLocusColor, float bulletLocusWidth, double bulletLocusDuration)
        {
            try
            {
                if (bulletLocusDuration > 0)
                {
                    CancellationTokenSource cts_LocusEnd = new();
                    CancellationToken token_LocusEnd = cts_LocusEnd.Token;
                    cts_LocusEnd.CancelAfterSlim(TimeSpan.FromSeconds(bulletLocusDuration));

                    ShowLocus_One(bulletLocusColor, bulletLocusWidth).Forget();
                    await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration),
                        cancellationToken: token_LocusEnd);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                ClearLocus();
            }
        }

        /// <summary>
        ///     显示一次轨迹
        /// </summary>
        public async UniTaskVoid ShowLocus_One(Color color, float width)
        {
            ClearLocus();
            // 每条轨迹线段显示为一个图片
            List<LineSegment> straightLocus = StraightLocus.ToList();
            foreach (LineSegment lineSegment in straightLocus)
            {
                try
                {
                    GameObject gObj = new("Locus")
                    {
                        transform =
                        {
                            parent = LocusRoot.transform,
                            position = lineSegment.PosMiddle,
                            right = lineSegment.DirNormal
                        }
                    };
                    SpriteRenderer sp = gObj.GetOrAddComponent<SpriteRenderer>();
                    sp.sprite = await ResMgrAsync.LoadResAsync<Sprite>("Assets/Temp/image/Sharp/White9X9.png");
                    sp.drawMode = SpriteDrawMode.Sliced;
                    sp.color = color;
                    sp.size = new Vector2(lineSegment.Length, width);
                }
                catch
                {
                    // ignore
                }
            }
        }
        // /// <inheritdoc/>
        // public override Vector3 CalcDir_Straight()
        // {
        //     var rtn = base.CalcDir_Straight();
        //     
        //     // 找出攻击的基准方向
        //     if (BulletThing.AttackBaseDirFollowThing != null)
        //     {
        //         // 起点跟随怪物
        //         transform.position = MonsterThing.Position;
        //         rtn = BulletThing.AttackBaseDirFollowThing.Position - transform.position;
        //         if (BulletThing.Angle != 0)
        //         {
        //             rtn = rtn.RotateAround(Vector3.forward, BulletThing.Angle);
        //         }
        //     }
        //
        //     return rtn;
        // }

        #region 生成轨迹

        /// <summary>
        ///     生成子弹移动线段轨迹(按子弹方向，带反弹和转回)
        /// </summary>
        public virtual async UniTask GenStraightLocus()
        {
            StraightLocus.Clear();
            float bulletLife = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();
            if (bulletLife > 0)
            {
                float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                if (speed > 0)
                {
                    Vector3 dir_1 = CalcDir_Straight();
                    // 初始移动轨迹(按移动方向、速度、时长计算出来的线段)
                    StraightLocus.Add(new LineSegment
                    {
                        PosStart = transform.position, Dir = speed * bulletLife * dir_1
                    });

                    // 移动这个长度之后方向逆转
                    float turnBackLength = 0f;
                    float turnBack = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.TurnBack)
                        .FirstOrDefault();
                    if (turnBack > 0)
                    {
                        turnBackLength = turnBack * speed;
                    }

                    // 处理子弹移动轨迹中的反弹和转回
                    await DoBounceTurnBack(0, BulletThing.BounceTimes.Value, turnBackLength);
                }
            }
        }

        /// <summary>
        ///     处理子弹移动线段轨迹的反弹和转回
        /// </summary>
        public virtual async UniTask DoBounceTurnBack(float length, int bounceTimes,
            float turnBackLength = 0, bool hadTurnBack = false, Func<RectObstacleThing, bool> predicate = null)
        {
            bool throughObstacle = 0 != BulletThing.CdExecutor.Thing.GetTotalLong(PropType.BulletThroughObstacle)
                .FirstOrDefault();

            // 下一帧开始处理(这样每帧就只处理一条线段)
            // await UniTask.NextFrame();
            // 不可穿过的地形。默认：墙以上
            predicate ??= x => x.TerrainType >= TerrainType.Wall;

            // 只处理最后一个线段
            LineSegment line = StraightLocus.Last();

            //  不可飞越障碍物
            if (!throughObstacle)
            {
                // 射线检测 找出碰到的地形
                // ReSharper disable once Unity.PreferNonAllocApi
                RaycastHit2D[] hits = Physics2D.CircleCastAll(line.PosStart, BulletThing.TotalProp_Radius,
                    line.DirNormal,
                    line.Length, LayerMask.GetMask("Terrain"));
                if (hits is { Length: > 0 })
                {
                    // 碰到的第一个不可穿过的矩形障碍物
                    // ReSharper disable once IdentifierTypo
                    RaycastHit2D raycastHit2D = hits.FirstOrDefault(x =>
                        x && x.collider.gameObject &&
                        x.collider.gameObject.GetComponent<RectObstacle>() is
                            { RectObstacleThing: not null } rectObstacle &&
                        predicate.Invoke(rectObstacle.RectObstacleThing));
                    if (raycastHit2D)
                    {
                        // 反弹点选为接近此边(未碰到)的位置
                        Vector2 dir = raycastHit2D.point - (Vector2)line.PosStart;
                        // 可向前走的长度
                        float len = dir.magnitude - BulletThing.TotalProp_Radius - 0.3f;
                        if (len < 0)
                        {
                            len = 0;
                        }

                        // 反弹前已达到转回长度，则按转回处理
                        if (!hadTurnBack && turnBackLength > length && length + len >= turnBackLength)
                        {
                            len = turnBackLength - length;
                            length = turnBackLength;
                            // 剩余长度
                            float lenLeft = line.Length - len;
                            if (lenLeft > 0)
                            {
                                // 终点改为转回点
                                line.Dir = line.DirNormal * len;
                                // 加上转回的线段
                                LineSegment newLine = new() { PosStart = line.PosEnd, Dir = -line.DirNormal * lenLeft };
                                StraightLocus.Add(newLine);
                                hadTurnBack = true;
                            }
                        }
                        // 按反弹处理
                        else
                        {
                            // 剩余长度
                            float lenLeft = line.Length - len;
                            // 终点改为反弹点
                            line.Dir = line.DirNormal * len;

                            if (bounceTimes > 0 && lenLeft > 0)
                            {
                                // 反弹
                                bounceTimes--;
                                // 反弹后的方向
                                Vector3 bounceDir_1 = Vector3.Reflect(line.DirNormal, raycastHit2D.normal);
                                // 加上反弹后的线段
                                LineSegment newLine = new() { PosStart = line.PosEnd, Dir = bounceDir_1 * lenLeft };
                                StraightLocus.Add(newLine);
                            }
                            else
                            {
                                return;
                            }
                        }

                        // 继续处理反弹和转回
                        await DoBounceTurnBack(length, bounceTimes, turnBackLength, hadTurnBack, predicate);
                        return;
                    }
                }
            }

            // 没有碰到障碍物，但有转回
            if (turnBackLength > length && line.Length > turnBackLength)
            {
                length = turnBackLength;
                float lenLeft = line.Length - length;

                if (lenLeft > 0)
                {
                    // 终点改为转回点
                    line.Dir = line.DirNormal * turnBackLength;

                    // 加上转回的线段
                    StraightLocus.Add(new LineSegment { PosStart = line.PosEnd, Dir = -line.DirNormal * lenLeft });
                    // 继续处理反弹和转回
                    await DoBounceTurnBack(length, bounceTimes, turnBackLength, true, predicate);
                }
            }
        }

        /// <summary>
        ///     将线段轨迹拆分为时长轨迹(带反弹和转回)
        /// </summary>
        public virtual async UniTask GenStraightLocusInterval(float duration = 0.03f)
        {
            // 将线段轨迹拆分为时长轨迹
            if (StraightLocus.Count > 0)
            {
                StraightLocus_Duration.Locus.Clear();
                StraightLocus_Duration.Progress = 0;

                float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                // 当前走到的位置
                Vector3 pos = StraightLocus.First().PosStart;
                // 步长
                float step = speed * duration;
                // 当前线段已移动的长度
                float length = 0f;
                // 当前线段索引号
                int lineIdx = 0;
                while (true)
                {
                    LineSegment line = StraightLocus[lineIdx];
                    if (length + step >= line.Length)
                    {
                        // 加入最后这小段
                        LineSegment s = new() { PosStart = pos, Dir = line.DirNormal * (line.Length - length) };
                        StraightLocus_Duration.Locus.Add(s);

                        // 换到下一线段
                        length = 0;
                        lineIdx++;
                        if (StraightLocus.Count <= lineIdx)
                        {
                            break;
                        }

                        pos = StraightLocus[lineIdx].PosStart;
                        // 下一帧再继续处理(这样每帧就只拆分一条线段)
                        await UniTask.NextFrame();
                    }
                    else
                    {
                        // 加入走过的这一小段
                        LineSegment s = new() { PosStart = pos, Dir = line.DirNormal * step };
                        StraightLocus_Duration.Locus.Add(s);

                        pos = s.PosEnd;
                        length += step;
                    }
                }
            }
        }

        #endregion

        #region 移动

        /// <summary>
        ///     生成子弹轨迹后开始自转和移动
        /// </summary>
        public async UniTaskVoid GenLocusThenStartMove()
        {
            // 先隐藏子弹图片
            ImgElement.SetActive(false);

            // 检查子弹是否一出生就碰到不可通过的障碍物
            List<ObstacleBase> obstacles = SingletonMgr.Instance.BattleMgr.MapMgr.Map.FindObstacles(new CircularArea2D
            {
                Center = transform.position,
                Radius = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault()
            });
            if (obstacles is { Count: > 0 } &&
                obstacles.Any(x => x.ObstacleThing is { TerrainType: >= TerrainType.Wall }))
            {
                // 显示子弹1秒后归还到子弹池
                ImgElement.SetActive(true);

                await UniTask.Delay(1000);

                TurnToPool().Forget();
                return;
            }

            #region 轨迹配置

            List<float> bulletLocusColor = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusColor)
                .ConvertAll(x => (float)x);
            if (bulletLocusColor.Count < 1)
            {
                bulletLocusColor.Add(255);
            }

            while (bulletLocusColor.Count < 3)
            {
                bulletLocusColor.Add(0);
            }

            if (bulletLocusColor.Count < 4)
            {
                bulletLocusColor.Add(1);
            }

            Color color_bulletLocus = new(bulletLocusColor[0], bulletLocusColor[1], bulletLocusColor[2],
                bulletLocusColor[3]);

            float bulletLocusWidth = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusWidth)
                .FirstOrDefault();
            if (bulletLocusWidth < 0)
            {
                bulletLocusWidth = 0.3f;
            }

            double bulletLocusTrackEnemyDuration = BulletThing.CdExecutor.Thing
                .GetTotalDouble(PropType.BulletLocusTrackEnemyDuration).FirstOrDefault();

            double bulletLocusDuration = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusDuration)
                .FirstOrDefault();

            #endregion

            // 生成并显示跟随敌人的轨迹
            await ShowTrackEnemyLocus(color_bulletLocus, bulletLocusWidth, bulletLocusTrackEnemyDuration);

            // 生成最终轨迹(固定)
            await GenStraightLocus();
            // 显示最终轨迹
            ShowLocus(color_bulletLocus, bulletLocusWidth, bulletLocusDuration).Forget();

            float timeBegin = Time.time;
            await GenStraightLocusInterval(MoveInterval);

            double bulletLocusShootDelay = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusShootDelay)
                .FirstOrDefault();
            bulletLocusShootDelay -= Time.time - timeBegin;
            if (bulletLocusShootDelay > 0)
            {
                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusShootDelay));
            }

            // 显示子弹图片
            ImgElement.SetActive(true);

            // 开始自转
            StartRotate();
            // 开始移动
            StartMove();
        }

        // /// <summary>
        // /// 开始移动(计算轨迹后，按固定时长进行移动)
        // /// </summary>
        // /// <returns>是否已开始移动</returns>
        // public override void StartMove()
        // {
        //     StopMove();
        //     CTS_Move = new();
        //
        //     MoveDuration.Value = 0;
        //     DoTask_Move(CTS_Move.Token).Forget();
        // }

        /// <summary>
        ///     任务实现:按轨迹移动
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.Delay(TimeSpan.FromSeconds(MoveInterval), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        // var line = MoveOne_PositionMove(Time.deltaTime);
                        LineSegment line = MoveOne_PositionMove(MoveInterval);
                        if (OnAfterMoveOne(line))
                        {
                            return;
                        }

                        // 需要移动
                        if (line.Dir != Vector3.zero)
                        {
                            // 子弹朝向
                            transform.right = line.DirNormal;
                            // 子弹移动
                            transform.DOMove(line.PosEnd, MoveInterval);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <inheritdoc />
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            // // 达到子弹最大存活时长,结束
            // if (BulletThing.LifeBeginTime.Value +
            //     BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault() <= Time.time)
            // {
            //     return true;
            // }

            return false;
        }

        /// <inheritdoc />
        protected override bool OnAfterMoveOne(LineSegment line)
        {
            // V42.7 追击导弹差异1：飞行过程中不会和怪物碰撞，只在到达目标位置时才碰撞
            var bulletType = (BulletType)(int)BulletThing.GetTotalLong(PropType.BulletType).FirstOrDefault();
            if (bulletType == BulletType.MissileTrackEnemy)
            {
                // V42.7 关键修复：检查ShouldDiscard标志，确保时间到或命中后能正确销毁
                if (BulletThing.ShouldDiscard.Value)
                {
                    Debug.Log($"=== V42.7/V43.0 追击导弹销毁确认【回收】 === 返回true停止移动并回收到子弹池，存活时间:{MoveDuration.Value:F1}秒");
                    return true; // 返回true让子弹停止移动并被回收
                }
                
                // 追击导弹跳过常规碰撞检测，由MoveOne_TrackEnemy自行处理终点碰撞
                return false; // 继续移动
            }
            
            // ShootMethod=9 圆弧追击子弹的特殊碰撞检测
            var shootMethod = (ShootMethod)(int)BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ShootMethod).FirstOrDefault();
            if (shootMethod == ShootMethod.CircularTrackBullet)
            {
                // 检查ShouldDiscard标志
                if (BulletThing.ShouldDiscard.Value)
                {
                    return true; // 返回true让子弹停止移动并被回收
                }
                
                // 圆弧追击子弹的碰撞检测
                if (BulletThing.NextHitEnemyTime.Value <= Time.time)
                {
                    IList<HitThingCells> enemies = DoCollideEnemies(line);
                    if (enemies.Count > 0)
                    {
                        // 击中的声音
                        AudioPlayer.Instance
                            .PlaySound(BulletThing.CdExecutor.Thing.GetTotalString(PropType.HitSound).FirstOrDefault())
                            .Forget();

                        BulletThing.NextHitEnemyTime.Value = Time.time +
                                                             (float)BulletThing.CdExecutor.Thing
                                                                 .GetTotalDouble(PropType.HitCd).FirstOrDefault();

                        // 最多伤害 剩余穿透次数+1 个敌人
                        int penetrateTimes = BulletThing.PenetrateTimes.Value;
                        enemies = enemies.Take(penetrateTimes + 1).ToList();

                        // 先调用常规的OnHitEnemy处理伤害
                        OnHitEnemy(line, BulletThing.CdExecutor.Thing, enemies);
                        
                        // 单独调用DamageEnemy处理怪物死亡回调
                        DamageEnemy(BulletThing.CdExecutor.Thing, enemies, (deadEnemy) => {
                            // 怪物死亡时，通知子弹重新寻找新目标
                            Debug.Log($"=== 圆弧追击子弹收到怪物死亡通知 === 死亡怪物:{deadEnemy.CsvRow_BattleBrushEnemy.Id}({deadEnemy.CsvRow_BattleBrushEnemy.EnemyName}) 开始寻找新目标");
                            CircularTrackBullet.FindNewTarget(BulletThing, transform);
                        });

                        // 然后处理圆弧追击子弹的特殊逻辑
                        foreach (var hitThingCells in enemies)
                        {
                            var hitEnemy = hitThingCells.Thing as MonsterThing;
                            if (hitEnemy != null)
                            {
                                // 使用圆弧追击子弹的特殊处理逻辑
                                var gunThing = BulletThing.CdExecutor?.Thing as GunThing;
                                if (gunThing != null)
                                {
                                    bool shouldContinueTracking = CircularTrackBullet.ProcessHitEnemy(BulletThing, hitEnemy, transform);
                                    // 注意：ProcessHitEnemy返回true表示继续追踪，false表示应该销毁子弹
                                    // 这里不需要额外设置ShouldDiscard，因为ProcessHitEnemy内部已经处理了
                                    break; // 只处理第一个击中的敌人
                                }
                            }
                        }

                        // 如果子弹被设置为该丢弃了,则本方法结束
                        if (BulletThing.ShouldDiscard.Value)
                        {
                            return true;
                        }
                    }
                }
                
                return false; // 继续移动
            }

            // 常规子弹的碰撞检测逻辑（完全复制ShootMethod=1的行为）
            if (BulletThing.NextHitEnemyTime.Value <= Time.time)
            {
                IList<HitThingCells> enemies = DoCollideEnemies(line);
                // 如果击中了敌人
                if (enemies.Count > 0)
                {
                    // 击中的声音
                    AudioPlayer.Instance
                        .PlaySound(BulletThing.CdExecutor.Thing.GetTotalString(PropType.HitSound).FirstOrDefault())
                        .Forget();

                    BulletThing.NextHitEnemyTime.Value = Time.time +
                                                         (float)BulletThing.CdExecutor.Thing
                                                             .GetTotalDouble(PropType.HitCd).FirstOrDefault();

                    // 子弹击中敌人后，默认设为应该丢弃
                    BulletThing.ShouldDiscard.Value = true;

                    // 最多伤害 剩余穿透次数+1 个敌人
                    int penetrateTimes = BulletThing.PenetrateTimes.Value;
                    enemies = enemies.Take(penetrateTimes + 1).ToList();

                    OnHitEnemy(line, BulletThing.CdExecutor.Thing, enemies);

                    // 如果子弹被设置为该丢弃了,则本方法结束
                    if (BulletThing.ShouldDiscard.Value)
                    {
                        // 返回true后,子弹会停止移动并被回收到子弹池
                        return true;
                    }
                }
            }

            // 如果子弹被设置为该丢弃了,则本方法结束
            if (BulletThing.ShouldDiscard.Value)
            {
                // 返回true后,子弹会停止移动并被回收到子弹池
                return true;
            }

            return false;
        }

        /// <inheritdoc />
        protected override void OnMoveEnd()
        {
            try
            {
                // 还给子弹池
                TurnToPool().Forget();
            }
            catch
            {
                // ignored
            }
        }

        /// <summary>
        ///     移动一次。从轨迹中找出这次直线移动的线段(或不动)
        /// </summary>
        /// <returns>这次直线移动经过的线段</returns>
        public override LineSegment MoveOne_PositionMove(float duration)
        {
            // 追击导弹功能：检查是否为MissileTrackEnemy类型
            var bulletType = (BulletType)(int)BulletThing.GetTotalLong(PropType.BulletType).FirstOrDefault();
            if (bulletType == BulletType.MissileTrackEnemy)
            {
                // V43.1 优先级：追击目标 > 定点攻击 > 直线飞行
                if (BulletThing.TrackEnemy != null)
                {
                    return MoveOne_TrackEnemy(duration);
                }
                else if (BulletThing.TrackPosition != null)
                {
                    return MoveOne_TrackPosition(duration);
                }
                else
                {
                    return MoveOne_StraightFlight(duration);
                }
            }
            
            // ShootMethod=9 圆弧追击子弹的特殊移动逻辑
            var shootMethod = (ShootMethod)(int)BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ShootMethod).FirstOrDefault();
            if (shootMethod == ShootMethod.CircularTrackBullet)
            {
                return MoveOne_CircularTrack(duration);
            }
            
            // 常规子弹移动逻辑（完全复制ShootMethod=1的轨迹移动）
            // 默认没有方向，就是不动
            LineSegment rtn = new() { PosStart = transform.position };
            MoveDuration.Value += duration;

            // 子弹被设置为停留状态，就不动了。
            if (ShouldStay)
            {
                return rtn;
            }

            // 移动时长超过停留区间的最大值，就应该停了
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            if (!ShouldStay && stayPeriod.Count > 1 && MoveDuration.Value > stayPeriod[1])
            {
                // 下次就不动了。
                ShouldStay = true;
            }

            if (StraightLocus_Duration.Locus.Count <= StraightLocus_Duration.Progress)
            {
                // 轨迹走完，该丢弃了
                BulletThing.ShouldDiscard.Value = true;
                return rtn;
            }

            // 找出这次要移动的线段
            rtn = StraightLocus_Duration.Locus[StraightLocus_Duration.Progress];

            // 向前推进
            StraightLocus_Duration.Progress++;

            return rtn;
        }

        /// <summary>
        /// ShootMethod=9 圆弧追击子弹的移动逻辑
        /// </summary>
        private LineSegment MoveOne_CircularTrack(float duration)
        {
            Vector3 currentPos = transform.position;
            LineSegment rtn = new() { PosStart = currentPos };

            MoveDuration.Value += duration;

            // 生存时间检查
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            double realMaxLifeTime = stayPeriod.Count > 1 ? stayPeriod[1] : BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();

            if (realMaxLifeTime > 0 && MoveDuration.Value > realMaxLifeTime)
            {
                BulletThing.ShouldDiscard.Value = true;
                return rtn;
            }

            // 修复：新子弹先直线飞行一段距离后再开始绕圈，避免新子弹跟其他子弹绕圈
            const float INITIAL_STRAIGHT_DISTANCE = 50f; // 新子弹先直线飞行50像素

            // 如果没有追击目标或目标已死亡，检查是否处于无目标圆弧追踪模式
            if (BulletThing.TrackEnemy == null || BulletThing.TrackEnemy.Hp.Value <= 0)
            {
                // 记录目标死亡时间，用于调试
                if (BulletThing.TrackEnemy != null && BulletThing.TrackEnemy.Hp.Value <= 0)
                {
                    Debug.Log($"=== 圆弧追击子弹目标死亡 === 目标:{BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.Id} 血量:{BulletThing.TrackEnemy.Hp.Value} 开始寻找新目标");
                }

                // 如果已经在无目标圆弧追踪模式下，检查是否需要重新寻找目标
                if (BulletThing.IsCircularTracking && BulletThing.NoTargetCircularCenter.HasValue)
                {
                    // 每隔0.2秒尝试寻找新目标
                    if (Time.time >= BulletThing.NextTargetSearchTime)
                    {
                        BulletThing.NextTargetSearchTime = Time.time + 0.2f;
                        Debug.Log($"=== 圆弧追击子弹定时寻找新目标 === 当前时间:{Time.time:F2} 下次寻找时间:{BulletThing.NextTargetSearchTime:F2}");
                        bool foundNewTarget = CircularTrackBullet.FindNewTarget(BulletThing, transform);
                        if (!foundNewTarget)
                        {
                            Debug.Log($"=== 圆弧追击子弹定时寻找失败 === 继续无目标绕圈");
                        }
                    }
                }
                else
                {
                    // 新子弹优先尝试寻找目标，找不到才进入无目标绕圈模式
                    bool foundTarget = CircularTrackBullet.FindNewTarget(BulletThing, transform);
                    if (!foundTarget)
                    {
                        // 找不到目标才设置为无目标圆弧追踪
                        CircularTrackBullet.SetupNoTargetCircularTrack(BulletThing, transform);
                    }
                }
            }

            // 修复：新子弹先直线飞向目标，飞行一定距离后再开始绕圈
            if (BulletThing.TrackEnemy != null && !BulletThing.IsCircularTracking)
            {
                // 计算已飞行的距离
                Vector3 initialPos = BulletThing.InitialPosition ?? currentPos;
                float distanceTraveled = Vector3.Distance(initialPos, currentPos);

                if (distanceTraveled < INITIAL_STRAIGHT_DISTANCE)
                {
                    // 继续直线飞行
                    Vector3 targetDir = (BulletThing.TrackEnemy.Position - currentPos).normalized;
                    float speed = Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                    Vector3 newPos = currentPos + targetDir * (float)speed * duration;

                    rtn.Dir = newPos - currentPos;
                    Thing.MoveDirection_Straight.Value = targetDir;

                    Debug.Log($"=== 圆弧追击子弹直线飞行阶段 === 已飞行距离:{distanceTraveled:F1}px 目标距离:{INITIAL_STRAIGHT_DISTANCE}px 目标:{BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.Id}");
                    return rtn;
                }
                else
                {
                    // 开始圆弧追击
                    CircularTrackBullet.SetupCircularTrack(BulletThing, BulletThing.TrackEnemy, transform);
                    Debug.Log($"=== 圆弧追击子弹开始绕圈 === 直线飞行完成，距离:{distanceTraveled:F1}px 目标:{BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.Id}");
                }
            }

            // 计算圆弧追击的目标位置
            Vector3 targetPos = CircularTrackBullet.CalculateCircularPosition(BulletThing, BulletThing.TrackEnemy, duration);

            // 直接使用计算出的圆弧位置，确保严格按照圆弧轨迹移动
            Vector3 newPos = targetPos;
            rtn = new LineSegment { PosStart = currentPos };
            rtn.Dir = newPos - currentPos;

            // 更新子弹的移动方向为实际移动方向
            Vector3 moveDir = (newPos - currentPos).normalized;
            Thing.MoveDirection_Straight.Value = moveDir;

            return rtn;
        }

        /// <summary>
        /// <summary>
        /// V43.3 追击导弹定点攻击逻辑：保持原有飞行曲线，螺旋轨迹朝目标点
        /// </summary>
        private LineSegment MoveOne_TrackPosition(float duration)
        {
            Vector3 currentPos = transform.position;
            LineSegment rtn = new() { PosStart = currentPos };

            MoveDuration.Value += duration;

            // V43.3 获取子弹初始发射数据（与追击敌人完全相同的算法）
            if (!BulletThing.InitialDirection.HasValue)
            {
                // 第一次追击移动，记录初始数据
                BulletThing.InitialDirection = Thing.MoveDirection_Straight.Value;
                BulletThing.InitialPosition = currentPos;
                BulletThing.BulletIndex = UnityEngine.Random.Range(0, 1000); // 用于轨迹差异化
            }

            Vector3 initialDir = BulletThing.InitialDirection.Value;
            Vector3 initialPos = BulletThing.InitialPosition.Value;
            int bulletIndex = BulletThing.BulletIndex;

            // V43.3 生存时间检查
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            var bulletRow = BulletThing.CsvRow_Bullet?.Value;
            double realMaxLifeTime = stayPeriod.Count > 1 ? stayPeriod[1] : BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();
            
            if (realMaxLifeTime > 0 && MoveDuration.Value > realMaxLifeTime)
            {
                BulletThing.ShouldDiscard.Value = true;
                Debug.Log($"=== V43.3 追击导弹定点攻击时间到【销毁】 === 导弹销毁，生存时间:{MoveDuration.Value:F1}秒，最大:{realMaxLifeTime:F1}秒");
                return rtn;
            }

            Vector3 targetPos = BulletThing.TrackPosition.Value;
            float distance = Vector3.Distance(currentPos, targetPos);

            // V43.3 到达目标点检测
            if (distance < 8.0f)
            {
                BulletThing.ShouldDiscard.Value = true;
                var gunThing = BulletThing.CdExecutor?.Thing as GunThing;
                Debug.Log($"=== V43.3 追击导弹到达定点【销毁】 === 枪ID:{gunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{bulletRow?.Id} → 到达目标点:({targetPos.x:F1},{targetPos.y:F1}) 距离:{distance:F1}");
                return rtn;
            }

            // V43.3 保持原有的智能分段飞行算法（与追击敌人完全相同）
            Vector3 targetDir = (targetPos - currentPos).normalized;
            Vector3 currentDir = Thing.MoveDirection_Straight.Value;
            
            // 计算初始角度和目标角度的夹角（0-180度）
            float angleDiff = Vector3.Angle(initialDir, (targetPos - initialPos).normalized);
            
            // 计算已飞行的距离
            float flownDistance = Vector3.Distance(currentPos, initialPos);
            
            // V43.5 大角度差缩短初始直线飞行距离
            float baseStraightDistance = Mathf.Lerp(3f, 5f, angleDiff / 180f); // 3-5像素
            if (angleDiff > 90f)
            {
                baseStraightDistance *= 0.5f; // 大角度差时距离减半
            }
            
            // 保持原有的轨迹差异化
            float trajectoryOffset = (bulletIndex % 5 - 2) * 5f; // ±10像素偏移
            float straightFlightThreshold = baseStraightDistance + trajectoryOffset;
            
            Vector3 moveDir;
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            string flightPhase;
            
            if (flownDistance < straightFlightThreshold)
            {
                // V43.5 阶段1：初始直线飞行（大角度时距离减半）
                moveDir = initialDir;
                flightPhase = "直线飞行";
            }
            else if (angleDiff > 90f)
            {
                // V43.5 阶段2：大角度差专用圆形转向飞行
                float turnDistance = flownDistance - straightFlightThreshold;
                const float circularRadius = 8f; // 固定半径8像素
                const float circularPhaseDistance = 8f; // 圆形飞行阶段距离
                
                if (turnDistance < circularPhaseDistance)
                {
                    // 圆形转向阶段：以半径8的圆弧飞行
                    float circularProgress = turnDistance / circularPhaseDistance;
                    
                    // 计算圆心位置（在子弹当前位置的左侧或右侧）
                    Vector3 perpendicular = Vector3.Cross(currentDir, Vector3.forward).normalized;
                    bool turnLeft = Vector3.Cross(currentDir, targetDir).z > 0; // 决定左转还是右转
                    Vector3 circleCenter = currentPos + perpendicular * (turnLeft ? circularRadius : -circularRadius);
                    
                    // 计算角速度（让子弹在圆形飞行结束时朝向水平方向）
                    float targetAngle = turnLeft ? 0f : 180f; // 目标是水平向右或向左
                    float currentAngle = Mathf.Atan2(currentDir.y, currentDir.x) * Mathf.Rad2Deg;
                    float angleDelta = Mathf.DeltaAngle(currentAngle, targetAngle);
                    float angularSpeed = angleDelta / circularPhaseDistance * speed; // 角速度
                    
                    // 计算新的移动方向（沿圆弧切线）
                    float newAngle = currentAngle + angularSpeed * duration;
                    moveDir = new Vector3(Mathf.Cos(newAngle * Mathf.Deg2Rad), Mathf.Sin(newAngle * Mathf.Deg2Rad), 0).normalized;
                    
                    flightPhase = $"圆形转向(进度:{circularProgress*100:F0}%,半径:{circularRadius}px,角度:{newAngle:F0}°)";
                }
                else
                {
                    // 圆形转向完成，现在朝向水平方向，开始平滑转向目标
                    moveDir = Vector3.Slerp(currentDir, targetDir, 0.5f * duration * 40f).normalized;
                    flightPhase = "水平转向目标";
                }
            }
            else
            {
                // V43.5 阶段2&3：小角度差保持原有逻辑
                float turnDistance = flownDistance - straightFlightThreshold;
                
                if (turnDistance < 20f)
                {
                    // 小角度快速转向
                    float turnProgress = turnDistance / 20f;
                    float turnStrength = Mathf.Lerp(0.2f, 0.6f, turnProgress);
                    moveDir = Vector3.Slerp(currentDir, targetDir, turnStrength * duration * 6f).normalized;
                    flightPhase = $"快速转弯(进度:{turnProgress*100:F0}%)";
                }
                else
                {
                    // 实时追击阶段：每帧重新计算目标方向，确保命中目标点
                    moveDir = targetDir;
                    flightPhase = "实时追击";
                }
            }
            
            // 更新导弹朝向和方向
            Thing.MoveDirection_Straight.Value = moveDir;
            transform.right = moveDir;
            
            // V43.6 大角度转向优化：150度以上限制每帧移动距离
            Vector3 moveDistance = moveDir * speed * duration;
            
            // V43.6 超大角度转向阶段限制每帧移动距离，防止来回飞行（已移除，逻辑在V43.6版本中处理）
            if (angleDiff > 150f && flownDistance >= straightFlightThreshold)
            {
                // 转向阶段限制每帧移动距离在5px以内
                float frameDistance = moveDistance.magnitude;
                if (frameDistance > 0.5f)
                {
                    moveDistance = moveDistance.normalized * 0.5f;
                }
            }
            
            // V43.3 详细的分段飞行日志
            var positionGunThing = BulletThing.CdExecutor?.Thing as GunThing;
            int positionPenetrateTimes = BulletThing.PenetrateTimes.Value;
            
            if (MoveDuration.Value % 1.0f < duration) // 每1秒打印一次
            {
                Debug.Log($"=== V43.5 定点圆形转向【彻底解决】 === 枪ID:{positionGunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{bulletRow?.Id} → 目标点:({targetPos.x:F1},{targetPos.y:F1}) 阶段:{flightPhase} 角度差:{angleDiff:F0}° 已飞行:{flownDistance:F0}px 直线阈值:{straightFlightThreshold:F0}px 距离目标:{distance:F0}px 导弹:({currentPos.x:F1},{currentPos.y:F1}) 存活:{MoveDuration.Value:F1}秒");
            }
            
            rtn.Dir = moveDistance;
            return rtn;
        }

        /// <summary>
        /// 追击导弹直线飞行逻辑（当没有目标时）
        /// </summary>
        private LineSegment MoveOne_StraightFlight(float duration)
        {
            Vector3 currentPos = transform.position;
            LineSegment rtn = new() { PosStart = currentPos };

            MoveDuration.Value += duration;

            // V42.7 修复生存时间检查：使用子弹配置的真实生存时间
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            var bulletRow = BulletThing.CsvRow_Bullet?.Value;
            double realMaxLifeTime = stayPeriod.Count > 1 ? stayPeriod[1] : BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();
            
            if (realMaxLifeTime > 0 && MoveDuration.Value > realMaxLifeTime)
            {
                BulletThing.ShouldDiscard.Value = true;
                Debug.Log($"=== V42.7 追击导弹直线飞行时间到【销毁】 === 导弹销毁，生存时间:{MoveDuration.Value:F1}秒，最大:{realMaxLifeTime:F1}秒");
                return rtn;
            }

            // 直线飞行
            Vector3 straightDir = Thing.MoveDirection_Straight.Value;
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            Vector3 moveDistance = straightDir * speed * duration;
            
            // V42.7 每2秒打印一次直线飞行日志
            var gunThing = BulletThing.CdExecutor?.Thing as GunThing;
            int penetrateTimes = BulletThing.PenetrateTimes.Value;
            if (MoveDuration.Value % 2.0f < duration)
            {
                Debug.Log($"=== 追击导弹直线飞行【每2秒】 === 枪ID:{gunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{bulletRow?.Id} → 无目标直线飞行 导弹:({currentPos.x:F1},{currentPos.y:F1}) 方向:({straightDir.x:F2},{straightDir.y:F2}) 穿透次数:{penetrateTimes} 存活时间:{MoveDuration.Value:F1}/{realMaxLifeTime:F1}秒");
            }
            
            rtn.Dir = moveDistance;
            return rtn;
        }

        /// <summary>
        ///     子弹传送至终点并反弹
        /// </summary>
        public override void TranslateToEndPoint_PositionMove(StraightEndPoint_PositionMove endPoint)
        {
            // 物件传送至终点
            base.TranslateToEndPoint_PositionMove(endPoint);

            // 如果终点是不可通过的障碍物，要么反弹，要么销毁
            if (endPoint.MirrorInNormal != Vector3.zero)
            {
                if (BulletThing.BounceTimes.Value > 0)
                {
                    BulletThing.BounceTimes.Value--;
                    // 反弹后的方向
                    Thing.MoveDirection_Straight.Value = Vector3
                        .Reflect(Thing.MoveDirection_Straight.Value, endPoint.MirrorInNormal).normalized;
                    // 子弹朝向
                    transform.right = Thing.MoveDirection_Straight.Value;
                }
                else
                {
                    BulletThing.ShouldDiscard.Value = true;
                }
            }
        }

        /// <summary>
        /// V43.3 追击导弹移动逻辑：保持原有飞行曲线，只修复转圈和目标死亡问题
        /// </summary>
        /// <param name="duration">移动持续时间</param>
        /// <returns>移动线段</returns>
        private LineSegment MoveOne_TrackEnemy(float duration)
        {
            Vector3 currentPos = transform.position;
            LineSegment rtn = new() { PosStart = currentPos };

            MoveDuration.Value += duration;

            // V43.3 获取子弹初始发射数据（保持原有算法）
            if (!BulletThing.InitialDirection.HasValue)
            {
                // 第一次追击移动，记录初始数据
                BulletThing.InitialDirection = Thing.MoveDirection_Straight.Value;
                BulletThing.InitialPosition = currentPos;
                BulletThing.BulletIndex = UnityEngine.Random.Range(0, 1000); // 用于轨迹差异化
            }

            Vector3 initialDir = BulletThing.InitialDirection.Value;
            Vector3 initialPos = BulletThing.InitialPosition.Value;
            int bulletIndex = BulletThing.BulletIndex;

            // V43.3 生存时间检查
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            var bulletRow = BulletThing.CsvRow_Bullet?.Value;
            double realMaxLifeTime = stayPeriod.Count > 1 ? stayPeriod[1] : BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();
            
            if (realMaxLifeTime > 0 && MoveDuration.Value > realMaxLifeTime)
            {
                BulletThing.ShouldDiscard.Value = true;
                Debug.Log($"=== V43.3 追击导弹生存时间到【销毁】 === 导弹销毁，生存时间:{MoveDuration.Value:F1}秒，最大:{realMaxLifeTime:F1}秒");
                return rtn;
            }

            // V43.6 增强的目标有效性检查：检查对象、组件、激活状态和血量
            if (BulletThing.TrackEnemy == null || 
                BulletThing.TrackEnemy.ThingBehaviour == null || 
                !BulletThing.TrackEnemy.ThingBehaviour.gameObject.activeInHierarchy ||
                BulletThing.TrackEnemy.Hp.Value <= 0)
            {
                var lostTargetGunThing = BulletThing.CdExecutor?.Thing as GunThing;
                var lostTargetBulletRow = BulletThing.CsvRow_Bullet?.Value;
                int lostTargetPenetrateTimes = BulletThing.PenetrateTimes.Value;
                
                string lostReason = BulletThing.TrackEnemy == null ? "目标为null" :
                                   BulletThing.TrackEnemy.ThingBehaviour == null ? "ThingBehaviour为null" :
                                   !BulletThing.TrackEnemy.ThingBehaviour.gameObject.activeInHierarchy ? "对象未激活" :
                                   BulletThing.TrackEnemy.Hp.Value <= 0 ? $"目标死亡(血量:{BulletThing.TrackEnemy.Hp.Value:F0})" : "未知原因";
                
                // V43.6 目标死亡处理：记录死亡坐标
                if (BulletThing.TrackEnemy != null && BulletThing.TrackEnemy.Hp.Value <= 0 && !BulletThing.TargetDeathPosition.HasValue)
                {
                    BulletThing.TargetDeathPosition = BulletThing.TrackEnemy.Position;
                    Debug.Log($"=== V43.6 记录目标死亡坐标 === 目标:{BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.Id}({BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.EnemyName}) 死亡坐标:({BulletThing.TargetDeathPosition.Value.x:F1},{BulletThing.TargetDeathPosition.Value.y:F1})");
                }
                
                // V43.6 尝试寻找新目标
                var newTarget = FindNewTarget();
                if (newTarget != null)
                {
                    BulletThing.TrackEnemy = newTarget;
                    Debug.Log($"=== V43.6 找到新目标 === 新目标:{newTarget.CsvRow_BattleBrushEnemy.Id}({newTarget.CsvRow_BattleBrushEnemy.EnemyName}) 位置:({newTarget.Position.x:F1},{newTarget.Position.y:F1}) 血量:{newTarget.Hp.Value:F0}");
                    // 继续追击新目标
                }
                else if (BulletThing.TargetDeathPosition.HasValue)
                {
                    // V43.6 没有新目标，飞向死亡坐标点
                    Vector3 deathPos = BulletThing.TargetDeathPosition.Value;
                    float distanceToDeathPos = Vector3.Distance(currentPos, deathPos);
                    
                    if (distanceToDeathPos < 8.0f)
                    {
                        // 到达死亡坐标点，销毁子弹
                        BulletThing.ShouldDiscard.Value = true;
                        Debug.Log($"=== V43.6 到达死亡坐标点【销毁】 === 导弹到达死亡坐标点({deathPos.x:F1},{deathPos.y:F1})，距离:{distanceToDeathPos:F1}px，销毁子弹");
                        return rtn;
                    }
                    
                    // 飞向死亡坐标点
                    Vector3 deathDir = (deathPos - currentPos).normalized;
                    float deathSpeed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                    Vector3 deathMoveDistance = deathDir * deathSpeed * duration;
                    
                    Thing.MoveDirection_Straight.Value = deathDir;
                    transform.right = deathDir;
                    
                    Debug.Log($"=== V43.6 飞向死亡坐标点 === 导弹:({currentPos.x:F1},{currentPos.y:F1}) → 死亡点:({deathPos.x:F1},{deathPos.y:F1}) 距离:{distanceToDeathPos:F1}px");
                    
                    rtn.Dir = deathMoveDistance;
                    return rtn;
                }
                else
                {
                    // V43.6 没有新目标也没有死亡坐标，改为直线飞行
                    Vector3 straightDir = Thing.MoveDirection_Straight.Value.normalized;
                    float fallbackSpeed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                    Vector3 fallbackMoveDistance = straightDir * fallbackSpeed * duration;
                    
                    Debug.Log($"=== V43.6 追击导弹目标丢失【改直线飞行】 === 枪ID:{lostTargetGunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{lostTargetBulletRow?.Id} → 原因:{lostReason} 导弹:({currentPos.x:F1},{currentPos.y:F1}) 直线方向:({straightDir.x:F2},{straightDir.y:F2}) 穿透:{lostTargetPenetrateTimes} 存活:{MoveDuration.Value:F1}秒");
                    
                    rtn.Dir = fallbackMoveDistance;
                    return rtn;
                }
                
                // 清除原目标引用
                if (BulletThing.TrackEnemy != null && BulletThing.TrackEnemy.Hp.Value <= 0)
                {
                    BulletThing.TrackEnemy = null;
                }
            }

            Vector3 targetPos = BulletThing.TrackEnemy.Position;
            float distance = Vector3.Distance(currentPos, targetPos);

            // V43.3 命中检测
            if (distance < 8.0f)
            {
                HitTrackEnemy();
                return rtn;
            }

            // V43.3 保持原有的智能分段飞行算法，只修复转向速度防止转圈
            Vector3 targetDir = (targetPos - currentPos).normalized;
            Vector3 currentDir = Thing.MoveDirection_Straight.Value;
            
            // 计算初始角度和目标角度的夹角（0-180度）
            float angleDiff = Vector3.Angle(initialDir, (targetPos - initialPos).normalized);
            
            // 计算已飞行的距离
            float flownDistance = Vector3.Distance(currentPos, initialPos);
            
            // V43.6 简化的分段飞行算法：彻底解决大角度来回飞问题
            float baseStraightDistance = 8f; // 固定初始直线距离
            float trajectoryOffset = (bulletIndex % 5 - 2) * 3f; // 减小轨迹偏移
            float straightFlightThreshold = baseStraightDistance + trajectoryOffset;
            
            Vector3 moveDir;
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            string flightPhase;
            
            // V43.6 计算转向进度，需要在外层作用域定义以便后续使用
            float turnDistance = flownDistance - straightFlightThreshold;
            float maxTurnDistance = 30f; // 转向阶段最大距离
            float turnProgress = Mathf.Clamp01(turnDistance / maxTurnDistance);
            
            if (flownDistance < straightFlightThreshold)
            {
                // V43.7 阶段1：大角度斜向飞行到150度，小角度直线飞行
                if (angleDiff > 150f)
                {
                    // V43.7 大角度特殊处理：持续斜向飞行直到角度降到150度
                    // 150度减1度，180度减10度，中间角度线性插值
                    float angleReduction = Mathf.Lerp(1f, 10f, (angleDiff - 150f) / 30f);
                    float adjustedAngle = angleDiff - angleReduction;
                    Vector3 initialToTarget = (targetPos - initialPos).normalized;
                    
                    // 计算调整后的方向向量
                    float angleInRadians = adjustedAngle * Mathf.Deg2Rad;
                    Vector3 adjustedDir = new Vector3(
                        Mathf.Cos(angleInRadians) * initialToTarget.x - Mathf.Sin(angleInRadians) * initialToTarget.y,
                        Mathf.Sin(angleInRadians) * initialToTarget.x + Mathf.Cos(angleInRadians) * initialToTarget.y,
                        0
                    ).normalized;
                    
                    moveDir = adjustedDir;
                    flightPhase = $"大角度斜向飞行(原角度:{angleDiff:F0}° 减少:{angleReduction:F1}° 调整为:{adjustedAngle:F1}° 帧数:{(MoveDuration.Value/0.016f):F0})";
                }
                else
                {
                    // 普通初始直线飞行
                    moveDir = initialDir;
                    flightPhase = "初始直线";
                }
            }
            else
            {
                // V43.6 阶段2：统一的平滑转向算法
                
                if (angleDiff > 150f)
                {
                    // V43.6 超大角度优化：150度以上特殊处理
                    // 计算最小转向时间：150度=0.3秒，180度=0.5秒
                    float minTurnTime = Mathf.Lerp(0.3f, 0.5f, (angleDiff - 150f) / 30f);
                    float elapsedTurnTime = turnDistance / speed; // 已转向时间
                    
                    // 根据最小时间计算转向速度，确保不会转得太快
                    float timeBasedTurnSpeed = Mathf.Min(1.5f * duration, elapsedTurnTime / minTurnTime);
                    moveDir = Vector3.Slerp(currentDir, targetDir, timeBasedTurnSpeed).normalized;
                    
                    flightPhase = $"超大角度转向(角度:{angleDiff:F0}° 进度:{turnProgress*100:F0}% 最小时间:{minTurnTime:F2}s)";
                }
                else if (angleDiff > 120f)
                {
                    // V43.6 大角度：使用稳定的渐进转向，避免来回飞行
                    float turnSpeed = 1.5f * duration; // 降低转向速度，提高稳定性
                    moveDir = Vector3.Slerp(currentDir, targetDir, turnSpeed).normalized;
                    
                    flightPhase = $"大角度转向(角度:{angleDiff:F0}° 进度:{turnProgress*100:F0}%)";
                }
                else if (angleDiff > 60f)
                {
                    // V43.6 大角度：使用中等速度转向
                    Vector3 targetDirection = Vector3.Slerp(initialDir, targetDir, turnProgress).normalized;
                    moveDir = Vector3.Slerp(currentDir, targetDirection, 1.0f * duration).normalized;
                    
                    flightPhase = $"大角度转向(角度:{angleDiff:F0}° 进度:{turnProgress*100:F0}%)";
                }
                else
                {
                    // V43.6 小角度：快速转向
                    moveDir = Vector3.Slerp(currentDir, targetDir, 4.0f * duration).normalized;
                    flightPhase = $"小角度转向(角度:{angleDiff:F0}°)";
                }
                
                // V43.6 最终阶段：直接朝向目标
                if (turnProgress >= 0.8f)
                {
                    moveDir = targetDir;
                    flightPhase = "最终追击";
                }
            }
            
            // 更新导弹朝向和方向
            Thing.MoveDirection_Straight.Value = moveDir;
            transform.right = moveDir;
            
            Vector3 moveDistance = moveDir * speed * duration;
            
            // V43.7 超大角度转向阶段限制每帧移动距离，防止来回飞行
            // 只有在转向阶段且角度仍然很大时才限制移动距离
            if (angleDiff > 150f && flownDistance >= straightFlightThreshold && turnProgress < 0.8f && flightPhase.Contains("超大角度转向"))
            {
                // 超大角度转向阶段，限制每帧最大移动距离为8px
                float currentMoveDistance = moveDistance.magnitude;
                if (currentMoveDistance > 8f)
                {
                    moveDistance = moveDistance.normalized * 8f;
                }
            }
            
            // V43.3 详细的分段飞行日志
            var trackingGunThing = BulletThing.CdExecutor?.Thing as GunThing;
            var trackingBulletRow = BulletThing.CsvRow_Bullet?.Value;
            int trackingPenetrateTimes = BulletThing.PenetrateTimes.Value;
            
            if (MoveDuration.Value % 1.0f < duration) // 每1秒打印一次
            {
                Debug.Log($"=== V43.6 追击稳定轨迹【彻底解决】 === 枪ID:{trackingGunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{trackingBulletRow?.Id} → 目标:{BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.Id}({BulletThing.TrackEnemy.CsvRow_BattleBrushEnemy.EnemyName}) 阶段:{flightPhase} 角度差:{angleDiff:F0}° 已飞行:{flownDistance:F0}px 直线阈值:{straightFlightThreshold:F0}px 距离目标:{distance:F0}px 导弹:({currentPos.x:F1},{currentPos.y:F1}) 目标:({targetPos.x:F1},{targetPos.y:F1}) 血量:{BulletThing.TrackEnemy.Hp.Value:F0} 穿透:{trackingPenetrateTimes} 存活:{MoveDuration.Value:F1}秒");
            }
            
            rtn.Dir = moveDistance;
            return rtn;
        }

        /// <summary>
        /// V43.6 寻找新目标
        /// </summary>
        private MonsterThing FindNewTarget()
        {
            var gunThing = BulletThing.CdExecutor?.Thing as GunThing;
            if (gunThing == null) return null;
            
            // 使用枪的寻敌逻辑
            var distanceThing = gunThing.FindEnemy();
            if (distanceThing.Thing2 is MonsterThing newMonster && newMonster.Hp.Value > 0)
            {
                Debug.Log($"=== V43.6 寻找新目标成功 === 找到新目标:{newMonster.CsvRow_BattleBrushEnemy.Id}({newMonster.CsvRow_BattleBrushEnemy.EnemyName}) 距离:{distanceThing.Distance:F1}px 血量:{newMonster.Hp.Value:F0}");
                return newMonster;
            }
            
            Debug.Log($"=== V43.6 寻找新目标失败 === 没有找到有效的新目标");
            return null;
        }
        
        /// <summary>
        /// 追击导弹命中目标处理 - 完全复制ShootMethod=1的伤害逻辑
        /// </summary>
        private void HitTrackEnemy()
        {
            var target = BulletThing.TrackEnemy;
            if (target == null) return;

            var gunThing = BulletThing.CdExecutor?.Thing as GunThing;
            var bulletRow = BulletThing.CsvRow_Bullet?.Value;

            // 检查是否在命中冷却时间内
            if (BulletThing.NextHitEnemyTime.Value > Time.time)
            {
                return;
            }

            // 设置下次命中时间
            BulletThing.NextHitEnemyTime.Value = Time.time +
                                                 (float)BulletThing.CdExecutor.Thing
                                                     .GetTotalDouble(PropType.HitCd).FirstOrDefault();

            // 完整一行伤害日志：枪ID、子弹ID、目标、血量、伤害、穿透次数
            int penetrateTimes = BulletThing.PenetrateTimes.Value;
            float targetHP = (float)target.Hp.Value;
            double bulletAttack = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.Attack).FirstOrDefault();
            Debug.Log($"=== V42.7 追击导弹命中【伤害】 === 枪ID:{gunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{bulletRow?.Id} → 目标:{target.CsvRow_BattleBrushEnemy.Id}({target.CsvRow_BattleBrushEnemy.EnemyName}) 目标血量:{targetHP:F0} 导弹攻击力:{bulletAttack:F0} 坐标:({transform.position.x:F1},{transform.position.y:F1}) 穿透剩余:{penetrateTimes}次 存活时间:{MoveDuration.Value:F1}秒");

            // 使用和常规子弹完全相同的碰撞伤害处理逻辑
            var hitCells = new List<HitThingCells>
            {
                new HitThingCells { Thing = target }
            };

            // 播放击中声音
            AudioPlayer.Instance
                .PlaySound(BulletThing.CdExecutor.Thing.GetTotalString(PropType.HitSound).FirstOrDefault())
                .Forget();

            // 调用标准的击中敌人处理方法
            var line = new LineSegment { PosStart = transform.position };
            line.Dir = target.Position - transform.position;
            OnHitEnemy(line, BulletThing.CdExecutor.Thing, hitCells);

            // V42.7 穿透逻辑修复：检查穿透次数决定是否销毁
            if (BulletThing.PenetrateTimes.Value <= 0)
            {
                // 穿透次数用尽，导弹应该销毁
                BulletThing.ShouldDiscard.Value = true;
                Debug.Log($"=== V42.7 追击导弹穿透用尽【销毁】 === 导弹销毁，穿透次数=0，存活时间:{MoveDuration.Value:F1}秒");
            }
            else
            {
                // 还有穿透次数，减1后继续飞行
                BulletThing.PenetrateTimes.Value--;
                Debug.Log($"=== V42.7 追击导弹穿透继续 === 剩余穿透次数:{BulletThing.PenetrateTimes.Value}，清除当前目标，继续寻找新目标");
                
                // 清除当前目标，让导弹继续寻找新目标或直线飞行
                BulletThing.TrackEnemy = null;
            }
        }

        #endregion
    }
}