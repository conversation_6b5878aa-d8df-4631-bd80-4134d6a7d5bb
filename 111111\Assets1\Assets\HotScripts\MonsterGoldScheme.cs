using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;

public class MonsterGoldScheme : Singleton<MonsterGoldScheme>
{
    private MonsterGold _data;
    private Dictionary<string, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load();
        }
    }
    public bool Load()
    {
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.MonsterGold;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            MemoryStream ms = new MemoryStream(HotResManager.ReadPb(pbFileName));
            _data = Serializer.Deserialize<MonsterGold>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].MissionID] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;

    }
    public MonsterGold.Item GetItem(string id)
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load();
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            Debug.LogWarning("id dont exist");
            return null;
        }

    }
}


