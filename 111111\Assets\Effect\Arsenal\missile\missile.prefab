%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1349866554620262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4622063790027994}
  - component: {fileID: 33098575336960486}
  - component: {fileID: 23459915720340730}
  - component: {fileID: 114711387600437298}
  - component: {fileID: 61866432050604138}
  - component: {fileID: 50471074731362184}
  - component: {fileID: 114410996297105678}
  - component: {fileID: 114383893069673502}
  - component: {fileID: 114124102854388166}
  m_Layer: 16
  m_Name: missile
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4622063790027994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -0.3, y: 0.3, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33098575336960486
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Mesh: {fileID: 0}
--- !u!23 &23459915720340730
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aa3b3954dea278b44b583c9b9d874ad4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: -1628607475
  m_SortingLayer: 2
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &114711387600437298
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 0b0904bfdd17c8147896fed3c4897a4b, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: playerMissile
  loop: 1
  timeScale: 1
--- !u!61 &61866432050604138
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 2, y: 2}
    newSize: {x: 1, y: 1}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 0.8, y: 0.5}
  m_EdgeRadius: 0
--- !u!50 &50471074731362184
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_BodyType: 1
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 0
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!114 &114410996297105678
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e1a732e2aa364c569bbb79498af8c5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  BoundsBasedOn: 1
  ExecuteOnEnable:
    m_PersistentCalls:
      m_Calls: []
  ExecuteOnDisable:
    m_PersistentCalls:
      m_Calls: []
  LifeTime: 10
  FaceDirection: 1
  Speed: 100
  Acceleration: 0
  Direction: {x: -1, y: 0, z: 0}
  DirectionCanBeChangedBySpawner: 1
  FlipValue: {x: -1, y: 1, z: 1}
  ProjectileIsFacingRight: 1
  InitialInvulnerabilityDuration: 0
  DamageOwner: 0
  SpawnSecurityCheck: 0
  SpawnSecurityCheckLayerMask:
    serializedVersion: 2
    m_Bits: 0
--- !u!114 &114383893069673502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76891b3b532f84acaaeed44c357bd00c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetLayerMask:
    serializedVersion: 2
    m_Bits: 271104
  MinDamageCaused: 10
  MaxDamageCaused: 10
  TypedDamages: []
  DamageCausedKnockbackType: 1
  DamageCausedKnockbackDirection: 0
  DamageCausedKnockbackForce: {x: 10, y: 2}
  InvincibilityDuration: 0.5
  RepeatDamageOverTime: 0
  AmountOfRepeats: 3
  DurationBetweenRepeats: 1
  DamageOverTimeInterruptible: 1
  RepeatedDamageType: {fileID: 0}
  DamageTakenEveryTime: 10
  DamageTakenDamageable: 5
  DamageTakenNonDamageable: 10
  DamageTakenKnockbackType: 0
  DamageTakenKnockbackDirection: 0
  DamageTakenKnockbackForce: {x: 0, y: 0}
  DamageTakenInvincibilityDuration: 0
  HitDamageableFeedback: {fileID: 0}
  HitNonDamageableFeedback: {fileID: 0}
  FreezeFramesOnHitDuration: 0
  Owner: {fileID: 0}
--- !u!114 &114124102854388166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1349866554620262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9716d97790a0a49889284ee813194576, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  CurrentHealth: 0
  TemporarilyInvulnerable: 0
  PostDamageInvulnerable: 0
  InitialHealth: 10
  MaximumHealth: 10
  Invulnerable: 0
  ImmuneToDamage: 0
  DamageFeedbacks: {fileID: 0}
  FeedbackIsProportionalToDamage: 0
  FlickerSpriteOnHit: 1
  FlickerColor: {r: 1, g: 0.078431375, b: 0.078431375, a: 1}
  ImmuneToKnockback: 0
  ImmuneToKnockbackIfZeroDamage: 0
  DeathFeedbacks: {fileID: 0}
  DestroyOnDeath: 1
  DelayBeforeDestruction: 0
  CollisionsOffOnDeath: 1
  GravityOffOnDeath: 0
  PointsWhenDestroyed: 0
  RespawnAtInitialLocation: 0
  ApplyDeathForce: 1
  DeathForce: {x: 0, y: 10}
  ResetForcesOnDeath: 0
  ResetColorOnRevive: 1
  ColorMaterialPropertyName: _Color
  UseMaterialPropertyBlocks: 0
  MasterHealth: {fileID: 0}
  TargetDamageResistanceProcessor: {fileID: 0}
