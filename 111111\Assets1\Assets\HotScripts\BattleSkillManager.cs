using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Extension;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using UniRx;

using UnityEditor;

using UnityEngine;

using ViewModel;

public class BattleSkillManager : Singleton<BattleSkillManager>
{
    //[HideInInspector]public CatSkill.Item currentSkillData;
    private Equipment.Item csvRow_Equipment_Weapon;
    private int _bulletAngle;
    private Bullet bullet;
    [HideInInspector] public int bulletDamageFromPlist;
    //private PlayerController player;
    private float bulletMultiplierFromPlist;
    private Sprite[] bulletSprites;
    private Transform playerSkeleton;
    public PlayerController playerController;
    private GameObject[] bulletFlash;
    private ProtonBulletSlot[] protonBulletSlots;
    public List<WhiteCateStruct> whiteCatList;
    public PlayerController whiteCatBGameObject;

    private GameObject _skyFireBoomPrefab;
    private GameObject _skyFireBoomMaxPrefab;
    public Weapon _weapon;
    public int tempSkillLevel = -1;

    private float realSkillSpeed;
    private double defaultDamege;
    private float iceBallTime;

    public List<Bullet> allBoomerangBullet;

    /// <summary>
    /// 激光发射器
    /// </summary>
    private LaserShooter LaserShooter { get; set; }

    //只放一次闪电声音

    public bool LightSoundOnce = false;

    public void init(
        PlayerController param_playerController,
        Equipment.Item EquipmentData,
        Bullet Bullet,
        int BulletDamageFromPlist,
        float BulletMultiplierFromPlist,
        Sprite[] BulletSprites,
        Transform PlayerSkeleton,
        GameObject[] BulletFlash,
        GameObject boomPrefab,
        GameObject boomMaxPrefab,
        Weapon weapon
        )
    {
        playerController = param_playerController;
        csvRow_Equipment_Weapon = EquipmentData;
        bulletDamageFromPlist = BulletDamageFromPlist;
        bullet = Bullet;
        bulletMultiplierFromPlist = BulletMultiplierFromPlist;
        bulletSprites = BulletSprites;
        playerSkeleton = PlayerSkeleton;
        bulletFlash = BulletFlash;
        _skyFireBoomPrefab = boomPrefab;
        _skyFireBoomMaxPrefab = boomMaxPrefab;
        _weapon = weapon;

        // 技能升级后,处理正在执行中的技能
        MessageBroker.Default.Receive<GameEvent.SkillUp>().Subscribe(e =>
        {
            // 结束技能:激光射线
            if (e.csvRow_CatSkill.Type == (int)SkillType.LaserRay_Weapon
            || e.csvRow_CatSkill.Type == (int)SkillType.LaserRay)
            {
                StopSkill_LaserRay(true).Forget();
                //StartSkill_LaserRay(e.csvRow_CatSkill).Forget();
            }
        }).AddTo(this);
    }

    public IEnumerator AutoRunSkill(int skillID)
    {
        CatSkill.Item currentSkillData = CatSkillScheme.Instance.GetItem(skillID);
        realSkillSpeed = currentSkillData.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate;
        bulletMultiplierFromPlist = Globals.UnityValueTransform(currentSkillData.DamageCoe);
        int bulletNum = currentSkillData.AttackCount + GameManager.instance.player.Stats.addBulletNum;
        if (currentSkillData.Type == ((int)SkillType.Refraction))//浮空刃
        {
            if (currentSkillData.NextLv != 0)
            {
                //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
                float AttackAngle = 360 / bulletNum;
                LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6029);
                for (int i = 0; i < bulletNum; i++)
                {
                    GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                    bullet = go.GetComponent<Bullet>();
                    bullet.skillData = currentSkillData;
                    bullet.SetBulletType(Bullet.BulletType.ReflectButtle);
                    if (GameData.instance.fileHandler.currentMission != 0)
                    {
                        //Debug.LogWarning("这个是技能ID" + currentSkillData.Id.ToString());
                        defaultDamege = currentSkillData.AttackValue;
                        defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
                    }
                    else
                    {
                        defaultDamege = bulletDamageFromPlist + (GameManager.instance.player.Stats.attack * bulletMultiplierFromPlist);
                    }
                    bullet.setDamage(defaultDamege);
                    bullet.isRemovable = true;
                    bullet.SetSpriteFrame(HotResManager.ReadAtlas("Bullet").GetSprite("jn" + currentSkillData.Id));
                    bullet.transform.localScale = Globals.UnityValueTransform(currentSkillData.BulletScale * (1.3f)) * Vector3.one;

                    bullet.setReactToWater(true);


                    if (GameManager.instance.player.transform.position.y < -200)
                    {
                        bullet.setReactToWater(false);
                    }


                    bullet.transform.position = GameManager.instance.player.transform.position;
                    Vector3 rotation1 = playerSkeleton.transform.eulerAngles;
                    rotation1.z += i * AttackAngle;
                    bullet.transform.rotation = Quaternion.Euler(rotation1);

                    bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));

                    bullet.gameObject.SetActive(true);
                    bullet.PushBack = 0; //Globals.CocosToUnity(7.5f - (gunLevel / 2));
                    bullet.SetReflectBulletParam(Globals.UnityValueTransform(currentSkillData.AttackTime), Globals.UnityValueTransform(realSkillSpeed));
                    bullet.SetRefectRota(true);
                    bullet.isInUse = true;
                    GameSharedData.Instance.playerBulletInUse.Add(bullet);
                }
            }
            else
            {
                //最大等级特效
                float minX = GameManager.instance.player.transform.position.x - 3;
                float minY = GameManager.instance.player.transform.position.y - 5;
                Vector3 rotation1 = playerSkeleton.transform.eulerAngles;
                bool toLeft = (rotation1.z > 90 && rotation1.z < 270);
                float rot = toLeft ? 180 : 0;
                Enemy near = FindRightOrLeftNearestAliveEnemy(!toLeft);
                float distance = Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(currentSkillData.AttackTime);
                Sequence seq = DOTween.Sequence();
                seq.AppendInterval(1f);
                seq.AppendCallback(() =>
                {
                    LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6028);
                });
                LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6027);
                for (int i = 0; i < bulletNum; i++)
                {
                    GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet);
                    bullet = go.GetComponent<Bullet>();
                    bullet.skillData = currentSkillData;
                    //bullet.SetBulletType(Bullet.BulletType.ReflectButtle);
                    if (GameData.instance.fileHandler.currentMission != 0)
                    {
                        defaultDamege = currentSkillData.AttackValue;
                        defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
                    }

                    bullet.setDamage(defaultDamege);
                    bullet.isRemovable = true;
                    bullet.SetSpriteFrame(HotResManager.ReadAtlas("Bullet").GetSprite("jn" + currentSkillData.Id));
                    //bullet.transform.localScale = Globals.UnityValueTransform(currentSkillData.BulletScale) * Vector3.one * (0.8f + 0.4f * Random.value);
                    //bullet.setReactToWater(true);
                    //if (GameManager.instance.player.transform.position.y < -200)
                    //{
                    //    bullet.setReactToWater(false);
                    //}
                    bullet.transform.position = new Vector3(minX + 6 * (UnityEngine.Random.value), minY + 10 * UnityEngine.Random.value, 0);
                    rotation1.z = rot;
                    bullet.transform.rotation = Quaternion.Euler(rotation1);

                    bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
                    bullet.transform.localScale = Vector3.zero;
                    float scale = Globals.UnityValueTransform(currentSkillData.BulletScale) * (1.7f);


                    bullet.gameObject.SetActive(true);
                    bullet.PushBack = 0;
                    //Debug.Log("per:" + ((float)(5f / bulletNum)).ToString());
                    //Debug.Log("i " + ((float)(5f / bulletNum) * (float)i).ToString());
                    //bullet.SetReflectBulletParam(Globals.UnityValueTransform(currentSkillData.AttackTime), Globals.UnityValueTransform(realSkillSpeed));
                    bullet.SetBoomerangParamMax(near, rot, distance, Globals.UnityValueTransform(currentSkillData.AttackTime) - 2, ((float)i * (float)(1f / bulletNum)), scale);
                    bullet.SetRefectRota(false);
                    //yield return new WaitForSeconds(5/ bulletNum);
                }
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.Boomerang))//合金浮力镖
        {
            allBoomerangBullet ??= new List<Bullet>();
            allBoomerangBullet.Clear();
            bulletNum = currentSkillData.AttackCount;
            float distance = Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(currentSkillData.AttackTime);
            float dir = Vector2.SignedAngle(Vector2.right, Globals.nearestAliveEnemy - (Vector2)GameManager.instance.player.transform.position);
            dir = dir < 0 ? 360 + dir : dir;
            for (int i = 0; i < bulletNum; i++)
            {
                LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPlaySound", 6012);
                GameObject go = GameSharedData.Instance.GetBulletPrefab(currentSkillData.NextLv == 0 ? DefaultGameObjectType.BoomerangMax : DefaultGameObjectType.Boomerang);
                bullet = go.GetComponent<Bullet>();
                bullet.skillData = currentSkillData;
                bullet.SetBulletType(Bullet.BulletType.Boomerang);
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    defaultDamege = Helper.GetPlayerBulletRealDamage(currentSkillData.AttackValue, Globals.UnityValueTransform(currentSkillData.DamageCoe));
                }
                else
                {
                    defaultDamege = bulletDamageFromPlist + (GameManager.instance.player.Stats.attack * bulletMultiplierFromPlist);
                }
                bullet.setDamage(defaultDamege);
                bullet.isRemovable = true;
                Vector3 scale = Globals.UnityValueTransform(currentSkillData.BulletScale) * Vector3.one;
                bullet.transform.localScale = scale;
                bullet.setReactToWater(true);
                if (GameManager.instance.player.transform.position.y < -200)
                {
                    bullet.setReactToWater(false);
                }
                bullet.transform.position = GameManager.instance.player.transform.position;
                Vector3 rotation1 = playerSkeleton.transform.eulerAngles;

                rotation1.z = dir;
                var dest = new Vector2(distance * Mathf.Cos(Mathf.Deg2Rad * rotation1.z), distance * Mathf.Sin(Mathf.Deg2Rad * rotation1.z));

                rotation1.z += (360 / bulletNum) * (i - 1);

                bullet.transform.rotation = Quaternion.Euler(rotation1);
                bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
                bullet.gameObject.SetActive(true);
                bullet.PushBack = 0;
                bullet.SetBoomerangParam(Globals.UnityValueTransform(currentSkillData.AttackTime), dest, scale, currentSkillData.Level, distance);
                bullet.isInUse = true;
                GameSharedData.Instance.playerBulletInUse.Add(bullet);
                allBoomerangBullet.Add(bullet);

                SpriteRenderer spriteRenderer = bullet.transform.Find("biao1").GetComponent<SpriteRenderer>();
                spriteRenderer.sprite = HotResManager.ReadAtlas("Bullet").GetSprite("jn" + currentSkillData.Id);
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.Skyfire))//毒爆弹
        {
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
            var rotation = playerSkeleton.transform.eulerAngles;
            var angle = 360 / bulletNum;
            for (int i = 0; i < bulletNum; i++)
            {
                GameObject node = new GameObject();
                node.transform.position = playerSkeleton.transform.position;
                node.transform.rotation = Quaternion.Euler(Vector3.zero);
                for (int ii = 0; ii < 10; ii++)
                {
                    GameObject go = Instantiate(GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.Bullet));
                    go.transform.SetParent(node.transform, false);
                    var bullet2 = go.GetComponent<Bullet>();
                    bullet2.SetBulletType(Bullet.BulletType.FrontMultiCanon);
                    var damage = bulletDamageFromPlist + (GameManager.instance.player.Stats.attack * bulletMultiplierFromPlist);
                    bullet2.setDamage(0);
                    bullet2.skillData = currentSkillData;
                    bullet2.isRemovable = false;
                    bullet2.SetSpriteFrame(bulletSprites[0]);
                    bullet2.transform.localScale *= 1.3f;
                    bullet2.transform.position = GameManager.instance.player.transform.position;
                    Vector3 rotation1 = new Vector3(rotation.x, rotation.y, rotation.z + (angle * i));
                    bullet2.transform.rotation = Quaternion.Euler(rotation1);
                    bullet2.setRadiusEffectSquared(Globals.CocosToUnity(85));
                    bullet2.gameObject.SetActive(true);
                    bullet2.PushBack = 0;
                    Vector3 dest = new Vector3(Mathf.Cos(Mathf.Deg2Rad * rotation1.z), Mathf.Sin(Mathf.Deg2Rad * rotation1.z)) * ii;
                    bullet2.transform.DOBlendableMoveBy(dest, 0.4f).SetEase(Ease.InCubic);
                    bullet2.isInUse = true;
                    GameSharedData.Instance.skyFireBoomPool.Add(new SkyFireStruct(go, damage, currentSkillData.DamageTime / 10000, 2f, 0));
                }
                DOTween.Sequence().AppendInterval(0.4f).AppendCallback(() =>
                {
                    node.transform.DOBlendableRotateBy(new Vector3(0, 0, 180), 4f).OnComplete(() =>
                    {
                        float time = (currentSkillData.DamageTime / 10000) - 3;
                        if (time <= 0)
                        {
                            time = 1;
                        }
                        DOTween.Sequence().AppendInterval(time).AppendCallback(() =>
                        {
                            GameObject.Destroy(node);
                        });
                    });
                });
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.ProtectiveShield))//米氏力场
        {
            bulletNum = currentSkillData.NextLv != 0 ? bulletNum : 5;
            bool isCreater = false;
            if (protonBulletSlots == null)
            {
                isCreater = true;
            }

            //释放的时候清除以前的粒子球
            if (protonBulletSlots != null && protonBulletSlots.Length > 0 && protonBulletSlots.Length != bulletNum)
            {

                foreach (var item in protonBulletSlots)
                {
                    item.bullet.ResetBullet();
                }
                protonBulletSlots = null;
                isCreater = true;
            }
            if (isCreater)
            {

                protonBulletSlots = new ProtonBulletSlot[bulletNum];
                for (int i = 0; i < protonBulletSlots.Length; i++)
                {
                    protonBulletSlots[i].isOccupied = false;
                    protonBulletSlots[i].angle = (360 / bulletNum) * i;
                    if (!protonBulletSlots[i].isOccupied)
                    {
                        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);
                        GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.FireWell);
                        bullet = go.GetComponent<Bullet>();
                        bullet.skillData = currentSkillData;
                        Transform trail = go.transform.Find("fenghuolun1/Trail");
                        if (trail != null)
                        {
                            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
                            if (trailRenderer != null) trailRenderer.Clear();
                        }

                        bullet.gameObject.SetActive(true);
                        bullet.SetBulletType(Bullet.BulletType.ProtectiveShield);
                        if (GameData.instance.fileHandler.currentMission != 0)
                        {
                            //Debug.LogWarning("这个是技能ID" + currentSkillData.Id.ToString());
                            defaultDamege = currentSkillData.AttackValue;
                            defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
                        }
                        else
                        {
                            defaultDamege = bulletDamageFromPlist + (GameManager.instance.player.Stats.attack * bulletMultiplierFromPlist);
                        }
                        bullet.setDamage(defaultDamege);
                        bullet.isRemovable = true;
                        bullet.isInUse = true;
                        //bullet.spriteRenderer.sprite = bulletSprites[3];
                        //bullet.transform.GetChild(2).gameObject.SetActive(true);
                        float radius = Globals.CocosToUnity(100);

                        bullet.transform.position = GameManager.instance.player.transform.position * Vector2.one
                            + new Vector2(radius * Mathf.Cos(protonBulletSlots[i].angle * Mathf.Deg2Rad),
                            radius * Mathf.Sin(protonBulletSlots[i].angle * Mathf.Deg2Rad));
                        bullet.transform.localScale = Vector3.one * Globals.UnityValueTransform(currentSkillData.BulletScale);
                        bullet.canCollionWithNormalEnemyBullet = true;
                        GameSharedData.Instance.playerBulletInUse.Add(bullet);
                        protonBulletSlots[i].bullet = bullet;
                        protonBulletSlots[i].isOccupied = true;

                        bullet.spriteRenderer.sortingLayerName = "Player";
                        bullet.spriteRenderer.sortingOrder = -19;
                        MeshRenderer meshRenderer = bullet.transform.Find("fenghuolun1").GetComponent<MeshRenderer>();
                        meshRenderer.sortingLayerName = "PlayerBullet";
                        Sprite sp = HotResManager.ReadAtlas("Bullet").GetSprite("jn" + currentSkillData.Id);

                        meshRenderer.material.mainTexture = Helper.GetTextureFromSprite(sp);

                    }
                }
                bullet.SetProtectiveShield(protonBulletSlots, currentSkillData.NextLv == 0, Globals.UnityValueTransform(currentSkillData.AttackRadius), Globals.UnityValueTransform(realSkillSpeed));
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.IceBall))//CN冰晶
        {
            float cd = currentSkillData.MinCD / 1000;
            float time = Premier.Instance.GetServerTime();
            if (time - iceBallTime < cd) yield return null;
            iceBallTime = time;
            //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.warMachine, 1);
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6019);
            GameObject go = GameSharedData.Instance.GetBulletPrefab(DefaultGameObjectType.IceBall);
            bullet = go.GetComponent<Bullet>();
            bullet.skillData = currentSkillData;
            bullet.SetSpriteFrame(null);
            if (GameData.instance.fileHandler.currentMission != 0)
            {
                //Debug.LogWarning("这个是技能ID" + currentSkillData.Id.ToString());
                defaultDamege = currentSkillData.AttackValue;
                defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
            }
            else
            {
                defaultDamege = bulletDamageFromPlist + (GameManager.instance.player.Stats.attack * bulletMultiplierFromPlist);
            }
            bullet.setDamage(defaultDamege);
            bullet.isRemovable = true;
            //bullet.spriteRenderer.sprite = bulletSprites[4];
            bullet.transform.localScale = 2f * Vector3.one;

            bullet.setReactToWater(true);


            if (GameManager.instance.player.transform.position.y < -200)
            {
                bullet.setReactToWater(false);
            }

            bullet.transform.position = GameManager.instance.player.transform.position;
            Vector3 rotation1 = playerSkeleton.transform.eulerAngles;

            bullet.transform.rotation = Quaternion.Euler(rotation1);
            float AttackAngle = currentSkillData.AttackAngle; //360 / bulletNum;
            float bulletScale = Globals.UnityValueTransform(currentSkillData.BulletScale);
            float distance = 0;//15;
            Vector2 dest = new Vector2(distance * Mathf.Cos(Mathf.Deg2Rad * rotation1.z),
                                        distance * Mathf.Sin(Mathf.Deg2Rad * rotation1.z));

            bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));

            bullet.gameObject.SetActive(true);
            bullet.PushBack = 0;
            bullet.SetIceBallParam(Globals.UnityValueTransform(currentSkillData.AttackTime), dest, Globals.UnityValueTransform(realSkillSpeed), bulletNum, AttackAngle, bulletScale, defaultDamege, currentSkillData.DamageTime);
            bullet.isInUse = true;
            GameSharedData.Instance.playerBulletInUse.Add(bullet);
            //bullet.SetBulletType(Bullet.BulletType.IceBall);

        }
        else if (currentSkillData.Type == ((int)SkillType.Lightning))//电磁脉冲
        {
            foreach (var enemy in GameSharedData.Instance.enemyList.Where(x => x.Tag == 1))
            {
                enemy.Tag = 0;
            }
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6016);
            //LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6015);
            //AttackLikeLightning(new Vector2(GameManager.instance.player.transform.position.x, GameManager.instance.player.transform.position.y), true, currentSkillData);

            //Debug.LogWarning("这个是技能ID" + currentSkillData.Id.ToString());
            defaultDamege = currentSkillData.AttackValue;
            defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
            PulseAttack(GameManager.instance.player.transform.position, GameManager.instance.player.CollisionRadius, 1, 2, currentSkillData, defaultDamege);
        }
        else if (currentSkillData.Type == ((int)SkillType.Missile)) //霹雳10
        {
            //StopSkill_LaserRay().Forget();
            StartSkill_Missile(currentSkillData).Forget();

            //Transform playerTransform = GameManager.instance.player.transform;
            ////追踪半径
            //float distanceFromEnemy = 20;
            ////追踪夹角
            //float angle = 360;
            //Enemy nearestEnemy = null;
            //Vector3 pos = Vector3.zero;
            //foreach (Enemy enemy in GameSharedData.Instance.enemyList)
            //{
            //    if (enemy != null && enemy.canCheckDistance)
            //    {
            //        Vector2 directionToEnemy = ((Vector2)enemy.transform.position + enemy.offset) - (Vector2)playerTransform.position;
            //        directionToEnemy.Normalize();
            //        float angleToEnemy = Vector3.Angle(playerTransform.right, directionToEnemy);
            //        float dist = Vector2.Distance(((Vector2)enemy.transform.position + enemy.offset), playerTransform.position);
            //        if (angleToEnemy <= angle && dist < distanceFromEnemy)
            //        {
            //            distanceFromEnemy = dist;
            //            nearestEnemy = enemy;
            //            pos = enemy.transform.position;
            //        }
            //    }
            //}
            //AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.rocketeerShoot, 0.3f);
            //for (int i = 0; i < bulletNum; i++)
            //{
            //    LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPlaySound", 6023);
            //    PlayerMissile missile = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.UAV).GetComponent<PlayerMissile>();
            //    //missile.transform.Find("Trail").GetComponent<TrailRenderer>().Clear();
            //    missile.skillData = currentSkillData;
            //    missile.SpecialInit(pos);
            //    missile.curSpeed = Globals.UnityValueTransform(currentSkillData.AttackSpeed * GameManager.instance.player.stats.bulletSpeedAddRate);
            //    missile.acceleration = 100;
            //    //Vector3 rotation1 = playerSkeleton.transform.eulerAngles;
            //    //missile.transform.rotation = Quaternion.Euler(rotation1);
            //    //missile.isSpecialTracking = currentSkillData.Level == 4;
            //    missile.removeSoundID = 6024;
            //    missile.isInUse = true;
            //    defaultDamege = currentSkillData.AttackValue;
            //    defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
            //    missile.damage = defaultDamege;
            //    missile.transform.position = GameManager.instance.player.transform.position;
            //    missile.transform.localScale = Vector3.one * Globals.UnityValueTransform(currentSkillData.BulletScale);
            //    missile.boomRadius = Globals.UnityValueTransform(currentSkillData.DamageRadius);
            //    GameSharedData.Instance.playerMissilesInUse.Add(missile);
            //    missile.SurvivalTime = Globals.UnityValueTransform(currentSkillData.AttackTime);
            //    SpriteRenderer spriteRenderer = missile.transform.Find("daodan").GetComponent<SpriteRenderer>();
            //    spriteRenderer.sprite = HotResManager.ReadAtlas("Bullet").GetSprite("jn" + currentSkillData.Id);
            //    yield return new WaitForSeconds(currentSkillData.DamageTime / 1000);
            //}
        }
        else if (currentSkillData.Type == ((int)SkillType.WhiteCat))//僚机A
        {
            //bulletNum = currentSkillData.Level < 4 ? bulletNum : 8;
            //bool isCreater = false;
            if (whiteCatList == null)
            {
                //isCreater = true;
                Globals.numberOfWhiteCat = 0;
                whiteCatList = new List<WhiteCateStruct>();
            }

            //释放的时候清除以前的白猫
            //if (whiteCatList != null && whiteCatList.Length > 0 && whiteCatList.Length != bulletNum)
            //{

            //    foreach (var item in whiteCatList)
            //    {
            //        item.player.gameObject.SetActive(false);
            //    }
            //    whiteCatList = null;
            //    isCreater = true;
            //}
            //if (isCreater)
            //{
            //whiteCatList = new WhiteCateStruct[bulletNum];
            for (int i = 0; i < bulletNum; i++)
            {
                WhiteCateStruct item;
                if (whiteCatList.Count <= i)
                {
                    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);
                    GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.WhiteCat);
                    item.player = go.GetComponent<PlayerController>();
                    item.isOccupied = false;
                    item.angle = (360 / bulletNum) * i;
                    if (!item.isOccupied)
                    {
                        item.isOccupied = true;
                        item.player.gameObject.SetActive(true);
                        //float radius = Globals.CocosToUnity(100);
                        item.player.transform.position = GameManager.instance.player.transform.position + new Vector3(1f, 0, 0);

                        item.player.SetWhiteCatData(i);
                    }
                    whiteCatList.Add(item);
                }
                //}
                //bullet.SetProtectiveShield(protonBulletSlots, player, currentSkillData.Level == 4, Globals.UnityValueTransform(currentSkillData.AttackRadius), Globals.UnityValueTransform(realSkillSpeed));
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.Hammer))//热力电锤
        {
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6018);
            //bulletNum = currentSkillData.Level < 4 ? bulletNum : 5;
            Vector3 targetPosition = GameManager.instance.player.transform.position;
            if (Globals.nearestEnemy != null)
            {
                targetPosition = new Vector3(Globals.nearestEnemy.transform.position.x, Globals.nearestEnemy.transform.position.y, 0);
            }
            float distance = Globals.UnityValueTransform(realSkillSpeed) * Globals.UnityValueTransform(currentSkillData.AttackTime);

            defaultDamege = currentSkillData.AttackValue;
            defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));

            for (int i = 0; i < bulletNum; i++)
            {
                GameObject go = GameSharedData.Instance.GetBulletPrefab(currentSkillData.NextLv == 0 ? DefaultGameObjectType.HammerMax : DefaultGameObjectType.Hammer);
                bullet = go.GetComponent<Bullet>();
                bullet.skillData = currentSkillData;
                bullet.SetBulletType(Bullet.BulletType.HammerBullet);
                bullet.setDamage(defaultDamege);
                bullet.isRemovable = true;
                Vector3 scale = 0.5f * Globals.UnityValueTransform(currentSkillData.BulletScale) * Vector3.one;
                bullet.transform.localScale = Vector3.zero;
                bullet.setReactToWater(true);
                if (GameManager.instance.player.transform.position.y < -200)
                {
                    bullet.setReactToWater(false);
                }
                //y = a * x + b
                float angle = (GameManager.instance.player.RotationInDegrees < 90 || GameManager.instance.player.RotationInDegrees > 270) ? 315 : 225;
                float aValue = (GameManager.instance.player.RotationInDegrees < 90 || GameManager.instance.player.RotationInDegrees > 270) ? -1 : 1;

                float bValue = targetPosition.y - (targetPosition.x * aValue);

                Vector2 startPoint = new Vector2((Globals.UPPERBOUNDARY - bValue) / aValue, Globals.UPPERBOUNDARY);
                Vector2 endPoint = new Vector2((Globals.LOWERBOUNDARY - bValue) / aValue, Globals.LOWERBOUNDARY);

                bullet.transform.position = startPoint;
                bullet.transform.rotation = Quaternion.Euler(new Vector3(0, 0, angle));
                bullet.setRadiusEffectSquared(Globals.CocosToUnity(85));
                bullet.layerName = "Player";
                bullet.gameObject.SetActive(true);
                bullet.PushBack = 0;
                bullet.SetHammerParam(Globals.UnityValueTransform(currentSkillData.AttackTime), endPoint, scale, currentSkillData.Level);
                bullet.isInUse = true;
                GameSharedData.Instance.playerBulletInUse.Add(bullet);
                yield return new WaitForSeconds(0.2f);
            }
            if (currentSkillData.NextLv == 0)
            {
                Sequence seq = DOTween.Sequence();
                seq.AppendInterval(Globals.UnityValueTransform(currentSkillData.AttackTime) + 0.5f);
                seq.Append(GameManager.instance.backgroundFadeLayer.DOFade(0.4f, 0.5f));
                seq.AppendInterval(3.6f);
                seq.Append(GameManager.instance.backgroundFadeLayer.DOFade(0.01f, 0.5f));
                LightningAttack(defaultDamege, Globals.UnityValueTransform(currentSkillData.AttackTime));
            }
        }
        else if (currentSkillData.Type == ((int)SkillType.WhiteCatB))//僚机B
        {

            bool isCreater = false;
            if (whiteCatBGameObject == null)
            {
                isCreater = true;
            }
            if (isCreater)
            {
                Globals.numberOfWhiteCat = 0;
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.magicFire);
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.WhiteCat);
                go.SetActive(true);
                whiteCatBGameObject = go.GetComponent<PlayerController>();
                whiteCatBGameObject.transform.position = GameManager.instance.player.transform.position;
                whiteCatBGameObject.SetWhiteCatDataB(currentSkillData.Level);
            }
            if (tempSkillLevel != currentSkillData.Level)
            {
                tempSkillLevel = currentSkillData.Level;
                whiteCatBGameObject.UpdateWhiteCatDataBSkill(currentSkillData.Level);
            }
        }
        else if (currentSkillData.Type == (int)SkillType.LaserRay_Weapon
            || currentSkillData.Type == (int)SkillType.LaserRay)
        {
            //StopSkill_LaserRay().Forget();
            StartSkill_LaserRay(currentSkillData).Forget();
        }
        yield return null;
    }

    /// <summary>
    /// 技能启动:霹雳10导弹
    /// </summary>
    private async UniTaskVoid StartSkill_Missile(CatSkill.Item csvRow_CatSkill)
    {
        await UniTask.NextFrame();

        float count = LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.攻击距离);
        float percent = LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.攻击距离);
        float distance = (Globals.playerAssistDirection + count) * (1 + percent / 10000f);

        // 在攻击范围内找最近敌人进行攻击
        var enemy = GameSharedData.Instance.enemyList.Where(x => !x.isDestroyed && x.canCheckDistance && !x.laserDamaging)
            .Select(x => new { Enemy = x, Distance = playerController.transform.position.CalcDistance2D_PointToCircle(x.transform.position, x.enemyCollisionRadius) })
            .Where(x => x.Distance.DistanceToEdge - playerController.CollisionRadius <= distance)
            .OrderBy(x => x.Distance)
            .FirstOrDefault()?.Enemy;

        if (enemy)
        {
            var sprite = playerController.Sprites.FirstOrDefault(x => x.Name == "daodan").Sprite;
            if (csvRow_CatSkill.NextLv == 0)
            {
                sprite = playerController.Sprites.FirstOrDefault(x => x.Name == "daodan1").Sprite;
            }
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.rocketeerShoot, 0.3f);

            var missileNum = csvRow_CatSkill.AttackCount;
            // 读出创建导弹所需的敌人数据（到了下一帧敌人可能就已经死了而不能读）
            var enemyPosition = enemy.transform.position;
            var enemyRadius = enemy.enemyCollisionRadius;

            for (int i = 0; i < csvRow_CatSkill.AttackCount; i++)
            {
                // 对称轨迹
                /*
                var missile = new Missile()
                {
                    MissileEjecter = new GameObject("MissileEject"),
                    Enemy = enemy,
                    EndPosition = enemyPosition,
                    EnemyRadius = enemyRadius,
                    csvRow_CatSkill = csvRow_CatSkill,
                    Speed = Globals.UnityValueTransform(csvRow_CatSkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate),
                };

                missile.MissileEjecter.transform.parent = playerController.transform;
                missile.MissileEjecter.transform.position = playerController.transform.position;

                var dir = enemyPosition - missile.MissileEjecter.transform.position;
                missile.MissileEjecter.transform.right = dir.normalized;

                DoSkill_MissilePath(missile, sprite, csvRow_CatSkill).Forget();
                */

                // 随机轨迹
                var missile1 = new Missile()
                {
                    MissileEjecter = new GameObject("MissileEject"),
                    Enemy = enemy,
                    EndPosition = enemyPosition,
                    EnemyRadius = enemyRadius,
                    csvRow_CatSkill = csvRow_CatSkill,
                    Speed = Globals.UnityValueTransform(csvRow_CatSkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate),
                };

                missile1.MissileEjecter.transform.parent = playerController.transform;
                missile1.MissileEjecter.transform.position = playerController.transform.position;

                var dir1 = enemyPosition - missile1.MissileEjecter.transform.position;
                missile1.MissileEjecter.transform.right = dir1.normalized;
                DoSkill_MissilePath(missile1, sprite, csvRow_CatSkill).Forget();

                var missile2 = new Missile()
                {
                    MissileEjecter = new GameObject("MissileEject"),
                    Enemy = enemy,
                    EndPosition = enemyPosition,
                    EnemyRadius = enemyRadius,
                    csvRow_CatSkill = csvRow_CatSkill,
                    Speed = Globals.UnityValueTransform(csvRow_CatSkill.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate),
                };

                missile2.MissileEjecter.transform.parent = playerController.transform;
                missile2.MissileEjecter.transform.position = playerController.transform.position;

                var dir2 = enemyPosition - missile2.MissileEjecter.transform.position;
                missile2.MissileEjecter.transform.right = dir2.normalized;
                DoSkill_MissilePath(missile2, sprite, csvRow_CatSkill, false).Forget();
            }
        }
    }

    /// <summary>
    /// 使用同一轨迹连续发射导弹
    /// </summary>
    /// <param name="up">轨迹是否在上边(否：在下边)</param>
    private async UniTaskVoid DoSkill_MissilePath(Missile missile, Sprite sprite, CatSkill.Item csvRow_CatSkill, bool up = true)
    {
        await UniTask.Delay(missile.EjectDelay);
        try
        {
            if (csvRow_CatSkill.AttackRadius <= 0)
            {
                Debug.LogError($"技能配置错误:[ID={csvRow_CatSkill.Id}] AttackRadius用于发射间隔时长,应该大于0");
                return;
            }

            for (int m = 0; m < csvRow_CatSkill.DamageTime; m += csvRow_CatSkill.AttackRadius)
            {
                DoSkill_MissileOne(missile, sprite, up);
                await UniTask.Delay(csvRow_CatSkill.AttackRadius);
            }
        }
        catch (System.OperationCanceledException)
        {
            throw;
        }
        catch (System.Exception ex)
        {
            Debug.LogException(ex);
            Object.Destroy(missile.MissileEjecter);
        }
    }

    /// <summary>
    /// 创建一个导弹,并追踪打击敌人
    /// </summary>
    /// <param name="missile">导弹发射器</param>
    /// <param name="sprite">导弹图片</param>
    /// <param name="up">轨迹是否在上边(否：在下边)</param>
    private void DoSkill_MissileOne(Missile missile, Sprite sprite, bool up = true)
    {
        if (missile.Enemy && !missile.Enemy.isDestroyed)
        {

            var missileObj = new GameObject("missle");
            {
                missileObj.transform.parent = missile.MissileEjecter.transform;
                missileObj.transform.position = missile.MissileEjecter.transform.position;
                var sr = missileObj.AddComponent<SpriteRenderer>();
                sr.sprite = sprite;
                missileObj.SetActive(true);
            }
            // 可能是导弹飞出声
            LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPlaySound", 6023);

            // 追踪敌人位置
            Observable.EveryUpdate().Where(_ => Time.deltaTime > 0).Subscribe(_ =>
            {
                missile.EndPosition = missile.Enemy.transform.position;
            }).AddTo(missile.Enemy);

            missile.EjectNum++;
            // 已飞行时长
            var durationFly = 0f;
            // 已飞行时长占比
            var pctFly = 0f;
            /*在 ARM 系统上，float.Epsilon 太小而无法检测到，因此它等于零。
             *可以改为定义等于 1.175494351E-38 的备用 epsilon 值。*/
            //每帧至少飞行万分之一(时长占比)
            var pctStepMin = 0.0001f;

            // 导弹飞行
            Observable.EveryUpdate().Where(_ => Time.deltaTime > 0).Subscribe(async _ =>
            {
                durationFly += Time.deltaTime;
                if (durationFly > 0f)
                {
                    // 计算全路径(有优化余地,比如将固定点和变化点分为两部分计算)
                    var positions1 = missile.CalcKeyPositions();
                    var cubicBezier = new CubicBezierPath(positions1.ToArray());

                    // 曲线距离(近似值)
                    var distance = cubicBezier.ComputeApproxLength();
                    // 飞行全线所需时长
                    var duration = distance / missile.Speed;
                    // 这一帧时长占比增量
                    var pctInc = Time.deltaTime / duration;
                    if (pctInc < pctStepMin) pctInc = pctStepMin;//保证增量不小于最小值

                    pctFly += pctInc;
                    // 飞到了
                    if (pctFly >= 0.9999)
                    {
                        var n = --missile.EjectNum;

                        if (missile.Enemy && !missile.Enemy.isDestroyed)
                        {
                            var damage = Helper.GetPlayerBulletRealDamage(missile.csvRow_CatSkill.AttackValue, Globals.UnityValueTransform(missile.csvRow_CatSkill.DamageCoe));
                            GameManager.instance.physicsManager.EnemyTakeHit(missile.Enemy, damage);
                        }

                        Destroy(missileObj);

                        // 可能是爆炸声
                        LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPlaySound", 6024);

                        // 导弹全销毁后，发射器也销毁
                        if (missile.EjectNum <= 0)
                        {
                            await UniTask.WaitForEndOfFrame(playerController.UniTaskWaiter, this.GetCancellationTokenOnDestroy());
                            Destroy(missile.MissileEjecter);
                        }

                        return;
                    }

                    #region 导弹前进一次(直接修改位置)
                    var p1 = cubicBezier.GetPointNorm(pctFly);
                    var tan1 = cubicBezier.GetTangentNorm(pctFly);
                    var p = missile.MissileEjecter.transform.TransformPoint(p1);
                    var dir = missile.MissileEjecter.transform.TransformPoint(tan1);

                    if (!up)
                    {
                        var p2 = new Vector3(p1.x, -p1.y);
                        var tan2 = new Vector3(tan1.x, -tan1.y);
                        p = missile.MissileEjecter.transform.TransformPoint(p2);
                        dir = missile.MissileEjecter.transform.TransformPoint(tan2);
                    }

                    missileObj.transform.position = p;
                    missileObj.transform.right = dir - missile.MissileEjecter.transform.position;
                    #endregion
                }
            }).AddTo(missile.MissileEjecter);
        }
    }

    /*
    void InitLaser(CatSkill.Item csvRow_CatSkill)
    {
        if (laserInited) return;
        laserInited = true;

        // LaserStart Begin
        GameObject ls = Instantiate(_weapon.laserStartPrefab);

        var pos = playerSkeleton.position;
        ls.transform.position = pos;
        ls.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));

        ls.transform.parent = playerSkeleton.parent;
        //ls.transform.localScale = new Vector3(4, 4, 1);

        laserStartSkeleton = ls.GetComponent<SkeletonAnimation>();
        laserStartSkeleton.gameObject.SetActive(false);
        // LaserStart End


        // Laser Begin
        for (int i = 0; i < laserNum; i++)
        {
            GameObject laser = Instantiate(_weapon.laserPrefab);
            laser.transform.position = pos;
            laser.transform.parent = playerSkeleton.parent;

            SpriteRenderer lr = laser.GetComponent<SpriteRenderer>();
            lr.drawMode = SpriteDrawMode.Sliced;
            laserRenderers.Add(lr);
            laser.SetActive(false);
        }

        //Laser End

        // LaserImpact Begin
        for (int i = 1; i <= laserNum * 2; i++)
        {
            GameObject li = Instantiate(_weapon.laserImpactPrefab);
            li.transform.localScale = new Vector3(1f, 1.25f + (csvRow_CatSkill.Level - 1) * 0.45f, 1);
            li.transform.parent = playerSkeleton.parent;
            li.transform.position = pos;
            li.transform.localRotation = Quaternion.Euler(new Vector3(180, 0, 180));
            li.SetActive(false);

            if (i % 2 != 0)
            {
                laserStartImpacts.Add(li);
            }
            else
            {
                laserEndImpacts.Add(li);
            }
        }
        // LaserImpact End

        // LaserStartParticle Start
        GameObject lp = Instantiate(_weapon.laserStartParticlePrefab);
        lp.transform.position = pos + Vector3.right * 0.1f;
        lp.transform.parent = playerSkeleton.parent;
        lp.transform.localScale = Vector3.one * (GameData.instance.fileHandler.currentMission != 0 ? Globals.UnityValueTransform(csvRow_CatSkill.BulletScale) : 1);
        laserStartParticle = lp.GetComponent<ParticleSystem>();
        lp.SetActive(false);
        // LaserStartParticle End

        // LaserStartGlow Start
        GameObject sg = Instantiate(_weapon.laserStartGlowPrefab);
        sg.transform.position = pos + Vector3.right * 0.1f;
        sg.transform.parent = playerSkeleton.parent;
        laserStartGlowAnim = sg.GetComponent<LaserStartGlow>();
        // LaserStartGlow End
    }
    */

    /// <summary>
    /// 技能启动:激光射线
    /// </summary>
    private async UniTaskVoid StartSkill_LaserRay(CatSkill.Item csvRow_CatSkill)
    {
        try
        {
            await UniTask.NextFrame();
            if (LaserShooter != null)
            {
                Debug.LogWarning("激光射线多次启动！自动忽略");
                return;
            }

            //Debug.LogError("激光射线启动");
            LaserShooter = new LaserShooter();
            await LaserShooter.CreateLightGlow(_weapon, csvRow_CatSkill.BulletScale);

            //激光伤害值
            defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(csvRow_CatSkill.DamageCoe));
            var angleStep = 360f / csvRow_CatSkill.AttackCount;
            for (var i = 0; i < csvRow_CatSkill.AttackCount; i++)
            {
                var angleInit = angleStep * i;

                // 激光长度和宽度
                var lenght = Globals.UnityValueTransform(csvRow_CatSkill.AttackRadius);
                var width = Globals.UnityValueTransform(csvRow_CatSkill.DamageRadius);

                LaserOne(LaserShooter.LaserCancel.Token, angleInit, lenght, width,
                    csvRow_CatSkill.Level, csvRow_CatSkill.AttackAngle, defaultDamege, csvRow_CatSkill).Forget();
            }

            await UniTask.Delay(System.TimeSpan.FromSeconds(Globals.UnityValueTransform(csvRow_CatSkill.AttackTime)), cancellationToken: LaserShooter.LaserCancel.Token);
            if (LaserShooter.LaserCancel.IsCancellationRequested) return;

            // 技能正常结束
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.laserEnd, 0.5f);
            StopSkill_LaserRay(true).Forget();
        }
        catch (System.OperationCanceledException)
        {
            throw;
        }
        catch (System.Exception ex)
        {
            Debug.LogException(ex);
            StopSkill_LaserRay(false).Forget();
        }
    }

    /// <summary>
    /// 创建一条激光,旋转并伤害碰到的敌人
    /// </summary>
    /// <param name="angleInit">初始角度</param>
    /// <param name="lenght">激光长度</param>
    /// <param name="width">激光宽度</param>
    /// <param name="skillLevel">技能等级</param>
    /// <param name="angleSpeed">旋转角速度</param>
    /// <param name="damage">激光伤害值</param>
    private async UniTaskVoid LaserOne(CancellationToken cancel, float angleInit, float lenght, float width,
        int skillLevel, float angleSpeed, double damage, CatSkill.Item csvRow_CatSkill)
    {
        await UniTask.SwitchToMainThread();

        try
        {
            // 计算激光终点的初始位置
            var pInit = playerSkeleton.position - playerSkeleton.parent.right * lenght;
            if (angleInit != 0)
            {
                pInit = pInit.RotateAround(playerSkeleton.position, Vector3.forward, angleInit);
            }

            // 创建激光
            var laser = LaserShooter.EjectLaser(_weapon, pInit, skillLevel);
            var sr = laser.LaserMiddle.GetComponent<SpriteRenderer>();
            sr.size = new Vector2(lenght, width);

            // 更换图片
            if (csvRow_CatSkill.NextLv != 0)
            {
                sr.sprite = playerController.Sprites.FirstOrDefault(x => x.Name == "laserParticles01")?.Sprite;
            }
            else
            {
                sr.sprite = playerController.Sprites.FirstOrDefault(x => x.Name == "laserParticles04")?.Sprite;
            }

            var laserMaterial = sr.GetComponent<MaterialMovement>();
            laserMaterial.ResetOffset();
            laser.LaserMiddle.SetActive(true);
            laser.LaserBegin.SetActive(true);
            laser.LaserEnd.SetActive(true);

            Observable.EveryUpdate().Where(_ => Time.deltaTime > 0).Subscribe(_ =>
            {
                try
                {
                    // 更新 终点位置 和 激光的方向
                    var angleRotate = angleSpeed * Time.deltaTime;
                    laser.LaserEnd.transform.position = laser.LaserEnd.transform.position.RotateAround(laser.LaserBegin.transform.position, Vector3.forward, angleRotate);
                    laser.LaserBegin.transform.right = laser.LaserMiddle.transform.right = laser.LaserEnd.transform.position - laser.LaserBegin.transform.position;

                    laserMaterial.OffsetUpdate();

                    // 碰到激光的都受伤
                    var hitEnemies = GameSharedData.Instance.GetEnemiesTouchLaser(laser.LaserBegin.transform, lenght, width);
                    foreach (var hitEnemy in hitEnemies)
                    {
                        GameManager.instance.physicsManager.LaserHitEnemy(damage, hitEnemy);
                    }
                }
                catch (System.OperationCanceledException)
                {
                    throw;
                }
                catch (System.Exception ex)
                {
                    Debug.LogException(ex);
                    StopSkill_LaserRay(false).Forget();
                }
            }).AddTo(laser.LaserMiddle)
            .AddTo(cancel);
        }
        catch (System.OperationCanceledException)
        {
            throw;
        }
        catch (System.Exception ex)
        {
            Debug.LogError("激光射线：创建激光失败");
            Debug.LogException(ex);
        }
    }

    /// <summary>
    /// 技能结束:销毁激光
    /// </summary>
    /// <param name="shouldPlaySound">是否播放激光结束音效</param>
    private async UniTaskVoid StopSkill_LaserRay(bool shouldPlaySound)
    {
        await UniTask.WaitForEndOfFrame(playerController.UniTaskWaiter, this.GetCancellationTokenOnDestroy());

        if (LaserShooter != null)
        {
            LaserShooter.LaserCancel.Cancel();
            LaserShooter.LaserCancel = new CancellationTokenSource();

            LaserShooter.Destroy();
            LaserShooter = null;

            //Debug.LogError("激光射线销毁");

            if (shouldPlaySound)
            {
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.laserEnd, 0.5f);
                GameManager.instance.ToggleDarkEffect(false);
            }
        }
    }

    private void LightningAttack(double defaultDamege, float runTime)
    {
        for (int i = 0; i < 30; i++)
        {
            Sequence seq = DOTween.Sequence();
            seq.AppendInterval(RandomNum.RandomFloat(0, 3f, 10000) + runTime);
            seq.AppendCallback(() =>
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.LighthningPrefab);
                LightningAttackEnemy lightningAttack = go.GetComponent<LightningAttackEnemy>();
                lightningAttack.damageValue = defaultDamege;
                lightningAttack.PlayLightningAnim();
            });
        }
    }

    private void AttackLikeLightning(Vector2 startPos, bool drawLine, CatSkill.Item currentSkillData)
    {
        int attackCount = 0;
        //Debug.LogWarning("这个是技能ID" + currentSkillData.Id.ToString());
        defaultDamege = currentSkillData.AttackValue;
        defaultDamege = Helper.GetPlayerBulletRealDamage(defaultDamege, Globals.UnityValueTransform(currentSkillData.DamageCoe));
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.Tag != 2 && !enemy.isDestroyed && enemy.canCheckDistance)
            {
                if (Vector2.Distance((Vector2)enemy.transform.position + enemy.offset, startPos) < Globals.UnityValueTransform(currentSkillData.AttackRadius) && enemy.Tag != 1)
                {
                    attackCount++;
                    if (drawLine)
                    {
                        GameObject zapl;
                        if (LightSoundOnce)
                        {
                            LightSoundOnce = false;
                            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6016);
                        }

                        if (currentSkillData.NextLv != 0)
                        {
                            zapl = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Zapline);
                        }
                        else
                        {
                            zapl = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.ZaplineMax);
                        }
                        zapl.SetActive(true);
                        zapl.transform.position = startPos.GetMidPoint(enemy.transform.position);
                        var dir = Vector2.SignedAngle(Vector2.right, (Vector2)enemy.transform.position - startPos);
                        dir = dir < 0 ? 360 + dir : dir;
                        zapl.transform.rotation = Quaternion.AngleAxis(dir, Vector3.forward);
                        float boxsize = zapl.GetComponent<SpriteRenderer>().bounds.size.x * 1.1f;
                        if (boxsize == 0)
                        {
                            boxsize = 0.00001f;
                        }
                        zapl.transform.SetScaleX(Vector2.Distance(startPos, enemy.transform.position) / boxsize);
                        zapl.transform.SetScaleY(3);
                    }
                    GameObject zap = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Zapper);
                    enemy.Tag = 1;
                    zap.transform.position = enemy.transform.position;
                    zap.SetActive(true);
                    string tweenId = "tesla" + zap.GetInstanceID();
                    DOTween.Sequence().SetId(tweenId).AppendInterval(0.1f).AppendCallback(() => { zap.SetActive(false); }).Play();
                    zap.transform.SetScale(enemy._jsonScale / 3);
                    if (attackCount < 4)
                    {
                        AttackLikeLightning(enemy.transform.position, true, currentSkillData);
                    }
                    LuaToCshapeManager.Instance.AddSkillDamageCount(currentSkillData.Type, defaultDamege);
                    if (enemy.TakeHit(defaultDamege))
                    {
                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            LuaToCshapeManager.Instance.curPhysicsManager.DestroyEnemy(enemy);
                        }

                    }

                }
            }
        }
    }

    /// <summary>
    /// 电磁脉冲 的攻击
    /// </summary>
    /// <param name="startPos">脉冲起点</param>
    /// <param name="startRadius">起点的半径(算距离时减去该值 和 怪物的半径)</param>
    /// <param name="pulseTimes">表示这是第几次脉冲(1开始)</param>
    /// <param name="maxTimes">最大脉冲次数(递归次数)</param>
    /// <param name="currentSkillData">技能数据</param>
    /// <param name="damage">伤害量</param>
    private void PulseAttack(Vector2 startPos, float startRadius, int pulseTimes, int maxTimes, CatSkill.Item currentSkillData, double damage)
    {
        if (pulseTimes > maxTimes) return;

        // 查找最近的敌人
        var enemies = GameSharedData.Instance.enemyList.Where(x => !x.isDestroyed && x.canCheckDistance && x.Tag == 0)
            .Select(x => new { Enemy = x, Distance = ((Vector3)startPos).CalcDistance2D_PointToCircle(x.transform.position, x.enemyCollisionRadius) })
            .Where(x => x.Distance.DistanceToEdge - startRadius <= Globals.UnityValueTransform(currentSkillData.AttackRadius))
            .OrderBy(x => x.Distance)
            .Take(currentSkillData.AttackCount);

        // 对找到的每个敌人进行攻击
        foreach (var e in enemies)
        {
            #region 脉冲效果
            GameObject zapl;
            if (currentSkillData.NextLv != 0)
            {
                zapl = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Zapline);
            }
            else
            {
                zapl = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.ZaplineMax);
            }
            zapl.SetActive(true);
            zapl.transform.SetScaleX(0);
            zapl.transform.position = startPos;
            var zaplEndPoint = startPos.GetMidPoint(e.Enemy.transform.position);
            var dir = Vector2.SignedAngle(Vector2.right, (Vector2)e.Enemy.transform.position - startPos);
            dir = dir < 0 ? 360 + dir : dir;
            zapl.transform.rotation = Quaternion.AngleAxis(dir, Vector3.forward);
            float boxsize = zapl.GetComponent<SpriteRenderer>().bounds.size.x * 1.1f;
            if (boxsize == 0)
            {
                boxsize = 0.00001f;
            }

            zapl.transform.SetScaleY(3);

            GameObject zap = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Zapper);
            e.Enemy.Tag = 1;
            zap.transform.position = startPos;
            zap.SetActive(true);
            zap.transform.SetScale(e.Enemy._jsonScale / 3);

            // 计算距离和所需时长
            var s = Vector2.Distance(startPos, e.Enemy.transform.position);
            var realSkillSpeed = Globals.UnityValueTransform(currentSkillData.AttackSpeed * GameManager.instance.player.Stats.bulletSpeedAddRate);
            var t = s / realSkillSpeed;

            var sequence = DOTween.Sequence();
            sequence.Append(zapl.transform.DOScaleX(s / boxsize, t).SetEase(Ease.Linear).OnComplete(() =>
            {
                // 蔓延并伤害敌人
                PulseAttack(e.Enemy.transform.position, 0, pulseTimes + 1, maxTimes, currentSkillData, damage);

                LuaToCshapeManager.Instance.AddSkillDamageCount(currentSkillData.Type, damage);
                if (e.Enemy.TakeHit(defaultDamege))
                {
                    if (!e.Enemy.isDestroyed)
                    {
                        e.Enemy.isDestroyed = true;
                        LuaToCshapeManager.Instance.curPhysicsManager.DestroyEnemy(e.Enemy);
                    }
                }
            }));
            sequence.Insert(0, zapl.transform.DOMove(zaplEndPoint, t).SetEase(Ease.Linear));
            sequence.Insert(0, zap.transform.DOMove(e.Enemy.transform.position, t).SetEase(Ease.Linear).OnComplete(() =>
            {
                zap.SetActive(false);
            }));
            sequence.Play();
            #endregion
        }
    }

    /// <summary>
    /// 获取玩家左边或者右边最近的敌人
    /// </summary>
    /// <param name="isRight"></param>
    /// <returns></returns>
    public Enemy FindRightOrLeftNearestAliveEnemy(bool isRight)
    {
        Transform playerTransform = GameManager.instance.player.transform;
        float distanceFromEnemy = 200;
        Enemy nearestEnemy = null;
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy != null)
            {
                if (isRight)
                {
                    if (enemy.transform.position.x > playerTransform.position.x)
                    {
                        float dist = Vector2.Distance(enemy.transform.position, playerTransform.position);
                        if (dist < distanceFromEnemy)
                        {
                            distanceFromEnemy = dist;
                            nearestEnemy = enemy;
                        }
                    }
                }
                else
                {
                    if (enemy.transform.position.x < playerTransform.position.x)
                    {
                        float dist = Vector2.Distance(enemy.transform.position, playerTransform.position);
                        if (dist < distanceFromEnemy)
                        {
                            distanceFromEnemy = dist;
                            nearestEnemy = enemy;
                        }
                    }
                }
            }
        }
        return nearestEnemy;
    }
}

public class SkyFireStruct
{
    public GameObject _go;
    /// <summary>
    /// 技能伤害
    /// </summary>
    public double _damage;
    /// <summary>
    /// 持续时间(单位/秒)
    /// </summary>
    public float _totleTime;
    /// <summary>
    /// 伤害范围半径
    /// </summary>
    public float _skyFireRadius;
    /// <summary>
    /// 伤害间隔时间(单位/秒)，默认：1秒
    /// </summary>
    public float _injuryInterval;


    public float _currentTime;
    //伤害次数，按照每秒一次
    public float _damageCount;

    public SkyFireStruct(GameObject go, double damage, float totleTime, float radius, float injuryInterval = 1f)
    {
        _go = go;
        _damage = damage;
        _totleTime = totleTime;
        _skyFireRadius = radius;
        _injuryInterval = injuryInterval > 0 ? injuryInterval : 0.1f;
        _currentTime = 0;
        _damageCount = 0;
    }
}
