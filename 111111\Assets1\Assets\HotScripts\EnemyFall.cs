using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class EnemyFall : Enemy
{
    [SerializeField] private GameObject flame;
    [SerializeField] private ParticleSystem trail;

    [HideInInspector] public bool isInUse = false;

    private int enemyType;
    private Vector2 acceleration;
    private Enemy enemy;



    public void InitFallEnemy(Enemy e, int type)
    {
        tweenId = "EF" + GetInstanceID();
        schedulerId = "EFS" + GetInstanceID();
        enemy = e;
        enemyType = type;
        Init();
    }

    public override void Init()
    {
        if (initialized)
            return;
        InitStats();
        isAFillerEnemy();
        enemyCollisionRadius = enemy.enemyCollisionRadius;
        explosionType = Explosions.ExplosionType.ExplosionTypeEnemyFall;
        gameObject.SetActive(true);
        base.Init();
        _allowKillPoint = false;
        allowRelocate = false;
        enemySprite.gameObject.SetActive(true);
        if (enemy.isBoss)
        {

        }
        else
        {
            //spSkeleton_setAttachment(enemySprite.getSkeleton(), "Pumpkin", "Pumpkin");
        }

        if (enemy.enemySprite == null || enemySprite == null)
        {
            base.Destroy();
            return;
        }

        if (enemyType == 1)
        {
            enemySprite.skeleton.SetSkin("enemyPlaneLevel1");
            enemySprite.state.TimeScale = 0.5f;
        }
        else if (enemyType == 2)
        {
            enemySprite.skeleton.SetSkin("enemyPlaneLevel2");
            enemySprite.state.TimeScale = 1.2f;
        }
        else if (enemyType == 3)
        {
            enemySprite.skeleton.SetSkin("enemyPlaneLevel3");
            if (enemy.isBoss)
            {
                enemySprite.skeleton.SetSkin("enemyPlaneLevel6");
                enemy.stats.speed = 5;

            }

        }
        else if (enemyType == 4)
        {
            enemySprite.skeleton.SetSkin("enemyPlaneLevel4");
            enemySprite.state.TimeScale = 1.0f;//watermissile
        }
        else if (enemyType == 5)
        {
            enemySprite.skeleton.SetSkin("enemyPlaneLevel5");
            enemySprite.state.TimeScale = 2.0f;
        }
        enemySprite.state.TimeScale = 0.5f;
        enemySprite.transform.SetRotation(enemy.enemySprite.transform.GetRotation());
        enemySprite.transform.localScale = enemy.enemySprite.transform.localScale;
        transform.position = enemy.transform.position;
        flame.SetActive(true);


        acceleration.x = Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) * enemy.stats.speed * -1;
        acceleration.y = Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.GetRotation() - 90)) * enemy.stats.speed;
        scheduleUpdate = true;
        trail.Play();
        scheduleUpdate = true;
        DOTween.Sequence().SetId(tweenId).Append(enemySprite.transform.DOLocalRotate(new Vector3(0, 0, -90), 4.0f)).Play();
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        transform.SetWorldPosition(transform.position.x + acceleration.x * Time.deltaTime * Globals.CocosToUnity(60),
            transform.position.y + acceleration.y * Time.deltaTime * Globals.CocosToUnity(60));
        acceleration.x = acceleration.x * 0.99999f;
        acceleration.y = acceleration.y - 0.05f * Time.deltaTime * 60;// * Time.deltaTime * Globals.CocosToUnity(60);


        if (transform.position.y < Globals.LOWERBOUNDARY)
        {
            scheduleUpdate = false;
            Explosions.ExplosionType type = Explosions.ExplosionType.ExplosionTypeBuilding;
            if (Globals.gameType == GameType.Arena)
            {
                type = Explosions.ExplosionType.ExplosionTypeBuildingMission;
            }
            GameSharedData.Instance.explosions.GenerateParticlesAt(type, transform.position, false, 1, 0.5f, 0);
            enemySprite.gameObject.SetActive(false);
            scheduleUpdate = false;
            trail.Stop();

            for (int i = 0; i < 3; i++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;
                debris.CreateWithData(400, 5, true);
                debris.transform.position = transform.position;
            }
            //if (_isFiller)
            //{
            //    Globals.numberOfEnemies--;
            //}

            base.Destroy();
            //RemoveEnemy();
            //GameSharedData.Instance.enemyList.Remove(this);
            //Destroy(gameObject);

        }
    }

    public override bool TakeHit(double damage)
    {
        if (healthBar && enemySprite != null)
        {
            stats.health -= damage;
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            enemySprite.GetComponent<Renderer>().material.DOKill();
            enemySprite.GetComponent<Renderer>().material.color = Color.red;
            enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
        }

        if (stats.health < 0)
        {
            return true;
        }

        return false;
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 0;
        stats.health = baseStats.health = enemy.stats.maxHealth.Value;
        stats.turnSpeed = baseStats.turnSpeed = 0;
        stats.bulletDamage = baseStats.bulletDamage = 0;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 0;
        stats.coinAwarded = baseStats.coinAwarded = 0;
        stats.missileDamage = baseStats.missileDamage = 150;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
    }

    //private IEnumerator Reset()
    //{
    //    yield return new WaitForEndOfFrame();
    //    GameSharedData.Instance.enemyList.Remove(this);
    //    yield return new WaitForSeconds(2f);
    //    transform.position = Vector3.zero;
    //    enemySprite.transform.rotation = Quaternion.identity;
    //    trail.Stop();
    //    flame.gameObject.SetActive(false);
    //    enemySprite.gameObject.SetActive(false);
    //    gameObject.SetActive(false);
    //}
}
