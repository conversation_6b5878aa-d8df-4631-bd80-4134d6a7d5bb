﻿using UnityEngine;

namespace Apq.Extension
{
    public static class Ext_UniEngine
    {
        /// <summary>
        /// 获取向量所在象限(1-4)
        /// </summary>
        public static int GetQuadrant(this Vector2 me)
        {
            return me.x switch
            {
                < 0 when me.y > 0 => 2,
                < 0 when me.y < 0 => 3,
                > 0 when me.y < 0 => 4,
                _ => 1
            };
        }

        /// <summary>
        /// 将向量镜像到目标象限
        /// </summary>
        /// <param name="me"></param>
        /// <returns></returns>
        public static Vector2 MirrorToQuadrant(this Vector2 me, int quadrant)
        {
            return quadrant switch
            {
                1 => new Vector2(Math<PERSON>.Abs(me.x), Mathf.Abs(me.y)),
                2 => new Vector2(-Mathf.Abs(me.x), Mathf.Abs(me.y)),
                3 => new Vector2(-Mathf.Abs(me.x), -Mathf.Abs(me.y)),
                4 => new Vector2(Math<PERSON>.Abs(me.x), -Mathf.Abs(me.y)),
                _ => me
            };
        }
        
        /// <summary>
        /// 点到线段的最短距离
        /// </summary>
        /// <param name="me">点</param>
        /// <param name="p1">线段的端点1</param>
        /// <param name="p2">线段的端点2</param>
        public static float CalcDistance_PointToLineSegment(this Vector3 me, Vector3 p1, Vector3 p2)
        {
            var xDis = p2.x - p1.x;
            var yDis = p2.y - p1.y;
            var zDis = p2.z - p1.z;
            var dx = me.x - p1.x;
            var dy = me.y - p1.y;
            var dz = me.z - p1.z;
            var d = xDis * xDis + yDis * yDis + zDis * zDis;
            var t = xDis * dx + yDis * dy + zDis * dz; //向量点积

            if (d > 0)
            {
                t /= d;
            }

            t = Mathf.Clamp(t, 0, 1);

            dx = p1.x + t * xDis - me.x;
            dy = p1.y + t * yDis - me.y;
            dz = p1.z + t * zDis - me.z;

            return Mathf.Sqrt(dx * dx + dy * dy + dz * dz);
        }

        /// <summary>
        /// 计算AB与CD两条线段的交点.
        /// </summary>
        /// <param name="a">A点</param>
        /// <param name="b">B点</param>
        /// <param name="c">C点</param>
        /// <param name="d">D点</param>
        /// <returns>是否相交, 交点</returns>
        /// <remarks>https://blog.csdn.net/sinat_25415095/article/details/114293638</remarks>
        public static (bool, Vector3) TryGetIntersectPoint(Vector3 a, Vector3 b, Vector3 c, Vector3 d)
        {
            var intersectPos = Vector3.zero;

            var ab = b - a;
            var ca = a - c;
            var cd = d - c;

            var v1 = Vector3.Cross(ca, cd);

            if (Mathf.Abs(Vector3.Dot(v1, ab)) > 10e-6)
            {
                // 不共面
                return (false, intersectPos);
            }

            if (Vector3.Cross(ab, cd).sqrMagnitude <= 10e-6)
            {
                // 平行
                return (false, intersectPos);
            }

            var ad = d - a;
            var cb = b - c;
            // 快速排斥
            if (Mathf.Min(a.x, b.x) > Mathf.Max(c.x, d.x) || Mathf.Max(a.x, b.x) < Mathf.Min(c.x, d.x)
               || Mathf.Min(a.y, b.y) > Mathf.Max(c.y, d.y) || Mathf.Max(a.y, b.y) < Mathf.Min(c.y, d.y)
               || Mathf.Min(a.z, b.z) > Mathf.Max(c.z, d.z) || Mathf.Max(a.z, b.z) < Mathf.Min(c.z, d.z)
            )
                return (false, intersectPos);

            // 跨立试验
            if (!(Vector3.Dot(Vector3.Cross(-ca, ab), Vector3.Cross(ab, ad)) > 0)
                || !(Vector3.Dot(Vector3.Cross(ca, cd), Vector3.Cross(cd, cb)) > 0))
            {
                return (false, intersectPos);
            }

            var v2 = Vector3.Cross(cd, ab);
            var ratio = Vector3.Dot(v1, v2) / v2.sqrMagnitude;
            intersectPos = a + ab * ratio;
            return (true, intersectPos);
        }

        /// <summary>
        /// 围绕指定的点进行旋转
        /// </summary>
        /// <param name="me">自身坐标</param>
        /// <param name="center">中心点</param>
        /// <param name="axis">旋转轴</param>
        /// <param name="angle">旋转角度</param>
        public static Vector3 RotateAround(this Vector3 me, Vector3 center, Vector3 axis, float angle)
        {
            return Quaternion.AngleAxis(angle, axis) * (me - center) + center;
        }

        /// <summary>
        /// 直线与圆有没有相交
        /// </summary>
        /// <param name="p1">直线上的点1</param>
        /// <param name="p2">直线上的点2</param>
        /// <param name="c">圆心</param>
        /// <param name="r">半径</param>
        /// <remarks>线段位于圆内也算相交</remarks>
        public static bool LineCircleIntersect2D(Vector2 p1, Vector2 p2, Vector2 c, float r)
        {
            var d1 = c - p1;
            var d2 = p2 - p1;
            var d2Norm = Vector3.Normalize(d2); //d2的单位向量
            var projection = Vector3.Dot(d1, d2Norm) * d2Norm; //d1在d2Norm上的投影向量

            return d1.sqrMagnitude - projection.sqrMagnitude <= r * r;
        }

        /// <summary>
        /// 判断矩形与圆有没有相交
        /// </summary>
        /// <param name="x1">矩形左下角坐标为（x1，y1）</param>
        /// <param name="y1">矩形左下角坐标为（x1，y1）</param>
        /// <param name="x2">矩形右上角坐标为（x2，y2）</param>
        /// <param name="y2">矩形右上角坐标为（x2，y2）</param>
        /// <param name="cx">圆心坐标为（cx，cy）</param>
        /// <param name="cy">圆心坐标为（cx，cy）</param>
        /// <param name="r">半径</param>
        /// <returns></returns>
        /// <remarks>https://blog.csdn.net/qq_41685265/article/details/107674368</remarks>
        public static bool RectCircleIntersect2D(float x1, float y1, float x2, float y2, float cx, float cy, float r)
        {
            //条件1
            float minx, miny;
            //找出x方向与cx最接近的
            minx = Mathf.Min(Mathf.Abs(x1 - cx), Mathf.Abs(x2 - cx));
            //找出y方向与cy最接近的
            miny = Mathf.Min(Mathf.Abs(y1 - cy), Mathf.Abs(y2 - cy));
            if (minx * minx + miny * miny < r * r) return true;

            //条件2
            float x0 = (x1 + x2) / 2;
            float y0 = (y1 + y2) / 2;
            if ((Mathf.Abs(x0 - cx) < Mathf.Abs(x2 - x1) / 2 + r) && Mathf.Abs(cy - y0) < Mathf.Abs(y2 - y1) / 2)
                return true;
            if ((Mathf.Abs(y0 - cy) < Mathf.Abs(y2 - y1) / 2 + r) && Mathf.Abs(cx - x0) < Mathf.Abs(x2 - x1) / 2)
                return true;

            return false;
        }

        /// <summary>
        /// 判断矩形与圆有没有碰到
        /// </summary>
        /// <param name="me">我的position作为矩形左边的中点</param>
        /// <param name="width">矩形宽度</param>
        /// <param name="height">矩形高度</param>
        /// <param name="point">圆心(世界坐标)</param>
        /// <param name="radius">半径</param>
        public static bool LaserToCircleIntersect2D(this Transform me, float width, float height, Vector2 point, float radius)
        {
            // 计算 point 在 me局部坐标系中 的坐标
            Vector2 p = me.InverseTransformPoint(point);

            //Debug.Log($"敌人坐标:{point} 转换后:{p}");

            // 找一对矩形的对角点坐标
            var x1 = 0f;
            var y1 = -height / 2;
            var x2 = width;
            var y2 = height / 2;
            return RectCircleIntersect2D(x1, y1, x2, y2, p.x, p.y, radius);
        }

        /// <summary>
        /// 计算点到圆的距离
        /// </summary>
        /// <param name="me">点</param>
        /// <param name="point">圆心</param>
        /// <param name="radius">半径</param>
        public static DistancePointToSolidCircle CalcDistance2D_PointToSolidCircle(this Vector3 me, Vector2 point, float radius)
        {
            var distance = new DistancePointToSolidCircle
            {
                // 点到圆心的距离
                DistanceToCenter = ((Vector3)point - me).magnitude
            };

            // 点到圆边的距离(<0时取0)
            var d = distance.DistanceToCenter - radius;
            if (d < 0) d = 0;
            distance.DistanceToEdge = d;

            return distance;
        }

        /// <summary>
        /// 计算圆到圆的距离
        /// </summary>
        /// <param name="me">圆心</param>
        /// <param name="radius">半径</param>
        /// <param name="otherCenter">另一个圆心</param>
        /// <param name="otherRadius">另一个半径</param>
        public static float CalcDistance2D_SolidCircleToSolidCircle(this Vector3 me, float radius, Vector3 otherCenter, float otherRadius)
        {
            var distance = Vector3.Distance(me, otherCenter);
            distance -= radius + otherRadius;
            if (distance < 0) distance = 0;
            return distance;
        }
    }
}
