﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace Apq.NotifyChange
{
    public class NotifyChangeList<T> : List<T>
    {
        #region ListChanging
        /// <summary>
        /// 列表即将更改事件
        /// </summary>
        public event Func<NotifyChangeListEventArgs<T>, bool> ListChanging;

        /// <summary>
        /// 通知列表即将更改
        /// </summary>
        protected virtual bool OnListChanging(NotifyChangeListEventArgs<T> e)
        {
            return FireListChanging(e);
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        /// <returns>是否阻止更改</returns>
        public bool FireListChanging(NotifyChangeListEventArgs<T> e)
        {
            if (ListChanging != null)
            {
                ListChanging.Invoke(e);
                return !e.Cancel;
            }
            return false;
        }
        #endregion

        #region ListChanged
        /// <summary>
        /// 列表已更改事件
        /// </summary>
        public event Action<NotifyChangeListEventArgs<T>> ListChanged;

        /// <summary>
        /// 通知列表已更改
        /// </summary>
        protected virtual void OnListChanged(NotifyChangeListEventArgs<T> e)
        {
            FireListChanged(e);
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        public void FireListChanged(NotifyChangeListEventArgs<T> e)
        {
            ListChanged?.Invoke(e);
        }
        #endregion ListChanged

        #region Item索引器
        // Sets or Gets the element at the given index.
        public new T this[int index]
        {
            get
            {
                return base[index];
            }
            set
            {
                var originalValue = this[index];
                bool equals = Utils.Util.IsEquals(originalValue, value);
                if (equals) return;

                NotifyChangeListEventArgs<T> e = new(this)
                {
                    ChangeType = ListChangedType.ItemChanged,
                };

                {
                    e.Deleted.Add(new() { Index = index, Value = originalValue });
                    e.Inserted.Add(new() { Index = index, Value = value });
                }

                if (!OnListChanging(e))
                {
                    base[index] = value;
                    OnListChanged(e);
                }
            }
        }
        #endregion

        #region Clear
        public new void Clear()
        {
            NotifyChangeListEventArgs<T> e = new(this)
            {
                ChangeType = ListChangedType.Reset,
            };

            {
                for (int i = 0; i < Count; i++)
                {
                    e.Deleted.Add(new()
                    {
                        Index = i,
                        Value = this[i]
                    });
                }
            }

            if (!OnListChanging(e))
            {
                base.Clear();
                OnListChanged(e);
            }
        }
        #endregion

        #region Add
        public new void AddRange(IEnumerable<T> collection)
        {
            if (collection.Any())
            {
                NotifyChangeListEventArgs<T> e = new(this)
                {
                    ChangeType = ListChangedType.ItemAdded,
                };

                {
                    foreach (T item in collection)
                    {
                        e.Inserted.Add(new() { Value = item });
                    }
                }

                if (!OnListChanging(e))
                {
                    foreach (ListItemChanged<T> item in e.Inserted)
                    {
                        item.Index = Count;
                        base.Add(item.Value);
                    }
                    OnListChanged(e);
                }
            }
        }

        public new void Add(T item)
        {
            AddRange(new List<T> { item });
        }
        #endregion

        #region Insert
        public new void Insert(int index, T item)
        {
            InsertRange(index, new List<T> { item });
        }

        public new void InsertRange(int index, IEnumerable<T> collection)
        {
            if (collection.Any())
            {
                NotifyChangeListEventArgs<T> e = new(this)
                {
                    ChangeType = ListChangedType.ItemAdded,
                };

                {
                    foreach (T item in collection)
                    {
                        e.Inserted.Add(new() { Value = item });
                    }
                }

                if (!OnListChanging(e))
                {
                    foreach (ListItemChanged<T> item in e.Inserted)
                    {
                        item.Index = index++;
                        base.Insert(item.Index, item.Value);
                    }
                    OnListChanged(e);
                }
            }
        }
        #endregion

        #region Remove
        public new bool Remove(T item)
        {
            int index = IndexOf(item);
            if (index >= 0)
            {
                RemoveAt(index);
                return true;
            }

            return false;
        }

        public new int RemoveAll(Predicate<T> match)
        {
            NotifyChangeListEventArgs<T> e = new(this)
            {
                ChangeType = ListChangedType.ItemDeleted,
            };

            {
                for (var i = 0; i < Count; i++)
                {
                    if (match(this[i]))
                    {
                        e.Deleted.Add(new()
                        {
                            Index = i,
                            Value = this[i]
                        });
                    }

                }
            }

            if (e.Deleted.Count != 0 && !OnListChanging(e))
            {
                for (var i = e.Deleted.Count - 1; i >= 0; i--)
                {
                    base.RemoveAt(e.Deleted[i].Index);
                }
                OnListChanged(e);
            }

            return e.Deleted.Count;
        }

        public new void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        public new void RemoveRange(int index, int count)
        {
            if (count > 0 && Count > index)
            {
                NotifyChangeListEventArgs<T> e = new(this)
                {
                    ChangeType = ListChangedType.ItemDeleted,
                };

                {
                    for (var i = index; i < count && i < Count; i++)
                    {
                        e.Deleted.Add(new()
                        {
                            Index = i,
                            Value = this[i]
                        });
                    }
                }

                if (!OnListChanging(e))
                {
                    for (var i = e.Deleted.Count - 1; i >= 0; i--)
                    {
                        base.RemoveAt(e.Deleted[i].Index);
                    }
                    OnListChanged(e);
                }
            }
        }
        #endregion

        #region Move
        // +++++++ 覆写排序方法
        #endregion
    }
}
