﻿
using System;
using System.Collections.Generic;

namespace PbContract
{
    public static class PbHelper
    {
        /// <summary>
        /// List中是否包含有意义的项(只含一个默认值是没有意义的)
        /// </summary>
        public static bool HasListItem<T>(IList<T> me) where T : IEquatable<T>
        {
            if (me == null) return false;
            if (me.Count == 0) return false;
            if (me.Count == 1 && me[0].Equals(default)) return false;
            return true;
        }
    }
}
