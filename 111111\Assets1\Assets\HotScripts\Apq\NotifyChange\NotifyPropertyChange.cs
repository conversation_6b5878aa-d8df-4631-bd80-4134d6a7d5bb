﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Apq.NotifyChange
{
    /// <summary>
    /// 属性更改通知(可暂停)
    /// </summary>
    /// <remarks></remarks>
    public abstract class NotifyPropertyChange : IDisposable
    {
        /// <summary>
        /// 表示用于阻止任意属性的更改事件的可用名称("All", "Any")
        /// </summary>
        public static readonly string[] EventSuspendAnyProperty = new string[]
        {
            "All",
            "Any"
        };

        /// <summary>
        /// 暂停列表中是否包含指定名称
        /// </summary>
        /// <returns>如果列表中包含了“任意属性”，则一定包含(即返回true)</returns>
        public static bool EventSuspendList_Contains(List<string> lst, string propertyName)
        {
            return lst.Contains(propertyName)
                || lst.Any(i => EventSuspendAnyProperty.Any(n =>
                    n.Equals(i, StringComparison.OrdinalIgnoreCase)));
        }

        public NotifyPropertyChange()
        {
            PropertyChanging += Me_PropertyChanging;
            PropertyChanged += Me_PropertyChanged;
        }

        protected virtual void Me_PropertyChanged(NotifyPropertyChange me, PropertyChangeEventArgs e)
        {
        }

        protected virtual bool Me_PropertyChanging(NotifyPropertyChange me, PropertyChangeEventArgs e)
        {
            return false;
        }

        #region IDisposable
        protected bool disposedValue;
        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    PropertyChanging -= Me_PropertyChanging;
                    PropertyChanged -= Me_PropertyChanged;
                }

                // TODO: 释放未托管的资源(未托管的对象)并重写终结器
                // TODO: 将大型字段设置为 null
                disposedValue = true;
            }
        }

        // // TODO: 仅当“Dispose(bool disposing)”拥有用于释放未托管资源的代码时才替代终结器
        // ~NotifyPropertyChangeBase()
        // {
        //     // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
        //     Dispose(disposing: false);
        // }

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        #endregion

        #region INotifyPropertyChanging
        public List<string> __EventSuspend_PropertyChanging { get; } = new();

        /// <summary>
        /// 暂停或恢复 引发属性即将更改事件
        /// </summary>
        /// <param name="propertyName">属性名("All"/"Any"表示任意属性)</param>
        /// <param name="Resume">是否恢复引发事件</param>
        public void EventSuspend_PropertyChanging(string propertyName, bool Resume = false)
        {
            if (Resume && __EventSuspend_PropertyChanging.Contains(propertyName))
            {
                __EventSuspend_PropertyChanging.Remove(propertyName);
            }
            else if (!Resume && !__EventSuspend_PropertyChanging.Contains(propertyName))
            {
                __EventSuspend_PropertyChanging.Add(propertyName);
            }
        }

        /// <summary>
        /// 属性即将更改事件
        /// </summary>
        public event Func<NotifyPropertyChange, PropertyChangeEventArgs, bool> PropertyChanging;

        /// <summary>
        /// 通知属性即将更改
        /// </summary>
        protected virtual bool OnPropertyChanging(string propertyName, object originalValue, object newValue)
        {
            return FirePropertyChanging(propertyName, originalValue, newValue);
        }

        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        /// <returns>是否阻止更改</returns>
        public virtual bool FirePropertyChanging(string propertyName, object originalValue, object newValue)
        {
            if (PropertyChanging != null
                && !__EventSuspend_PropertyChanging.Contains(propertyName)
                && !__EventSuspend_PropertyChanging.Any(i => EventSuspendAnyProperty.Any(n => n.Equals(i, StringComparison.OrdinalIgnoreCase)))
            )
            {
                var e = new PropertyChangeEventArgs(propertyName)
                {
                    OriginalValue = originalValue,
                    NewValue = newValue,
                };
                PropertyChanging.Invoke(this, e);
                return e.Cancel;
            }
            return false;
        }
        #endregion

        #region INotifyPropertyChanged
        public List<string> __EventSuspend_PropertyChanged = new();

        /// <summary>
        /// 暂停或恢复 引发属性已更改事件
        /// </summary>
        /// <param name="propertyName">属性名("All"/"Any"表示任意属性)</param>
        /// <param name="Resume">是否恢复引发事件</param>
        public void EventSuspend_PropertyChanged(string propertyName, bool Resume = false)
        {
            if (Resume && __EventSuspend_PropertyChanged.Contains(propertyName))
            {
                __EventSuspend_PropertyChanged.Remove(propertyName);
            }
            else if (!Resume && !__EventSuspend_PropertyChanged.Contains(propertyName))
            {
                __EventSuspend_PropertyChanged.Add(propertyName);
            }
        }

        /// <summary>
        /// 属性已更改事件
        /// </summary>
        public event Action<NotifyPropertyChange, PropertyChangeEventArgs> PropertyChanged;

        /// <summary>
        /// 通知属性已更改
        /// </summary>
        protected virtual void OnPropertyChanged(string propertyName, object originalValue, object newValue)
        {
            FirePropertyChanged(propertyName, originalValue, newValue);
        }

        /// <summary>
        /// 仅触发事件(暂停则不触发)
        /// </summary>
        public virtual void FirePropertyChanged(string propertyName, object originalValue, object newValue)
        {
            if (PropertyChanged != null
                && !__EventSuspend_PropertyChanged.Contains(propertyName)
                && !__EventSuspend_PropertyChanging.Any(i => EventSuspendAnyProperty.Any(n => n.Equals(i, StringComparison.OrdinalIgnoreCase)))
            )
            {
                var e = new PropertyChangeEventArgs(propertyName)
                {
                    OriginalValue = originalValue,
                    NewValue = newValue,
                };
                PropertyChanged.Invoke(this, e);
            }
        }
        #endregion
    }
}
