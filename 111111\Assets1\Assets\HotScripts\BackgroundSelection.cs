﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class BackgroundSelection : MonoBehaviour
{
    public void OpenBackground(int index)
    {
        BackgroundController.backgroundType = (BackgroundController.BackgroundType)index;
    }

    public void OpenGUN(int index)
    {
        //if(index == 0)
        //{
        //    Weapon.gun = Weapon.GunType.FrontMachineGun;
        //    SecondaryWeapon.gun = SecondaryWeapon.GunType.RearBackFire;
        //}
        //else if (index == 1)
        //{
        //    Weapon.gun = Weapon.GunType.FrontMultiCanon;
        //    SecondaryWeapon.gun = SecondaryWeapon.GunType.RearBackFire;
        //}
        //else if (index == 2)
        //{
        //    Weapon.gun = Weapon.GunType.FrontPlasma;
        //    SecondaryWeapon.gun = SecondaryWeapon.GunType.FlameThrower;
        //}
        //else if (index == 3)
        //{
        //    Weapon.gun = Weapon.GunType.FrontRocket;
        //    SecondaryWeapon.gun = SecondaryWeapon.GunType.FlameThrower;
        //}
        //else if (index == 4)
        //{
        //    Weapon.gun = Weapon.GunType.FrontLaser;
        //    SecondaryWeapon.gun = SecondaryWeapon.GunType.FlameThrower;
        //}
    }

}
