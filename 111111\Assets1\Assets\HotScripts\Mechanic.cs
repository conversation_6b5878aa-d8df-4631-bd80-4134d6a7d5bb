﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using <PERSON><PERSON>;

public class Mechanic : Sidekick
{
    Spine.AnimationState animationState;
    [SerializeField] private ParticleSystem mechParticle;

    void Start()
    {
        Init();
        sidekickSkeleton.state.Data.SetMix("select", "idle", 0.4f);
        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);

        transform.localScale = new Vector3(scale, scale, 1);

        //sidekickSkeleton.state.Data.SetMix("idle", "mech-healing", 0.1f);
        //sidekickSkeleton.state.Data.SetMix("mech-healing", "idle", 0.1f);

        SetStartingPosition();
    }

    public override void StartSidekick()
    {
        sidekickSkeleton.state.SetAnimation(0, "idle", true);
        InvokeRepeating("CheckHeal", 0, 3);//healtime
    }

    private void Update()
    {
        SidekicksUpdate();
    }

    void GeneratePlayerParticles()
    {
        {
            //Sprite* trail = Sprite::createWithSpriteFrameName("smokeParticlePlayer3.png");
            //trail->setColor(Color3B::GREEN);
            //Player::getInstance()->addChild(trail, -2);
            //trail->setPosition(-300 + CCRANDOM_0_1() * 600, -150 + CCRANDOM_0_1() * 300);
            //trail->setScale(5 + CCRANDOM_0_1() * 0.4f);
            //trail->setCameraMask(GAMECAMERA);

            //trail->runAction(MoveTo::create(0.3f + CCRANDOM_0_1() * 0.1f, cocos2d::Point(0, trail->getPosition().x + 400 + CCRANDOM_0_1() * 200)));
            //trail->runAction(Sequence::create(ScaleTo::create(0.3 + CCRANDOM_0_1() * 0.1f, 0), RemoveSelf::create(), NULL));
            //if (rand_0_1() < 0.5f)
            //{
            //    trail->setLocalZOrder(2);
            //}
        }

        {
            //Sprite* trail = Sprite::createWithSpriteFrameName("lineParticle.png");
            //Player::getInstance()->addChild(trail, -2);
            //trail->setPosition(-300 + CCRANDOM_0_1() * 600, -150 + CCRANDOM_0_1() * 300);
            //trail->setScale(2 + CCRANDOM_0_1() * 0.4f);
            //trail->setCameraMask(GAMECAMERA);

            //trail->runAction(MoveTo::create(0.3f + CCRANDOM_0_1() * 0.1f, cocos2d::Point(0, trail->getPosition().x + 600 + CCRANDOM_0_1() * 200)));
            //trail->runAction(Sequence::create(ScaleTo::create(0.3 + CCRANDOM_0_1() * 0.1f, 0), RemoveSelf::create(), NULL));
            //if (rand_0_1() < 0.5f)
            //{
            //    trail->setLocalZOrder(2);
            //}
        }


        {
            //Sprite* trail = Sprite::createWithSpriteFrameName("smokeParticlePlayer3.png");
            //sideKickSprite->addChild(trail, -2);
            ////    trail->setPosition( CCRANDOM_0_1()*10 + ( 200*sinf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()+90 ))),  CCRANDOM_0_1()*10  + ( 200*cosf(CC_DEGREES_TO_RADIANS(Player::getInstance()->getRotation()+90 ))));
            //trail->setPosition(-150 + CCRANDOM_0_1() * 300, -150 + CCRANDOM_0_1() * 300);
            //trail->setScale(3 + CCRANDOM_0_1() * 0.4f);
            //trail->setCameraMask(GAMECAMERA);
            //trail->setRotation(CCRANDOM_0_1() * 360);

            //trail->runAction(MoveTo::create(0.3f + CCRANDOM_0_1() * 0.1f, cocos2d::Point(0, trail->getPosition().x + 400 + CCRANDOM_0_1() * 200)));
            //trail->runAction(Sequence::create(ScaleTo::create(0.3 + CCRANDOM_0_1() * 0.1f, 0), RemoveSelf::create(), NULL));

        }
    }
    void CheckHeal()
    {
        if (Globals.resetControls)
            return;

        Attributes playerStats = player.Stats;
        if (playerStats.health < playerStats.maxHealth.Value && player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            sidekickSkeleton.state.SetAnimation(0, "shoot", false);
            sidekickSkeleton.state.AddAnimation(0, "idle", true, 0);


            int value = (int)(damage);
            player.Stats.health += value;

            player.DisplayHitText(value);


            //this->schedule(schedule_selector(Mechanic::generatePlayerParticles), 0.05, 20, 0);//healtime

            player.PlayMechParticle();
            mechParticle.Play();

            if (player.Stats.health > player.Stats.maxHealth.Value)
            {
                player.Stats.health = player.Stats.maxHealth.Value;
            }
        }
    }
}
