using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
public class Balloon : Enemy
{
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private GameObject bomb;
    private Tesla tesla;
    private PlayerPing missionPing;
    private bool startIdle;
    private float balloonOffset;

    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        return false;
    }

    public void AddTesla(Tesla t)
    {
        tesla = t;
        DOTween.Sequence().SetId(tweenId).Append(transform.DOMove(new Vector2(tesla.transform.position.x - Globals.CocosToUnity(300), transform.position.y), 180)).AppendCallback(Attack).Play();

    }

    public override void Destroy()
    {
        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeSaveBuildingFromBalloon && Globals.gameType == GameType.Training)
        {
            GameManager.instance.missionManager.MissionComplete();
        }
        base.Destroy();
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        tweenId = "Balloon" + GetInstanceID();
        schedulerId = "BalloonS" + GetInstanceID();
        allowRelocate = false;

        balloonOffset = Globals.CocosToUnity(-500) + Random.value * Globals.CocosToUnity(250);

        enemyCollisionRadius = Globals.CocosToUnity(800 * 800);

        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;

        transform.SetWorldPositionY(Globals.CocosToUnity(900));
        enemySprite.state.SetAnimation(0, "idle", true);
        transform.SetWorldPositionX(Globals.CocosToUnity(-10000));

        DOTween.Sequence().SetId(schedulerId).AppendInterval(1f).AppendCallback(() =>
        {
            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);


            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);
        }).Play();

        Tag = 2;
        scheduleUpdate = true;
        startMove = false;
        startIdle = false;
        
    }

    private void Update()
    {
        if (startMove)
        {
            if (Mathf.Abs(player.transform.position.x - transform.position.x) <= Mathf.Abs(balloonOffset))
            {
                startMove = false;
                startIdle = true;
            }
            //if (player.transform.position.x > transform.position.x)
            //{
            //    transform.SetWorldPositionX(transform.position.x + stats.speed * Time.deltaTime);
            //}

            //if (player.transform.position.x  < transform.position.x)
            //{
            //    transform.SetWorldPositionX(transform.position.x - stats.speed * Time.deltaTime);
            //}
        }
        if(startIdle)
        {
            timeOfStopMode -= Time.deltaTime;
            if (timeOfStopMode <= 0)
            {
                startIdle = false;
                DOTween.Sequence().SetId(tweenId).Append(transform.DOMove(new Vector2(transform.position.x + (isBorthRight ? (-Globals.CocosToUnity(20000)) : (+Globals.CocosToUnity(20000))) , transform.position.y), 4)).AppendCallback(Destroy).Play();
            }
        }
        CheckUpdate();
    }
    private void Attack()
    {
        enemySprite.state.SetAnimation(0, "shoot", false);
        enemySprite.state.AddAnimation(0, "idle", true);

        bomb.transform.parent = null;
        bomb.transform.position = new Vector2(enemySprite.transform.position.x + Globals.CocosToUnity(70), enemySprite.transform.position.y + Globals.CocosToUnity(100));

        GameSharedData.Instance.enemyList.Remove(this);
        if (tesla)
        {
            DOTween.Sequence().SetId("bomb"+bomb.GetInstanceID()).AppendInterval(0.75f)
                .AppendCallback(()=>{ bomb.SetActive(true); })
                .Append(bomb.transform.DOMove(tesla.transform.position,1.25f).SetEase(Ease.Linear,1.5f,1f))
                .AppendCallback(()=> { bomb.SetActive(false); })
                .Play();
            DOTween.Sequence().SetId(tesla.schedulerId).AppendInterval(2)
                .AppendCallback(CreateExplosions)
                .AppendInterval(0.25f)
                .AppendCallback(() => { tesla.gameObject.SetActive(false); })
                .Play();
        }
        DOTween.Sequence().SetId(tweenId).AppendInterval(4).Append(transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(4000), 0), 5)).AppendCallback(Destroy).Play();
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = 7 + Random.value;//bhut taiz
        stats.turnSpeed = 0.5f + Random.value;
        stats.health = -1500 + GameData.instance.fileHandler.currentMission * 900;
        stats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        stats.bulletSpeed = 7;
        stats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = stats.health;

        stats.coinAwarded = 15;


        baseStats.health = -1500 + GameData.instance.fileHandler.currentMission * 900;
        baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        baseStats.bulletSpeed = 7;
        baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        baseStats.maxHealth.Value = baseStats.health;
        baseStats.coinAwarded = 15;

    }

    private void CreateExplosions()
    {
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");

        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBuildingMission, tesla.transform.position, false, 2, 1.0f, 0);
       GameManager.instance.missionManager.MissionFailed();

    }


}
