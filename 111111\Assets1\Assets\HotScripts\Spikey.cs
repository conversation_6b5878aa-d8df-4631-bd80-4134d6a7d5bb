using UnityEngine;
using System.Collections;
using Spine.Unity;
using Spine;
using DG.Tweening;

public class Spikey : Enemy
{

    [SerializeField] private EnemyFall enemyFallPrefab;
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] private Sprite chargeShotSprite;
    [SerializeField] private SkeletonAnimation flame;
    [SerializeField] private SkeletonAnimation turret;
    [SerializeField] private GameObject chargeAnimationObject;
    [SerializeField] private GameObject bulletFlash;


    private Bone leftTurretBone;
    private Bone rightTurretBone;


    private int enemyLevel;//all

    private bool allowShooting;//all
    private bool allowBoost;//2+

    private float scaleFactor;//all
    private Vector2 chaseOffset;
    private float chaseDistance;
    private bool allowRotation = true;
    private float distanceToShoot;
    //	 bool init();

    private WaveStructure _info;

    private bool _createdAsType = false;
    private Sprite _ping = null;


    private bool _charge = false;
    private bool _isIntroComplete = false;


    private Bullet bullet;

    private float tSpeed;
    private float rSpeed;
    private float totalDistanceSquared;
    private Vector2 PlayerPositionToFollow;
    private float playerRotation;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowFollow = true;
        SetVariable();
        SetAnimationVariables();
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spikyIntro);
        //print(PlayerPrefs.GetInt("DashTutorial"));
        if (PlayerPrefs.GetInt("DashTutorial") == 1)
        {
            GameData.instance.fileHandler.dashTutorialTime = 0;
        }
        else
        {
            GameData.instance.fileHandler.dashTutorialTime = 5;
        }


        //maskInteraction(GAMECAMERA);
        if (Random.value < 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(800), Random.value * Globals.CocosToUnity(600 + 200));
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(800), Random.value * Globals.CocosToUnity(600 + 200));

        }
        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
        //rigid = gameObject.GetComponentInChildren<Rigidbody2D>();
    }

    private void SetVariable()
    {
        distanceToShoot = Globals.CocosToUnity(5000)*2;
        enemySchedulerSpeed = 0.075f;
        scaleFactor = 0.35f;
        allowRelocate = false;
        allowPushBack = true;
        allowStack = false;
        allowDeathParticles = true;
        float offset = Globals.CocosToUnity(100);
        chaseOffset = new Vector2((-offset / 2) + (Random.value * offset), (-offset / 2) + (Random.value * offset));
        chaseDistance = Globals.CocosToUnity(30000) + Random.value * Globals.CocosToUnity(70000);
        InitStats();
    }

    private void SetAnimationVariables()
    {
        enemySprite.transform.localScale = new Vector2(scaleFactor, scaleFactor);
        transform.position = new Vector2(player.transform.position.x - 12, Random.value* 0.8f +0.2f);
        enemySprite.state.SetAnimation(0, "idleBoss", true);
        enemySprite.state.Event += HandleGunShoot;
        turret.state.Event += HandleBossChargeShot;
        InvokeRepeating(nameof(ChargeShot), 5f, 5f);
        leftTurretBone = enemySprite.skeleton.FindBone("burstleft");
        rightTurretBone = enemySprite.skeleton.FindBone("burstright");

    }

    private void Update()
    {

        SetUpdateVariables();


        //enemySprite.transform.eulerAngles = new Vector3(0,0,(Globals.Modulus(enemySprite.transform.eulerAngles.z + 360f, 360f)));


        //if (Player::getStats().mode != Player::PLAYER_MODE_FLYING)//TODO
        //{
        //    allowFollow = false;
        //}


        HandleRotation();
        HandleShooting();
        HandleMovement();
        SetEnemyImage();

        if (healthBar)
        {
            healthBar.transform.position = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y - Globals.CocosToUnity(75));
        }


    }
    private void SetUpdateVariables()
    {

        totalDistanceSquared = Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position);//  transform.position.distanceSquared(Player::getInstance().getPosition());
        PlayerPositionToFollow = player.transform.position;
        Globals.bossPosition = transform.position;
        //if (Vector2.SqrMagnitude(transform.position - player.transform.position) > Globals.CocosToUnity(13000))
        //{
        //    stats.speed += 0.3f;
        //    stats.speed = Mathf.Clamp(stats.speed, 4, 20);
        //    flame.gameObject.SetActive(true);
        //}
        //else if (Vector2.SqrMagnitude(transform.position - player.transform.position) < Globals.CocosToUnity(2500))
        //{
        //    stats.speed -= 0.3f;
        //    stats.speed = Mathf.Clamp(stats.speed, 1, 20);
        //    flame.gameObject.SetActive(false);

        //}
        //else
        //{
        //    if (stats.speed < 4)
        //    {
        //        stats.speed += 0.5f;

        //    }
        //    else if (stats.speed > 6)
        //    {
        //        stats.speed -= 0.5f;

        //    }

        //    stats.speed = Mathf.Clamp(stats.speed, 3, 7);
        //    flame.gameObject.SetActive(false);

        //}
        //Debug.Log("stats.speed=" + stats.speed);
        //Vector2 direct = player.transform.position - transform.position;
        //rigid.velocity = stats.speed * direct.normalized;
        tSpeed = 1;
        rSpeed = 1;
        if (enemySprite.transform.position.y < Globals.LOWERBOUNDARY)
        {
            //transform.position = new Vector2(transform.position.x, transform.position.y);
            tSpeed = 0.25f;

        }
        if (enemySprite.transform.position.y > Globals.UPPERBOUNDARY)
        {
            tSpeed = 0.25f;
            //transform.position = new Vector2(transform.position.x, Globals.UPPERBOUNDARY);
        }

    }
    
    private void HandleRotation()
    {
        Vector2 posDiff1 = player.transform.position - transform.position;
        playerRotation = Vector2.Angle(posDiff1, transform.right);
        if (playerRotation == 0)
        {
            playerRotation = 1;
        }

        if (player.transform.position.y < transform.position.y)
        {
            playerRotation *= -1;
            playerRotation += 360;
        }

        if (_info)
        {
            playerRotation = Globals.CalcAngle(enemySprite.transform.position,
                new Vector2(PlayerPositionToFollow.x + _info.sInfo._position.x, PlayerPositionToFollow.y + _info.sInfo._position.y)) - 180;
        }
        if (playerRotation - enemySprite.transform.eulerAngles.z > 200)
        {
            int i = (int)(playerRotation - enemySprite.transform.eulerAngles.z) / 360;
            playerRotation = playerRotation - (360 * (i + 1));


        }
        if (playerRotation - enemySprite.transform.eulerAngles.z < -200)
        {
            int i = (int)(playerRotation - enemySprite.transform.eulerAngles.z) / 360;

            playerRotation = playerRotation + (360 * (i + 1));

        }
        if (allowFollow)
        {

            if (allowRotation)
            {
                float angleDifference = -enemySprite.transform.eulerAngles.z + playerRotation;
                if (Mathf.Abs(angleDifference) < 60 * Time.deltaTime) // finished rotation
                {
                    enemySprite.transform.eulerAngles = new Vector3(0, 0, playerRotation);
                }
                else if (angleDifference > 0)
                {
                    enemySprite.transform.rotation = Quaternion.Euler(0, 0, enemySprite.transform.eulerAngles.z + 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
                else
                {
                    enemySprite.transform.rotation = Quaternion.Euler(0, 0, enemySprite.transform.eulerAngles.z - 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
            }

        }
    }

    private void HandleShooting()
    {
        curCheckStateTime += Time.deltaTime;
        if (curCheckStateTime > checkStateTime)
        {
            curCheckStateTime = 0;
            posDiff2 = player.transform.position - enemySprite.transform.position;
            shootRandom = Random.Range(0, 10000);
            if (shootRandom > skillShootProbability)
            {
                enemySprite.state.SetAnimation(0, "idle", true);
                allowShooting = false;
                return;
            }

            if (Vector2.Angle(posDiff2, enemySprite.transform.right) < angleToShoot && totalDistanceSquared < distanceToShoot && !_charge)
            {

                if (!allowShooting)
                {
                    allowShooting = true;
                    enemySprite.state.SetAnimation(0, "shootEnemyPlane6", true);
                }

            }
            else
            {

                if (allowShooting)
                {
                    //enemySprite.state.SetAnimation(0, "idleBoss", true);
                }
                allowShooting = false;
            }
        }
    }
    
    private void HandleMovement()
    {
        //transform.position = new Vector2(transform.position.x + stats.speed * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)) * Time.deltaTime * Globals.CocosToUnity(60f), transform.position.y + stats.speed * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)) * Time.deltaTime * Globals.CocosToUnity(60f) * tSpeed);
        //if (allowBoost && Vector2.SqrMagnitude(enemySprite.transform.position - player.transform.position) > Globals.CocosToUnity(625))
        //{
        //    transform.position = new Vector2(transform.position.x + stats.speed / 2 * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z + 90)), transform.position.y + stats.speed / 2 * Mathf.Cos(Mathf.Deg2Rad * (transform.eulerAngles.z - 90)));
        //}

        CheckUpdate();
    }

    private void HandleGunShoot(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shootright")
        {
            if (enemySprite.transform.localScale.y > 0)
            {
                shootLeft();
            }
            else
            {
                shootRight();
            }
        }
        else if (spineEvent.Data.Name == "shootleft")
        {
            if (enemySprite.transform.localScale.y > 0)
            {
                shootRight();
            }
            else
            {
                shootLeft();
            }
        }
    }

    private void HandleBossChargeShot(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            if (Globals.gameType != GameType.Survival)
            {
                if (GameData.instance.fileHandler.dashTutorialTime < 3)
                {
                    GameData.instance.fileHandler.dashTutorialTime++;
                    Observer.DispatchCustomEvent("DashTutorial");
                    PlayerPrefs.SetInt("DashTutorial", 0);
                }
                else
                {
                    InputController.instance.SetDash(true);
                }
            }

            Bullet bullet = null;

            for (int i = 0; i < 3; i++)
            {
                bool didFindBullet = false;
                foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        bullet.isInUse = true;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                bullet.SetSpriteFrame(chargeShotSprite);
                //bullet.bulletType = EnemyBulletFireball;
                //                            SkeletonAnimation *bulletOverlay = SkeletonAnimation::createWithJsonFile("res/Missions/turretBullet.json", "res/Missions/turretBullet.atlas");
                //                            bullet.addChild(bulletOverlay);
                //                            bulletOverlay.setCameraMask(GAMECAMERA);
                //                            bulletOverlay.setScale(3);

                //transform.localPosition = new Vector3(transform.position.x - Globals.CocosToUnity(5) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)), transform.position.y - Globals.CocosToUnity(5) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z - 90)));

                float angle = enemySprite.transform.eulerAngles.z - 90 - 10 + (i * 10);

                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.SpiderCatFall, 0.5f);

                //Globals.PlaySound("res/Sounds/Bosses/Boss6/SpiderCatFall.mp3", false, 0.5f);
                bullet.transform.SetPositionAndRotation(chargeAnimationObject.transform.position, Quaternion.Euler(0, 0, angle));
                Vector2 dest = new Vector2(Globals.CocosToUnity(2600) * Mathf.Sin(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z)) * -1, Globals.CocosToUnity(2600) * Mathf.Cos(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z)));

                bullet.gameObject.SetActive(true);
                bullet.PlayBulletAnim(1, dest);
                GameSharedData.Instance.enemyBulletInUse.Add(bullet);
                bullet.setRadiusEffectSquared(Globals.CocosToUnity(120));


            }
            bulletFlash.SetActive(true);
            Invoke(nameof(DisableChargeShot), 1f);
        }

    }

    private void DisableChargeShot()
    {
        _charge = false;
    }

    private void initWithInfo(WaveStructure info)
    {

    }


    private void ChargeShot()
    {
        if (stats.health < stats.maxHealth.Value * 0.25f)
        {
            //Director::getInstance().getEventDispatcher().dispatchCustomEvent("ChangeBossState");
        }
        _charge = true;
        enemySprite.state.SetAnimation(0, "Charge", false);
        enemySprite.state.AddAnimation(0, "idleBoss", true, 0);
        chargeAnimationObject.SetActive(true);
        turret.state.SetAnimation(0, "shoot2", false);
    }


    public override void SetEnemySpecialAttributes()
    {

    }



    public void shootLeft()
    {
        if (!allowShooting)
        {
            return;
        }

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        
        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);
        bullet.transform.position = leftTurretBone.GetWorldPosition(enemySprite.transform);
        bullet.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z - 90);
        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))) , Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));

        bullet.gameObject.SetActive(true);

       AudioManager.instance.PlaySound(AudioType.Enemy,Constants_Audio.Audio.spikyShoot, 0.7f);
        bullet.PlayBulletAnim(stats.bulletSpeed / 2, dest, false);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all


    public void shootRight()
    {
        if (!allowShooting)
        {
            return;
        }

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);


        bullet.transform.position = rightTurretBone.GetWorldPosition(enemySprite.transform);
        bullet.transform.eulerAngles = new Vector3(0, 0, enemySprite.transform.eulerAngles.z - 90);

        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemySprite.transform.eulerAngles.z)));

        bullet.gameObject.SetActive(true);
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spikyShoot, 0.7f);
        bullet.PlayBulletAnim(stats.bulletSpeed/2, dest, false);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all

    private void SetEnemyImage()
    {
        float rotation;
        rotation = enemySprite.transform.eulerAngles.z;


        if (rotation < 90 || rotation > 90 + 180)
        {
            enemySprite.transform.localScale = new Vector2(scaleFactor, scaleFactor);

        }
        else
        {
            enemySprite.transform.localScale = new Vector2(scaleFactor, -scaleFactor);

        }
    }//all
    
    private void InitStats()
    {
        isBoss = true;
        enemyCollisionRadius = 1.7f;
        stats = new Attributes();
        baseStats = new Attributes();

        int bossNumber = 1;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            bossNumber = GameData.instance.fileHandler.currentEvent;
        }
        if(Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }

       
        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = System.Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = System.Convert.ToSingle((bossStats["xp"] as PList)["value"]); ;
        stats.coinAwarded = baseStats.coinAwarded = (int)System.Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.bulletSpeed = baseStats.bulletSpeed = 5;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }

        //stats.speed = baseStats.speed = 10f;
        //stats.turnSpeed = baseStats.turnSpeed = 1.65f + Random.value * 0.5f;
        //stats.health = baseStats.health =  100;// = (::getStats().maxHealth + 20 + difficulty) / 1.5f;
        ////    stats.health = baseStats.health = 600;
        //stats.maxHealth = baseStats.maxHealth = stats.health * 3.5f;
        //stats.bulletDamage = baseStats.bulletDamage = (2.5f + Globals.difficulty + (5.0f * /*FileHandler::getInstance().currentMission*/ 1 / 5));
        //stats.bulletSpeed = baseStats.bulletSpeed = 5;
        //stats.coinAwarded = baseStats.coinAwarded = 4;
        //stats.xp = baseStats.xp = stats.health;

        //stats.speed = 4;
        //stats.health = baseStats.health = 850f;
        //stats.maxHealth = baseStats.maxHealth = stats.health;

        //stats.turnSpeed = 2;
        //stats.bulletDamage = 6;
        //stats.regen = 0.5f;
        //stats.xp = 250;
        //stats.coinAwarded = 25;

        isBoss = true;
    }
    
    public override void Destroy()
    {
        if(gameObject != null && gameObject.activeInHierarchy)
        {
            EnemyFall enemyFall = null;
            if (Random.value < 0.2f && allowDeathParticles) //&& Globals.gameType == GameType.Training)
            {
                // enemyFall = Instantiate(enemyFallPrefab);
                // enemyFall.InitFallEnemy(this, 3);
                for (int i = 0; i < 3; i++)
                {
                    GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                    Gibs debris = go.GetComponent<Gibs>();
                    debris.isInUse = true;
                    debris.CreateWithData(400, 5, true, true);
                    debris.transform.position = transform.position;
                }
            }
            //yield return new WaitForEndOfFrame();
            base.Destroy();
            Globals.ResetZoomValue();
        }
        Globals.ResetBoundary();
            //StartCoroutine(DestroyCoroutine());
        //if (allowDeathParticles) //&& FileHandler::getInstance().currentMission == 2 && gameType == GameType::Arena)TODO
        //{
        //    dynamicFallEnemy(enemy);
        //}
  
        //yield return new WaitForEndOfFrame();
    }
	
    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        EnemyFall enemyFall = null;
        if (Random.value < 0.2f && allowDeathParticles) //&& Globals.gameType == GameType.Training)
        {
            // enemyFall = Instantiate(enemyFallPrefab);
            // enemyFall.InitFallEnemy(this, 3);
            for (int i = 0; i < 3; i++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;    
                debris.CreateWithData(400, 5, true, true);
                debris.transform.position = transform.position;
            }
        }
        //yield return new WaitForEndOfFrame();
        base.Destroy();
        Globals.ResetZoomValue();
    }

}
