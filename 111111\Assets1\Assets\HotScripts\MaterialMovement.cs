﻿using UnityEngine;

public class MaterialMovement : MonoBehaviour
{
    [SerializeField] Material material;
    public float speed;
    public bool updateMovement = false;
    public float offsetX;

    // Start is called before the first frame update
    void Start()
    {
        offsetX = 0;
    }

    public void ResetOffset()
    {
        material.mainTextureOffset = new Vector2(0, 0);
    }

    public void OffsetUpdate()
    {
        offsetX += speed * Time.deltaTime;
        material.mainTextureOffset = new Vector2(offsetX, 0);
    }

    private void Update()
    {
        if (updateMovement) OffsetUpdate();
    }
}
