using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using TMPro;
public class MainSettingsMenu : MonoBehaviour
{

    private int currentSelectedMenu = 0;
    [SerializeField] private SettingsAccessiblityMenu ss;
    [SerializeField] private SettingsGeneralMenu lb;

    [SerializeField] private Transform front;
    [SerializeField] private Transform back;
    [SerializeField] private Color active;
    [SerializeField] private Color deactive;
    [SerializeField] private Image[] generalButtonImage;
    [SerializeField] private TextMeshProUGUI generalButtonlabel;
    [SerializeField] private GameObject generalSettingsButton;
    [SerializeField] private Image[] accessibilitButtonImage;
    [SerializeField] private TextMeshProUGUI accessibilityButtonlabel;
    [SerializeField] private GameObject accessibilitySettingButton;
    [SerializeField] private GameObject blur;


    public void Init()
    {
        transform.localScale = Vector3.zero;
        gameObject.SetActive(true);
        transform.DOScale(Vector3.one, 0.2f).OnComplete(()=> { blur.gameObject.SetActive(true);
            //Time.timeScale = 0;
            //LuaToCshapeManager.Instance.PauseOrResumeBattle(0);
        });
        //        Shared::rescale(this, 1);



        //        {
        //            lb = SettingsGeneralMenu::create();
        //            this.addChild(lb, 5);
        //            lb.setScale(0);
        //            lb.setEnabledNode(false);
        //            lb.runAction(ScaleTo::create(0.2f, 1));

        //            ss = SettingsAccessiblityMenu::create();
        //            this.addChild(ss, 5);
        //            ss.setScale(0);
        //            ss.setEnabledNode(true);
        //            if (PlayerPrefs.GetInt("firstTimeOnUpdate", 1) == 1)
        //            {
        //                ss.setEnabledNode(false);
        //                lb.setEnabledNode(true);


        //            }
        //            ss.runAction(ScaleTo::create(0.2f, 1));


        //            lb._mainButton.addTouchEventListener([=](Ref * sender, ui::Widget::TouchEventType type){
        //                if (!lb.getEnabledNode())
        //                {
        //                    lb.setEnabledNode(true);
        //                    ss.setEnabledNode(false);
        //                    Shared::playSound(SOUND_BUTTON_TAP);

        //                }
        //            });
        //            ss._mainButton.addTouchEventListener([=](Ref * sender, ui::Widget::TouchEventType type){
        //                if (!ss.getEnabledNode())
        //                {
        //                    ss.setEnabledNode(true);
        //                    lb.setEnabledNode(false);
        //                    Shared::playSound(SOUND_BUTTON_TAP);

        //                }
        //            });
        //            //
        //        }
        //        currentSelectedMenu = 0;
        //        if (UserDefault::getInstance().getBoolForKey("firstTimeOnUpdate", true))
        //        {
        //            currentSelectedMenu = 1;

        //        }
        //        settingsGeneralMenuEnabled = true;
        //        {
        //            auto keyListener = EventListenerKeyboard::create();
        //            keyListener.onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event *event)
        //        {

        //        if (keyCode == EventKeyboard::KeyCode::KEY_ESCAPE)
        //        {

        //        }

        //        if (keyCode == EventKeyboard::KeyCode::KEY_UP_ARROW)
        //        {



        //        }

        //        else if (keyCode == EventKeyboard::KeyCode::KEY_DOWN_ARROW)
        //        {


        //        }

        //        else if (keyCode == EventKeyboard::KeyCode::KEY_RIGHT_ARROW)
        //        {

        //            if (currentSelectedMenu == 0)
        //            {
        //                currentSelectedMenu++;
        //                EnableGeneralSettingsMenu();
        //                settingsGeneralMenuEnabled = false;
        //                settingsAccessibilityMenuEnabled = true;

        //            }


        //        }

        //        else if (keyCode == EventKeyboard::KeyCode::KEY_LEFT_ARROW)
        //        {

        //            if (currentSelectedMenu == 1)
        //            {
        //                currentSelectedMenu = 0;
        //                EnableAccessibilitySettingsMenu();
        //                settingsAccessibilityMenuEnabled = false;
        //                settingsGeneralMenuEnabled = true;

        //            }

        //        }

        //        else if (keyCode == EventKeyboard::KeyCode::KEY_ENTER)
        //        {


        //        }


        //    };
        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);
        //    GamePad_Apple *ga  = GamePad_Apple::create();
        //        this.addChild(ga);
        //}
        //return true;
    }

    private void Update()
    {

    }

    public void EnableGeneralSettingsMenu()
    {
        if (!lb.gameObject.activeInHierarchy)
        {
            lb.gameObject.SetActive(true);
            ss.gameObject.SetActive(false);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
            //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
            generalSettingsButton.transform.SetParent(front);
            foreach (Image i in generalButtonImage)
            {
                i.color = Color.white;
            }
            generalButtonlabel.color = active;
            accessibilitySettingButton.transform.SetParent(back);
            foreach (Image i in accessibilitButtonImage)
            {
                i.color = deactive;
            }
            accessibilityButtonlabel.color = deactive;
        }

    }

    public void EnableAccessibilitySettingsMenu()
    {
        if (!ss.gameObject.activeInHierarchy)
        {
            ss.gameObject.SetActive(true);
            lb.gameObject.SetActive(false);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
            //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

            generalSettingsButton.transform.SetParent(back);
            foreach (Image i in generalButtonImage)
            {
                i.color = deactive;
            }
            generalButtonlabel.color = deactive;
            accessibilitySettingButton.transform.SetParent(front);
            foreach (Image i in accessibilitButtonImage)
            {
                i.color = Color.white;
            }
            accessibilityButtonlabel.color = active;
        }
    }

    public void ClosePopUp()
    {
        Globals.isMouseOverUI = false;
        gameObject.SetActive(false);
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
    }


}
