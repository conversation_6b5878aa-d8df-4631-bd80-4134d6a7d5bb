using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine;
using Spine.Unity;
using System;

public class OktoPuss : Enemy
{
    [SerializeField] private Sprite bulletSprite;
    [SerializeField] private Sprite bulletSpritePhase3;
    [SerializeField] private Sprite mineSprite;
    [SerializeField] private Sprite missileSprite;
    [SerializeField] private Sprite rocketSprite;
    [SerializeField] private SpriteRenderer shadow = null;
    [SerializeField] private BubbleCreator[] bubbleCreator;
    [SerializeField] private Transform followTransform;
    [SerializeField] private Transform bubbleContainer;
    [SerializeField] private JellyFish jellyFish;
    [SerializeField] private Transform bladeContainer;
    [SerializeField] private List<Blade> bladeList = new List<Blade>();
    [SerializeField] private List<Bullet> reboundBullet = new List<Bullet>();
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private float[] pincherRotation;
    private Bone bone = null;
    private Bone root = null;
    private Bounds bladeBound;

    private int currentState = 0;
    private int destroyTentacleState = 0;
    private bool allowDamageBlock = false;
    private double damageBlockedInTime = 0;
    private bool allowShadow = false;
    private float boundaryDistance = 2000/103;
    private OktoPussStateMachine stateMachine = null;

    public bool BlockDamage { set { allowDamageBlock = value; } }
    public float BoundryDistance { set { boundaryDistance = value; } }
    public SpriteRenderer Shadow { get { return shadow; } }



    private const float ENEMY_SCALE = 2.0f;
    private const float ROOT_SCALE = 0.34f;
    private const int NUMBER_OF_JELLY_SWARM = 15;

    private delegate void bulletAnim(Bullet b);
    private event bulletAnim bAnim;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "okto" + GetInstanceID().ToString();
        schedulerId = "oktoS" + GetInstanceID().ToString();
        bladeContainer.parent = null;
        InitStats();

        isBoss = true;
        allowRelocate = false;
        Globals.bossShouldStayOnScreen = true;
        isOktoBoss = true;

        transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1400), Globals.LOWERBOUNDARY - Globals.CocosToUnity(60));
        enemySprite.state.Data.SetMix("attack phase4", "idle3", 0.08f);
        enemySprite.state.Data.SetMix("idle3", "attack phase4", 0.08f);

        SetShadowEnabled(false);
        InitEventListeners();

        root = enemySprite.skeleton.FindBone("root");

        //bound = spSkeletonBounds_create();
        //ladeBound = spSkeletonBounds_create();


        stateMachine = OktoPussStateMachine.Create(this);
        stateMachine.AddState<OktoStateEntry>();
        stateMachine.AddState<OktoStateIdleGround>();
        stateMachine.AddState<OktoStateWalkGround>();
        stateMachine.AddState<OktoStateEntryPhase2>();
        stateMachine.AddState<OktoStateIdlePhase2>();
        stateMachine.AddState<OktoStateWalkPhase2>();
        stateMachine.AddState<OktoStateBlock>();
        stateMachine.AddState<OktoStateBlockPhase2>();
        stateMachine.AddState<OktoStateSummonJellyFish>();
        stateMachine.AddState<OktoStateDoubleRocket>();
        stateMachine.AddState<OktoStateTentacleAttack>();
        stateMachine.AddState<OktoStateEntryPhase3>();
        stateMachine.AddState<OktoStateAttackPhase3>();
        stateMachine.AddState<OktoStateEntryPhase4>();
        stateMachine.AddState<OktoStateIdlePhase4>();
        stateMachine.AddState<OktoStateSummonJellyFishSwarm>();
        stateMachine.AddState<OktoStateAttackPhase4>();
        stateMachine.AddState<OktoStateDeath>();

        stateMachine.EnterState<OktoStateEntry>();
        bubbleContainer.parent = null;
        for (int i = 0; i < 2; i++)
        {
            bubbleCreator[i].SetFollowObject(followTransform);
            bubbleCreator[i].scaleMultiplier = 2.0f;
            bubbleCreator[i].distanceMultiplier = 2.0f;
            bubbleCreator[i].SpawnBubble();
            bubbleCreator[i].transform.SetLocalPositionY(Globals.CocosToUnity(200) + (i * Globals.CocosToUnity(100)));
        }
        scheduleUpdate = true;
        
    }

    
    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        offset = new Vector2(0, 3);
        int bossNumber = 12;
        if (GameManager.instance.missionManager.missionType == "Boss")
        { 
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }
        bossNumber = 12; //TODO Remove
        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"])/3f;
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.bulletSpeed = baseStats.bulletSpeed = 6.5f;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }

    }

    private void InitEventListeners()
    {
        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        {
            //TODO
            //if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)
            //{
            //Fixme fix sound res name
            if (spineEvent.Data.Int == 1)
            {
                string soundName;
                soundName = "%s" + spineEvent.Data.Name;
                Globals.PlaySound(soundName);
                AudioManager.instance.PlaySound(AudioType.Enemy,soundName); //todo
            }
            if (string.Compare(spineEvent.Data.Name, "Monster Big Walking Distant 1_02") == 0)
            {
                GameManager.instance.ShakeCamera(1, 5);

                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.MonsterBigWalkingDistant);
            }
            if (string.Compare(spineEvent.Data.Name, "ShootPhase1") == 0)
            {
                ShootPhase1();
            }
            if (string.Compare(spineEvent.Data.Name, "Mine") == 0)
            {
                DeployMine();

            }
            if (string.Compare(spineEvent.Data.Name, "Phase3Attack") == 0)
            {
                Phase3Attack();
            }
            if (string.Compare(spineEvent.Data.Name, "phase3Attack") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy,Constants_Audio.Audio.phase3Attack);
            }
            if (string.Compare(spineEvent.Data.Name, "Jump") == 0)
            {
                print("normjump");
                DOTween.Sequence().SetId(tweenId).Append(transform.DOBlendableMoveBy(new Vector2(0, Globals.CocosToUnity(500)), 0.5f)).Play();
            }
            if (string.Compare(spineEvent.Data.Name, "BigSonicBlast") == 0)
            {
                CreateBlades();
            }
            if (string.Compare(spineEvent.Data.Name, "Phase4Jump") == 0)
            {
                print("jump");
                DOTween.Sequence().SetId(tweenId).Append(transform.DOLocalJump(new Vector2(Globals.CocosToUnity(2000) * (root.ScaleX < 0 ? 1 : -1), Globals.LOWERBOUNDARY-0.60f), Globals.CocosToUnity(350), 1, 1.37f)).Play();
            }
            if (string.Compare(spineEvent.Data.Name, "ScaleXCheck") == 0)
            {
                ScaleXCheck();
            }
            if (string.Compare(spineEvent.Data.Name, "DoubleRocket") == 0)
            {
                DoubleRocket();
            }
            if (string.Compare(spineEvent.Data.Name, "EnableShadow") == 0)
            {
                SetShadowEnabled(true);
            }
            if (string.Compare(spineEvent.Data.Name, "Explosion") == 0)
            {
                bone = enemySprite.skeleton.FindBone("main");

                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, bone.GetWorldPosition(transform), false, 1, 4, 0);
                //Fixme this.addChild(explosion, 2);

            }
            if (string.Compare(spineEvent.Data.Name, "SpawnJellyFish") == 0)
            {
                for (int i = 0; i < 5; i++)
                {
                    JellyFish jf = Instantiate(jellyFish);
                    jf.Init();
                }
            }
            if (string.Compare(spineEvent.Data.Name, "MissileAttack") == 0)
            {
                for (int i = 0; i < 15 + (currentState * 25); i++)
                {
                    DOTween.Sequence().SetId(tweenId).AppendInterval(0.05f * i).AppendCallback(MissileAttack).Play();
                }
            }
            if (string.Compare(spineEvent.Data.Name, "Complex Interface 5") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.ComplexInterface);
            }
            if (string.Compare(spineEvent.Data.Name, "creature demonic (1)") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.creaturedemonic);
            }
            if (string.Compare(spineEvent.Data.Name, "Device 2 Stop") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Device2Stop);
            }
            if (string.Compare(spineEvent.Data.Name, "Door 1 Close 1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Door1Close1);
            }
            if (string.Compare(spineEvent.Data.Name, "Door 3 Close 1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Door3Close1);
            }
            if (string.Compare(spineEvent.Data.Name, "Door 8 Close 2") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Door8Close2);
            }
            if (string.Compare(spineEvent.Data.Name, "Door 9 Open") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Door9Open);
            }
            if (string.Compare(spineEvent.Data.Name, "Entry") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Entry);
            }
            if (string.Compare(spineEvent.Data.Name, "footstep 9 (2)") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.footstep9);
            }
            if (string.Compare(spineEvent.Data.Name, "gunAttack") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.gunAttack);
            }
            if (string.Compare(spineEvent.Data.Name, "gunAttack1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.gunAttack1);
            }
            if (string.Compare(spineEvent.Data.Name, "MediumExplosion15") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.MediumExplosion15);
            }
            if (string.Compare(spineEvent.Data.Name, "tranisiton") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.tranisiton);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Loop 1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterLoop1);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Loop 2") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterLoop2);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Loop 3") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterLoop3);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Loop 4") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterLoop4);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Loop 5") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterLoop5);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement1);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 2") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement2);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 3") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement3);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 4") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement4);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 5") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement5);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 6") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement6);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 7") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement7);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 8") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement8);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 9") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement9);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 10") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement10);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 11") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement11);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 12") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement12);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 13") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement13);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 14") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement14);
            }
            if (string.Compare(spineEvent.Data.Name, "Underwater Movement 15") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.UnderwaterMovement15);
            }
            if (string.Compare(spineEvent.Data.Name, "Weapon 10 Shoot 1") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Weapon10Shoot1);
            }
            if (string.Compare(spineEvent.Data.Name, "Weapon 14 Shoot 3 (Flamethrower)") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Weapon14Shoot3Flamethrower);
            }
            if (string.Compare(spineEvent.Data.Name, "Weapon 14 Shoot 3 (Flamethrower)") == 0)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.Weapon14Shoot3Flamethrower);
            }
        };
    }

    private void Update()
    {
        stateMachine.UpdateWithDeltaTime();
        //spSkeletonBounds_update(_bound, enemySprite.getSkeleton(), false);
        if (healthBar)
        {
            //healthBar.setPosition(enemySprite.getPosition().x - 50, enemySprite.getPosition().y + 25);
        }
        //    damageBlockedInTime -= dt;
        //    if(damageBlockedInTime < 0)
        //    {
        //        damageBlockedInTime = 0;
        //    }

        if (shadow && allowShadow)
        {
            shadow.transform.position = new Vector2(transform.position.x, Globals.LOWERBOUNDARY - 35f);
            float scaleX = 1.0f;
            scaleX = 12 + (transform.position.y - Globals.LOWERBOUNDARY - Globals.CocosToUnity(61)) / 100;
            scaleX = Mathf.Clamp(scaleX, 12, 18);
            shadow.transform.localScale = new Vector2(scaleX * 1.5f, scaleX * 0.25f);
            shadow.color = new Color(1, 1, 1, Mathf.Clamp(scaleX * 0.1f / 255, 0, 1));
        }

        foreach (Blade blade in bladeList)
        {
            if (blade.isInUse)
            {

                if (Vector2.SqrMagnitude(player.transform.position - blade.transform.position) < Globals.CocosToUnity(10000)
                    && player.canHit)// Player::getInstance().getPosition().distanceSquared(bullet.getPosition()) < 1000000)
                {

                    //spSkeletonBounds_update(_bladeBound, bullet.getSkeleton(), true);
                    if (blade.boundingBox.bounds.Contains(player.transform.position))// spSkeletonBounds_containsPoint(_bladeBound, Player::getInstance().getPosition().x - bullet.getPosition().x, Player::getInstance().getPosition().y - bullet.getPosition().y))
                    {
                        player.GotHit(22.0f, false);
                    }

                }
            }

        }

        Globals.LEFTBOUNDARY = Globals.LEFTBOUNDARY + (-Globals.LEFTBOUNDARY + (transform.position.x - boundaryDistance)) * Time.deltaTime * Globals.CocosToUnity(2);
        Globals.RIGHTBOUNDARY = Globals.RIGHTBOUNDARY + (-Globals.RIGHTBOUNDARY + (transform.position.x + boundaryDistance)) * Time.deltaTime * Globals.CocosToUnity(2);
        BulletReboundSeq();
    }

    public void SetRootScaleX(float scaleX)
    {
        root.ScaleX = scaleX;
    }

    public float GetRootScaleX()
    {
        return root.ScaleX;
    }


    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        else
            return false;
    }
    public override bool TakeHit(double damage)
    {
        if (allowDamageBlock)
        {
            return false;
        }
        damageBlockedInTime += damage+20;
        if (currentState == 0)
        {
            if (damageBlockedInTime > stats.maxHealth.Value * 0.035f)
            {
                stateMachine.EnterState<OktoStateBlock>();
                damageBlockedInTime = 0;
            }


        }
        if (currentState == 1)
        {
            if (damageBlockedInTime > stats.maxHealth.Value * 0.1f)
            {

                stateMachine.EnterState<OktoStateBlockPhase2>();
                damageBlockedInTime = 0;
            }


        }

        if (currentState == 2)
        {
            if (damageBlockedInTime > stats.maxHealth.Value * 0.05f)
            {

                DestroyTentacles();
                damageBlockedInTime = 0;
            }


        }
        bool isDead = base.TakeHit(damage);
        if (currentState == 0)
        {
            if (stats.health < stats.maxHealth.Value * 0.8f)
            {
                stateMachine.EnterState<OktoStateEntryPhase2>();
                currentState++;
                damageBlockedInTime = 0;
                Observer.DispatchCustomEvent("ChangeBossState");


            }
        }

        if (currentState == 1)
        {
            if (stats.health < stats.maxHealth.Value * 0.5f)
            {
                stateMachine.EnterState<OktoStateEntryPhase3>();
                currentState++;
                damageBlockedInTime = 0;
                Observer.DispatchCustomEvent("ChangeBossState");

            }
        }
        return isDead;

    }

    public void ScaleXCheck()
    {
        if (player.transform.position.x > transform.position.x)
        {
            SetRootScaleX(-ROOT_SCALE);
        }
        else
        {
            SetRootScaleX(ROOT_SCALE);
        }
    }

    public void SetShadowEnabled(bool val)
    {
        shadow.gameObject.SetActive(val);
        allowShadow = val;
    }

    public void ArmTracking()
    {
        bone = enemySprite.skeleton.FindBone("Right_Arm2");
        float angle = Globals.CalcAngle(transform.position, player.transform.position) + (root.ScaleX < 0 ? 180 : 0);
        angle = angle * (root.ScaleX < 0 ? 1.0f : -1.0f);
        if (bone.Rotation - angle > 180)
        {
            bone.Rotation -= 360;
        }
        if (bone.Rotation - angle < -180)
        {
            bone.Rotation += 360;
        }
        bone.Rotation = bone.Rotation + (-bone.Rotation + angle) * Time.deltaTime * 5;

        bone = enemySprite.skeleton.FindBone("Left_Arm2");

        if (bone.Rotation - angle > 180)
        {
            bone.Rotation -= 360;
        }
        if (bone.Rotation - angle < -180)
        {
            bone.Rotation += 360;
        }
        bone.Rotation = bone.Rotation + (-bone.Rotation + angle) * Time.deltaTime * 5;
        //bone = enemySprite.skeleton.FindBone("Right_Arm2");
        //float dir = Vector2.SignedAngle(player.transform.position - bone.GetWorldPosition(transform), transform.right) + 90;
        //float rotDir = dir < 0 ? 360 + dir : dir;
        //rotDir = rotDir * (root.ScaleX < 0 ? -1.0f : 1.0f);
        //bone.Rotation = rotDir;
        //bone = enemySprite.skeleton.FindBone("Left_Arm2");
        //dir = Vector2.SignedAngle(player.transform.position - bone.GetWorldPosition(transform), transform.right) + 90;
        //rotDir = dir < 0 ? 360 + dir : dir;
        //bone.Rotation = rotDir;
    }

    public void TentacleAttackTracking()
    {
        bone = enemySprite.skeleton.FindBone("Tentacle_36");
        float dir = Vector2.SignedAngle(player.transform.position - bone.GetWorldPosition(transform), transform.right)+90;
        float rotDir = dir < 0 ? 360 + dir : dir;
        rotDir = rotDir * (root.ScaleX < 0 ? -1.0f : 1.0f);
        bone.Rotation = rotDir;// + (-bone.Rotation + angle) * Time.deltaTime * 5;

    }

    public void TentacleAttackCollision()
    {
        bone = enemySprite.skeleton.FindBone("Pincher8");
        float dir = Vector2.SignedAngle(player.transform.position - bone.GetWorldPosition(transform), transform.right) + 90;
        float rotDir = dir < 0 ? 360 + dir : dir;
        if (Vector2.Distance(player.transform.position, bone.GetWorldPosition(transform)) < Globals.CocosToUnity(250))
        {
            player.Knockback(rotDir, Globals.CocosToUnity(200), Globals.CocosToUnity(60));
            //GETPLAYERCONTROLLER.knockback(Shared::calcAngle(Player::getInstance().getPosition(), Point(enemySprite.getPosition().x, enemySprite.getPosition().y)), 200, 60);
        }

    }

    public void DestroyTentacles()
    {
        destroyTentacleState++;
        enemySprite.state.TimeScale = 0.75f + (destroyTentacleState * 0.35f);

        enemySprite.state.SetAnimation(10, "tentacleOff" + destroyTentacleState, false);
        for (int i = (destroyTentacleState * 2) - 1; i <= (destroyTentacleState * 2); i++)
        {

            string ch = "Pincher" + i;
            bone = enemySprite.skeleton.FindBone(ch);

            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, bone.GetWorldPosition(transform), false, 1, 5, 0);
            //this.addChild(explosion);
        }
        if (destroyTentacleState == 4)
        {
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, transform.position, false, 1, 0.5f, 0);
            //this.addChild(explosion);

            stateMachine.EnterState<OktoStateEntryPhase4>();
            currentState++;
            Observer.DispatchCustomEvent("ChangeBossState");

        }
    }

    public void SummonJellyFishSwarm()
    {
        DOTween.Sequence().SetId(tweenId).AppendCallback(() =>
        {
            JellyFish jf = Instantiate(jellyFish);
            jf.Init();
        }).AppendInterval(1f).SetLoops(NUMBER_OF_JELLY_SWARM).Play();
        DOTween.Sequence().SetId(tweenId).AppendInterval(NUMBER_OF_JELLY_SWARM).AppendCallback(() =>
        {
            stateMachine.EnterState<OktoStateIdlePhase4>();
        }).Play();
        //this->runAction(Sequence::create(Repeat::create(Sequence::create(CallFunc::create([=](){
        //    JellyFish* jf = JellyFish::create();
        //    this->addChild(jf);
        //}),DelayTime::create(1.0f), NULL), NUMBER_OF_JELLY_SWARM),CallFunc::create([=](){
        //}),NULL));

    }

    public void CreateBlades()
    {
            int bladeCount = 0;
            for (int i = 0; i < bladeList.Count; i++)
            {
                if (!bladeList[i].isInUse)
                {

                    bladeList[i].isInUse = true;
                //bone = bladeList[i].skeletonAnimation.skeleton.FindBone("root");
                //float originalRotation = bone.Rotation;
                float rot;
                if (root.ScaleX < 0)
                {
                    rot = -75 + (i * 30);
                }
                else
                {
                    rot = -75 - (i * 30)-30;
                }
                 bladeList[i].transform.SetRotation( rot);
                bladeList[i].transform.position = transform.position;
                    bladeList[i].meshRenderer.enabled = true;
                    bladeList[i].gameObject.SetActive(true);
                    bladeList[i].skeletonAnimation.state.SetAnimation(0, "idle", false);
                    PlayBladeAnim(bladeList[i]);

                    bladeCount++;
                }
                if (bladeCount == 6)
                {
                    break;
                }
            }
        

        //stateMachine.EnterState<OktoStateAttackPhase4>();

    }

    private void PlayBladeAnim(Blade obj)
    {
        Sequence seq = DOTween.Sequence().SetId(tweenId)
                    .Append(obj.transform.DOBlendableMoveBy(new Vector3(Globals.CocosToUnity(-6000) * Mathf.Sin(Mathf.Deg2Rad * obj.transform.eulerAngles.z),
                    Globals.CocosToUnity(6000) * Mathf.Cos(Mathf.Deg2Rad * obj.transform.eulerAngles.z)), 5f))
                    .OnComplete(() =>
                    {
                        obj.skeletonAnimation.state.SetEmptyAnimation(0, 0);
                        obj.transform.eulerAngles = Vector3.zero;
                        obj.transform.localPosition = Vector3.zero;
                        obj.isInUse = false;
                        obj.meshRenderer.enabled = false;
                        //obj.gameObject.SetActive(false);
                        //bone.Rotation = rotation;

                    }).Play();
    }

    public float GetPosX()
    {
        float posX;
        Bone bonePos = enemySprite.skeleton.FindBone("main");

        posX = bonePos.GetWorldPosition(transform).x;
        return posX;
    }

    public float GetPosY()
    {
        float posY;
        Bone bonePos = enemySprite.skeleton.FindBone("main");

        posY = bonePos.GetWorldPosition(transform).y;
        return posY;
    }




    private StateMachine GetStateMachine()
    {
        return stateMachine;
    }

    private void ShootPhase1()
    {
        for (int i = 0; i < 2; i++)
        {
            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.spriteRenderer.color = new Color(1, 1, 1, 0f);
            bullet.setDamage(stats.bulletDamage);
            bullet.PushBack = Globals.CocosToUnity(25);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(220));

            bullet.transform.SetScale(2.5f);
            bone = enemySprite.skeleton.FindBone("Pointer" + (i + 1));
            bullet.transform.position = bone.GetWorldPosition(transform);
            bullet.duration = 4.0f;
            bullet.transform.SetRotation(bone.LocalToWorldRotation(bone.Rotation)+90);// bone.Rotation + 90);
            Vector2 dest = new Vector2(Globals.CocosToUnity(4000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()),
                Globals.CocosToUnity(4000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation())*-1);

            bullet.gameObject.SetActive(true);
            bullet.PlayBulletAnim(bullet.duration, dest);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            for (int j = 0; j < 1; j++)
            {
                //SkeletonAnimation* sonic = SkeletonAnimation::createWithJsonFile("res/Enemy/Sonic_Bullet.json", "res/Enemy/Sonic_Bullet.atlas");
                bullet.SetBulletChild(BulletChild.SonicBullet);
                //bullet.addChild(sonic);
                //sonic.setPosition(bullet.getContentSize() / 2);
                //sonic.setAnimation(0, "Sonic_Bullet", true);
                //sonic.setCameraMask(GAMECAMERA);
                //bullet.setOpacity(0.1f);
                //sonic.setRotation(90);
                //sonic.setScale(0.75f);

            }
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
        }
    }

    private void DeployMine()
    {
        Mine mine = null;
        bool didFindMine = false;
        foreach (Mine m in GameSharedData.Instance.enemyMinePool)
        {
            if (!m.isInUse)
            {
                mine = m;
                mine.isInUse = true;
                didFindMine = true;
                break;
            }

        }
        if (!didFindMine)
        {
            return;
        }
        mine.transform.position = new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(250));
        mine.missileSprite.sprite = mineSprite;
        mine.Init();
        mine.SetDamage(300);
        mine.duration = 8;
        mine.radiusSQ = Globals.CocosToUnity(250);
        mine.transform.SetScale(0.8f);
        mine.SetVerticalAcceleration(-7f);
        mine.SetInitialSpeed(-18f);
        DOTween.Kill(mine.tweenId);
        mine.explosionType = Explosions.ExplosionType.ExplosionTypeAir2;
        float scale = root.ScaleX;
        mine.RemoveAfterDuration();
        DOTween.Sequence().SetId(mine.tweenId).AppendCallback(() =>
        {
            mine.transform.DORotate(new Vector3(0, 0, mine.transform.eulerAngles.z + 360 * scale), 1);
        }).SetLoops(-1).Play();
        GameSharedData.Instance.enemyMissilesInUse.Add(mine);

    }

    private void MissileAttack()
    {
        HomingMissile missile = null;
        bool didFindMissile = false;
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            return;
        }
        missile.missileSprite.sprite = missileSprite;
        missile.ResetBoostPosition();
        missile.SetDamage(stats.bulletDamage * 3f);
        missile.SetSpeed(25);
        missile.homingMultiplier = 3.0f;
        missile.duration = 15.0f;
        missile.radiusSQ = Globals.CocosToUnity(150);
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.deatomizerSound,  0.85f);
        //Globals.PlaySound("res/Sounds/SFX/deatomizerSound.mp3", false, 0.85f);
        missile._doExplodeOnWater = true;
        missile.transform.SetRotation(UnityEngine.Random.value * 180);
        missile.transform.position = new Vector2(transform.position.x, transform.position.y + Globals.CocosToUnity(450));
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.RemoveAfterDuration();
        missile.Init();

    }

    private void DoubleRocket()
    {
        for (int i = 0; i < 2; i++)
        {
            HomingMissile missile = null;
            bool didFindMissile = false;
            foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
            {
                if (!m.isInUse)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindMissile = true;
                    break;
                }

            }
            if (!didFindMissile)
            {
                return;
            }
           
            bone = enemySprite.skeleton.FindBone("claw" + (i + 1));
            missile.Init();
            missile.missileSprite.sprite = rocketSprite;
            missile.ResetBoostPosition();
            missile.SetDamage(250);
            missile.SetSpeed(18);
            missile.isDestructable = false;
            missile.homingMultiplier = 1.5f;
            missile.duration = 12;
            missile.radiusSQ = Globals.CocosToUnity(200);
            missile._doExplodeOnWater = true;
            missile.transform.SetRotation(bone.WorldToLocalRotationX + 180);
            missile.transform.localScale = new Vector2(-1.5f, 1.5f);
            missile.transform.position = bone.GetWorldPosition(transform);
            GameSharedData.Instance.enemyMissilesInUse.Add(missile);
            missile.CreateWithHomingDuration(20);
            missile.RemoveAfterDuration();
        }
    }

    private void Phase3Attack()
    {
        for (int i = 1 + (destroyTentacleState * 2); i <= 8; i++)
        {
            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.setDamage(stats.bulletDamage * 5);
            bullet.PushBack = Globals.CocosToUnity(35);
            bullet.SetSpriteFrame(bulletSpritePhase3);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(375));

            bullet.transform.SetScale(5.2f);
            bone = enemySprite.skeleton.FindBone("Pincher"+i);
            bullet.transform.position = bone.GetWorldPosition(transform);
            bullet.duration = 3.0f;
            float randomFactor = 10 + (destroyTentacleState * 10);
            bullet.transform.SetRotation(pincherRotation[i - 1] -randomFactor+ UnityEngine.Random.value*(randomFactor*2));// -randomFactor + (Random.value * (randomFactor * 2)));// + (Random.value * (randomFactor * 2))) // - randomFactor + (Random.value * (randomFactor * 2)));
           
            Vector2 dest = new Vector2(Globals.CocosToUnity(4000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()),
                Globals.CocosToUnity(4000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation())*-1);

            bullet.gameObject.SetActive(true);
            bullet.PlayBulletAnim(bullet.duration, dest);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            for (int j = 0; j < 1; j++)
            {
                //SkeletonAnimation* sonic = SkeletonAnimation::createWithJsonFile("res/Enemy/Sonic_Bullet.json", "res/Enemy/Sonic_Bullet.atlas");
                bullet.SetBulletChild(BulletChild.SonicBullet);
                //bullet.addChild(sonic);
                //sonic.setPosition(bullet.getContentSize() / 2);
                //sonic.setAnimation(0, "Sonic_Bullet", true);
                //sonic.setCameraMask(GAMECAMERA);
                //bullet.setOpacity(0.1f);
                //sonic.setRotation(90);
                //sonic.setScale(0.75f);

            }
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            reboundBullet.Add(bullet);
            //BulletReboundSeq(bullet,"bullet"+bullet.gameObject.GetInstanceID().ToString());

        }
    }

    private void BulletReboundSeq()
    {
        for (int i = 0; i < reboundBullet.Count; i++)
        {
            if (reboundBullet[i].transform.position.y < Globals.LOWERBOUNDARY + Globals.CocosToUnity(100))
            {//FixMe
                reboundBullet[i].transform.SetRotation(-reboundBullet[i].transform.GetRotation() + 180);
                reboundBullet[i].transform.DOKill();
                Vector2 dest = new Vector2(Globals.CocosToUnity(4000) * Mathf.Sin(Mathf.Deg2Rad * reboundBullet[i].transform.GetRotation()),
                Globals.CocosToUnity(4000) * Mathf.Cos(Mathf.Deg2Rad * reboundBullet[i].transform.GetRotation()) * -1);
                reboundBullet[i].PlayBulletAnim(reboundBullet[i].duration, dest);
                reboundBullet.Remove(reboundBullet[i]);
                i--;
            }
        }

    }

    public void Test(Bullet b)
    {

    }
    
    public override void Destroy()
    {
        scheduleUpdate = false;

        if (Globals.gameType == GameType.Survival)
        {
            Globals.LEFTBOUNDARY = -90000000;
            Globals.RIGHTBOUNDARY = 90000000;
        }
        Globals.allDamage = 200000;

        stateMachine.EnterState<OktoStateDeath>();
        GameSharedData.Instance.enemyList.Remove(this); //getInstance()->g_enemyArray.eraseObject(this);
        Globals.ResetBoundary();
    }

}
