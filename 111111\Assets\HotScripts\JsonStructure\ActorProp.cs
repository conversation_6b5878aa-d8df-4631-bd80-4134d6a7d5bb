﻿using System;

using X.PB;

namespace JsonStructure
{
    /// <summary>
    /// 角色属性(由Lua计算后传回C#)
    /// </summary>
    public class ActorProp : ICloneable
    {
        /// <summary>
        /// 初始质量
        /// </summary>
        public float Mass { get; set; }
        /// <summary>
        /// 生命
        /// </summary>
        public double HP { get; set; }
        /// <summary>
        /// 基础攻击(%)
        /// </summary>
        public double Attack { get; set; }
        /// <summary>
        /// 生命恢复
        /// </summary>
        public double Defense { get; set; }
        /// <summary>
        /// 攻击
        /// </summary>
        public double PhysicsAttack { get; set; }
        /// <summary>
        /// 伤害范围(%)
        /// </summary>
        public float MagicAttack { get; set; }
        /// <summary>
        /// 狂暴时长(%)
        /// </summary>
        public float PhysicsDefense { get; set; }
        /// <summary>
        /// 能量上限(%)
        /// </summary>
        public double MagicDefense { get; set; }
        /// <summary>
        /// 子弹伤害(%)
        /// </summary>
        public float CriticalStrike { get; set; }
        /// <summary>
        /// 直接伤害
        /// </summary>
        public double AntiCriticalStrike { get; set; }
        /// <summary>
        /// 伤害减免
        /// </summary>
        public double Parry { get; set; }
        /// <summary>
        /// 狂暴伤害(%)
        /// </summary>
        public double AntiParry { get; set; }
        /// <summary>
        /// 射程(%)
        /// </summary>
        public float Dodge { get; set; }
        /// <summary>
        /// 子弹速度(%)
        /// </summary>
        public float Hit { get; set; }
        /// <summary>
        /// XP值恢复(%)
        /// </summary>
        public float Armor { get; set; }
        /// <summary>
        /// 能量上限
        /// </summary>
        public double AntiArmor { get; set; }
        /// <summary>
        /// 减伤(%)
        /// </summary>
        public float DamageReduction { get; set; }
        /// <summary>
        /// 加伤(%)
        /// </summary>
        public float DamageAdd { get; set; }
        /// <summary>
        /// 移动速度
        /// </summary>
        public float 移动速度 { get; set; } = 1;
        /// <summary>
        /// 移动速度(%)
        /// </summary>
        public float MoveSpeed { get; set; }
        /// <summary>
        /// 转向速度
        /// </summary>
        public float 转向速度 { get; set; }
        /// <summary>
        /// 转向速度(%)
        /// </summary>
        public float TurnSpeed { get; set; }
        /// <summary>
        /// 基础生命(%)
        /// </summary>
        public double AttackDefense { get; set; }
        /// <summary>
        /// XP技能伤害(%)
        /// </summary>
        public double XpDamagePct { get; set; }
        /// <summary>
        /// 暴击率(%)
        /// </summary>
        public float CriticalHitRate { get; set; }
        /// <summary>
        /// 攻击速度(%)
        /// </summary>
        public float AttackSpeed { get; set; }
        /// <summary>
        /// 总生命(%)
        /// </summary>
        public double HP_Final { get; set; }
        /// <summary>
        /// 总攻击(%)
        /// </summary>
        public double Attack_Final { get; set; }
        /// <summary>
        /// 暴击伤害(%)
        /// </summary>
        public double DamageCritical { get; set; }
        /// <summary>
        /// 对BOSS伤害(%)
        /// </summary>
        public double DamageBossAddPct { get; set; }
        /// <summary>
        /// 获得经验(%)
        /// </summary>
        public double AcquireExp { get; set; }
        /// <summary>
        /// 获得银币(%)
        /// </summary>
        public float AcquireGold { get; set; }
        /// <summary>
        /// 吸血(%)
        /// </summary>
        public double AbsorbHP { get; set; }
        /// <summary>
        /// 反伤(%)
        /// </summary>
        public double DamageBounce { get; set; }

        #region 额外的数据(等级和经验)
        /// <summary>
        /// 等级
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Style", "IDE1006:命名样式", Justification = "<挂起>")]
        public int level { get; set; }
        /// <summary>
        /// 当前经验值
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Style", "IDE1006:命名样式", Justification = "<挂起>")]
        public double curEXP { get; set; }
        /// <summary>
        /// 最大经验值
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Style", "IDE1006:命名样式", Justification = "<挂起>")]
        public double maxEXP { get; set; }
        #endregion

        #region ICloneable
        public object Clone()
        {
            var clone = new ActorProp();
            CopyTo(clone);
            return clone;
        }

        public void CopyTo(ActorProp other)
        {
            other.Mass = Mass;
            other.HP = HP;
            other.Attack = Attack;
            other.Defense = Defense;
            other.PhysicsAttack = PhysicsAttack;
            other.MagicAttack = MagicAttack;
            other.PhysicsDefense = PhysicsDefense;
            other.MagicDefense = MagicDefense;
            other.CriticalStrike = CriticalStrike;
            other.AntiCriticalStrike = AntiCriticalStrike;
            other.Parry = Parry;
            other.AntiParry = AntiParry;
            other.Dodge = Dodge;
            other.Hit = Hit;
            other.Armor = Armor;
            other.AntiArmor = AntiArmor;
            other.DamageReduction = DamageReduction;
            other.DamageAdd = DamageAdd;
            other.移动速度 = 移动速度;
            other.MoveSpeed = MoveSpeed;
            other.转向速度 = 转向速度;
            other.TurnSpeed = TurnSpeed;
            other.AttackDefense = AttackDefense;
            other.XpDamagePct = XpDamagePct;
            other.CriticalHitRate = CriticalHitRate;
            other.AttackSpeed = AttackSpeed;
            other.HP_Final = HP_Final;
            other.Attack_Final = Attack_Final;
            other.DamageCritical = DamageCritical;
            other.DamageBossAddPct = DamageBossAddPct;
            other.AcquireExp = AcquireExp;
            other.AcquireGold = AcquireGold;
            other.AbsorbHP = AbsorbHP;
            other.DamageBounce = DamageBounce;
            other.level = level;
            other.curEXP = curEXP;
            other.maxEXP = maxEXP;
        }
        #endregion

        /// <summary>
        /// 加载数据:从MonsterNew表中的一行
        /// </summary>
        public void LoadFromMonsterNew(MonsterNew.Item csvRow_MonsterNew)
        {
            HP = csvRow_MonsterNew.Hp;
            PhysicsAttack = csvRow_MonsterNew.PhysicsAttack;
            移动速度 = Globals.UnityValueTransform(csvRow_MonsterNew.MoveSpeed);
            转向速度 = Globals.UnityValueTransform(csvRow_MonsterNew.TurnSpeed);
        }

        /// <summary>
        /// 加载数据:从BattleBrushEnemy表中的一行
        /// </summary>
        public void LoadFromBattleBrushEnemy(BattleBrushEnemy.Item csvRow_BattleBrushEnemy)
        {
            // 在配置值的±10%范围内随机
            HP = csvRow_BattleBrushEnemy.Hp * (0.9f + (UnityEngine.Random.value * 0.2f));
            PhysicsAttack = csvRow_BattleBrushEnemy.PhysicsAttack * (0.9f + (UnityEngine.Random.value * 0.2f));
            移动速度 = Globals.UnityValueTransform(csvRow_BattleBrushEnemy.MoveSpeed) * (0.9f + (UnityEngine.Random.value * 0.2f));
            转向速度 = Globals.UnityValueTransform(csvRow_BattleBrushEnemy.TurnSpeed) * (0.9f + (UnityEngine.Random.value * 0.2f));
        }
    }
}
