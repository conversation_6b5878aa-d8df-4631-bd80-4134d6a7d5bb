using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using Spine;
using TMPro;
using UnityEngine.SceneManagement;
public class MainMenu : MonoBehaviour
{
    [SerializeField] private StartMenu startMenu;
    public TextMeshProUGUI progress;
    public GameObject continueLayer;
    public GameObject trackLayer;
    public Slider slider;
    public GameObject dataLoadingBar;
    public TextMeshProUGUI tapToStartLabel;
    public bool _allowContinue;
    public float delayTime = 0.0f;
    public InputField txtField;
    public InputField gameStateField;
    public Menu pMenu;
    public SkeletonGraphic cloudTransition;
    public Bone bindBone;
    public SkeletonGraphic spineAnimation;
    public Vector2 bindBonePosition;
    public int gameCenterSignIn = 0;
    private AnimationStateData stateData;

    [SerializeField] private bool isDebugMode = false;
    [SerializeField] private CustomButton cheatButton;
    [SerializeField] private GameObject cheatLayer;
    [SerializeField] private GameObject cheatButtonsContainer;
    [SerializeField] private TextMeshProUGUI godModeLabel;
    [SerializeField] private TextMeshProUGUI dieModeLabel;
    [SerializeField] private PopUp notificationPopUp;
    [SerializeField] private PopUp notificationPopUp2;
    [SerializeField] private GameObject frameCounter;

    private bool scheduleUpdate = false;

    private void Start()
    {
        Init();
    }

    private void Init()
    {



        //TODO
        //ThirdPartyInterface::initThirdParty();

        /*****
         GameCenter Initialization
         *****/

        Globals.StopAllSounds();
        Globals.g_SoundVolume = 1.0f;
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);

        if (PlayerPrefs.GetInt("firstLaunch", 1)==1)
        {
            FirstLaunch();
        }


        Globals.AllowBgMusic = PlayerPrefs.GetInt("xmMusic", 0) == 0 ? true : false;
        Globals.AllowMusic = PlayerPrefs.GetInt("xmSound", 0) == 0 ? true : false;
        Vector2 winSize;
        winSize.x = 1334;
        winSize.y = 750;

        Vector2 actualWinSize = Globals.GetScreenSize();
        float factorX = actualWinSize.x / winSize.x;
        float factorY = actualWinSize.y / winSize.y;
        //spineAnimation.GetComponent<RectTransform>().localScale = new Vector2(0.27f * factorX, 0.27f * factorY);
        //Globals.Rescale(spineAnimation.gameObject, 0.6f);
        if (Globals.AllowBgMusic)
        {

            //TODO
            // AudioManager.instance.PlayMusic(Track.menuBg, false, 0.25f);
            AudioManager.instance.PlayMusic(7010);
            //CocosDenshion::SimpleAudioEngine::getInstance().setBackgroundMusicVolume(0.25);
            //CocosDenshion::SimpleAudioEngine::getInstance().setEffectsVolume(0.6);
        }

        //Cursor.visible = false;
        //spineAnimation.gameObject.SetActive(false);
        //    if (GameData::getInstance().getCurrentLanguageCode() == std::string("en") ||
        //        GameData::getInstance().getCurrentLanguageCode() == std::string("en-gb") ||
        //        GameData::getInstance().getCurrentLanguageCode() == std::string("en-ca"))
        //{
        //    }
        //else
        //    {
        //        spSkeleton_setAttachment(spineAnimation.getSkeleton(), "Logo", NULL);

        //    }

        if (Screen.width / Screen.height < 1.45f)
        {
            //TODO
            spineAnimation.transform.localScale = new Vector2(0.55f, 0.55f);
            //Globals.RelativeScaling(spineAnimation, 1.1, 1.1);
        }
        else if (Screen.width / Screen.height > 1.9f)
        {
            //TODO
            spineAnimation.transform.localScale = new Vector2(0.65f, 0.55f);
            //Globals.relativeScaling(spineAnimation, 1.12, 1);
        }
        if (!Configuration.FIRSTTIME)
        {
            spineAnimation.AnimationState.SetAnimation(0, "Entry", false);
            spineAnimation.AnimationState.AddAnimation(0, "idle", true);
        }
        else
        {
            spineAnimation.AnimationState.SetAnimation(0, "BeforeEntry", false);
        }
        //TODO
        spineAnimation.AnimationState.Data.SetMix("idle", "MenuToOptions", 0.15f);
        spineAnimation.AnimationState.Data.SetMix("idle", "MenuToMap", 0.15f);
        bindBone = spineAnimation.Skeleton.FindBone("bindConstraint");
        spineAnimation.AnimationState.Event += HandleAnimEvent;


        

        InitVariables();
#if !UNITY_STANDALONE
        //addTempMenuForMobileControls();
#endif
        //TODO
        //auto keylistener = EventListenerKeyboard::create();
        //keylistener.onKeyPressed = CC_CALLBACK_2(MainMenuScene::onKeyPressed, this);
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);
        //GamePad_Apple* ga = GamePad_Apple::create();
        //this.addChild(ga);

#if UNITY_STANDALONE


        //TODO CreateMouse
        //    DesktopPointer *dp = DesktopPointer::create(true);
        //    this.addChild(dp,INT_MAX);





        ////    Globals.ChangeCursor("");



        //    auto s = Director::getInstance().getWinSize();
        //    auto mouseListener = EventListenerMouse::create();


        //    mouseListener.onMouseMove = [=](EventMouse *event){
        //        _bindBonePosition.x = (event.getCursorX() - s.width/2) /3000;
        //        _bindBonePosition.y = (event.getCursorY() - s.height/2)/3000;

        //    };


        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);

#else
        //TODO NO Pointer
        //_allowContinue = true;
        //delayTime = 1.5f;

        //auto listener = EventListenerTouchAllAtOnce::create();
        //listener.onTouchesBegan = [=](const std::vector<Touch*>& touches, Event* event){

        if (_allowContinue)
        {
            spineAnimation.AnimationState.SetAnimation(0, "MenuToMap3", false);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.menuToMap);
            // tapToStartLabel.setVisible(false);
            //this.runAction(Sequence::create(DelayTime::create(0.3f), CallFunc::create([=](){
            //    Director::getInstance().replaceScene(TransitionFade::create(0.1f, WorldScene::createScene(), Color3B(157, 207, 236)));
            //}), NULL));

        }


        //};

        //_eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);


        //Device::setAccelerometerEnabled(true);

        //auto accListener = EventListenerAcceleration::create([=](Acceleration * acceleration, Event *event){
        //        _bindBonePosition.x = acceleration.x;
        //_bindBonePosition.y = acceleration.y;


        //event.stopPropagation();
        //});
        //getEventDispatcher().addEventListenerWithSceneGraphPriority(accListener, this);

#endif
        //    ThirdPartyInterface::initThirdParty();


        scheduleUpdate = true;

        if (Configuration.FIRSTTIME)
        {
#if !UNITY_STANDALONE
                DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() => {

                    int AFNotifcation = PlayerPrefs.GetInt("askForNotification", 0);
                    AFNotifcation++;
                    PlayerPrefs.SetInt("askForNotification", AFNotifcation);

                    if (AFNotifcation == 2)
                    {
                        //TODO SetNotification
                        notificationPopUp.CreateAsConfirmDialogue(GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationHeading"] as string, (GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationText1"] as string + "\n" + GameData.instance.GetMenuData(Globals.NOTIFICATION)["allowNotificationText2"] as string), () => {
                            //ThirdPartyInterface::registerForNotifications();
                        });
                        //Director::getInstance().getRunningScene().addChild(p, INT_MAX);
                    }
                });
#endif
        }


        _allowContinue = false;

        //Vector2 size = Director::getInstance().getWinSize();


        if (Application.internetReachability != NetworkReachability.NotReachable)
        {
            //    trackLayer.runAction(Repeat::create(Sequence::create(CallFunc::create([=](){

            //        if (ThirdPartyInterface::isPlayerSignedIn())
            //        {
            //            Director::getInstance().getOpenGLView().setCursorVisible(false);
            //            getEventDispatcher().dispatchCustomEvent(ThirdPartyInterface::EVENT_DID_SIGN_IN);
            //            gameCenterSignIn = 1;
            //            trackLayer.stopAllActions();
            //            ThirdPartyInterface::loadPlayerData();


            //            trackLayer.runAction(Sequence::create(DelayTime::create(5), CallFunc::create([=](){
            //                trackLayer.stopAllActions();
            //                this.readyToGo();
            //            }), NULL));
            //            trackLayer.runAction(Repeat::create(Sequence::create(CallFunc::create([=](){

            //                if (ThirdPartyInterface::isPlayerDataLoaded())
            //                {
            //                    if (ThirdPartyInterface::isGameCenterAvailable())
            //                    {
            //                        ThirdPartyInterface::GetPlayerPhoto();
            //                    }

            //                    trackLayer.stopAllActions();
            //                    std::string str = StringUtils::format("%d", 100) + "%";
            //                    slider.setPercent(97);
            //                    _progress.setString(Globals.convertStringNumbersToArabic(str));

            //                    trackLayer.runAction(Sequence::create(DelayTime::create(0.5f), CallFunc::create([=](){

            //                        this.readyToGo();
            //                        UserDefault::getInstance().destroyInstance();
            //                        FileHandler::getInstance().readData();

            //                    }), NULL));


            //                }
            //                else
            //                {
            //                    slider.setPercent(slider.getPercent() + 1);
            //                    std::string str = StringUtils::format("%d", slider.getPercent()) + "%";
            //                    _progress.setString(Globals.convertStringNumbersToArabic(str));
            //                    if (slider.getPercent() > 57)
            //                    {
            //                        slider.setPercent(100);
            //                        std::string str = StringUtils::format("%d", slider.getPercent()) + "%";
            //                        _progress.setString(Globals.convertStringNumbersToArabic(str));
            //                    }

            //                }

            //            }),DelayTime::create(0.4),NULL), 15));
            //        }
            //        else
            //        {
            //            slider.setPercent(slider.getPercent() + 1);
            //            std::string str = StringUtils::format("%d", slider.getPercent()) + "%";
            //            _progress.setString(Globals.convertStringNumbersToArabic(str));
            //            if (slider.getPercent() > 57)
            //            {
            //                slider.setPercent(100);
            //                std::string str = StringUtils::format("%d", slider.getPercent()) + "%";
            //                _progress.setString(Globals.convertStringNumbersToArabic(str));
            //            }
            //        }

            //    }),DelayTime::create(0.4),NULL), 30));
            //    trackLayer.runAction(Sequence::create(DelayTime::create(7), CallFunc::create([=](){
            //        trackLayer.stopAllActions();
            //        this.readyToGo();
            //    }), NULL));
            //}
            //else
            //{
            //    this.readyToGo();
            //}



            //getEventDispatcher().addCustomEventListener(ThirdPartyInterface::EVENT_DID_SIGN_IN,[=](EventCustom * event){
            //    std::string str = StringUtils::format("%d", 50) + "%";
            //    slider.setPercent(50);
            //    _progress.setString(Globals.convertStringNumbersToArabic(str));
            //});



        }
        else
        {
            ReadyToGo();
        }

        Globals.backFromGame = false;
        Configuration.FIRSTTIME = false;

        //    UIiCloudConflict *c = UIiCloudConflict::create();
        //    this.addChild(c,INT_MAX);
        //Globals.applyFBO(this);


        //TODO
        //this.runAction(Sequence::create(DelayTime::create(0.85f), CallFunc::create([=](){
        //    GamePadInstructions::create(GamePadInstructions::GPI_TYPE::BASIC, GamePadInstructions::GPI_POSITION::TOP_RIGHT);
        //    Director::getInstance().getRunningScene().setName("MainMenuScene");




#if UNITY_TVOS
    if (!Globals.isJoystickConnected)
    {
        UICustom::Popup* p = UICustom::Popup::createAsMessage(GameData.instance.GetMenuData(Globals.MAP_MENU)["gameControllerButton"] as string + " " + GameData.instance.GetMenuData(Globals.TUTORIAL)["required"] as string, GameData.instance.GetMenuData(Globals.MAIN_MENU)["controllerRequiredText"] as string);
        Director::getInstance().getRunningScene().addChild(p);

    }


#endif

        //        debug{
        //            UICustom::Popup *p = UICustom::Popup::create(Size(850,550), "DPAD SETTINGS");
        //            Director::getInstance().getRunningScene().addChild(p);
        //
        //
        //            MobileDpadSettings *mc = MobileDpadSettings::create();
        //            p.getDisplayArea().addChild(mc);
        //            mc.setPosition(p.getDisplayArea().getContentSize()/2);
        //
        //        }
        //}), NULL));

        Observer.RegisterCustomEvent(gameObject, "SHOW_SAVE_GAME_UI", () =>
        {
            DOTween.Sequence().AppendInterval(0.25f).AppendCallback(() =>
            {
                    //TODO SaveGameMenu
                    //UISaveGame* sg = UISaveGame::create();
                    //Director::getInstance().getRunningScene().addChild(sg, INT_MAX);

                });

        });

        //        cocos2d::ParticleSystemQuad *m_emitter;
        //        m_emitter=ParticleSystemQuad::create("res/MainMenu/snow.plist");
        //        m_emitter.setBlendFunc(BlendFunc::LINEAR_DODGE);
        //        m_emitter.setTexture(Director::getInstance().getTextureCache().addImage( "res/MainMenu/particle_texture.png"));
        //        m_emitter.setVisible(true);
        ////    m_emitter.setBlendFunc(BlendFunc::ALPHA_NON_PREMULTIPLIED);
        //        this.a
        ReadyToGo();

        if (isDebugMode)
        {
            cheatLayer.SetActive(true);
            cheatButton.defaultAction = ShowCheatButtonsCallBack;
            if (Globals.DONTDIE)
            {
                godModeLabel.text = "GOD MODE : ON"; 
            }

            if (Globals.DIEQUICK)
            {
                godModeLabel.text = "DIE QUICK MODE : ON";
            }
        }
    }
    private void HandleAnimEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
    }

    public void InitVariables()
    {
       
        if (GameData.instance.fileHandler.coins < 0)
        {
            GameData.instance.fileHandler.coins = 1000;
            //#if !Desktop
            //        if(sdkbox::PluginSdkboxPlay::isConnected())
            //        {
            //        sdkbox::PluginSdkboxPlay::saveGameDataBinary("coins", (const void *)to_string(GameData.instance.fileHandler.coins).c_str(), 16);
            //        }
            //#endif

            //GameData.instance.fileHandler.saveData(false);


        }

        GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));
    }

    public void PlayButtonCallback() {}
    public void MapButtonCallback()
    {
        if (_allowContinue)
        {

            //Director::getInstance()->replaceScene(WorldScene::createScene());
        }

    }
    public void ShopButtonCallback() {}

    public void FirstLaunch()
    {
        Debug.Log("first launch");

        //GameData.instance.fileHandler.EmptyData();
        //GameData.instance.fileHandler.ReadData();

        //GameData.instance.fileHandler.SaveData();




        InitVariables();

        GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));

        PlayerPrefs.SetInt("firstLaunch", 0);
        PlayerPrefs.SetInt("DashTutorial", 1);
        GameData.instance.fileHandler.SaveData();
    }

    public void Update()
    {
        {

            //TODO
            //bindBone.x = bindBone.x + (-bindBone.x + (bindBonePosition.x * 0.3f)) * Time.deltaTime * 10;
            //bindBone.y = bindBone.y + (-bindBone.y + (bindBonePosition.y * 0.3f)) * Time.deltaTime * 10;

        }
        if (notificationPopUp.IsOpen())
            return;
        if (Input.GetKeyUp(KeyCode.Q))
        {
            if(notificationPopUp)
            QuickLoadCallBack();
        }
        if (Input.GetKeyUp(KeyCode.R))
        {
            ResetCallBack();
        }
        if (Input.GetKeyUp(KeyCode.C))
        {
            SetCoinsCallBack();
        }
        if (Input.GetKeyUp(KeyCode.F))
        {
            ShowFpsCallBack();
        }
    }

    public void QuickLoadCallBack()
    {
        notificationPopUp.SetOkButtonDefaultAction(() =>
        {
            var txt = notificationPopUp.GetTextField();
            if (txt.text.Length > 0)
            {
                int val = int.Parse(txt.text);
                if (val > 0 && val < 33)
                {
                    GameData.instance.fileHandler.currentMission = val;
                    GameData.instance.fileHandler.missionsCompleted = val;
                    GameData.instance.fileHandler.missionUnlock = 0;
                    GameData.instance.fileHandler.coins = 1000000;
                    GameData.instance.fileHandler.SaveData();
                    notificationPopUp.ExitCallback();
                }
                else
                {
                    notificationPopUp2.CreateAsMessage("Error", "MISSION NUMBER MUST BE \n A NUMBER BETWEEN 1-32");
                }
            }
            else
            {
                notificationPopUp2.CreateAsMessage("Error", "MISSION NUMBER MUST BE \n A NUMBER BETWEEN 1-32");
            }
        });
        notificationPopUp.CreateInputPopup("Quick Load", "Mission Number");
    }

    public void ResetCallBack()
    {
        notificationPopUp.CreateAsConfirmDialogue("CHEAT MENU", "ARE YOU SURE YOU WANT \n TO RESART GAMESTATE?", () =>
         {
             GameData.instance.fileHandler.EmptyData();
             GameData.instance.fileHandler.ReadData();
             GameData.instance.fileHandler.SaveData();
             InitVariables();
             GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));
             //SceneManager.LoadScene(0);
         });
    }

    public void SetCoinsCallBack()
    {
       notificationPopUp.CreateAsConfirmDialogue("CHEAT MENU", "GET 1000000 coins?", ()=>
       {
            GameData.instance.fileHandler.coins = 1000000;
            GameData.instance.fileHandler.SaveData();
        });

    }

    public void ShowFpsCallBack()
    {
        frameCounter.SetActive(true);
    }

    public void GodModeCallBack()
    {
        if (Globals.DONTDIE)
        {
            Globals.DONTDIE = false;
            godModeLabel.text = "GOD MODE : OFF";
        }
        else
        {
            Globals.DONTDIE = true;
            godModeLabel.text = "GOD MODE : ON";
        }
    }

    public void DieQuickCallBack()
    {
        if (Globals.DIEQUICK)
        {
            Globals.DIEQUICK = false;
            dieModeLabel.text = "DIE QUICK MODE: OFF";
        }
        else
        {
            Globals.DIEQUICK = true;
            dieModeLabel.text = "DIE QUICK MODE: ON";
        }
    }

    private void ShowCheatButtonsCallBack()
    {
        if (cheatButtonsContainer.activeInHierarchy)
        {
            cheatButtonsContainer.SetActive(false);
        }
        else
        {
            cheatButtonsContainer.SetActive(true);
        }
    }

    public void PlayButtonCallbackWithValue(int val)
    {

    }

    //public void onKeyPressed(cocos2d::EventKeyboard::KeyCode keyCode, cocos2d::Event*event)



    public void ReadGameStateFile(string filename)
    {
        //TODO
        //    if (FileUtils::getInstance()->isFileExist(fileName))
        //    {
        //        ValueMap v = FileUtils::getInstance()->getValueMapFromFile(fileName);
        //        for (auto keyVal : v)
        //        {
        //            string key = keyVal.first;

        //            Value val = keyVal.second;
        //            if (val.getType() == Value::Type::INTEGER)
        //            {
        //                UserDefault::getInstance()->setIntegerForKey(key.c_str(), val.asInt());
        //            }
        //            if (val.getType() == Value::Type::STRING)
        //            {
        //                UserDefault::getInstance()->setStringForKey(key.c_str(), val.asString());
        //            }
        //            if (val.getType() == Value::Type::BOOLEAN)
        //            {
        //                UserDefault::getInstance()->setBoolForKey(key.c_str(), val.asBool());
        //            }
        //        }
        //    }


        //    UserDefault::getInstance()->flush();
        //    GameData.instance.fileHandler.readData();

        //    UICustom::Popup* pNode = UICustom::Popup::createAsMessage("CHEAT MENU", "GameState Loaded");
        //    Director::getInstance()->getRunningScene()->addChild(pNode, INT_MAX - 1);
    }

    private void ReadyToGo()
    {


        //ThirdPartyInterface::setGKAccessPoint(true, true);

        spineAnimation.AnimationState.SetAnimation(0, "Entry", false);
        spineAnimation.AnimationState.AddAnimation(0, "idle", true);
        //StartMenuDesktop* smd = StartMenuDesktop::create(spineAnimation);
        //this->addChild(smd);
        startMenu.InitStartMenu();


    }


    public void initController() { }
    public void updateTrackPosition(float percentage) { }

    private void OnDestroy()
    {
        DOTween.KillAll();
    }
}
