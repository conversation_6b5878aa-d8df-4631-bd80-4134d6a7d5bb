using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class BaseMenu : MonoBehaviour
{
    [HideInInspector] public int exitIndex = 0;
    [HideInInspector] public List<MainMenuButton> buttonsList = new List<MainMenuButton>();
    [HideInInspector] public string clickSound;
    [HideInInspector] public string hoverSound;
    [HideInInspector] public int selectedPosition = 0;

    [SerializeField] private GameObject blurObject;

    private bool firstSound = true;
    private bool isEnabled = true;
    private bool foundTouch = false;

    public void SetEnable(bool enable)
    {
        if (enable)
        {
            DOTween.Sequence().AppendInterval(0.2f).AppendCallback(()=>{ isEnabled = true; }).Play();
        }
        else
        {
            isEnabled = false;
        }

    }

    public void AddNewGameLabel(MainMenuButton button, Vector2 pos)
    {
        button.rectTransform.anchoredPosition =pos;
        buttonsList.Add(button);
        UpdateSelected();
    }

    public List<MainMenuButton> GetButtonList()
    {
        return buttonsList;
    }

    public void OnExit()
    {

    }

    public void Init()
    {
        //    cocos2d::Point positionOffset = cocos2d::Point(0,0);
        //    Point winMinBounds = cocos2d::Point(-positionOffset.x * screenScaleValueX, -positionOffset.y * screenScaleValueY);
        //    Point winMaxBounds = cocos2d::Point((_winSize.width - positionOffset.x) * screenScaleValueX, (_winSize.height - positionOffset.y) * screenScaleValueY);
        //    _winSize = winMaxBounds;
        clickSound = Constants.AudioClips.SOUND_BUTTON_TAP;
        hoverSound = Constants.AudioClips.SOUND_HOVER;

        //if (!LayerColor::initWithColor(Color4B::BLACK, _winSize.width, _winSize.height))
        //{
        //    return false;
        //}
        //this.setOpacity(150);



//        auto listener = EventListenerTouchOneByOne::create();
//        listener.setSwallowTouches(true);

//        listener.onTouchBegan = [=](Touch * touch, Event * event){
//        if (!_isEnabled)
//        {
//            return false;
//        }

//        cocos2d::Point p = touch.getLocation();
//        p = this.convertToNodeSpace(p);
//        for (int i = 0; i < buttonsList.Count; ++i)
//        {
//            if (buttonsList.[i).checkCollision(cocos2d::Point(p.x - buttonsList.[i).getPosition().x, p.y - buttonsList.[i).getPosition().y)))
//            {
//                this._foundTouch = true;
//                return true;
//            }
//            else
//            {
//                this._foundTouch = false;
//            }
//        }
//        return false;
//    };
//    listener.onTouchEnded = [=](Touch* touch, Event* event){
//        if (!_isEnabled)
//        {
//            return false;
//        }

//        cocos2d::Point p = touch.getLocation();
//        p = this.convertToNodeSpace(p);
//        for (int i = 0; i < buttonsList.Count; ++i)
//        {
//            if (buttonsList.[i).checkCollision(cocos2d::Point(p.x - buttonsList.[i).getPosition().x, p.y - buttonsList.[i).getPosition().y)) && _foundTouch)
//            {
//                buttonsList.[i).active();
//                Shared::playSound(clickSound, false, 0.5f);
//                    event.stopPropagation();
//                    return true;
//                }
//            }
//            return false;
//        };
//_eventDispatcher.addEventListenerWithSceneGraphPriority(listener, this);


//#if UNITY_STANDALONE

//    auto mouseListener = EventListenerMouse::create();
//    mouseListener.onMouseMove = CC_CALLBACK_1(MacMenu::onMouseMove, this);
//    mouseListener.onMouseUp = CC_CALLBACK_1(MacMenu::onMouseUp, this);

//    _eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
//#endif

//auto keylistener = EventListenerKeyboard::create();
//keylistener.onKeyPressed = CC_CALLBACK_2(MacMenu::onKeyPressed, this);

//_eventDispatcher.addEventListenerWithSceneGraphPriority(keylistener, this);

//return true;
#if UNITY_STANDALONE
        foreach (MainMenuButton b in buttonsList)
        {
            b.mouseOverFunc = OnMouseMoveOver;
        }
#endif
    }

    public void UpdateSelected()
    {
        if (buttonsList.Count == 0)
            return;
        selectedPosition = Mathf.Clamp(selectedPosition, 0, buttonsList.Count - 1);
        if (buttonsList[selectedPosition].GetIsSelected())
        {
            return;
        }

        foreach (MainMenuButton b in buttonsList)
        {
            b.SetIsSelected(false);
        }
        if (firstSound)
        {
            firstSound = false;
        }
        else
        {
            //        Shared::playSound(SOUND_BUTTON_TAP,false,0.5f);
            
            AudioManager.instance.PlaySound(AudioType.Menu, hoverSound);

            //Globals.PlaySound(hoverSound, false, 1.0f);

        }
        buttonsList[selectedPosition].SetIsSelected(true);
    }

    public void SetBlurScene(bool val = true)
    {
        if (blurObject)
        {
            blurObject.SetActive(val);
        }
    }


    private void Update()
    {
        OnKeyPressed();
    }

    void OnKeyPressed()
    {

        if (!isEnabled || buttonsList.Count == 0)
        {
            return;
        }
        if (UnityEngine.Input.GetKeyUp(KeyCode.Escape))
        {
            if (exitIndex != -1)
            {
                buttonsList[exitIndex].CallMainFunc();
            }
        }
        if (UnityEngine.Input.GetKeyDown(KeyCode.DownArrow) || UnityEngine.Input.GetKeyDown(KeyCode.RightArrow))
        {
            selectedPosition++;
            UpdateSelected();
        }
        if (UnityEngine.Input.GetKeyDown(KeyCode.UpArrow) || UnityEngine.Input.GetKeyDown(KeyCode.LeftArrow))
        {
            selectedPosition--;
            UpdateSelected();
        }
        if (UnityEngine.Input.GetKeyDown(KeyCode.Return))
        {
            AudioManager.instance.PlaySound(AudioType.Menu, clickSound, 0.5f);
            //Globals.PlaySound(clickSound, false, 0.5f);
            buttonsList[selectedPosition].CallMainFunc();
        }
    }

    public void OnMouseMoveOver()
    {
        if (!isEnabled)
        {
            return;
        }
        for (int i = 0; i < buttonsList.Count; ++i)
        {
            if (buttonsList[i].GetPointerOver())
            {
                selectedPosition = i;
                UpdateSelected();
            }
        }
    }
    //void onMouseUp(cocos2d::EventMouse*event);
}
