using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
public class PopUp : MonoBehaviour
{
    [SerializeField] private Transform mainWindowContainer;
    [SerializeField] private CustomButton yesButton;
    [SerializeField] private CustomButton noButton;
    [SerializeField] private CustomButton okButton;
    [SerializeField] private NavigationButton yesNavButton, okNavButton;
    [SerializeField] private TextMeshProUGUI main;
    [SerializeField] private TextMeshProUGUI message;
    [SerializeField] private TMP_InputField textField;
    [SerializeField] private BlurHandlerCustom blurHandler;
    [SerializeField] private TextMeshSizeFitter sizeFitter;
    [SerializeField] private GameObject buttonsContainer;
    public System.Action addedCloseFunction;
    public System.Action addedNoCallback;

    bool isChildOfPopup = false;
    int selectedButton = 0;

     Vector2 CONFIRM_DIALOGUE_SIZE_OFFSET = new Vector2(100, 180);
     float ANIMATION_TIME = 0.25f;

     bool BLUR_EFFECT = true;

    public void Create(Vector2 size, string title, bool isChildOfPopup = false)
    {
        addedNoCallback = null;
        this.isChildOfPopup = isChildOfPopup;
        Init();
    }

    private void ResetPopup()
    {
        yesButton.gameObject.SetActive(false);
        noButton.gameObject.SetActive(false);
        okButton.gameObject.SetActive(false);
    }

    public void CreateAsMessage(string title, string msg, bool isOkButton = false)
    {
        print("here");
        CreateAsConfirmDialogue(title, msg, null, isOkButton);
        Init();
    }

    public void CreateAsConfirmDialogue(string title, string msg, System.Action YesFunc, bool isOkButton = false)
    {
        CreateDialogue(title, msg, YesFunc, isOkButton, false);
        Init();
    }
    public void CreateDialogue(string title, string msg,System.Action YesFunc, bool isOkButton, bool txtField)
    {
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp,0.25f);

        ResetPopup();

        //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.25f);
        if (!isOkButton )
        {
            message.text = msg;
            sizeFitter.SetOffset(50, 150);
            buttonsContainer.SetActive(false);
        }
        else
        {
            message.text = "";
            print("hi");
            sizeFitter.SetOffset(270, 250);

        }
        main.text = title;
        if (YesFunc != null && !isOkButton)
        {
            sizeFitter.SetOffset(50, 200);
            buttonsContainer.SetActive(true);
            yesButton.gameObject.SetActive(true);
            noButton.gameObject.SetActive(true);
            yesButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["yes"] as string;//, GAME_FONT, H3);

            bool isYesCalled = false;
            yesButton.defaultAction = () =>
              {
                  if (!isYesCalled)
                  {
                      YesFunc?.Invoke();
                      ExitCallback();
                      isYesCalled = true;
                      yesButton.StopAttention();
                  }
              };
            yesButton.CallForAttention(1);
            noButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["no"] as string;//, GAME_FONT, H3);

            noButton.defaultAction = () =>
            {
                ExitCallback();
            };

            if (yesNavButton)
            {
                NavigationButton.ChangeCurrentlySelected(yesNavButton, true);
            }
        }
        if (isOkButton)
        {
            buttonsContainer.SetActive(true);
            okButton.gameObject.SetActive(true);
            textField.gameObject.SetActive(true);
            print(textField.placeholder.GetComponent<TextMeshProUGUI>().text);
            //if (okButton.defaultAction != null)
            //{
            //    okButton.defaultAction = () =>
            //    {
            //        if (txtField)
            //        {
            //            //Shared::pauseRecursive(Director::getInstance().getRunningScene(), false);

            //            //Director::getInstance().getEventDispatcher().dispatchCustomEvent(title, (void*)node._txtField.getString().c_str());
            //        }
            //        ExitCallback();
            //    };
            //}

            if (okNavButton)
            {
                NavigationButton.ChangeCurrentlySelected(okNavButton, true);
            }
        }

        if (txtField)
        {
            
                //lbl.setVisible(false);


            
        }
    }
    public void CreateInputPopup(string title, string msg)
    {
        print("Input");
        CreateDialogue(title, msg, null, true, true);
        Init();
    }

    public void ExitCallback()
    {
        if (BLUR_EFFECT)
        {
            blurHandler.DisableBlur();
        }

        addedNoCallback?.Invoke();
        yesButton.StopAttention();


        DOTween.Sequence().AppendInterval(ANIMATION_TIME / 2).AppendCallback(() =>
          {
              Observer.DispatchCustomEvent("MOUSE_POINTER_SHOW");

              textField.gameObject.SetActive(false);
              gameObject.SetActive(false);
          });
        //runAction(FadeTo::create(ANIMATION_TIME / 2, 0));
        //this->runAction(Sequence::create(DelayTime::create(ANIMATION_TIME / 2), CallFunc::create([](){
        //    Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("MOUSE_POINTER_SHOW");
        //}),RemoveSelf::create(), NULL));
    }

    //public Image getDisplayArea()
    //{

    //}

    private void Init()
    {
        Observer.DispatchCustomEvent("MOUSE_POINTER_HIDE");
        //        if(!LayerColor::initWithColor(Color4B(0,0,0,0),g_winSize.width,g_winSize.width/aspectRatio))
        //        {
        //            return false;
        //        }

        //if(_doBgOverlay)
        selectedButton = 0;
        //Shared::rescale(this, 1);


        mainWindowContainer.localScale = Vector3.zero;
        gameObject.SetActive(true);
        if (BLUR_EFFECT)
        {
            DOTween.Sequence().AppendInterval(0.1f).Append(mainWindowContainer.DOScale(Vector3.one, 0.05f)).Play();
        }
        else
        {
            DOTween.Sequence().AppendInterval(0.02f).Append(mainWindowContainer.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutElastic)).Play();

        }
        //        this->runAction(FadeTo::create(ANIMATION_TIME/2,100));
//#if Desktop
//        //DesktopPointer *dp = DesktopPointer::create(false);
//        //this->addChild(dp,20);
//        //dp->setToConvertNodeSpace();
//#endif

        //        Shared::scaleNode(dp);
        //        dp->setScaleX(dp->getScaleX( ) * 0.75f);
        //        dp->setScaleY(dp->getScaleY() * 0.75f);
        //        dp->setScale(0.75f * MAX(this->getScaleX() ,this->getScaleY()));

        //#if Desktop
        if (BLUR_EFFECT)
        {
            if (blurHandler)
            {
                gameObject.SetActive(true);
                blurHandler.BeginBlur();
                StartCoroutine(WaitTillScreenShot());
                IEnumerator WaitTillScreenShot()
                {
                    while (!blurHandler.capturedScreenshot)
                    {
                        yield return new WaitForEndOfFrame();
                    }
                }
            }
        }

        //#endif
    }

    private void OnDisable()
    {
        mainWindowContainer.SetScale(0);
    }

    public void SetOkButtonDefaultAction(System.Action action)
    {
        okButton.defaultAction = action;
    }

    public TMP_InputField GetTextField()
    {
        return textField;
    }

    public bool IsOpen()
    {
        if (mainWindowContainer.localScale.x > 0)
        {
            return true;
        }
        return false;
    }

}
