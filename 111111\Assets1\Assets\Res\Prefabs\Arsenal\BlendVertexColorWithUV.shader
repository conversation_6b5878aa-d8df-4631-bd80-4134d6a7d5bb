﻿// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

// unlit, vertex color, alpha blended, offset uv's
 // cull off

 Shader "BlendVertexColorWithUV" 
 {
     Properties 
     {
         _MainTex ("Base (RGB) Trans (A)", 2D) = "white" {}
     }

     SubShader
     {
         Tags {"Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"}
         ZWrite Off Lighting Off Cull Off Fog { Mode Off } Blend SrcAlpha OneMinusSrcAlpha
         LOD 110

         Pass 
         {
             CGPROGRAM
             #pragma vertex vert_vct
             #pragma fragment frag_mult 
             #pragma fragmentoption ARB_precision_hint_fastest
             #include "UnityCG.cginc"

             sampler2D _MainTex;
             float4 _MainTex_ST;

             struct vin_vct 
             {
                 float4 vertex : POSITION;
                 float4 color : COLOR;
                 float2 texcoord : TEXCOORD0;
             };

             struct v2f_vct
             {
                 float4 vertex : POSITION;
                 fixed4 color : COLOR;
                 float2 texcoord : TEXCOORD0;
             };

             v2f_vct vert_vct(vin_vct v)
             {
                 v2f_vct o;
                 o.vertex = UnityObjectToClipPos(v.vertex);
                 o.color = v.color;
                 o.texcoord = TRANSFORM_TEX (v.texcoord, _MainTex);;
                 return o;
             }

             fixed4 frag_mult(v2f_vct i) : COLOR
             {
                 fixed4 col = tex2D(_MainTex, i.texcoord) * i.color;
                 return col;
             }

             ENDCG
         } 
     }

     SubShader
     {
         Tags {"Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"}
         ZWrite Off Blend SrcAlpha OneMinusSrcAlpha Cull Off Fog { Mode Off }
         LOD 100

         BindChannels 
         {
             Bind "Vertex", vertex
             Bind "TexCoord", texcoord
             Bind "Color", color
         }

         Pass 
         {
             Lighting Off
             SetTexture [_MainTex] { combine texture * primary } 
         }
     }
 }