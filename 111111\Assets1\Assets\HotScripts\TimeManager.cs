using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class TimeManager : MonoBehaviour
{
    [HideInInspector] public string tweenID, schedulerID;

    public float TimeScale { get { return Time.timeScale; } }

    private void Start()
    {
        tweenID = "TimeManagerTween";
        schedulerID = "TimeManagerScheduler";
    }

    public void SetTimescale(float scale)
    {
        scale = Mathf.Clamp(scale, 0, 1);
        if (scale == 1) scale = Globals.game_speed;
        Time.timeScale = scale;
    }

    public void ResetTimescale()
    {
        Time.timeScale = Globals.game_speed;
    }

    public void SetTimescaleWithDelay(float scale, float delay)
    {
        DOTween.Sequence().SetId(schedulerID).AppendInterval(delay).AppendCallback(() =>
        {
            scale = Mathf.Clamp(scale, 0, 1);
            if (scale == 1) scale = Globals.game_speed;
            Time.timeScale = scale;
        });
    }

    public void ResetTimescaleWithDelay(float delay)
    {
        DOTween.Sequence().SetId(schedulerID).AppendInterval(delay).AppendCallback(ResetTimescale);
    }

    public void SetResetWithDelay(float setDelay, float setScale, float resetDelay)
    {
        DOTween.Sequence().SetId(schedulerID).AppendInterval(setDelay).AppendCallback(() =>
        {
            setScale = Mathf.Clamp(setScale, 0, 1);
            if (setScale == 1) setScale = Globals.game_speed;
            SetTimescale(setScale);
        }).AppendInterval(resetDelay).AppendCallback(ResetTimescale);
    }
}
