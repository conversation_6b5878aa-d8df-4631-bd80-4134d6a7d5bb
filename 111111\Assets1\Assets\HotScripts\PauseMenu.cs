using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using DG.Tweening;
using TMPro;
using UnityEngine.UI;
public class PauseMenu : BaseMenu
{
    [SerializeField] private BlurHandlerCustom blurHandler;
    [SerializeField] private GameObject pauseMenuDesktop;
    [SerializeField] private MainMenuButton resumeButton;
    [SerializeField] private MainMenuButton retryButton;
    [SerializeField] private MainMenuButton difficultyButton;
    [SerializeField] private MainMenuButton shopButton;
    [SerializeField] private MainMenuButton hqButton;
    [SerializeField] private MainMenuButton exitButton;
    [SerializeField] private GameObject shopHighlight;
    [SerializeField] private TextMeshProUGUI shopHighlightText;

    [SerializeField] private TextMeshProUGUI musicLabel;
    [SerializeField] private TextMeshProUGUI soundLabel;
    [SerializeField] private GameObject pauseMenuMobile;
    [SerializeField] private Image bgFade;
    [SerializeField] private CustomButton closeButton;
    [SerializeField] private CustomButton musicButton;
    [SerializeField] private CustomButton soundButton;
    [SerializeField] private CustomButton hqButtonMobile;
    [SerializeField] private CustomButton retryButtonMobile;
    [SerializeField] private CustomButton resumeButtonMobile;
    private List<CustomButton> buttonListMobile = new List<CustomButton>();
    [SerializeField] private Button closeBtn;

    //DesktopPointer dp;

    // Start is called before the first frame update
    void Start()
    {
        


        exitIndex = 0;
        //cocos2d::Point positionOffset = cocos2d::Point(0, 0);
        //Point winMinBounds = cocos2d::Point(-positionOffset.x * screenScaleValueX, -positionOffset.y * screenScaleValueY);
        //Point winMaxBounds = cocos2d::Point((_winSize.width - positionOffset.x) * screenScaleValueX, (_winSize.height - positionOffset.y) * screenScaleValueY);
        //_winSize = winMaxBounds;
        //this->setOpacity(0);
#if UNITY_STANDALONE
    //dp = DesktopPointer::create(false);
    //this->addChild(dp, INT_MAX);
    //dp->setToConvertNodeSpace();
    //Director::getInstance()->getOpenGLView()->setCursorVisible(false);
#endif
        //GamePad_Apple* ga = GamePad_Apple::create();
        //this->addChild(ga);
        //    setBlurScene();
        //TODO Fix This
        Globals.resetControls = true;
        //    Shared::pauseRecursive(this->getParent(), true);
        //if (ThirdPartyInterface::isGameCenterAvailable())
        //{

        //    GameCenterAccessPointUI* _gameCenterAccessPointObj = GameCenterAccessPointUI::create();
        //    this->addChild(_gameCenterAccessPointObj);
        //    //    _gameCenterAccessPointObj->setPosition(Vec2(winMaxBounds.x - 80 , winMaxBounds.y -80));
        //    _gameCenterAccessPointObj->setPosition(Vec2(80, winMaxBounds.y - 80));


        //    {
        //        MenuOptionMac* node = MenuOptionMac::createWithLabel(" ", [=](){

        //            _gameCenterAccessPointObj->GameCenterAccessPointCall();

        //        });
        //        this->addChild(node);
        //        _labelArray.pushBack(node);
        //        node->setOpacity(0.1);
        //        node->setSelectedFunc([=](){

        //            _gameCenterAccessPointObj->SelectedCall();

        //        });
        //        node->setUnSelectedFunc([=]{
        //            _gameCenterAccessPointObj->UnSelectedCall();


        //        });

        //    }
        //}
#if UNITY_ANDROID||UNITY_IOS
        closeButton.defaultAction = ResumeButtonCallBack;
        buttonListMobile.Add(closeButton);
        musicButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;
        musicButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;
        buttonListMobile.Add(musicButton);
        musicButton.defaultAction = () =>
        {
            musicButton.SetIsOn(false);
            Globals.AllowBgMusic = false;
            Globals.StopAllSounds();
            AudioManager.instance.SetMusicActive(true);
            PlayerPrefs.SetInt("MusicState", 0);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        };
        closeBtn.onClick.AddListener(ResumeButtonCallBack);
        musicButton.offAction = () =>
        {
            musicButton.SetIsOn(true);
            Globals.AllowBgMusic = true;
            if (Globals.AllowBgMusic)
            {
                AudioManager.instance.SetMusicActive(false);
                // AudioManager.instance.PlayMusic(Track.mainMenuMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7009);
            }
            PlayerPrefs.SetInt("MusicState", 1);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
        };

        if (PlayerPrefs.GetInt("MusicState") == 1)
        {
            musicButton.SetIsOn(true);

        }
        else
        {
            musicButton.SetIsOn(false);
        }

        soundButton.defaultLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["on"] as string;
        soundButton.offLabel.text = GameData.instance.GetMenuData(Globals.MAIN_MENU)["off"] as string;
        buttonListMobile.Add(soundButton);
        soundButton.defaultAction = () =>
        {
            soundButton.SetIsOn(false);
            PlayerPrefs.SetInt("SoundState", 0);
            AudioManager.instance.SetSoundEffectsActive(true);
            Globals.StopAllSounds();
            if (Globals.AllowBgMusic)
            {
                // AudioManager.instance.PlayMusic(Track.mainMenuMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7009);
            }
            Globals.AllowMusic = false;
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
        };

        soundButton.offAction = () =>
        {
            soundButton.SetIsOn(true);
            AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
            PlayerPrefs.SetInt("SoundState", 1);
        };

        if (PlayerPrefs.GetInt("SoundState") == 1)
        {
            soundButton.SetIsOn(true);
        }
        else
        {
            soundButton.SetIsOn(false);
        }
#endif
        {
#if UNITY_ANDROID || UNITY_IOS
            resumeButtonMobile.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["resumeButton"] as string;
            resumeButtonMobile.defaultAction = ResumeButtonCallBack;
            buttonListMobile.Add(resumeButtonMobile);
#else
            resumeButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["resumeButton"] as string, ResumeButtonCallBack);
            buttonsList.Add(resumeButton);
#endif
        }
        {
#if UNITY_ANDROID || UNITY_IOS
            retryButtonMobile.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["retryButton"] as string;
            retryButtonMobile.defaultAction = RetryButtonCallBack;
            buttonListMobile.Add(retryButtonMobile);
#else
            retryButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["retryButton"] as string, RetryButtonCallBack);
            buttonsList.Add(retryButton);
#endif
        }
#if UNITY_ANDROID || UNITY_IOS

#else
        {
            if (Globals.gameType != GameType.Survival)
            {
               difficultyButton.InitVariables( GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["selectDifficulty"] as string,()=>
               {

                   GameData.instance.fileHandler.ReadGuns();
                   GameData.instance.fileHandler.SaveData();
                   GameManager.instance.timeManager.SetTimescale(1);
                   Globals.g_showDifficultyInUtilityMenu = true;
                   SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex);
               });
                buttonsList.Add(difficultyButton);
            }
            else
            {
                difficultyButton.gameObject.SetActive(false);
            }
        }


        if (GameData.instance.fileHandler.missionsCompleted >= 1)
        {
            shopButton.InitVariables( GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["visitShop"] as string,ShopButtonCallBack);
            buttonsList.Add(shopButton);
            int getShopItemsAvailable = GameData.instance.CalculateShopItemsToBeUnlocked();
            if (getShopItemsAvailable > 0)
            {
                shopHighlight.SetActive(true);
                DOTween.Sequence().SetId("highlight").SetUpdate(true).Append(shopHighlight.transform.DOScale(Vector3.one * 1.87f, 0.75f).SetEase(Ease.InOutSine)).Append(shopHighlight.transform.DOScale(Vector3.one * 1.75f, 0.75f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();
                shopHighlightText.text = getShopItemsAvailable.ToString();
            }


        }
        else
        {
            shopButton.gameObject.SetActive(false);
        }
#endif
        if (GameData.instance.fileHandler.missionsCompleted >= 0)
        {
#if UNITY_ANDROID || UNITY_IOS
            hqButtonMobile.defaultLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["headQuarters"] as string;
            hqButtonMobile.defaultAction = HqButtonCallBack;
            buttonListMobile.Add(hqButtonMobile);
#else
           hqButton.InitVariables( GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["headQuarters"] as string,HqButtonCallBack);
            buttonsList.Add(hqButton);
#endif
        }
#if UNITY_ANDROID || UNITY_IOS

#else
        if (GameData.instance.fileHandler.missionsCompleted < 0)
        {
            exitButton.InitVariables( GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["exitGame"] as string,()=>{

                GameData.instance.fileHandler.ReadGuns();
                GameData.instance.fileHandler.SaveData();
                GameManager.instance.timeManager.SetTimescale(1);
                Application.Quit();
            });
            
            buttonsList.Add(exitButton);
        }
        else
        {
            exitButton.gameObject.SetActive(false);
        }
        
        selectedPosition = 0;
        Init();
        UpdateSelected();
#endif

    }

    public void Show()
    {
        if (!gameObject.activeSelf)
        {
            GameManager.instance.timeManager.SetTimescale(0);
#if UNITY_ANDROID || UNITY_IOS
            gameObject.SetActive(true);
            pauseMenuMobile.gameObject.SetActive(true);
            DOTween.Sequence().Append(pauseMenuMobile.transform.DOScale(Vector3.one, 0.1f)).SetUpdate(true).Play();
            bgFade.gameObject.SetActive(true);
            DOTween.Sequence().Append(bgFade.DOFade(0.75f,0.1f)).SetUpdate(true).Play();
            selectedPosition = 1;
            //UpdateSelected();
#else
            blurHandler.BeginBlur();
            gameObject.SetActive(true);
            StartCoroutine(WaitTillScreenShot());
            IEnumerator WaitTillScreenShot()
            {
                while(!blurHandler.capturedScreenshot)
                {
                    yield return new WaitForEndOfFrame();
                }
                selectedPosition = 0;
                pauseMenuDesktop.SetActive(true);
                UpdateSelected();
            }
#endif
        }
    }

    private void UpdateSelectedButton()
    {
        if (buttonListMobile.Count == 0)
            return;
        selectedPosition = Mathf.Clamp(selectedPosition, 0, buttonListMobile.Count - 1);
        buttonListMobile[selectedPosition].CallForAttention(0.1f);
    }

    private void ShopButtonCallBack()
    {
        GameData.instance.fileHandler.ReadGuns();
        GameData.instance.fileHandler.SaveData();
        GameManager.instance.timeManager.SetTimescale(1);
        Globals.showShop = true;
        SceneManager.LoadScene(3);
    }

    private void ResumeButtonCallBack()
    {
        Globals.resetControls = false;
#if UNITY_ANDROID || UNITY_IOS
        pauseMenuMobile.gameObject.SetActive(true);
        DOTween.Sequence().Append(pauseMenuMobile.transform.DOScale(Vector3.zero, 0.1f).OnComplete(()=>
        {
            Globals.SetPauseSounds(false);
            GameManager.instance.timeManager.SetTimescale(1);
            gameObject.SetActive(false);
            bgFade.gameObject.SetActive(false);
        })).SetUpdate(true).Play();
        DOTween.Sequence().Append(bgFade.DOFade(0, 0.1f)).SetUpdate(true).Play();
#else
        Globals.SetPauseSounds(false);
        GameManager.instance.timeManager.SetTimescale(1);
        blurHandler.DisableBlur();
        pauseMenuDesktop.SetActive(false);
        gameObject.SetActive(false);
#endif
    }

    private void RetryButtonCallBack()
    {
        GameData.instance.fileHandler.ReadGuns();
        GameData.instance.fileHandler.SaveData();
        GameManager.instance.timeManager.SetTimescale(1);
        SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex);
    }

    private void HqButtonCallBack()
    {
        Globals.backFromGamePlay = true;
        GameData.instance.fileHandler.ReadGuns();
        GameData.instance.fileHandler.SaveData();
        GameManager.instance.timeManager.SetTimescale(1);
        SceneManager.LoadScene(2);
    }
}
