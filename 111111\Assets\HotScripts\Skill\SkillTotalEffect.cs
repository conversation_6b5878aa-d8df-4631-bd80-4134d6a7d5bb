﻿using System;

using Apq.NotifyChange;

/// <summary>
/// 技能的总提升
/// </summary>
public class SkillTotalEffect : NotifyPropertyChange, ICloneable
{
    public NotifyChangeProperty<float> CD { get; }
    public NotifyChangeProperty<float> HitCD { get; }
    /// <summary>
    /// 持续时长
    /// </summary>
    public NotifyChangeProperty<float> 持续时长 { get; }
    /// <summary>
    /// 至少持续时长：秒(激光单向摆动一次的时长)
    /// </summary>
    public NotifyChangeProperty<float> MinAttackDuration { get; }
    /// <summary>
    /// 子弹缩放
    /// </summary>
    public NotifyChangeProperty<float> 子弹缩放 { get; }
    /// <summary>
    /// 攻击距离
    /// </summary>
    public NotifyChangeProperty<float> 攻击距离 { get; }
    /// <summary>
    /// 连射次数
    /// </summary>
    public NotifyChangeProperty<float> 连射次数 { get; }
    /// <summary>
    /// 子弹速度
    /// </summary>
    public NotifyChangeProperty<float> 子弹速度 { get; }
    /// <summary>
    /// 子弹数量
    /// </summary>
    public NotifyChangeProperty<float> 子弹数量 { get; }
    /// <summary>
    /// 环绕角速度
    /// </summary>
    public NotifyChangeProperty<float> 环绕角速度 { get; }
    public NotifyChangeProperty<float> 最大摆动次数 { get; }
    /// <summary>
    /// 爆炸半径(大于0才爆炸)
    /// </summary>
    public NotifyChangeProperty<float> 爆炸半径 { get; }
    public NotifyChangeProperty<float> 击中后爆炸的概率 { get; }
    public NotifyChangeProperty<float> 击杀后爆炸的概率 { get; }
    public NotifyChangeProperty<float> 击杀后爆炸的次数 { get; }
    public NotifyChangeProperty<float> 爆炸伤害系数 { get; }
    public NotifyChangeProperty<float> 子弹最大穿透次数 { get; }
    public NotifyChangeProperty<float> 子弹最大反弹次数 { get; }
    /// <summary>
    /// 穿透时伤害的最小间隔时长:秒
    /// </summary>
    public NotifyChangeProperty<float> MinDamageInterval { get; }
    public NotifyChangeProperty<float> 子弹最大分裂次数 { get; }
    public NotifyChangeProperty<float> 击中敌人后子弹分裂数量 { get; }
    /// <summary>
    /// 子弹最大存活时长：秒
    /// </summary>
    public NotifyChangeProperty<float> 子弹时长 { get; }
    //public NotifyChangeList<int> BuffIDs { get; }
    public NotifyChangeList<int> BuffPriorities { get; }
    /// <summary>
    /// 技能伤害系数
    /// </summary>
    public NotifyChangeProperty<float> DamageCoe { get; }
    /// <summary>
    /// 定身时长(秒)
    /// </summary>
    public NotifyChangeProperty<float> DenyMoveTime { get; }
    /// <summary>
    /// 定身时长百分比
    /// </summary>
    public NotifyChangeProperty<float> DenyMoveTimePct { get; }
    /// <summary>
    /// 击退时长(秒)
    /// </summary>
    public NotifyChangeProperty<float> HitBackTime { get; }
    /// <summary>
    /// 击退时长百分比
    /// </summary>
    public NotifyChangeProperty<float> HitBackTimePct { get; }
    /// <summary>
    /// 击退速度
    /// </summary>
    public NotifyChangeProperty<float> HitBackSpeed { get; }
    /// <summary>
    /// 击退速度百分比
    /// </summary>
    public NotifyChangeProperty<float> HitBackSpeedPct { get; }
    /// <summary>
    /// 击退时长(秒)
    /// </summary>
    public NotifyChangeProperty<float> SlowTime { get; }
    /// <summary>
    /// 击退时长百分比
    /// </summary>
    public NotifyChangeProperty<float> SlowTimePct { get; }
    /// <summary>
    /// 击退速度
    /// </summary>
    public NotifyChangeProperty<float> SlowSpeed { get; }
    /// <summary>
    /// 击退速度百分比
    /// </summary>
    public NotifyChangeProperty<float> SlowSpeedPct { get; }

    public SkillTotalEffect()
    {
        CD = new(nameof(CD), this);
        HitCD = new(nameof(HitCD), this);
        持续时长 = new(nameof(持续时长), this);
        MinAttackDuration = new(nameof(MinAttackDuration), this);
        子弹缩放 = new(nameof(子弹缩放), this);
        攻击距离 = new(nameof(攻击距离), this);
        连射次数 = new(nameof(连射次数), this);
        子弹速度 = new(nameof(子弹速度), this);
        子弹数量 = new(nameof(子弹数量), this);
        环绕角速度 = new(nameof(环绕角速度), this);
        最大摆动次数 = new(nameof(最大摆动次数), this);
        爆炸半径 = new(nameof(爆炸半径), this);
        击中后爆炸的概率 = new(nameof(击中后爆炸的概率), this);
        击杀后爆炸的概率 = new(nameof(击杀后爆炸的概率), this);
        击杀后爆炸的次数 = new(nameof(击杀后爆炸的次数), this);
        爆炸伤害系数 = new(nameof(爆炸伤害系数), this);
        子弹最大穿透次数 = new(nameof(子弹最大穿透次数), this);
        子弹最大反弹次数 = new(nameof(子弹最大反弹次数), this);
        MinDamageInterval = new(nameof(MinDamageInterval), this);
        子弹最大分裂次数 = new(nameof(子弹最大分裂次数), this);
        击中敌人后子弹分裂数量 = new(nameof(击中敌人后子弹分裂数量), this);
        子弹时长 = new(nameof(子弹时长), this);
        //BuffIDs = new();
        BuffPriorities = new();
        DamageCoe = new(nameof(DamageCoe), this);
        DenyMoveTime = new(nameof(DenyMoveTime), this);
        DenyMoveTimePct = new(nameof(DenyMoveTimePct), this);
        HitBackTime = new(nameof(HitBackTime), this);
        HitBackTimePct = new(nameof(HitBackTimePct), this);
        HitBackSpeed = new(nameof(HitBackSpeed), this);
        HitBackSpeedPct = new(nameof(HitBackSpeedPct), this);
        SlowTime = new(nameof(SlowTime), this);
        SlowTimePct = new(nameof(SlowTimePct), this);
        SlowSpeed = new(nameof(SlowSpeed), this);
        SlowSpeedPct = new(nameof(SlowSpeedPct), this);
    }

    #region ICloneable
    public object Clone()
    {
        var clone = new SkillTotalEffect();
        CopyTo(clone);
        return clone;
    }

    public void CopyTo(SkillTotalEffect other)
    {
        other.CD.Value = CD.Value;
        other.HitCD.Value = HitCD.Value;
        other.持续时长.Value = 持续时长.Value;
        other.MinAttackDuration.Value = MinAttackDuration.Value;
        other.子弹缩放.Value = 子弹缩放.Value;
        other.攻击距离.Value = 攻击距离.Value;
        other.连射次数.Value = 连射次数.Value;
        other.子弹速度.Value = 子弹速度.Value;
        other.子弹数量.Value = 子弹数量.Value;
        other.环绕角速度.Value = 环绕角速度.Value;
        other.最大摆动次数.Value = 最大摆动次数.Value;
        other.爆炸半径.Value = 爆炸半径.Value;
        other.击中后爆炸的概率.Value = 击中后爆炸的概率.Value;
        other.击杀后爆炸的概率.Value = 击杀后爆炸的概率.Value;
        other.击杀后爆炸的次数.Value = 击杀后爆炸的次数.Value;
        other.爆炸伤害系数.Value = 爆炸伤害系数.Value;
        other.子弹最大穿透次数.Value = 子弹最大穿透次数.Value;
        other.子弹最大反弹次数.Value = 子弹最大反弹次数.Value;
        other.MinDamageInterval.Value = MinDamageInterval.Value;
        other.子弹最大分裂次数.Value = 子弹最大分裂次数.Value;
        other.击中敌人后子弹分裂数量.Value = 击中敌人后子弹分裂数量.Value;
        other.子弹时长.Value = 子弹时长.Value;
        //other.BuffIDs.Clear();
        //other.BuffIDs.AddRange(BuffIDs);
        other.BuffPriorities.Clear();
        other.BuffPriorities.AddRange(BuffPriorities);
        other.DamageCoe.Value = DamageCoe.Value;
        other.DenyMoveTime.Value = DenyMoveTime.Value;
        other.DenyMoveTimePct.Value = DenyMoveTimePct.Value;
        other.HitBackTime.Value = HitBackTime.Value;
        other.HitBackTimePct.Value = HitBackTimePct.Value;
        other.HitBackSpeed.Value = HitBackSpeed.Value;
        other.HitBackSpeedPct.Value = HitBackSpeedPct.Value;
        other.SlowTime.Value = SlowTime.Value;
        other.SlowTimePct.Value = SlowTimePct.Value;
        other.SlowSpeed.Value = SlowSpeed.Value;
        other.SlowSpeedPct.Value = SlowSpeedPct.Value;
    }
    #endregion
}
