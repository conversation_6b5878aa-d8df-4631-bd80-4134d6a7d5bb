using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class InfoItem : Mono<PERSON><PERSON><PERSON>our, IPointerExitHandler, IPointerEnterHandler
{
    private Shop shop;
    [SerializeField] private ShopButton shopButton;
    [SerializeField] string itemString;

    Vector2 sizeDelta;
    RectTransform rectTransform;
    PList shopMenuData;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        sizeDelta = rectTransform.sizeDelta;
    }

    private void Start()
    {
        shop = MainMenuController.instance.shopMenu;
        shopMenuData = GameData.instance.GetMenuData("ShopScene");
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (shopButton)
        {
            string infoStr = shopMenuData["missionPopup"] as string;
            infoStr += " " + shopButton.GetUnlockLevel();

            shop.ShowInfoDialogue(true, true, rectTransform, infoStr);
        }
        else
        {
            string infoStr = shopMenuData[itemString] as string;
            rectTransform.sizeDelta = sizeDelta * 1.2f;
            shop.ShowInfoDialogue(true, false, rectTransform, infoStr, new Vector2(rectTransform.sizeDelta.x / 6f, rectTransform.sizeDelta.y / 2.75f));
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (shopButton)
        {
            shop.ShowInfoDialogue(false, true);
        }
        else
        {
            rectTransform.sizeDelta = sizeDelta;
            shop.ShowInfoDialogue(false, false);
        }
    }
}
