﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.ApqMath;
using Apq.NotifyChange;

using JsonStructure;

using Newtonsoft.Json;

using PbContract;

using UnityEngine;

using X.PB;

/// <summary>
/// 技能的属性
/// </summary>
public class SkillProps : NotifyPropertyChange
{
	/// <summary>
	/// 技能完成的令牌
	/// </summary>
	public CancellationTokenSource CTS_SkillFinish { get; set; } = new();

	/// <summary>
	/// 技能加入到了哪个调度器里
	/// </summary>
	public SkillScheduler SkillScheduler { get; set; }

	/// <summary>
	/// 最后使用时间(Time.time)
	/// </summary>
	public float LastDoTime { get; set; }

	/// <summary>
	/// 使用者的属性
	/// </summary>
	public CreatureProps CreatureProps => SkillScheduler.Creature.FightProp;

	/// <summary>
	/// 技能ID
	/// </summary>
	public int SkillID => CsvRow_CatSkill.FirstSkillID;

	/// <summary>
	/// 技能等级
	/// </summary>
	public int SkillLvl => SkillEffects.Count + 1;

	protected CatSkill.Item CsvRow_CatSkill_m;

	/// <summary>
	/// 技能配置(该等级的基础值)
	/// </summary>
	public CatSkill.Item CsvRow_CatSkill
	{
		get => CsvRow_CatSkill_m;
		set
		{
			if (CsvRow_CatSkill_m == value) return;

			if (OnPropertyChanging(nameof(CsvRow_CatSkill), CsvRow_CatSkill_m, value)) return;

			var originalValue = CsvRow_CatSkill_m;
			CsvRow_CatSkill_m = value;
			OnPropertyChanged(nameof(CsvRow_CatSkill), originalValue, value);
		}
	}

	#region 属性提升

	/// <summary>
	/// 获得的技能提升
	/// </summary>
	public NotifyChangeList<CatSkillEffect.Item> SkillEffects { get; } = new();

	/// <summary>
	/// 总提升(对SkillEffects的汇总)
	/// </summary>
	public SkillTotalEffect TotalEffect { get; } = new();

	/// <summary>
	/// 玩家装备给技能的提升
	/// </summary>
	public ActorSkill ActorSkill { get; set; }

	#endregion

	#region 提升后的属性值

	/// <summary>
	/// CD
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> CD { get; }

	/// <summary>
	/// HitCD
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> HitCD { get; }

	/// <summary>
	/// 持续时长
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 持续时长 { get; }

	/// <summary>
	/// 至少持续时长：秒(激光单向摆动一次的时长)
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> MinAttackDuration { get; }

	/// <summary>
	/// 子弹缩放
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹缩放 { get; }

	/// <summary>
	/// 攻击距离
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 攻击距离 { get; }

	/// <summary>
	/// 攻击距离的平方
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 攻击距离Sqr { get; }

	/// <summary>
	/// 连射次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 连射次数 { get; }

	/// <summary>
	/// 子弹速度
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹速度 { get; }

	/// <summary>
	/// 环绕角速度
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 环绕角速度 { get; }

	/// <summary>
	/// 最大摆动次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 最大摆动次数 { get; }

	/// <summary>
	/// 爆炸半径(大于0才爆炸)
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 爆炸半径 { get; }

	/// <summary>
	/// 击中后爆炸的概率
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 击中后爆炸的概率 { get; }

	/// <summary>
	/// 击杀后爆炸的概率
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 击杀后爆炸的概率 { get; }

	/// <summary>
	/// 击杀后爆炸的次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 击杀后爆炸的次数 { get; }

	/// <summary>
	/// 爆炸伤害系数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 爆炸伤害系数 { get; }

	/// <summary>
	/// 子弹最大穿透次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹最大穿透次数 { get; }

	/// <summary>
	/// 子弹最大反弹次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹最大反弹次数 { get; }

	/// <summary>
	/// 穿透时伤害的最小间隔时长:秒
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> MinDamageInterval { get; }

	/// <summary>
	/// 子弹最大分裂次数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹最大分裂次数 { get; }

	/// <summary>
	/// 子弹最大存活时长：秒
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> 子弹时长 { get; }

	///// <summary>
	///// BuffIDs
	///// </summary>
	///// <remarks>提升后的属性值</remarks>
	//public NotifyChangeList<int> BuffIDs { get; }
	/// <summary>
	/// BuffPriorities
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeList<int> BuffPriorities { get; }

	/// <summary>
	/// 技能伤害系数
	/// </summary>
	/// <remarks>提升后的属性值</remarks>
	public NotifyChangeProperty<float> DamageCoe { get; }

	/// <summary>
	/// 定身时长(秒)
	/// </summary>
	public NotifyChangeProperty<float> DenyMoveTime { get; }

	/// <summary>
	/// 定身时长百分比
	/// </summary>
	public NotifyChangeProperty<float> DenyMoveTimePct { get; }

	/// <summary>
	/// 击退时长(秒)
	/// </summary>
	public NotifyChangeProperty<float> HitBackTime { get; }

	/// <summary>
	/// 击退时长百分比
	/// </summary>
	public NotifyChangeProperty<float> HitBackTimePct { get; }

	/// <summary>
	/// 击退速度
	/// </summary>
	public NotifyChangeProperty<float> HitBackSpeed { get; }

	/// <summary>
	/// 击退速度百分比
	/// </summary>
	public NotifyChangeProperty<float> HitBackSpeedPct { get; }

	/// <summary>
	/// 减速时长(秒)
	/// </summary>
	public NotifyChangeProperty<float> SlowTime { get; }

	/// <summary>
	/// 减速时长百分比
	/// </summary>
	public NotifyChangeProperty<float> SlowTimePct { get; }

	/// <summary>
	/// 减速速度
	/// </summary>
	public NotifyChangeProperty<float> SlowSpeed { get; }

	/// <summary>
	/// 减速速度百分比
	/// </summary>
	public NotifyChangeProperty<float> SlowSpeedPct { get; }

	#endregion

	/// <summary>
	/// 转为传给Lua的Json { SkillID:0, MaxLevel:6, SkillLvl: 0, Type: 0, CD: 0.0, MinCD: 0.0, Icon: "", SkillEffectIDs: [1,2...] }
	/// </summary>
	public string ToJson_Lua()
	{
		JsonStructure.Skill rtn = new()
		{
			SkillID = SkillID,
			MaxLevel = Globals.CsvRow_CatMainStage.MaxSkillLvl,
			Type = CsvRow_CatSkill.Type,
			CD = CD.Value,
			MinCD = CsvRow_CatSkill.MinCD,
			Icon = CsvRow_CatSkill.Icon,
			SkillEffectIDs = SkillEffects.Select(x => x.Id).ToList(),
		};
		return JsonConvert.SerializeObject(rtn);
	}

	public SkillProps()
	{
		CD = new(nameof(CD), this);
		HitCD = new(nameof(HitCD), this);
		持续时长 = new(nameof(持续时长), this);
		MinAttackDuration = new(nameof(MinAttackDuration), this);
		子弹缩放 = new(nameof(子弹缩放), this);
		攻击距离 = new(nameof(攻击距离), this);
		攻击距离Sqr = new(nameof(攻击距离Sqr), this);
		连射次数 = new(nameof(连射次数), this);
		子弹速度 = new(nameof(子弹速度), this);
		环绕角速度 = new(nameof(环绕角速度), this);
		最大摆动次数 = new(nameof(最大摆动次数), this);
		爆炸半径 = new(nameof(爆炸半径), this);
		击中后爆炸的概率 = new(nameof(击中后爆炸的概率), this);
		击杀后爆炸的概率 = new(nameof(击杀后爆炸的概率), this);
		击杀后爆炸的次数 = new(nameof(击杀后爆炸的次数), this);
		爆炸伤害系数 = new(nameof(爆炸伤害系数), this);
		子弹最大穿透次数 = new(nameof(子弹最大穿透次数), this);
		子弹最大反弹次数 = new(nameof(子弹最大反弹次数), this);
		MinDamageInterval = new(nameof(MinDamageInterval), this);
		子弹最大分裂次数 = new(nameof(子弹最大分裂次数), this);
		子弹时长 = new(nameof(子弹时长), this);
		//BuffIDs = new();
		BuffPriorities = new();
		DamageCoe = new(nameof(DamageCoe), this);
		DenyMoveTime = new(nameof(DenyMoveTime), this);
		DenyMoveTimePct = new(nameof(DenyMoveTimePct), this);
		HitBackTime = new(nameof(HitBackTime), this);
		HitBackTimePct = new(nameof(HitBackTimePct), this);
		HitBackSpeed = new(nameof(HitBackSpeed), this);
		HitBackSpeedPct = new(nameof(HitBackSpeedPct), this);
		SlowTime = new(nameof(SlowTime), this);
		SlowTimePct = new(nameof(SlowTimePct), this);
		SlowSpeed = new(nameof(SlowSpeed), this);
		SlowSpeedPct = new(nameof(SlowSpeedPct), this);

		#region 属性的级联改变

		SkillEffects.ListChanged += SkillEffects_ListChanged;
		TotalEffect.PropertyChanged += TotalEffect_PropertyChanged;
		TotalEffect.BuffPriorities.ListChanged += TotalEffect_BuffPriorities_ListChanged;
		攻击距离.Changed += 攻击距离_Changed;
		攻击距离Sqr.Changed += 攻击距离Sqr_Changed;

		#endregion
	}

	#region 属性的级联改变

	protected override void Me_PropertyChanged(NotifyPropertyChange me, PropertyChangeEventArgs e)
	{
		switch (e.PropertyName)
		{
			// 配置改变时，重新计算所有属性
			case nameof(CsvRow_CatSkill):
				{
					ReGenAll();
				}
				break;
		}

		base.Me_PropertyChanged(me, e);
	}

	private void SkillEffects_ListChanged(NotifyChangeListEventArgs<CatSkillEffect.Item> e)
	{
		// 提升列表改变,重新计算总提升
		var n = new SkillTotalEffect();

		n.CD.Value = (float)SkillEffects.Sum(x => (double)x.Cd);
		n.HitCD.Value = (float)SkillEffects.Sum(x => (double)x.HitCD);
		n.持续时长.Value = (float)SkillEffects.Sum(x => (double)x.AttackDuration);
		n.MinAttackDuration.Value = (float)SkillEffects.Sum(x => (double)x.MinAttackDuration);
		n.子弹缩放.Value = (float)SkillEffects.Sum(x => (double)x.BulletScale);
		n.攻击距离.Value = (float)SkillEffects.Sum(x => (double)x.AttackRadius);
		n.连射次数.Value = (float)SkillEffects.Sum(x => (double)x.ShootTimes);
		n.子弹速度.Value = (float)SkillEffects.Sum(x => (double)x.BulletSpeed);
		n.子弹数量.Value = (int)SkillEffects.Sum(x => (double)x.BulletQty);
		n.环绕角速度.Value = (float)SkillEffects.Sum(x => (double)x.SurroundSpeed);
		n.最大摆动次数.Value = (float)SkillEffects.Sum(x => (double)x.MaxSwingTimes);
		n.爆炸半径.Value = (float)SkillEffects.Sum(x => (double)x.ExploseRadius);
		n.击中后爆炸的概率.Value = (float)SkillEffects.Sum(x => (double)x.ExplosePriority);
		n.击杀后爆炸的概率.Value = (float)SkillEffects.Sum(x => (double)x.KillExplosePriority);
		n.击杀后爆炸的次数.Value = (float)SkillEffects.Sum(x => (double)x.KillExploseTimes);
		n.爆炸伤害系数.Value = (float)SkillEffects.Sum(x => (double)x.ExploseCoe);
		n.子弹最大穿透次数.Value = (float)SkillEffects.Sum(x => (double)x.MaxPenetrateTimes);
		n.子弹最大反弹次数.Value = (float)SkillEffects.Sum(x => (double)x.MaxBounceTimes);
		n.MinDamageInterval.Value = (float)SkillEffects.Sum(x => (double)x.MinDamageInterval);
		n.子弹最大分裂次数.Value = (float)SkillEffects.Sum(x => (double)x.MaxSeparateTimes);
		n.击中敌人后子弹分裂数量.Value = (float)SkillEffects.Sum(x => (double)x.BulletSeparate);
		n.子弹时长.Value = (float)SkillEffects.Sum(x => (double)x.BulletLife);
		n.DamageCoe.Value = (float)SkillEffects.Sum(x => (double)x.DamageCoe);
		n.DenyMoveTime.Value = (float)SkillEffects.Sum(x => (double)x.DenyMoveTime);
		n.DenyMoveTimePct.Value = (float)SkillEffects.Sum(x => (double)x.DenyMoveTimePct);
		n.HitBackTime.Value = (float)SkillEffects.Sum(x => (double)x.HitBackTime);
		n.HitBackTimePct.Value = (float)SkillEffects.Sum(x => (double)x.HitBackTimePct);
		n.HitBackSpeed.Value = (float)SkillEffects.Sum(x => (double)x.HitBackSpeed);
		n.HitBackSpeedPct.Value = (float)SkillEffects.Sum(x => (double)x.HitBackSpeedPct);
		n.SlowTime.Value = (float)SkillEffects.Sum(x => (double)x.SlowTime);
		n.SlowTimePct.Value = (float)SkillEffects.Sum(x => (double)x.SlowTimePct);
		n.SlowSpeed.Value = (float)SkillEffects.Sum(x => (double)x.SlowSpeed);
		n.SlowSpeedPct.Value = (float)SkillEffects.Sum(x => (double)x.SlowSpeedPct);
		//SkillEffects.ForEach(x => n.BuffIDs.AddRange(x.BuffIDs));

		{
			List<int> lst = new();
			foreach (var x in SkillEffects)
			{
				_ = x.BuffPriorities.Select((p, i) =>
				{
					if (lst.Count <= i)
					{
						lst.Add(p);
					}
					else
					{
						lst[i] += p;
					}

					return p;
				});
			}

			n.BuffPriorities.AddRange(lst);
		}

		n.CopyTo(TotalEffect);
	}

	private void TotalEffect_PropertyChanged(NotifyPropertyChange sender, PropertyChangeEventArgs e)
	{
		switch (e.PropertyName)
		{
			case nameof(CD):
				{
					GenCD();
				}
				break;
			case nameof(HitCD):
				{
					GenHitCD();
				}
				break;
			case nameof(持续时长):
				{
					Gen持续时长();
				}
				break;
			case nameof(MinAttackDuration):
				{
					GenMinAttackDuration();
				}
				break;
			case nameof(子弹缩放):
				{
					Gen子弹缩放();
				}
				break;
			case nameof(攻击距离):
				{
					Gen攻击距离();
				}
				break;
			case nameof(连射次数):
				{
					Gen连射次数();
				}
				break;
			case nameof(子弹速度):
				{
					Gen子弹速度();
				}
				break;
			case nameof(环绕角速度):
				{
					Gen环绕角速度();
				}
				break;
			case nameof(最大摆动次数):
				{
					Gen最大摆动次数();
				}
				break;
			case nameof(爆炸半径):
				{
					Gen爆炸半径();
				}
				break;
			case nameof(击中后爆炸的概率):
				{
					Gen击中后爆炸的概率();
				}
				break;
			case nameof(击杀后爆炸的概率):
				{
					Gen击杀后爆炸的概率();
				}
				break;
			case nameof(击杀后爆炸的次数):
				{
					Gen击杀后爆炸的次数();
				}
				break;
			case nameof(爆炸伤害系数):
				{
					Gen爆炸伤害系数();
				}
				break;
			case nameof(子弹最大穿透次数):
				{
					Gen子弹最大穿透次数();
				}
				break;
			case nameof(子弹最大反弹次数):
				{
					Gen子弹最大反弹次数();
				}
				break;
			case nameof(MinDamageInterval):
				{
					GenMinDamageInterval();
				}
				break;
			case nameof(子弹最大分裂次数):
				{
					Gen子弹最大分裂次数();
				}
				break;
			case nameof(子弹时长):
				{
					Gen子弹时长();
				}
				break;
			case nameof(DamageCoe):
				{
					GenDamageCoe();
				}
				break;
			case nameof(DenyMoveTime):
				{
					GenDenyMoveTime();
				}
				break;
			case nameof(DenyMoveTimePct):
				{
					GenDenyMoveTimePct();
				}
				break;
			case nameof(HitBackTime):
				{
					GenHitBackTime();
				}
				break;
			case nameof(HitBackTimePct):
				{
					GenHitBackTimePct();
				}
				break;
			case nameof(HitBackSpeed):
				{
					GenHitBackSpeed();
				}
				break;
			case nameof(HitBackSpeedPct):
				{
					GenHitBackSpeedPct();
				}
				break;
			case nameof(SlowTime):
				{
					GenSlowTime();
				}
				break;
			case nameof(SlowTimePct):
				{
					GenSlowTimePct();
				}
				break;
			case nameof(SlowSpeed):
				{
					GenSlowSpeed();
				}
				break;
			case nameof(SlowSpeedPct):
				{
					GenSlowSpeedPct();
				}
				break;
		}
	}

	private void TotalEffect_BuffPriorities_ListChanged(NotifyChangeListEventArgs<int> e)
	{
		GenBuffPriorities();
	}

	private void 攻击距离_Changed(string propertyName, float originalValue, float newValue)
	{
		攻击距离Sqr.Value = MathF.Pow(攻击距离.Value, 2);
	}

	private void 攻击距离Sqr_Changed(string propertyName, float originalValue, float newValue)
	{
		攻击距离.Value = MathF.Sqrt(newValue);
	}

	#endregion

	#region IDisposable

	/// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
	protected override void Dispose(bool disposing)
	{
		if (!disposedValue)
		{
			if (disposing)
			{
				SkillEffects.ListChanged -= SkillEffects_ListChanged;
				TotalEffect.PropertyChanged -= TotalEffect_PropertyChanged;
				TotalEffect.BuffPriorities.ListChanged -= TotalEffect_BuffPriorities_ListChanged;
				攻击距离.Changed -= 攻击距离_Changed;
				攻击距离Sqr.Changed -= 攻击距离Sqr_Changed;
			}

			// TODO: 释放未托管的资源(未托管的对象)并重写终结器
			// TODO: 将大型字段设置为 null
			disposedValue = true;
		}
	}

	#endregion

	#region Gen XX

	public void ReGenAll()
	{
		GenCD();
		GenHitCD();
		Gen持续时长();
		GenMinAttackDuration();
		Gen子弹缩放();
		Gen攻击距离();
		Gen连射次数();
		Gen子弹速度();
		Gen环绕角速度();
		Gen最大摆动次数();
		Gen爆炸半径();
		Gen击中后爆炸的概率();
		Gen击杀后爆炸的概率();
		Gen击杀后爆炸的次数();
		Gen爆炸伤害系数();
		Gen子弹最大穿透次数();
		Gen子弹最大反弹次数();
		GenMinDamageInterval();
		Gen子弹最大分裂次数();
		Gen子弹时长();
		//GenBuffIDs();
		GenBuffPriorities();
		GenDamageCoe();
		GenDenyMoveTime();
		GenDenyMoveTimePct();
		GenHitBackTime();
		GenHitBackTimePct();
		GenHitBackSpeed();
		GenHitBackSpeedPct();
		GenSlowTime();
		GenSlowTimePct();
		GenSlowSpeed();
		GenSlowSpeedPct();
	}

	public void GenCD()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.Cd;
		}

		float d = 1;

		if (TotalEffect != null && TotalEffect.CD.Value > 0)
		{
			d += TotalEffect.CD.Value;
		}

		if (ActorSkill != null)
		{
			d += Globals.UnityValueTransform(ActorSkill.Cd);
		}

		if (CreatureProps != null && CreatureProps.攻击速度.Value > 0)
		{
			d += CreatureProps.攻击速度.Value;
			//Debug.Log($"加上生物的攻击速度后 除数d={d}");
		}

		nValue /= d;

		// 最终CD不会比最小值更小
		if (nValue < CsvRow_CatSkill?.MinCD)
		{
			nValue = CsvRow_CatSkill.MinCD;
		}

		CD.Value = nValue;
	}

	public void GenHitCD()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.HitCD;
		}

		float d = 1;

		if (TotalEffect != null && TotalEffect.HitCD.Value > 0)
		{
			d += TotalEffect.HitCD.Value;
		}

		if (ActorSkill != null)
		{
			d += Globals.UnityValueTransform(ActorSkill.HitCD);
		}

		if (CreatureProps != null && CreatureProps.攻击速度.Value > 0)
		{
			d += CreatureProps.攻击速度.Value;
		}

		nValue /= d;

		//// 最终CD不会比最小值更小
		//if (nValue < CsvRow_CatSkill.MinCD)
		//{
		//    nValue = CsvRow_CatSkill.MinCD;
		//}

		HitCD.Value = nValue;
	}

	public void Gen持续时长()
	{
		float nValue = 1;
		if (CsvRow_CatSkill != null)
		{
			nValue *= CsvRow_CatSkill.AttackDuration;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.持续时长.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.AttackDuration);
		}

		持续时长.Value = nValue;
	}

	public void GenMinAttackDuration()
	{
		float nValue = 1;
		if (CsvRow_CatSkill != null)
		{
			nValue *= CsvRow_CatSkill.MinAttackDuration;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.MinAttackDuration.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.MinAttackDuration);
		}

		MinAttackDuration.Value = nValue;
	}

	public void Gen子弹缩放()
	{
		float nValue = 1;
		if (CsvRow_CatSkill != null)
		{
			nValue *= CsvRow_CatSkill.BulletScale;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.子弹缩放.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.BulletScale);
		}

		子弹缩放.Value = nValue;
	}

	public void Gen攻击距离()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.AttackRadius;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.攻击距离.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.AttackRadius);
		}

		攻击距离.Value = nValue;
	}

	public void Gen连射次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.ShootTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.连射次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.ShootTimes);
		}

		连射次数.Value = nValue;
	}

	public void Gen子弹速度()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.BulletSpeed;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.子弹速度.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.BulletSpeed);
		}

		if (CreatureProps != null)
		{
			nValue += CreatureProps.TotalHoist.子弹速度.Value;
			nValue *= 1 + CreatureProps.TotalHoist.子弹速度Pct.Value;
		}

		子弹速度.Value = nValue;
	}

	public void Gen环绕角速度()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.SurroundSpeed;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.环绕角速度.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.SurroundSpeed);
		}

		环绕角速度.Value = nValue;
	}

	public void Gen最大摆动次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.MaxSwingTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.最大摆动次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.MaxSwingTimes);
		}

		最大摆动次数.Value = nValue;
	}

	public void Gen爆炸半径()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.ExploseRadius;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.爆炸半径.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.ExploseRadius);
		}

		爆炸半径.Value = nValue;
	}

	public void Gen击中后爆炸的概率()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.ExplosePriority;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.击中后爆炸的概率.Value;
		}

		if (ActorSkill != null)
		{
			nValue += ActorSkill.ExplosePriority;
		}

		击中后爆炸的概率.Value = nValue;
	}

	public void Gen击杀后爆炸的概率()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.KillExplosePriority;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.击杀后爆炸的概率.Value;
		}

		if (ActorSkill != null)
		{
			nValue += ActorSkill.KillExplosePriority;
		}

		击杀后爆炸的概率.Value = nValue;
	}

	public void Gen击杀后爆炸的次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.KillExploseTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.击杀后爆炸的次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.KillExploseTimes);
		}

		击杀后爆炸的次数.Value = nValue;
	}

	public void Gen爆炸伤害系数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.ExploseCoe;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.爆炸伤害系数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.ExploseCoe);
		}

		爆炸伤害系数.Value = nValue;
	}

	public void Gen子弹最大穿透次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.MaxPenetrateTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.子弹最大穿透次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.MaxPenetrateTimes);
		}

		子弹最大穿透次数.Value = nValue;
	}

	public void Gen子弹最大反弹次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.MaxBounceTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.子弹最大反弹次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.MaxBounceTimes);
		}

		子弹最大反弹次数.Value = nValue;
	}

	public void GenMinDamageInterval()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.MinDamageInterval;
		}

		float d = 1;

		if (TotalEffect != null && TotalEffect.MinDamageInterval.Value > 0)
		{
			d += TotalEffect.MinDamageInterval.Value;
		}

		if (ActorSkill != null)
		{
			d += Globals.UnityValueTransform(ActorSkill.MinDamageInterval);
		}

		if (CreatureProps != null && CreatureProps.攻击速度.Value > 0)
		{
			d += CreatureProps.攻击速度.Value;
		}

		nValue /= d;

		//// 最终CD不会比最小值更小
		//if (nValue < CsvRow_CatSkill.MinCD)
		//{
		//    nValue = CsvRow_CatSkill.MinCD;
		//}

		MinDamageInterval.Value = nValue;
	}

	public void Gen子弹最大分裂次数()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.MaxSeparateTimes;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.子弹最大分裂次数.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.MaxSeparateTimes);
		}

		子弹最大分裂次数.Value = nValue;
	}

	public void Gen子弹时长()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.BulletLife;
		}

		if (TotalEffect != null)
		{
			nValue += TotalEffect.子弹时长.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.BulletLife);
		}

		子弹时长.Value = nValue;
	}

	//public void GenBuffIDs()
	//{
	//    BuffIDs.Clear();
	//    if (CsvRow_CatSkill != null)
	//    {
	//        BuffIDs.AddRange(CsvRow_CatSkill.BuffIDs);
	//    }
	//    if (TotalEffect != null)
	//    {
	//        BuffIDs.AddRange(TotalEffect.BuffIDs);
	//    }
	//}

	public void GenBuffPriorities()
	{
		BuffPriorities.Clear();
		if (CsvRow_CatSkill != null)
		{
			BuffPriorities.AddRange(CsvRow_CatSkill.BuffPriorities);
		}

		if (ActorSkill != null)
		{
			// 来自装备对技能提升(所有项)
			for (var i = 0; i < BuffPriorities.Count; i++)
			{
				BuffPriorities[i] += ActorSkill.BuffPriority;
			}
		}

		// 按索引位置提升(来自技能的效果提升)
		TotalEffect?.BuffPriorities.Select((p, i) =>
		{
			if (ActorSkill != null)
			{
				p += ActorSkill.BuffPriority;
			}

			if (BuffPriorities.Count <= i)
			{
				BuffPriorities.Add(p);
			}
			else
			{
				BuffPriorities[i] += p;
			}

			return p;
		}).ToList();

		//        Debug.Log($@"技能={SkillID}
		//BuffIDs={JsonConvert.SerializeObject(CsvRow_CatSkill.BuffIDs)}
		//BuffPriorities={JsonConvert.SerializeObject(BuffPriorities)}");
	}

	public void GenDamageCoe()
	{
		float nValue = 0;
		if (CsvRow_CatSkill != null)
		{
			nValue += CsvRow_CatSkill.DamageCoe;
		}

		if (TotalEffect != null)
		{
			nValue *= 1 + TotalEffect.DamageCoe.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.DamageCoe);
		}

		DamageCoe.Value = nValue;
	}

	public void GenDenyMoveTime()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.DenyMoveTime.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.DenyMoveTime);
		}

		DenyMoveTime.Value = nValue;
	}

	public void GenDenyMoveTimePct()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.DenyMoveTimePct.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.DenyMoveTimePct);
		}

		DenyMoveTimePct.Value = nValue;
	}

	public void GenHitBackTime()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.HitBackTime.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.HitBackTime);
		}

		HitBackTime.Value = nValue;
	}

	public void GenHitBackTimePct()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.HitBackTimePct.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.HitBackTimePct);
		}

		HitBackTimePct.Value = nValue;
	}

	public void GenHitBackSpeed()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.HitBackSpeed.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.HitBackSpeed);
		}

		HitBackSpeed.Value = nValue;
	}

	public void GenHitBackSpeedPct()
	{
		float nValue = 0;
		//if (CsvRow_CatSkill != null)
		//{
		//    nValue += CsvRow_CatSkill.MaxSeparateTimes;
		//}
		if (TotalEffect != null)
		{
			nValue += TotalEffect.HitBackSpeedPct.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.HitBackSpeedPct);
		}

		HitBackSpeedPct.Value = nValue;
	}

	public void GenSlowTime()
	{
		float nValue = 0;
		if (TotalEffect != null)
		{
			nValue += TotalEffect.SlowTime.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.SlowTime);
		}

		SlowTime.Value = nValue;
	}

	public void GenSlowTimePct()
	{
		float nValue = 0;
		if (TotalEffect != null)
		{
			nValue += TotalEffect.SlowTimePct.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.SlowTimePct);
		}

		SlowTimePct.Value = nValue;
	}

	public void GenSlowSpeed()
	{
		float nValue = 0;
		if (TotalEffect != null)
		{
			nValue += TotalEffect.SlowSpeed.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.SlowSpeed);
		}

		SlowSpeed.Value = nValue;
	}

	public void GenSlowSpeedPct()
	{
		float nValue = 0;
		if (TotalEffect != null)
		{
			nValue += TotalEffect.SlowSpeedPct.Value;
		}

		if (ActorSkill != null)
		{
			nValue += Globals.UnityValueTransform(ActorSkill.SlowSpeedPct);
		}

		SlowSpeedPct.Value = nValue;
	}

	#endregion

	/// <summary>
	/// 半径*缩放
	/// </summary>
	/// <returns></returns>
	public float Get子弹半径()
	{
		if (CsvRow_CatSkill.BulletRadius > 0 && 子弹缩放.Value > 0)
		{
			return CsvRow_CatSkill.BulletRadius * 子弹缩放.Value;
		}

		return 1;
	}

	/// <summary>
	/// 给技能添加提升(升级)
	/// </summary>
	/// <param name="sillEffectID"></param>
	/// <param name="unique">技能的效果是否唯一(不允许重复)</param>
	/// <remarks>已是最大等级则啥也不干</remarks>
	public SkillProps AddEffect(int sillEffectID, bool unique = false)
	{
		if (sillEffectID == 0) return this;
		if (SkillLvl >= Globals.CsvRow_CatMainStage.MaxSkillLvl) return this;
		if (unique && SkillEffects.Any(x => x.Id == sillEffectID)) return this;

		var effect = CatSkillEffectScheme.Instance.GetItem(sillEffectID);
		SkillEffects.Add(effect);
		// 升到终极形态时改变技能的基础值
		if (SkillLvl >= Globals.CsvRow_CatMainStage.MaxSkillLvl)
		{
			//+++++++++++++++++++++++终极的升级规则尚未实现
			CsvRow_CatSkill = CatSkillScheme.Instance.GetItem(CsvRow_CatSkill.FinalSkillIDs[0]);
		}

		return this;
	}

	/// <summary>
	/// 按权重随机获取一个技能的效果ID
	/// </summary>
	/// <param name="unique">是否唯一(不允许重复)</param>
	/// <returns>没有随机出结果则返回0</returns>
	public int GetRandomEffectID(bool unique = false)
	{
		// 筛选出可出的EffectID(按MustHasEffectIDs条件和MaxRepeatTimes)
		List<int> effectIDs = new();
		List<int> effectWeightings = new();
		for (int i = 0; i < CsvRow_CatSkill.EffectIDs.Length; i++)
		{
			var effectID = CsvRow_CatSkill.EffectIDs[i];
			var csvRow_CatSkillEffect = CatSkillEffectScheme.Instance.GetItem(effectID);

			// 判定每个EffectID是否可出
			bool canAppear = true;

			{
				// 等级没达到最低要求，不能出
				if (CsvRow_CatSkill.EffectPremiseActorLvls.Length > i)
				{
					var effcetPremiseActorLvl = CsvRow_CatSkill.EffectPremiseActorLvls[i];
					if (effcetPremiseActorLvl > 0 &&
						CreatureProps.ActorProp.level < CsvRow_CatSkill.EffectPremiseActorLvls[i])
					{
						canAppear = false;
					}
				}
			}

			if (canAppear)
			{
				// 前置技能未全部获得，不能出
				if (CsvRow_CatSkill.EffectPremiseSkillIDs.Count > i)
				{
					var pSkillIDs = CsvRow_CatSkill.EffectPremiseSkillIDs[i].Split('|').Select(int.Parse)
						.ToList();
					if (pSkillIDs.Any(x => x > 0 && SkillScheduler.Skills.All(s => s.SkillID != x)))
					{
						canAppear = false;
					}
				}
			}

			if (canAppear)
			{
				// 已拥有最多次数，不能出
				if (csvRow_CatSkillEffect.MaxRepeatTimes > 0)
				{
					if (SkillEffects.Where(x => x.Id == effectID).Count() >= csvRow_CatSkillEffect.MaxRepeatTimes)
					{
						canAppear = false;
					}
				}
			}

			if (canAppear)
			{
				// 未获得前置EffectID，不能出
				if (PbHelper.HasListItem(csvRow_CatSkillEffect.MustHasEffectIDs))
				{
					csvRow_CatSkillEffect.MustHasEffectIDs.ToList().ForEach(id =>
					{
						if (!SkillEffects.Any(x => x.Id == id))
						{
							canAppear = false;
						}
					});
				}
			}

			if (canAppear)
			{
				effectIDs.Add(effectID);
				effectWeightings.Add(CsvRow_CatSkill.EffectWeightings[i]);
			}
		}


		var range = RandomWithWeighting<int>.GenList(effectIDs, effectWeightings);
		Func<WeightingItem<int>, bool> predicate = null;
		if (unique)
		{
			// 不允许重复则只能获得尚未拥有的效果
			predicate = w => !SkillEffects.Any(x => x.Id == w.Value);
		}

		var rndResult = RandomWithWeighting<int>.GetOne(range, predicate);

		if (rndResult != null)
		{
			return rndResult.Value;
		}

		return 0;
	}

	/// <summary>
	/// 按权重随机获取一个技能的效果ID(用于升级选择,尽量不重复)
	/// </summary>
	public void FillRandomEffectIDs(int[] rtn)
	{
		for (var i = 0; i < rtn.Length; i++)
		{
			var MaxRetryTimes = 100;
			var ReTryTimes = 0;

			var effectID = GetRandomEffectID();

			for (; ReTryTimes < MaxRetryTimes; ReTryTimes++, effectID = GetRandomEffectID())
			{
				if (rtn.Where(x => x > 0).All(x => x != effectID))
				{
					break;
				}
			}
			rtn[i] = effectID;
		}
	}

	/// <summary>
	/// 技能升至顶级(或不能再升)，随机效果
	/// </summary>
	/// <param name="unique">技能的效果是否唯一(不允许重复)</param>
	public SkillProps Upgrade_MaxByRandom(bool unique = false)
	{
		bool isUp = false;
		for (var (curLvl, preLvl) = (SkillLvl, SkillLvl - 1);
			 curLvl > preLvl && SkillLvl < Globals.CsvRow_CatMainStage.MaxSkillLvl;
			 preLvl = curLvl, curLvl = SkillLvl)
		{
			Upgrade_RandomEffect(unique);
			if (SkillLvl > curLvl) isUp = true;
		}

		if (isUp && SkillScheduler != null)
		{
			SkillScheduler.FireAfterSkillUp(this);
		}

		//Debug.Log($"技能[{SkillID}]已升至顶级:{SkillLvl} {CsvRow_CatSkill.Id}");

		return this;
	}

	/// <summary>
	/// 随机升一级
	/// </summary>
	/// <param name="unique">技能的效果是否唯一(不允许重复)</param>
	/// <remarks>已是最大等级则啥也不干</remarks>
	public SkillProps Upgrade_RandomEffect(bool unique = false)
	{
		if (SkillLvl >= Globals.CsvRow_CatMainStage.MaxSkillLvl) return this;

		var range = RandomWithWeighting<int>.GenList(CsvRow_CatSkill.EffectIDs, CsvRow_CatSkill.EffectWeightings);
		Func<WeightingItem<int>, bool> predicate = null;
		if (unique)
		{
			// 不允许重复则只能获得尚未拥有的效果
			predicate = w => SkillEffects.All(x => x.Id != w.Value);
		}

		var effectID = RandomWithWeighting<int>.GetOne(range, predicate);

		if (effectID != null)
		{
			AddEffect(effectID.Value, unique);
		}

		return this;
	}

	/// <summary>
	/// 按概率抽取技能附带的Buff
	/// </summary>
	public IList<int> PickBuffIDs_ByPriority()
	{
		List<int> lst = new();
		for (int i = 0; i < CsvRow_CatSkill.BuffIDs.Length; i++)
		{
			if (BuffPriorities.Count > i && RandomNum.RandomInt(0, 10000) < BuffPriorities[i])
			{
				lst.Add(CsvRow_CatSkill.BuffIDs[i]);
			}
		}

		return lst;
	}
}