using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using IFix;
using UnityEngine;

[Configure]
public class FixConfig
{
    private static List<Type> fixableTypes;

    [IFix]
    static IEnumerable<Type> Fix
    {
        get
        {
            if (fixableTypes == null)
            {
                fixableTypes = GetFixableTypes().ToList();
                //添加GameGlobal
                //fixableTypes.Add(typeof(GameGlobal));
            }

            return fixableTypes;
            //return new List<Type>() { 
            //    typeof(LuaToCshapeManager) 
            //};
        }
    }

    static IEnumerable<Type> GetFixableTypes()
    {
        Assembly[] assemblies = AppDomain.CurrentDomain.GetAssemblies();

        Assembly assemblyCSharp = assemblies.FirstOrDefault(assembly => assembly.GetName().Name == "Assembly-CSharp");

        if (assemblyCSharp == null)
        {
            // 如果找不到 Assembly-CSharp，可能需要根据具体情况进行错误处理
            return Enumerable.Empty<Type>();
        }

        Type[] allTypes = assemblyCSharp.GetTypes();
        foreach(Type t in allTypes)
        {
            Debug.Log("获取到的type：" + t.Name);
        }

        // 返回所有类
        return allTypes;
    }
}
