﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Apq
{
    public class Cast
    {
        #region TryChangeType
#line hidden   //pdb中排除:阻止调试进入
        /// <summary>
        /// 通用类型转换,失败时返回 default(T)
        /// </summary>
        /// <typeparam name="T">输出类型</typeparam>
        /// <param name="obj">原始对象</param>
        public static T TryChangeType<T>(object obj) => TryChangeType(obj, default(T));

        /// <summary>
        /// 通用类型转换,失败时返回 fValue
        /// </summary>
        /// <typeparam name="T">输出类型</typeparam>
        /// <param name="obj">原始对象</param>
        /// <param name="fValue">转换失败时返回的值</param>
        public static T TryChangeType<T>(object obj, T fValue)
        {
            var (rtn, _) = ChangeType<T>(obj, fValue);
            return rtn;
        }

        /// <summary>
        /// 通用类型转换,失败时返回失败值。注意：泛型只支持一个形参，主要是为了Nullable&lt;T&gt;。
        /// </summary>
        /// <param name="obj">原始对象</param>
        /// <param name="type">输出类型</param>
        /// <param name="fValue">失败值(匹配优先级比上面的泛型方法低,因此不能加参数默认值)</param>
        public static object TryChangeType(object obj, Type type, object fValue)
        {
            var (rtn, _) = ChangeType(obj, type, fValue);
            return rtn;
        }
        #endregion

        #region ChangeType
        /// <summary>
        /// 通用类型转换,失败时返回 default(T)
        /// </summary>
        /// <typeparam name="T">输出类型</typeparam>
        /// <param name="obj">原始对象</param>
        public static (T, bool) ChangeType<T>(object obj) => ChangeType(obj, default(T));

        /// <summary>
        /// 通用类型转换,失败时返回 fValue
        /// </summary>
        /// <typeparam name="T">输出类型</typeparam>
        /// <param name="obj">原始对象</param>
        /// <param name="fValue">转换失败时返回的值</param>
        public static (T, bool) ChangeType<T>(object obj, T fValue)
        {
            // 先尝试强制类型转换(必须)
            try
            {
                return ((T)obj, true);
            }
            catch { }
            // 失败后使用通用类型转换后强制类型转换
            try
            {
                var (rtn, s) = ChangeType(obj, typeof(T), fValue);
                return ((T)rtn, s);
            }
            catch
            {
                // 强制类型转换失败则返回fValue
                return (fValue, false);
            }
        }

        /// <summary>
        /// 通用类型转换,失败时返回失败值。注意：泛型只支持一个形参，主要是为了Nullable&lt;T&gt;。
        /// </summary>
        /// <param name="obj">原始对象</param>
        /// <param name="type">输出类型</param>
        /// <param name="fValue">失败值(匹配优先级比上面的泛型方法低,因此不能加参数默认值)</param>
        /// <returns>转换结果和是否转换成功</returns>
        /// <remarks>http://www.cnblogs.com/youring2/archive/2012/07/26/2610035.html</remarks>
        public static (object, bool) ChangeType(object obj, Type type, object fValue)
        {
            try
            {
                switch (obj)
                {
                    case null when type.IsGenericType:
                        return (Activator.CreateInstance(type), true);
                    case null:
                        return (null, true);
                }

                if (type == obj.GetType()) return (obj, true);
                if (type.IsEnum)
                {
                    return (obj is string s ? Enum.Parse(type, s) : Enum.ToObject(type, obj), true);
                }
                if (!type.IsInterface && type.IsGenericType)
                {
                    var innerType = type.GetGenericArguments()[0];
                    var innerobj = TryChangeType(obj, innerType, null);
                    return (Activator.CreateInstance(type, new object[] { innerobj }), true);
                }
                switch (obj)
                {
                    case string s when type == typeof(Guid):
                        return (new Guid(s), true);
                    case string s when type == typeof(Version):
                        return (new Version(s), true);
                }

                return (!(obj is IConvertible) ? obj : Convert.ChangeType(obj, type), true);
            }
            catch
            {
                return (fValue, false);
            }
        }
#line default
        #endregion

        #region ToExcelObject
        /// <summary>
        /// 任意值转换到 Excel 能接受的值
        /// </summary>
        public static object ToExcelObject(object obj)
        {
            switch (obj)
            {
                case DBNull _:
                    return string.Empty;
                case bool _:
                case byte _:
                    {
                        var n = Convert.ToInt32(obj);
                        return ToExcelObject(n);
                    }
                case Guid _:
                    return obj.ToString();
                case byte[] aryBytes:
                    return "0x" + BytesToHexString(aryBytes);
                default:
                    return obj;
            }

        }
        #endregion

        #region BytesToHexString
        /// <summary>
        /// 将字节串转换为16进制字符串
        /// </summary>
        public static string BytesToHexString(IEnumerable<byte> input)
        {
            var sb = new StringBuilder();
            if (input == null) return sb.ToString();
            var tor = input.GetEnumerator();
            while (tor.MoveNext())
            {
                sb.Append(tor.Current.ToString("X2"));
            }
            return sb.ToString();
        }
        #endregion

        #region HexStringToBytes
        /// <summary>
        /// 将16进制字符串转换为字节串
        /// </summary>
        public static byte[] HexStringToBytes(string input)
        {
            // 去掉0x前缀
            while (input.StartsWith("0x"))
            {
                input = input[2..];
            }

            if (input.Length <= 1) return null;
            var lst = new byte[input.Length / 2];
            for (int i = 0, j = 2; j <= input.Length; i += 2, j += 2)
            {
                var str = input.Substring(i, 2);
                lst[i / 2] = Convert.ToByte(str, 16);
            }
            return lst;
        }
        /// <summary>
        /// 将16进制字符串转换为字节串
        /// </summary>
        public static IList<byte> HexStringToBytesList(string input)
        {
            // 去掉0x前缀
            while (input.StartsWith("0x"))
            {
                input = input[2..];
            }

            var lst = new List<byte>(input.Length / 2);
            for (var i = 2; i <= input.Length; i += 2)
            {
                var str = input.Substring(i - 2, 2);
                lst.Add(Convert.ToByte(str, 16));
            }
            return lst;
        }
        #endregion
    }
}
