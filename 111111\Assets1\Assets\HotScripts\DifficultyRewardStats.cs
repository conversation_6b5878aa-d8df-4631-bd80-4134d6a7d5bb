using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class DifficultyRewardStats : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI xpLabel;
    [SerializeField] private TextMesh<PERSON><PERSON>UGUI coinsLabel;
    private int currentDifficulty = 0;
    private bool showImage = false;

    public void Create(int difficultyType, int coins, int xp)
    {
        Init(difficultyType, coins, xp);
    }
    private void Init(int difficultyType, int coins, int xp)
    {
       
        //coinsSprite.setName("c");
        //if (GameData.instance.getInstance().getCurrentLanguageCode() == "ar")
        //{
        //    fontSize = 30;
        //}
        coinsLabel.text = coins.ToString();
        xpLabel.text = xp.ToString();
        currentDifficulty = difficultyType - 1;
        
#if UNITY_STANDALONE
    //Shared::fontToCustom(xpLabel);
    //Shared::fontToCustom(coinsLabel);
#endif
    }

}
