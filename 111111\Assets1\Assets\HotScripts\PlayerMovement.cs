﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerMovement : MonoBehaviour
{
    [HideInInspector] public bool inDash = false, allowDash = true;
    [HideInInspector] public float playerDir;
    [HideInInspector] public float rotationDir;
    [HideInInspector] public Vector2 rightStickVector;

    public FloatingJoystick virtualJoystick, rightJoystick;
    
    private bool seeMouse = false;
    /// <summary>
    /// 是否通过模拟手柄操作
    /// </summary>
    private bool semiAssist = false;

    PlayerController player;
    PlayerInputActions playerInputActions;
    [HideInInspector] public InputAction movement;
    Camera mainCamera;
    Transform planeTransform;
    Vector2 acceleration = Vector2.zero, movementVector;
    bool gotMovementInput, isInWater = false, enableRotation;
    float maxSpeed, dashSpeed, speedFromPlist, turnSpeedFromPList, thrustFromPList;

    [HideInInspector] public bool scheduleUpdate;
    [HideInInspector] public float assistDirAngle;
    //Vector3 tempValue;
    public bool GotMovementInput
    {
        get
        {
            return gotMovementInput;
        }
    }

    private void Awake()
    {
        player = GetComponent<PlayerController>();
        playerInputActions = new PlayerInputActions();
        mainCamera = Camera.main;

        scheduleUpdate = true;
        enableRotation = true;
        Globals.resetControls = false;
    }

    // Start is called before the first frame update
    void Start()
    {
        planeTransform = player.skeletonAnimationTran;
        Globals.openMoveBuff = PlayerPrefs.GetInt("SYSTEM_MOVE_BUFF", 0) == 1;
        maxSpeed = Globals.CocosToUnity(Globals.SPEEDLIMIT) / 1.25f;
        dashSpeed = Globals.CocosToUnity(Globals.DASH_DISTANCE) * 2;
        seeMouse = !Globals.mobileControls;
        Globals.isAssistMode = true;//自动模式写死
        semiAssist = !Globals.isAssistMode && Globals.mobileControls;
        //semiAssist = true; // REMOVE TODO
        GameManager.instance.SetShootButton(!semiAssist);
        int planeIndex = PlayerPrefs.GetInt("Category3") + 1;
        PList planeData = GameData.instance.GetShop()["Category3"] as PList;
        speedFromPlist = System.Convert.ToSingle(((((planeData["Gun" + planeIndex.ToString()] as PList)["Stats"] as PList)["stat1"] as PList)
            ["Level"] as PList)["L1"]) / 2f;
        turnSpeedFromPList = System.Convert.ToSingle(((((planeData["Gun" + planeIndex.ToString()] as PList)["Stats"] as PList)["stat3"] as PList)
            ["Level"] as PList)["L1"]) / 2f;
        thrustFromPList = System.Convert.ToSingle(((((planeData["Gun" + planeIndex.ToString()] as PList)["Stats"] as PList)["stat2"] as PList)
            ["Level"] as PList)["L1"]) / 2f;
    }

    private void OnEnable()
    {
        movement = playerInputActions.Player.Movement;
        movement.Enable(); // Input action doesn't work if it isn't enabled
    }

    private void OnDisable()
    {
        movement.Disable();
    }

    public Vector2 GetAcceleration() => acceleration;

    public void SetAcceleration(Vector2 accel)
    {
        acceleration = accel;
    }

    void AddAcceleration(float dir, float speed)
    {
        if (Globals.resetControls)
        {
            acceleration = Vector2.zero;
            return;
        }

        speed *= 1.5f * (0.5f + thrustFromPList);
        acceleration += new Vector2(speed * Mathf.Cos(Mathf.Deg2Rad * dir), speed * Mathf.Sin(Mathf.Deg2Rad * dir)) * (Time.deltaTime * 5);

        //acceleration = new Vector2(Mathf.Clamp(acceleration.x, -speed, speed), Mathf.Clamp(acceleration.y, -speed, speed));
    }

    // Update is called once per frame
    void Update()
    {
        if (!scheduleUpdate)
            return;

        if (player == null)
        {
            return;
        }
        if (player.isWhiteCatType)
        {
            return;
        }

        Vector2 a = virtualJoystick == null ? Vector2.zero : virtualJoystick.Value;
        Vector2 b = movement.ReadValue<Vector2>();


        movementVector = Globals.resetControls || Globals.lockPlayerMovementForTutorial ? Vector2.zero
            : (!seeMouse ? a : b);

        float dir = Vector2.SignedAngle(Vector2.right, movementVector);
        rotationDir = dir < 0 ? 360 + dir : dir;

        float mouseDir = Vector2.SignedAngle(Vector2.right,
            Input.mousePosition - mainCamera.WorldToScreenPoint(planeTransform.position));
        mouseDir = mouseDir < 0 ? 360 + mouseDir : mouseDir;

        float assistDir = !seeMouse ? GetAssistDirection() : -1;
        assistDirAngle = assistDir;
        if (movementVector.magnitude > 0)
        {
            if (!inDash) AddAcceleration(rotationDir, maxSpeed); // To ensure that acceleration is only applied via PerformDash function when in Dash mode

            if (assistDir == -1 && !seeMouse)
            {
                Debug.Log("!seeMouse assistDir=" + assistDir);
                RotatePlayer(rotationDir);
            }

            gotMovementInput = true;
        }
        else
        {
            gotMovementInput = false;
        }
        //移动缓冲开关
        if (!Globals.openMoveBuff && !inDash)
        {
            acceleration = movementVector / 8;
        }
        LimitSpeed();
        if (double.IsNaN(acceleration.x) || double.IsNaN(acceleration.y))
        {
            acceleration = Vector2.zero;
        }
        CheckBoundaries();

        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            float xRatio = 1 / (1 + (Time.deltaTime * 2));
            acceleration *= xRatio;
        }

        if (seeMouse && enableRotation)
        {
            //Debug.Log("mouseDir=" + mouseDir);
            RotatePlayer(mouseDir);
        }
        else if (assistDir != -1 && enableRotation)
        {
            //Debug.Log("assistDir=" + assistDir);
            RotatePlayer(assistDir);
        }
        else if (assistDir == -1 && Globals.resetControls)
        {
            RotatePlayer(0);
        }
        //tempValue = acceleration * Time.deltaTime * 60 * player.Stats.speed;

        //if (float.IsNaN(tempValue.x) || float.IsNaN(tempValue.y) || float.IsNaN(tempValue.z))
        //{
        //    tempValue = Vector3.one;
        //}
        //transform.Translate(tempValue);

        if (transform.position.y - 0.2f <= Globals.LOWERBOUNDARY && !isInWater && acceleration.y < 0)
        {
            float intensity = acceleration.y / (-maxSpeed * 1.5f);
            intensity = Mathf.Clamp(intensity, 0, 1.2f);
            isInWater = true;
            GameManager.instance.HitWater(transform.position, intensity);
            if (Globals.AllowMusic)
            {
                //if (player.Stats.speed < 2)
                //{
                //    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.splashSlow);
                //}
                //else if (player.Stats.speed < 4)
                //{
                //    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.splashMedium);
                //}
                //else
                //{
                //    AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.splashFast);
                //}

            }
        }

        if (transform.position.y - 0.2f > Globals.LOWERBOUNDARY && isInWater)
        {
            isInWater = false;
        }
    }

    /// <summary>
    /// 在攻击距离内查找一个怪作为攻击方向
    /// </summary>
    /// <returns>找到的怪在角色的哪个角度([0,360),-1表示没有怪)</returns>
    float GetAssistDirection()
    {
        Enemy enemy = null;
        float rightStickDir = Vector2.SignedAngle(Vector2.right, Vector2.zero);
        rightStickDir = rightStickDir < 0 ? 360 + rightStickDir : rightStickDir;
        float angle = -1;
        //rotationDir = angle == -1 ? rotationDir : angle;
        float count = LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.攻击距离);
        float percent = LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.攻击距离);
        float closestDistance = (Globals.playerAssistDirection + count) * (1 + percent / 10000f);
        int listCount = GameSharedData.Instance.enemyList.Count;

        if (!player.IsShooting) return -1;

        float recordClosestDistance = 10000f;
        for (int i = 0; i < listCount; i++)
        {
            var enemyList = GameSharedData.Instance.enemyList;

            if (enemyList[i] && enemyList[i].healthBar)
            {
                if (i >= enemyList.Count)
                    break;

                var enemyPositionVector = (Vector2)enemyList[i].transform.position + enemyList[i].offset;
                var distance = Vector2.Distance(enemyPositionVector, transform.position);

                if (distance < closestDistance)
                {
                    if (semiAssist)
                    {
                        float playerFieldAngle = Vector2.SignedAngle(
                            new Vector2(Mathf.Cos(rightStickDir * Mathf.Deg2Rad),
                            Mathf.Sin(rightStickDir * Mathf.Deg2Rad)),
                            enemyPositionVector - (Vector2)transform.position);

                        // 不攻击 60度扇形范围外 的怪
                        if (!((playerFieldAngle >= 0 && playerFieldAngle <= 30)
                            || (playerFieldAngle < 0 && playerFieldAngle >= -30)))
                            continue;
                    }

                    if (distance < recordClosestDistance)
                    {
                        enemy = enemyList[i];
                        recordClosestDistance = distance;
                    }
                    
                    //break;
                }
            }
        }

        if (enemy)
        {
            Vector2 directionVector = ((Vector2)enemy.transform.position + enemy.offset) - (Vector2)transform.position;
            float dir = Vector2.SignedAngle(Vector2.right, directionVector);
            angle = dir < 0 ? 360 + dir : dir;
        }

        return angle;
    }

    public void PlayerDash()
    {
        if (Globals.resetControls || Globals.lockPlayerMovementForTutorial || player.isWhiteCatType)
            return;


        Vector2 a;
        float dirResult;
        if (seeMouse)
        {
            a = movement.ReadValue<Vector2>();
        }
        else
        {
            a = virtualJoystick.Value;
        }

        if (a.x == 0 && a.y == 0)
        {
            dirResult = playerDir;
        }
        else
        {
            dirResult = rotationDir;
        }
        StartCoroutine(PerformDash(dirResult));
    }

    IEnumerator PerformDash(float dir)
    {
        //dir = playerDir;
        inDash = true;
        allowDash = false;
        player.canHit = false;

        player.SetPlayerAnimation(PlayerController.PlayerAnimation.Dash);

        acceleration = new Vector2(dashSpeed * Mathf.Cos(Mathf.Deg2Rad * dir), dashSpeed * Mathf.Sin(Mathf.Deg2Rad * dir))
            * 0.5f;


        yield return new WaitForSeconds(0.1f);

        inDash = false;

        yield return new WaitForSeconds(0.4f);

        player.canHit = true;

        if (player.weapon.isShooting)
        {
            player.SetPlayerAnimation(PlayerController.PlayerAnimation.Shoot);
        }
        else
        {
            if (gotMovementInput)
            {
                player.SetPlayerAnimation(PlayerController.PlayerAnimation.Flying);
            }
            else
            {
                player.SetPlayerAnimation(PlayerController.PlayerAnimation.Idle);
            }
        }

        yield return new WaitForSeconds(1f);

        if (player.weapon.isShooting)
        {
            player.SetState<PlayerShoot>();
        }
        else
        {
            if (gotMovementInput)
            {
                player.SetState<PlayerFlying>();
            }
            else
            {
                player.SetState<PlayerIdle>();
            }
        }

        allowDash = true;
    }


    public void RotatePlayer(float dir)
    {
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_DEATHDROP ||
            player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE ||
            player.isWhiteCatType)
            return;

        if (Globals.resetControls)
            dir = 0;

        playerDir = MoveAngleTowards(playerDir, dir, Time.deltaTime * 500 * player.Stats.turnSpeed
            * (turnSpeedFromPList + 0.5f));

        var rot = Quaternion.AngleAxis(playerDir, Vector3.forward);

        planeTransform.rotation = rot;

        CheckRotation(playerDir);
    }

    public void SetPlayerRotation(float dir)
    {
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_DEATHDROP ||
            player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE)
            return;

        if (Globals.resetControls)
            dir = 0;
        playerDir = dir;

        var rot = Quaternion.AngleAxis(playerDir, Vector3.forward);

        planeTransform.rotation = rot;

        CheckRotation(playerDir);
    }

    void CheckRotation(float dir)
    {
        float scale = planeTransform.localScale.y;

        if (dir > 270 || dir < 90)
        {
            if (scale == -1)
            {
                player.SetState<PlayerFlip>();
            }
        }
        else
        {
            if (scale == 1)
            {
                player.SetState<PlayerFlip>();
            }
        }
    }

    float MoveAngleTowards(float current, float target, float maxDistanceDelta)
    {
        if (current == target)
            return current;

        float angle1 = Mathf.Abs(target - current);
        float angle2 = 360 - angle1;
        float sign = Mathf.Sign(target - current) > 0 ? (angle1 > angle2 ? -1 : 1) : (angle1 > angle2 ? 1 : -1);
        float result = current + maxDistanceDelta * sign;

        result = Mathf.Abs(result - current) > Mathf.Min(angle1, angle2) ? current + Mathf.Min(angle1, angle2) * sign
            : result;

        result = result % 360;

        if (result < 0)
            result = 360 + result;

        //result = sign > 0 ? (result > target ? target : result) : (result < target ? target : result);


        return result;
    }

    void CheckBoundaries()
    {
        var playerPosY = transform.position.y;
        var playerPosX = transform.position.x;

        var accY = acceleration.y;
        var accX = acceleration.x;

        if (Globals.GROUND_ENABLED)
        {
            if (playerPosY < Globals.LOWERBOUNDARY - Globals.CocosToUnity(20))
            {
                transform.position = new Vector3(transform.position.x,
                    Globals.LOWERBOUNDARY - Globals.CocosToUnity(20),
                    transform.position.z);
                if (accY < 0)
                {
                    accY = accY / 2;
                }
            }
        }

        if (playerPosY < Globals.LOWERBOUNDARY - Globals.CocosToUnity(50))
        {
            accY = Mathf.Clamp(accY, Globals.CocosToUnity(-10), Globals.CocosToUnity(4.5f));
            accY = accY + Globals.GRAVITY * 5 * Time.deltaTime * 8;
            accY += Globals.CocosToUnity(1.1f) * Time.deltaTime * 60;

            if (playerPosY < Globals.LOWERBOUNDARY - Globals.CocosToUnity(200))
            {

                accY = Mathf.Clamp(accY, Globals.CocosToUnity(-3), Globals.CocosToUnity(6));
                accY = accY + Globals.GRAVITY * 5 * Time.deltaTime * 10;
                accY += Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;
            }
        }

        if (playerPosY > Globals.UPPERBOUNDARY + Globals.CocosToUnity(50))
        {
            accY = Mathf.Clamp(accY, Globals.CocosToUnity(-4.5f), Globals.CocosToUnity(10));
            accY = accY + -(Globals.GRAVITY * 5) * Time.deltaTime * 8;

            if (playerPosY > Globals.UPPERBOUNDARY + Globals.CocosToUnity(100))
            {

                transform.position = new Vector3(transform.position.x, Globals.UPPERBOUNDARY + Globals.CocosToUnity(100),
                    transform.position.z);
            }
            accY -= Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;
        }

        if (playerPosX < Globals.LEFTBOUNDARY - Globals.CocosToUnity(225))
        {
            accX = Mathf.Clamp(accX, Globals.CocosToUnity(-10), Globals.CocosToUnity(4.5f));
            accX = accX + (Globals.GRAVITY * 5) * Time.deltaTime * 8;
            accX += Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;

            if (playerPosX < Globals.LEFTBOUNDARY - Globals.CocosToUnity(250))
            {

                transform.position = new Vector3(Globals.LEFTBOUNDARY - Globals.CocosToUnity(300), transform.position.y,
                    transform.position.z);
                accX = Mathf.Clamp(accY, Globals.CocosToUnity(-3), Globals.CocosToUnity(6));
                accX = accX + (Globals.GRAVITY * 5) * Time.deltaTime * 60;
                accX += Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;
                transform.position = new Vector3(Globals.LEFTBOUNDARY - Globals.CocosToUnity(250), transform.position.y,
                    transform.position.z);

            }

        }

        if (playerPosX > Globals.RIGHTBOUNDARY + Globals.CocosToUnity(225))
        {
            accX = Mathf.Clamp(accX, Globals.CocosToUnity(-10), Globals.CocosToUnity(4.5f));
            accX = accX - (Globals.GRAVITY * 5) * Time.deltaTime * 8;
            accX -= Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;

            if (playerPosX > Globals.RIGHTBOUNDARY + Globals.CocosToUnity(250))
            {
                transform.position = new Vector3(Globals.RIGHTBOUNDARY + Globals.CocosToUnity(250), transform.position.y,
                    transform.position.z);

                accX = Mathf.Clamp(accY, Globals.CocosToUnity(-3), Globals.CocosToUnity(6));
                accX = accX - (Globals.GRAVITY * 5) * Time.deltaTime * 60;
                accX -= Globals.CocosToUnity(1.4f) * Time.deltaTime * 60;
            }
        }

        acceleration = new Vector2(accX, accY);
    }

    void LimitSpeed()
    {
        if (!gotMovementInput && !player.IsShooting)
            return;

        float maximumSpeed = maxSpeed * player.Stats.speed * (0.5f + speedFromPlist) * movementVector.magnitude;
        float deltaMultiplier = 7.5f * Time.deltaTime;
        float accX = acceleration.x, accY = acceleration.y;
        if (acceleration.x > maximumSpeed / 1.25f)
        {
            accX += (-accX + maximumSpeed / 1.25f) * deltaMultiplier;
        }
        if (acceleration.x < -maximumSpeed / 1.25f)
        {
            accX += (-accX + maximumSpeed / -1.25f) * deltaMultiplier;
        }

        if (acceleration.y > maximumSpeed / 1.25f)
        {
            accY += (-accY + maximumSpeed / 1.25f) * deltaMultiplier;
        }
        if (acceleration.y < -maximumSpeed / 1.25f)
        {
            accY += (-accY + maximumSpeed / -1.25f) * deltaMultiplier;
        }

        acceleration = new Vector2(accX, accY);
    }

    public void PushBack(float intensity)
    {
        if (Globals.isGameInTutorialState)
            return;
        float dir = (playerDir + 180) % 360;
        intensity /= (Time.deltaTime * 60);

        AddAcceleration(dir, gotMovementInput ? intensity / 2 : intensity);
    }

    public void KnockBack(float intensity, float dir)
    {
        AddAcceleration(dir, intensity);
    }

    public void SetSeeMouse(bool val)
    {
        enableRotation = val;
    }
}
