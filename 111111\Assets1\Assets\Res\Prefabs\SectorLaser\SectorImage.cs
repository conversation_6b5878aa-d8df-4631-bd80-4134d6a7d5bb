using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SectorImage : MonoBehaviour
{
    private string lowLaserIcon = "laser2031";
    private string hightLaserIcon = "laser2036";
    public Texture lowTexture;
    public Texture hightTexture;
    private Dictionary<string, Texture> imageList = new Dictionary<string, Texture>();

    public void Init()
    {
        if (!imageList.ContainsKey(lowLaserIcon))
        {
            imageList.Add(lowLaserIcon, lowTexture);
        }
        if (!imageList.ContainsKey(hightLaserIcon))
        {
            imageList.Add(hightLaserIcon, hightTexture);
        }
    }
    public Texture GetLaserIcon(int nextLevel)
    {
        string iconName = nextLevel == 0 ? hightLaserIcon : lowLaserIcon;
        if (imageList.ContainsKey(iconName))
        {
            return imageList[iconName];
        }
        return null;
    }
}
