using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
public class LaserBoundary : Enemy
{

    [SerializeField] private SkeletonAnimation waterExplosion;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        allowRelocate = false;

        transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1400), Globals.CocosToUnity(-2000));
        Observer.RegisterCustomEvent(gameObject, Globals.ACTIVE_LASER_EVENT, () => {
            Activate();
        });

        Observer.RegisterCustomEvent(gameObject, Globals.DEACTIVE_LASER_EVENT, () => {
            enemySprite.state.SetAnimation(0, "end", true);
            scheduleUpdate = false;

        });
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            if (player.transform.position.x < enemySprite.transform.position.x + Globals.CocosToUnity(350) && player.transform.position.x > enemySprite.transform.position.x - Globals.CocosToUnity(350))
            {
                player.GotHit(stats.bulletDamage, false);
            }


            if (player.transform.position.x < enemySprite.transform.position.x + Globals.CocosToUnity(100) && player.transform.position.x > enemySprite.transform.position.x - Globals.CocosToUnity(100))
            {
                player.GotHit(stats.bulletDamage * 10, false);
            }
        }
    }

    public void Activate()
    {
        DOTween.Sequence().Append(transform.DOMove(new Vector2(transform.position.x, Globals.LOWERBOUNDARY), 0.35f)).Play();
        waterExplosion.gameObject.SetActive(true);
        waterExplosion.state.SetAnimation(0, "bg" + (Globals.g_bgType-1), false);
        DOTween.Sequence().AppendInterval(0.5f).AppendCallback(()=>{waterExplosion.gameObject.SetActive(false); }).Play();

        DOTween.Sequence().AppendInterval(0.75f).AppendCallback(()=>{
            scheduleUpdate = true;
            enemySprite.state.SetAnimation(0, "boundaryTower", true);

        }).Play();
    }

    public void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        stats.speed = baseStats.speed = 2;
        stats.health = baseStats.health = (player.Stats.maxHealth.Value + 20) + Globals.difficulty * 5;
        stats.turnSpeed = baseStats.turnSpeed = 2;
        stats.bulletDamage = baseStats.bulletDamage = 5;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50;
        stats.coinAwarded = baseStats.coinAwarded = 10;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
    }
}
