using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;
using DG.Tweening;

public class Powerup : MonoBehaviour
{
    public enum PowerupType {
        PowerUpMechanic = 0,
        PowerUpRocketeer = 1,
        PowerUpGunKitty = 2,
        PowerUpLaser = 3,
        PowerUpMulticanon = 4,
        PowerupMachineGun = 5,
        PowerUpCrazyRockets = 6,
        NumberOfPowerUps = 7
    }

    public PowerupType type;

    [SerializeField] private SkeletonAnimation powerUpSkeleton, effectSkeleton;
    [SerializeField] private Collider2D boundsCollider;
    private PlayerController player;
    private bool isPowerupCollect;
    string tweenID;

    Bounds bounds;

    private void Start()
    {
        tweenID = "Powerup" + gameObject.GetInstanceID();
        Init();
    }

    void Init()
    {
        player = GameManager.instance.player;

        transform.position = new Vector3(player.transform.position.x + (-Globals.CocosToUnity(750) + Random.value * Globals.CocosToUnity(1500)),
            Globals.UPPERBOUNDARY + Globals.CocosToUnity(250));
        DOTween.Sequence().SetId(tweenID)
            .Append(transform.DOBlendableMoveBy(new Vector3(transform.position.x, Globals.LOWERBOUNDARY - 5), 20))
            .AppendCallback(EndPowerUp);
        powerUpSkeleton.state.SetAnimation(0, "powerUp", true);
        powerUpSkeleton.transform.SetScale(0.34f);
        type = (PowerupType)(Random.Range(0, int.MaxValue) % (int)PowerupType.NumberOfPowerUps);
        isPowerupCollect = false;
        effectSkeleton.gameObject.SetActive(false);
        powerUpSkeleton.gameObject.SetActive(true);

        var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        ping.Init(transform, true, PlayerPing.Type.Powerup);


        if (transform.position.x < Globals.LEFTBOUNDARY)
        {
            transform.SetWorldPositionX(Globals.LEFTBOUNDARY + Globals.CocosToUnity(200));
        }

        if (transform.position.x > Globals.RIGHTBOUNDARY)
        {
            transform.SetWorldPositionX(Globals.RIGHTBOUNDARY - Globals.CocosToUnity(200));
        }

        DOTween.Sequence().SetId(tweenID).AppendInterval(0.33f).AppendCallback(PowerupUpdate).SetLoops(-1);
    }

    void Collected()
    {
        if (isPowerupCollect)
        {
            return;
        }

        isPowerupCollect = true;

        transform.DOKill();
        DOTween.Kill(tweenID);
        powerUpSkeleton.gameObject.SetActive(false);
        effectSkeleton.gameObject.SetActive(true);

        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.powerUpCollected);
        effectSkeleton.state.SetAnimation(0, "powerUp", false);

        if (Globals.zoomInForSec != 0)
        {
            effectSkeleton.timeScale = 3;
        }

        effectSkeleton.transform.SetScale(2);

        DOTween.Sequence().AppendInterval(0.75f).AppendCallback(EndPowerUp);
    }

    void PowerupUpdate()
    {
        bounds = boundsCollider.bounds;

        if (bounds.Contains(player.transform.position))
        {
            if (!isPowerupCollect)
            {
                Collected();
                player.playerPowerUp.Collected();
                return;
            }
        }
    }

    void EndPowerUp()
    {
        transform.DOKill();
        DOTween.Kill(tweenID);
        Destroy(gameObject);
    }
}
