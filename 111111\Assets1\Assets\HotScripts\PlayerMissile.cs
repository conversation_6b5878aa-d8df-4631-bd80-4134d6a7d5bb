using DG.Tweening;
using System.Collections;

using UnityEngine;

public class PlayerMissile : MonoBehaviour
{
    float myAngle;
    [HideInInspector] public double damage;
    [HideInInspector] public double damageExplosionAttack;//爆炸伤害
    [HideInInspector] public bool isHoming, isInUse = false;
    [HideInInspector] public bool reactToWater = true;
    [HideInInspector] public float initSpeed = 60;
    [HideInInspector] public float curSpeed = 0;
    [HideInInspector] public float acceleration = 0;
    private Vector2 enemyPosition;
    bool scheduleUpdate;
    /// <summary>
    /// 是否在抛物线移动
    /// </summary>
    bool isParabolaMove = false;
    /// <summary>
    /// 导弹上一次位置
    /// </summary>
    private Vector3 lastPos;
    /// <summary>
    /// 追踪目标
    /// </summary>
    public Enemy trackingTarget;

    #region new var
    [HideInInspector] public int removeSoundID = 0;
    [HideInInspector] public CatSkill.Item skillData;
    [HideInInspector] public bool isTracking = true;
    [HideInInspector] public bool isDestroyAfterCollision = true;
    [HideInInspector] public float boomRadius = 1;
    /// <summary>
    /// 只有主角的主武器能够去检测是否暴击
    /// </summary>
    [HideInInspector] public bool canCheckIsCriticalHit;
    /// <summary>
    /// 是否是特殊追踪模式，普通模式是生成导弹的时候设定一个目标点
    /// </summary>
    [HideInInspector] 
    
    public bool isSpecialTracking = false;

    private SpriteRenderer spriteRenderer;

    public float SurvivalTime = 20f;
    #endregion
	
    public void Init()
    {
        if (Globals.nearestEnemy == null)
        {
            enemyPosition = Vector2.zero;
        }
        else
        {
            enemyPosition = Globals.nearestAliveEnemy;
        }
        removeSoundID = 0;
        if (spriteRenderer == null) spriteRenderer = GetComponent<SpriteRenderer>();
        float rot = GameManager.instance.player.RotationInDegrees; // Random.value * 360;
        int angle = Random.Range(1, 60);
        int angle2 = (angle % 2 == 0) ? 70 : -70;
        rot += angle;
        transform.rotation = Quaternion.AngleAxis(rot, Vector3.forward);
        myAngle = rot;

        transform.localScale = Vector3.one;
        transform.localScale = new Vector3(1.25f, 1.25f, 1);
        gameObject.SetActive(true);
        isHoming = true;
        scheduleUpdate = true;
        

        StopCoroutine(nameof(MissileSchedule));
        StartCoroutine(nameof(MissileSchedule));


        isDestroyAfterCollision = true;
        curSpeed = initSpeed;
        if(skillData != null) ParabolaMove();
    }
    public void SetSpriteFrame(Sprite bulletSprite)
    {
        spriteRenderer.sprite = bulletSprite;
    }
    /// <summary>
    /// 霹雳10
    /// </summary>
    /// <param name="target">追踪目标</param>
    public void SpecialInit(Vector3 target) {
        enemyPosition = target;
        isSpecialTracking = false;
        Init();
        StartCoroutine(nameof(DelayTracking));
    }

    IEnumerator DelayTracking() {
        yield return new WaitForSeconds(0.005f);
        isSpecialTracking = true;
    }

    IEnumerator MissileSchedule()
    {
        yield return new WaitForSeconds(SurvivalTime);
        StopHoming();

        yield return new WaitForSeconds(0.1f);

        RemoveSelfNow();
    }
    
    IEnumerator MissileSpecialSchedule()
    {
        yield return new WaitForSeconds(SurvivalTime);
        StopHoming();
        yield return new WaitForSeconds(0.1f);
        RemoveSelfNow();
    }

    public void ResetMissile()
    {
        if (isParabolaMove)
        {
            return;
        }
        StopCoroutine(nameof(ResetValues));
        StartCoroutine(nameof(ResetValues));
    }

    IEnumerator ResetValues()
    {
        yield return new WaitForEndOfFrame();
        if (scheduleUpdate)
        {
            //GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.WXRExplosionAttack, transform.position, 1.5f, 1.5f, damageExplosionAttack, skillData);

            GameSharedData.Instance.playerMissilesInUse.Remove(this);
            StopCoroutine(nameof(MissileSchedule));
            scheduleUpdate = false;
            transform.position = Vector3.zero;
            transform.rotation = Quaternion.identity;
            isInUse = false;
            skillData = null;
            canCheckIsCriticalHit = false;
        }
        gameObject.SetActive(false);

    }

    void Update() {
        if (isParabolaMove) 
        {
            transform.right = transform.position - lastPos;
            lastPos = transform.position;
            return;
        }

        if(!scheduleUpdate) return;
        float curSpeed2 = Mathf.Min(curSpeed + acceleration * Time.deltaTime, 500f);

        if (isSpecialTracking && !trackingTarget)
        {
            trackingTarget = SpecialTracking();
        }

        if (isSpecialTracking && enemyPosition != Vector2.zero)
        {
            float dir = Vector2.SignedAngle(Vector2.right, (Vector2)enemyPosition - (Vector2)transform.position);
            float rotationDir = dir < 0 ? 360 + dir : dir;
            if (isHoming)
            {
                myAngle = Globals.MoveAngleTowards(myAngle, rotationDir, Time.deltaTime * 60 * 6);
            }
            transform.rotation = Quaternion.AngleAxis(myAngle, Vector3.forward);
        }


        float sd = 0.15f * Time.deltaTime * curSpeed2;
        transform.position += new Vector3(Mathf.Cos(Mathf.Deg2Rad * myAngle), Mathf.Sin(Mathf.Deg2Rad * myAngle)) * sd;
    }


    void CreateSmoke()
    {
        //Sprite* smoke = Sprite::create("res/Arsenal/smoke.png");
        //smoke->setPosition(missileSprite->getPosition());
        //this->addChild(smoke, -1);
        //smoke->setScale(0.2f, 0.2f);
        //smoke->setRotation(CCRANDOM_0_1() * 360);
        //smoke->runAction(ScaleBy::create(0.2f, 2));
        //smoke->setBlendFunc(BlendFunc::ADDITIVE);
        //smoke->runAction(Sequence::create(FadeOut::create(0.30f), CallFuncN::create(CC_CALLBACK_1(playerMissile::removeSelfNow, this)), NULL));
        //smoke->setCameraMask(GAMECAMERA);

    }

    void RemoveSelfNow()
    {
        StopCoroutine(nameof(MissileSchedule));

        //GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1,
        //transform.position, false, 1, 0.5f, 1.5f);

        ResetMissile();
    }
    public void PlayExplosionAttack()
    {

    }

    void StopHoming()
    {
        isHoming = false;
    }

    void StartHoming()
    {
        isHoming = true;
    }

    /// <summary>
    /// 特殊跟踪，导弹前面90度的夹角，3米半径的怪物才追踪
    /// </summary>
    Enemy SpecialTracking()
    {
        Transform playerTransform = transform;
        //追踪半径
        float distanceFromEnemy = 10;
        //追踪夹角
        float angle = 180;
        Enemy nearestEnemy = null;

        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy != null && enemy.canCheckDistance)
            {

                Vector2 directionToEnemy = ((Vector2)enemy.transform.position + enemy.offset) - (Vector2)playerTransform.position;

                directionToEnemy.Normalize();

                float angleToEnemy = Vector3.Angle(playerTransform.right, directionToEnemy);

                float dist = Vector2.Distance(((Vector2)enemy.transform.position + enemy.offset), playerTransform.position);

                if (angleToEnemy <= angle && dist < distanceFromEnemy)
                {
                    distanceFromEnemy = dist;
                    nearestEnemy = enemy;
                }

            }
        }

        return nearestEnemy;
    }

    /// <summary>
    /// 抛物线移动
    /// </summary>
    private void ParabolaMove()
    { 
        isParabolaMove = true;
        lastPos = transform.position;
        gameObject.SetActive(true);
        float jumpPower = skillData == null ? 21 : skillData.AttackSpeed * 0.0001f;
        float duringTime = skillData == null ? 2 : skillData.AttackTime * 0.0001f;
        transform.DOJump(enemyPosition, jumpPower, 1, duringTime).onComplete = () =>
        {
            ExplosionsAttack();
            isParabolaMove = false;
            gameObject.SetActive(false);
        };
    }

    /// <summary>
    /// 爆炸攻击
    /// </summary>
    private void ExplosionsAttack()
    {
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.WXRExplosionAttack, transform.position, 1.5f, 1.5f, damageExplosionAttack, skillData);

        //GameSharedData.Instance.playerMissilesInUse.Remove(this);
        //StopCoroutine(nameof(MissileSchedule));
        scheduleUpdate = false;
        transform.position = Vector3.zero;
        transform.rotation = Quaternion.identity;
        isInUse = false;
        skillData = null;
        canCheckIsCriticalHit = false;
    }
}
