using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;

public class ControlTower : Enemy
{

    [SerializeField] private new BoundingBoxFollower boundingBox;
    [SerializeField] private GameObject flame;
    private float[] buffer = null;
    private bool onFire = false;
    private float enemyScale = 1.4f;

    [HideInInspector] public bool allowLeftCannon = true;
    [HideInInspector] public bool allowRightCannon = true;
    [HideInInspector] public float zoomFactor;

    public Bone cannon;

    private bool isInitialized = false;

    private PlayerPing missionPing;

    //public static ControlTower Create()
    //{
    //    ControlTower ct = new GameObject().AddComponent<ControlTower>();

    //    return ct;
    //}

    //private void Start()
    //{
    //    Init();
    //}

    public void SetScale(float scale)
    {
        enemyScale = scale;
        enemySprite.transform.SetScale(scale);
        enemySprite.skeleton.FindBone("bone21").ScaleX = 0.9f / scale;
        enemySprite.skeleton.FindBone("bone21").ScaleY = 0.9f / scale;
    }

    public void Init()
    {
        if (isInitialized)
            return;


        schedulerId = "CTS" + GetInstanceID();
        tweenId = "CT" + GetInstanceID();
        isInitialized = true;
        base.Init();
        allowRelocate = false;
        isBoss = false;
        InitStats();
        _jsonScale = enemyScale;
        transform.position = new Vector2(player.transform.position.x + (Globals.CocosToUnity(5000)), Globals.LOWERBOUNDARY);
        //numberOfEnemies++;
        //enemySprite.transform.position = new Vector2(Globals.CocosToUnity(5000), 0);

        //this.addChild(enemySprite);
        //enemySprite.setAnimation(0, "controlTowerAnim", true);
        //enemySprite.setCameraMask(GAMECAMERA);
        //enemySprite.addChild(healthBar, 5);
        //healthBar.setScaleRatio(3.5);
        //healthBar.setPosition(-75, -10);
        //spSkeletonBounds_update(bounds, enemySprite.getSkeleton(), true);

        //TODO
        //this.runAction(Sequence::create(DelayTime::create(1), CallFunc::create([=]{ 
        //    setAllowPing(true, Enemy::PINGTYPE::MISSION_AHEAD);
        //    PlayerPing* missionPing = PlayerPing::createWithPingNode(enemySprite);
        //    this.addChild(missionPing);
        //}), NULL));

        DOTween.Sequence().SetId(schedulerId).AppendInterval(1f).AppendCallback(() =>
        {

            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, true);
            missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            missionPing.Init(transform, false);
        }).Play();


        //TODO
        explosionType = Explosions.ExplosionType.ExplosionTypeBuildingMission;
        zoomFactor = Globals.zoomValueWhileGame;

        //updateBounds(NULL);

        //this.schedule(schedule_selector(ControlTower::updateBounds), 4, 2, 2);

        //this.schedule(schedule_selector(ControlTower::shootFromRightCannon), 3);
        //this.schedule(schedule_selector(ControlTower::shootFromLeftCannon), 3);

        InvokeRepeating(nameof(ShootLeft), 3, 3f);
        InvokeRepeating(nameof(ShootRight), 3, 3f);

        //if (isTutorial)

        //{
        //    this.scheduleUpdate();

        //}

        //TODO!!
        additionalOnDestroy += () =>
        {
            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeEnemyBuilding && Globals.gameType == GameType.Training)
            {
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    GameManager.instance.missionManager.MissionComplete();
                }
                else
                {
                    //(static_cast<GameController*>(GETGAMECONTROLLER).mobileAssistiveTutorialObj).changeState(MobileAssistiveControlsTutorial::AssistModeTutorialState::Aim_Assist_Rotation);

                }
            }

            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeEnemyBase && Globals.gameType == GameType.Training)
            {
                if (GameData.instance.fileHandler.currentMission != 0)
                    GameManager.instance.missionManager.AddPoint();
            }
        };
        //return true;
    }

    private void Update()
    {

    }

    private void ShootLeft()
    {
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(ShootFromLeftCannon());
        }
        
    }
    private void ShootRight() 
    {
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(ShootFromRightCannon());
        }
    } 

    private IEnumerator ShootFromLeftCannon()
    {
        if (!allowLeftCannon)
        {
            yield break;
        }
        WaterMissile missile = null;
        for (int i = 0; i < 3; i++)
        {
            cannon = enemySprite.skeleton.FindBone("cannon2");
            bool didFindMissile = false;
            foreach (WaterMissile m in GameSharedData.Instance.enemyWaterMissilePool)
            {
                if (!m.isInUse)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindMissile = true;
                    break;
                }

            }
            if (!didFindMissile)
            {
               yield break;
            }
            missile.Init();
            missile.SetHorizontalVelocity(i * -0.5f - 1);
            missile.SetDamage(35);
            missile.radiusSQ = Globals.CocosToUnity(90);
            missile.transform.localScale = Vector2.one;
            missile.transform.SetPositionAndRotation(cannon.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0,0, 0));
            GameSharedData.Instance.enemyMissilesInUse.Add(missile);
            missile.PlayRotateAnim(-90, 5);
            yield return new WaitForSeconds(0.5f);

        }
    }

    private IEnumerator ShootFromRightCannon()
    {
        if (!allowRightCannon)
        {
            yield break;
        }
        WaterMissile missile = null;
        for (int i = 0; i < 3; i++)
        {
            cannon = enemySprite.skeleton.FindBone("cannon1");
            bool didFindMissile = false;
            foreach (WaterMissile m in GameSharedData.Instance.enemyWaterMissilePool)
            {
                if (!m.isInUse&&!m.hasHit)
                {
                    missile = m;
                    missile.isInUse = true;
                    didFindMissile = true;
                    break;
                }

            }
            if (!didFindMissile)
            {
                yield break;
            }
            missile.Init();
            missile.SetHorizontalVelocity(i * 0.5f + 1);
            missile.SetDamage(35);
            missile.radiusSQ = Globals.CocosToUnity(90);
            missile.transform.localScale = Vector2.one;
            missile.transform.SetPositionAndRotation(cannon.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0, 0, 180));
            GameSharedData.Instance.enemyMissilesInUse.Add(missile);
            missile.PlayRotateAnim(-90, 5);
            yield return new WaitForSeconds(0.5f);
        }
    }

    private void UpdateBound()
    {
    }

    public override void Destroy()
    {
        //TODO
        //shakeScreen = 15;
        //shakeIntensity = 125;
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);

        //hared::playSound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        GameManager.instance.ShakeCamera(1, 5);
        base.Destroy();
    }

    public void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = baseStats.speed = 3.5f + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = 1500;
        stats.bulletDamage = baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 25;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;
        stats.bulletSpeed = baseStats.bulletSpeed = 100;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (stats.health / stats.maxHealth.Value < 0.4 && onFire == false)
        {
            //TODO
            flame.SetActive(true);
            onFire = true;
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAboveWater, transform.position, false, 1, 1, 1);
            //enemySprite.addChild(exp);
        }
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        else
        {
            return false;
        }
    }

}
