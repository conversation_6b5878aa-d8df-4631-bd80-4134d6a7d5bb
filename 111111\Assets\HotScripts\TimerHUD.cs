using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using DG.Tweening;
public class TimerHUD : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI gameLabel;
    bool completeMission = true;
    int time = 0;
    bool forwardTimer = false;
    string schedulerId;
    PlayerController player;
    MissionManager missionManager;

    public void CreateWithTime(int sec)
    {
        gameObject.SetActive(true);
        player = GameManager.instance.player;
        missionManager = GameManager.instance.missionManager;
        schedulerId = "Timer" + GetInstanceID();
        Init(sec);
    }

    public void StartTimer()
    {
        DOTween.Sequence().SetId(schedulerId).AppendCallback(ScheduleFunction).AppendInterval(1).SetLoops(-1).Play();
    }

    public void StartWithForwardTimer()
    {
        forwardTimer = true;
        DOTween.Sequence().SetId(schedulerId).AppendCallback(ScheduleFunction).AppendInterval(1).SetLoops(-1).Play();

    }

    public void StartWithBackwardTimer()
    {
        forwardTimer = false;
        DOTween.Sequence().SetId(schedulerId).AppendCallback(ScheduleFunction).AppendInterval(1).SetLoops(-1).Play();
    }

    private void Init(int t)
    {
        time = t;
        gameLabel.gameObject.SetActive(false);
    }

    private void ScheduleFunction()
    {
        if (player.Mode != PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
            return;
        if (forwardTimer)
        {
            time++;
            UpdateLabel();

        }

        else

        {
            time--;
            {
                if (completeMission)
                {

                    missionManager.AddPoint();
                }
            }
            UpdateLabel();

            if (time == 0)
            {
                DOTween.Kill(schedulerId);
                Observer.DispatchCustomEvent(Globals.DEACTIVE_LASER_EVENT);
                Observer.DispatchCustomEvent("GeneratorDestroyed");

                if (completeMission)
                {
                    missionManager.MissionComplete();
                }
                DOTween.Sequence().SetId(schedulerId).AppendInterval(3).AppendCallback(() => { gameObject.SetActive(false); }).Play();
            }
        }
    }
    private void UpdateLabel()
    {
        //char ch[32];
        //sprintf(ch, "%d", _time);

//        gameLabel.text = time.ToString();
  //      _GameLabel->setString(Shared::convertStringNumbersToArabic(std::string(ch)));

        gameLabel.text = time.ToString();
        gameLabel.gameObject.SetActive(true);

    }

    //    GameLabel *_GameLabel = nullptr;
}
