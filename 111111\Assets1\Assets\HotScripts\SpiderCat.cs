using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using Spine;

public class SpiderCat : Enemy
{
    public enum TripodStates
    {
        Shoot1,
        Shoot2,
        Spider1,
        Laser1,
        Spider2,
        Laser2,
        Final
    };

    [SerializeField] Sprite creepyBulletSprite;
    [SerializeField] Laser laser;
    [SerializeField] Collider2D gun1Collider, gun2Collider, headCollider;
    Collider2D currentCollider;

    bool isInAir = false;
    bool isFtueBoss = false;

    bool allowShoot;

    int TriPodLevel;
    int numberOfFires;
    float Pattern1Angle = 0;
    bool checkDistance;
    bool checkBelow;
    bool laserOn = false;

    Sequence gun1Sequence, gun2Sequence, headSequence;
    Laser laserObj;
    TripodStates currentState;
    Bone bone;
    Bounds bounds;
    bool laserMovingDown = true;
    Vector2 AdditionalPosition;
    Sequence healthBarSequence;

    Transform playerTransform;

    float armPosX;
    float armPosY;

    bool tutorialFirstDialogueShown = false;
    bool tutorialSecondDialogueShown = false;
    bool phase1ShootingStart = false;
    bool playerWingsActivatedForTutorial = false;

    private void InitLaser()
    {
        laser.Init();
        laser.setDamage = 5.0f;
        laser.SetLaserScale(55, 2f);
        laser.setAllowSound = true;
        laser.SetAllowShrink(false);
        laser.SetIsActive(false);
        laserOn = false;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStats();
        Globals.LEFTBOUNDARY = -96.49439f;
        Globals.RIGHTBOUNDARY = 96.49439f;
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            isFtueBoss = true;
            stats.maxHealth.Value *= 0.2f;
            stats.health *= 0.2f;
            Globals.LEFTBOUNDARY = -Globals.CocosToUnity(90000000);
            Globals.RIGHTBOUNDARY = Globals.CocosToUnity(90000000);
        }
        allowRelocate = false;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.SpiderCatIntro);
        //Shared::playSound("res/Sounds/Bosses/Boss6/SpiderCatIntro.mp3");
        isTripodBoss = true;
        checkDistance = true;
        checkBelow = true;
        allowShoot = true;

        currentCollider = headCollider;

        enemySprite.state.SetAnimation(0, "entry2", false);
        enemySprite.state.AddAnimation(0, "shootLvl1", true);

        transform.position = new Vector3(transform.position.x, Globals.CocosToUnity(525), transform.position.z);
        InitMixes();

        InitLaser();

        playerTransform = GameManager.instance.player.transform;

        if (GameManager.instance.player.GetPlaneScale() > 0)
        {
            enemySprite.transform.localScale = new Vector3(1, 1, 1);
            transform.position = new Vector3(playerTransform.position.x + Globals.CocosToUnity(800),
                transform.position.y, transform.position.z);

            if (transform.position.x > Globals.RIGHTBOUNDARY - Globals.CocosToUnity(600))
            {
                transform.position = new Vector3(Globals.RIGHTBOUNDARY - Globals.CocosToUnity(600),
                    transform.position.y, transform.position.z);
            }
        }
        else
        {
            enemySprite.transform.localScale = new Vector3(-1, 1, 1);
            transform.position =
                new Vector3(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(800),
                transform.position.y, transform.position.z);

            if (transform.position.x < Globals.LEFTBOUNDARY + Globals.CocosToUnity(600))
            {
                transform.position = new Vector3(Globals.LEFTBOUNDARY + Globals.CocosToUnity(600),
                    transform.position.y, transform.position.z);
            }
        }



        Globals.bossPosition = enemySprite.transform.position;


        healthBar.transform.SetWorldPositionY(-Globals.CocosToUnity(50));
        healthBar.SetDisplayHealth(100);
        healthBar.gameObject.SetActive(true);

        {
            enemySprite.state.Event += SCEventHandler;
        }
        armPosX = transform.position.x;
        armPosY = transform.position.y;
        scheduleUpdate = true;
    }

    private void OnDisable()
    {
        enemySprite.state.Event -= SCEventHandler;
    }

    void SCEventHandler(TrackEntry entry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "land")
        {
            GameManager.instance.ShakeCamera(2, 10);
            float scale = 6;

            for (int i = 0; i < 4; i++)
            {
                if (i == 0)
                {
                    bone = enemySprite.Skeleton.FindBone("15");//front most leg
                    scale = 7;
                }
                if (i == 1)
                {
                    bone = enemySprite.Skeleton.FindBone("8");//back leg
                    scale = 6;
                }
                if (i == 2)
                {
                    bone = enemySprite.Skeleton.FindBone("4");//back leg
                    scale = 6;
                }
                if (i == 3)
                {
                    bone = enemySprite.Skeleton.FindBone("12");//front leg
                    scale = 4;
                }

                GameManager.instance.SpawnSplash(bone.GetWorldPosition(enemySprite.transform), scale / 2);

                //GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBuilding,
                //    new Vector3(transform.position.x + bone.WorldX, Globals.LOWERBOUNDARY - Globals.CocosToUnity(150),
                //    0), false, 1, scale, 0);

                //sprintf(ch, "bg%d", g_bgType); TODO
                //waterExplosion.setAnimation(0, ch, false); TODO

            }
        }
        if (spineEvent.Data.Name == "shoot1")
        {
            allowShoot = true;
            Shoot();
        }
        if (spineEvent.Data.Name == "shoot2")
        {
            allowShoot = true;
            Shoot2();
        }
        if (spineEvent.Data.Name == "explosion")
        {
            Explosion();
        }
        if (spineEvent.Data.Name == "laserOn")
        {
            laserOn = true;
            laser.SetIsActive(true);
        }
        if (spineEvent.Data.Name == "stepEnd")
        {
            if (currentState == TripodStates.Shoot1)
            {
                enemySprite.state.SetAnimation(0, "shootLvl1", true);

                checkBelow = true;
            }
            else if (currentState == TripodStates.Shoot2)
            {
                enemySprite.state.SetAnimation(0, "shootLvl2", true);
                checkBelow = true;
            }
        }

    }

    void InitMixes()
    {
        enemySprite.state.Data.SetMix("shootLvl1", "stepBack", 0.1f);
        enemySprite.state.Data.SetMix("stepBack", "shootLvl1", 0.1f);
        enemySprite.state.Data.SetMix("shootLvl2", "stepBack", 0.1f);
        enemySprite.state.Data.SetMix("stepBack", "shootLvl2", 0.1f);
        enemySprite.state.Data.SetMix("stepBack", "idleLaser", 0.1f);
        enemySprite.state.Data.SetMix("shootLvl1", "lvl2", 0.2f);
        enemySprite.state.Data.SetMix("entry2", "shootLvl1", 0.2f);
        enemySprite.state.Data.SetMix("entry", "shootLvl1", 0.05f);
        enemySprite.state.Data.SetMix("entry", "shootLvl2", 0.05f);
        enemySprite.state.Data.SetMix("entry", "idle", 0.05f);
        enemySprite.state.Data.SetMix("stepBack", "idle", 0.1f);
        enemySprite.state.Data.SetMix("littleLaserOn", "stepBack", 0.1f);
        enemySprite.state.Data.SetMix("stepBack", "stepBack", 0.1f);
        enemySprite.state.Data.SetMix("idleLaser", "laserShoot", 0.1f);
    }

    void Update()
    {
        if (!scheduleUpdate)
            return;
        if (isFtueBoss)
        {
            healthBar.gameObject.SetActive(false);
        }
        bounds = currentCollider.bounds;

        healthBar.transform.SetWorldPositionX(transform.position.x);

        {
            Globals.bossPosition = (Vector2)transform.position + AdditionalPosition;
        }

        if (checkDistance && checkBelow
                && Mathf.Abs(playerTransform.position.x - transform.position.x) > Globals.CocosToUnity(2000)
                && currentState != TripodStates.Laser2)
        {
            checkDistance = false;
            Jump();
        }

        if (checkBelow && checkDistance && currentState != TripodStates.Laser2 && currentState != TripodStates.Final)
        {
            if (enemySprite.transform.localScale.x == 1)
            {
                float angle = Globals.CalcAngle(playerTransform.position, transform.position);

                if (angle < 300 || angle > 410)
                {
                    if (transform.position.x < Globals.RIGHTBOUNDARY - Globals.CocosToUnity(1500))
                    {
                        StepBack();
                        transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(1400), 0), 0.45f).OnComplete(() =>
                        {
                            ShootAnimation();
                        });
                        checkBelow = false;
                    }
                }
            }
            else
            {

                float angle = Globals.CalcAngle(transform.position, playerTransform.position);

                if (angle < 300 || angle > 410)
                {
                    if (transform.position.x > Globals.LEFTBOUNDARY + Globals.CocosToUnity(1500))
                    {
                        StepBack();
                        transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(1400), 0), 0.45f).OnComplete(() =>
                        {
                            ShootAnimation();
                        });
                        checkBelow = false;
                    }
                }
            }
        }

        
        if (laserOn)
        {
            if (laser.gameObject.activeInHierarchy == false)
            {
                laser.SetIsActive(true);
            }
            bone = enemySprite.skeleton.FindBone("laserOn");
            var bonePosition = bone.GetWorldPosition(enemySprite.transform);
            
            laser.transform.position = new Vector3(bonePosition.x, bonePosition.y, laser.transform.position.z);

            armPosX = transform.position.x + bone.WorldX;
            armPosY = transform.position.y + bone.WorldY;

            bone = enemySprite.Skeleton.FindBone("head");

            if (laserMovingDown)
            {
                bone.Rotation += 0.5f * Time.deltaTime * 60;

                if (bone.Rotation > 70)
                {
                    laserMovingDown = false;
                }
            }
            else
            {
                bone.Rotation -= 0.5f * Time.deltaTime * 60;

                if(bone.Rotation < -70)
                {
                    laserMovingDown = true;
                }
            }

            laser.SetLaserRotation(-bone.Rotation);
        }
        

    }
    void StepBack()
    {
        enemySprite.state.SetAnimation(0, "stepBack", false);

        if (currentState == TripodStates.Shoot1)
        {
            enemySprite.state.AddAnimation(0, "shootLvl1", true);
            checkBelow = true;
        }
        else if (currentState == TripodStates.Shoot2)
        {
            enemySprite.state.AddAnimation(0, "shootLvl2", true);
            checkBelow = true;
        }
        else if (currentState == TripodStates.Laser1)
        {
            enemySprite.state.AddAnimation(0, "idleLaser", false);
            enemySprite.state.AddAnimation(0, "laserShoot", true);

            checkBelow = true;
        }
        else if (currentState == TripodStates.Laser2)
        {
            enemySprite.state.AddAnimation(0, "idleLaser", false);
            enemySprite.state.AddAnimation(0, "laserShoot", true);
            checkBelow = true;
        }

        allowShoot = false;
    }

	
    void Jump()
    {
        laserOn = false;
        checkDistance = false;
        laser.SetIsActive(false);
        enemySprite.state.SetAnimation(0, "jump", false);
        if (healthBar)
        {
            healthBar.gameObject.SetActive(false);
        }
        DOTween.Sequence().AppendInterval(2).AppendCallback(() => Land());
        allowShoot = false;
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.SpiderCatJump);

        //Shared::playSound("res/Sounds/Bosses/Boss6/SpiderCatJump.mp3");
        isInAir = true;
        canCheckDistance = false;
    }
	
    void Land()
    {
        isInAir = false;
        canCheckDistance = true;
        checkDistance = true;
        enemySprite.state.SetAnimation(0, "entry", false);
        if (currentState == TripodStates.Shoot1)
        {
            enemySprite.state.AddAnimation(0, "shootLvl1", true);

            checkBelow = true;
        }
        else if (currentState == TripodStates.Shoot2)
        {
            enemySprite.state.AddAnimation(0, "shootLvl2", true);
            checkBelow = true;
        }
        else if (currentState == TripodStates.Laser1)
        {
            enemySprite.state.AddAnimation(0, "idleLaser", false);
            enemySprite.state.AddAnimation(0, "laserShoot", true);
            checkBelow = true;
        }
        else if (currentState == TripodStates.Laser2)
        {
            enemySprite.state.AddAnimation(0, "idleLaser", false);
            enemySprite.state.AddAnimation(0, "laserShoot", true);
            checkBelow = true;
        }

        if (GameManager.instance.player.Acceleration.x > 0)
        {
            enemySprite.transform.localScale = new Vector3(1, 1, 1);
            transform.position = new Vector3(playerTransform.position.x + Globals.CocosToUnity(800),
                transform.position.y, transform.position.z);
            if (transform.position.x > Globals.RIGHTBOUNDARY - Globals.CocosToUnity(600))
            {
                transform.position = new Vector3(Globals.RIGHTBOUNDARY - Globals.CocosToUnity(600),
                    transform.position.y, transform.position.z);
            }
        }
        else
        {
            enemySprite.transform.localScale = new Vector3(-1, 1, 1);
            transform.position = new Vector3(playerTransform.position.x - Globals.CocosToUnity(800),
                transform.position.y, transform.position.z);
            if (currentState == TripodStates.Laser2) 
            {
                if (!isFtueBoss)
                    transform.position = new Vector3(Globals.LEFTBOUNDARY, transform.position.y, transform.position.z);
            }
            if (transform.position.x < Globals.LEFTBOUNDARY + Globals.CocosToUnity(600))
            {
                transform.position = new Vector3(Globals.LEFTBOUNDARY + Globals.CocosToUnity(600), transform.position.y,
                    transform.position.z);
            }
        }
    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        isBoss = true;
        int bossNumber = 7;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {        
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn); 
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }
        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }
        var bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            stats.speed = baseStats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
            stats.health = baseStats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]);
            stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
            stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        }
        else
        {

            stats.speed = baseStats.speed = System.Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
            stats.health = baseStats.health = System.Convert.ToSingle((bossStats["health"] as PList)["value"]);
            stats.turnSpeed = baseStats.turnSpeed = System.Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
            stats.bulletDamage = baseStats.bulletDamage = System.Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        }
        stats.regen = baseStats.regen = System.Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = System.Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = System.Convert.ToInt32((bossStats["coins"] as PList)["value"]);
        stats.bulletSpeed = baseStats.bulletSpeed = 6.5f;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }
    void ShootAnimation()
    {
        checkBelow = true;
    }

    void Shoot()
    {
        {
            phase1ShootingStart = true;
            Bullet bullet = null;

            for (int i = 0; i < 2; i++)
            {
                bool didFindBullet = false;
                foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        bullet.isInUse = true;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }

                bullet.SetSpriteFrame(creepyBulletSprite);
                bullet.SetSortingLayer("Enemies", 6);
                bullet.setDamage(stats.bulletDamage);
                bullet.duration = 6.5f;
                bullet.transform.localScale = new Vector3(1, 1, 1);

                bullet.transform.rotation = Quaternion.Euler(new Vector3(0, 0, 90));

                bullet.setRadiusEffectSquared(Globals.CocosToUnity(75));

                if (isFtueBoss) 
                {
                    bullet.PushBack=0.25f;
                }

                bone = enemySprite.Skeleton.FindBone("gun1");

                AdditionalPosition = new Vector2(bone.WorldX, bone.WorldY);
                var pos = bone.GetWorldPosition(enemySprite.transform);
                bullet.transform.position = new Vector3(pos.x, pos.y);
                armPosX = bullet.transform.position.x;
                armPosY = bullet.transform.position.y;

                float angle = Pattern1Angle + (i * 180);
                bullet.transform.rotation = Quaternion.Euler(new Vector3(0, 0, angle));
                Vector2 dest =
                    new Vector2(Globals.CocosToUnity(5000) * Mathf.Sin(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z)) * -1,
                    Globals.CocosToUnity(5000) * Mathf.Cos(Mathf.Deg2Rad * (bullet.transform.eulerAngles.z)));
                bullet.gameObject.SetActive(true);
                bullet.PlayBulletAnim(6.5f, dest);

                GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            }
        }
        //TODO Distance Sound
        //Shared::playSound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3", enemySprite.getPosition());
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spiderCatBullet2);

        Pattern1Angle += 10;


    }

    void Shoot2()
    {
        if (numberOfFires % 10 < 5)
        {
            Bullet bullet = null;
            for (int i = 0; i < 10; i++)
            {

                if (i == 5)
                {
                }
                else
                {
                    bool didFindBullet = false;
                    foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                    {
                        if (!b.isInUse)
                        {
                            bullet = b;
                            bullet.isInUse = true;
                            didFindBullet = true;
                            break;
                        }
                    }
                    if (!didFindBullet)
                    {
                        return;
                    }

                    bullet.setDamage(stats.bulletDamage);
                    bullet.SetSpriteFrame(creepyBulletSprite);
                    bullet.SetSortingLayer("Enemies", 6);
                    bullet.transform.localScale = new Vector3(1, 1, 1);
                    bullet.setRadiusEffectSquared(Globals.CocosToUnity(75));

                    bone = enemySprite.skeleton.FindBone("gun2");
                    bullet.transform.rotation = Quaternion.Euler(new Vector3(0, 0, 90));
                    AdditionalPosition = new Vector2(bone.WorldX, bone.WorldY);

                    var pos = bone.GetWorldPosition(enemySprite.transform);
                    bullet.transform.position = new Vector3(pos.x, pos.y);
                    armPosX = bullet.transform.position.x;
                    armPosY = bullet.transform.position.y;

                    bullet.gameObject.SetActive(true);
                    bullet.duration = 6.5f;
                    bullet.ResetWithDelay();

                    if (playerTransform.position.x > transform.position.x)
                    {
                        DOTween.Sequence()
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(100), 0), 0.5f))
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(0,
                                Globals.CocosToUnity((i * 200) - 1000)), 1f))
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(30, 0), 5f));
                    }
                    else
                    {
                        DOTween.Sequence()
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(100), 0), 0.5f))
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(0,
                                Globals.CocosToUnity((i * 200) - 1000)), 1f))
                            .Append(bullet.transform.DOBlendableMoveBy(new Vector2(-30, 0), 5f));
                    }


                    GameSharedData.Instance.enemyBulletInUse.Add(bullet);
                }
            }
        }
        else
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.setDamage(stats.bulletDamage * 2);
            bullet.SetSpriteFrame(creepyBulletSprite);
            bullet.SetSortingLayer("Enemies", 6);
            bullet.duration = 5.5f;
            bullet.transform.localScale = new Vector3(2, 2, 1);
            bullet.transform.rotation = Quaternion.Euler(new Vector3(0, 0, 90));

            bone = enemySprite.Skeleton.FindBone("gun2");

            bullet.setRadiusEffectSquared(Globals.CocosToUnity(120));

            var pos = bone.GetWorldPosition(enemySprite.transform);
            bullet.transform.position = new Vector2(pos.x, pos.y);

            bullet.gameObject.SetActive(true);

            if (playerTransform.position.x > transform.position.x)
            {
                DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() =>
                {
                    bullet.PlayBulletAnim(5, new Vector2(Globals.CocosToUnity(3000), 0));
                });
            }
            else
            {
                DOTween.Sequence().AppendInterval(1.5f).AppendCallback(() =>
                {
                    bullet.PlayBulletAnim(5, new Vector2(-Globals.CocosToUnity(3000), 0));
                });
            }


            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }
        //TODO Distance Sound
        //Shared::playSound("res/Sounds/Bosses/Boss6/Bullet/spiderCatBullet2.mp3", enemySprite.getPosition());

        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spiderCatBullet2);
        numberOfFires++;
    }

    void Explosion()
    {
        if (currentState == TripodStates.Shoot2)
        {
            bone = enemySprite.Skeleton.FindBone("gunBB1");
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1,
                            new Vector3(transform.position.x + bone.WorldX, transform.position.y + bone.WorldY,
                            0), false, 1, 5, 0);
        }
        else if (currentState == TripodStates.Spider1)
        {
            bone = enemySprite.Skeleton.FindBone("gunBB2");
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1,
                                        new Vector3(transform.position.x + bone.WorldX, transform.position.y + bone.WorldY,
                                        0), false, 1, 5, 0);
        }
    }
    
    public override bool TakeHit(double damage)
    {
        if (isInAir)
        {
            return false;
        }


        stats.health = stats.health - damage; // Remove '* 20' TODO

        if (isFtueBoss && stats.health > 15)
        {
            GameManager.instance.player.Stats.health = GameManager.instance.player.Stats.maxHealth.Value
                    * (stats.health / stats.maxHealth.Value);
        }

        if (stats.health < 4000 && !playerWingsActivatedForTutorial && isFtueBoss)
        {

            //GameManager.instance.player.getRearGun().forceActivateWingsSpecial(); TODO
            playerWingsActivatedForTutorial = true;
        }

        healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
        healthBar.gameObject.SetActive(true);
        healthBarSequence.Kill();
        //healthBarSequence = DOTween.Sequence().AppendInterval(3).AppendCallback(() => healthBar.gameObject.SetActive(false));

        if (isFtueBoss)
        {
            if (currentState != TripodStates.Laser2)
            {
                enemySprite.Skeleton.FindSlot("gun2").R = 1;
                enemySprite.Skeleton.FindSlot("gun2").G = 0.0f;
                enemySprite.Skeleton.FindSlot("gun2").B = 0.0f;

                gun2Sequence.Kill();
                gun2Sequence = DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                {
                    enemySprite.Skeleton.FindSlot("gun2").R = 1;
                    enemySprite.Skeleton.FindSlot("gun2").G = 1;
                    enemySprite.Skeleton.FindSlot("gun2").B = 1;
                });
            }
            if (stats.health < (stats.maxHealth.Value * 0.80f) && !tutorialFirstDialogueShown)

            {
                {
                    tutorialFirstDialogueShown = true;
                    DOTween.Sequence().AppendInterval(2)
                        .AppendCallback(() =>
                    {
                        currentState = TripodStates.Laser2;
                        bone = enemySprite.Skeleton.FindBone("gunBB1");

                        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1,
                                        new Vector3(transform.position.x + bone.WorldX, transform.position.y + bone.WorldY,
                                        0), false, 1, 10, 0);

                        enemySprite.state.SetAnimation(0, "lvl2", false);

                        checkBelow = true;
                    })
                        .AppendInterval(1)
                        .AppendCallback(() =>
                        {
                            GameManager.instance.timeManager.SetTimescale(0.2f);
                            Globals.zoomToBossForSec = 0.6f;
                        })
                        .AppendInterval(1/2)
                        .AppendCallback(() =>
                        {
                            Globals.LEFTBOUNDARY = playerTransform.position.x - Globals.CocosToUnity(1500);
                            Globals.RIGHTBOUNDARY = playerTransform.position.x + Globals.CocosToUnity(1500);
                            //Time.timeScale = 1;
                            LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
                            Jump();
                        })
                        .AppendInterval(1.5f/2f)
                        .AppendCallback(() =>
                        {
                            // TODO
                            //Director::getInstance().getEventDispatcher().dispatchCustomEvent("Spawn_Mid_Dialogues", (void*)2);
                            Observer.DispatchCustomEvent("SpawnMidDialogues");

                            DOTween.Sequence().AppendInterval(3.2f).AppendCallback(() =>
                            {
                                laser.SetLaserScale(55, 8.0f);
                                enemySprite.state.AddAnimation(0, "laserMode", false);
                            });

                            InvokeRepeating("CheckReEntries", 5, 3); 
                        });

                }
            }


            if (stats.health < 0 && !tutorialSecondDialogueShown)
            {
                tutorialSecondDialogueShown = true;

                DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                {
#if UNITY_ANDROID || UNITY_IOS

                    Observer.DispatchCustomEvent("Hide_SpecialAbility_Hud");
#endif

                }).Play() ;
            }
        }
        else
        {
            if (currentState == TripodStates.Shoot1)
            {
                enemySprite.Skeleton.FindSlot("gun2").R = 1;
                enemySprite.Skeleton.FindSlot("gun2").G = 0.0f;
                enemySprite.Skeleton.FindSlot("gun2").B = 0.0f;

                gun2Sequence.Kill();
                gun2Sequence = DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                {
                    enemySprite.Skeleton.FindSlot("gun2").R = 1;
                    enemySprite.Skeleton.FindSlot("gun2").G = 1;
                    enemySprite.Skeleton.FindSlot("gun2").B = 1;
                });



                if (stats.health / stats.maxHealth.Value < 0.8f && allowShoot)
                {
                    Observer.DispatchCustomEvent("ChangeBossState");

                    currentState = TripodStates.Shoot2;
                    currentCollider = headCollider;
                    Globals.zoomToBossForSec = 0.6f;

                    enemySprite.state.SetAnimation(0, "lvl2", false);
                    enemySprite.state.AddAnimation(0, "shootLvl2", true);
                }
            }
            else if (currentState == TripodStates.Shoot2)
            {
                enemySprite.Skeleton.FindSlot("gun").R = 1;
                enemySprite.Skeleton.FindSlot("gun").G = 0.0f;
                enemySprite.Skeleton.FindSlot("gun").B = 0.0f;

                gun1Sequence.Kill();
                gun1Sequence = DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                {
                    enemySprite.Skeleton.FindSlot("gun").R = 1;
                    enemySprite.Skeleton.FindSlot("gun").G = 1;
                    enemySprite.Skeleton.FindSlot("gun").B = 1;
                });

                if (stats.health / stats.maxHealth.Value < 0.6f)
                {
                    Observer.DispatchCustomEvent("ChangeBossState");

                    currentState = TripodStates.Spider1;
                    currentCollider = headCollider;
                    Globals.zoomToBossForSec = 0.6f;
                    enemySprite.state.SetAnimation(0, "lvl3", false);
                    enemySprite.state.AddAnimation(0, "jump", false);
                    checkDistance = false;
                    isInAir = true;
                    canCheckDistance = false;
                    healthBar.gameObject.SetActive(false);
                    GameSharedData.Instance.ClearEnemyBullets();

                    DOTween.Sequence().AppendInterval(4).AppendCallback(() =>
                    {
                        //Globals.zoomValueWhileGame = Globals.CocosToUnity(1000);

                        {
                            LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                            node.CreateWithType(SpiderType.LASER);
                            node._allowKillPoint = false;
                            node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }

                        {
                            LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                            node.CreateWithType(SpiderType.LASER);
                            node._allowKillPoint = false;
                            node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }

                        {
                            BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                            node._allowKillPoint = false;
                            node.boundaryPosX = playerTransform.position.x - Globals.CocosToUnity(2500);
                            node.AllowBoundaryChange = true;
                            node._isLeft = 1;
                            node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }

                        {
                            BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                            node._allowKillPoint = false;
                            node.boundaryPosX = playerTransform.position.x + Globals.CocosToUnity(2500);
                            node.AllowBoundaryChange = true;
                            node._isLeft = 0;
                            node.SetAttributeByLord(stats.bulletDamage, stats.health);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }
                    });

                    InvokeRepeating("CheckReEntries", 5, 3);
                }
            }
            else if (currentState == TripodStates.Laser1)
            {
                enemySprite.Skeleton.FindSlot("head").R = 1;
                enemySprite.Skeleton.FindSlot("head").G = 0.0f;
                enemySprite.Skeleton.FindSlot("head").B = 0.0f;

                headSequence.Kill();
                headSequence = DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                {
                    enemySprite.Skeleton.FindSlot("head").R = 1;
                    enemySprite.Skeleton.FindSlot("head").G = 1;
                    enemySprite.Skeleton.FindSlot("head").B = 1;
                });

                if (stats.health < stats.maxHealth.Value * 0.45f)
                {
                    Observer.DispatchCustomEvent("ChangeBossState");

                    currentState = TripodStates.Spider2;
                    laser.SetIsActive(false);
                    enemySprite.state.SetAnimation(0, "lvl3", false);
                    enemySprite.state.AddAnimation(0, "jump", false);
                    checkDistance = false;
                    isInAir = true;
                    canCheckDistance = false;
                    healthBar.gameObject.SetActive(false);

                    DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
                    {
                        //Globals.zoomValueWhileGame = Globals.CocosToUnity(1000);
                        {
                            LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                            node.CreateWithType(SpiderType.GUN);
                            node._allowKillPoint = false;
                            node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }
                        {
                            LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                            node.CreateWithType(SpiderType.GUN);
                            node._allowKillPoint = false;
                            node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                            Ping.Init(node.transform, true);
                        }
                    })
                        .AppendInterval(1)
                        .AppendCallback(() =>
                        {
                            //Globals.zoomValueWhileGame = Globals.CocosToUnity(1000);
                            {
                                LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                                node.CreateWithType(SpiderType.GUN);
                                node._allowKillPoint = false;
                                node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                                PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                                Ping.Init(node.transform, true);

                            }
                            {
                                LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                                node.CreateWithType(SpiderType.GUN);
                                node._allowKillPoint = false;
                                node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                                PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                                Ping.Init(node.transform, true);
                            }
                        });

                    InvokeRepeating("CheckReEntries", 5, 3);
                }
            }

            else if (currentState == TripodStates.Laser2 || currentState == TripodStates.Final)
            {
                enemySprite.Skeleton.FindSlot("head").R = 1;
                enemySprite.Skeleton.FindSlot("head").G = 0.0f;
                enemySprite.Skeleton.FindSlot("head").B = 0.0f;

                headSequence.Kill();
                //headSequence = DOTween.Sequence().AppendInterval(0.2f).AppendCallback(() =>
                //{
                //            enemySprite.Skeleton.FindSlot("head").R = 1;
                //            enemySprite.Skeleton.FindSlot("head").G = 1;
                //            enemySprite.Skeleton.FindSlot("head").B = 1;
                //});

                if (stats.health < (0.1f * stats.maxHealth.Value) && currentState == TripodStates.Laser2)
                {
                    currentState = TripodStates.Final;
                    //Globals.zoomValueWhileGame = Globals.CocosToUnity(1100);

                    DOTween.To(() => Globals.LEFTBOUNDARY, x => Globals.LEFTBOUNDARY = x,
                        Globals.LEFTBOUNDARY - Globals.CocosToUnity(1000), 2);
                }
            }
        }

        {
            if (stats.health < 0)
            {
                if (isBoss)
                {
                    scheduleUpdate = false;
                    Globals.isBossMode = false;

                }
                laser.SetIsActive(false);
                return true;
            }
        }

        return false;
    }
	
    public override void Destroy()
    {

        scheduleUpdate = false;
        //if (Globals.gameType == GameType.Survival)
        //{
        //    Globals.LEFTBOUNDARY = -Globals.CocosToUnity(90000000);
        //    Globals.RIGHTBOUNDARY = Globals.CocosToUnity(90000000);
        //}
        //Globals.allDamage = 200000;
        laser.SetIsActive(false);
        checkDistance = false;
        laserOn = false;

        healthBar.gameObject.SetActive(false);
        //healthBar.setCascadeOpacityEnabled(true);
        //healthBar.setOpacity(0.05f);

        enemySprite.state.SetAnimation(0, "death2", false);

        //if (isFtueBoss)
        //{
        //    Globals.bossPosition = enemySprite.transform.position;
        //    Globals.zoomToBossForSec = 1f;
        //}

        //DOTween.Sequence().AppendCallback(() =>
        //{
        //    GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss,
        //                                    new Vector3(transform.position.x + Globals.CocosToUnity(25),
        //                                    transform.position.y + Globals.CocosToUnity(25), 0),
        //                                    false, 1, 2, 0);
        //}).AppendInterval(3)
        //.AppendCallback(() =>
        //{
        //    if (isFtueBoss)
        //    {
        //        Globals.endFtue = true;
        //        GameManager.instance.SpawnEndingDialogues();
        //    }

        //}).Play();

        //StartCoroutine(RemoveSelf());
        //AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.SpiderCatFall);

        //Shared::playSound("res/Sounds/Bosses/Boss6/SpiderCatFall.mp3");
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }

    IEnumerator RemoveSelf()
    {
        yield return new WaitForEndOfFrame();
        GameSharedData.Instance.enemyList.Remove(this);
    }
	
    void CheckReEntries()
    {
        if (GameSharedData.Instance.enemyList.Count == 1 && currentState == TripodStates.Spider1)
        {
            Observer.DispatchCustomEvent("ChangeBossState");

            CancelInvoke("CheckReEntries");

            currentState = TripodStates.Laser1;
            enemySprite.state.SetAnimation(0, "laserMode", false);
            Land();

            //Globals.zoomValueWhileGame = Globals.CocosToUnity(1000);

            {
                LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                node.CreateWithType(SpiderType.LASER);
                node._allowKillPoint = false;
                node.healthMultiplier = 2;
                node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                Ping.Init(node.transform, true);
            }
            {
                LittleSpiders node = GameManager.instance.SpawnEnemy("LITTLESPIDERS", null, true) as LittleSpiders;
                node.CreateWithType(SpiderType.LASER);
                node._allowKillPoint = false;
                node.healthMultiplier = 2;
                node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                Ping.Init(node.transform, true);
            }
        }

        if (GameSharedData.Instance.enemyList.Count == 1 && currentState == TripodStates.Spider2)
        {
            if (stats.health < stats.maxHealth.Value * 0.45f)
            {
                Observer.DispatchCustomEvent("ChangeBossState");

                currentState = TripodStates.Laser2;
                enemySprite.state.AddAnimation(0, "laserMode", false);
                //laser.setLaserScale(55, 8.0f);

                Land();

                {
                    BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                    node._allowKillPoint = false;
                    node.boundaryPosX = Globals.LEFTBOUNDARY + Globals.CocosToUnity(450);
                    node.AllowBoundaryChange = true;
                    node._isLeft = 3;
                    node.healthMultiplier = 20;
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                    PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    Ping.Init(node.transform, true);
                }
                {
                    BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                    node._allowKillPoint = false;
                    node.boundaryPosX = Globals.LEFTBOUNDARY + Globals.CocosToUnity(4500);
                    node.AllowBoundaryChange = true;
                    node._isLeft = 0;
                    node.healthMultiplier = 20;
                    node.SetAttributeByLord(stats.bulletDamage, stats.maxHealth.Value);
                    PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                    Ping.Init(node.transform, true);
                }
            }
        }
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (currentState == TripodStates.Spider1 || currentState == TripodStates.Spider2)
        {
            return false;
        }

        if (Vector2.Distance(transform.position, P1) < Globals.CocosToUnity(400))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    float GetPosX()
    {
        // if attack phases check
        if (currentState == TripodStates.Laser2 || currentState == TripodStates.Final)
        {
            var bone = enemySprite.Skeleton.FindBone("root");
            armPosX = transform.position.x + bone.WorldX;

        }

        return armPosX;
    }
    float GetPosY()
    {
        // if attack phases check
        if (currentState == TripodStates.Laser2 || currentState == TripodStates.Final)
        {
            var bone = enemySprite.Skeleton.FindBone("root");
            armPosX = transform.position.y + bone.WorldY;
        }
        return armPosY;

    }

}
