using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
using Spine;
public class Soldier : Mono<PERSON>ehaviour
{
    public SkeletonAnimation enemySprite;
    [SerializeField] private Sprite bulletSprite;

    private Bone bone;

    public void Run()
    {
        enemySprite.state.SetAnimation(0, "run", true);
        enemySprite.state.TimeScale =0.6f + Random.value * 0.8f;
    }

    private void Shoot()
    {

        bone = enemySprite.skeleton.FindBone("gunShoot");
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        //bullet.duration = 2;
        //bullet.setRadiusEffectSquared(1);
        //bullet.setDamage(15);
        bullet.SetSpriteFrame(bulletSprite);

        bullet.transform.SetScale(0.5f);
        //bullet.setAnchorPoint(cocos2d::Point(0.5, 0.5));
        //bullet.setCameraMask((unsigned int)CameraFlag::USER5);


        if (transform.localScale.x > 0)
        {
            bullet.transform.position = bone.GetWorldPosition(transform);// (_enemySprite.getPosition().x + _bone.WorldX, _enemySprite.getPosition().y + _bone.WorldY);
            //bullet.setRotation(spBone_getWorldRotationX(_bone) + 30);
            bullet.transform.SetRotation(bone.WorldRotationX + 30);
        }
        else
        {
            bullet.transform.position = bone.GetWorldPosition(transform);
            //bullet.setPosition(_enemySprite.getPosition().x - _bone.WorldX, _enemySprite.getPosition().y + _bone.WorldY);
            //bullet.setRotation(spBone_getWorldRotationX(_bone) - 90);
            bullet.transform.SetRotation(bone.WorldRotationX - 90);
        }

        Vector2 dest = new Vector2(Globals.CocosToUnity(2500) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), Globals.CocosToUnity(2500) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation()));
        bullet.PlayBulletAnim(dest);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }


    public void Init()
    {
        enemySprite.state.SetAnimation(0, "fire", true);
        enemySprite.state.TimeScale = 0.6f;
        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
          {
              if (spineEvent.Data.Name == "gunShoot")
              {
                  Shoot();
              }
          };
    }
}
