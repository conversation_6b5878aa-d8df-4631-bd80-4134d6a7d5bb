using System.Collections;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
public class BugBot : Enemy
{
    [SerializeField] private Laser laserObj = null;
    [SerializeField] private GameObject blast;
    private float detonationDistance = 5f;
    private bool isLaserDroid = false;
    private bool isLeft = false;
    private Vector2 stickyPos = Vector2.zero;
    private float stickyTime = 0;
    //private Bone bone = null;
    private bool isDead = false;
    private bool isStuck = false;
    private float rotationAngle;
    private bool isBotDestroyed = false;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        detonationDistance = 1f;
        enemySprite.gameObject.SetActive(true);
        tweenId = "BugBot" + GetInstanceID().ToString();
        schedulerId = "BugBotS" + GetInstanceID().ToString();
        allowRelocate = false;
        if (!isLaserDroid)
        {
            allowPushBack = true;
        }
        _allowKillPoint = true;
        InitStats();
        enemyCollisionRadius = 1;
        _jsonScale = 1f;
        enemySprite.state.Event += HandleSpineEvent;
        //enemySprite.transform.rotation = Quaternion.Euler(0, 0, Random.value * 360);
        //bone = enemySprite.skeleton.FindBone("root");
        //bone.rotation = Random.value * 360;
        rotationAngle = enemySprite.transform.eulerAngles.z;

        if (isLaserDroid)
        {
            laserObj.Init();
            laserObj.setDamage = 10.0f;
            laserObj.SetLaserScale(23, 1f);
            laserObj.setAllowSound = true;
            laserObj.SetAllowShrink(false);
            laserObj.SetIsActive(false);
        }
        else
        {
            laserObj.gameObject.SetActive(false);
        }

        if (stickyTime > 0)
        {
            DOTween.Sequence().SetId(schedulerId).AppendInterval(stickyTime).AppendCallback(() =>
            {
                stickyPos = Vector2.zero;
            }).Play();
        }

        if (isLaserDroid)
        {
            stats.turnSpeed = 20;
            stats.speed = 20;
            enemyCollisionRadius = 0.01f;

            enemySprite.skeleton.SetAttachment("shield", "shield");

        }
        else
        {
            DestroyAfter(12);
        }
        scheduleUpdate = true;
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        //if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)TODO
        //{
        if (spineEvent.Data.Name == "laserOn")
        {
            if (isLeft)
            {
                AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.flameThrowerShoot);
                //Globals.PlaySound("res/Sounds/SFX/flameThrowerShoot.mp3");
            }
            if (laserObj)
            {
                laserObj.SetIsActive(true);
            }
        }
        if (spineEvent.Data.Name == "destroy")
        {
            KillCount();
            scheduleUpdate = false;
            CancelInvoke();
            enemySprite.gameObject.SetActive(false);
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, transform.position, false, 1, 2.5f, 0);
            DOTween.Kill(tweenId);

            DOTween.Sequence().SetId(tweenId).AppendInterval(1f).AppendCallback(Destroy).Play();
            //explosion.generateParticlesAt(Explosion::ExplosionType::ExplosionTypeAir2, enemySprite.getPosition(), false, 1, 2.5f, 100);
            //this.addChild(explosion);
            //this.stopAllActions();
            //this.runAction(Sequence::create(DelayTime::create(1.0f), CallFunc::create([this](){
            //    this.Destroy();
            //}), NULL));
        }
        //}
    }

    public void InitWithStickyPositionFor(Vector2 pos, float time)
    {
        stickyPos = pos;
        stickyTime = time;
        Init();
    }

    public void InitAsLaserDroids(Vector2 pos, bool left)
    {
        stickyPos = pos;
        isLeft = left;
        isLaserDroid = true;
        healthBar = null;
        Init();
        isBotDestroyed = true;
    }

    public void TurnOnLaserFor(float time, float afterTime)
    {

        DOTween.Sequence().SetId(tweenId).AppendInterval(afterTime).AppendCallback(() =>
        {

            enemySprite.state.SetAnimation(0, "laserLoad", false);
            enemySprite.state.AddAnimation(0, "laserShoot", true);
            const float animationTime = 2.0f;
            DOTween.Sequence().AppendInterval(time + animationTime).AppendCallback(() =>
            {

                enemySprite.state.SetAnimation(0, "idle", true);
                if (laserObj)
                {
                    laserObj.SetIsActive(false);
                }
            }).Play();

        }).Play();

    }

    public void DestroyAfter(float delay)
    {
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(delay).AppendCallback(() =>
        {
            isDead = true;
            isBotDestroyed = true;
            enemySprite.state.SetAnimation(0, "destroy", false);
            if (laserObj)
            {
                laserObj.SetIsActive(false);
            }
        }).Play();
    }
    //不走physicsManager的计数，这里要加计数以及刷波数

    public void KillCount()
    {
        if (!_allowKillPoint) return;
        GameData.instance.fileHandler.totalKills++;
        GameManager.instance.killsThisRun++;
        GameManager.instance.playerHud.survivalCurPoint = GameManager.instance.killsThisRun;
        if (Globals.gameType == GameType.Survival)
        {

            if (GameManager.instance.killsThisRun == (Globals.totalEnemiesInCurrentWave + Globals.enemiesTillLastWave))
            {

                Globals.enemiesTillLastWave += Globals.totalEnemiesInCurrentWave;
                DOTween.Sequence().AppendInterval(1f).AppendCallback(
                    () =>
                    {
                        Observer.DispatchCustomEvent("newWave");
                    }).Play();
            }
        }
    }
    public void Restat()
    {
        stats = new Attributes();

        if (Random.value > 0.5f)
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1500), transform.position.y);
        }
        else
        {
            transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1500), transform.position.y);
        }
        stats.speed = 5;

        stats.health = 3 + GameData.instance.fileHandler.currentMission * 2;
        stats.turnSpeed = stats.speed / 1.5f;
        stats.bulletDamage = 5 + GameData.instance.fileHandler.currentMission * 3;
        stats.regen = 0;
        stats.xp = 0;
        stats.coinAwarded = 0;
        stats.missileDamage = 4;
        stats.maxHealth.Value = stats.health;
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            OnCollision();
            if (!isLaserDroid)
            {
                Vector2 toPos;
                if (stickyPos != Vector2.zero)
                {
                    toPos = stickyPos;
                }
                else
                {
                    toPos = player.transform.position;
                }

                float dir = Vector2.SignedAngle(Vector2.right, Vector2.one * transform.position - toPos);
                float rotationDir = dir < 0 ? 360 + dir : dir;
                rotationAngle = Globals.MoveAngleTowards(rotationAngle, rotationDir, Time.deltaTime * stats.turnSpeed * 10f);
                var rot = Quaternion.AngleAxis(rotationAngle, Vector3.forward);
                enemySprite.transform.rotation = rot;
                stats.speed += 0.005f;
                transform.position = new Vector2(transform.position.x + (Mathf.Cos(Mathf.Deg2Rad * (-enemySprite.transform.eulerAngles.z)) * Time.deltaTime * stats.speed * 0.6f) * -1
                    , transform.position.y + Mathf.Sin(Mathf.Deg2Rad * (-enemySprite.transform.eulerAngles.z)) * Time.deltaTime * stats.speed * 0.6f);
            }
            else
            {

                if (isStuck)
                {
                    transform.SetWorldPositionX(transform.position.x + ((-transform.position.x + (stickyPos.x + player.transform.position.x)) * Time.deltaTime * stats.speed));
                    transform.SetWorldPositionY(transform.position.y + ((-transform.position.y + stickyPos.y) * Time.deltaTime * stats.speed));
                    if (isLeft)
                    {
                        enemySprite.transform.eulerAngles = new Vector3(0, 0, 180);

                    }
                    else
                    {
                        enemySprite.transform.eulerAngles = Vector3.zero;
                    }
                    //bone.rotation = angle;
                    //print(bone.rotation);
                }
                else if (Vector2.Distance(transform.position, new Vector2(stickyPos.x + player.transform.position.x, stickyPos.y)) > Globals.CocosToUnity(100f))// enemySprite.getPosition().distance(cocos2d::Point(_stickyPos.x + Player::getInstance().getPosition().x, _stickyPos.y)) > 100)
                {

                    Vector2 toPos;
                    if (stickyPos != Vector2.zero)
                    {
                        toPos.x = stickyPos.x + player.transform.position.x;
                        toPos.y = stickyPos.y;
                    }
                    else
                    {
                        toPos = player.transform.position;
                    }
                    float dir = Vector2.SignedAngle(Vector2.right, Vector2.one * transform.position - toPos);
                    float rotationDir = dir < 0 ? 360 + dir : dir;
                    rotationAngle = Globals.MoveAngleTowards(rotationAngle, rotationDir, Time.deltaTime * stats.turnSpeed * 100f);
                    var rot = Quaternion.AngleAxis(rotationAngle, Vector3.forward);
                    enemySprite.transform.rotation = rot;
                    stats.speed += 0.005f;
                    transform.position = new Vector2(transform.position.x + (Mathf.Cos(Mathf.Deg2Rad * (-enemySprite.transform.eulerAngles.z)) * Time.deltaTime * stats.speed) * -1
                        , transform.position.y + Mathf.Sin(Mathf.Deg2Rad * (-enemySprite.transform.eulerAngles.z)) * Time.deltaTime * stats.speed);

                    if (laserObj)
                    {
                        laserObj.SetIsActive(false);
                    }
                }
                else
                {
                    isStuck = true;
                }

            }


            if (laserObj)
            {
                laserObj.GetLaser().transform.position = transform.position;// (enemySprite.getPosition());
                laserObj.SetLaserRotation(180);
            }
        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = 10 + Random.value * 3f;
        stats.health = 350;
        stats.turnSpeed = stats.speed / 2;
        stats.bulletDamage = GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 50;
        stats.regen = 0;
        stats.xp = 50;
        stats.coinAwarded = 0;
        stats.missileDamage = 4;
        stats.maxHealth.Value = stats.health;


        baseStats.health = 350;
        baseStats.turnSpeed = stats.speed / 2;
        baseStats.bulletDamage = GameData.instance.fileHandler.currentEvent != (int)EventBoss.kBossTinyBots ? 1 : 50;
        baseStats.regen = 0;
        baseStats.xp = 50; ;
        baseStats.coinAwarded = 0;
        baseStats.missileDamage = 4;
        baseStats.maxHealth.Value = baseStats.health;
    }

    public void SetBulletDamage(double damage)
    {
        baseStats.bulletDamage = damage;
        stats.bulletDamage = damage;
        if (isLaserDroid)
        {
            laserObj.setDamage = damage / 10.0f;
        }
    }

    private void OnCollision()
    {
        if (Vector2.SqrMagnitude(player.transform.position - transform.position) < detonationDistance && player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING && !isLaserDroid && !isDead)
        {
            scheduleUpdate = false;
            enemySprite.gameObject.SetActive(false);
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, transform.position, false, 1, 2.5f, 0);
            KillCount();
            blast.SetActive(true);
            DOTween.Sequence().SetId("blast" + tweenId).Append(blast.transform.DOScale(new Vector2(2.5f, 2.5f), 0.2f)).AppendInterval(0.1f).AppendCallback(() => { blast.SetActive(false); }).Play();
            float colorVal = 1;
            DOTween.To(() => colorVal, x => colorVal = x, 0, 0.3f).OnUpdate(() =>
            {
                blast.GetComponent<Renderer>().material.color = new Color(1, 1, 1, colorVal);
            });
            //DOTween.Sequence().SetId("blast" + tweenId).Append(blast.GetComponent<Renderer>().material.DOFade(0, 0.3f).OnUpdate(()=>
            //{

            //    print(blast.GetComponent<Renderer>().material.color);
            //})).Play();
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.5f).AppendCallback(Destroy).Play();
            player.GotHit(stats.bulletDamage);
            Observer.DispatchCustomEvent("Heal");
        }
    }

    public override bool TakeHit(double damage)
    {

        if (healthBar)
        {
            stats.health -= damage;
            healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
            //healthBar.SetVisible(true);
            //healthBar.stopAllActions();
            //healthBar.runAction(Sequence::create(DelayTime::create(3), Hide::create(), NULL));

            enemySprite.GetComponent<Renderer>().material.DOKill();
            enemySprite.GetComponent<Renderer>().material.color = Color.red;
            enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
        }

        if (stats.health < 0)
        {

            isBotDestroyed = true;
            DestroyAfter(0.3f);
            healthBar.gameObject.SetActive(false);
            enemySprite.state.TimeScale = 5.0f;
        }

        return false;
    }

    private void LateUpdate()
    {
        if (isBotDestroyed && isDead)
        {
            GameSharedData.Instance.enemyList.Remove(this);
            isBotDestroyed = false;
        }
    }

}
