﻿using System;
using System.Collections.Generic;

namespace Apq
{
    /// <summary>
    /// 全局对象
    /// </summary>
    public static class GlobalObject
    {
        #region NamedInstances

        /// <summary>
        /// 命名实例集合
        /// </summary>
        public static Dictionary<string, object> NamedInstances { get; } = new();
        #endregion

        #region SingletonInstances

        private static readonly Dictionary<Type, object> LockObjects_Singleton = new();
        private static readonly Dictionary<Type, object> SingletonInstances = new();

        /// <summary>
        /// 获取或添加单态实例
        /// </summary>
        /// <param name="factory">提供创建实例的方法</param>
        public static T? GetOrAddSingleton<T>(Func<T>? factory = null) where T : class, ISingleton<T>, new()
        {
            if (!LockObjects_Singleton.TryGetValue(typeof(T), out var lockObj))
            {
                lockObj = new object();
                LockObjects_Singleton.Add(typeof(T), lockObj);
            }

            lock (lockObj)
            {
                if (SingletonInstances.TryGetValue(typeof(T), out var rtn)) return rtn as T;

                rtn = (factory?.Invoke() ?? new T()).Init();
                if (rtn != null)
                {
                    SingletonInstances.Add(typeof(T), rtn);
                }

                return rtn as T;
            }
        }

        #endregion
    }
}
