﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine;
using Spine.Unity;
using UnityEngine.UI;
public static class ExtentionMethods
{
    public static void SetLocalPosition(this Transform trans, float xVal, float yVal)
    {
        trans.localPosition = new Vector3(xVal, yVal, trans.localPosition.z);
    }

    public static void SetLocalPositionX(this Transform trans, float xVal)
    {
        trans.localPosition = new Vector3(xVal, trans.localPosition.y, trans.localPosition.z);
    }

    public static void SetLocalPositionY(this Transform trans, float yVal)
    {
        trans.localPosition = new Vector3(trans.localPosition.x, yVal, trans.localPosition.z);
    }

    public static void SetLocalPositionZ(this Transform trans, float zVal)
    {
        trans.localPosition = new Vector3(trans.localPosition.x, trans.localPosition.y, zVal);
    }

    public static float GetLocalPositionX(this Transform trans)
    {
        return trans.localPosition.x;
    }

    public static float GetLocalPositionY(this Transform trans)
    {
        return trans.localPosition.y;
    }

    public static float GetLocalPositionZ(this Transform trans)
    {
        return trans.localPosition.z;
    }

    public static void SetWorldPosition(this Transform trans, float xVal,float yVal)
    {
        trans.position = new Vector3(xVal, yVal, trans.position.z);
    }

    public static void SetWorldPositionX(this Transform trans, float xVal)
    {
        trans.position = new Vector3(xVal, trans.position.y, trans.position.z);
    }

    public static void SetWorldPositionY(this Transform trans, float yVal)
    {
        trans.position = new Vector3(trans.position.x, yVal, trans.position.z);
    }

    public static void SetWorldPositionZ(this Transform trans, float zVal)
    {
        trans.position = new Vector3(trans.position.x, trans.position.y, zVal);
    }

    public static float GetWorldPositionX(this Transform trans)
    {
        return trans.position.x;
    }

    public static float GetWorldPositionY(this Transform trans)
    {
        return trans.position.y;
    }

    public static float GetWorldPositionZ(this Transform trans)
    {
        return trans.position.z;
    }

    public static void SetRotation(this Transform trans, float val)
    {
        trans.rotation = Quaternion.Euler(0, 0, val);
    }

    public static float GetRotation(this Transform trans)
    {
        return trans.eulerAngles.z;
    }

    public static void SetScale(this Transform trans, float val)
    {
        trans.localScale = new Vector3(val, val, val);
    }

    public static void SetScaleX(this Transform trans ,float xVal)
    {
        trans.localScale = new Vector3(xVal, trans.localScale.y, trans.localScale.z);
    }

    public static void SetScaleY(this Transform trans, float yVal)
    {
        trans.localScale = new Vector3(trans.localScale.x, yVal, trans.localScale.z);
    }

    public static void DOFade(this SkeletonAnimation animation, float value, float duration , float easeDuration =0)
    {
        foreach (Material mat in animation.GetComponent<Renderer>().materials)
        {
            mat.DOFade(value, duration).SetEase(Ease.Linear,easeDuration,1);
        }
    }

    public static void SetOpacity(this SkeletonAnimation animation, float value)
    {
        foreach (Material mat in animation.GetComponent<Renderer>().materials)
        {
            mat.color = new Color(mat.color.r, mat.color.g, mat.color.b, value);
        }
    }

    public static Vector2 GetMidPoint(this Vector2 a, Vector2 b)
    {
        Vector2 result;
        result.x = (a.x + b.x) / 2;
        result.y = (a.y + b.y) / 2;
        return result;
    }

    public static Texture2D ToTexture2D(this RenderTexture rTex)
    {
        Texture2D tex = new Texture2D(rTex.width, rTex.height, TextureFormat.RGB24, false);
        var old_rt = RenderTexture.active;
        RenderTexture.active = rTex;

        tex.ReadPixels(new Rect(0, 0, rTex.width, rTex.height), 0, 0);
        tex.Apply();

        RenderTexture.active = old_rt;
        return tex;
    }

    public static void DoBlink(this GameObject go, float duration, int numberOfBlinks)
    {
        DOTween.Sequence().AppendCallback(() =>
        {
            go.SetActive(false);
        }).AppendInterval(duration/2).AppendCallback(() =>
        {
            go.SetActive(true);
        }).AppendInterval(duration/2).SetLoops(numberOfBlinks).Play();
    }

    public static void SetOpacity(this SpriteRenderer ren, float opacity)
    {
        ren.color = new Color(ren.color.r, ren.color.g, ren.color.b, opacity / 255);
    }

    public static void SetOpacity(this Image img, float opacity)
    {
        img.color = new Color(img.color.r, img.color.g, img.color.b, opacity / 255);
    }
}
