using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public enum GameObjectPool
{
    Coin,
}
public class GameObjectPoolManager : Singleton<GameObjectPoolManager>
{
    [HideInInspector] public Dictionary<int, GameObject> MedicamentPrefab = new Dictionary<int, GameObject>();

    public Dictionary<string, MMSimpleObjectPooler> AllGameObjectPool = new Dictionary<string, MMSimpleObjectPooler>();

    public GameObject GetItemFromPooler(string type)
    {
        if (!AllGameObjectPool.ContainsKey(type))
        {
            GameObject go = new GameObject(type);
            go.transform.parent = transform;
            MMSimpleObjectPooler mMSimpleObjectPooler = go.AddComponent<MMSimpleObjectPooler>();
            GameObject res = HotResManager.ReadModel(type);
            mMSimpleObjectPooler.InitPooler(res);
            AllGameObjectPool.Add(type, mMSimpleObjectPooler);
        }
        return AllGameObjectPool[type].GetPooledGameObject();
    }
    public GameObject GetItemFromPooler(GameObject prefab)
    {
        string type = prefab.name; 
        if (!AllGameObjectPool.ContainsKey(type))
        {
            GameObject go = new GameObject(type);
            go.transform.parent = transform;
            MMSimpleObjectPooler mMSimpleObjectPooler = go.AddComponent<MMSimpleObjectPooler>();
            mMSimpleObjectPooler.InitPooler(prefab);
            AllGameObjectPool.Add(type, mMSimpleObjectPooler);
        }
        return AllGameObjectPool[type].GetPooledGameObject();
    }

    public MMSimpleObjectPooler CreaterPool(string url)
    {
        GameObject go = new GameObject(url);
        MMSimpleObjectPooler mMSimpleObjectPooler = go.AddComponent<MMSimpleObjectPooler>();
        GameObject res = HotResManager.ReadModel(url);
        mMSimpleObjectPooler.InitPooler(res);
        AllGameObjectPool.Add(url, mMSimpleObjectPooler);
        return mMSimpleObjectPooler;
    }
}
