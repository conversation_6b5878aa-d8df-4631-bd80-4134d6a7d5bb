using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using UnityEngine.UI;

public class NavigationButton : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerEnterH<PERSON><PERSON>, IPointerExitHandler, IPointerDownHandler, IPointerMoveHandler
{
    public static NavigationButton currentlySelected;

    public NavigationButton onUpSelect, onDownSelect, onLeftSelect, onRightSelect, onSubmitSelect, onBackSelect;
    public UnityEvent onPointerEnter, onPointerExit, onPointerDown, onPointerMove, onSubmit, onBack, onSelect, onDeselect;

    private Button button;
    private DefaultInputActions inputActions;

    private void Awake()
    {
        inputActions = new DefaultInputActions();
        button = GetComponent<Button>();
    }

    private void OnEnable()
    {
        inputActions.UI.Navigate.performed += Move;
        inputActions.UI.Navigate.Enable();

        inputActions.UI.Submit.performed += Submit;
        inputActions.UI.Submit.Enable();

        inputActions.UI.Cancel.performed += Back;
        inputActions.UI.Cancel.Enable();
    }
    private void OnDisable()
    {
        inputActions.UI.Navigate.performed -= Move;
        inputActions.UI.Navigate.Disable();

        inputActions.UI.Submit.performed -= Submit;
        inputActions.UI.Submit.Disable();

        inputActions.UI.Cancel.performed -= Back;
        inputActions.UI.Cancel.Disable();
    }

    void Move(InputAction.CallbackContext obj)
    {
        if (currentlySelected != this)
            return;

        Vector2 val = obj.ReadValue<Vector2>();

        if (val == Vector2.zero)
            return;

        if (val.y > 0)
            MoveUp();
        else if (val.y < 0)
            MoveDown();
        else if (val.x > 0)
            MoveRight();
        else if (val.x < 0)
            MoveLeft();
    }

    void MoveUp()
    {
        NavigationButton navButton = GetUp();

        if (!navButton)
            navButton = this;

        ChangeCurrentlySelected(navButton, true);
    }
    void MoveDown()
    {
        NavigationButton navButton = GetDown();

        if (!navButton)
            navButton = this;

        ChangeCurrentlySelected(navButton, true);
    }
    void MoveRight()
    {
        NavigationButton navButton = GetRight();

        if (!navButton)
            navButton = this;

        ChangeCurrentlySelected(navButton, true);
    }
    void MoveLeft()
    {
        NavigationButton navButton = GetLeft();

        if (!navButton)
            navButton = this;

        ChangeCurrentlySelected(navButton, true);
    }

    void Submit(InputAction.CallbackContext obj)
    {
        if (currentlySelected != this)
            return;

        var navButton = GetNext();

        if (navButton)
            ChangeCurrentlySelected(navButton, true);

        onSubmit?.Invoke();
    }

    void Back(InputAction.CallbackContext obj)
    {
        if (currentlySelected != this)
            return;
        if(onBackSelect)
            ChangeCurrentlySelected(onBackSelect, true);

        onBack?.Invoke();
    }

    public NavigationButton GetNext()
    {
        if (!onSubmitSelect)
            return null;

        var navButton = onSubmitSelect.gameObject.activeInHierarchy ? onSubmitSelect : null;

        if (navButton)
            return navButton;

        navButton = onSubmitSelect.GetUp();

        if (navButton)
            return navButton;

        navButton = onSubmitSelect.GetDown();

        return navButton;
    }

    public NavigationButton GetUp()
    {
        if (!onUpSelect)
            return null;

        if (onUpSelect.gameObject.activeInHierarchy)
            return onUpSelect;
        else
            return onUpSelect.GetUp();
    }
    public NavigationButton GetDown()
    {
        if (!onDownSelect)
            return null;

        if (onDownSelect.gameObject.activeInHierarchy)
            return onDownSelect;
        else
            return onDownSelect.GetDown();
    }
    public NavigationButton GetLeft()
    {
        if (!onLeftSelect)
            return null;

        if (onLeftSelect.gameObject.activeInHierarchy)
            return onLeftSelect;
        else
            return onLeftSelect.GetLeft();
    }
    public NavigationButton GetRight()
    {
        if (!onRightSelect)
            return null;

        if (onRightSelect.gameObject.activeInHierarchy)
            return onRightSelect;
        else
            return onRightSelect.GetRight();
    }

    public void Select()
    {
        if(onSelect.GetPersistentEventCount() > 0)
        {
            onSelect.Invoke();
            return;
        }
        
        if (button)
        {
            button.Select();
        }
    }

    public void Deselect()
    {
        if (onDeselect.GetPersistentEventCount() > 0)
        {
            onDeselect.Invoke();
            return;
        }

        if (button && EventSystem.current != null)
        {
            EventSystem.current.SetSelectedGameObject(null);
        }
    }

    public static void ChangeCurrentlySelected(NavigationButton navButton, bool select = false)
    {
        currentlySelected.Deselect();

        if (!currentlySelected.gameObject.activeInHierarchy)
        {
            currentlySelected = navButton;

            if (select)
                currentlySelected.Select();

            return;
        }

        currentlySelected.ChangeSelected(navButton, select);
    }

    void ChangeSelected(NavigationButton navButton, bool select)
    {
        StartCoroutine(ChangeSelection(navButton, select));
    }

    IEnumerator ChangeSelection(NavigationButton navButton, bool select)
    {
        yield return new WaitForEndOfFrame();

        currentlySelected = navButton;

        if (select)
            currentlySelected.Select();
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        ChangeCurrentlySelected(this);

        onPointerEnter?.Invoke();
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        onPointerExit?.Invoke();
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        ChangeCurrentlySelected(this);

        onPointerDown?.Invoke();
    }

    public void OnPointerMove(PointerEventData eventData)
    {
        ChangeCurrentlySelected(this);

        onPointerMove?.Invoke();
    }
}