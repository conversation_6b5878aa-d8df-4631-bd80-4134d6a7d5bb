﻿using System.Threading;

using Cysharp.Threading.Tasks;
using HotScripts;
using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 怪物的激光束
    /// </summary>
    public class CreatureSkill_1003 : CreatureSkillBase
    {
        /// <summary>
        /// 使用技能的怪物
        /// </summary>
        protected Enemy Monster => Creature as Enemy;
        
        ///// <summary>
        ///// 预制件:激光端点
        ///// </summary>
        //public GameObject LaserPrefab_Impact { get; set; }
        ///// <summary>
        ///// 预制件:激光线条(矩形)
        ///// </summary>
        //public GameObject LaserPrefab_Rect { get; set; }

        /// <summary>
        /// 发出的激光
        /// </summary>
        public BulletProps Laser { get; protected set; }

        public override void ClearSkill()
        {
            base.ClearSkill();

            Laser?.Dispose();
        }

        public override async UniTaskVoid DoSkill()
        {
            base.DoSkill().Forget();
            try
            {
                await UniTask.SwitchToMainThread();

                // 激光最大长度、宽度
                var maxLength = Skill.攻击距离.Value;
                var width = 2 * Skill.Get子弹半径();

                // 持续时长：秒
                var attackDuration = Skill.持续时长.Value;
                // 造成伤害的最小间隔时长:秒
                var minDamageInterval = Skill.MinDamageInterval.Value;

                // 超时令牌
                var CTS_timeout = new CancellationTokenSource();
                CTS_timeout.CancelAfterSlim(System.TimeSpan.FromSeconds(attackDuration));

                // 当持续时长够了或攻击者死了，自动结束。(但对于发出的激光,至少要存在最小持续时长)
                CancellationTokenSource cts_Shoot = CancellationTokenSource.CreateLinkedTokenSource(
                    CTS_timeout.Token,
                    Creature.GetCancellationTokenOnDestroy()
                    );
                CTS_Shoots.Add(cts_Shoot);

                // 开火声音
                AudioPlayer.Instance.PlaySound(Skill.CsvRow_CatSkill.ShootSound).Forget();

                // 单条激光的任务
                DoLaser(cts_Shoot.Token, width,
                    attackDuration, minDamageInterval).Forget();
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        /// 单条激光
        /// </summary>
        /// <param name="width">激光宽度</param>
        /// <param name="attackDuration">持续时长：秒</param>
        /// <param name="minDamageInterval">造成伤害的最小间隔时长:秒</param>
        protected async UniTaskVoid DoLaser(CancellationToken token,
            float width,
            float attackDuration,
            float minDamageInterval)
        {
            //await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);
            await UniTask.SwitchToMainThread();

            // 攻击的结束时间
            float endTime = Time.time + attackDuration;

            try
            {
                for (; ; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested) break;
                    if (Time.time >= Preset_FinshTime) break;
                    if (Time.deltaTime <= 0) continue;

                    await DoLaser_Eject(token,
                        Creature.transform.position, width,
                        endTime,
                        minDamageInterval);
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
            {
                Laser.Dispose();
            }
        }

        protected async UniTask DoLaser_Eject(CancellationToken token,
            Vector3 pBegin,
            float width,
            float endTime,
            float minDamageInterval
            )
        {
            // 创建一条光线
            var laserLine = CreatureBase.CreateLaser(
                pBegin,
                Vector3.one,
                GameManager.instance.player.weapon.laserPrefab,
                GameManager.instance.player.weapon.laserImpactPrefab);
            laserLine.SetLaserActive(false);
            var sr = laserLine.LaserMiddle.GetComponent<SpriteRenderer>();
            sr.size = new Vector2(1, width);// 设置激光宽度
            laserLine.LaserMiddle.GetComponent<MaterialMovement>().ResetOffset();
            Laser.LaserLines.Add(laserLine);

            // 实际结束时间
            float eTime = endTime;

            try
            {
                // 攻击到结束时间或技能被强停
                for (; ; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested) break;
                    if (Time.time >= eTime) break;
                    if (Time.deltaTime <= 0) continue;

                    // 先隐藏激光,再显示
                    laserLine.SetLaserActive(false);

                    // 如果玩家没死，则显示激光(刷新)
                    if (GameManager.instance.player.FightProp.HP.Value > 0)
                    {
                        //// 调整实际结束时间(至少持续配置的最小时长)
                        //if (eTime <= endTime)//这个判断,确保最多调整一次
                        //{
                        //    var minEndTime = Time.time + minAttackDuration;
                        //    if (endTime < minEndTime)
                        //    {
                        //        eTime = minEndTime;
                        //    }
                        //}

                        // 显示激光（更新 起止位置 和 激光的方向、长度）
                        laserLine.LaserBegin.transform.position = Creature.transform.position;
                        laserLine.LaserMiddle.transform.position = GameManager.instance.player.transform.position;
                        laserLine.LaserEnd.transform.position = laserLine.LaserMiddle.transform.position;
                        var dir = laserLine.LaserEnd.transform.position - laserLine.LaserBegin.transform.position;
                        var dir_1 = dir.normalized;
                        laserLine.LaserBegin.transform.right = dir_1;
                        laserLine.LaserMiddle.transform.right = dir_1;
                        laserLine.LaserEnd.transform.right = -dir_1;
                        var distance = dir.magnitude;
                        laserLine.LaserMiddle.GetComponent<SpriteRenderer>().size = new Vector2(distance, width);
                        laserLine.LaserMiddle.GetComponent<MaterialMovement>().OffsetUpdate();
                        laserLine.SetLaserActive(true);

                        // 如果可以造成伤害的时间到了,则造成一次伤害
                        if (Laser.NextHitEnemyTime.Value <= Time.time)
                        {
                            Laser.NextHitEnemyTime.Value = Time.time + minDamageInterval;
                            //Debug.Log($"激光1003下次伤害时间: {laser.NextHitEnemyTime.Value}");

                            var maxRadius = Laser.DamageEnemy(new CreatureBase[] { GameManager.instance.player });
                        }
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
			{
				// 隐藏激光段
				laserLine.gameObject.SetActive(false);
            }
        }
    }
}
