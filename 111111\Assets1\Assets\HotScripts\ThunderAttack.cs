using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;

public class ThunderAttack : MonoBehaviour
{
    #region Variables
    public float _lightningNumber = 0;
    public float laserAngle = 0;
    public Vector2 laserPosition = Vector2.zero;
    public bool isDoingDamage = false;
    public int soundId = 0;
    public static float attackTime = 0;

    #endregion

    #region References
    [SerializeField] SkeletonAnimation endEffect;
    [SerializeField] Laser laserObj;
    [SerializeField] GameObject lightning;
    [SerializeField] GameObject strobeEffect;

    private string tweenId;
    private string schedulerId;
    #endregion
    // Start is called before the first frame update
    void Awake()
    {
        tweenId = "TA" + GetInstanceID();
        schedulerId = "TAS" + GetInstanceID();
    }

    public void CreateWithTime(float time)
    {
        attackTime = time;
        Init();
    }

    public void Init()
    {
        laserObj.Init();
        laserObj.setDamage=7.5f;
        laserObj.GetLaser().skeleton.SetColor(new Color(125/255, 249/255, 255/255, 26/255));
        //laserObj.GetLaser().skeleton.SetColor(Color.magenta);
        //laserObj.GetLaser().GetComponent<MeshRenderer>().material.color = new Color(125, 249, 255);
        //laserObj.GetLaser().GetComponent<MeshRenderer>().material.color = Color.magenta;
        //_laserObj->getLaser()->updateDisplayedColor(Color3B(125, 249, 255));
        //_laserObj->getLaser()->setColor(Color3B::MAGENTA);
        laserObj.SetLaserScale(40, 0.75f);
        laserObj.setAllowSound = true;
        laserObj.SetAllowShrink(true);
        laserObj.GetImpactBone().ScaleX = 0;
        laserObj.GetImpactBone().ScaleY = 0;
        laserObj.SetIsActive(false);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.5f).AppendCallback(()=>{
            laserObj.SetIsActive(true);

        }).AppendInterval(1.5f + attackTime).AppendCallback(()=>{
            //TODO
            //Shared::stopSound(_soundId);

        }).Play();
        //laserObj.GetLaser().skeleton.SetColor(new Color(255, 255, 255, 0));
        //_laserObj->getLaser()->setOpacity(0.1);
        //DOTween.Sequence().AppendInterval(0.5f).AppendCallback(()=>
        //{
        //    laserObj.SetIsActive(true);
        //});


        //this->runAction(Sequence::create(DelayTime::create(0.5), CallFunc::create([=]{
        //    _laserObj->setIsActive(true);

        //}) ,  DelayTime::create(1.5 + _attackTime), CallFunc::create([=]{

        //    Shared::stopSound(_soundId);

        //}) , NULL));

        laserObj.GetLaser().transform.position = laserPosition;
        laserObj.SetLaserRotation(360 - (laserAngle + (_lightningNumber * 45)));
        lightning.transform.SetParent(laserObj.GetLaser().transform);
        
        //lightning.transform.SetScaleX(4);
        
        lightning.transform.SetScaleX(4);
        lightning.transform.SetScaleY(laserObj.GetLaserBone().ScaleX);
        lightning.transform.SetRotation((laserAngle + (_lightningNumber * 45)) + 90);
        lightning.SetActive(true);
        //_lightning->setAnchorPoint(Vec2(0.5, 0.15f));
        //_lightning->runAction(Shared::createAnimation("Thunder%d.png", 1, 16, true));
        endEffect.state.SetAnimation(0, "zapperAttack", true);
        endEffect.gameObject.SetActive(false);
        endEffect.transform.SetScale(0.75f);

        StartCoroutine(Delay(0.5f));
        lightning.GetComponent<SpriteRenderer>().color = new Color(125, 249, 255);

        //Sprite* alert = Sprite::create("res/Arsenal/strobe.png");
        //_laserObj->addChild(alert);
        //alert->setScaleY(24);
        //alert->setScaleX(1);

        //alert->setAnchorPoint(Vec2(0.5, 0.1));
        //alert->setColor(Color3B(125, 249, 255));

        //alert->setOpacity(0);
        //alert->setCameraMask(GAMECAMERA);
        //alert->runAction(Sequence::create(CallFunc::create([=]{
        //    alert->setPosition(_laserObj->getLaser()->getPosition());
        //    alert->setRotation(_lightning->getRotation());

        //}) ,FadeIn::create(0.5),RemoveSelf::create(), NULL));
    }
    
    public void RotateLightning(float time, float totalAngle)
    {
        //float val = Mathf.Lerp(laserObj.GetLaserBone().Rotation, laserObj.GetLaserBone().Rotation + totalAngle, time / 2);
        float val = laserObj.GetLaserBone().Rotation;
        DOTween.Sequence().AppendInterval(0.5f).AppendCallback(() =>
            {
                DOTween.To(() => val, x => val = x, laserObj.GetLaserBone().Rotation + totalAngle, time / 2).OnUpdate(() =>
                {
                    laserObj.SetLaserRotation(val);
                    if (lightning)
                    {
                        lightning.transform.SetRotation((val * -1) + 90);
                    }
                }).OnComplete(() =>
                {
                    val = laserObj.GetLaserBone().Rotation + totalAngle;
                    DOTween.To(() => val, x => val = x, laserObj.GetLaserBone().Rotation, time / 2).OnUpdate(() =>
                    {
                        laserObj.SetLaserRotation(val);
                        if (lightning)
                        {
                            lightning.transform.SetRotation((val * -1) + 90);
                        }
                    }).OnComplete(() =>
                    {
                        if (laserObj)
                        {
                            laserObj.SetIsActive(false);
                        }
                        endEffect.gameObject.SetActive(false);
                    });//Destroy(gameObject, 0.1f));
                });
            }).Play();
            
        //this->runAction(Sequence::create(DelayTime::create(0.5), ActionFloat::create(time / 2, _laserObj->getLaserBone()->rotation, _laserObj->getLaserBone()->rotation + totalAngle, [=](float val){
        //laserObj.SetLaserRotation(val);
        //lightning.transform.SetRotation((val * -1) + 90);

        //val = Mathf.Lerp(laserObj.GetLaserBone().Rotation + totalAngle, laserObj.GetLaserBone().Rotation, time / 2);
        
        //Destroy(gameObject, 0.1f);
    }

    public void SetLaserRotation(float angle)
    {
        laserAngle = angle;
        laserObj.SetLaserRotation(360 - (laserAngle + (_lightningNumber * 45)));
        lightning.transform.SetRotation(laserAngle + (_lightningNumber * 45) + 90);
    }

    public void SetLaserPosition(Vector2 position)
    {
        laserPosition = position;
        laserObj.GetLaser().transform.position = laserPosition;
    }

    void Update()
    {
        //lightning.size = new Vector2(2, laserObj.GetLaserBone().ScaleX * 0.5f);
        lightning.transform.SetScaleY(laserObj.GetLaserBone().ScaleX * 0.5f);
        endEffect.transform.position = GameManager.instance.player.transform.position;
        if(laserObj.isActiveAndEnabled)
        {
            if (lightning.transform.localScale.y < 10 && !endEffect.gameObject.activeInHierarchy)
            {
                endEffect.gameObject.SetActive(true);
                AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.Spark);

                //_soundId = Shared::playSound("res/Sounds/SFX/Spark.mp3", true, 1.0f); TODO
            }
            else if (lightning.transform.localScale.y >= 10 && endEffect.gameObject.activeInHierarchy)
            {
                endEffect.gameObject.SetActive(false);

                //Shared::stopSound(_soundId);TODO
            }
        }
        else
        {
            endEffect.gameObject.SetActive(false);
        }
        
        //    _endEffect->setScale(1.0f/_lightning->getScaleX(),1.0f/_lightning->getScaleY());
    }

    public void DeactivateZapperEffect()
    {
        endEffect.gameObject.SetActive(false);
    }

    IEnumerator Delay(float delay)
    {
        yield return new WaitForSeconds(delay);
    }

    public Laser GetLaser()
    {
        return laserObj;
    }
}
