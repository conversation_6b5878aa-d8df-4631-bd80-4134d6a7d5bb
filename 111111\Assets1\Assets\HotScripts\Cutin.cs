using UnityEngine;
using Spine;
using Spine.Unity;
using System.Collections;
public class Cutin : Enemy
{
    private enum AnimState
    {
        Idle,
        Shooting
    }

    private AnimState animsState = AnimState.Idle;
    private int animationState;
    private bool movingLeft;

    private int shootCalls;
    private SpineSlot explotionEffect;
    private bool flipX;
    private Bone gun;

    [SerializeField] private Collider2D boundingBox;
    [SerializeField] private Sprite bulletSprite;

    private Bounds bounds;

    private IEnumerator NextFrameGetBounds()
    {
        yield return null;
        bounds = boundingBox.bounds;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        transform.position = new Vector2(player.transform.position.x + Random.value * 2 + 15, Globals.LOWERBOUNDARY);
        enemyCollisionRadius = Globals.CocosToUnity(500 * 500);
        InitStats();
        flipX = false;
        isBoss = false;
        StartCoroutine(NextFrameGetBounds());
        scheduleUpdate = true;
        StartCoroutine(UpdateBounds());
        shootCalls = 0;
        enemySprite.state.SetAnimation(0, "ship1", false);
        enemySprite.state.AddAnimation(0, "idleone", true,0);
        explosionType = Explosions.ExplosionType.ExplosionTypeShip;
        //if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKillShips && Globals.gameType == GameType.Training && player.Mode ==  PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        //{

        //    if (GameManager.instance.missionManager.totalPointsRequired != GameManager.instance.missionManager.totalPoints)
        //    {

                var ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                ping.Init(transform, true, PlayerPing.Type.Red);
                allowRelocate = false;

        //    }
        //}


        enemySprite.state.Event += HandleSpineEvent;

        InvokeRepeating(nameof(ChangeAnimation), 6, 6);
        InvokeRepeating(nameof(LaunchMissile), 3, 3);
    }
    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            Shoot();
        }
    }

    private IEnumerator UpdateBounds()
    {
        while (scheduleUpdate)
        {
            yield return new WaitForSeconds(0.3f);
            bounds = boundingBox.bounds;
        }
    }
    private void Update()
    {
        //healthBar.transform.position = new Vector2(transform.position.x, transform.position.y - 0.5f);  
        if (scheduleUpdate)
        {
            if (isBoss)
            {
                Globals.bossPosition = transform.position;
            }

            if (movingLeft)
            {
                transform.position = new Vector2(transform.position.x - stats.speed * Time.deltaTime * 0.6f, transform.position.y);
                if (transform.position.x - player.transform.position.x < -9)
                {

                    movingLeft = false;
                    FlipShip();
                }
            }
            else
            {
                transform.position = new Vector2(transform.position.x + stats.speed * Time.deltaTime * 0.6f, transform.position.y);
                if (transform.position.x - player.transform.position.x > 9)
                {

                    movingLeft = true;
                    FlipShip();

                }

            }
        }
    }

    private void FlipShip()
    {
        if (!flipX)
        {
            flipX = true;
            //        enemySprite.setScaleX(-1);
            enemySprite.skeleton.ScaleX = -1;
        }
        else
        {
            flipX = false;
            //        enemySprite.setScaleX(1);
            enemySprite.skeleton.ScaleX = 1;

        }
    }

    private void LaunchMissile()
    {
        HomingMissile missile = null;
        bool didFindMissile = false;
        foreach (HomingMissile m in GameSharedData.Instance.enemyHomingMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                missile.isInUse = true;
                didFindMissile = true;
                break;
            }

        }
        if (!didFindMissile)
        {
            return;
        }
        missile.Init();
        missile.SetDamage(stats.missileDamage);
        float dir = Vector2.SignedAngle(Vector2.right, player.transform.position - transform.position);
        float rotationDir = dir < 0 ? 360 + dir : dir;
        GameSharedData.Instance.enemyMissilesInUse.Add(missile);
        missile.transform.SetPositionAndRotation(transform.position, Quaternion.Euler(0,0, dir));
        missile.RemoveAfterDuration();
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = baseStats.speed = 4 + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = 1200;
        stats.bulletDamage = baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.xp = baseStats.xp = stats.maxHealth.Value;
        stats.coinAwarded = baseStats.coinAwarded = 20;
    }

    private void Shoot()
    {
        CreateBullet("gunBurst");
        CreateBullet("gunBurst2");
        shootCalls++;
        if (shootCalls == 8)
        {
            shootCalls = 0;
        }
    }

    private void CreateBullet(string boneName)
    {
        int mult = -1;
        if (flipX)
        {
            mult = -1;
        }
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.setDamage(stats.bulletDamage);
        bullet.SetSpriteFrame(bulletSprite) ;
        gun = enemySprite.skeleton.FindBone(boneName);
        bullet.transform.SetPositionAndRotation(gun.GetWorldPosition(enemySprite.transform), Quaternion.Euler(0, 0, shootCalls * 15 + 45 / 2 - 90));// bullet poisition to enemySprite ... will change to gun position;
        bullet.transform.localScale = Vector3.one;
        bullet.transform.rotation = Quaternion.Euler(0, 0, (mult * gun.Parent.Parent.Rotation + 90) * -1);
        if (movingLeft)
        {
        }
        else
        {
            //bullet.transform.rotation = Quaternion.Euler(0, 0, mult * gun.parent.parent.rotation + 90);
        }//        this.addChild(bullet) 
        
        Vector2 dest = new Vector2(Globals.CocosToUnity(1000) * Mathf.Sin(bullet.transform.eulerAngles.z * Mathf.Deg2Rad)*-1, Globals.CocosToUnity(1000) * Mathf.Cos(bullet.transform.eulerAngles.z * Mathf.Deg2Rad));

        bullet.PlayBulletAnim(3, dest);
        //bullet.setCameraMask(GAMECAMERA);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.gameObject.SetActive(true);
        bullet.setRadiusEffectSquared(1);
    }

    private void ChangeAnimation()
    {
        enemySprite.state.SetAnimation(0, "shoot", false);
        enemySprite.state.AddAnimation(0, "idleone", true,0);
    }


    public static void Create()
    {

    }

    public static void CreateAsBoss()
    {

    }

    private void Boss()
    {
        isBoss = true;
        stats.speed = 1.5f + Random.value;//bhut taiz
        stats.turnSpeed = 0.5f + Random.value;
        stats.health = (14 + 10) * 100;
        stats.bulletDamage = (14) * 3;
        stats.missileDamage = (14) * 10;
        stats.bulletSpeed = 7;
        stats.maxHealth.Value = stats.health;
        stats.coinAwarded = 15;
        stats.xp = stats.maxHealth.Value;
    }

    public override void Destroy()
    {
        //TODO
        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKillShips && (Globals.gameType == GameType.Training || Globals.gameType == GameType.Survival) && player.Mode ==  PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            GameManager.instance.missionManager.AddPoint();
        }

        //if (this.getReferenceCount() != 2)
        //{
        //    CCASSERT(0, "ref count must = to 2");
        //}
        base.Destroy();
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if(bounds != null)
        {
            return bounds.Contains(P1);
        }
        else
        {
            return false;
        }
    }
}

