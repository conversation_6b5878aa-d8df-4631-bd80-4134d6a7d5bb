using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using DG.Tweening;
using System;

public class Dialogue : MonoBehaviour
{
    public struct DialogueConfig
    {
        public string heading;
        public string path;
        public string battleString;
        //public TextVAlignment _vAlign;
        //public TextHAlignment _hAlign;
        public bool alignLeft;
        public bool exitButton;
        public bool pauseAll;
    }

    public Action onCloseDialogue, closeFaceDialogue;

    [SerializeField] RectTransform arrowRect;
    [SerializeField] TextMeshProUGUI headingTMP, dialogueTMP;

    string tweenID, schedulerID;

    RectTransform rectTransform;
    Vector3 initialPosition;
    Vector2 referenceResolution;
    DialogueConfig dialogueConfig;
    bool dialogueShowing;

    float targetX, targetY;

    private void Start()
    {
        rectTransform = GetComponent<RectTransform>();
        initialPosition = rectTransform.anchoredPosition; // Initial position is always Left aligned
        referenceResolution = GetComponentInParent<CanvasScaler>().referenceResolution;
        var sizeDelta = new Vector2(referenceResolution.x + rectTransform.sizeDelta.x,
            referenceResolution.y + rectTransform.sizeDelta.y);
        targetY = -referenceResolution.y / 2 - sizeDelta.y / 2 * 1.1f;

        tweenID = "DialogueTween";
        schedulerID = "DialogueScheduler";

        rectTransform.anchoredPosition = new Vector2(rectTransform.anchoredPosition.x, targetY);
    }

    public void Init(string heading, string path, string battleString, bool alignLeft = true,
        bool pauseAll = true, bool exitButton = false)
    {
        dialogueConfig = new DialogueConfig();
        dialogueConfig.heading = heading;
        dialogueConfig.path = path;
        dialogueConfig.battleString = battleString;
        dialogueConfig.alignLeft = alignLeft;
        dialogueConfig.exitButton = exitButton;
        dialogueConfig.pauseAll = pauseAll;
        dialogueShowing = false;

        headingTMP.text = heading;
        dialogueTMP.text = battleString;

        if (!rectTransform)
        {
            rectTransform = GetComponent<RectTransform>();
            initialPosition = rectTransform.anchoredPosition; // Initial position is always Left aligned
            referenceResolution = GetComponentInParent<CanvasScaler>().referenceResolution;
        }

        var sizeDelta = new Vector2(referenceResolution.x + rectTransform.sizeDelta.x,
            referenceResolution.y + rectTransform.sizeDelta.y);

        targetX = alignLeft ? initialPosition.x : -initialPosition.x;
        targetY = -referenceResolution.y / 2 - sizeDelta.y / 2 * 1.1f;

        rectTransform.anchoredPosition = new Vector3(targetX, targetY, 0);

        arrowRect.anchorMin = arrowRect.anchorMax = alignLeft ? new Vector2(1, 0) : new Vector2(0, 0);
        arrowRect.anchoredPosition = alignLeft
            ? new Vector2(-sizeDelta.x / 8, sizeDelta.y / 35)
            : new Vector2(sizeDelta.x / 8, sizeDelta.y / 35);
        arrowRect.localScale = alignLeft ? new Vector3(-1, 1, 1) : new Vector3(1, 1, 1);

        rectTransform.DOAnchorPos(new Vector2(targetX, initialPosition.y), 0.2f).SetUpdate(true).OnComplete(() =>
            {
                dialogueShowing = true;
            });
        GameManager.instance.timeManager.SetTimescale(0);
    }

    private void Update()
    {
        if (dialogueShowing)
        {
            if (UnityEngine.Input.GetMouseButtonDown(0)) CloseDialogue();
        }
    }

    void CloseDialogue()
    {
        dialogueShowing = false;

        closeFaceDialogue?.Invoke();
        rectTransform.DOAnchorPos(new Vector2(targetX, targetY), 0.2f).SetUpdate(true).OnComplete(() =>
        {
            GameManager.instance.timeManager.SetTimescale(1);
            onCloseDialogue?.Invoke();
        });
    }
}
