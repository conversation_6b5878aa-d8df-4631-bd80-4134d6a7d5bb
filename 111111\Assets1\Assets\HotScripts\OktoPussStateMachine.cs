using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public static class OktoConst
{
    public const int TRACK_REPEATING = 0;
    public const int TRACK_MAIN = 1;
    public const int TRACK_ALT = 2;
    public const float MELEE_ATTACK_DISTANCE = 1200f/103f;
    public const float PHASE_3_BOUNDARY_DISTANCE = 1000f/103f;
    public const float PHASE_4_BOUNDARY_DISTANCE = 1800f/103f;
}

public class OktoPussStateMachine : StateMachine
{
    public OktoPuss okto;
    OktoPussStateMachine(OktoPuss o)
    {
        okto = o;
    }

    public static OktoPussStateMachine Create(OktoPuss o)
    {
        OktoPussStateMachine osm = new OktoPussStateMachine(o);

        return osm;
    }
}

public class OktoStateEntry : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("entry2", "idle3", 0.1f);
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("entry2").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "entry2", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_REPEATING, "Glow and fan", true);

        okto.enemySprite.state.TimeScale =5;
        timeToRun = timeToRun / okto.enemySprite.state.TimeScale;
        //Debug.Log(okto.transform.position);
        Globals.bossPosition = new Vector2(okto.transform.position.x, okto.transform.position.y + Globals.CocosToUnity(500));
        //Globals.zoomValueOnBoss = 1000;
        //Globals.zoomToBossForSec = 0.4f;
        okto.BlockDamage=true;

    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateIdleGround>();
        }
    }

    public override void WillExitWithNextState(State nextState)
    {
        //okto.initHealthBar();
        okto.enemySprite.state.TimeScale =1;
        okto.BlockDamage =false;

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateEntry";
    }
}

public class OktoStateIdleGround : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("idle3").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "idle3", true);
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_ALT, "attack idle", false);
        currentTime = 0.0f;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        const float distanceToFollow = 2000/103;
        if (currentTime > timeToRun * 2)
        {
            GetStateMachine().EnterState<OktoStateWalkGround>();

        }

        if (okto.enemySprite.transform.position.x < okto.player.transform.position.x - distanceToFollow)
        {
            GetStateMachine().EnterState<OktoStateWalkGround>();

        }
        if (okto.enemySprite.transform.position.x > okto.player.transform.position.x + distanceToFollow)
        {
            GetStateMachine().EnterState<OktoStateWalkGround>();

        }
    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateIdleGround";
    }
}

public class OktoStateWalkGround : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("idle3").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase4", true);
    }

    public override void UpdateState()
    {
        const float distanceToTurn = 1200/103;
        if (okto.GetRootScaleX() > 0)
        {
            okto.transform.SetWorldPositionX(okto.transform.position.x - 8*Time.deltaTime);
            if (okto.transform.position.x < okto.player.transform.position.x - distanceToTurn)
            {
                okto.SetRootScaleX(-okto.GetRootScaleX());
                GetStateMachine().EnterState<OktoStateIdleGround>();

            }
        }
        else
        {
            okto.transform.SetWorldPositionX(okto.transform.position.x + 8*Time.deltaTime);
            if (okto.transform.position.x > okto.player.transform.position.x + distanceToTurn)
            {
                okto.SetRootScaleX(-okto.GetRootScaleX());
                GetStateMachine().EnterState<OktoStateIdleGround>();

            }
        }

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateWalkGround";
    }
}

public class OktoStateEntryPhase2 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("transition", "idle1", 0.2f);
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("idle3").Duration;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation("transition").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "idle3", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "transition", false);
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_ALT, "DisableGuns", false);
        currentTime = 0.0f;
        okto.BlockDamage = (true);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateIdlePhase2>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateEntryPhase2";
    }
}

public class OktoStateIdlePhase2 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;
    State previousState;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("transition", "idle1", 0.2f);
        okto.enemySprite.state.Data.SetMix("idle1", "dash_inplace", 0.2f);
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("idle1").Duration;

        okto.BlockDamage = (false);
    }

    public override void DidEnterWithPreviousState(State prevState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1", true);
        currentTime = 0.0f;
        previousState = prevState;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        //    'const float distanceToFollow = 2600;
        if (currentTime > timeToRun * 2)
        {
            if (previousState.GetStateType() == "OktoStateEntryPhase2")
            {
                GetStateMachine().EnterState<OktoStateTentacleAttack>();
                return;

            }
            if (previousState.GetStateType() != "OktoStateWalkPhase2")
            {
                GetStateMachine().EnterState<OktoStateWalkPhase2>();
                return;

            }
            float chance = Random.value;
            if (chance < 0.45f)
            {
                GetStateMachine().EnterState<OktoStateSummonJellyFish>();
            }
            else
            {
                GetStateMachine().EnterState<OktoStateDoubleRocket>();

            }


        }

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateIdlePhase2";
    }
}

public class OktoStateWalkPhase2 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("dash_inplace", "idle1 flip3", 0.1f);
        okto.enemySprite.state.Data.SetMix("idle1 flip3", "idle1", 0.25f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "dash_inplace").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "dash_inplace", false);
        okto.ScaleXCheck();
        currentTime = 0.0f;
        okto.BlockDamage = (true);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun - 0.17f)
        {
            okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1 flip3", false);
            GetStateMachine().EnterState<OktoStateIdlePhase2>();
            return;
        }
        const float distanceToTurn = 2000;
        if (okto.GetRootScaleX() > 0)
        {
            okto.transform.SetWorldPositionX(okto.transform.position.x - 16*Time.deltaTime);
            if (okto.transform.position.x > okto.player.transform.position.x)
            {
                okto.transform.SetWorldPositionX(okto.transform.position.x - 14*Time.deltaTime);

            }
            if (okto.transform.position.x < okto.player.transform.position.x - distanceToTurn)
            {

                okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1 flip3", false);

                GetStateMachine().EnterState<OktoStateIdlePhase2>();

            }

        }
        else
        {
            okto.transform.SetWorldPositionX(okto.transform.position.x + 16*Time.deltaTime);
            if (okto.transform.position.x < okto.player.transform.position.x)
            {
                okto.transform.SetWorldPositionX(okto.transform.position.x + 14*Time.deltaTime);

            }
            if (okto.transform.position.x > okto.player.transform.position.x + distanceToTurn)
            {
                okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1 flip3", false);
                GetStateMachine().EnterState<OktoStateIdlePhase2>();

            }
        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateWalkPhase2";
    }
}

public class OktoStateBlock : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;
    State prevState;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "block1").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        currentTime = 0.0f;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "block1", false);
        okto.BlockDamage = true;
        prevState = previousState;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateIdleGround>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateBlock";
    }
}

public class OktoStateBlockPhase2 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;
    State prevState;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "block2").Duration;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        currentTime = 0.0f;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "block2", false);
        okto.BlockDamage = (true);
        prevState = previousState;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateIdlePhase2>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateBlockPhase2";
    }
}

public class OktoStateSummonJellyFish : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "SummonJellyFish").Duration;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation( "idle2").Duration * 2;

        okto.enemySprite.state.Data.SetMix("idle1", "SummonJellyFish", 0.2f);
        okto.enemySprite.state.Data.SetMix("SummonJellyFish", "idle2", 0.2f);
        okto.enemySprite.state.Data.SetMix("idle2", "idle1", 0.4f);
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        currentTime = 0.0f;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "SummonJellyFish", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle2", true);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            if (Vector2.Distance(okto.player.transform.position,okto.transform.position) < OktoConst.MELEE_ATTACK_DISTANCE)
            {
                GetStateMachine().EnterState<OktoStateTentacleAttack>();
            }
            else
            {
                GetStateMachine().EnterState<OktoStateIdlePhase2>();

            }

        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateSummonJellyFish";
    }
}

public class OktoStateDoubleRocket : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase2e").Duration * 3;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation( "idle1").Duration;

        okto.enemySprite.state.Data.SetMix("idle1", "attack phase2e", 0.5f);
        okto.enemySprite.state.Data.SetMix("attack phase2e", "idle1", 0.7f);
        okto.enemySprite.state.Data.SetMix("attack phase2e", "attack phase2e", 0.2f);
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        currentTime = 0.0f;

        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase2e", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "attack phase2e", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "attack phase2e", false);

        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1", false);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;

        if (currentTime > timeToRun)
        {
            if (Vector2.Distance(okto.player.transform.position,okto.transform.position) < OktoConst.MELEE_ATTACK_DISTANCE)
            {
                GetStateMachine().EnterState<OktoStateTentacleAttack>();
            }
            else
            {
                GetStateMachine().EnterState<OktoStateIdlePhase2>();

            }
        }
        okto.ArmTracking();

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateDoubleRocket";
    }
}

public class OktoStateTentacleAttack : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase2A2").Duration;

        okto.enemySprite.state.Data.SetMix("idle1", "attack phase2A2", 0.2f);
        okto.enemySprite.state.Data.SetMix("attack phase2A2", "idle1", 0.2f);

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        currentTime = 0.0f;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase2A2", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle1", true);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime < timeToRun * 0.75f)
        {
            okto.TentacleAttackCollision();

        }
        okto.TentacleAttackTracking();

        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateIdlePhase2>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateTentacleAttack";
    }
}

public class OktoStateEntryPhase3 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle1", "idle2", 0.2f);
        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "idle2").Duration * 2;
        okto.BoundryDistance = OktoConst.PHASE_3_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = false;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "idle2", true);
        timeToRun = timeToRun / okto.enemySprite.state.TimeScale;
        if (currentTime == 0)
        {
            okto.BlockDamage = (true);
        }
        currentTime = 0;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateAttackPhase3>();
        }

        //Globals.zoomValueWhileGame = Globals.zoomValueWhileGame + (-Globals.zoomValueWhileGame + 1000) * Time.deltaTime * 2;

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateEntryPhase3";
    }
}

public class OktoStateAttackPhase3 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle2", "attack phase2B", 0.2f);
        okto.enemySprite.state.Data.SetMix("attack phase2B", "idle2", 0.75f);
        okto.enemySprite.state.Data.SetMix("attack phase2B", "attack phase2B", 0.1f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase2B").Duration;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = false;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase2B", true);
        currentTime = 0;
        okto.enemySprite.state.TimeScale =0.75f;
        timeToRun = timeToRun / okto.enemySprite.state.TimeScale;
        okto.BlockDamage = (false);
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            //        GetStateMachine().EnterState<OktoStateAttackPhase3>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateAttackPhase3";
    }
}

public class OktoStateEntryPhase4 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle2", "attack phase3 transition1", 0.5f);
        okto.enemySprite.state.Data.SetMix("attack phase2B", "attack phase3 transition1", 0.5f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase3 transition1").Duration;
        okto.BoundryDistance = OktoConst.PHASE_3_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = true;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase3 transition1", false);
        okto.BlockDamage = (true);
        DOTween.Sequence().SetId(okto.tweenId).Append(okto.transform.DOMove(new Vector2(okto.transform.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(60)), 1f)).Play();
        currentTime = 0;
        okto.enemySprite.state.TimeScale =1;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateSummonJellyFishSwarm>();
        }
        //Globals.zoomValueWhileGame = Globals.zoomValueWhileGame + (-Globals.zoomValueWhileGame + 800) * Time.deltaTime * 2;

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateEntryPhase4";
    }
}

public class OktoStateIdlePhase4 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("attack phase3 transition1", "idle4", 0.04f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "idle4").Duration * 3;
        okto.BoundryDistance = OktoConst.PHASE_3_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = false;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "idle4", true);
        okto.BlockDamage = (true);
        DOTween.Sequence().SetId(okto.tweenId).Append(okto.transform.DOMove(new Vector2(okto.transform.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(60)), 1f)).Play();
        currentTime = 0;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            GetStateMachine().EnterState<OktoStateAttackPhase4>();
        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateIdlePhase4";
    }
}

public class OktoStateSummonJellyFishSwarm : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("attack phase3 transition1", "attack phase3", 0.04f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase3").Duration * 10;
        okto.BoundryDistance = OktoConst.PHASE_3_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = true;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase3", true);
        okto.BlockDamage = (true);
        DOTween.Sequence().SetId(okto.tweenId).Append(okto.transform.DOMove(new Vector2(okto.transform.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(40)), 1f)).Play();
        currentTime = 0;
        okto.SummonJellyFishSwarm();
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {

        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateSummonJellyFishSwarm";
    }
}

public class OktoStateAttackPhase4 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle4", "attack phase3c2", 0.04f);
        okto.enemySprite.state.Data.SetMix("attack phase3c2", "attack phase3b", 0.04f);
        okto.enemySprite.state.Data.SetMix("attack phase3b", "idle4", 0.04f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "attack phase3c2").Duration;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation( "attack phase3b").Duration;
        //    timeToRun += okto.enemySprite.skeleton.data.FindAnimation("idle4").duration ;


        okto.BoundryDistance = OktoConst.PHASE_4_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = true;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase3c2", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "attack phase3b", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle4", true);


        Debug.Log("enetered"+ timeToRun);
        okto.BlockDamage = (false);
        currentTime = 0;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            currentTime = 0;
            DidEnterWithPreviousState(GetStateMachine().GetState());
            //GetStateMachine().EnterState<OktoStateInterimAttackPhase4>();

        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateAttackPhase4";
    }
}

public class OktoStateInterimAttackPhase4 : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle4", "attack phase3c2", 0.04f);
        okto.enemySprite.state.Data.SetMix("attack phase3c2", "attack phase3b", 0.04f);
        okto.enemySprite.state.Data.SetMix("attack phase3b", "idle4", 0.04f);

        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation("attack phase3c2").Duration;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation("attack phase3b").Duration;
        //    timeToRun += okto.enemySprite.skeleton.data.FindAnimation("idle4").duration ;


        okto.BoundryDistance = OktoConst.PHASE_4_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        Globals.bossShouldStayOnScreen = true;
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "attack phase3c2", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "attack phase3b", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "idle4", true);


        Debug.Log("enetered" + timeToRun);
        okto.BlockDamage = (false);
        currentTime = 0;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            currentTime = 0;
            GetStateMachine().EnterState<OktoStateAttackPhase4>();

        }

    }

    public override void WillExitWithNextState(State nextState)
    {
        okto.BlockDamage = (false);
    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateAttackPhase4";
    }
}

public class OktoStateDeath : State
{
    OktoPussStateMachine oktoPussStateMachine;
    OktoPuss okto;

    float timeToRun = 0.0f;
    float currentTime = 0.0f;

    void Init()
    {
        oktoPussStateMachine = stateMachine as OktoPussStateMachine;
        okto = oktoPussStateMachine.okto;
        okto.enemySprite.state.Data.SetMix("idle5", "defeat3", 0.04f);


        timeToRun = okto.enemySprite.skeleton.Data.FindAnimation( "idle5").Duration;
        timeToRun += okto.enemySprite.skeleton.Data.FindAnimation( "defeat3").Duration;

        okto.BoundryDistance = OktoConst.PHASE_4_BOUNDARY_DISTANCE;

    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (oktoPussStateMachine == null)
            Init();
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_MAIN, "idle5", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "defeat3", false);
        okto.enemySprite.state.AddAnimation(OktoConst.TRACK_MAIN, "defeat damage", true);
        okto.enemySprite.state.SetAnimation(OktoConst.TRACK_REPEATING, "Glow and fan off", true);


        okto.BlockDamage = (true);
        currentTime = 0;
    }

    public override void UpdateState()
    {
        currentTime += Time.deltaTime;
        if (currentTime > timeToRun)
        {
            okto.Shadow.transform.position = new Vector2(okto.transform.position.x, Globals.LOWERBOUNDARY - Globals.CocosToUnity(35.0f));
            float scaleX = 1.0f;
            scaleX = 12 + (okto.enemySprite.transform.position.y - Globals.LOWERBOUNDARY - Globals.CocosToUnity(61)) / Globals.CocosToUnity(100);
            scaleX = Mathf.Clamp(scaleX, 12, 18);
            okto.Shadow.transform.localScale = new Vector2(scaleX * 1.5f, scaleX * 0.25f);
            okto.Shadow.color = new Color(1,1,1,Mathf.Clamp(scaleX * 0.1f/255, 0, 1));
        }

    }

    public override void WillExitWithNextState(State nextState)
    {

    }

    public override bool IsValidNextState(State state)
    {
        if (state == GetStateMachine().GetState())
        {
            return false;
        }

        return true;
    }

    public override string GetStateType()
    {
        return "OktoStateDeath";
    }
}
