using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;

public class Rocketeer : Sidekick
{

    void Start()
    {
        Init();
        
    }

    public override void Init()
    {
        if (isInitialized)
            return;
        base.Init();
        //sideKickSprite->setMix("select", "idle", 0.4f);
        sidekickSkeleton.state.SetAnimation(0, "menuIdle", true);
        SetStartingPosition();
        sidekickSkeleton.state.TimeScale = 0.7f;
        transform.localScale = new Vector3(scale, scale, 1);
    }

    public override void StartSidekick()
    {
        sidekickSkeleton.state.SetAnimation(0, "select", true);
        sidekickSkeleton.state.AddAnimation(0, "idle", true);
        sidekickSkeleton.AnimationState.Event += ShootEvent;
    }

    private void OnDisable()
    {
        sidekickSkeleton.AnimationState.Event -= ShootEvent;
    }

    void ShootEvent(TrackEntry trackEntry, Spine.Event e)
    {
        if (e.Data.Name == "shoot")
            Shoot();
    }

    private void Update()
    {
        SidekicksUpdate();
    }



    void Shoot()
    {
        if (!Globals.allowSidekickShoot || Globals.resetControls)
            return;

        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.rocketeerShoot, 0.3f);
        //Globals.PlaySound("res/Sounds/SFX/rocketeerShoot.mp3", false, 0.3f);

        PlayerMissile missile = null;
        bool didFindBullet = false;
        foreach (PlayerMissile m in GameSharedData.Instance.playerMissilePool)
        {
            if (!m.isInUse)
            {
                missile = m;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        missile.Init();
        missile.isInUse = true;

        missile.damage = (GameData.instance.fileHandler.currentMission != 0) ? damage : (player.Stats.attack * 3.5f);
        missile.transform.position = (Vector2)transform.position;
        GameSharedData.Instance.playerMissilesInUse.Add(missile);


        //if (sideKickSprite->getScaleX() < 0)
        //{
        //    missile->missileSprite->setRotation(0);
        //}
        //else
        //{
        //    missile->missileSprite->setRotation(180);
        //}
    }
}
