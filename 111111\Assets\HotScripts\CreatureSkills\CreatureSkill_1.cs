﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Extension;

using Cysharp.Threading.Tasks;
using HotScripts;
using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 机枪
    /// </summary>
    public class CreatureSkill_1 : CreatureSkillBase
    {
        /// <summary>
        /// 射击方向(最多启动时改变一次)
        /// </summary>
        protected Vector3 ShootDir { get; set; } = Vector3.zero;

        public override async UniTaskVoid DoSkill()
        {
            // 按一轮攻击的持续时长预设结束时间
            base.DoSkill().Forget();

            await UniTask.SwitchToMainThread();

            // 技能可由令牌或玩家死亡的方式停止
            var cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Skill.Token,
                Creature.GetCancellationTokenOnDestroy()
                );

            try
            {
                // 在攻击范围内找最近的敌人作为射击方向
                var enemy = GameSharedData.Instance.enemyList.Where(x => x)
                    .Select(x => new
                    {
                        Enemy = x,
                        Distance = x ? Creature.transform.position.CalcDistance2D_SolidCircleToSolidCircle(Creature.FightProp.Radius, x.transform.position, x.FightProp.Radius)
                                    : float.PositiveInfinity,
                    })
                    .Where(x => x.Distance <= Skill.攻击距离.Value)
                    .OrderBy(x => x.Distance)
                    .FirstOrDefault()?.Enemy;
                ShootDir = enemy ? (enemy.transform.position - Creature.transform.position).normalized
                    : new Vector3(RandomNum.RandomFloat(0, 10000), RandomNum.RandomFloat(0, 10000), 0).normalized;

                var shootTimes = (int)Skill.连射次数.Value;

                //Debug.Log($"连射次数:{shootTimes}");

                // 根据配置延时后射击
                Skill.CsvRow_CatSkill.ShootTimings.Take(shootTimes).Select((x, i) =>
                {
                    var bulletQty = Skill.TotalEffect.子弹数量.Value;
                    if (Skill.CsvRow_CatSkill.BulletQtys.Length > i)
                    {
                        bulletQty += Skill.CsvRow_CatSkill.BulletQtys[i];
                    }

                    if (Skill.ActorSkill != null)
                    {
                        bulletQty += Globals.UnityValueTransform(Skill.ActorSkill.BulletQty);
                    }

                    if (Skill.CsvRow_CatSkill.ShootAngles.Count <= i) return i;
                    
                    var angles = Skill.CsvRow_CatSkill.ShootAngles[i].Split('|').ToList().ConvertAll(float.Parse);
                    DoSkill_MachineGun(cts_Skill.Token, x, bulletQty, angles).Forget();
                    return i;
                }).ToList();
            }
            catch { }
        }

        /// <summary>
        /// 机枪延时射击
        /// </summary>
        /// <param name="delay">延时:秒</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="angles">依次发射的角度</param>
        private async UniTaskVoid DoSkill_MachineGun(CancellationToken token, float delay, float bulletQty, IList<float> angles)
        {
            try
            {
                await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested) return;

                // 开火声音
                AudioPlayer.Instance.PlaySound(Skill.CsvRow_CatSkill.ShootSound).Forget();
                //Debug.Log($"一轮发射子弹数:{bulletQty}");

                // 发射一轮
                for (var z = 0; z < bulletQty; z++)
                {
                    // 发射方向依次按配置的角度旋转
                    var angle = angles.IndexOf_ByCycle(z);
                    var dir_1 = ShootDir.RotateAround(Vector3.zero, Vector3.forward, angle);
                    var bullet = Creature.CreateBullet(
                        this,
                        dir_1,
                        // 提供子弹初始位置
                        _ => Creature.transform.position + (/*Creature.FightProp.Radius*/ 0.3f * dir_1),
                        0,
                        Skill.子弹最大穿透次数.Value,
                        Skill.子弹最大反弹次数.Value,
                        Skill.子弹最大分裂次数.Value);

                    // 计算移动方向
                    bullet.BulletProp.MoveDirection_Straight = bullet.BulletProp.CalcMoveDirection_Straight(Creature.transform.position);
                    bullet.BulletProp.StartMove_Straight().Forget();
                }
            }
            catch (System.OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        //public override void ClearSkill()
        //{
        //    // 回收或销毁本轮发出的所有子弹
        //    foreach (var bullet in GameSharedData.Instance.playerBulletInUse.Where(x => x.SkillProps == Skill))
        //    {
        //        if (bullet.is来自子弹池)
        //        {
        //            // 重置子弹并回收到池中
        //            bullet.ResetBullet();
        //        }
        //        else
        //        {
        //            Object.Destroy(bullet);
        //        }
        //    }
        //}
    }
}
