﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace Apq.Extension
{
	public static class Ext_String
	{
		public static string WhiteSpaces { get; } = @"\r\n	 　" + (char)160 + (char)8204;

		/// <summary>
		/// 移除所有空白或不可见字符
		/// </summary>
		/// <param name="me"></param>
		/// <returns></returns>
		public static string RemoveWhiteSpaces(this string me)
		{
			if (string.IsNullOrEmpty(me)) return me;

			var sb = new StringBuilder();
			foreach (var c in me.Where(c => !WhiteSpaces.Contains(c)))
			{
				sb.Append(c);
			}

			return sb.ToString();
		}

		/// <summary>
		/// 查找所有子串出现的位置
		/// </summary>
		public static List<Tuple<int, string>> IndexOf(this string me,
			IEnumerable<string> subs)
		{
			return subs.Select(sub => new Tuple<int, string>(me.IndexOf(sub, StringComparison.Ordinal), sub)).ToList();
		}

		/// <summary>
		/// 字符串重复count次(0次就是string.Empty)
		/// </summary>
		public static string Repeate(this string me, int count)
		{
			var sb = new StringBuilder();
			if (count < 0) count = 0;
			for (int i = 0; i < count; i++)
			{
				sb.Append(me);
			}
			return sb.ToString();
		}

		public static string XmlEncode(this string me)
		{
			return me.Replace("&", "&amp;").Replace("\"", "&quot;").Replace("'", "&apos;").Replace("<", "&lt;").Replace(">", "&gt;");
		}
		public static string XmlDecode(this string me)
		{
			return me.Replace("&amp;", "&").Replace("&quot;", "\"").Replace("&apos;", "'").Replace("&lt;", "<").Replace("&gt;", ">");
		}
		public static string HtmlEncode(this string me)
		{
			return HttpUtility.HtmlEncode(me);
		}
		public static string HtmlDecode(this string me)
		{
			return HttpUtility.HtmlDecode(me);
		}

		public static string UrlEncode(this string me, Encoding encoding = null)
		{
			encoding ??= Encoding.UTF8;
			return HttpUtility.UrlEncode(me, encoding);
		}
		public static string UrlDecode(this string me)
		{
			return HttpUtility.UrlDecode(me);
		}

		public static string JsEncode(this string me)
		{
			return HttpUtility.JavaScriptStringEncode(me);
		}

		public static NameValueCollection ParseQueryString(this string me)
		{
			return HttpUtility.ParseQueryString(me);
		}

		/// <summary>
		/// 按索引范围取子串[idxBegin,idxEnd)
		/// </summary>
		/// <returns>尽量按范围取(范围无效时返回string.Empty)</returns>
		public static string GetRange(this string str, int idxBegin, int idxEnd)
		{
			if (idxBegin < 0) idxBegin = 0;
			if (idxEnd < 0) idxEnd = 0;
			if (idxBegin > str.Length) idxBegin = str.Length;
			if (idxEnd > str.Length) idxEnd = str.Length;

			var min = Math.Min(idxBegin, idxEnd);
			var max = Math.Max(idxBegin, idxEnd);

			if (min >= str.Length || min == max) return string.Empty;
			return str[min..max];
		}

		/// <summary>
		/// 字符串是否为十进制整数(可包含正负号[开头允许有一个],小数点[仅结尾可有])(不是使用正则表达式)
		/// </summary>
		public static bool IsInt(this string str)
		{
			//确保不为null
			str = string.IsNullOrWhiteSpace(str) ? string.Empty : str.Trim();

			//空串
			if (string.IsNullOrWhiteSpace(str)) return false;

			//多于一个小数点
			if (str.Count(i => i == '.') > 1) return false;

			// 小数点不在最后
			if (str.Count(i => i == '.') == 1 && !str.EndsWith(".")) return false;

			// 去掉小数点
			str = str.Replace(".", string.Empty);

			// 如果包含符号,去掉符号位
			if (str.StartsWith("+") || str.StartsWith("-"))
			{
				str = str[1..];
			}

			// 不能只有符号,小数点
			if (string.IsNullOrEmpty(str)) return false;

			// 剩下的全是数字则为整数
			return str.All(char.IsNumber);
		}

		/// <summary>
		/// 字符串是否为十进制数值(可包含正负号[开头允许有一个],小数点[最多一个])(不是使用正则表达式)
		/// </summary>
		public static bool IsNum(this string str)
		{
			//确保不为null
			str = string.IsNullOrWhiteSpace(str) ? string.Empty : str.Trim();

			//空串
			if (string.IsNullOrWhiteSpace(str)) return false;

			//多于一个小数点
			if (str.Count(i => i == '.') > 1) return false;

			// 去掉小数点
			str = str.Replace(".", string.Empty);

			// 如果包含符号,去掉符号位
			if (str.StartsWith("+") || str.StartsWith("-"))
			{
				str = str[1..];
			}

			// 不能只有符号,小数点
			if (string.IsNullOrEmpty(str)) return false;

			// 剩下的全是数字则为十进制数
			return str.All(char.IsNumber);
		}

		/// <summary>
		/// 模仿??操作符，取列表中第一个有意义的字符串，全无意则取最后一个值。（无意义：null, 空白字符串[Empty,WhiteSpace]）
		/// </summary>
		public static string GetFirstMeaningfulString(params string[] lst)
		{
			if (lst.Length == 0) return null;

			for (var i = 0; i < lst.Length - 1; i++)
			{
				if (!string.IsNullOrWhiteSpace(lst[i])) return lst[i];
			}

			return lst.Last();
		}

		/// <summary>
		/// 判断字符串是否可能是SQL注入(包含以下字符:";","'","--","/*","*/","xp_")
		/// </summary>
		/// <param name="me">应该是从不可信任来源传入的字符串(比如：客户端)</param>
		public static bool MaybeSqlInjection(this string me)
		{
			if (string.IsNullOrWhiteSpace(me)) return false;

			//参考：http://technet.microsoft.com/zh-cn/library/ms161953.aspx
			return me.IndexOf(";", StringComparison.Ordinal) != -1
				   || me.IndexOf("'", StringComparison.Ordinal) != -1
				   || me.IndexOf("--", StringComparison.Ordinal) != -1
				   || me.IndexOf("/*", StringComparison.Ordinal) != -1
				   || me.IndexOf("*/", StringComparison.Ordinal) != -1
				   || me.IndexOf("xp_", StringComparison.Ordinal) != -1;
		}

		/// <summary>
		/// 提取中文(连在一起)
		/// </summary>
		public static string GetChineseWord(this string me)
		{
			const string x = @"[\u4E00-\u9FFF]+";
			var matches = Regex.Matches(me, x, RegexOptions.IgnoreCase);
			var sb = new StringBuilder();
			foreach (Match nextMatch in matches.Cast<Match>())
			{
				sb.Append(nextMatch.Value);
			}
			return sb.ToString();
		}

		/// <summary>
		/// 判断字符串是否为IP4
		/// </summary>
		public static bool IsIP4(this string IP)
		{
			const string num = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
			return Regex.IsMatch(IP, ("^" + num + "\\." + num + "\\." + num + "\\." + num + "$"));
		}

		/// <summary>
		/// 将字符串拆分为两个列表,数字与非数字
		/// </summary>
		public static void SplitNum<T>(this string me,
			out List<T> lstNum,
			out List<string> lstStr,
			string[] separator,
			StringSplitOptions splitOptions = StringSplitOptions.RemoveEmptyEntries)
			where T : struct
		{
			lstNum = new List<T>();
			lstStr = new List<string>();

			if (string.IsNullOrWhiteSpace(me)) return;
			var ary = me.Split(separator, splitOptions);
			foreach (var str in ary)
			{
				if (str.IsNum())
				{
					lstNum.Add(Cast.TryChangeType<T>(str));
				}
				else
				{
					lstStr.Add(str);
				}
			}
		}
	}
}
