using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;

public class CatSkillScheme : Singleton<CatSkillScheme>
{
private CatSkill _data;
private Dictionary<int, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load();
        }
    }
    public bool Load()
{
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.CatSkill;
    string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
    try
    {
        MemoryStream ms = new MemoryStream(HotResManager.ReadPb(pbFileName));
        _data = Serializer.Deserialize<CatSkill>(ms);
    }
    catch
    {
        throw new Exception(pbFileName + ".pb fail");
    }
    for (int i = 0; i != _data.Items.Count; ++i)
    {
        _idIndexMap[_data.Items[i].Id] = i;

    }
    Debug.LogWarning(pbFileName + "pb succes");
    return true;

}
public CatSkill.Item GetItem(int id)
{
    if (_idIndexMap == null)
    {
        _idIndexMap = new Dictionary<int, int>();
        Load();
    }
    if (_idIndexMap.ContainsKey(id))
    {
        return _data.Items[_idIndexMap[id]];
    }
    else
    {
        throw new Exception("id dont exist");
    }

}
}

