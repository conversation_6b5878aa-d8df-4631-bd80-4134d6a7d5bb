using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
/// <summary>
/// 战斗准备管理，对应原版的WorldMapController
/// </summary>
public class PrepareBattleManager : Singleton<PrepareBattleManager>
{
    private DefaultInputActions inputActions;
    protected override void Awake()
    {
        base.Awake();
        inputActions = new DefaultInputActions();
#if UNITY_ANDROID || UNITY_IOS
            //Globals.mobileControls = true;
            //if (Input.GetJoystickNames().Length > 0)
            //{
            //    Globals.isJoystickConnected = true;
            //}
                Globals.mobileControls = true;
#else
        Globals.mobileControls = false;
       // 暂时注释掉模拟设备
        // string[] sname = Input.GetJoystickNames();
        //if (Input.GetJoystickNames().Length > 0)
        //{
        //    Globals.isJoystickConnected = true;
        //}
#endif
    }

    //设置关卡数据
    
    public void SetMissionsData(int currentMission)
    {
        Globals.g_currentStageData = CatMainStageScheme.Instance.GetItem(currentMission);
        Globals.g_currentStageDrops = Globals.g_currentStageData.GeneralDrop.Split(';');
        Globals.g_currentStageName = Globals.g_currentStageData.Name;
        Globals.g_showDifficultyInUtilityMenu = true;
        //Time.timeScale = 1;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(1);
        Globals.allowDialogueFromBoss = true;
        GameData.instance.fileHandler.currentMission = currentMission;
        PList vMap = GameData.instance.GetMissions();
        //string missionTxt = (GameData.instance.GetTextData(Constants.GAME_TEXT.MISSION_DATA)[currentMission.ToString()] as PList)["Text"] as string;
        string str = "Mission" + currentMission.ToString();
        PList plist = (vMap[str] as PList);
        string missionType = plist["Type"] as string;

        if (Globals.g_currentStageData.ChapMonster == "1" && GameData.instance.fileHandler.currentMission != 0)
        {
            Globals.gameType = GameType.Survival;
        }
        else
        {
            if(missionType == "Boss")
            {
                Globals.gameType = GameType.Arena;
                string bn = System.Convert.ToString(plist["Boss Number"]);
                int missionBossNumber = System.Convert.ToInt32(bn); 
                GameData.instance.fileHandler.currentEvent = missionBossNumber;
            }
            else
            {
                Globals.gameType = GameType.Training;
            }
        }
        //Debug.Log("PlayMusic--" + PlayerPrefs.GetInt("xmMusic", 0));
        Globals.AllowBgMusic = PlayerPrefs.GetInt("xmMusic", 0) == 0 ? true : false;
        Globals.AllowMusic = PlayerPrefs.GetInt("xmSound", 0) == 0 ? true : false;
    }

}
