using System.Collections;
using System.Collections.Generic;
using System.Linq;

using Apq;
using Apq.Extension;

using DG.Tweening;

using TMPro;

using UnityEngine;

using ViewModel;

public class PhysicsManager : MonoBehaviour
{
    //[HideInInspector] public Bullet[] playerBullet;

    [SerializeField] float lowerBoundry;
    [SerializeField] bool allowParticles;

    [SerializeField] private PlayerController player;
    [SerializeField] Transform[] boxes;
    private int scheduleShift = 0;
    private bool TenDistanceHadEnemy = false;
    private string tweenId;
    private string schedulerId;
    private void Awake()
    {
        schedulerId = "Physics" + GetInstanceID();
        schedulerId = "PhysicsS" + GetInstanceID();
        LuaToCshapeManager.Instance.curPhysicsManager = this;
        LuaToCshapeManager.Instance.CheckAutoShootMode();
    }
    //private void Start()
    //{
    //    StartCoroutine(AutoPlayerCollisionCollectorUpdate());
    //}
    private float curAutoInterval = 0;
    private float curABAutoInterval = 0;
    private float checkAutoInterval = 0;

    private object[] _lasterReturnResult = new object[2];

    private double _bulletOrMissileDamage;

    private void Update()
    {
        PlayerBulletCollionUpdate();
        //EnemyUpdate();
        AreaDamage();
        PlayerCollisionMissileUpdate();
        PlayerCollisionCollectorUpdate();
        ScheduleShift();
        FindNearestAliveEnemy();
        SkyFireBoomUpdate();
        PlayerCollisionExplosionAttackUpdate();

        if (Globals.allDamage > 5.0f)
        {
            float allDamage = Globals.allDamage;
            DOTween.Sequence().AppendInterval(0.75f).AppendCallback(() => DoAllEnemyDamage(allDamage));
            Globals.allDamage = 0.0f;
        }
        if (Globals.isClearAllButtle)
        {
            Globals.isClearAllButtle = false;
            DOTween.Sequence().AppendInterval(0.75f).AppendCallback(() => DoClearAllBullet());
        }
        //ShowBattleUIData();
    }

    private void ShowBattleUIData()
    {
        GameManager.instance.playerHud.UpdateBattleUI();
    }

    private void FindNearestAliveEnemy()
    {
        if (Time.timeScale == 0)
        {
            return;
        }
        Transform playerTransform = player.transform;
        //float distanceFromEnemy = 2;
        Enemy nearestEnemy = null;
        float count = LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.攻击距离);
        float percent = LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.攻击距离);
        float findDistance = (Globals.whiteCatAssistDirection + count) * (1 + percent / 10000f);
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy != null && enemy.canCheckDistance)
            {
                float dist = Vector2.Distance(((Vector2)enemy.transform.position + enemy.offset), playerTransform.position);

                if (dist < findDistance)
                {
                    nearestEnemy = enemy;
                    break;
                }
            }
        }

        Globals.nearestEnemy = nearestEnemy;
        if (nearestEnemy)
        {
            //Debug.Log("Globals.whiteCatAssistDirection--->" + Globals.whiteCatAssistDirection);
            //Debug.Log("find--->"+Vector2.Distance(((Vector2)nearestEnemy.transform.position + nearestEnemy.offset), playerTransform.position));
            Globals.nearestAliveEnemy = ((Vector2)nearestEnemy.transform.position + nearestEnemy.offset);
            Globals.nearestEnemy = TenDistanceHadEnemy ? nearestEnemy : null;
            
            //如果开了自动射击，根据距离来判断是否开始自动射击
            if (Globals.autoShootMode)
            {
                TenDistanceHadEnemy = (Vector2.Distance(Globals.nearestAliveEnemy, playerTransform.position) < findDistance) ? true : false;

                if (TenDistanceHadEnemy)
                {
                    curAutoInterval += Time.deltaTime;
                    curAutoInterval = Mathf.Clamp(curAutoInterval, 0, 1);
                    checkAutoInterval = player.isBerzerkMode ? Globals.autoShootBerzerInterval : Globals.autoShootInterval;
                    //if (curAutoInterval >= checkAutoInterval)
                    //{
                    //    GameManager.instance.StartAutoShootAB();
                    //}
                    if (curAutoInterval >= checkAutoInterval && !GameManager.instance.player.IsShooting)
                    {
                        curAutoInterval = 0;
                        GameManager.instance.StartAutoShoot();
                    }
                }
                else
                {
                    if (GameManager.instance.player.IsShooting)
                    {

                        GameManager.instance.EndAutoShoot();
                    }
                }
            }
        }
        else
        {
            if (GameManager.instance.player.IsShooting)
            {

                GameManager.instance.EndAutoShoot();
            }
        }

        //每个僚机单独计算最近敌人开枪
        if (Globals.autoShootMode)
        {
            if (GameManager.instance.player.Stats.energy > 0 && !Globals.antoShootRestoredEnergying)
            {
                if (BattleSkillManager.Instance.whiteCatList != null && BattleSkillManager.Instance.whiteCatList.Count > 0)
                {
                    if (Globals.autoShootMode)
                    {
                        curABAutoInterval += Time.deltaTime;
                        curABAutoInterval = Mathf.Clamp(curABAutoInterval, 0, 1);
                        checkAutoInterval = player.isBerzerkMode ? Globals.autoShootBerzerInterval : Globals.autoShootInterval;
                        if (curABAutoInterval >= checkAutoInterval)
                        {
                            curABAutoInterval = 0;
                            foreach (var item in BattleSkillManager.Instance.whiteCatList)
                            {
                                bool whiteCatEnableShoot = false;
                                foreach (Enemy enemy in GameSharedData.Instance.enemyList)
                                {
                                    if (enemy != null && enemy.canCheckDistance)
                                    {
                                        float dist = Vector2.Distance(((Vector2)enemy.transform.position + enemy.offset), item.player.skeletonAnimationTran.position);

                                        if (dist < findDistance)
                                        {
                                            item.player.SetShootGarget(enemy.transform);
                                            whiteCatEnableShoot = true;
                                            if (!item.player.IsShooting) item.player.StartShooting();
                                            continue;
                                        }
                                        //else
                                        //{
                                        //    if (item.player.IsShooting) item.player.EndShooting();
                                        //}
                                    }
                                }
                                if (item.player.IsShooting && whiteCatEnableShoot) item.player.EndShooting();
                            }
                        }
                    }
                }
            }
        }
    }

    private void ScheduleShift()
    {
        if (scheduleShift == 0)
        {
            EnemyUpdate();
            scheduleShift = 1;


        }
        else if (scheduleShift == 1)
        {

            HeadOnCollision();

            scheduleShift = 2;

        }
        else if (scheduleShift == 2)
        {
            scheduleShift = 3;

        }
        else if (scheduleShift == 3)
        {
            scheduleShift = 4;

        }
        else
        {
            scheduleShift = 0;
        }
    }


    private void AreaDamage()
    {
        if (Globals.allDamage > 5.0f)
        {
            DoAllEnemyDamage(Globals.allDamage);
            DOTween.Sequence().SetId(schedulerId).AppendInterval(0.75f).AppendCallback(() => { DoAllEnemyDamage(Globals.allDamage); }).Play();
            Globals.allDamage = 0.0f;
        }
        if (Globals.skillallDamage)
        {
            DoAllEnemyDamage();
            Globals.skillallDamage = false;
        }
    }

    void DoAllEnemyDamage(float damage)
    {
        foreach (EnemyMissile missile in GameSharedData.Instance.enemyMissilesInUse)
        {
            if (missile.isDestructable)
            {
                DOTween.Sequence().SetId(missile.schedulerId).AppendInterval(0.2f).AppendCallback(() => { RemoveMissileFromScene(missile); }).Play();
                GameManager.instance.ShakeCamera(Globals.CocosToUnity(70), 10);
            }
        }

        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.TakeHit(damage))
            {
                //DOTween.Sequence().SetId(enemy.schedulerId).AppendInterval(0.2f).AppendCallback(() =>
                //{
                if (!enemy.isDestroyed)
                {
                    enemy.isDestroyed = true;
                    DestroyEnemy(enemy);
                }
                //});
            }

        }

    }
    /// <summary>
    /// 清除所有的子弹和导弹
    /// </summary>

    void DoClearAllBullet()
    {
        foreach (EnemyMissile enemyMissile in GameSharedData.Instance.enemyMissilesInUse)
        {
            if (enemyMissile.hasHit)
            {
                return;
            }
            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, enemyMissile.transform.position, false, 1, 1.3f, 0);
            enemyMissile.hasHit = true;
            enemyMissile.RemoveMissile();
        }
        foreach (Bullet bullet in GameSharedData.Instance.enemyBulletInUse)
        {
            if (!bullet.hasHit && bullet.gameObject != null && bullet.gameObject.activeInHierarchy)
            {
                bullet.ResetBullet();
            }
        }
    }

    void DoAllEnemyDamage()
    {
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            var damage = enemy.stats.maxHealth.Value * 0.02f;
            //小怪直接杀
            if (enemy.monsterData != null && enemy.monsterData.Type == 0)
            {
                if (!enemy.isDestroyed)
                {
                    enemy.isDestroyed = true;
                    DestroyEnemy(enemy);
                }
            }
            //boos收到2%最大生命的伤害
            else
            {

                if (enemy.TakeHit(damage))
                {
                    //DOTween.Sequence().SetId(enemy.schedulerId).AppendInterval(0.2f).AppendCallback(() =>
                    //{
                    if (!enemy.isDestroyed)
                    {
                        enemy.isDestroyed = true;
                        DestroyEnemy(enemy);
                    }
                    //});
                }
            }

        }

        foreach (EnemyMissile enemyMissile in GameSharedData.Instance.enemyMissilesInUse)
        {
            if (enemyMissile.hasHit)
            {
                return;
            }

            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, enemyMissile.transform.position, false, 1, 1.3f, 0);
            enemyMissile.hasHit = true;
            enemyMissile.RemoveMissile();
            GameManager.instance.ShakeCamera(0.7f, 10);
        }

        foreach (Bullet bullet in GameSharedData.Instance.enemyBulletInUse)
        {
            if (!bullet.hasHit && bullet.gameObject != null && bullet.gameObject.activeInHierarchy)
            {
                bullet.ResetBullet();
            }
        }

    }

    private void RemoveMissileFromScene(EnemyMissile missile)
    {
        if (!missile)
        {
            return;
        }
        if (missile.hasHit)
        {
            return;
        }
        if (Vector2.SqrMagnitude(player.transform.position - missile.transform.position) < Globals.CocosToUnity(640000))
        {
            float scale = 1.3f;
            if (missile.explosionType == Explosions.ExplosionType.ExplosionTypeWXREnemy1)
            {
                scale = 0.5f;
            }
            missile.hasHit = true;

            GameSharedData.Instance.explosions.GenerateParticlesAt(missile.explosionType, missile.transform.position, false, 1, scale, 0);
        }
        missile.endFunc?.Invoke();
        missile.hasHit = true;
        missile.RemoveMissile();
    }

    public void DamageInsideArea(Vector2 center, float area, float damage)
    {
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (Vector2.Distance(center, (Vector2)enemy.transform.position + enemy.offset) > area)
                continue;

            if (enemy.TakeHit(damage))
            {
                if (!enemy.isDestroyed)
                {
                    enemy.isDestroyed = true;
                    DestroyEnemy(enemy);
                }
            }
        }

        foreach (EnemyMissile enemyMissile in GameSharedData.Instance.enemyMissilesInUse)
        {
            if (Vector2.Distance(center, enemyMissile.transform.position) > area)
                continue;

            if (enemyMissile.hasHit)
            {
                return;
            }

            if (enemyMissile.isDestructable)
            {
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, enemyMissile.transform.position, false, 1, 1.3f, 0);
                enemyMissile.hasHit = true;
                enemyMissile.RemoveMissile();
                GameManager.instance.ShakeCamera(0.7f, 10);
            }
        }
    }

    private void PlayerBulletCollionUpdate()
    {
        //enemies = FindObjectsOfType<EnemyBase>();
        GameSharedData.Instance.tempPlayerBulletInUse.Clear();
        foreach (Bullet bullet in GameSharedData.Instance.playerBulletInUse)
        {
            // 子弹碰到水面
            if (allowParticles && bullet.getReactToWater() && bullet.transform.position.y < Globals.LOWERBOUNDARY)
            {
                //_bg.applyWaterForce(playerBullet.bulletSprite.getPosition().x, -10);
                GameManager.instance.HitWater(bullet.transform.position);
                bullet.setReactToWater(false);
            }


            if (!bullet.hasHit && GameSharedData.Instance.enemyList.Count > 0)
            {
                for (int i = GameSharedData.Instance.enemyList.Count - 1; i >= 0; i--)
                {
                    Enemy enemy = GameSharedData.Instance.enemyList[i];
                    if (enemy != null && !enemy.isDestroyed && enemy.CheckCollision(bullet.transform.position, Globals.UnityValueTransform(bullet.skillData == null ? 0 : bullet.skillData.DamageRadius)))
                    {
                        //playerBullet.setPlaySound(enemy._isInteractable);
                        if (enemy.allowPushBack)
                        {
                            // 受击后,后退一点
                            enemy.transform.position = new Vector2((enemy.transform.position.x + (bullet.isFlipped ? -1f : 1f) * (bullet.PushBack) * Mathf.Cos(Mathf.Deg2Rad * (bullet.transform.rotation.eulerAngles.z)) * 0.1f),
                            enemy.transform.position.y + (bullet.isFlipped ? -1f : 1f) * (bullet.PushBack) * Mathf.Sin(Mathf.Deg2Rad * (bullet.transform.rotation.eulerAngles.z)) * 0.1f);
                        }

                        if (bullet.skillData != null)
                        {
                            int type = (bullet.skillData.Id == 1010) ? 19 : bullet.skillData.Type;
                            LuaToCshapeManager.Instance.AddSkillDamageCount(type, bullet.getDamage());
                        }
                        _bulletOrMissileDamage = bullet.getDamage();
                        double bossAddPercent = 0;
                        bool isCriticalHit = false;
                        if (enemy.isBoss)//是否为boss
                        {
                            //是否检查暴击
                            if (bullet.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
                            {
                                isCriticalHit = true;
                                bossAddPercent = 1 + player.Stats.damageBossAddPct * 0.0001f;
                            }
                            else
                            {
                                bossAddPercent = (1 + player.Stats.damageCritical * 0.0001f) * (1 + player.Stats.damageBossAddPct * 0.0001f);
                            }
                        }
                        else
                        {
                            //是否检查暴击
                            if (bullet.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
                            {
                                isCriticalHit = true;
                                bossAddPercent = 1.5f + player.Stats.damageCritical * 0.0001f;
                            }
                            else
                            {
                                bossAddPercent = 1f;
                            }
                        }
                        _bulletOrMissileDamage *= bossAddPercent;
                        if (isCriticalHit) ShowCriticalTMP(enemy, _bulletOrMissileDamage);//暴击飘字

                        bool _bool = false;
                        if (bullet.isDestroyAfterCollision)
                        {
                            _bool = enemy.TakeHit(_bulletOrMissileDamage);
                        }
                        else if (bullet.PenetrationDetection(enemy))
                        {
                            _bool = enemy.TakeHit(_bulletOrMissileDamage);
                        }

                        if (_bool && !enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            //enemy.Destroy();
                            DestroyEnemy(enemy);
                        }
                        HandleButtleHitEnemyPerformance(enemy, bullet);
                        if (bullet.isDestroyAfterCollision)
                        {
                            bullet.hasHit = true;
                            if (bullet.isRemovable)
                            {
                                GameSharedData.Instance.RemovePlayerBullet(bullet);
                            }
                        }
                    }
                }
            }
            if (!bullet.hasHit && bullet.canCollionWithNormalEnemyBullet)
            {
                foreach (Bullet enemyBullet in GameSharedData.Instance.enemyBulletInUse)
                {
                    float bulletRadiu = 0.5f;
                    if (bullet.skillData != null)
                    {
                        bulletRadiu = bulletRadiu + Globals.UnityValueTransform(bullet.skillData.BulletScale) / 2f - 0.5f;
                    }
                    if (Vector3.SqrMagnitude(bullet.transform.position - enemyBullet.transform.position) < bulletRadiu)
                    {
                        if (bullet.skillData != null)
                        {
                            int type = (bullet.skillData.Id == 1010) ? 19 : bullet.skillData.Type;
                            LuaToCshapeManager.Instance.AddSkillDamageCount(type, bullet.getDamage());
                        }
                        if (enemyBullet.hasHit)
                        {
                            continue;
                        }
                        enemyBullet.HasHit();
                    }
                }
            }
            if (!bullet.hasHit)
            {
                foreach (EnemyMissile enemyMissile in GameSharedData.Instance.enemyMissilesInUse)
                {

                    if (enemyMissile.gameObject.activeSelf && !enemyMissile.hasHit && Vector3.SqrMagnitude(bullet.transform.position - enemyMissile.transform.position) < enemyMissile.radiusSQ && enemyMissile.isCollidable)
                    {
                        if (enemyMissile.isDestructable)
                        {
                            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, enemyMissile.transform.position, false, 1, 1.3f, 0);
                            //this.addChild(explosion);
                            enemyMissile.hasHit = true;
                            enemyMissile.RemoveMissile();
                            GameManager.instance.ShakeCamera(0.7f, 10);
                            //shakeScreen = 10;
                            //shakeIntensity = 70;
                        }
                        if (bullet.isDestroyAfterCollision)
                        {
                            bullet.hasHit = true;
                            if (bullet.isRemovable)
                            {
                                GameSharedData.Instance.RemovePlayerBullet(bullet);
                            }
                        }

                        //if (playerBullet.bulletType != frontPlasma)
                        //{
                        //    removingBullet(playerBullet);
                        //}
                        return;
                    }
                }
            }
        }
        GameSharedData.Instance.playerBulletInUse.AddRange(GameSharedData.Instance.tempPlayerBulletInUse);
        //foreach (Bullet bullet in GameSharedData.Instance.playerBulletInUse)
        //{
        //    if (!bullet.hasHit)
        //    {
        //        foreach (EnemyMissile enemyMissile in GameSharedData.Instance.enemyMissilesInUse)
        //        {

        //            if (Vector3.SqrMagnitude(bullet.transform.position - enemyMissile.transform.position) < enemyMissile.radiusSQ && enemyMissile.isCollidable)
        //            {
        //                if (enemyMissile.hasHit)
        //                {
        //                    return;
        //                }

        //                if (enemyMissile.isDestructable)
        //                {
        //                    GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir2, enemyMissile.transform.position, false, 1, 1.3f, 0);
        //                    //this.addChild(explosion);
        //                    enemyMissile.hasHit = true;
        //                    enemyMissile.RemoveMissile();
        //                    GameManager.instance.ShakeCamera(0.7f, 10);
        //                    //shakeScreen = 10;
        //                    //shakeIntensity = 70;
        //                }

        //                bullet.hasHit = true;
        //                if (bullet.isRemovable)
        //                {
        //                    GameSharedData.Instance.RemovePlayerBullet(bullet);
        //                }

        //                //if (playerBullet.bulletType != frontPlasma)
        //                //{
        //                //    removingBullet(playerBullet);
        //                //}
        //                return;
        //            }
        //        }
        //    }
        //}

    }

    private void EnemyUpdate()
    {
        foreach (Bullet bullet in GameSharedData.Instance.enemyBulletInUse)
        {
            if (!bullet.hasHit)
            {

                //Vector2.SqrMagnitude(player.transform.position - bullet.transform.position);
                if (Vector2.SqrMagnitude(player.skeletonAnimationTran.position - bullet.transform.position) < bullet.getRadiusEffectSquared()
                    && player.canHit)
                {
                    bullet.hasHit = true;
                    bullet.HasHit();
                    player.GotHit(bullet.getDamage());
                    if (bullet.PushBack > 0)
                    {
                        Vector2 dirVector = player.skeletonAnimationTran.position - bullet.transform.position;
                        float dir = Vector2.SignedAngle(Vector2.right, dirVector);
                        dir = dir < 0 ? 360 + dir : dir;
                        player.Knockback(dir, bullet.PushBack, 0);
                    }
                    //shakeScreen = 3; //TODO
                    //shakeIntensity = 30;
                    //bullet.bulletSprite.setGlobalZOrder(10);
                    //RemoveBulletFromScene(bullet);


                }
            }
        }

        foreach (EnemyMissile missile in GameSharedData.Instance.enemyMissilesInUse)
        {

            if (missile)
            {
                if (missile.hasHit)
                {
                    return;
                }
                if (missile.transform.position.y < Globals.LOWERBOUNDARY)
                {
                    if (missile.reactToWater)
                    {
                        GameManager.instance.HitWater(missile.transform.position, 0.3f);
                        //_bg.applyWaterForce(missile.missileSprite.getPosition().x, -15);
                        missile.reactToWater = false;
                    }

                    if (missile._doExplodeOnWater)
                    {
                        //Explosion* explosion = Explosion::create();
                        //explosion.generateParticlesAt(Explosion::ExplosionType::ExplosionTypeBuilding, missile.missileSprite.getPosition(), false, 1, fabsf(missile.missileSprite.getScaleX()) / 2, 0);
                        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1, missile.transform.position, false, 1, Mathf.Abs(missile.transform.localScale.x / 2), 0);
                        missile.hasHit = true;
                        missile.RemoveMissile();
                        return;
                    }
                }

                if (Vector2.SqrMagnitude(player.skeletonAnimationTran.position - missile.transform.position) < missile.radiusSQ && missile.isCollidable
                    && player.canHit)
                {
                    //Explosion* explosion = Explosion::create();
                    //explosion.generateParticlesAt(missile.explosionType, missile.missileSprite.getPosition(), false, 1, 1.0f, 0);
                    GameSharedData.Instance.explosions.GenerateParticlesAt(missile.explosionType, missile.transform.position, false, 1, 1, 0);
                    GameManager.instance.ShakeCamera(0.8f, 12);
                    //this.addChild(explosion);

                    //shakeScreen = 12;
                    //shakeIntensity = 80;
                    player.GotHit(missile.GetDamage());
                    missile.hasHit = true;
                    missile.RemoveMissile();
                    return;
                }
            }

        }
    }

    public void PlayerCollisionCollectorUpdate()
    {
        if (!LuaToCshapeManager.Instance.canCollected && GameData.instance.fileHandler.currentMission != 0)
        {
            return;
        }
        foreach (Coin coin in GameSharedData.Instance.coinsInUse)
        {
            if (Vector2.SqrMagnitude(coin.transform.position - player.transform.position) < 0.2f)
            {
                if (!coin.IsCollected() && coin.IsMoved())
                {
                    coin.Collected();
                    //Globals._coinsThisRun++;

                    //if (!coin._isOrb)
                    //{
                    //    //GameData.instance.fileHandler.coins += 1;
                    //    //GameData.instance.fileHandler.logTotalCoins += 1;
                    //    //GameManager.instance.playerHud.UpdateCoins();

                    //}
                    //else
                    //{
                    //    GameManager.instance.playerHud.AddKillToComboManager();
                    //    if (GameData.instance.fileHandler.currentMission == 4 && PlayerPrefs.GetInt("rageTutorialExecuted") != 1)
                    //    {

                    //        //                    this->runAction(Sequence::create(DelayTime::create(1), CallFunc::create([this]() {
                    //        //                        this->runAction(Sequence::create(DelayTime::create(2), CallFunc::create([](){
                    //        //                        }), NULL));
                    //        //    UserDefault::getInstance()->setBoolForKey("rageTutorialExecuted", true);
                    //        //    UserDefault::getInstance()->flush();


                    //        //}),NULL));

                    //    }
                    //}
                }
            }

        }
        foreach (Coin coin in GameSharedData.Instance.extenCoinsInUse)
        {
            if (Vector2.SqrMagnitude(coin.transform.position - player.transform.position) < 0.2f)
            {
                if (!coin.IsCollected() && coin.IsMoved())
                {
                    coin.Collected();
                }
            }

        }

    }

    //自动收集掉落
    public IEnumerator AutoPlayerCollisionCollectorUpdate()
    {
        while (true)
        {
            if (LuaToCshapeManager.Instance.canCollected && GameData.instance.fileHandler.currentMission != 0)
            {
                foreach (Coin coin in GameSharedData.Instance.coinsInUse)
                {
                    if (!coin.IsCollected())
                    {
                        float followCheckDistance = Globals.CocosToUnity(612000);
                        //Debug.Log("followCheckDistance="+ followCheckDistance);
                        coin.SetDistanceFollow(followCheckDistance);
                    }
                }
                foreach (Coin coin in GameSharedData.Instance.extenCoinsInUse)
                {
                    if (!coin.IsCollected())
                    {
                        float followCheckDistance = Globals.CocosToUnity(612000);
                        //Debug.Log("followCheckDistance="+ followCheckDistance);
                        coin.SetDistanceFollow(followCheckDistance);
                    }
                }
            }

            yield return new WaitForSeconds(2f);
        }

    }


    private void HeadOnCollision()
    {
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            return;
        }

        float distanceToClosestEnemy = Globals.CocosToUnity(4000);

        float distance;
        int count = 0;
        Vector2 lerpedDistance;
        lerpedDistance = Globals.averageBulletDistance;
        Globals.averageBulletDistance = Vector2.zero;

        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
#if !UNITY_STANDALONE
            if (!enemy.isDestructable)
            {
                continue;
            }
#endif
            distance = Vector2.Distance(player.transform.position, (Vector2)enemy.transform.position + enemy.offset);
            //Camera* camera = Director::getInstance().getRunningScene().getCameras().at(1);

            //AABB a = AABB(Vec3(enemy.enemySprite.getPosition().x - enemy.enemySprite.getContentSize().width / 2, enemy.enemySprite.getPosition().y - enemy.enemySprite.getContentSize().height / 2, 0), Vec3(enemy.enemySprite.getPosition().x + enemy.enemySprite.getContentSize().width / 2, enemy.enemySprite.getPosition().y + enemy.enemySprite.getContentSize().height / 2, 0));
            //Renderer enemyRenderer = enemy.enemySprite.GetComponent<Renderer>();
            if (distance < distanceToClosestEnemy)
            {
                distanceToClosestEnemy = distance;
#if !UNITY_STANDALONE
                Globals.nearestAliveEnemy = enemy.transform.position;
#endif
            }
            //            if (distance < distanceToClosestEnemy)
            //            {
            //                if (enemyRenderer.isVisible)
            //                {
            //                    distanceToClosestEnemy = distance;
            //#if !UNITY_STANDALONE
            //                    Globals.nearestAliveEnemy = enemy.transform.position;
            //#endif
            //                }
            //            }

            //if (distance < 1000) TODO
            //{
            //    AverageBulletDistance.x = AverageBulletDistance.x + (-Player::getInstance().getPosition().x + enemy.enemySprite.getPosition().x);
            //    AverageBulletDistance.y = AverageBulletDistance.y + (-Player::getInstance().getPosition().y + enemy.enemySprite.getPosition().y);
            //    count++;
            //}
        }

        //if (GameSharedData::getInstance().g_enemyArray.size() == 0) TODO
        //{
        //    AverageBulletDistance = cocos2d::Point::ZERO;
        //}
        //else
        //{

        //    AverageBulletDistance.x = AverageBulletDistance.x / count;
        //    AverageBulletDistance.y = AverageBulletDistance.y / count;

        //    lerpedDistance.x = lerpedDistance.x + (-lerpedDistance.x + AverageBulletDistance.x) * dt * 0.25;
        //    lerpedDistance.y = lerpedDistance.y + (-lerpedDistance.y + AverageBulletDistance.y) * dt * 0.25;

        //    AverageBulletDistance = lerpedDistance;
        //    if (gameType == GameType::Arena)
        //    {
        //        AverageBulletDistance.x = clampf(AverageBulletDistance.x, -200, 200);
        //        AverageBulletDistance.y = clampf(AverageBulletDistance.y, -200, 100);
        //    }
        //    else
        //    {
        //        AverageBulletDistance.x = clampf(AverageBulletDistance.x, -200, 200);
        //        AverageBulletDistance.y = clampf(AverageBulletDistance.y, -200, 100);
        //    }
        //}
    }

    public void RemovePLayerBulletFromList(Bullet bullet)
    {
        bullet.hasHit = true;
        if (bullet.isRemovable)
        {
            GameSharedData.Instance.RemovePlayerBullet(bullet);
        }
    }

    //void RemovingBullet(Bullet playerBullet)
    //{
    //    if (playerBullet.Type == Bullet.BulletType.FrontMachineGun || playerBullet.Type == Bullet.BulletType.FrontMultiCanon)
    //    {
    //        if (playerBullet.hasHit)
    //        {
    //            //if (playerBullet.getPlaySound())
    //            //{
    //            //    Shared::playSound("res/Sounds/SFX/enemyBeingHit.mp3", playerBullet.bulletSprite.getPosition());
    //            //}
    //            playerBullet.RemoveWithSplash();
    //        }
    //    }

    //    if (playerBullet.Type == Bullet.BulletType.ProtonCanon)
    //    {
    //        //Sprite* blast = Sprite::createWithSpriteFrameName("blueBlast_00000.png");
    //        //blast.runAction(Sequence::create(Shared::createAnimation("blueBlast_0000%d.png", 0, 6, false), RemoveSelf::create(), NULL));
    //        //this.addChild(blast);
    //        //blast.setPosition(playerBullet.bulletSprite.getPosition());
    //        //blast.setCameraMask(GAMECAMERA);
    //        //blast.setScale(2);

    //        //SkeletonAnimation* blast2 = SkeletonAnimation::createWithJsonFile("res/Arsenal/blast.json", "res/Arsenal/blast.atlas");
    //        //blast2.setAnimation(0, "plasmaBlast", true);
    //        //blast2.runAction(Sequence::create(ScaleTo::create(0.2f, 2), DelayTime::create(0.1f), RemoveSelf::create(), NULL));
    //        //blast2.runAction(FadeOut::create(0.3f));
    //        //this.addChild(blast2, 2);
    //        //blast2.setPosition(playerBullet.bulletSprite.getPosition().x, playerBullet.bulletSprite.getPosition().y);
    //        //blast2.setCameraMask(GAMECAMERA);




    //        //removeBulletWithSplash(false, playerBullet);
    //        Globals.PlaySound("res/Sounds/SFX/enemyHit.mp3");

    //        playerBullet.RemoveProtonBullet();
    //    }


    //    //if (playerBullet.Type == Bullet.BulletType.FrontPlasma)
    //    //{
    //    //    GameSharedData::getInstance().playerBullets.eraseObject(playerBullet);
    //    //    playerBullet.bulletSprite.runAction(ScaleTo::create(0.5, 0));
    //    //    playerBullet.runAction(Sequence::create(DelayTime::create(0.5), RemoveSelf::create(), NULL));
    //    //}


    //    if (playerBullet.Type == Bullet.BulletType.FrontRocket)
    //    {
    //        playerBullet.HasHit();
    //        //float radius = 0.5 + FileHandler::getInstance().rocketLevel * 1.25;
    //        float radius = 0.5f + 1.25f;
    //        //if (FileHandler::getInstance()._frontGunStr != kRocket)
    //        //{
    //        //    radius = 1.5 * 5;
    //        //}

    //        float newVal = radius * 3;

    //        int countEnemies = 0;
    //        bool endLoop = false;

    //        while (!endLoop)
    //        {
    //            foreach (Enemy enemy in GameSharedData.Instance.enemyList)
    //            {
    //                if (Vector2.Distance(enemy.transform.position, playerBullet.transform.position) < newVal)
    //                {

    //                    countEnemies++;
    //                    if (countEnemies == 5)
    //                    {
    //                        endLoop = true;
    //                        break;
    //                    }
    //                    if (enemy.TakeHit(playerBullet.getDamage() / countEnemies))
    //                    {

    //                        if (!enemy.isDestroyed)
    //                        {
    //                            enemy.isDestroyed = true;
    //                            enemy.Destroy();
    //                        }
    //                        break;

    //                    }

    //                }
    //            }
    //        }
    //    }
    //}

    /// <summary>
    /// 激光伤害最近的一个敌人
    /// </summary>
    public object[] CheckLaserForEnemies(Vector3 laserOrigin, float laserLength, float laserHeight, float damage, PlayerController player)
    {
        Enemy selectedEnemy = null;
        float foundDistance = laserLength;
        var pointX1 = laserOrigin.x + laserHeight / 5 * Mathf.Cos((player.RotationInDegrees + 90) * Mathf.Deg2Rad);
        var pointY1 = laserOrigin.y + laserHeight / 5 * Mathf.Sin((player.RotationInDegrees + 90) * Mathf.Deg2Rad);
        var pointX2 = laserOrigin.x + laserHeight / 5 * Mathf.Cos((player.RotationInDegrees + 270) * Mathf.Deg2Rad);
        var pointY2 = laserOrigin.y + laserHeight / 5 * Mathf.Sin((player.RotationInDegrees + 270) * Mathf.Deg2Rad);

        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.isDestroyed)
            {
                continue;
            }

            float distance = laserLength;
            float pointDifferential = Globals.CocosToUnity(35);
            int loopLength = (int)(distance / pointDifferential);


            for (int i = 1; i < loopLength; i++)
            {
                var newVector = new Vector3(i * pointDifferential * Mathf.Cos(player.RotationInDegrees * Mathf.Deg2Rad),
                    (i * pointDifferential) * Mathf.Sin(player.RotationInDegrees * Mathf.Deg2Rad));

                var point1 = new Vector3(pointX1, pointY1, laserOrigin.z) + newVector;
                var point2 = new Vector3(pointX2, pointY2, laserOrigin.z) + newVector;

                if (enemy.CheckCollision(point1))
                {
                    if ((i * pointDifferential) < distance)
                    {
                        distance = i * pointDifferential;
                    }
                }

                if (enemy.CheckCollision(point2))
                {

                    if ((i * pointDifferential) < distance)
                    {
                        distance = i * pointDifferential;
                    }
                }
            }

            if (distance < foundDistance)
            {
                //  laser.setScaleX(distance/laser.getContentSize().width);
                foundDistance = distance;
                selectedEnemy = enemy;
            }

        }

        LaserHitEnemy(damage, selectedEnemy);
        _lasterReturnResult[0] = foundDistance;
        _lasterReturnResult[1] = selectedEnemy;
        return _lasterReturnResult;
    }

    /// <summary>
    /// 激光伤害一个敌人
    /// </summary>
    public object[] CheckLaserForEnemies(Vector3 laserOrigin, float laserLength, float laserHeight, float damage, PlayerController player, Enemy enemy)
    {
        //Enemy selectedEnemy = null;
        float foundDistance = Vector3.Distance(player.transform.position, enemy.transform.position);
        //var pointX1 = laserOrigin.x + laserHeight / 5 * Mathf.Cos((player.RotationInDegrees + 90) * Mathf.Deg2Rad);
        //var pointY1 = laserOrigin.y + laserHeight / 5 * Mathf.Sin((player.RotationInDegrees + 90) * Mathf.Deg2Rad);
        //var pointX2 = laserOrigin.x + laserHeight / 5 * Mathf.Cos((player.RotationInDegrees + 270) * Mathf.Deg2Rad);
        //var pointY2 = laserOrigin.y + laserHeight / 5 * Mathf.Sin((player.RotationInDegrees + 270) * Mathf.Deg2Rad);

        //if (enemy.isDestroyed)
        //{
        //    return _lasterReturnResult;
        //}

        //float distance = laserLength;
        //float pointDifferential = Globals.CocosToUnity(35);
        //int loopLength = (int)(distance / pointDifferential);


        //for (int i = 1; i < loopLength; i++)
        //{
        //    var newVector = new Vector3(i * pointDifferential * Mathf.Cos(player.RotationInDegrees * Mathf.Deg2Rad),
        //        (i * pointDifferential) * Mathf.Sin(player.RotationInDegrees * Mathf.Deg2Rad));

        //    var point1 = new Vector3(pointX1, pointY1, laserOrigin.z) + newVector;
        //    var point2 = new Vector3(pointX2, pointY2, laserOrigin.z) + newVector;

        //    if (enemy.CheckCollision(point1))
        //    {
        //        if ((i * pointDifferential) < distance)
        //        {
        //            distance = i * pointDifferential;
        //        }
        //    }

        //    if (enemy.CheckCollision(point2))
        //    {

        //        if ((i * pointDifferential) < distance)
        //        {
        //            distance = i * pointDifferential;
        //        }
        //    }
        //}

        //if (distance < foundDistance)
        //{
        //    //  laser.setScaleX(distance/laser.getContentSize().width);
        //    foundDistance = distance;
        //    selectedEnemy = enemy;
        //}
        //foundDistance = distance;
        //selectedEnemy = enemy;
        LaserHitEnemy(damage, enemy);
        _lasterReturnResult[0] = foundDistance;
        _lasterReturnResult[1] = enemy;
        return _lasterReturnResult;
    }

    /// <summary>
    /// 获取 攻击范围内的随机敌人(最多num个)
    /// </summary>
    public List<Enemy> GetForEnemies(PlayerController player, int num, float distance)
    {
        var enemies = GameSharedData.Instance.enemyList.Where(x => !x.isDestroyed && x.canCheckDistance && x.Tag == 0)
            .Select(x => new { Enemy = x, Distance = player.transform.position.CalcDistance2D_PointToCircle(x.transform.position, x.enemyCollisionRadius) })
            .Where(x => x.Distance.DistanceToEdge - player.CollisionRadius <= distance)
            .OrderBy(x => RandomNum.RandomInt())
            .Take(num)
            .Select(x => x.Enemy)
            .ToList();

        return enemies;
    }

    /// <summary>
    /// 激光伤害某个敌人(一帧)
    /// </summary>
    /// <returns>伤害后敌人是否死亡</returns>
    public bool LaserHitEnemy(double damage, Enemy enemy)
    {
        if (enemy)
        {
            //selectedEnemy.laserDamaging = true;
            //allLasterDamageEnemy.Add(selectedEnemy);
            //damage = 0;

            _bulletOrMissileDamage = damage * Time.deltaTime;
            //if (_bulletOrMissileDamage < 1f) _bulletOrMissileDamage = 1f;// 每帧最少伤害1点
            //是否检查暴击
            if (!player.isWhiteCatType && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
            {
                _bulletOrMissileDamage *= 1.5f;
                if (enemy.lasterTime == 0)
                {
                    ShowCriticalTMP(enemy, _bulletOrMissileDamage);
                }
                enemy.lasterTime += Time.deltaTime;
                if (enemy.lasterTime > 1)
                {
                    enemy.lasterTime = 0;
                }
            }

            LuaToCshapeManager.Instance.AddSkillDamageCount(3, _bulletOrMissileDamage);
            var rtn = enemy.TakeHit(_bulletOrMissileDamage);
            if (rtn)
            {
                if (!enemy.isDestroyed)
                {
                    enemy.isDestroyed = true;
                    DestroyEnemy(enemy);
                }
            }
            return rtn;
        }

        return true;
    }

    public List<GameObject> survivalLaster = new();

    void PlayerCollisionMissileUpdate()
    {
        foreach (PlayerMissile missile in GameSharedData.Instance.playerMissilesInUse)
        {
            foreach (Enemy enemy in GameSharedData.Instance.enemyList)
            {
                if (enemy.canCheckDistance && Vector2.Distance(missile.transform.position, (Vector2)enemy.transform.position + enemy.offset) < 1f)
                {
                    if (missile.skillData != null)
                    {
                        LuaToCshapeManager.Instance.AddSkillDamageCount(missile.skillData.Type, missile.damage);
                    }
                    _bulletOrMissileDamage = missile.damage;
                    //是否检查暴击
                    if (missile.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
                    {
                        _bulletOrMissileDamage *= 1.5f;
                        ShowCriticalTMP(enemy, _bulletOrMissileDamage);
                    }
                    if (enemy.TakeHit(_bulletOrMissileDamage))
                    {
                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            DestroyEnemy(enemy);
                            //enemy.Destroy();
                        }
                    }
                    RemovingMissile(missile);
                    return;
                }
            }
        }

        foreach (PlayerMissile missile in GameSharedData.Instance.playerMissilesInUse)
        {

            foreach (EnemyMissile eMissile in GameSharedData.Instance.enemyMissilesInUse)
            {

                if (Vector2.Distance(missile.transform.position, eMissile.transform.position) < 1f)
                {
                    //shakeScreen = 10;
                    //shakeIntensity = 70;
                    if (missile.skillData != null)
                    {
                        LuaToCshapeManager.Instance.AddSkillDamageCount(missile.skillData.Type, missile.damage);
                    }
                    ///bug fix
                    if (eMissile.isDestructable)
                    {
                        GameSharedData.Instance.enemyMissilesInUse.Remove(eMissile);
                    }


                    RemovingMissile(missile);

                    return;
                }
            }
        }
    }

    void PlayerCollisionExplosionAttackUpdate()
    {
        foreach (WXR_ExplosionAttack expAttack in GameSharedData.Instance.wxrExplosionsAttackInUse)
        {
            foreach (Enemy enemy in GameSharedData.Instance.enemyList)
            {
                if (enemy.canCheckDistance && Vector2.Distance(expAttack.transform.position, (Vector2)enemy.transform.position) < expAttack.skillData.DamageRadius*0.00004f)
                {
                    if (expAttack.skillData != null)
                    {
                        LuaToCshapeManager.Instance.AddSkillDamageCount(expAttack.skillData.Type, expAttack.damage);
                    }
                    _bulletOrMissileDamage = expAttack.damage;
                    ////是否检查暴击
                    //if (missile.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
                    //{
                    //    _bulletOrMissileDamage *= 1.5f;
                    //    ShowCriticalTMP(enemy, _bulletOrMissileDamage);
                    //}
                    if (enemy.TakeHit(_bulletOrMissileDamage))
                    {
                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            DestroyEnemy(enemy);
                            //enemy.Destroy();
                        }
                    }
                    //expAttack.Reset();
                    return;
                }
            }
        }
    }

    void SkyFireBoomUpdate()
    {
        foreach (SkyFireStruct skyFire in GameSharedData.Instance.skyFireBoomPool)
        {
            skyFire._currentTime += Time.deltaTime;
            skyFire._damageCount += Time.deltaTime;
            if (skyFire._currentTime >= skyFire._injuryInterval && skyFire._damageCount < skyFire._totleTime)
            {
                skyFire._currentTime = 0;
                foreach (Enemy enemy in GameSharedData.Instance.enemyList)
                {
                    if (enemy.CheckCollision(skyFire._go.transform.position, skyFire._skyFireRadius))
                    {
                        LuaToCshapeManager.Instance.AddSkillDamageCount(14, skyFire._damage);
                        if (enemy.TakeHit(skyFire._damage))
                        {
                            if (!enemy.isDestroyed)
                            {
                                enemy.isDestroyed = true;
                                DestroyEnemy(enemy);
                                //enemy.Destroy();
                            }
                        }
                    }
                }
            }
            else
            {
                if (skyFire._damageCount >= skyFire._totleTime)
                {
                    Destroy(skyFire._go);
                    GameSharedData.Instance.skyFireBoomPool.Remove(skyFire);
                    return;
                }
            }
        }

    }

    /// <summary>
    /// 敌人受击，显示血条，被暴击等
    /// </summary>
    public void EnemyTakeHit(Enemy enemy, double damage, bool canCriticalHit = true)
    {
        //是否检查暴击
        if (canCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
        {
            damage *= 1.5f;
            ShowCriticalTMP(enemy, damage);
        }
        if (enemy.TakeHit(damage))
        {
            if (!enemy.isDestroyed)
            {
                enemy.isDestroyed = true;
                DestroyEnemy(enemy);
                //enemy.Destroy();
            }
        }
    }

    [IFix.Patch]
    void RemovingMissile(PlayerMissile playerMissile)
    {
        if (playerMissile.isDestroyAfterCollision)
        {
            //GameManager.instance.ShakeCamera(1);

            GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeWXREnemy1,
                        playerMissile.transform.position, false, 1, 0.5f, 1.5f);

            //playerMissile.ResetMissile();

            if (playerMissile.removeSoundID > 0)
            {
                LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", playerMissile.removeSoundID);
            }
        }
        playerMissile.ResetMissile();

        //开始范围伤害
        //foreach (PlayerMissile missile in GameSharedData.Instance.playerMissilesInUse)
        //{
        //    foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        //    {
        //        if (!enemy.isDestroyed && enemy.canCheckDistance && Vector2.Distance(missile.transform.position, (Vector2)enemy.transform.position + enemy.offset) < missile.boomRadius)
        //        {
        //            if (missile.skillData != null)
        //            {
        //                LuaToCshapeManager.Instance.AddSkillDamageCount(missile.skillData.Type, missile.damage);
        //            }
        //            _bulletOrMissileDamage = missile.damage;
        //            //是否检查暴击
        //            if (missile.canCheckIsCriticalHit && player.Stats.criticalHitRate > 0 && (Random.value * 10000 < player.Stats.criticalHitRate))
        //            {
        //                _bulletOrMissileDamage *= 1.5f;
        //                ShowCriticalTMP(enemy, _bulletOrMissileDamage);
        //            }
        //            if (enemy.TakeHit(_bulletOrMissileDamage))
        //            {
        //                if (!enemy.isDestroyed)
        //                {
        //                    enemy.isDestroyed = true;
        //                    DestroyEnemy(enemy);
        //                    //enemy.Destroy();
        //                }
        //            }
        //        }
        //    }
        //}

    }

    void SpawnCoinsOnDestroy(bool isOrb, Enemy enemy)
    {
        if (GameData.instance.fileHandler.currentMission != 0)
        {
            //Debug.Log("死了一个敌人开始掉落");
            //新的掉落
            //Debug.Log("怪物的掉落");
            GenerateDrop(enemy, enemy.prizeID);
            if (Globals.g_currentStageDrops != null)
            {
                if (Globals.g_currentStageDrops[(int)Globals.gameModeType] != null)
                {
                    int prizeID = System.Convert.ToInt32(Globals.g_currentStageDrops[(int)Globals.gameModeType]);
                    if (prizeID != 0)
                    {
                        //Debug.Log("关卡的掉落");
                        GenerateDrop(enemy, prizeID);
                    }
                }
            }
        }
        else
        {
            Coin coin = null;
            GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Coin);
            coin = go.GetComponent<Coin>();
            coin.isInUse = true;
            //bool didFindCoin = false;
            //foreach (Coin c in GameSharedData.Instance.coinsPool)
            //{
            //    if (!c.isInUse)
            //    {
            //        coin = c;
            //        coin.isInUse = true;
            //        didFindCoin = true;
            //        break;
            //    }
            //}
            //if (!didFindCoin)
            //{
            //    return;
            //}

            coin.gameObject.SetActive(true);
            if (isOrb)
            {
                coin.CreateAsOrb(enemy.transform.position);
            }
            else
            {
                coin.CreateWithLocation(enemy.transform.position);
            }
            GameSharedData.Instance.coinsInUse.Add(coin);
        }
    }

    public void GenerateDrop(Enemy enemy, int prizeID)
    {
        if (prizeID <= 0)
        {
            return;
        }
        //Debug.Log("怪物的奖励ID(掉落的id)" + prizeID);
        var dropData = CatDropScheme.Instance.GetItem(prizeID);
        int limitNum = 0;
        string[] goodsData1 = dropData.Goods.Split('|');

        List<int> randomNumberList = new List<int>();
        while (limitNum < dropData.Limit)
        {
            int r = Random.Range(0, 10000);
            //Debug.Log("随机的数" + r.ToString());
            randomNumberList.Add(r);
            limitNum++;
        }

        for (int z = 0; z < randomNumberList.Count; z++)
        {
            for (int i = 0; i < goodsData1.Length; i++)
            {
                string[] drop2 = goodsData1[i].Split(';');
                int subID = System.Convert.ToInt32(drop2[1]);
                int num = System.Convert.ToInt32(drop2[2]);
                bool hit = false;
                if (randomNumberList[z] < System.Convert.ToInt32(drop2[3]))// && randomNumberList[z] <= lastNum)
                {
                    //Debug.Log("比较数" + randomNumberList[z].ToString());
                    //Debug.Log("权重" + drop2[3]);
                    //Debug.Log("命中了第" + i + "数据");
                    hit = true;
                }
                if (!hit)
                {
                    continue;
                }
                else
                {

                    var medicamentData = MedicamentScheme.Instance.GetItem(subID);
                    //正常的掉落
                    string prefabFile;
                    float prefabScale = 1;
                    if (subID < 1000)
                    {
                        var _creature = CreatureScheme.Instance.GetItem(medicamentData.CreatureID);
                        prefabFile = _creature.Prefab;
                        prefabScale = _creature.Scale;
                    }
                    //物品掉落
                    else
                    {
                        prefabFile = "model/prefab/diaoluo/Box1";
                        //prefabScale = 1.5f;
                    }
                    int goodID = medicamentData.Id;

                    int extenNum = 0;

                    //Debug.Log("num =" + num);
                    //Debug.Log("GameSharedData.Instance.coinsInUse.Count ="+ GameSharedData.Instance.coinsInUse.Count);



                    if (GameSharedData.Instance.coinsInUse.Count >= 300) return;

                    for (int j = 0; j < num; j++)
                    {
                        if (GameSharedData.Instance.coinsInUse.Count >= 300) return;

                        Vector3 pos = enemy.transform.position;
                        GameObject copyGameObject = GameObjectPoolManager.Instance.GetItemFromPooler(prefabFile);
                        copyGameObject.transform.position = pos;
                        copyGameObject.transform.rotation = Quaternion.Euler(Vector3.zero);
                        //GameObject copyGameObject = (GameObject)Instantiate(medicamentPrefab, pos, Quaternion.Euler(Vector3.zero));
                        Coin coin = copyGameObject.GetComponent<Coin>();

                        coin.SetCollectData(goodID, medicamentData.SubType == 7 ? 5006 : 5005, medicamentData, player, num, j);
                        coin.SetIsCaculate(true);
                        copyGameObject.transform.localScale = Vector3.one * prefabScale;
                        coin.gameObject.SetActive(true);
                        if (medicamentData.SubType == 12) //狂暴点数
                        {
                            coin.CreateAsOrb(enemy.transform.position);
                        }
                        else
                        {
                            coin.CreateWithLocation(enemy.transform.position);
                        }
                        GameSharedData.Instance.coinsInUse.Add(coin);
                        //技能卷轴需要有特殊移动
                        if (medicamentData.SubType == 13)
                        {

                        }
                        if (medicamentData.SubType == 14)//银币掉落加成
                        {
                            int baseCount = 1 + LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.金币加成);
                            float basePercent = 1f + LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.金币加成) / 10000f;
                            extenNum += (int)(baseCount * basePercent);
                            ShowAddCoinTMP(enemy, extenNum);
                        }

                        //copyGameObject.GetComponent<SpriteRenderer>().sortingOrder = 100;
                        //DropCollect dropCollect = copyGameObject.AddComponent<DropCollect>();
                        //dropCollect.InitData(goodID, medicamentData.SubType == 7 ? 5006 : 5005);
                        //if (medicamentData.SubType == 7 || medicamentData.SubType == 8)
                        //{
                        //    MonsterManager.Instance.AllGoldAnExpPrefab.Add(copyGameObject, dropCollect);
                        //}
                    }

                    int leftNum = 0;
                    if (leftNum > 0)
                    {
                        for (int j = 0; j < leftNum; j++)
                        {
                            Vector3 pos = enemy.transform.position;
                            GameObject copyGameObject = GameObjectPoolManager.Instance.GetItemFromPooler(prefabFile);
                            copyGameObject.transform.position = pos;
                            copyGameObject.transform.rotation = Quaternion.Euler(Vector3.zero);
                            Coin coin = copyGameObject.GetComponent<Coin>();

                            coin.SetCollectData(goodID, medicamentData.SubType == 7 ? 5006 : 5005, medicamentData, player, num, j);
                            coin.SetIsCaculate(false);
                            copyGameObject.transform.localScale = Vector3.one * prefabScale;
                            coin.gameObject.SetActive(true);
                            if (medicamentData.SubType == 12) //狂暴点数
                            {
                                coin.CreateAsOrb(enemy.transform.position);
                            }
                            else
                            {
                                coin.CreateWithLocation(enemy.transform.position);
                            }
                            GameSharedData.Instance.extenCoinsInUse.Add(coin);
                        }
                    }

                }
                break;
            }


        }
    }


    public void DestroyEnemy(Enemy enemy)
    {
        Globals.alreadyAttacking = false;
        Globals.enemyPosInArray = -1;


#if UNITY_STANDALONE
        if(Globals.isTutorial)
        {
            // if count value becomes 5 or something // call next tutorial state
            Globals.enemyKilledCountInTutorial++;
            //log("enemyKilledCountInTutorial:%d", enemyKilledCountInTutorial );
        
        
        }

        if (Globals.isTutorial)
        {
            if (enemy.name == "tutorialEnemy1")
            {
                Globals.tutorialEnemy1Killed = true;
            }
            if (enemy.name == "tutorialEnemy2")
            {
                Globals.tutorialEnemy2Killed = true;
            }

        }
#endif
        //    if (Player::getStats().level < enemy.stats.level){


        if (GameData.instance.fileHandler.currentMission != 0)
        {
            SpawnCoinsOnDestroy(false, enemy);
        }
        else
        {
            //GameManager.instance.AddXp((int)enemy.stats.xp);
            //    if(gameType != GameType::Survival)
            if (!Globals.isTutorial)
            {
                if (Globals.canDropCoins)
                {
                    for (int i = 0; i < enemy.stats.coinAwarded; i++)
                    {
                        //TODO Coins
                        SpawnCoinsOnDestroy(false, enemy);
                        //Coins* coin = Coins::createWithLocation(enemy.enemySprite.getPosition());
                        //this.addChild(coin, 3);
                    }
                }
                if (Globals.canDropOrbs)
                {
                    if (Globals.isFTUETutorial)

                    {
                        if (enemy.stats.coinAwarded > 0 || Globals.tutorialOrbsDropState)
                        {

                            GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Enter_Berserk_Mode);
                            for (int i = 0; i < 20; i++)
                            {
                                //TODO Berserk Mode
                                //if (Globals.isFTUETutorial)
                                {

                                }
                                SpawnCoinsOnDestroy(true, enemy);
                                //Coins* orb = Coins::createAsOrb(enemy.enemySprite.getPosition());
                                //this.addChild(orb, 3);


                            }
                        }
                    }
                    else
                    {
                        if (enemy.stats.coinAwarded > 0 && GameData.instance.fileHandler.missionsCompleted >= 3)
                        {
                            //TODO Coin Orbs
                            SpawnCoinsOnDestroy(true, enemy);
                            //Coins* orb = Coins::createAsOrb(enemy.enemySprite.getPosition());
                            //this.addChild(orb, 3);
                        }
                    }

                }
            }
        }
        int numOfExp = 0;
        if (enemy.monsterType == Enemy.MonsterType.normal) numOfExp = 1;
        if (enemy.monsterType == Enemy.MonsterType.select) numOfExp = 4;
        if (enemy.monsterType == Enemy.MonsterType.elit) numOfExp = 6;
        if (enemy.monsterType == Enemy.MonsterType.boss) numOfExp = 10;
        if (enemy.explosionType == Explosions.ExplosionType.ExplosionTypeBuildingMission)
        {
            //_bg.runAction(Sequence::create(TintTo::create(0.14, 50, 50, 50), DelayTime::create(0.3), TintTo::create(1.5f, 255, 255, 255), NULL));TODO

            GameSharedData.Instance.explosions.GenerateParticlesAt(enemy.explosionType, enemy.transform.position, false, numOfExp, enemy._jsonScale / 2, 1);

        }
        else if (enemy.explosionType == Explosions.ExplosionType.ExplosionTypeBoss)
        {
            //_bg.runAction(Sequence::create(TintTo::create(0.14, 50, 50, 50), DelayTime::create(0.5), TintTo::create(1.5f, 255, 255, 255), NULL)); TODO
            GameSharedData.Instance.explosions.GenerateParticlesAt(enemy.explosionType, enemy.transform.position, false, numOfExp, 1f, 1);

        }
        else
        {
            //Debug.LogWarning("hi");
            GameSharedData.Instance.explosions.GenerateParticlesAt(enemy.explosionType, enemy.transform.position, false, numOfExp, 1f, 1);
        }

        if ((GameManager.instance.missionManager.missionType == Globals.MissionTypeKill || GameManager.instance.missionManager.missionType == Globals.MissionTypeSurvivalMode) && (Globals.gameType == GameType.Training || Globals.gameType == GameType.Survival) && !Globals.isMissionComplete)
        {
            if (enemy._allowKillPoint)
            {

                GameManager.instance.missionManager.AddPoint();

            }
        }

        if (enemy._allowKillPoint)
        {
            GameData.instance.fileHandler.totalKills++;
            GameManager.instance.killsThisRun++;
            GameManager.instance.playerHud.survivalCurPoint = GameManager.instance.killsThisRun;
            if (Globals.gameType == GameType.Survival)
            {

                if (GameManager.instance.killsThisRun == (Globals.totalEnemiesInCurrentWave + Globals.enemiesTillLastWave))
                {

                    Globals.enemiesTillLastWave += Globals.totalEnemiesInCurrentWave;
                    DOTween.Sequence().AppendInterval(1f).AppendCallback(
                        () =>
                        {
                            Observer.DispatchCustomEvent("newWave");
                        }).Play();
                }
                //TODO Add Hud
                //_hudLayer._survivalKills = _killsThisRun;
            }
        }


        if (enemy.isBoss)
        {
            print("Physics Manager Over");
            player.canHit = false;
            GameManager.instance.GameOver();
        }

        enemy.Destroy();
        if (GameManager.instance.killsThisRun >= 10 && Globals.isTutorial)
        {
            //TODO Add When Mobile Controls Added
            GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Aim_Assist_Settings);
            Globals.allowShoot = false;
        }

    }

    /// <summary>
    /// 处理玩家子弹攻击到敌人时的表现相关
    /// </summary>
    /// <param name="enemy"></param>
    /// <param name="bullet"></param>

    public void HandleButtleHitEnemyPerformance(Enemy enemy, Bullet bullet)
    {
        //回旋镖击中要有音效
        if (bullet.Type == Bullet.BulletType.Boomerang)
        {
            LuaManager.Instance.RunLuaFunction("SoundManager.CSharpPlaySound", 6026, 0, true);
            //第一次击中后停止移动
            if (BattleSkillManager.Instance.allBoomerangBullet.Count > 0)
            {
                foreach (Bullet boomerang in BattleSkillManager.Instance.allBoomerangBullet)
                {
                    boomerang.BoomerangMoveSeq.Kill();
                }
                BattleSkillManager.Instance.allBoomerangBullet.Clear();
            }
        }
        if (enemy != null && !enemy.isDestroyed)
        {
            //冰封球的减速buff
            if (bullet.bulletType == Bullet.BulletType.IceBullet || bullet.bulletType == Bullet.BulletType.IceBall)
            {
                enemy.ApplyBuff(System.Convert.ToInt32(bullet.skillData.Buff));
            }
            //主武器buff相关的
            if (GameManager.instance.player.Stats.mainWeaponUltimateSkills != -1)
            {
                //重机枪满级燃烧敌人
                if (bullet.bulletType == Bullet.BulletType.FrontMachineGun)
                {
                    enemy.ApplyBuff(GameManager.instance.player.Stats.mainWeaponUltimateSkills);
                }
            }
        }
    }

    /// <summary>
    /// 暴击飘字
    /// </summary>
    public void ShowCriticalTMP(Enemy enemy, double damage)
    {
        GameObject _criticalTMP = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.CriticalTMP);
        _criticalTMP.transform.position = (Vector2)enemy.transform.position + enemy.tmpOffset;
        float x = _criticalTMP.transform.position.x + 2;
        float y = _criticalTMP.transform.position.y + 2;
        _criticalTMP.SetActive(true);
        TextMeshPro criticaTmp = _criticalTMP.GetComponent<TextMeshPro>();
        criticaTmp.text = "暴击+" + (long)damage;
        DOTween.Sequence().Append(_criticalTMP.transform.DOMove(new Vector3(x, y, 0), 0.2f)).AppendInterval(0.3f).AppendCallback(() =>
        {
            _criticalTMP.SetActive(false);
        }).Play();
    }

    /// <summary>
    /// 金币飘字
    /// </summary>
    public void ShowAddCoinTMP(Enemy enemy, int coinNum)
    {
        //暂时屏蔽
        //GameObject _coinTMP = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.AddCoinTMP);
        //_coinTMP.transform.position = (Vector2)enemy.transform.position + enemy.tmpOffset;
        //float y = _coinTMP.transform.position.y + 1;
        //_coinTMP.SetActive(true);
        //TextMeshPro coinTmp = _coinTMP.GetComponent<TextMeshPro>();
        //coinTmp.text = "金币+" + coinNum;
        //DOTween.Sequence().Append(_coinTMP.transform.DOMoveY(y, 0.2f)).AppendInterval(0.5f).AppendCallback(() =>
        //{
        //    _coinTMP.SetActive(false);
        //}).Play();
    }
}
