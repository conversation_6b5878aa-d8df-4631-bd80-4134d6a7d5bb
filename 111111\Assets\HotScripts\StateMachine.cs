﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class StateMachine
{
    /**
     *  Create state machine
     *
     *  @return new state machine
     */
    public static StateMachine Create()
    {
        var s = new StateMachine();
        return s;
    }

    /**
     *  Find state by id and return casted state
     *
     *  @return casted state
     */
    //template<class T>
    public T FindState<T>() where T : State
    {
        string name = typeof(T).Name;
        if (states.ContainsKey(name))
        {
            return states[name] as T;
        }
        return default(T);
    }

    /**
     *  Add new state to state machine
     *
     *  @param args arguments to pass to constructor of state
     */
    //template<typename T, class... Args>
    public void AddState<T>() where T : State, new()
    {
        var typeId = typeof(T).Name;

        var state = new T();
        state.SetStateMachine(this);

        if (states == null)
            states = new Dictionary<string, State>();

        states.Add(typeId, state);
    }

    /**
     *  Check if we can enter state
     *
     *  @return true if this state is valid, false otherwise
     */
    //template<typename T>
    bool CanEnterState<T>() where T : State
    {
        if (currentState == null)
        {
            return true;
        }
        else
        {
            var state = FindState<T>() as State;
            if (state != null)
            {
                return currentState.IsValidNextState(state);
            }
        }
        return false;
    }

    /**
     *  Enters new state
     *
     *  Before entering new state old state will check if it is a valid state to execute
     *  transaction
     *
     *  Order of execution:
     *
     *  willExitWithNextState will be called on current state
     *  didEnterWithPreviousState will be called on new state
     *
     *  @return true if entered, false otherwise
     */
    //template<typename T>
    public bool EnterState<T>() where T : State
    {
        var state = FindState<T>() as State;
        if (state != null)
        {
            if (currentState == null)
            {
                currentState = state;
                currentState.DidEnterWithPreviousState(null);
                return true;
            }
            else
            {
                if (currentState.IsValidNextState(state))
                {
                    currentState.WillExitWithNextState(state);
                    state.DidEnterWithPreviousState(currentState);
                    currentState = state;
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *  Enters new state without any check if next state is valid
     *
     *
     *  Order of execution:
     *
     *  willExitWithNextState will be called on current state
     *  didEnterWithPreviousState will be called on new state
     *
     *  @return true if entered, false otherwise
     */
    //template<typename T>
    public bool SetState<T>() where T : State
    {
        var state = FindState<T>() as State;
        var previousState = currentState == null ? "NULL" : currentState.GetStateType();

        if (previousState == state.GetStateType())
            return false;

        if (state != null)
        {
            if (currentState == null)
            {
                currentState = state;
                currentState.DidEnterWithPreviousState(null);
                return true;
            }
            else
            {
                currentState.WillExitWithNextState(state);
                state.DidEnterWithPreviousState(currentState);
                currentState = state;
                return true;
            }
        }
        return false;
    }

    /**
     *  Update state machine delta time, this will call updateWithDeltaTime on current state
     *
     *  @param delta delta time
     */
    public void UpdateWithDeltaTime()
    {
        if (currentState != null)
        {
            currentState.UpdateState();
        }
    }

    /**
     *  Get current state
     *
     *  @return current state
     */
    public State GetState()
    {
        return currentState;
    }

    ~StateMachine()
    {
        currentState = null;
    }

    private Dictionary<string, State> states;
    private State currentState;
}


