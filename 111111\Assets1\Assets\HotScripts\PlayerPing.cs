using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using UnityEngine;

public class PlayerPing : MonoBehaviour
{
    public enum Type { Yellow, Red, Powerup }

    public Image image, bgImage;
    [SerializeField] RectTransform pingBGTransform;
    [SerializeField] Sprite redSprite, powerupSprite;
    RectTransform rectTransform, parentRect;
    private Transform targetTransform, playerTransform;
    private Vector2 offset;
    private bool isOnScreenEdge;

    private CanvasGroup canvasGroup;
    Camera mainCam;
    bool _allowRotation = true;

    private void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
        gameObject.SetActive(false);
    }

    public void Init(Transform targetTransform, bool isOnScreenEdge, Type pingType = Type.Yellow)
    {
        rectTransform = GetComponent<RectTransform>();
        parentRect = transform.parent.GetComponent<RectTransform>();
        offset = isOnScreenEdge ? pingBGTransform.sizeDelta / 1.5f : Vector2.zero;
        mainCam = Camera.main;
        this.targetTransform = targetTransform;
        this.playerTransform = GameManager.instance.player.transform;
        rectTransform.localScale = Vector3.one;
        this.isOnScreenEdge = isOnScreenEdge;

        if(pingType == Type.Red)
        {
            image.sprite = redSprite;
            _allowRotation = false;
            SetBackgroundVisible(false);
            rectTransform.sizeDelta *= 1.3f;
            offset = rectTransform.sizeDelta / 1.5f;
        }

        if (pingType == Type.Powerup)
        {
            image.sprite = powerupSprite;
            _allowRotation = true;
            SetBackgroundVisible(false);
            rectTransform.sizeDelta *= 1.2f;
            offset = rectTransform.sizeDelta / 1.5f;
        }
    }

    public void SetPing(bool visible)
    {
        //gameObject.SetActive(visible);
    }

    public void SetCenterVisible(bool visible)
    {
        image.color = new Color(image.color.r, image.color.g, image.color.b,
            visible ? 1 : 0);
    }

    public void SetBackgroundVisible(bool visible)
    {
        bgImage.color = new Color(bgImage.color.r, bgImage.color.g, bgImage.color.b,
            visible ? 1 : 0);
    }

    public void SetPingTexture(Sprite sprite)
    {
        image.sprite = sprite;
    }

    public void SetRotationEnabled(bool val)
    {
        _allowRotation = val;
    }

    //void LateUpdate()
    //{
    //    if (!targetTransform)
    //    {
    //        Destroy(gameObject);
    //        return;
    //    }

    //    Vector3 pingWorldPos = isOnScreenEdge ? targetTransform.position
    //        : playerTransform.position + (targetTransform.position - playerTransform.position).normalized * 2;
    //    Vector2 viewPortPosition = mainCam.WorldToViewportPoint(pingWorldPos);

    //    float angle = Vector2.SignedAngle(Vector2.right, (Vector2)(targetTransform.position - playerTransform.position));
    //    angle = angle < 0 ? 360 + angle : angle;

    //    if (isOnScreenEdge)
    //    {
    //        if (viewPortPosition.x > 0 && viewPortPosition.x < 1 && viewPortPosition.y > 0 && viewPortPosition.y < 1)
    //        {
    //            if (canvasGroup.alpha > 0)
    //                canvasGroup.alpha = 0;
    //        }
    //        else
    //        {
    //            if (canvasGroup.alpha < 1)
    //                canvasGroup.alpha = 1;
    //        }
    //    }

    //    viewPortPosition.x = Mathf.Clamp(viewPortPosition.x, 0 + offset.x / parentRect.rect.width,
    //        1 - offset.x / parentRect.rect.width);
    //    viewPortPosition.y = Mathf.Clamp(viewPortPosition.y, 0 + offset.y / parentRect.rect.height,
    //        1 - offset.y / parentRect.rect.height);

    //    rectTransform.anchoredPosition3D = new Vector3(viewPortPosition.x * parentRect.rect.width,
    //        viewPortPosition.y * parentRect.rect.height, 0);

    //    if (!_allowRotation)
    //    {
    //        pingBGTransform.rotation = Quaternion.Euler(0, 0, angle);
    //    }
    //    else
    //    {
    //        rectTransform.rotation = Quaternion.Euler(0, 0, angle);
    //    }
    //}
}
