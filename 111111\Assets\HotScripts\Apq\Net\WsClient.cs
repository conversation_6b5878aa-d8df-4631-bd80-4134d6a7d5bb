﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityWebSocket;

namespace Apq.Net
{
    /// <summary>
    /// 代表一个连接到服务端的长连接(客户端)
    /// </summary>
    public class WsClient : PersistentClient
    {
        /// <summary>
        /// 代表一个连接到服务端的长连接(客户端)
        /// </summary>
        /// <param name="provider_NewClient">提供创建新连接的方法</param>
        public WsClient(Func<object> provider_NewClient) : base(provider_NewClient)
        {
        }

        /// <summary>
        /// WebSocket
        /// </summary>
        /// <remarks>创建后才不为null</remarks>
        public WebSocket WebSocket => Client as WebSocket;

        #region 连接

        /// <summary>
        /// 设置Client相关事件的处理
        /// </summary>
        protected override void BindClientEvents()
        {
            if (WebSocket == null) return;

            // base.BindClient();
            WebSocket.OnClose += WebSocket_OnClose;
            WebSocket.OnMessage += WebSocket_OnMessage;
        }

        /// <summary>
        /// 取消Client相关事件的处理
        /// </summary>
        protected override void UnBindClientEvents()
        {
            if (WebSocket == null) return;

            // base.UnBindClient();
            WebSocket.OnClose -= WebSocket_OnClose;
            WebSocket.OnMessage -= WebSocket_OnMessage;
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public override bool Connected => WebSocket is { ReadyState: WebSocketState.Open };

        /// <summary>
        /// Client发起连接
        /// </summary>
        protected override UniTaskVoid DoTask_Connect(CancellationToken token = default)
        {
            try
            {
                WebSocket.ConnectAsync();
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                Debug.LogException(ex);
            }

            return new UniTaskVoid();
        }

        #endregion

        #region 关闭

        /// <summary>
        /// 调用Client的关闭方法
        /// </summary>
        protected override void CloseClient()
        {
            WebSocket?.CloseAsync();
        }

        private void WebSocket_OnClose(object sender, CloseEventArgs e)
        {
            OnAfterClose(e.Reason);
        }

        #endregion

        #region 发送

        /// <summary>
        /// 发送数据到服务端(实现)
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected override UniTask<bool> DoTask_Send(IList<byte> data, CancellationToken token = default)
        {
            try
            {
                WebSocket.SendAsync(data.ToArray());
                return UniTask.FromResult(true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"发送数据时异常!{Environment.NewLine}{ex?.Message}{Environment.NewLine}{ex?.StackTrace}");
                return UniTask.FromResult(false);
            }
        }

        /// <summary>
        /// 发送消息到服务端
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected override UniTask<bool> DoTask_Send(string msg, CancellationToken token = default)
        {
            try
            {
                WebSocket.SendAsync(msg);
                return UniTask.FromResult(true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"发送消息时异常!{Environment.NewLine}{ex?.Message}{Environment.NewLine}{ex?.StackTrace}");
                return UniTask.FromResult(false);
            }
        }

        #endregion

        #region 接收

        private void WebSocket_OnMessage(object sender, MessageEventArgs e)
        {
            if (e.IsText)
            {
                OnAfterReceiveMsg(e.Data);
            }

            if (e.IsBinary)
            {
                OnAfterReceiveData(e.RawData);
            }
        }

        /// <summary>
        /// 任务实现:接收
        /// </summary>
        /// <remarks>WebSocket用事件实现,无需此任务</remarks>
        protected override async UniTaskVoid DoTask_Receive(CancellationToken token = default)
        {
            await UniTask.Delay(0, cancellationToken: token);
        }

        #endregion
    }
}