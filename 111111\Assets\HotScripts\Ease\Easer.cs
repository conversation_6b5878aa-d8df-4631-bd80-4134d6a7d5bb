﻿// 来源:https://blog.csdn.net/qq_46273241/article/details/130468111
using UnityEngine;

namespace Apq
{
    /// <summary>
    /// 缓动器(进度转换：时间进度=>插值进度)
    /// </summary>
    public static class Easer
    {
        public static float Evaluate(EaseType type, float x)
        {
            float rtn;
            switch (type)
            {
                case EaseType.InSine:
                    rtn = -Mathf.Cos(x * 1.5707964f) + 1f;
                    break;
                case EaseType.OutSine:
                    rtn = Mathf.Sin(x * 1.5707964f);
                    break;
                case EaseType.InOutSine:
                    rtn = -0.5f * ((float)System.Math.Cos(System.Math.PI * x) - 1f);
                    break;
                case EaseType.InQuad:
                    rtn = x * x;
                    break;
                case EaseType.OutQuad:
                    rtn = 1f - (1f - x) * (1f - x);
                    break;
                case EaseType.InOutQuad:
                    rtn = x < 0.5f ? 2f * x * x : 1f - 2f * (1f - x) * (1f - x);
                    break;
                case EaseType.InCubic:
                    rtn = x * x * x;
                    break;
                case EaseType.OutCubic:
                    rtn = (x - 1f) * (x - 1f) * (x - 1f) + 1f;
                    break;
                case EaseType.InOutCubic:
                    rtn = x < 0.5f ? 4f * x * x * x : 1f + 4f * (x - 1f) * (x - 1f) * (x - 1f);
                    break;
                case EaseType.InQuart:
                    rtn = x * x * x * x;
                    break;
                case EaseType.OutQuart:
                    rtn = 1f - (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f);
                    break;
                case EaseType.InOutQuart:
                    rtn = x < 0.5f ? 8f * x * x * x * x : 1f - 8f * (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f);
                    break;
                case EaseType.InQuint:
                    rtn = x * x * x * x * x;
                    break;
                case EaseType.OutQuint:
                    rtn = (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f) + 1f;
                    break;
                case EaseType.InOutQuint:
                    rtn = x < 0.5f ? 16f * x * x * x * x * x : 1f + 16f * (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f) * (x - 1f);
                    break;
                case EaseType.InExpo:
                    rtn = x == 0f ? 0f : Mathf.Pow(2f, 10f * (x - 1f));
                    break;
                case EaseType.OutExpo:
                    rtn = x == 1f ? 1f : -Mathf.Pow(2f, -10f * x) + 1f;
                    break;
                case EaseType.InOutExpo:
                    rtn = x == 0f ? 0f : (x == 1f ? 1f : (x < 0.5f ? 0.5f * Mathf.Pow(2f, 10f * (2f * x - 1f)) : 0.5f * (-Mathf.Pow(2f, -10f * (2f * x - 1f)) + 2f)));
                    break;
                case EaseType.InCirc:
                    rtn = 1f - Mathf.Sqrt(1f - x * x);
                    break;
                case EaseType.OutCirc:
                    rtn = Mathf.Sqrt(1f - (x - 1f) * (x - 1f));
                    break;
                case EaseType.InOutCirc:
                    rtn = x < 0.5f ? 0.5f * (1f - Mathf.Sqrt(1f - 4f * x * x)) : 0.5f * (Mathf.Sqrt(1f - 4f * (x - 1f) * (x - 1f)) + 1f);
                    break;
                case EaseType.InOutBack:
                    rtn = x < 0.5f ? 2 * x * x * ((1.70158f * 1.525f + 1) * 2 * x - 1.70158f * 1.525f) : (1f + Mathf.Pow(2 * x - 2, 3) + 1.70158f * 1.525f * Mathf.Pow(2f * x - 2, 2)) / 2;
                    break;
                case EaseType.InBack:
                    rtn = 1.70158f * (1.525f * x * x * x - x * x);
                    break;
                case EaseType.OutBack:
                    rtn = x * x * ((1.70158f + 1) * x + 1.70158f) + 1;
                    break;
                case EaseType.InBounce:
                    rtn = 1 - Evaluate(EaseType.OutBounce, 1 - x);
                    break;
                case EaseType.OutBounce:
                    {
                        const float n1 = 7.5625f;
                        const float d1 = 2.75f;

                        if (x < 1 / d1)
                        {
                            rtn = n1 * x * x;
                        }
                        else if (x < 2 / d1)
                        {
                            rtn = n1 * (x -= 1.5f / d1) * x + 0.75f;
                        }
                        else if (x < 2.5 / d1)
                        {
                            rtn = n1 * (x -= 2.25f / d1) * x + 0.9375f;
                        }
                        else
                        {
                            rtn = n1 * (x -= 2.625f / d1) * x + 0.984375f;
                        }
                    }
                    break;
                case EaseType.InOutBounce:
                    // 递归疑似没有出口
                    rtn = x < 0.5f ? (1 - Evaluate(EaseType.InOutBounce, 1 - 2 * x)) / 2 : (1 + Evaluate(EaseType.InOutBounce, 2 * x - 1)) / 2;
                    break;
                case EaseType.InElastic:
                    rtn = (x == 0) ? 0 : ((x == 1) ? 1 : -Mathf.Pow(2, 10 * x - 10) * (float)System.Math.Sin((x * 10f - 10.75f) * (2 * System.Math.PI / 3)));
                    break;
                case EaseType.OutElastic:
                    rtn = (x == 0) ? 0 : ((x == 1) ? 1 : Mathf.Pow(2, -10 * x) * (float)System.Math.Sin((x * 10f - 0.75f) * (2 * System.Math.PI / 3)) + 1);
                    break;
                case EaseType.InOutElastic:
                    rtn = (x == 0) ? 0 : ((x == 1) ? 1 : (x < 0.5f ? -(Mathf.Pow(2, 20 * x - 10) * (float)System.Math.Sin((20 * x - 11.125f) * (2f * (float)System.Math.PI / 4.5f))) / 2 : (Mathf.Pow(2, -20 * x + 10) * (float)System.Math.Sin((20 * x - 11.125f) * (2f * (float)System.Math.PI / 4.5f))) / 2 + 1));
                    break;

                default:
                case EaseType.Linear:
                    rtn = x;
                    break;
            }

            return rtn;
        }
    }
}
