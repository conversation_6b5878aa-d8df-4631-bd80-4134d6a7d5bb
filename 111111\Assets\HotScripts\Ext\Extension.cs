﻿using System.Linq;
using System.Text;
using System.Threading;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using Spine;
using Spine.Unity;

using UniRx;

using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace X
{
    public static class Extension
    {
        public static void ClickThrottle(this Button btn, System.Action clickAction, float duration = 1.5f)
        {
            //防连击 按一下按钮就会有X秒没有反应
            btn.onClick.AsObservable().ThrottleFirst(System.TimeSpan.FromSeconds(duration)).Subscribe(_ => clickAction()).AddTo(btn.gameObject);
        }
        public static TrackEntry PlaySpine(this GameObject o, string name, bool loop)
        {
            var spine = o.GetComponentInChildren<SkeletonGraphic>();
            if (spine != null)
            {
                //Debug.Log($"{o.name} {name}");
                if (spine.SkeletonData.Animations.Any(x => x.Name == name))
                {
                    return spine.AnimationState.SetAnimation(0, name, loop);
                }
            }

            return null;
        }
        public static void PlayAnimator(this GameObject o, string name)
        {
            var anim = o.GetComponent<Animator>();
            if (anim != null)
            {
                anim.Play(name, 0, 0);
            }
            else
            {
                Debug.LogWarning($"GameObject {o.name} doesn't contain Animator component.");
            }
        }

        public static void DoFade(this CanvasGroup cg, float endValue, float seconds)
        {
            DOTween.To(() => cg.alpha, x => cg.alpha = x, endValue, seconds);
        }
        //<sprite name="9"><sprite name="9"><sprite name="9"><sprite name=","><sprite name="9"><sprite name="9"><sprite name="9">
        public static string ToStringASSpriteFont(this long n)
        {
            var s = n.ToString("N0");
            StringBuilder sb = new();
            foreach (var c in s.ToCharArray())
            {
                sb.Append($"<sprite name=\"{c}\">");
            }
            return sb.ToString();
        }
        public static string ToStringASSpriteFont(this int n)
        {
            var s = n.ToString("N0");
            StringBuilder sb = new();
            foreach (var c in s.ToCharArray())
            {
                sb.Append($"<sprite name=\"{c}\">");
            }
            return sb.ToString();
        }
        public static string ToStringASSpriteFont(this float n)
        {
            var s = n.ToString("N0");
            StringBuilder sb = new();
            foreach (var c in s.ToCharArray())
            {
                sb.Append($"<sprite name=\"{c}\">");
            }
            return sb.ToString();
        }
        public static string ToStringASSpriteFontMB(this long n)
        {
            return n switch
            {
                >= 1000_000_000 => $"{n / 1000_000_000.0f}B",
                >= 1000_000 => $"{n / 1000_000.0f}M",
                _ => n.ToString("N0")
            };
        }
        public static string ToStringASSpriteFontKMB(this long n)
        {
            return n switch
            {
                >= 1000_000_000 => $"{n / 1000_000_000.0f}B",
                >= 1000_000 => $"{n / 1000_000.0f}M",
                >= 1000 => $"{n / 1000.0f}K",
                _ => n.ToString("N0")
            };
        }
        public static string ToStringASSpriteFontKMB(this int n)
        {
            return n switch
            {
                >= 1000_000_000 => $"{n / 1000_000_000.0f}B",
                >= 1000_000 => $"{n / 1000_000.0f}M",
                >= 1000 => $"{n / 1000.0f}K",
                _ => n.ToString("N0")
            };
        }
        public static string ToStringASCommonFontKMB(this long n)
        {
            var s = n switch
            {
                >= 1000_000_000 => $"{(n / 1000_000_000.0f).ToString("G3")}B",
                >= 1000_000 => $"{(n / 1000_000.0f).ToString("G3")}M",
                >= 1000 => $"{(n / 1000.0f).ToString("G3")}K",
                _ => n.ToString("N0")
            };
            StringBuilder sb = new();
            foreach (var c in s.ToCharArray())
            {
                if (c == '.')
                {
                    sb.Append($"<sprite name=\",\">");
                }
                else
                {
                    sb.Append($"<sprite name=\"{c}\">");
                }
            }
            return sb.ToString();
        }
        /// <summary>
        /// 字符串变化时,TextMeshProUGUI跟随改变
        /// </summary>
        public static System.IDisposable SubscribeToText(this System.IObservable<string> source, TMPro.TextMeshProUGUI text)
        {
            return source.SubscribeWithState(text, (x, t) => t.text = x);
        }
        /// <summary>
        /// 实例变化时(一般是数值类),TextMeshProUGUI跟随改变
        /// </summary>
        public static System.IDisposable SubscribeToText<T>(this System.IObservable<T> source, TMPro.TextMeshProUGUI text)
        {
            return source.SubscribeWithState(text, (x, t) => t.text = x.ToString());
        }

        public static void SetY(this Transform a, float y)
        {
            var p = a.localPosition;
            p.y = y;
            a.localPosition = p;
        }
        public static void SetY(this RectTransform a, float y)
        {
            var p = a.anchoredPosition3D;
            p.y = y;
            a.anchoredPosition3D = p;
        }
        public static bool HasComponent<T>(this GameObject a) where T : Component
            => a.GetComponent<T>() != null;
        public static T GetOrAddComponent<T>(this GameObject child) where T : Component
        {
            if (!child.TryGetComponent<T>(out var result))
            {
                result = child.AddComponent<T>();
            }
            return result;
        }
        public static UniTask OnAnimationProgressAsync(this UnityEvent e, CancellationToken cancellationToken)
        {
            return new AsyncUnityEventHandler(e, cancellationToken, true).OnInvokeAsync().SuppressCancellationThrow();
        }
        public static UniTask WithTimerCancel(this UniTask e, float seconds)
        {
            return UniTask.WhenAny(e, UniTask.Delay(System.TimeSpan.FromSeconds(seconds))).SuppressCancellationThrow();
        }

        public static Vector3 GetRectCenterPosition(this Transform target)
        {
            return target.TransformPoint((target as RectTransform).rect.center);
        }
    }
}