using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
using UnityEngine.Events;

public class StoneTower : Enemy
{
    #region Variables
    private float pattern1Angle = 0;
    private UnityAction moveAction;
    private int ballsCounter = 0;
    private float posX;
    private float posY;
    static int towerType = 1;
    
    #endregion

    #region UI 
    private Bounds bounds;
    [SerializeField] ThunderAttack[] thunderAttack;
    [SerializeField] GameObject thunderAttackSpawnObject;
    [SerializeField] GameObject electricGeyserPrefab;
    [SerializeField] BoundingBoxFollower boundingBox;
    
    [SerializeField] Sprite electricBall;
    [SerializeField] Sprite blueBlast;
    [SerializeField] Texture [] blueBoost;
    [SerializeField] RuntimeAnimatorController animatorController;
    [SerializeField] RuntimeAnimatorController blueBlastAnimatorController;
    [SerializeField] Material bulletTrailMaterial;

    public List<GameObject> electricBallsArray;

    private GameObject bulletTempObject;
    private PlayerPing missionPing;
    #endregion


    // Update is called once per frame


    public void StoneTowerType(int type)
    {
        towerType = type;
    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        //StoneTowerType(1);
        allowRelocate = false;
        allowLessFrames = false;
        electricBallsArray = new List<GameObject>();
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/ElectricityBall.plist");
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/Thunder.plist");
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/ElectricMineLight.plist");
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/ElectricMineNew.plist");
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/TowerElectricBall.plist");
        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Enemy/ElectricityChargeRadial.plist"); TODO

        isBoss = false;
        InitStats();
        Globals.numberOfEnemies++;
        healthBar.gameObject.SetActive(true);
        healthBar.ScaleRatio = 2;

        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, true);


        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, false);
        explosionType = Explosions.ExplosionType.ExplosionTypeBuildingMission;
        InitializeEnemyParameters();
        StartCoroutine( InitializeTowerType() );
    }

    private void InitializeEnemyParameters()
    {
        transform.position = new Vector2(Globals.CocosToUnity(6000), Globals.LOWERBOUNDARY);
        enemySprite.state.SetAnimation(0, "Idle", true);
    }

    private void CallShootFunctionOnRepeat()
    {
        StartCoroutine(Shoot());
    }

    IEnumerator InitializeTowerType()
    {
        posX = transform.position.x;
        posY = transform.position.y;
        if (towerType==1)
        {
            StartCoroutine(Shoot());
        }

        if(towerType==2)
        {
            //Shoot2();
            InvokeRepeating(nameof(Shoot2), 0, 1.75f);
            yield return new WaitForSeconds(0.25f);
            for(int i=1;i<=3;i++)
            {
                InstantiateElectricGeyser(transform.position.x + (-Globals.CocosToUnity(400) * i), 1);
            }

            for (int i = 1; i <= 3; i++)
            {
                InstantiateElectricGeyser(transform.position.x + (Globals.CocosToUnity(400) * i), 2);
            }
        }
    }

    private void InstantiateElectricGeyser(float pos,int type)
    {
        if(type==1)
        {
            GameObject temp = Instantiate(electricGeyserPrefab.gameObject,transform);
            temp.transform.position = new Vector2(pos, temp.transform.position.y);
            temp.GetComponent<ElectricGeyser>().CreateType1();
        }
        else
        {
            GameObject temp = Instantiate(electricGeyserPrefab.gameObject, transform);
            temp.transform.position = new Vector2(pos, temp.transform.position.y);
            temp.GetComponent<ElectricGeyser>().CreateType2();
        }
    }

    void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 3.5f + Random.value;
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;

        if (towerType == 1)
        {
            stats.health = baseStats.health = 25000;

        }
        if (towerType == 2)
        {
            stats.health = baseStats.health = 1250;
        }

        stats.bulletDamage = baseStats.bulletDamage = 50;//GameData.instance.fileHandler.TrainingLevel * 50;
        stats.missileDamage = baseStats.missileDamage = 50;//GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 25;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;
    }

    private void Update()
    {
        posX = transform.position.x;
        posY = transform.position.y;
    }

    public override void Destroy()
    {
        base.Destroy();
        CancelInvoke();
       
        scheduleUpdate = false;
        GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 2, 2, 0);
                                            
        GameManager.instance.ShakeCamera(Globals.CocosToUnity(125), (int)Globals.CocosToUnity(15));
        electricBallsArray.Clear();

        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);

        //Shared::playSound("res/Sounds/SFX/enemyBuildingDestroy.mp3"); TODO
    }

    private void Shoot2()
    {
        enemySprite.state.SetAnimation(0, "Attack", false);
        enemySprite.state.AddAnimation(0, "Idle", true);

        float randomBaseValue = Random.value * Globals.CocosToUnity(360);
        for (int i = 0; i < 12; i++)
        {
            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            
            bullet.SetSpriteFrame(electricBall);
            bullet.setDamage(90);
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            bullet.GetComponent<Animator>().runtimeAnimatorController = animatorController;
            bullet.GetComponent<Animator>().Play("ElectricityBall");
            bullet.transform.DOBlendableRotateBy(new Vector3(0, 0, Globals.CocosToUnity(2000)), 5);
            bullet.transform.SetScale(1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(125));
            bullet.duration = 2;
            bullet.transform.position = new Vector2(posX, posY + Globals.CocosToUnity(400));
            bullet.transform.SetRotation(randomBaseValue + (i * 30));
            
            Vector2 dest = new Vector2(Globals.CocosToUnity(1500) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), Globals.CocosToUnity(1500) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation()));
            //bullet.transform.DOBlendableMoveBy(dest,4).SetEase(Ease.OutQuint).OnComplete(()=>
            //{
            //    bullet.RemoveWithSplash();
            //});
            bullet.PlayStoneTowerBulletAnim(2, dest,blueBlast,blueBlastAnimatorController);
            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            
            bullet.GetTrailRenderer().material = bulletTrailMaterial;
            bullet.GetTrailRenderer().transform.position = bullet.transform.position;
            //bullet.GetTrailRenderer().material.mainTexture = electricBall;
            bullet.GetTrailRenderer().gameObject.SetActive(true);
            //StartCoroutine(SetMaterialTexture(bullet));
        }
    }

    IEnumerator SetMaterialTexture(Bullet bullet)
    {
        for (int j = 0; j < blueBoost.Length; j++)
        {
            bullet.GetTrailRenderer().material.SetTexture("_MainTex", blueBoost[j]);
            yield return new WaitForSeconds(0.5f);
        }
    }

    //private void InstantiateThunderAttackObject(int lightningNumber,float angle)
    //{
    //    ThunderAttack temp = Instantiate(thunderAttack, thunderAttackSpawnObject.transform);
    //    temp._lightningNumber = lightningNumber;
    //    temp.SetLaserRotation(360 - angle);
    //    temp.SetLaserPosition(new Vector2(posX, posY + Globals.CocosToUnity(800)));
    //    temp.RotateLightning(3.0f, 40.0f);
    //}

    IEnumerator Shoot()
    {
        enemySprite.state.SetAnimation(0, "Attack3", false);
        enemySprite.state.AddAnimation(0, "Idle", true);
        pattern1Angle = 0;
        ballsCounter = 0;
        yield return new WaitForSeconds(0.5f);
        for(int i=0;i<9;i++)
        {
            yield return new WaitForSeconds((i+1)*0.05f);
            GenerateElectrictyBall();
        }

        //yield return new WaitForSeconds(2.5f - (ballsCounter * 0.1f));
        for (int i=0;i<9;i++)
        {
            electricBallsArray[i].transform.DOKill();
            electricBallsArray[i].transform.DOMove(new Vector2(posX, posY + Globals.CocosToUnity(800)), 0.25f);
            //bullet.PlayBulletAnim(0.25f, new Vector2(posX, posY + Globals.CocosToUnity(800)));
            electricBallsArray[i].transform.DOScale(3.0f, 0.5f);
        }
        
        yield return new WaitForSeconds(1f);
        AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.LightningSpell10);

        yield return new WaitForSeconds(0.75f);
        for (int i=0;i<8;i++)
        {
            float angle;
            angle = (Globals.CalcAngle(new Vector2(posX + Globals.CocosToUnity(200), posY), player.transform.position));
            angle = (Globals.CalcAngle(new Vector2(posX, posY + Globals.CocosToUnity(200)), player.transform.position));

            thunderAttack[i].gameObject.SetActive(true);
            thunderAttack[i].Init();
            thunderAttack[i]._lightningNumber = i;
            thunderAttack[i].SetLaserRotation(360 - angle);
            thunderAttack[i].SetLaserPosition(new Vector2(posX, posY + Globals.CocosToUnity(800)));
            thunderAttack[i].RotateLightning(3.0f, 40.0f);
            //thunderAttack[i].GetLaser().SetIsActive(true);
            //InstantiateThunderAttackObject(i, angle);
            //yield return new WaitForEndOfFrame();
        }
        enemySprite.state.AddAnimation(0, "Idle", true);
        
        yield return new WaitForSeconds(3);
        for (int i = 0; i < electricBallsArray.Count; i++)
        {
            if(electricBallsArray[i].activeSelf)
            {
                electricBallsArray[i].GetComponent<Bullet>().RemoveStoneTowerWithSplash(blueBlast, blueBlastAnimatorController);
            }
        }
        electricBallsArray.Clear();
        CallShootFunctionOnRepeat();
    }

    private void GenerateElectrictyBall()
    {
        ballsCounter++;
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.LightningSpell14);

        //Shared::playSound("res/Sounds/SFX/LightningSpell14.wav", false, 1); TODO
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(electricBall);
        bullet.spriteRenderer.sortingLayerName = "Enemies";
        bullet.spriteRenderer.sortingOrder = 10;
        bullet.GetComponent<Animator>().runtimeAnimatorController = animatorController;
        bullet.GetComponent<Animator>().Play("ElectricityBall");
        bullet.setDamage(100);
        bullet.duration = 5.5f;
        
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(200));
        bullet.GetComponent<Animator>().Play("ElectricityBall");
        electricBallsArray.Add(bullet.gameObject);
        bullet.transform.SetScale(0.7f);
        bullet.transform.position = new Vector2(posX, posY + Globals.CocosToUnity(630));
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
        bullet.gameObject.SetActive(true);
        bullet.transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(2550) * Mathf.Sin(Mathf.Deg2Rad * (pattern1Angle / 2 * 360 - 90)), Globals.CocosToUnity(2550) * Mathf.Cos(Mathf.Deg2Rad * (pattern1Angle / 2 * 360 - 90))),4);
        //Vector2 dest = new Vector2(Globals.CocosToUnity(2550) * Mathf.Sin(Mathf.Deg2Rad * (pattern1Angle / 2 * 360-90 )), Globals.CocosToUnity(2550) * Mathf.Cos(Mathf.Deg2Rad * (pattern1Angle / 2 * 360-90 )));
        //bullet.PlayBulletAnim(4, dest);
        pattern1Angle += 0.15f;

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        else
            return false;
    }
}


