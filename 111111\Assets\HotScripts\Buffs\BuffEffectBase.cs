﻿using System.Threading;

using Cysharp.Threading.Tasks;

using X.PB;

namespace Buffs
{
    /// <summary>
    /// BuffEffect基类(BuffEffect.csv中的每一种类型(GroupID),对应一个派生类)
    /// </summary>
    public abstract class BuffEffectBase
    {
        /// <summary>
        /// 所属Buff
        /// </summary>
        public Buff Buff { get; set; }
        public BuffEffect.Item CsvRow_BuffEffect { get; set; }

        /// <summary>
        /// 即将开始一次BuffEffect工作
        /// </summary>
        public event System.Action<BuffEffectBase> BeforeDoWork;
        /// <summary>
        /// BuffEffect工作一次后
        /// </summary>
        public event System.Action<BuffEffectBase> AfterDoWork;
        /// <summary>
        /// 即将移除BuffEffect
        /// </summary>
        public event System.Action<BuffEffectBase> BeforeDoKill;
        /// <summary>
        /// BuffEffect移除后
        /// </summary>
        public event System.Action<BuffEffectBase> AfterDoKill;

        #region Create
        /// <summary>
        /// 根据配置创建BuffEffect派生类的实例
        /// </summary>
        /// <param name="csvRow_BuffEffect"></param>
        /// <returns></returns>
        public static BuffEffectBase Create(BuffEffect.Item csvRow_BuffEffect)
        {
            BuffEffectBase rtn;
            switch (csvRow_BuffEffect.Param12)
            {
                // 默认是算作属性提升类 (但允许加入无属性的提升， 这样的提升 实际无提升作用)
                default:
                case 1:
                    {
                        rtn = new BuffPropAddition()
                        {
                            CsvRow_BuffEffect = csvRow_BuffEffect,
                        };
                    }
                    break;
                case 101:
                    {
                        rtn = new Buff击退()
                        {
                            CsvRow_BuffEffect = csvRow_BuffEffect,
                        };
                    }
                    break;
                case 102:
                    {
                        rtn = new Buff定身()
                        {
                            CsvRow_BuffEffect = csvRow_BuffEffect,
                        };
                    }
                    break;
                case 1000:
                    {
                        rtn = new Buff一次性回血()
                        {
                            CsvRow_BuffEffect = csvRow_BuffEffect,
                        };
                    }
                    break;

            }
            return rtn;
        }
        #endregion

        #region DoWork
        /// <summary>
        /// 该Effect要干的活
        /// </summary>
        public async UniTaskVoid DoWork(CancellationToken cancel)
        {
            await UniTask.SwitchToMainThread(cancel);
            DoWork_Before(cancel);
            DoWork_Do(cancel);
            DoWork_After(cancel);
        }

        protected virtual void DoWork_Before(CancellationToken cancel)
        {
            OnBeforeDoWork(cancel);
        }
        protected virtual void DoWork_Do(CancellationToken cancel)
        {

        }
        protected virtual void DoWork_After(CancellationToken cancel)
        {
            OnAfterDoWork(cancel);
        }

        /// <summary>
        /// Effect工作前
        /// </summary>
        public virtual void OnBeforeDoWork(CancellationToken cancel)
        {
            FireBeforeDoWork();
        }

        /// <summary>
        /// Effect工作后
        /// </summary>
        public virtual void OnAfterDoWork(CancellationToken cancel)
        {
            FireAfterDoWork();
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireBeforeDoWork()
        {
            BeforeDoWork?.Invoke(this);
        }
        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireAfterDoWork()
        {
            AfterDoWork?.Invoke(this);
        }
        #endregion

        #region DoKill
        /// <summary>
        /// 移除BuffEffect
        /// </summary>
        public void DoKill()
        {
            DoKill_Before();
            DoKill_Do();
            DoKill_After();
        }

        protected virtual void DoKill_Before()
        {
            OnBeforeDoKill();
        }
        protected virtual void DoKill_Do()
        {

        }
        protected virtual void DoKill_After()
        {
            OnAfterDoKill();
        }

        /// <summary>
        /// 移除前
        /// </summary>
        public virtual void OnBeforeDoKill()
        {
            FireBeforeDoKill();
        }

        /// <summary>
        /// 移除后
        /// </summary>
        public virtual void OnAfterDoKill()
        {
            FireAfterDoKill();
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireBeforeDoKill()
        {
            BeforeDoKill?.Invoke(this);
        }
        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireAfterDoKill()
        {
            AfterDoKill?.Invoke(this);
        }
        #endregion
    }
}