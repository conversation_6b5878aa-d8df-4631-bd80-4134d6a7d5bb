using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;
public class LightningAttackEnemy : MonoBehaviour
{
    public bool isInUse;

    [SerializeField] private SkeletonAnimation lightning = null;
    //[SerializeField] private SpriteRenderer alert;
    [SerializeField] private Color lightningColor;

    private float attackDelay = 1.0f;
    bool allowDmg = false;
    float mainPosX;
    private bool scheduleUpdate;
    [HideInInspector] public double damageValue;
    
    public void PlayLightningAnim()
    {
        gameObject.SetActive(true);
        allowDmg = false;
        mainPosX = GameManager.instance.player.transform.position.x + (Globals.CocosToUnity(-1000) + (Random.value * Globals.CocosToUnity(2000)));
        //alert.gameObject.SetActive(true);
        //alert.transform.position = new Vector2(mainPosX, (Globals.LOWERBOUNDARY + Globals.UPPERBOUNDARY) / 2);
        //alert.transform.SetScaleY(20);
        //DOTween.Sequence().Append(alert.DOColor(lightningColor, attackDelay)).AppendCallback(() =>
        //{
        //    alert.gameObject.SetActive(false);
        //}).Play();
        lightning.gameObject.SetActive(false);
        lightning.transform.SetWorldPositionX(mainPosX);
        DOTween.Sequence().AppendInterval(attackDelay).AppendCallback(() => {
            LuaManager.Instance.RunLuaFunction<int, int, bool>("SoundManager.CSharpPlaySound", 6025, 0, true);
            
            lightning.gameObject.SetActive(true);
            lightning.state.SetAnimation(0, "bolt", true);
            allowDmg = true;

        }).Play();

        DOTween.Sequence().AppendInterval(attackDelay + 0.5f).AppendCallback(() =>
        {
            lightning.gameObject.SetActive(false);
            mainPosX = 0;
            allowDmg = false;
            scheduleUpdate = false;
            gameObject.SetActive(false);
        });
        scheduleUpdate = true;
    }

    public void Update()
    {

        if (!scheduleUpdate)
            return;
        if (!allowDmg)
        {
            return;
        }

        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (!enemy.isDestroyed && enemy.transform.position.x < lightning.transform.position.x + Globals.CocosToUnity(120) &&
                enemy.transform.position.x > lightning.transform.position.x - Globals.CocosToUnity(120))
                {
                    LuaToCshapeManager.Instance.AddSkillDamageCount(20, damageValue);
                    if (enemy.TakeHit(damageValue))
                    {
                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            LuaToCshapeManager.Instance.curPhysicsManager.DestroyEnemy(enemy);
                        }
                    }
                }
        }

        
    }
}

