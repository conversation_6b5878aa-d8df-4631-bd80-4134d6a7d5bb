using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using Spine;
using DG.Tweening;

public class SuperGeyser : Enemy
{

    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite bulletSprite;

    private bool isActivated = false;

    public void Create()
    {
        Init();
    }

    public override void Init()
    {
        if (initialized)
            return;

        tweenId = "SuperG" + GetInstanceID();
        schedulerId = "SuperGS" + GetInstanceID();
        base.Init();
        isDestructable = false;
        InitStats();
        //SpriteFrameCache::getInstance().addSpriteFramesWithFile("res/Enemy/ElectricityBall.plist");
        //code here

        enemySprite.state.SetAnimation(0, "closed", true);
        //enemySprite.setAllowCulling(false);
        allowRelocate = false;
        transform.SetWorldPositionY(Globals.LOWERBOUNDARY - Globals.CocosToUnity(30));
        transform.SetWorldPositionX(Globals.CocosToUnity(800));
        //bounds = spSkeletonBounds_create();
        //updateBounds(NULL);
        //this.schedule(schedule_selector(SuperGeyser::updateBounds), 4, 2, 2);


        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        {

            if (spineEvent.Data.Name == "Attack")
            {
                Bullet bullet = null;
                bool didFindBullet = false;
                foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                {
                    if (!b.isInUse)
                    {
                        bullet = b;
                        bullet.isInUse = true;
                        didFindBullet = true;
                        break;
                    }
                }
                if (!didFindBullet)
                {
                    return;
                }
                bullet.setDamage(200);
                bullet.spriteRenderer.enabled = false;
                bullet.SetBulletChild(BulletChild.ElectricBall);
                bullet.duration = 1.5f;

                bullet.transform.position = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y + Globals.CocosToUnity(200));
                bullet.transform.SetRotation(0);

                bullet.transform.localScale = new Vector3(0.5f, 2.0f);
                DOTween.Sequence().SetId(bullet.tweenId).Append(bullet.transform.DOScale(Vector2.one, 2).SetEase(Ease.OutElastic)).Play();
                Vector2 dest = new Vector2(0, Globals.CocosToUnity(600));
                bullet.PlayBulletAnim(bullet.duration, dest, false, (Bullet bullet) =>
                {
                    for (int i = 0; i < 12; i++)
                    {
                        Bullet newbullet = null;
                        bool didFindBullet = false;
                        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                        {
                            if (!b.isInUse)
                            {
                                newbullet = b;
                                newbullet.isInUse = true;
                                didFindBullet = true;
                                break;
                            }
                        }
                        if (!didFindBullet)
                        {
                            return;
                        }
                        newbullet.setRadiusEffectSquared(1);
                        newbullet.setDamage(60);
                        newbullet.SetSpriteFrame(bulletSprite);
                        newbullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);

                        newbullet.transform.SetScale(0.65f);

                        newbullet.duration = 2;
                        newbullet.transform.position = bullet.transform.position;


                        newbullet.transform.SetRotation(135 + (i * 30));

                        Vector2 dest = new Vector2(Globals.CocosToUnity(700) * Mathf.Sin(Mathf.Deg2Rad * newbullet.transform.GetRotation())*-1, Globals.CocosToUnity(700) * Mathf.Cos(Mathf.Deg2Rad * newbullet.transform.GetRotation())*-1);
                        newbullet.PlayBulletAnim(2, dest, false, null, null, Ease.OutCirc);
                        //b.runAction(EaseIn::create(MoveBy::create(2, cocos2d::Point(700 * sinf(CC_DEGREES_TO_RADIANS(b.getRotation())), 700 * cosf(CC_DEGREES_TO_RADIANS(b.getRotation())))), 0.5f));

                        GameSharedData.Instance.enemyBulletInUse.Add(newbullet);
                        //GameSharedData::getInstance().g_bulletsToAddArray.pushBack(bLayer);

                    }

                }, null, Ease.OutExpo);
                DOTween.Sequence().SetId(bullet.tweenId).Append(bullet.transform.DOBlendableRotateBy(Vector3.forward * 360, 5)).Play();
                //bul
                //bullet.runAction(Shared::createAnimation("ElectrictyBall%d.png", 1, 20, true));
                //bullet.runAction(RotateBy::create(5, 360));

                GameSharedData.Instance.enemyBulletInUse.Add(bullet);

                bullet.setRadiusEffectSquared(Globals.CocosToUnity(170));
                bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            }

        };

        scheduleUpdate = true;
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;
        if (!isActivated)
        {
            if (Vector2.SqrMagnitude(player.transform.position- transform.position) < Globals.CocosToUnity(10000))
            {
                isActivated = true;
                enemySprite.state.SetAnimation(0, "open", false);
                DOTween.Sequence().SetId(schedulerId).AppendInterval(3.5f).AppendCallback(Attack).SetLoops(-1).Play();
            }
        }
    }

    private void Attack()
    {
        enemySprite.state.SetAnimation(0, "attack", false);
        enemySprite.state.AddAnimation(0, "idle", true);

    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();



        stats.speed = baseStats.speed = 2;


        stats.health = baseStats.health = (player.Stats.maxHealth.Value + 20) + Globals.difficulty * 5;
        stats.turnSpeed = baseStats.turnSpeed = 2;
        stats.bulletDamage = baseStats.bulletDamage = 4;
        stats.regen = baseStats.regen = 0;
        stats.xp = baseStats.xp = 50;
        stats.coinAwarded = baseStats.coinAwarded = 10;
        stats.missileDamage = baseStats.missileDamage = 4;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;


    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (boundingBox.CurrentCollider)
        {
            return boundingBox.CurrentCollider.bounds.Contains(P1);
        }
        else
            return false;
    }



}
