using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;


public class BattleBrushEnemyScheme : Singleton<BattleBrushEnemyScheme>
{
    private BattleBrushEnemy _data;
    private Dictionary<int, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load(false);
        }
    }
    public bool Load(bool isReadResources)
    {
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.BattleBrushEnemy;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            byte[] by;
            if (isReadResources)
            {
                by = HotResManager.ReadPbFromResources(pbFileName);
            }
            else
            {
                by = HotResManager.ReadPb(pbFileName);
            }
            MemoryStream ms = new MemoryStream(by);
            _data = Serializer.Deserialize<BattleBrushEnemy>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].Id] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;

    }
    public BattleBrushEnemy.Item GetItem(int id, bool isReadResources = false)
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<int, int>();
            Load(isReadResources);
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            throw new Exception("id dont exist");
        }

    }
}


