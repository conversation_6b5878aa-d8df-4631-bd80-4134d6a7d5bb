using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System.Linq;
public class UnlockManager : MonoBehaviour
{
    [SerializeField] private Image blackOverlay;
    [SerializeField] private UnlockItem[] item;


    private string schedulerId;
    private string tweenId;
    private int itemCount;
    private int currentItem;
    private PList MainMap;

    private void Awake()
    {
        schedulerId = "UnlockMS" + GetInstanceID();
        tweenId = "UnlockM" + GetInstanceID();
    }

    public void Show()
    {
        Init();
    }

    private void Init()
    {


        //SpriteFrameCache::getInstance()->addSpriteFramesWithFile("res/Shop/ShopSpriteSheet.plist");
        if (GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission).ContainsKey("Unlock") && GameData.instance.fileHandler.missionsCompleted < GameData.instance.fileHandler.currentMission)
        {
            gameObject.SetActive(true);
            MainMap = GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Unlock"] as PList;
            Globals.StopAllSounds();
            DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(1, 0.5f)).Play();

            currentItem = 0;
            itemCount = MainMap.Count;

            UnlockItemNow();

            Observer.RegisterCustomEvent(gameObject, "UnlockComplete", () =>
             {
                 UnlockItemNow();
             });

        }
    }

    private void UnlockItemNow()
    {
        if (currentItem<itemCount)
        {
            item[currentItem].Show(MainMap.ElementAt(currentItem).Key,MainMap);
            currentItem++;
        }
        else
        {
            DOTween.Sequence().SetId(tweenId).Append(blackOverlay.DOFade(0, 0.5f)).AppendCallback(()=> {
                gameObject.SetActive(false);
                Observer.DispatchCustomEvent("needOpenReward");
            }).Play();
        }

    }
}
