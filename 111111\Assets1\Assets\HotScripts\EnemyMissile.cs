using UnityEngine;
using System.Collections;
using System;
using DG.Tweening;
public class EnemyMissile : MonoBehaviour
{
    public SpriteRenderer missileSprite;
    [HideInInspector] public bool hasHit;
    [HideInInspector] public bool isCollidable = true;
    [HideInInspector] public bool _doExplodeOnWater = false;
    [HideInInspector] public float radiusSQ = 0.12f;
    [HideInInspector] public bool reactToWater = true;
    [HideInInspector] public float duration = 5;
    [HideInInspector] public bool allowPing = false;
    [HideInInspector] public bool isDestructable = true;
    [HideInInspector] public bool _allowComboPoint = false;
    [HideInInspector] public bool isInUse = false;
    [HideInInspector] public PlayerController player;
    [HideInInspector] public bool scheduleUpdate = false;
    [HideInInspector] public Explosions.ExplosionType explosionType = Explosions.ExplosionType.ExplosionTypeAir2;

    public System.Action endFunc;

    [HideInInspector] public string tweenId;
    [HideInInspector] public string schedulerId;

    private double damage;
    protected bool createExplosion = true;

    private void Start()
    {
        player = GameManager.instance.player;
    }

    public virtual void Init()
    {
        scheduleUpdate = true;
        gameObject.SetActive(true);
    }

    public void RemoveAfterDuration()
    {
        createExplosion = false;
        DOTween.Sequence().SetId(schedulerId).AppendInterval(duration).AppendCallback(RemoveMissile).Play();
    }

    public void SetDamage(double val)
    {
        damage = val;
    }

    public double GetDamage()
    {
        return damage;
    }


    public virtual void RemoveMissile()
    {
        if (gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(IRemoveMissle());
        }
    }

    private IEnumerator IRemoveMissle()
    {
        if (createExplosion)
        {
            if (Vector2.SqrMagnitude(player.transform.position - transform.position) < Globals.CocosToUnity(640))
            {
                float scale = 1.3f;
                if (explosionType == Explosions.ExplosionType.ExplosionTypeWXREnemy1)
                {
                    scale = 0.5f;
                }
                GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1, scale, 0);
            }
            else
            {
                float scale = 1.3f;
                if (explosionType == Explosions.ExplosionType.ExplosionTypeWXREnemy1)
                {
                    scale = 0.5f;
                }
                GameSharedData.Instance.explosions.GenerateParticlesAt(explosionType, transform.position, false, 1, scale, 0);
            }
            if (endFunc != null)
            {
                endFunc();
            }
        }
        endFunc = null;
        CancelInvoke();
        //transform.position = Vector3.zero;
        //transform.rotation = Quaternion.identity;
        //await Task.Delay(200);
        yield return new WaitForEndOfFrame();
        DOTween.Kill(tweenId);
        DOTween.Kill(schedulerId);
        gameObject.SetActive(false);
        GameSharedData.Instance.enemyMissilesInUse.Remove(this);
        ResetValues();
    }
    private void ResetValues()
    {
        hasHit =false;
        isCollidable = true;
        _doExplodeOnWater = false;
        radiusSQ = 0.12f;
        reactToWater = true;
        duration = 5;
        allowPing = false;
        isDestructable = true;
        _allowComboPoint = false;
        isInUse = false;
        scheduleUpdate = false;
        missileSprite.enabled = true;
        createExplosion = true;
    }

}