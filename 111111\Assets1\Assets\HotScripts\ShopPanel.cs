﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ShopPanel : MonoBehaviour
{
    [SerializeField] Shop shop;
    [SerializeField] Transform buttonsContainer;
    [SerializeField] int panelIndex;

    List<ShopButton> shopButtonsList;
    Action onAnimationComplete;
    bool isInitialized;

    private void Awake() 
    {
        shopButtonsList = new List<ShopButton>();

        for(int i = 0; i < buttonsContainer.childCount; i++)
        {
            ShopButton button = buttonsContainer.GetChild(i).GetComponent<ShopButton>();
            button.shop = shop;
            button.Category = panelIndex;
            button.onTap = ResetAllShopButtons;
            button.onSelect = ResetSelections;
            shopButtonsList.Add(button);
        }

        ResetAllShopButtons();
    }

    void Init()
    {
        foreach (ShopButton shopButton in shopButtonsList)
        {
            shopButton.Init();
        }
    }

    public int CheckUnlockedItems()
    {
        int unlocks = 0;

        for(int i = 0; i < shopButtonsList.Count; i++)
        {
            unlocks += shopButtonsList[i].CheckIfUnlocked();
        }

        return unlocks;
    }

    public void SelectItem(int itemIndex)
    {
        foreach (ShopButton shopButton in shopButtonsList)
        {
            shopButton.ToggleButtonsContainer = false;
        }

        shop.SelectItem(itemIndex);
    }

    public void ResetSelections(ShopButton exception)
    {
        foreach (ShopButton shopButton in shopButtonsList)
        {
            if (shopButton == exception)
                continue;

            shopButton.MouseExit();
        }
    }

    public void ResetShopPanel()
    {
        if(!isInitialized)
        {
            Init();
            isInitialized = true;
        }    

        ResetAllShopButtons();

        foreach (ShopButton shopButton in shopButtonsList)
        {
            shopButton.CheckUnlockedItems();
        }
    }

    void ResetAllShopButtons(ShopButton buttonException = null)
    {
        foreach (ShopButton shopButton in shopButtonsList)
        {
            shopButton.ToggleButtonsContainer = false;

            if (buttonException != null)
                if (buttonException == shopButton)
                    continue;

            shopButton.ExpandButton(false);
        }

        if (buttonException == null)
            return;

        StopCoroutine(nameof(CheckButtons));
        onAnimationComplete = null;

        if(shop.GetCurrentTab == Shop.TabType.FrontGuns)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.openShopItem);
            if (buttonException.IsExpanded)
            {
                shop.SetFrontGun(buttonException.ButtonIndex);
            }
            else
            {
                shop.ResetFrontGun();

            }
        }
        else if (shop.GetCurrentTab == Shop.TabType.AltGuns)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.openShopItem);
            if (buttonException.IsExpanded)
            {
                shop.SetRearGun(buttonException.ButtonIndex);
            }
            else
            {
                shop.ResetRearGun();

            }
        }
        else if (shop.GetCurrentTab == Shop.TabType.Planes)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.openShopItem);
            if (buttonException.IsExpanded)
            {
                shop.SetPlane(buttonException.ButtonIndex);
            }
            else
            {
                shop.ResetPlane();
            }
        }

        if (buttonException.IsExpanded)
        {
            onAnimationComplete = () =>
            {
                buttonException.ToggleButtonsContainer = true;
            };
            StartCoroutine(nameof(CheckButtons));
        }

        buttonException.ResetButton();
    }

    public void DisableButtons(int exceptIndex)
    {
        for(int i = 0; i < shopButtonsList.Count; i++)
        {
            if(i != exceptIndex) shopButtonsList[i].gameObject.SetActive(false);
        }
    }

    IEnumerator CheckButtons()
    {
        yield return new WaitForSeconds(0.083f);

        onAnimationComplete?.Invoke();
    }
}
