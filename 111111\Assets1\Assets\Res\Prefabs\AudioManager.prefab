%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &392707393683778122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4187243804979095291}
  - component: {fileID: 2575651285364003253}
  - component: {fileID: 4864057966630547526}
  m_Layer: 0
  m_Name: SoundEffect3DSource (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4187243804979095291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392707393683778122}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &2575651285364003253
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392707393683778122}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &4864057966630547526
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392707393683778122}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 2575651285364003253}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &1188173204980963111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3315037099578866090}
  - component: {fileID: 6363274561766485630}
  - component: {fileID: 6016804774713581884}
  m_Layer: 0
  m_Name: SoundEffect3DSource (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3315037099578866090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1188173204980963111}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6363274561766485630
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1188173204980963111}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &6016804774713581884
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1188173204980963111}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 6363274561766485630}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &1644712824845335200
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7147020984401888376}
  - component: {fileID: 1033895150410724152}
  - component: {fileID: 5657025764921847066}
  m_Layer: 0
  m_Name: SoundEffect3DSource (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7147020984401888376
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1644712824845335200}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1033895150410724152
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1644712824845335200}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &5657025764921847066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1644712824845335200}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 1033895150410724152}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &2252445030775214682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 635044836300318544}
  - component: {fileID: 8158753256572308259}
  - component: {fileID: 3304367942069551406}
  m_Layer: 0
  m_Name: SoundEffect3DSource (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &635044836300318544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2252445030775214682}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &8158753256572308259
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2252445030775214682}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &3304367942069551406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2252445030775214682}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 8158753256572308259}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &2474043055586341122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4233411909791810701}
  - component: {fileID: 4746716595723826752}
  - component: {fileID: 263035344900176213}
  m_Layer: 0
  m_Name: SoundEffect3DSource (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4233411909791810701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2474043055586341122}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &4746716595723826752
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2474043055586341122}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &263035344900176213
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2474043055586341122}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 4746716595723826752}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &4380851297098907235
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6738129773489766917}
  - component: {fileID: 5491987280966670870}
  - component: {fileID: 6348920267133801268}
  m_Layer: 0
  m_Name: SoundEffect3DSource (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6738129773489766917
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380851297098907235}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &5491987280966670870
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380851297098907235}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &6348920267133801268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380851297098907235}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 5491987280966670870}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &4642962360066324167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7922057710983509376}
  - component: {fileID: 5866544678572644009}
  - component: {fileID: 7111340868038204404}
  m_Layer: 0
  m_Name: SoundEffect3DSource (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7922057710983509376
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4642962360066324167}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &5866544678572644009
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4642962360066324167}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &7111340868038204404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4642962360066324167}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 5866544678572644009}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &4745621312820346928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2431520982064035744}
  - component: {fileID: 7860078898571876030}
  m_Layer: 0
  m_Name: OneShotMusicSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2431520982064035744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4745621312820346928}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &7860078898571876030
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4745621312820346928}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &5777917035471334724
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7674572986215306027}
  - component: {fileID: 895731685079749564}
  - component: {fileID: 2423286711960392760}
  m_Layer: 0
  m_Name: SoundEffect3DSource (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7674572986215306027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5777917035471334724}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &895731685079749564
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5777917035471334724}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &2423286711960392760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5777917035471334724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 895731685079749564}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &6655122780313497209
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122780313497210}
  - component: {fileID: 6655122780313497211}
  m_Layer: 0
  m_Name: EnemiesSoundEffect
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122780313497210
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780313497209}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122780313497211
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780313497209}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122780628422539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122780628422261}
  - component: {fileID: 6655122780628422260}
  m_Layer: 0
  m_Name: PlayerAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122780628422261
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780628422539}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122780628422260
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780628422539}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122780720364829
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122780720364830}
  - component: {fileID: 6655122780720364831}
  m_Layer: 0
  m_Name: MuteSoundEffect
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122780720364830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780720364829}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122780720364831
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780720364829}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 1
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122780823891838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122780823891839}
  - component: {fileID: 6655122780823891832}
  m_Layer: 0
  m_Name: LoopSoundEffects
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122780823891839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780823891838}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122780823891832
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122780823891838}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122781465531111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122781465531104}
  - component: {fileID: 6655122781465531105}
  m_Layer: 0
  m_Name: ExplosionsSoundEffect
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122781465531104
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781465531111}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122781465531105
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781465531111}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122781565341830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122781565341825}
  - component: {fileID: 6655122781565341824}
  - component: {fileID: 6655122781565341831}
  m_Layer: 0
  m_Name: AudioManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122781565341825
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781565341830}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6655122780628422261}
  - {fileID: 6655122781721825523}
  - {fileID: 6655122780823891839}
  - {fileID: 6655122780720364830}
  - {fileID: 6655122781465531104}
  - {fileID: 6655122780313497210}
  - {fileID: 5069697974286447727}
  - {fileID: 6626360667607635258}
  - {fileID: 3315037099578866090}
  - {fileID: 7922057710983509376}
  - {fileID: 5463591851617981192}
  - {fileID: 4233411909791810701}
  - {fileID: 635044836300318544}
  - {fileID: 6738129773489766917}
  - {fileID: 7674572986215306027}
  - {fileID: 7147020984401888376}
  - {fileID: 4187243804979095291}
  - {fileID: 396749798349851578}
  - {fileID: 2431520982064035744}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6655122781565341824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781565341830}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8bc8ca40a523a4cbb9a7d3b5378c9383, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  musicSource: {fileID: 6655122781565341831}
  oneShotMusicSource: {fileID: 7860078898571876030}
  playerSource: {fileID: 6655122780628422260}
  normalAudioSource: {fileID: 6655122781721825522}
  loopAudioSource:
  - {fileID: 6655122780823891832}
  muteAudioSource: {fileID: 6655122780720364831}
  explosionsAudioSource: {fileID: 6655122781465531105}
  enemiesAudioSource: {fileID: 6655122780313497211}
  audioSource3D: {fileID: 7860078898571876030}
  tracks:
  - {fileID: 8300000, guid: 9fb1771baac3ba84ba802432d24ae15d, type: 3}
  - {fileID: 8300000, guid: bf390660e6dfc4c608a791e1aedd2504, type: 3}
  - {fileID: 8300000, guid: bf390660e6dfc4c608a791e1aedd2504, type: 3}
  - {fileID: 8300000, guid: ac223611e5b734124b5ae2a9ded8b348, type: 3}
  - {fileID: 8300000, guid: 6583fba6ac64249039a57302748c0408, type: 3}
  - {fileID: 8300000, guid: 0475dcad571ba4acda44b174d6f3f8e8, type: 3}
  - {fileID: 8300000, guid: e40d8e8d298854f9cbf9eb2d89f9c66b, type: 3}
  - {fileID: 8300000, guid: 915d96403f886445bbcd61993cfbfbfb, type: 3}
  - {fileID: 8300000, guid: a87f73dcbae01445dac8e5523607f992, type: 3}
  - {fileID: 8300000, guid: 13822ea300fe14d6fb4ee467a612db3a, type: 3}
  - {fileID: 8300000, guid: a3d605d41fd6e441e8fb29ba3d1ec852, type: 3}
  - {fileID: 8300000, guid: a3ee9b2f4012845d0b4d97399334dfc3, type: 3}
  - {fileID: 8300000, guid: 036dce112d05847a980817109f37135e, type: 3}
  - {fileID: 8300000, guid: 383562f9836354e38a1a1b31daa86604, type: 3}
  - {fileID: 8300000, guid: cc1a1034abb2446c4afaa3f1f3fce070, type: 3}
  soundEffects:
  - name: spikyIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: 15bbc3f5b0e124454b12508fa3d24630, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: spikyShoot
    type: 0
    clips:
    - {fileID: 8300000, guid: 542f415b4ab97454eb8b1127a7c7b28e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: spIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: 811da7220cc93430ab93d534ac498b7d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: spShoot
    type: 0
    clips:
    - {fileID: 8300000, guid: 75f79f105050f431a9353b25cdcc6c8d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: cutinIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: 0dceec72b6d0741fe98fca73ece3b826, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: explosion
    type: 0
    clips:
    - {fileID: 8300000, guid: 8d059c59bfe1a4af296582329e2a2d64, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: flying
    type: 0
    clips:
    - {fileID: 8300000, guid: 1b882d3e259314f69aab3928ef1559c2, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: trigger
    type: 0
    clips:
    - {fileID: 8300000, guid: a9748bccc5ba3444cba07aba7c82738d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: barkMeowIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: aa9b8b0a48e544820a48dee73023a601, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: backMeowLaserOff
    type: 0
    clips:
    - {fileID: 8300000, guid: bd7225846f6114c63a110c3f22788707, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: barkMeowLaserLoop
    type: 0
    clips:
    - {fileID: 8300000, guid: 06f93b8ea52ac4b20b726a65f170323f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: barkMeowLaserOn
    type: 0
    clips:
    - {fileID: 8300000, guid: a2b44e6ce5f6f411a95b9a8ccac641b6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: mafiaIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: 80aa1559c971a48fb936e2f196de3e65, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Spider Cat - Bullet 1 (loop)
    type: 0
    clips:
    - {fileID: 8300000, guid: db462c930e9c84854b1704f5e5d30f14, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Spider Cat - Bullet 1 (Trigger OFF)
    type: 0
    clips:
    - {fileID: 8300000, guid: 1c5c9d895214d4089b52d88d5cb4f41b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Spider Cat - Bullet 1 (Trigger ON)
    type: 0
    clips:
    - {fileID: 8300000, guid: 22d92b833c2d949078c095124ea05be0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: spiderCatBullet2
    type: 0
    clips:
    - {fileID: 8300000, guid: 492974010c94f4015adcb01d8ddc1108, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SpiderCatFall
    type: 0
    clips:
    - {fileID: 8300000, guid: 12e9eacf762da4b399ed6e959b07fec0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SpiderCatIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: 7af15b189cf49433b8da0bc169230077, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SpiderCatJump
    type: 0
    clips:
    - {fileID: 8300000, guid: 344ee60e3c8824f24ab886e5fd47494e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Greek Egyptian - Bullet
    type: 0
    clips:
    - {fileID: 8300000, guid: 3781132d715e949debc948979fa39749, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Greek Egyptian - Intro
    type: 0
    clips:
    - {fileID: 8300000, guid: cbfc56499fe8d4137a0abd755c2203ab, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: sentinelAttack
    type: 0
    clips:
    - {fileID: 8300000, guid: 1e88454f39b5544588b42ebdbf3ace6c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: sentinelLaser
    type: 0
    clips:
    - {fileID: 8300000, guid: 66d7dbebcf75b4b48b431b08a6c937dc, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: laserLooped
    type: 0
    clips:
    - {fileID: 8300000, guid: 6268df86198d74595a7bf68fd679529c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SentinelIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: ecf1cc5bd514d440294ffaa61e2e1c2e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: dragonAttack
    type: 0
    clips:
    - {fileID: 8300000, guid: 2c6801edfdf9d4b85902143fe57370e1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: dragonSistersIntro
    type: 0
    clips:
    - {fileID: 8300000, guid: cf74ecf4dae6840b69d534a754f00563, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: startFire
    type: 0
    clips:
    - {fileID: 8300000, guid: fc80f2481b38c4de099cf7a60f8e8ca6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Tiny Bot - BG (loop)
    type: 1
    clips:
    - {fileID: 8300000, guid: 0ab592672117b4dca9b67960b692675e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Complex Interface 5
    type: 0
    clips:
    - {fileID: 8300000, guid: 7f9959addaad5437791ac7f2584e4fa4, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: creature demonic (1)
    type: 0
    clips:
    - {fileID: 8300000, guid: 9ec0966afd15d4d62b439bb4034ce6b6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Device 2 Stop
    type: 0
    clips:
    - {fileID: 8300000, guid: c9344024cc4c04aa5ba6ec149a63742d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Door 1 Close 1
    type: 0
    clips:
    - {fileID: 8300000, guid: 16fcd3ec39e224f7190a852c07c21dfe, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Door 3 Close 1
    type: 0
    clips:
    - {fileID: 8300000, guid: d06e663fb7acd48388749f87448ffb73, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Door 8 Close 2
    type: 0
    clips:
    - {fileID: 8300000, guid: 16b779f7bd5274eb39c0be2813326d24, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Door 9 Open
    type: 0
    clips:
    - {fileID: 8300000, guid: 1bb11086c9f764343bfa6b1077101609, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Entry
    type: 0
    clips:
    - {fileID: 8300000, guid: e25aa1a77b66e431ba9db8fcabd855b8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: footstep 9 (2)
    type: 0
    clips:
    - {fileID: 8300000, guid: 815dc2f3288f74e36a12d3e0401b0611, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: gunAttack
    type: 0
    clips:
    - {fileID: 8300000, guid: b38b97239d88c44db9ccc25c4973d412, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: gunAttack1
    type: 0
    clips:
    - {fileID: 8300000, guid: df198cf4d27004c56a99b5b8998f04ba, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: MediumExplosion15
    type: 0
    clips:
    - {fileID: 8300000, guid: f3c9091df169d41a6bc29ce272c4a897, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Monster Big Walking Distant 1_02
    type: 0
    clips:
    - {fileID: 8300000, guid: f9e2ffb6a5e884878b0c0a973d072959, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: phase3Attack
    type: 0
    clips:
    - {fileID: 8300000, guid: 5c70941466d614433a00adfeab2a97b8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: tranisiton
    type: 0
    clips:
    - {fileID: 8300000, guid: fb3ed73f9a546409a8e75518f4403657, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Loop 1
    type: 0
    clips:
    - {fileID: 8300000, guid: f0c2351d7f638487081ee20ff4549da8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Loop 2
    type: 0
    clips:
    - {fileID: 8300000, guid: 0db9d3813122d49bfabda1c9ac5f987e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Loop 3
    type: 0
    clips:
    - {fileID: 8300000, guid: b7ea160dba1aa449fa04b8eb8c1d9f16, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Loop 4
    type: 0
    clips:
    - {fileID: 8300000, guid: 68fe2ab56a9cf4d65895c1053e124947, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Loop 5
    type: 0
    clips:
    - {fileID: 8300000, guid: 40630fd4cc0b8425cb8242b112374f08, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 1
    type: 0
    clips:
    - {fileID: 8300000, guid: 61b4bca0c582446c09855991f09fe50a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 2
    type: 0
    clips:
    - {fileID: 8300000, guid: 30f9f68f6624b4f1585a9570be1e6cbd, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 3
    type: 0
    clips:
    - {fileID: 8300000, guid: 43e369dc03f484808928dd513f3d94eb, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 4
    type: 0
    clips:
    - {fileID: 8300000, guid: d4e303b86521f42ca970730c2e2eb2f8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 5
    type: 0
    clips:
    - {fileID: 8300000, guid: 21c1a58b5239c4a7e99d4d5ed47c460b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 6
    type: 0
    clips:
    - {fileID: 8300000, guid: 5e9c3e045533e44d7b087a87e531a128, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 7
    type: 0
    clips:
    - {fileID: 8300000, guid: f4efd66f30498452d962edbab7192af0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 8
    type: 0
    clips:
    - {fileID: 8300000, guid: 6e1c32e5f18a844c688f84a79984a9c1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 9
    type: 0
    clips:
    - {fileID: 8300000, guid: 55c3e9e80d78e48848480aea65b329ff, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 10
    type: 0
    clips:
    - {fileID: 8300000, guid: 7415c244b315140cd8477aa100231400, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 11
    type: 0
    clips:
    - {fileID: 8300000, guid: 43242fd0ec0c1472e9ff83579c0e2a20, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 12
    type: 0
    clips:
    - {fileID: 8300000, guid: 65915ce2040bc4f138438a4bd37a1406, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 13
    type: 0
    clips:
    - {fileID: 8300000, guid: 62d0e7a7f14e5431983a2814f7a6d943, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 14
    type: 0
    clips:
    - {fileID: 8300000, guid: f7aaa1312f6f94f518136d4be047922a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Underwater Movement 15
    type: 0
    clips:
    - {fileID: 8300000, guid: 481020f1a1a544a3fbcff024931dbc18, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Weapon 10 Shoot 1
    type: 0
    clips:
    - {fileID: 8300000, guid: 05ebcc5687b524fe480e296362d725fa, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Weapon 14 Shoot 3 (Flamethrower)
    type: 0
    clips:
    - {fileID: 8300000, guid: b3c01126fc5fd4f4c91819317f7adccc, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: backFireSound
    type: 0
    clips:
    - {fileID: 8300000, guid: b4e8c513c224944beb1b7ba850351214, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: boss_unlock
    type: 0
    clips:
    - {fileID: 8300000, guid: 06ed2d5e999ec4c629efaa1715676382, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: buttonTap
    type: 0
    clips:
    - {fileID: 8300000, guid: 2192c343ecbf54456b55298ba18cb0ba, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: buyButton
    type: 0
    clips:
    - {fileID: 8300000, guid: 9538039bc76ae4fb69bba1f6c19d7927, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: catsWalkerAttack
    type: 0
    clips:
    - {fileID: 8300000, guid: ca6c6d4984b04499d83dc1fb48db6904, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: chest_drop
    type: 0
    clips:
    - {fileID: 8300000, guid: 8bd0c65986b0747f7908c9816e9a178f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: chest_unlock
    type: 0
    clips:
    - {fileID: 8300000, guid: 302df4f29749f480c8175ac8bb7dad2e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: chestIdle
    type: 0
    clips:
    - {fileID: 8300000, guid: 350b07a3aba2f42429ea65ea671954bc, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: chestOpen
    type: 0
    clips:
    - {fileID: 8300000, guid: 617615e0db5fe481c8a553db4a7baaa9, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: click
    type: 0
    clips:
    - {fileID: 8300000, guid: b9c398ed2b5a04884a10e01fb4578213, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: coinCollected
    type: 0
    clips:
    - {fileID: 8300000, guid: 649e53c57bd264312b66619e3ec23ba1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: cursorClick
    type: 0
    clips:
    - {fileID: 8300000, guid: dbe168fae29e64d6c9ee5f87251dd447, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: deatomizerSound
    type: 0
    clips:
    - {fileID: 8300000, guid: a8c6612fb1e694e8c869db25fa0df4e9, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: empty
    type: 0
    clips:
    - {fileID: 8300000, guid: f01a8fb68fd564296bf61a0c2e952a38, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: empty2
    type: 0
    clips:
    - {fileID: 8300000, guid: 1cb3de7cf0c514dd3b8984e6cc2a609a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: enemyBeingHit
    type: 0
    clips:
    - {fileID: 8300000, guid: 169b59f1556be4a25904551e9a4e4c9a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: enemyBuildingDestroy
    type: 0
    clips:
    - {fileID: 8300000, guid: 9f170f73ec0be4e388b01365592f23f3, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: enemyHit
    type: 0
    clips:
    - {fileID: 8300000, guid: cad70184c2b6c4cea89d271f63b0393b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: explosionEnemyPlane
    type: 0
    clips:
    - {fileID: 8300000, guid: 7509b29dc8e9e43b7b78b3cf93f04449, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: explosionEnemyPlane2
    type: 0
    clips:
    - {fileID: 8300000, guid: 4afd5bfa017254e86aba57751000b496, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: flameThrowerShoot
    type: 0
    clips:
    - {fileID: 8300000, guid: 5a62f9840ebea4172ac4fe1cdf7a713a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: hairball
    type: 0
    clips:
    - {fileID: 8300000, guid: 4190f3bb2c0ba499088c758fde9c9ac5, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: kerBlasterFire
    type: 0
    clips:
    - {fileID: 8300000, guid: a7e6b0bbaedc3443bb4c9b1d986ccfca, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: laserEnd
    type: 0
    clips:
    - {fileID: 8300000, guid: b9ea7dc9ca1254c62b93d3ffffbcfc5c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: laserLoop
    type: 0
    clips:
    - {fileID: 8300000, guid: 3000a2ac344c34db0bf2ecb9b7ea973e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: laserStart
    type: 0
    clips:
    - {fileID: 8300000, guid: 343bd8816e4054c41aee5e98259bf16d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: levelUp
    type: 0
    clips:
    - {fileID: 8300000, guid: d2d60a9dae0d54dd28427f93a7b29a84, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: lightning
    type: 0
    clips:
    - {fileID: 8300000, guid: ddabf4fa1076a49afa250e0c54ac4daa, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell1
    type: 0
    clips:
    - {fileID: 8300000, guid: 1fc420344c5424a92ad6206e8807e6c6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell2
    type: 0
    clips:
    - {fileID: 8300000, guid: db9f56015cfd744bba11cbd6a31b57c7, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell3
    type: 0
    clips:
    - {fileID: 8300000, guid: d5af5bc827779433faa697452d6de377, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell4
    type: 0
    clips:
    - {fileID: 8300000, guid: 5fc2503b4cf1746faa13f172b12f8c86, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell5
    type: 0
    clips:
    - {fileID: 8300000, guid: bb65d6a67b8e84e08b78a0e2562a814c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell6
    type: 0
    clips:
    - {fileID: 8300000, guid: aeafbf8facadb469388b6f059f1fc419, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell7
    type: 0
    clips:
    - {fileID: 8300000, guid: 25b4fbff2269b4aeeac939ca9e64a12c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell8
    type: 0
    clips:
    - {fileID: 8300000, guid: 6ab361415100b4025a36540175219db0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell9
    type: 0
    clips:
    - {fileID: 8300000, guid: 02013eea829694c86bc55694f7fad1d6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell10
    type: 0
    clips:
    - {fileID: 8300000, guid: f916049fd98644a25bf61fc963b413dd, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell11
    type: 0
    clips:
    - {fileID: 8300000, guid: 96ea401aa678b4917b04673b8be3f1b2, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell12
    type: 0
    clips:
    - {fileID: 8300000, guid: cb9fdb7fc4df1464d8452e8fdef9961d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell13
    type: 0
    clips:
    - {fileID: 8300000, guid: 4124a61304c904ff1920698e2edf0f30, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell14
    type: 0
    clips:
    - {fileID: 8300000, guid: fdae7db991b1643a5af964e8b4f42909, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: LightningSpell15
    type: 0
    clips:
    - {fileID: 8300000, guid: 3c67e1a69d36c485dba7a02a9a1e4926, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: looseCannon
    type: 0
    clips:
    - {fileID: 8300000, guid: 0d73d83adfe464c13bdb7cb493bcc36f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: magicFire
    type: 0
    clips:
    - {fileID: 8300000, guid: 4c5907910cbee4b208ae72ab58042629, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: mapButtonActivate
    type: 0
    clips:
    - {fileID: 8300000, guid: 559811adbfadd48a0b13fde9abe300f2, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: mapTrailTick
    type: 0
    clips:
    - {fileID: 8300000, guid: 9f8fcf65e840d477798d55d85c5916ab, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: menuBg
    type: 0
    clips:
    - {fileID: 8300000, guid: 7de0920208939429f8b3b15b9ba8a21e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: menuClick
    type: 0
    clips:
    - {fileID: 8300000, guid: 70a5b4f0907bf423ba07ab651d55dbf5, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: menuToMap
    type: 0
    clips:
    - {fileID: 8300000, guid: 545117dbd624e4642bc092c6dba62e65, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: openShopItem
    type: 0
    clips:
    - {fileID: 8300000, guid: 4f1abb68aeea24b9e843bb4536ec954b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: planeBoost1
    type: 0
    clips:
    - {fileID: 8300000, guid: 68fc6eed4d2f24d76bfd8661db207c1d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: planeBoost2
    type: 0
    clips:
    - {fileID: 8300000, guid: 6509a1c21bac4439ba13c661de951780, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: planeBoost3
    type: 0
    clips:
    - {fileID: 8300000, guid: 90ef4705a21d94d0494b503f9194ecc8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: planeBoost4
    type: 0
    clips:
    - {fileID: 8300000, guid: 7243f735ba75b4d068a1195315229855, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: planeBoostLoop
    type: 0
    clips:
    - {fileID: 8300000, guid: d2f18965542fb42ee91f0618a8f0506f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: playerBoostStart
    type: 0
    clips:
    - {fileID: 8300000, guid: 0427d9b2e2e224bad803f42056c3ab61, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: playerBoostSustain
    type: 0
    clips:
    - {fileID: 8300000, guid: 30974a7ce7a744f5b9a83311d2bf9793, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: playerDeath
    type: 0
    clips:
    - {fileID: 8300000, guid: 8806b0290ab1c4daf9cee520040ccc39, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: playerHit
    type: 0
    clips:
    - {fileID: 8300000, guid: 9a77e134a3b5f4c5b913881d7df0ec69, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: powerUpCollected
    type: 0
    clips:
    - {fileID: 8300000, guid: 8b9ece3bcd6104fa1aabd846a66a76c2, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: powerUpCollected2
    type: 0
    clips:
    - {fileID: 8300000, guid: 0bb9f893706ab4761906f69deddeed8b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: rocketeerShoot
    type: 0
    clips:
    - {fileID: 8300000, guid: 282e065c5f19a4fd5a46f3a1cb7344b0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: ScoreCounterEnd
    type: 0
    clips:
    - {fileID: 8300000, guid: 60ca8391b2465497bbe75b93a840fee4, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: selectBoss
    type: 0
    clips:
    - {fileID: 8300000, guid: 99e208349b24d46d198b69fcda60e94d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: sidekickUnlock
    type: 0
    clips:
    - {fileID: 8300000, guid: 056937cd987284548a94ad9d10479104, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Spark
    type: 1
    clips:
    - {fileID: 8300000, guid: 9b82b22e472b4448b97fc06fbe470d2c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Special Click 05
    type: 0
    clips:
    - {fileID: 8300000, guid: a91eb92ecfb904ad795cc44dabf75ca2, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: upcomingWave
    type: 0
    clips:
    - {fileID: 8300000, guid: 7bc103f8d1d6d4b959cfcfcf5fd84807, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: victory
    type: 0
    clips:
    - {fileID: 8300000, guid: ee072601a545241e693da14d15f99f79, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: warMachine copy
    type: 0
    clips:
    - {fileID: 8300000, guid: c738291b1a05042c98f4e7f60eebd9ab, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: warMachine
    type: 0
    clips:
    - {fileID: 8300000, guid: 4fb16617eb32946f0bac905211e1e3c1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: waveCompleted
    type: 0
    clips:
    - {fileID: 8300000, guid: bdea9587a4fd64b27b652e8ec7503984, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: Whoosh 8_1
    type: 0
    clips:
    - {fileID: 8300000, guid: 6ad30df7b0faf4097ac96437f1eaa6ac, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: whoosh1
    type: 0
    clips:
    - {fileID: 8300000, guid: 2b0837613f0224a0fafa6c369865fc24, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: whoosh2
    type: 0
    clips:
    - {fileID: 8300000, guid: febcd45b3c1e944abaf2dc50a3738eaf, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: wings
    type: 0
    clips:
    - {fileID: 8300000, guid: 9b1a38e10d6eb4eb585c2033bb1af7ed, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: popUp
    type: 0
    clips:
    - {fileID: 8300000, guid: 65598f9e38b7143d68cabafce5f1ed30, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: unique-thunderstorm
    type: 0
    clips:
    - {fileID: 8300000, guid: 1ac8ad10072c24eeea83d467c1cc040e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: worldWhoosh
    type: 0
    clips:
    - {fileID: 8300000, guid: 3191b4aec9557493ca4dd4c4ebeb25e0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: splashFast
    type: 0
    clips:
    - {fileID: 8300000, guid: 8ca7811bfc3a749cc9be441de4191fbf, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: splashMedium
    type: 0
    clips:
    - {fileID: 8300000, guid: 73c01d818833d41369b4f98ed15fbc04, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: splashSlow
    type: 0
    clips:
    - {fileID: 8300000, guid: 2764fcfd1bcf64cd09874e3c40e556a8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 11. Sound effect for PistalPaws
    type: 0
    clips:
    - {fileID: 8300000, guid: 929b6dfca96754f0f96d731ecfdde656, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 13. Sound effect for Fixit-Feline
    type: 0
    clips:
    - {fileID: 8300000, guid: 81cd8fb20972043ce9d1300bdb4a2446, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 17. Dailybonus claim button
    type: 0
    clips:
    - {fileID: 8300000, guid: 801c59a2219134ff0bac924cefa3c6bd, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 21. Collecting mission reward sfx
    type: 0
    clips:
    - {fileID: 8300000, guid: a58a15073ca4d4ce99a4c21b2bc738ec, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 23. In-game plane flying(shorter)
    type: 0
    clips:
    - {fileID: 8300000, guid: 1c1f578928ae14dd6b3e03564ee2d387, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 32. Rain
    type: 0
    clips:
    - {fileID: 8300000, guid: caab3fad46d4847a7aaae5f42b6a32ad, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 40533_fanfare-victory-4-mw_by_symphony-of-specters_preview
    type: 0
    clips:
    - {fileID: 8300000, guid: 40d9e42fab9e445adaebb8a72486b6f0, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 7489860_rock-n-roll-victory-2_by_benridgemusic_preview
    type: 0
    clips:
    - {fileID: 8300000, guid: 1f75190f3a3d44dfeb487f2cc726061d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: 7497246_rock-n-roll-victory-logo-3_by_benridgemusic_preview
    type: 0
    clips:
    - {fileID: 8300000, guid: 4438fe9b2713e4cc09c424f2e0ef7592, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: energyTransition
    type: 0
    clips:
    - {fileID: 8300000, guid: 131b61a5227aa448c8d1906eb623894e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: equip
    type: 0
    clips:
    - {fileID: 8300000, guid: d5556c5340cd141a78bf42a8b785257c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: ftuxTransition
    type: 0
    clips:
    - {fileID: 8300000, guid: 612374ae8f6714e688c897954033fd1f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: gameOverExplosionTransition
    type: 0
    clips:
    - {fileID: 8300000, guid: 7cba9a35f7f8e45608b06b5b81aa2cae, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Energy 15 Digital Swoosh
    type: 0
    clips:
    - {fileID: 8300000, guid: ff198725d29ae4809992117e5db90d10, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Energy 23 Blaster
    type: 0
    clips:
    - {fileID: 8300000, guid: 3379a1757968140aba00cdf0e7a024c8, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Energy 28 Pulse
    type: 0
    clips:
    - {fileID: 8300000, guid: 8bc93728bfd454da687e5af9da769d22, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Energy 29 Electricity Charge Hit
    type: 0
    clips:
    - {fileID: 8300000, guid: 2ff3e832517fb4e07b86c7350c7e898a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Explosion 10 Distant Underwater
    type: 0
    clips:
    - {fileID: 8300000, guid: 4578acfeaa34b443288c4cf203f93f2d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Explosion 15 Energy
    type: 0
    clips:
    - {fileID: 8300000, guid: 9cc63503c2cee42a09790f3c8b0b5b75, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Explosion 22 Swoosh
    type: 0
    clips:
    - {fileID: 8300000, guid: f2731982d9dba4ab18932cd0cb681968, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Explosion 25
    type: 0
    clips:
    - {fileID: 8300000, guid: 52f5a149430334dd59bc994758edf0c4, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 12 noCT
    type: 0
    clips:
    - {fileID: 8300000, guid: ce467f6d11ba942f7a72043d0d19a44b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 18-A
    type: 0
    clips:
    - {fileID: 8300000, guid: 0cc6304f3e77947caa305f8ddf3e978a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 20 (Start)
    type: 0
    clips:
    - {fileID: 8300000, guid: c91b15435ef59465d843236a4269f44d, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 29-A
    type: 0
    clips:
    - {fileID: 8300000, guid: f96e1f78135c142dfa3c782212cf0800, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 29-B
    type: 0
    clips:
    - {fileID: 8300000, guid: d81c625dfc0fd4ed1b7cd14c8c7fc461, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 30-A
    type: 0
    clips:
    - {fileID: 8300000, guid: 90f075c89296b49e1b32aa4fefcb3f9b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Logo 30-B
    type: 0
    clips:
    - {fileID: 8300000, guid: e91d09ee725e6484f908664fe71dd3f3, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SFX - Swoosh 06
    type: 0
    clips:
    - {fileID: 8300000, guid: 47f16b267b237471a9ac543f8ff28e49, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: SS - Kar Blaster
    type: 0
    clips:
    - {fileID: 8300000, guid: 2dd1975b94c7d46c7a9ae61b1c21deae, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: victory
    type: 0
    clips:
    - {fileID: 8300000, guid: 992005642ba554bc0ae178a97197b5b6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a1
    type: 0
    clips:
    - {fileID: 8300000, guid: bf782ca7ef80649e99530703511588da, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a2
    type: 0
    clips:
    - {fileID: 8300000, guid: 15684fc8f35e14150a055b7894b113b6, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a3
    type: 0
    clips:
    - {fileID: 8300000, guid: 58a7714fcb9324fb79ad8309ed117539, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a4
    type: 0
    clips:
    - {fileID: 8300000, guid: a2a33315250cd43fe9f9e23b5636ba2f, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a5
    type: 0
    clips:
    - {fileID: 8300000, guid: 0ec22031229364b69b1701a82dceb536, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a6
    type: 0
    clips:
    - {fileID: 8300000, guid: 0e826d6fcf3694b478c19581ce7df142, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a7
    type: 0
    clips:
    - {fileID: 8300000, guid: b843fda6cdbe64efeba8d03d2d217168, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a8
    type: 0
    clips:
    - {fileID: 8300000, guid: bd72b84a0a2c34a85816a2fd5421bcac, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a9
    type: 0
    clips:
    - {fileID: 8300000, guid: 1dfc8c2953c3f485c9d805af6411bc3c, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a10
    type: 0
    clips:
    - {fileID: 8300000, guid: cba7720f981564c76adadffff4c5cca7, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a11
    type: 0
    clips:
    - {fileID: 8300000, guid: fee7410b15f544561b5b4bcb36614dec, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a12
    type: 0
    clips:
    - {fileID: 8300000, guid: e892b06a343ed41c5b9f9adac84041f5, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a13
    type: 0
    clips:
    - {fileID: 8300000, guid: 52a62f7733b5e4c8890bee2b2768b405, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a14
    type: 0
    clips:
    - {fileID: 8300000, guid: de6bc3cee342d4124b321fe8eb5d4a3b, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a15
    type: 0
    clips:
    - {fileID: 8300000, guid: b458fe0ba4490471eac96e611a479491, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a16
    type: 0
    clips:
    - {fileID: 8300000, guid: 1ee1fffc2aedb47e393805a1d824dd7a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a17
    type: 0
    clips:
    - {fileID: 8300000, guid: f8c3b45e72f71436b8c158cb94df650e, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a18
    type: 0
    clips:
    - {fileID: 8300000, guid: 74ab3a493c7e84e5291899cd070deff3, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a19
    type: 0
    clips:
    - {fileID: 8300000, guid: 69e91bfef0f2343f98dc6204a1c8ade1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a20
    type: 0
    clips:
    - {fileID: 8300000, guid: c37809fea52734456a0141fad094ef88, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a21
    type: 0
    clips:
    - {fileID: 8300000, guid: 21e26f7fa1deb427fb3d3568d0d384b1, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  - name: a22
    type: 0
    clips:
    - {fileID: 8300000, guid: 9403e6ea8a25443fd93daf15d916626a, type: 3}
    volume: 0
    pitch: 0
    source: {fileID: 0}
    lastPitchShiftTime: 0
  soundEffects3D:
  - {fileID: 4412203193107924716}
  - {fileID: 6183850376769952482}
  - {fileID: 6016804774713581884}
  - {fileID: 7111340868038204404}
  - {fileID: 3455510683410056845}
  - {fileID: 263035344900176213}
  - {fileID: 3304367942069551406}
  - {fileID: 6348920267133801268}
  - {fileID: 2423286711960392760}
  - {fileID: 5657025764921847066}
  - {fileID: 4864057966630547526}
  - {fileID: 3402233965461255043}
--- !u!82 &6655122781565341831
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781565341830}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6655122781721825521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6655122781721825523}
  - component: {fileID: 6655122781721825522}
  m_Layer: 0
  m_Name: NormalAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6655122781721825523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781721825521}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6655122781721825522
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6655122781721825521}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &7333776538319389920
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5069697974286447727}
  - component: {fileID: 3213008258326438000}
  - component: {fileID: 4412203193107924716}
  m_Layer: 0
  m_Name: SoundEffect3DSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5069697974286447727
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7333776538319389920}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &3213008258326438000
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7333776538319389920}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &4412203193107924716
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7333776538319389920}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 3213008258326438000}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &7591478181350187649
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5463591851617981192}
  - component: {fileID: 4773866371272870216}
  - component: {fileID: 3455510683410056845}
  m_Layer: 0
  m_Name: SoundEffect3DSource (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5463591851617981192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7591478181350187649}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &4773866371272870216
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7591478181350187649}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &3455510683410056845
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7591478181350187649}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 4773866371272870216}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &8896476331990351584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 396749798349851578}
  - component: {fileID: 2734145485155720181}
  - component: {fileID: 3402233965461255043}
  m_Layer: 0
  m_Name: SoundEffect3DSource (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &396749798349851578
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8896476331990351584}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &2734145485155720181
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8896476331990351584}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &3402233965461255043
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8896476331990351584}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 2734145485155720181}
  clip: {fileID: 0}
  isInUse: 0
--- !u!1 &8948500639936780627
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6626360667607635258}
  - component: {fileID: 6185749852474587742}
  - component: {fileID: 6183850376769952482}
  m_Layer: 0
  m_Name: SoundEffect3DSource (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6626360667607635258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8948500639936780627}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6655122781565341825}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6185749852474587742
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8948500639936780627}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 0
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &6183850376769952482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8948500639936780627}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e15793bf846304c92a363c68c1236d9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  source: {fileID: 6185749852474587742}
  clip: {fileID: 0}
  isInUse: 0
