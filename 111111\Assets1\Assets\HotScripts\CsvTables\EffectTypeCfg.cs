﻿using ProtoBuf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

public partial class EffectTypeCfg : Singleton<EffectTypeCfg>
{
    public Dictionary<int, PropNames> dic = new()
    {
    };

    protected override void InitializeSingleton()
    {
        DontDestroyOnLoad(this);
        // 最大生命
        dic.Add(0, new()
        {
            EffectTypeID = 0,
            Name = nameof(Attributes.maxHealth),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.Hp)
        });
        // 基础攻击百分比
        dic.Add(1, new()
        {
            EffectTypeID = 1,
            Name = nameof(Attributes.addAttack),
            //ActorProp = nameof(PlayerBaseProp.Item.),
            EquipSmeltProp = nameof(EquipSmelt.Item.Attack),
            PctForName = nameof(Attributes.attack)
        });
        // 生命恢复量百分比
        dic.Add(2, new()
        {
            EffectTypeID = 2,
            Name = nameof(Attributes.regen),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.Defense)
        });
        // 攻击
        dic.Add(3, new()
        {
            EffectTypeID = 3,
            Name = nameof(Attributes.attack),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.PhysicsAttack)
        });
        // 伤害半径百分比
        dic.Add(4, new()
        {
            EffectTypeID = 4,
            Name = nameof(Attributes.damageReduction),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.MagicAttack)
        });
        // 狂暴时长百分比
        dic.Add(4, new()
        {
            EffectTypeID = 5,
            Name = nameof(Attributes.rageModeTime),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.PhysicsDefense)
        });
        // 能量上限百分比
        dic.Add(4, new()
        {
            EffectTypeID = 6,
            Name = nameof(Attributes.maxEnergy),
            ActorProp = nameof(PlayerBaseProp.Item.Hp),
            EquipSmeltProp = nameof(EquipSmelt.Item.MagicDefense)
        });

        base.InitializeSingleton();
    }
}

public partial class PropNames
{
    public int EffectTypeID { get; set; }
    public string Name { get; set; }
    public string ActorProp { get; set; }
    public string EquipSmeltProp { get; set; }
    public string PctForName { get; set; }
    public bool PctForFinal { get; set; }
}