﻿using System.Threading;

namespace Buffs
{
    /// <summary>
    /// 属性提升类Buff效果
    /// </summary>
    public class BuffPropAddition : BuffEffectBase
    {
        /// <summary>
        /// 对应的提升
        /// </summary>
        public FightHoist Hoist { get; protected set; }

        protected override void DoWork_Do(CancellationToken cancel)
        {
            if (Hoist == null)
            {
                Hoist = new FightHoist
                {
                    HoistType = Globals.MapToHoistType_BuffEffectParam1(CsvRow_BuffEffect.Param1),
                    IsPct = CsvRow_BuffEffect.Param2 == 2,
                };

                if (Hoist.IsPct)
                {
                    Hoist.HoistValue = Globals.UnityValueTransform(CsvRow_BuffEffect.Param3);
                }
                else
                {
                    Hoist.HoistValue = CsvRow_BuffEffect.Param3;
                }

                if (Hoist.HoistType != HoistType.无)
                {
                    Buff.BuffScheduler.Creature.FightProp.Hoists.Add(Hoist);
                }
            }
        }

        protected override void DoKill_Do()
        {
            if (Hoist != null && Hoist.HoistType != HoistType.无)
            {
                // 移除对属性的提升效果
                Buff.BuffScheduler.Creature.FightProp.Hoists.Remove(Hoist);
            }
        }
    }
}