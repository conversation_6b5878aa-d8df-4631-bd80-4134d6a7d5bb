using System;
using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using DG.Tweening;
using UnityEngine;
using TMPro;

public class BossFactory : MonoBehaviour
{
    string tweenID, schedulerID;
    bool showDialogue = true;

    public static BossFactory Create()
    {
        BossFactory bFactory = new GameObject().AddComponent<BossFactory>();
        bFactory.tweenID = "BFTween";
        bFactory.schedulerID = "BFScheduler";
        bFactory.SpawnBoss(); 

        return bFactory;
    }

    IEnumerator ScheduleFunction(float delay, Action callBack)
    {
        yield return new WaitForSeconds(delay);

        callBack?.Invoke();
    }
    [IFix.Patch]
    void SpawnBoss()
    {
        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSpiky || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossSpiky)
        {
            var seversky = GameManager.instance.SpawnEnemy("SPIKY") as Spikey;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // seversky.monsterData = monsterData;
            // seversky.prizeID = monsterData.PrizeID;
            Globals.zoomToBossForSec = 0.35f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
            Globals.bossShouldStayOnScreen = true;
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.3f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/enemy3"] as string, seversky, 0.5f, Vector2.zero, 0.6f);

            if (Globals.AllowBgMusic)
            {
                //    Shared::stopSound(gameplayMusicId);
                //    gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);
            }
            seversky.SetEnemyDifficulty(0.5f, 2.0f);

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = seversky;
                bossHud.SetSkin("Spiky");
            }
        }
        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSugarPuff || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossSugarPuff)
        {
            Globals.UPPERBOUNDARY = 4.687f;

            var bomber = GameManager.instance.SpawnEnemy("SUGARPUFF") as SugarPuff;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // bomber.monsterData = monsterData;
            // bomber.prizeID = monsterData.PrizeID;
            Globals.zoomToBossForSec = 0.65f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(600));
            Globals.maxCameraZoom = Globals.CocosToUnity(1300);
            // Globals.bossShouldStayOnScreen = true;

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/bomber"] as string, bomber, 0.65f, Vector2.zero, 0.65f);

            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/SugarPuff.mp3", true, 0.75f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.SugarPuff, false, 0.75f);
                AudioManager.instance.PlayMusic(7013);
            }

            bomber.SetEnemyDifficulty();

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = bomber;
                bossHud.SetSkin("SugarPuff");
            }
        }
        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossCutin || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossCutin)
        {
            var enemy = GameManager.instance.SpawnEnemy("VLADMIRCUTIN") as Patches;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.cutinIntro);

            //Shared::playSound("res/Sounds/Bosses/Boss3/cutinIntro.mp3");

            Globals.zoomToBossForSec = 0.45f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(450);
            Globals.bossShouldStayOnScreen = true;

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/ship2"] as string,
                enemy, 0.8f, new Vector2(0, Globals.CocosToUnity(100)), 0.8f, 0.65f);



            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/Cutin.mp3", true, 0.75f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.Cutin, false, 0.75f);
                AudioManager.instance.PlayMusic(7006);

            }
            enemy.SetEnemyDifficulty();

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = enemy;
                bossHud.SetSkin("Cutin");
            }
        }

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossBarkMeow || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossBarkMeow)
        {

            var enemy = GameManager.instance.SpawnEnemy("BARKMEOW",
                new Vector2(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1000), Globals.LOWERBOUNDARY + Globals.CocosToUnity(200))) as BarkMeow;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;

            //Globals.zoomToBossForSec = 0.35f;
            //Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
            //Globals.SetZoomValueWhileGame(Globals.CocosToUnity(50));


            Globals.zoomToBossForSec = 0.5f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
            Globals.maxCameraZoom = Globals.CocosToUnity(1200);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(200));
            Globals.bossShouldStayOnScreen = true;

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/vaderboss"] as string, enemy, 0.65f, Vector2.zero, 1.0f, 0.85f);


            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/BarkMeow.mp3", true, 0.75f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.BarkMeow, false, 0.75f);
                AudioManager.instance.PlayMusic(7001);

            }

            enemy.SetEnemyDifficulty();

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = enemy;
                bossHud.SetSkin("BarkMeow");
            }
        }

        else if (GameData.instance.fileHandler.currentEvent ==(int)EventBoss.kBossTomasScratcher || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossTomasScratcher)
        {
            var enemy = GameManager.instance.SpawnEnemy("TOMAS",
                new Vector2(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), Globals.CocosToUnity(200))) as TomasScratcher;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;
            //TomasScratcher* enemy = TomasScratcher::create();
            //this->addChild(enemy);

            //zoomToBossForSec = 2.35f;
            //slowDownForBoss = false;
            //zoomValueOnBoss = 350;
            //zoomValueWhileGame = 800;

            Globals.zoomToBossForSec = 1f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(350);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(850));
            Globals.bossShouldStayOnScreen = true;
            Globals.LEFTBOUNDARY *= 10;
            Globals.RIGHTBOUNDARY *= 10;

            //enemy->runAction(Repeat::create((ActionInterval*)CallFunc::create([](){ zoomValueOnBoss += 0.5f; }),100));
            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);

            }
            enemy.SetEnemyDifficulty();


            //this->runAction(Sequence::create(DelayTime::create(1.45f), CallFunc::create([this, enemy](){
            //    createBossEntry(GameData::getInstance()->getTextData(GAME_TEXT::DIALOGUES).at("Keys").asValueMap().at("res/Enemy/mafiaBoss").asString(), enemy, 1.25f, cocos2d::Point::ZERO, 1);
            //}), NULL));

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.65f);

            DOTween.Sequence().SetId(tweenID).AppendInterval(1.5f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() =>
            {
                CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/mafiaBoss"] as string,
                enemy, 0.65f, Vector2.zero, 1.0f);
            });

            //BossHud* bossHud = BossHud::createWithEnemy(enemy);
            //this->addChild(bossHud);
            //bossHud->setSkin("Tomas");

        }

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSpiderCat || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossSpiderCat)
        {
            var enemy = GameManager.instance.SpawnEnemy("SPIDERCAT") as SpiderCat;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;
            Globals.zoomToBossForSec = 0.5f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(1200);

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);

            DOTween.Sequence().AppendInterval(0.05f).AppendCallback(() =>
            {
                CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/tripodBoss"] as string,
                enemy, 2, new Vector2(0, -Globals.CocosToUnity(300)), 2f);
            });

            DOTween.Sequence().SetId(tweenID).AppendInterval(1f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(900)); // TODO Change 800 to 1300 
            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);

            }
            enemy.SetEnemyDifficulty();

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = enemy;
                bossHud.SetSkin("SpiderCat");
            }
        }

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossGreek || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossGreek)
        {
            var enemy = GameManager.instance.SpawnEnemy("CUTEUSMAXIMUS", new Vector2(7, Globals.LOWERBOUNDARY - 0.6f)) as CuteusShip;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;
            //Globals.zoomToBossForSec = 0.4f;
            //Globals.zoomValueOnBoss = Globals.CocosToUnity(800);
            //Globals.zoomValueWhileGame = Globals.CocosToUnity(100);
            Globals.zoomToBossForSec = 0.5f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1000));
            Globals.bossShouldStayOnScreen = true;

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/captain"] as string,
                enemy, 1.6f, new Vector2(0, 2), 1.3f, 0.8f);


            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //Globals.gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);

            }
            enemy.SetEnemyDifficulty();

            var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

            if (bossHud)
            {
                bossHud._enemy = enemy;
                bossHud.SetSkin("Maximus");
            }
        }

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSentinel || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossSentinel)
        {
            var enemy = GameManager.instance.SpawnEnemy("SENTINEL") as Sentinel;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;
            //TODO
            Globals.zoomToBossForSec = 0.5f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(700);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(300));
            Globals.maxCameraZoom = Globals.CocosToUnity(1000);

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/sentinel"] as string,
                enemy, 1.3f, new Vector2(0, 0.8f), 1.2f);
            Globals.bossShouldStayOnScreen = true;

            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);
            }

            enemy.SetEnemyDifficulty();

            //BossHud* bossHud = BossHud::createWithEnemy(enemy);
            //this->addChild(bossHud);
            //bossHud->setSkin("Sentinel");

        }


        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossPurgana || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossPurgana)
        {
            //var enemy = GameManager.instance.SpawnEnemy("PURGANA", new Vector2(7, Globals.LOWERBOUNDARY)) as Dr;

            DragonManager dragon = GameManager.instance.InstantiatePrefab("DRAGONMANAGER").GetComponent<DragonManager>();
            dragon.Init();
            Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(600));
            Globals.maxCameraZoom = Globals.CocosToUnity(1200);
            Globals.zoomToBossForSec = 1.1f;
            GameManager.instance.timeManager.SetTimescale(0.1f);
            //DragonManager* node = DragonManager::create();
            //this->addChild(node);

            //zoomToBossForSec = 1.1f;
            //zoomValueOnBoss = 250;
            //zoomValueWhileGame = 1500;

            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);
            }
        }

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossTinyBots)
        {
            BotController botController = GameManager.instance.InstantiatePrefab("BOTCONTROLLER").GetComponent<BotController>();

            Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(800));
            Globals.zoomToBossForSec = 0.35f;

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.35f);
            if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots )
            {
            
                DialoguePopup.CreateAsPreBattle();
            }

            DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            //SkeletonAnimation* entry = SkeletonAnimation::createWithJsonFile("res/Enemy/bossEntry.json", "res/Enemy/bossEntry.atlas");
            //botController->getPortal()->enemySprite->addChild(entry, -1);
            //botController->getPortal()->enemySprite->setTimeScale(3);

            //entry->setAnimation(0, "bossEntry", false);
            //entry->setTimeScale(6);
            //entry->setVisible(false);
            //entry->runAction(Sequence::create(Show::create(), DelayTime::create(0.5f),
            //                                  CallFunc::create([=](){
            //    botController->getPortal()->enemySprite->setTimeScale(1);
            //    LEFTBOUNDARY = botController->getPortal()->enemySprite->getPosition().x - 2000;
            //    RIGHTBOUNDARY = botController->getPortal()->enemySprite->getPosition().x + 2000;
            //    Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("Spawn_Starting_Dialogues");


            //})
            //                              ,  DelayTime::create(1.0f),  ScaleTo::create(0.1f, 0),RemoveSelf::create(), CallFunc::create([]{

            //}), NULL));
            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
                AudioManager.instance.PlayMusic(7000);
            }
        }
        //else if (FileHandler::getInstance()->currentEvent == kBossMeowthena)
        //{
        //    Meowthena* enemy = Meowthena::create();
        //    this->addChild(enemy);


        //}

        else if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossOkto || GameData.instance.fileHandler.currentEvent == (int)EventBoss.SpecialkBossOkto)
        {
            var enemy = GameManager.instance.SpawnEnemy("OKTO") as OktoPuss;
            // MonsterNew.Item monsterData = MonsterNewScheme.Instance.GetItem(GameData.instance.fileHandler.currentBossMonsterID);
            // enemy.monsterData = monsterData;
            // enemy.prizeID = monsterData.PrizeID;

            //Okto* enemy = Okto::create();
            //this->addChild(enemy);
            //TODO
            //zoomToBossForSec = 0.4;
            //zoomValueOnBoss = 900;
            //zoomValueWhileGame = 500;

            Globals.zoomToBossForSec = 0.6f;
            Globals.zoomValueOnBoss = Globals.CocosToUnity(800);
            Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
            Globals.bossShouldStayOnScreen = true;
            enemy.SetEnemyDifficulty();

            GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.6f);
            DOTween.Sequence().SetId(tweenID).AppendInterval(0.8f).AppendCallback(() =>
            {
                Observer.DispatchCustomEvent("SpawnStartingDialogues");
            });

            CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/Okto-puss"] as string,
                enemy, 1, new Vector2(0, 2.5f), 2f);
            if (Globals.AllowBgMusic)
            {
                //Shared::stopSound(gameplayMusicId);
                //gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.15f);
                // AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.15f);
                AudioManager.instance.PlayMusic(7000);
            }
            //BossHud* bossHud = BossHud::createWithEnemy(enemy);
            //this->addChild(bossHud);
            //bossHud->setSkin("Okto-Puss");

        }
        //else if (FileHandler::getInstance()->currentEvent == (int)EventBoss.kBossOkto)
        //{


        //    Okto* enemy = Okto::create();
        //    this->addChild(enemy);
        //    //
        //    zoomToBossForSec = 0.4;
        //    zoomValueOnBoss = 900;
        //    zoomValueWhileGame = 500;

        //    createBossEntry(GameData::getInstance()->getTextData(GAME_TEXT::DIALOGUES).at("Keys").asValueMap().at("res/Enemy/Okto-puss").asString(), enemy, 3, cocos2d::Point(0, 150), 2);

        //    if (AllowBgMusic)
        //    {
        //        Shared::stopSound(gameplayMusicId);
        //        gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.15f);
        //    }

        //    enemy->setEnemyDifficulty();
        //    BossHud* bossHud = BossHud::createWithEnemy(enemy);
        //    this->addChild(bossHud);
        //    bossHud->setSkin("Okto-Puss");

        //}


        //zoomValueWhileGame = GameData::getInstance()->getBoss(FileHandler::getInstance()->currentEvent).at("CameraDistance").asFloat();
    }


    //void NewSpawnBoss(HudLayer* _hudLayer, int bossNumber, bool createHud)
    //{
    //    if (bossNumber == kBossSpiderCat)
    //    {

    //        showDialogue = false;
    //        Tripod* enemy = Tripod::create();
    //        this->addChild(enemy);

    //        zoomToBossForSec = 0.30f;
    //        zoomValueOnBoss = 1100;

    //        createBossEntry(GameData::getInstance()->getTextData(GAME_TEXT::DIALOGUES).at("Keys").asValueMap().at("res/Enemy/tripodBoss").asString(), enemy, 6, cocos2d::Point(0, -300), 3);

    //        zoomValueWhileGame = 1300;

    //        if (AllowBgMusic)
    //        {
    //            Shared::stopSound(gameplayMusicId);
    //            gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));

    //        }
    //        enemy->setEnemyDifficulty();


    //        if (createHud)
    //        {
    //            BossHud* bossHud = BossHud::createWithEnemy(enemy);
    //            this->addChild(bossHud);
    //            bossHud->setSkin("SpiderCat");
    //        }



    //    }
    //    if (bossNumber == kBossTomasScratcher)
    //    {
    //        showDialogue = false;
    //        TomasScratcher* enemy = TomasScratcher::create();
    //        this->addChild(enemy);

    //        bossShouldStayOnScreen = true;

    //        zoomToBossForSec = 2.35f;
    //        slowDownForBoss = false;
    //        zoomValueOnBoss = 350;
    //        zoomValueWhileGame = 800;

    //        enemy->runAction(Repeat::create((ActionInterval*)CallFunc::create([](){ zoomValueOnBoss += 0.5f; }),100));
    //        if (AllowBgMusic)
    //        {
    //            Shared::stopSound(gameplayMusicId);
    //            gameplayMusicId = experimental::AudioEngine::play2d("res/Sounds/BGM/arenaMusic.mp3", true, 0.25f * UserDefault::getInstance()->getFloatForKey("MUSIC_VOLUME", 1.0f));

    //        }
    //        enemy->setEnemyDifficulty();


    //        this->runAction(Sequence::create(DelayTime::create(1.45f), CallFunc::create([this, enemy](){
    //            createBossEntry(GameData::getInstance()->getTextData(GAME_TEXT::DIALOGUES).at("Keys").asValueMap().at("res/Enemy/mafiaBoss").asString(), enemy, 1.25f, cocos2d::Point::ZERO, 1);
    //        }), NULL));



    //        BossHud* bossHud = BossHud::createWithEnemy(enemy);
    //        this->addChild(bossHud);
    //        bossHud->setSkin("Tomas");


    //    }
    //}


    void CreateBossEntry(string str, Enemy enemy, float scale, Vector2 positionOffset, float fontMultiplier = 1,
        float disappearDelayMultiplier = 1)
    {
        return;
        // TODO
        var entry = GameManager.instance.InstantiatePrefab("BossEntry").GetComponent<SkeletonAnimation>();
        //entry->setVisible(false);
        var entryTransform = entry.transform;
        var enemyTransform = enemy.transform;
        entryTransform.localScale = new Vector3(scale, scale, 1);
        //entryTransform.localScale = new Vector3(1 / enemyTransform.localScale.y * 0.4f * scale,
        //    1 / enemyTransform.localScale.y * 0.4f * scale, 1);
        entryTransform.parent = enemyTransform;
        entryTransform.localPosition = positionOffset;
        //entryTransform.position = new Vector3(Globals.CocosToUnity(positionOffset.x),
        //    Globals.CocosToUnity(positionOffset.y), entryTransform.position.z);
        entry.state.SetAnimation(0, "bossEntry", false);
        entry.timeScale = 6;

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossGreek)
        {
            //entry->setGlobalZOrder(-5);
        }

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossOkto)
        {
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));
        }
        // TODO ASK
        //entry->runAction(Sequence::create(Show::create(), DelayTime::create(0.5f), ScaleTo::create(0.1f, 0), RemoveSelf::create(), NULL));
        StartCoroutine(ScheduleFunction(0.5f * disappearDelayMultiplier, () =>
        {
            StartCoroutine(nameof(EntryDisappear), entryTransform);
        }));

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSugarPuff)
        {
            StopCoroutine(nameof(EntryDisappear));
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));
            SkeletonAnimation heart = GameManager.instance.InstantiatePrefab("HeartExplosion").GetComponent<SkeletonAnimation>();
            heart.GetComponent<MeshRenderer>().sortingLayerName = "Foreground";
            heart.GetComponent<MeshRenderer>().sortingOrder = 50;
            heart.timeScale = 2.5f;
            heart.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            heart.transform.parent = enemyTransform;;
            heart.transform.localPosition = Vector3.zero;
            heart.state.SetAnimation(0, "entry", false);
            //heart.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            //heart->runAction(Sequence::create(Show::create(), DelayTime::create(0.15f), RemoveSelf::create(), NULL));
            StartCoroutine(ScheduleFunction(0.25f, () =>
            {
                Destroy(heart.gameObject);
            }));

            var heart2 = GameManager.instance.InstantiatePrefab("HeartExplosion").GetComponent<SkeletonAnimation>();
            heart2.timeScale = 2.5f;
            heart2.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            heart2.transform.parent = enemyTransform;
            heart2.transform.localPosition = Vector3.zero;
            heart2.state.SetAnimation(0, "entry", false);
            //heart2.transform.localScale = new Vector3(0.35f, 0.35f, 1);

            StartCoroutine(ScheduleFunction(0.5f, () =>
            {
                Destroy(heart2.gameObject);
            }));
        }

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossCutin)
        {
            StopCoroutine(nameof(EntryDisappear));
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));

            var waterExplosion = GameManager.instance.InstantiatePrefab("WaterEntry").GetComponent<SkeletonAnimation>();
            waterExplosion.GetComponent<MeshRenderer>().sortingLayerName = "Foreground";
            waterExplosion.GetComponent<MeshRenderer>().sortingOrder = -1;
            waterExplosion.timeScale = 2f;
            waterExplosion.transform.localScale = new Vector3(1.4f, 0.75f, 1);
            waterExplosion.transform.parent = enemyTransform;
            waterExplosion.transform.localPosition = new Vector2(2.99f, -1.16f);
            waterExplosion.state.SetAnimation(0, "entry", false);
            StartCoroutine(ScheduleFunction(0.35f, () =>
            {
                Destroy(waterExplosion.gameObject);
            }));
        }

        var label = GameManager.instance.InstantiatePrefab("BossEntryLabel").GetComponent<TextMeshPro>();
        label.text = str;
        label.transform.parent = entryTransform;
        label.transform.localPosition = new Vector3(0, -1.11f, 0);
        label.transform.SetScale(0);
        DOTween.Sequence().AppendInterval(0.2f)
            .Append(label.transform.DOScale(1 / entryTransform.localScale.x * fontMultiplier, 0.03f));

        if (showDialogue)
        {
            if (GameData.instance.fileHandler.currentMission != 0)
            {
                DialoguePopup.CreateAsPreBattle();
            }
        }
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            Observer.DispatchCustomEvent("Spawn_SpiderCat_FTUE_Dialogues");
        }

        //#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)

        //            if (!isJoystickConnected)
        //            {
        //                Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("Activate_Mobile_Assist_Controls");
        //            }
        //#endif
        //            enemy->takeDamage = true;

        //        }), NULL));

    }

    IEnumerator EntryDisappear(Transform entryTransform)
    {
        float duration = 0.1f;
        float scale = entryTransform.localScale.x, dt;

        while(scale > 0)
        {
            dt = Time.deltaTime < duration ? Time.deltaTime : duration;
            scale -= scale * (dt / duration);
            duration -= dt;

            entryTransform.localScale = new Vector3(scale, scale, 1);

            yield return null;
        }
        //TODO：这个地方会报错
        //Destroy(entryTransform.gameObject);
    }

    public void SpawnFtueSpiderCat()
    {
        var enemy = GameManager.instance.SpawnEnemy("SPIDERCAT", new Vector2(7, 6)) as SpiderCat;

        Globals.zoomToBossForSec = 0.30f;
        Globals.zoomValueOnBoss = Globals.CocosToUnity(800);

        GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.3f);
        DOTween.Sequence().SetId(tweenID).AppendInterval(0.7f).AppendCallback(() =>
        {
            Observer.DispatchCustomEvent("SpawnStartingDialogues");
        });

        CreateBossEntry(
            (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/tripodBoss"] as string,
            enemy, 6, new Vector2(0, -Globals.CocosToUnity(300)), 3f);

        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1300)); // TODO Change 800 to 1300 
        if (Globals.AllowBgMusic)
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {
                AudioManager.instance.PlayMusic(Track.arenaMusic, false, 0.25f);
            }else{

                AudioManager.instance.PlayMusic(7000);
            }
        }
        enemy.SetEnemyDifficulty();

        var bossHud = GameManager.instance.InstantiatePrefab("BossHud").GetComponent<BossHud>();

        if (bossHud)
        {
            bossHud._enemy = enemy;
            bossHud.SetSkin("SpiderCat");
        }


    }
}
