using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine.Unity;
using Spine;
public class LaserBase : Enemy
{
    [SerializeField] private List<Soldier> soldierArray;
    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite mineSprite;
    [SerializeField] private LaserTower laserTower;
    [SerializeField] private Transform laserTowerPos;
    [SerializeField] private Turret[] turret;
    [SerializeField] private SkeletonAnimation[] flameAnim;

    private bool isOnFire = false;
    private Bone cannon;


    public override bool CheckCollision(Vector2 P1)
    {
        return false;
    }

    public override void Destroy()
    {
        if (GameManager.instance.missionManager.missionType== Globals.MissionTypeEnemyLaserBase && Globals.gameType == GameType.Training)
        {
            GameManager.instance.missionManager.MissionComplete();
            GameManager.instance.missionManager.Location = Vector2.zero;
        }
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        Globals.numberOfEnemies--;
        base.Destroy();
        //GameSharedData::getInstance().g_enemyArray.eraseObject(this);
        //this.removeAllChildrenWithCleanup(true);
        //this.removeFromParentAndCleanup(true);

    }

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "LaserBase" + GetInstanceID().ToString();
        tweenId = "LaserBaseS" + GetInstanceID().ToString();
        allowRelocate = false;
        //this.unschedule(schedule_selector(EnemyLaserBase::reclocate));//TODO Fix this shit
        transform.position = new Vector3(40, -2.6f);
        InitStats();
        Globals.numberOfEnemies++;
        healthBar.gameObject.SetActive(false);
        healthBar.ScaleRatio=0;
        explosionType = Explosions.ExplosionType.ExplosionTypeBuilding;
        InitSoldiers();
        SpawnTowers();
        SpawnMines();
        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.3f).AppendCallback(UpdateBounds).SetLoops(-1).Play();
    }

    private void Update()
    {

    }

    private void UpdateBounds()
    {

        if (!isOnFire && Globals.isMissionComplete)
        {
            //const float firePositionY = 0.0f;
            isOnFire = true;
            for (int i = 0; i < flameAnim.Length; i++)
            {
                flameAnim[i].gameObject.SetActive(true);
                flameAnim[i].state.SetAnimation(0, "idle", true);
                flameAnim[i].state.TimeScale = (0.8f + Random.value * 0.4f);
            }
            //{
            //    SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
            //    enemySprite.addChild(node, -1);
            //    node.setPosition(-800, firePositionY);
            //    flameAnim[0].transform.SetScale(2);
            //    node.setCameraMask(GAMECAMERA);
            //}
            //{
            //    SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
            //    enemySprite.addChild(node, -1);
            //    node.setPosition(800, firePositionY);
            //    node.setScale(2);
            //    node.setCameraMask(GAMECAMERA);
            //    node.setAnimation(0, "idle", true);
            //    node.setTimeScale(0.8 + rand_0_1() * 0.4);

            //}

            //{
            //    SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
            //    enemySprite.addChild(node, -1);
            //    node.setPosition(0, firePositionY);
            //    node.setScale(2);
            //    node.setCameraMask(GAMECAMERA);
            //    node.setAnimation(0, "idle", true);
            //    node.setTimeScale(0.8 + rand_0_1() * 0.4);

            //}

            for (int i=0; i<soldierArray.Count;i++)
            {
                soldierArray[i].Run();
            }
        }
        // spSkeletonBounds_update(_bounds, enemySprite.getSkeleton(), true);

    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        stats.speed = baseStats.speed = 7 + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;
        stats.health = baseStats.health = GameData.instance.fileHandler.TrainingLevel * 700;
        stats.bulletDamage = baseStats.bulletDamage = GameData.instance.fileHandler.TrainingLevel;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;

        stats.coinAwarded = baseStats.coinAwarded = 15;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;

    }

    private void InitSoldiers()
    {
        for (int i = 0; i < soldierArray.Count; i++)
        {
            soldierArray[i].gameObject.SetActive(true);
            soldierArray[i].Init();
        }
    }

    private void SpawnMines()
    {
        for (int i = 0; i < 16; i++)
        {
            Mine mine = null;
            bool didFindMine = false;
            foreach (Mine m in GameSharedData.Instance.enemyMinePool)
            {
                if (!m.isInUse)
                {
                    mine = m;
                    mine.isInUse = true;
                    didFindMine = true;
                    break;
                }

            }
            if (!didFindMine)
            {
                return;
            }
            mine.Init();
            mine.transform.position = new Vector2(transform.position.x - Globals.CocosToUnity(2500) + (i * 3), Globals.LOWERBOUNDARY + Random.value * 0.3f);
            //mine.missileSprite.setPosition(enemySprite.getPosition().x - 2500 + (i * 350), LOWERBOUNDARY + rand_0_1() * 30);
            DOTween.Kill(mine.tweenId);
            mine.missileSprite.sprite = mineSprite;
            mine.duration = 500;
            mine.SetDamage(1200);
            mine.RemoveAfterDuration();
            GameSharedData.Instance.enemyMissilesInUse.Add(mine);
        }
    }

    private void SpawnTowers()
    {
        laserTower.gameObject.SetActive(true);
        laserTower.player = player;
        laserTower.Init();
        laserTower.stats.bulletDamage = 5;
        laserTower.transform.localPosition = Vector3.zero;
        print(laserTower.transform.position);
        {
            //turret[0].transform.SetLocalPositionX(laserTower.generator.transform.position.x - Globals.CocosToUnity(600));
            turret[0].Init();
            turret[0].stats.bulletDamage = turret[0].stats.bulletDamage * 1.5f;
            turret[0].isDestructable = false;
        }

        {
            //Turret* turret = Turret::create();
            //this.addChild(turret);
            //turret[0].enemySprite.setPositionX(node.generator.enemySprite.getPosition().x + 600);
            turret[1].Init();
            turret[1].stats.bulletDamage = turret[1].stats.bulletDamage * 1.5f;
            turret[1].isDestructable = false;


        }


        {
            //Turret* turret = Turret::create();
            //this.addChild(turret);
            //turret[2].enemySprite.setPositionX(enemySprite.getPosition().x - 1200);
            turret[2].Init();
            turret[2].isDestructable = false;

        }

        {
            //Turret* turret = Turret::create();
            //this.addChild(turret);
            //turret[3].enemySprite.setPositionX(enemySprite.getPosition().x + 1200);
            turret[3].Init();
            turret[3].isDestructable = false;

        }

    }


}
