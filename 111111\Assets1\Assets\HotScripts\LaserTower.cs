using UnityEngine;
using Spine.Unity;
using System.Collections;
using System;
using Spine;
using DG.Tweening;
public class LaserTower : Enemy
{
    public enum LaserTowerType
    {
        Shielded,
        Unshielded
    }
    [SerializeField] private BoundingBoxFollower colliderShield;
    [SerializeField] private BoundingBoxFollower colliderShieldOff;
    [SerializeField] private GameObject flames;

    [HideInInspector] public LaserTowerType _currentState;
    [HideInInspector] public bool onFire;
    public static Action DestroyTowerOnSkipTutorial;
    public Generator generator;
    //[HideInInspector] public PlayerPing missionPing;

    private BoundingBoxFollower bounds;
    private PlayerPing missionPing;
    
    public override void Init()
    {
        if (initialized)
            return;
        tweenId = "LT" + GetInstanceID();
        schedulerId = "LTS" + GetInstanceID();
        if (GameData.instance.fileHandler.currentMission==15)
        {
            isLaserBase = true;
        }
        base.Init();
        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
        _currentState = LaserTowerType.Shielded;

        if (GameData.instance.fileHandler.currentMission==0)
        {
            Globals.SetZoomValueWhileGame(Globals.zoomValueWhileGame - Globals.CocosToUnity(300));
            isTutorialTower = true;
            _allowKillPoint = false;
            _currentState = LaserTowerType.Unshielded;


            DOTween.Sequence().SetId(tweenId).AppendInterval(1).AppendCallback(() => {
                missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                missionPing.Init(transform, true);

                missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                missionPing.Init(transform, false);
            }).Play();
        }

        Globals.numberOfEnemies++;
        CancelRelocate();
        isBoss = false;
        InitStats();
        enemySprite.state.SetAnimation(0, "idleShield", true);
        transform.position = new Vector2(Globals.CocosToUnity(3000) +  Globals.CocosToUnity(UnityEngine.Random.value * 1000), Globals.LOWERBOUNDARY );
        if(!Globals.isTutorial)
            healthBar.gameObject.SetActive(false);
        if (GameData.instance.fileHandler.currentMission==0)
        {
            transform.position = new Vector2(Globals.CocosToUnity(3000), Globals.LOWERBOUNDARY /*Globals.CocosToUnity(-30)*/);

        }





        // add this in an avent

        //            EventListenerCustom *event = EventListenerCustom::create("show_mission_ping", [=] (EventCustom* c){ TODO maybe was not being used in Cocos


        //            this.setAllowPing(true, Enemy::PINGTYPE::MISSION_AHEAD);


        //});
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(event, this);
        Observer.RegisterCustomEvent(gameObject, "Destroy_Tower_On_Skip_Tutorial",() => { Destroy(); });



        explosionType = Explosions.ExplosionType.ExplosionTypeBuildingMission;
        _jsonScale = 2.5f;
        if (GameData.instance.fileHandler.currentMission != 0)
        {
            generator.gameObject.SetActive(true);
            generator.Init();
        }
        else
        {
            _currentState = LaserTowerType.Unshielded;
        }




        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeStages) 
        {
            generator.transform.SetWorldPositionX(Globals.CocosToUnity(800000));
            DOTween.Sequence().SetId(generator.schedulerId).AppendInterval(1.5f).AppendCallback(() => {
                generator.transform.SetWorldPositionX(transform.position.x + Globals.CocosToUnity(600));
            }).Play();
        }
        scheduleUpdate = true;
        enemySprite.state.Event += HandleSpineEvent;


        additionalOnDestroy += () =>
        {
            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeLaserTower && Globals.gameType == GameType.Training)
            {

                if (GameData.instance.fileHandler.currentMission!= 0)
                {
                    GameManager.instance.missionManager.MissionComplete() ;
                    GameManager.instance.missionManager.Location =Vector2.zero;
                }
                else
                {
                    //                if(!isJoystickConnected)
                    {
                        GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Aim_Assist_Rotation);
                        GameManager.instance.controlsTutorialManager.isLaserTowerDestroyed = true;

                    }


                }


            }

            if (GameManager.instance.missionManager.missionType == Globals.MissionTypeEnemyLaserBase && Globals.gameType == GameType.Training)
            {
                GameManager.instance.missionManager.MissionComplete();
                GameManager.instance.missionManager.Location = Vector2.zero;
            }
        };

        colliderShield.gameObject.SetActive(true);
        bounds = colliderShield;
        if (_currentState == LaserTowerType.Shielded)
        {
            bounds = colliderShield;
        }
        else
        {
            bounds = colliderShieldOff;
        }
    }

    private IEnumerator UpdateBounds()
    {
        while (true)
        {
            
            yield return new WaitForSeconds(1f);
        }
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        //    if (Player::getStats().mode != Player::PLAYER_MODE_DEATHDROP)
        //    {
        //        if (strcmp(event.Data.Name, "") == 0){

        //}
        if (spineEvent.Data.Name == "shoot")
        {
            //this.schedule(schedule_selector(LaserTower::shoot));

        }
        if (spineEvent.Data.Name == "shootOff")
        {
            //this.unschedule(schedule_selector(LaserTower::shoot));
        }
    }

    public void Shoot()
    {
        if (player.transform.position.x < transform.position.x + Globals.CocosToUnity(450)
            && player.transform.position.x > transform.position.x - Globals.CocosToUnity(450) && player.canHit)
        {
            player.GotHit(stats.bulletDamage, false);
        }
    }

    public override bool TakeHit(double damage)
    {
        if (_currentState == LaserTowerType.Unshielded)
        {
            return base.TakeHit(damage);
        }
        else
        {
            return false;
        }
    }

    private void Update()
    {
        if (scheduleUpdate)
        {
            if (Globals.isTutorial)
            {
                if (player.transform.position.x > transform.position.x - Globals.CocosToUnity(700) && !Globals.lockPlayerMovementForTutorial)
                {
                    if (GameManager.instance.controlsTutorialManager.currentState != TutorialState.Shooting)
                    {

                        DOTween.Sequence().SetId(tweenId).AppendInterval(1f).AppendCallback(() => {
                            //DOTween.Sequence().SetId(player.tweenId).Append(player.transform.DOMove(new Vector2(transform.position.x - Globals.CocosToUnity(700), transform.position.y + Globals.CocosToUnity(550)), 1.5f)).Play();

                            SetAllowPing(false, PINGTYPE.MISSION_AHEAD);

                            //DOTween.Sequence().SetId(player.tweenId).Append(player.GetSkeletonAnimation().transform.DORotate(Vector3.forward * 180, 1)).AppendCallback(() => {
                            //    player.TurnOnBoost(false);
                            //}).Play();

                            GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Shooting);
                        }).Play();
                    }
                    //if(!Globals.isJoystickConnected)
                    //{
                    //    GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.LockPlayerMovement);
                    //}

                }
            }

            if (_currentState == LaserTowerType.Shielded)
            {
                if (generator._isDead == true)
                {

                    healthBar.gameObject.SetActive(true);
                    enemySprite.state.SetAnimation(0, "idle", true);
                    _currentState = LaserTowerType.Unshielded;
                    colliderShieldOff.gameObject.SetActive(true);
                    bounds = colliderShieldOff;

                        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                        missionPing.Init(transform, true);

                        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
                        missionPing.Init(transform, false);
                    missionPing.bgImage.color =  new Color(1, 127 / 255f, 0);
                    missionPing.image.color = new Color(1, 127 / 255f, 0);

                    if (Globals.isTutorial)
                    {
                        missionPing.SetPing(false);

                    }
                }

            }
        }
    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        stats.speed = baseStats.speed = 3.5f + UnityEngine.Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + UnityEngine.Random.value;
        stats.health = baseStats.health = 10000;
        stats.bulletDamage = baseStats.bulletDamage = 3;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 50;
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.coinAwarded = baseStats.coinAwarded = 25;
        stats.xp = baseStats.xp = stats.maxHealth.Value / 2;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (_currentState == LaserTowerType.Shielded)
        {
            if (bounds.CurrentCollider)
            {
                return bounds.CurrentCollider.bounds.Contains(P1);
            }
        }
        else
        {
            if (stats.health / stats.maxHealth.Value < 0.4 && onFire == false)
            {
                flames.SetActive(true);
                onFire = true;
                GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAboveWater, flames.transform.position, false, 1, 1, 1);
            }
            if (bounds.CurrentCollider)
            {
                return bounds.CurrentCollider.bounds.Contains(P1);
            }
        }
        return false;
    }

    public override void Destroy()
    {
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        if (GameData.instance.fileHandler.currentMission==0)
        {
            Globals.enemyKilledCountInTutorial = 0;
        }
        base.Destroy();
        
    }
}
