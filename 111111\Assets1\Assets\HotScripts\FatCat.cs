using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
public class FatCat : Enemy
{
    private Bone bone;
    private Tesla tesla;

    [SerializeField] private BoundingBoxFollower boundingBox;
    [SerializeField] private Sprite bulletSprite;

    private PlayerPing missionPing;
    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        tweenId = "fatCat" + GetInstanceID();
        schedulerId = "fatCatS" + GetInstanceID();
        InitStats();
        allowRelocate = false;

        enemyCollisionRadius = Globals.CocosToUnity(1000 * 1000);
        explosionType = Explosions.ExplosionType.ExplosionTypeBoss;
        transform.SetWorldPositionY(Globals.LOWERBOUNDARY);// +Globals.CocosToUnity(-50));
        enemySprite.state.SetAnimation(0, "idle", true);
        transform.SetWorldPositionX(Globals.CocosToUnity(15000));

        DynamicZoom dz = DynamicZoom.Create(enemySprite.transform, 2, 400, Globals.CocosToUnity(1300));
        //this.addChild(dz);


        missionPing = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
        missionPing.Init(transform, false);

        enemySprite.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
          {
              if (spineEvent.Data.Name == "shoot")
              {
                  Shoot();
              }



          };

        scheduleUpdate = true;
    }

    //inherited function
    public override bool CheckCollision(Vector2 P1)
    {

        if (Vector2.SqrMagnitude(P1 - (Vector2)transform.position) < enemyCollisionRadius)
        {
            if (boundingBox.CurrentCollider)
            {
                return boundingBox.CurrentCollider.bounds.Contains(P1);
            }
        }
        return false;
    }

    public override bool TakeHit(double damage)
    {
        return base.TakeHit(damage);
    }

    //points the fat cat towards tesla
    public void AddTesla(Tesla newTesla)
    {
        tesla = newTesla;
        DOTween.Sequence().SetId(tweenId).Append(transform.DOMove(new Vector2(tesla.transform.GetWorldPositionX() + Globals.CocosToUnity(400), transform.GetWorldPositionY()), 80)).AppendCallback(Attack).Play();

    }

    public void Shoot()
    {
        {

            bone = enemySprite.skeleton.FindBone("gunShoot");
            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.setDamage(stats.bulletDamage * 5);
            bullet.SetSpriteFrame(bulletSprite);
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            bullet.transform.SetScale(1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(100));
            bullet.duration = stats.bulletSpeed;
            bullet.transform.position = bone.GetWorldPosition(enemySprite.transform);



            bullet.transform.SetRotation(bone.WorldRotationX + 75);

            Vector2 pos = bullet.transform.position;
            pos.x = pos.x + Globals.CocosToUnity(700) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation());
            pos.y = pos.y + Globals.CocosToUnity(700) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation()) * -1;
            bullet.gameObject.SetActive(true);
            DOTween.Sequence().SetId(bullet.tweenId).Append(bullet.transform.DOJump(pos, 3, 1, stats.bulletSpeed)).AppendCallback(() => { bullet.HasHit(); }).Play();
            //bullet.runAction(Sequence::create(JumpBy::create(stats.bulletSpeed, cocos2d::Point(700 * sinf(CC_DEGREES_TO_RADIANS(bullet.getRotation())), 700 * cosf(CC_DEGREES_TO_RADIANS(bullet.getRotation()))), 300, 1), NULL));

            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
            DOTween.Sequence().SetId(schedulerId).AppendInterval(stats.bulletSpeed - 0.01f).AppendCallback(() =>
            {
                for (int i = 0; i < 5; i++)
                {
                    Bullet bLayer = null;
                    bool didFindBullet = false;
                    foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
                    {
                        if (!b.isInUse)
                        {
                            bLayer = b;
                            bLayer.isInUse = true;
                            didFindBullet = true;
                            break;
                        }
                    }
                    if (!didFindBullet)
                    {
                        return;
                    }
                    bLayer.setDamage(60);
                    bLayer.SetSpriteFrame(bulletSprite);
                    bLayer.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
                    bLayer.setRadiusEffectSquared(1);
                    bLayer.transform.SetScale(0.65f);

                    bLayer.duration = 2;
                    bLayer.transform.position = pos;


                    bLayer.transform.SetRotation(135 + (i * 20));
                    Vector2 dest = new Vector2(Globals.CocosToUnity(700) * Mathf.Sin(Mathf.Deg2Rad * bLayer.transform.GetRotation()), Globals.CocosToUnity(700) * Mathf.Cos(Mathf.Deg2Rad * bLayer.transform.GetRotation()));

                    bLayer.PlayBulletAnim(2, dest, false, null, null, Ease.InSine);//.runAction(EaseIn::create(MoveBy::create(2, cocos2d::Point(700 * sinf(CC_DEGREES_TO_RADIANS(b.getRotation())), 700 * cosf(CC_DEGREES_TO_RADIANS(b.getRotation())))), 0.5));

                    GameSharedData.Instance.enemyBulletInUse.Add(bLayer);

                }

            }).Play();

        }
    }

    public override void Destroy()
    {
        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeSaveBuilding && Globals.gameType == GameType.Training)
        {
            GameManager.instance.missionManager.MissionComplete();
        }
        base.Destroy();

    }

    private void Update()
    {

    }

    private void Attack()
    {
        enemySprite.state.SetAnimation(0, "shoot", false);
        enemySprite.state.AddAnimation(0, "idle", true);

        GameSharedData.Instance.enemyList.Remove(this);

        if (tesla)
        {
            DOTween.Sequence().SetId(tesla.tweenId).AppendInterval(2).AppendCallback(CreateExplosions).AppendInterval(0.25f).AppendCallback(tesla.Destroy).Play();
        }
        DOTween.Sequence().SetId(schedulerId).AppendInterval(7f).AppendCallback(base.Destroy).Play();
        DOTween.Sequence().SetId(tweenId).AppendInterval(4f).Append(transform.DOBlendableMoveBy(new Vector2(Globals.CocosToUnity(-10000), 0), 3)).Play();
        GameManager.instance.missionManager.MissionFailed();

    }

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();


        stats.speed = baseStats.speed = 7 + Random.value;//bhut taiz
        stats.turnSpeed = baseStats.turnSpeed = 0.5f + Random.value;

        //TODO REmove
        float value = PlayerPrefs.GetInt("GamePlayModeType");
        float mappedValue = value * (0.75f / 2) + 0.25f;

        stats.health = baseStats.health = 8000 * mappedValue;
        stats.bulletDamage = baseStats.bulletDamage = 60;
        stats.bulletSpeed = baseStats.bulletSpeed = 2;
        stats.missileDamage = baseStats.missileDamage = GameData.instance.fileHandler.TrainingLevel * 10;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;

        stats.coinAwarded = 15;

    }

    private void CreateExplosions()
    {
        AudioManager.instance.PlaySound(AudioType.Explosion, Constants_Audio.Audio.enemyBuildingDestroy);
        //Globals.PlaySound("res/Sounds/SFX/enemyBuildingDestroy.mp3");
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBuildingMission, tesla.transform.position, false, 1, 1, 0);
    }


}