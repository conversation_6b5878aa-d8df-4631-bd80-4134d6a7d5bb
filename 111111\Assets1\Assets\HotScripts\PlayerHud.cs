using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using TMPro;

public class PlayerHud : MonoBehaviour
{
    RectTransform rectTransform;
    PlayerController player;

    [SerializeField] TextMeshProUGUI levelLabel, SkillNumLabel, coinsLabel,yinbiLabel, progressLabel, survivalProgressLabel, residueWaveLabel, diamondLabel;
    [SerializeField] RectTransform progressBarRect, survivalProgressRect;
    [SerializeField] Image xpBarFill, specialButtonFill, diamondImg;
    [SerializeField] Slider progressBarFill;
    [SerializeField] GameObject specialLabel;
    [SerializeField] CustomButton desktopSpecialAbility;
    [SerializeField] Animator[] comboAnims;

    [SerializeField] private ComboManager comboManagerObj;
    [SerializeField] private Sprite[] consoleSprite;
    [SerializeField] private GameObject dashButtonContainer;
    [SerializeField] private CustomButton dashButtonConsole;
    [SerializeField] private CustomButton dashButtonConsoleGlyph;
    [SerializeField] private Image dashButtonConsoleGlyphImage;
    [SerializeField] private Image dashButtonDesktop;
    [SerializeField] private Sprite[] dashButtonDesktopSprite;
    [SerializeField] private TextMeshProUGUI dashLabel;
    [SerializeField] private TextMeshProUGUI waveNumberLabel;
    [SerializeField] private TextMeshProUGUI waveStatusLabel;
    [SerializeField] private TextMeshProUGUI nextWaveTimerLabel;
    [SerializeField] private GameObject waveIcon;

    [SerializeField] private GameObject pauseButton;
    [SerializeField] private GameObject switchAutoShootButton;
    //[SerializeField] private GameObject autoShootOpen;
    //[SerializeField] private GameObject autoShootClose;
    [SerializeField] private Image FlashImage;
    [SerializeField] private SkeletonGraphic flashSpine;
    [SerializeField] private RectTransform notice;
    [SerializeField] private Image noticeBg;
    [SerializeField] private Text noticText;
    [SerializeField] private Text LevelText;
    [SerializeField] private RectTransform titleTran;

    Sequence noticSeq;
    bool canShowSpecialLabel = false;
    bool _allowComboGlow = true;
    bool allowSpecialGlow = true;
    //bool _isComboUnlocked = false;
    int comboLevel = 0;
    float initialX;
    float initPosX;
    float initPosY;

    Color HEALTH_COLOR = new Color(24, 222, 23);
    Color ENERGY_COLOR = new Color(254, 138, 19);
    const string EVENT_COMBO = "ComboEnd";

    private string tweenId;
    private string schedulerId;

    public int survivalTotalPoint = 0;
    public int survivalCurPoint = 0;
    public int survivalResidueWave = 0;
    public Dictionary<string, WaveInfoStruct> _wInfo;

    //public TextMeshProUGUI allKill, curEnemyNum, curEnemyMiss;

    public Transform attackCircle;
    private float atio = 0.1f;
    public void UpdateBattleUI()
    {
        //allKill.text = "当前关卡已经击杀的数量" + GameManager.instance.missionManager.totalPoints;
        //curEnemyNum.text = "当前屏幕中的怪的总数" + GameSharedData.Instance.enemyList.Count;
        //curEnemyMiss.text = "当前敌人导弹的数量" + GameSharedData.Instance.enemyMissilesInUse.Count;
    }

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
    }

    private void Start()
    {
        //if (Helper.IsNotchDetection()) {
        //    //Debug.Log("-------是刘海屏-------");
        //    titleTran.anchoredPosition = new Vector2(0f, -180f);
        //} else {
        //    //Debug.Log("-------非刘海屏-------");
        //    titleTran.anchoredPosition = new Vector2(0f, -100f);
        //}
        //Debug.Log($"{UnityEngine.Screen.height}X{ UnityEngine.Screen.width}");
        tweenId = "PH" + GetInstanceID();
        schedulerId = "PHS" + GetInstanceID();
        comboManagerObj.Init();
        RegisterEvents();
        //pauseButton.SetActive(false);
        diamondImg.gameObject.SetActive(false);
        diamondLabel.gameObject.SetActive(false);

        //allKill.gameObject.SetActive(false);
        //curEnemyNum.gameObject.SetActive(false);
        //curEnemyMiss.gameObject.SetActive(false);
    }

    public int GetKillCombo()
    {
        return comboManagerObj.killComboCounter;
    }

    public void AddKillToComboManager()
    {
        comboManagerObj.AddKill();
        //_playerHUD->updateComboBar(comboManagerObj->killComboCounter);

    }

    private void RegisterEvents()
    {
        {
            Observer.RegisterCustomEvent(gameObject, "newWave", () =>
            {
                int val = GameData.instance.fileHandler.coins;
                int finalVal = GameData.instance.fileHandler.coins + 100;
                DOTween.To(() => val, x => val = x, finalVal, 1).
                OnUpdate(() =>
                        {
                            //                   coinsLabel->setString(to_string(int(val)));
                            //coinsLabel.text = val.ToString();
                            GameData.instance.fileHandler.coins = val;


                        });



                DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(() =>
                {
                    UpdateCoins();

                });
                //停止自动射击
                GameManager.instance.player.EndShooting();

                //waveNumberLabel.text = "第" + Globals.survivalModeWaveCounter.ToString() + "波";
                //waveStatusLabel.text = "已消灭!";
                //waveStatusLabel.color = Color.green;
                //AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.waveCompleted);
                string sc = "Wave" + (Globals.survivalModeWaveCounter + 1).ToString();
                Debug.Log("下一波的key" + sc);
                Debug.Log("_wInfo总数量" + _wInfo.Count.ToString());
                if (_wInfo.ContainsKey(sc))
                {
                    //Debug.Log("进来了");
                    //DOTween.Sequence().SetId(tweenId + "waveIcon").Append(waveIcon.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveIcon.transform.DOScale(0, 0)).AppendInterval(1.5f)
                    //    .Append(waveIcon.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveIcon.transform.DOScale(0, 0)).Play();

                    //DOTween.Sequence().SetId(tweenId + "waveNumberLabel").Append(waveNumberLabel.transform.DOScale(1, 0.5f)).AppendInterval(1.5f).Append(waveNumberLabel.transform.DOScale(0, 0)).AppendCallback
                    //    (() =>
                    //    {
                            //一波结束开始三选一
                            LuaToCshapeManager.Instance.StopShoot();
                            Debug.Log("请求打开");
                            LuaManager.Instance.RunLuaFunction("BattleManager.UpgradeSkillHandle");

                    //        waveNumberLabel.text = "第" + (Globals.survivalModeWaveCounter) + "波";
                    //    }).AppendInterval(1.5f)
                    //    .Append(waveNumberLabel.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveNumberLabel.transform.DOScale(0, 0)).Play();

                    //DOTween.Sequence().SetId(tweenId + "waveStatusLabel").Append(waveStatusLabel.transform.DOScale(1, 0.5f)).AppendInterval(1.5f).Append(waveStatusLabel.transform.DOScale(0, 0)).AppendCallback
                    //(() =>
                    //{
                    //    waveStatusLabel.color = Color.white;
                    //    waveStatusLabel.text = "来袭!";
                    //}).AppendInterval(1.5f)
                    //.AppendCallback(() =>
                    //{
                    //    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.upcomingWave);
                    //})
                    //.Append(waveStatusLabel.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveStatusLabel.transform.DOScale(0, 0)).Play();









                    // DOTween.Sequence().SetId(tweenId).Append(waveIcon.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveIcon.transform.DOScale(0, 0)).AppendInterval(1.5f)
                    // .AppendCallback(()=> {
                    //         AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.upcomingWave);
                    //         //一波结束开始三选一
                    //         LuaToCshapeManager.Instance.StopShoot();
                    //         LuaManager.Instance.RunLuaFunction<bool>("BattleManager.UpgradeSkillHandle", false);
                    //  }).Play();

                    // DOTween.Sequence().SetId(tweenId).Append(waveStatusLabel.transform.DOScale(1, 0.5f)).AppendInterval(1.5f).Append(waveStatusLabel.transform.DOScale(0, 0)).AppendInterval(1.5f)
                    // .AppendCallback(() =>
                    // {
                    //     waveNumberLabel.text = "WAVE - " + Globals.survivalModeWaveCounter + 1;
                    //     waveStatusLabel.color = Color.white;
                    //     waveStatusLabel.text = "INCOMING";
                    // }).AppendInterval(0.5f)
                    // .AppendCallback(() =>
                    // {
                    //    AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.upcomingWave);
                    // })
                    //.Append(waveStatusLabel.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveStatusLabel.transform.DOScale(0, 0)).Play();




                    //waveNumberLabel.text = "WAVE - " + Globals.survivalModeWaveCounter + 1;
                    //waveStatusLabel.color = Color.white;
                    //waveStatusLabel.text = "INCOMING";

                    //DOTween.Sequence().SetId(tweenId).Append(waveIcon.transform.DOScale(1f, 0.5f)).AppendInterval(1.5f).Append(waveIcon.transform.DOScale(0, 0)).AppendInterval(0.5f).Play();

                    //DOTween.Sequence().SetId(tweenId).Append(waveNumberLabel.transform.DOScale(1, 0.5f)).AppendInterval(1.5f).Append(waveNumberLabel.transform.DOScale(0, 0)).AppendInterval(0.5f).Play();
                    //DOTween.Sequence().SetId(tweenId).Append(waveStatusLabel.transform.DOScale(1, 0.5f)).AppendInterval(1.5f).Append(waveStatusLabel.transform.DOScale(0, 0)).AppendInterval(0.5f).Play();


                    //DOTween.Sequence().SetId(tweenId).Append(nextWaveTimerLabel.transform.DOScale(1, 1f)).AppendCallback(() =>
                    //{
                    //    //UpdateNextWaveTimer();
                    //    //一波结束开始三选一
                    //    LuaToCshapeManager.Instance.StopShoot();
                    //    LuaManager.Instance.RunLuaFunction("BattleManager.UpgradeSkillHandle");

                    //}).AppendInterval(4f).Append(nextWaveTimerLabel.transform.DOScale(0, 0)).Play();
                }
                else
                {
                    Debug.Log("_wInfo中不存在" + sc);
                }



            });

        }

    Observer.RegisterCustomEvent(gameObject, "show_player_hud", () => {
            //TODO
            //_playerHUD->runAction(MoveTo::create(0.5f, cocos2d::Point(winMinBounds.x + offsetInside + extraNotchDistance, winMaxBounds.y - offsetInside)));
            //_playerHUD->setInitialPosition(cocos2d::Point(winMinBounds.x + offsetInside + extraNotchDistance, winMaxBounds.y - offsetInside));


        });


        Observer.RegisterCustomEvent(gameObject, "hide_player_hud", () =>
         {
             HideHud();
         });

Observer.RegisterCustomEvent(gameObject, "EndControllerDashTutorial", () => {

    if (dashLabel && dashButtonContainer)
    {
        dashLabel.gameObject.SetActive(false);
        dashButtonContainer.SetActive(false);
        GameManager.instance.timeManager.SetTimescale(1.0f);

    }

});

Observer.RegisterCustomEvent(gameObject, "DashTutorial", () =>
{
    dashButtonContainer.SetActive(true);
    if (Globals.isJoystickConnected)
    {

        string dashButtonString = "L2";
        if (Globals.controllerSetting == 2)
            dashButtonString = "R2";

        Sprite sp = null;
#if UNITY_PS4 || UNITY_PS5
            sp = consoleSprite[0];
            
#elif UNITY_XBOXONE
            sp = consoleSprite[0];
#endif
        if (sp)
        {
            dashButtonConsoleGlyph.gameObject.SetActive(true);
            dashButtonConsoleGlyphImage.sprite = sp;
            dashButtonConsoleGlyph.CallForAttention(0.01f);
        }
        else
        {
            dashButtonConsole.gameObject.SetActive(true);
            dashButtonConsoleGlyph.CallForAttention(0.01f);
        }


    }
    else
    {
#if UNITY_STANDALONE

                dashButtonDesktop.gameObject.SetActive(true);

                 DOTween.Sequence().SetId(tweenId).SetUpdate(true).AppendInterval(0.1f).AppendCallback(() => { dashButtonDesktop.sprite = dashButtonDesktopSprite[0]; }).AppendInterval(0.1f).AppendCallback(() => { dashButtonDesktop.sprite = dashButtonDesktopSprite[1]; }).SetLoops(-1).Play();
#else
        // set dash button glow visisble
        //TODO Check
        //static_cast<GameController*>(GETPLAYERCONTROLLER->getParent())->_hudLayer->pauseLayer->setScale(0);

        InputController.instance.dashButtonGlow.gameObject.SetActive(true);
        InputController.instance.dashTutorialHand.gameObject.SetActive(true);

#endif
    }
    dashLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.TUTORIAL)["dash"] as string; ;
    if (Vector2.Distance(GameManager.instance.player.transform.position, GameSharedData.Instance.enemyList[0].transform.position) > Globals.CocosToUnity(600))
    {
        float val = GameManager.instance.timeManager.TimeScale;
        DOTween.To(() => val, x => val = x, 0.015f, 0.1f).SetUpdate(true).OnUpdate(() =>
        {
            if (PlayerPrefs.GetInt("DashTutorial2") == 0)
            {
                GameManager.instance.timeManager.SetTimescale(1);
            }
            else if (GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
            {
                GameManager.instance.timeManager.SetTimescale(val);
            }
        });
    }
    else
    {
        if (GameManager.instance.player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING && GameManager.instance.player.Stats.health > 0)
        {

            GameManager.instance.timeManager.SetTimescale(0.015f);

            //  add here
        }

    }
    PlayerPrefs.SetInt("DashTutorial2", 1);
    InputController.instance.SetDash(true);
});
    }

    private void UpdateNextWaveTimer()
    {
        nextWaveTimerLabel.text = "NEXT WAVE IN:\n00:03";
        DOTween.Sequence().SetId(schedulerId).AppendInterval(1).AppendCallback(()=> {
            nextWaveTimerLabel.text = "NEXT WAVE IN:\n00:02";

        }).AppendInterval(1).AppendCallback(() => {
            nextWaveTimerLabel.text = "NEXT WAVE IN:\n00:01";

        }).AppendInterval(1).AppendCallback(() => {
            nextWaveTimerLabel.text = "NEXT WAVE IN:\n00:00";

        }).Play();
    }

    private void Update()
    {
        if (Input.GetMouseButtonUp(1))
        {
            if (GameData.instance.fileHandler.currentMission == 2)
            {
                DOTween.Kill(tweenId);
                DOTween.Kill(schedulerId);
                dashButtonContainer.SetActive(false);
                dashButtonDesktop.gameObject.SetActive(false);
                //if (PlayerPrefs.GetInt("DashTutorial2") == 1)
                //{
                //    GameManager.instance.timeManager.SetTimescale(1.0f);
                //    PlayerPrefs.SetInt("DashTutorial2", 0);
                //}
            }
        }
    }

    public void Init()
    {

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            HideProgressBar();
        }

        DOTween.Sequence().AppendCallback(UpdateProgress).AppendInterval(0.5f).SetLoops(-1);

        //#if UNITY_STANDALONE
        //        canShowSpecialLabel = true;
        //#else
        //    if (GameData.instance.fileHandler.currentMission != 0)
        //    {
        //        if (Globals.gameType == GameType.Training)
        //        {
        //            InitProgressLabel();
        //                DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
        //                {
        //                    DOTween.Sequence().AppendCallback(UpdateProgress).AppendInterval(0.5f).SetLoops(-1);
        //                });
        //        }

        //    }
        //#endif
        if (specialLabel) specialLabel.SetActive(false);

        //_coinBg = Sprite::createWithSpriteFrameName("coinHud-idle_0.png");
        //_coinLabel = Label::createWithTTF(to_string(FileHandler::getInstance()->coins), GAME_FONT, 50);


        //_xpBar = ProgressTimer::create(Sprite::create("res/GameHud/PlayerHud/XP_Radial_Bar.png"));

        //Sprite* xpBg = Sprite::create("res/GameHud/PlayerHud/XP_base.png");
        //if (FileHandler::getInstance()->currentMission == 0)
        //{
        //    xpBg->setTexture("res/GameHud/PlayerHud/XP_baseFtux.png");
        //}
        //_xpBar->addChild(xpBg, -1);
        //xpBg->setPosition(xpBg->getContentSize() / 2);

        //_level->setPosition(xpBg->getContentSize() / 2);
        //_xpBar->addChild(_level, 5);
        //_level->enableOutline(Color4B::BLACK, 2);

        GameManager.instance.SetSpecialGlow(false);
        player = GameManager.instance.player;
        UpdateLevel(GameData.instance.fileHandler.playerLevel);
        UpdateXpBar();
        UpdateHealthBar(100);
        UpdateEnergyBar();
        UpdateComboBar(0);
        UpdateCoins();
        UpdateYinBi();
        InitSwitchAutoShoot();
        DOTween.Sequence().AppendCallback(UpdateEnergyBar).AppendInterval(0.03f).SetLoops(-1);
        //foreach (Animator animator in comboAnims)
        //    animator.Play("Main", 0, 1);

        //EventListenerCustom* eventComboUpdate = EventListenerCustom::create(EVENT_COMBO, [=](EventCustom * c){
        //    this->updateComboBar((int)(unsigned long)c->getUserData());
        //});

        //_eventDispatcher->addEventListenerWithSceneGraphPriority(eventComboUpdate, this);


        //if (GameData.instance.fileHandler.missionsCompleted >= 3)
        //{
        //    _isComboUnlocked = true;
        //}

        //if (Globals.isTutorial)
        //{
        //    _isComboUnlocked = true;
        //}

        //{

        //    EventListenerCustom *event = EventListenerCustom::create("hide_player_hud", [=](EventCustom * c){

        //        _coinLabel->setVisible(false);
        //        _coinBg->setVisible(false);


        //    });
        //    _eventDispatcher->addEventListenerWithSceneGraphPriority(event, this);


        //}
        canShowSpecialLabel = true;
        initialX = rectTransform.anchoredPosition.x;

        InitProgressLabel();

        LevelText.text = Globals.g_currentStageName;

        rectTransform.DOAnchorPos(new Vector2(0, rectTransform.anchoredPosition.y), 0.5f);

        pauseButton.SetActive(GameData.instance.fileHandler.currentMission != 0);

        SpriteRenderer sr = attackCircle.GetComponent<SpriteRenderer>();
        Sprite sp = sr.sprite;
        if (sp)
        {
            atio = sp.rect.width / 100f;
        }
        
        SetAttributeSkillAddDistance();
    }

    public void SetAttributeSkillAddDistance()
    {

        float count = LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.攻击距离);
        float percent = LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.攻击距离);
        float findDistance = (Globals.whiteCatAssistDirection + count) * (1 + percent/10000f);
        float scaleValue = findDistance * 2 / atio;
        //Debug.Log("count=" + count + "   percent = " + percent);
        //Debug.Log("Globals.whiteCatAssistDirection="+ Globals.whiteCatAssistDirection + "   findDistance = " + findDistance);
        attackCircle.DOScale(scaleValue, 0.5f);
    }

    public void SetInitialPosition(Vector2 pos)
    {
        initPosX = pos.x;
        initPosY = pos.y;
    }


    public Vector2 GetInitialPosition()
    {
        return new Vector2(initPosX, initPosY);
    }

    public void UpdateLevel(int level)
    {
        if (!levelLabel)
            return;

        levelLabel.text = level.ToString();//GameData.instance.fileHandler.playerLevel.ToString();
    }

    public void UpdateXpBar()
    {
        //原版
        float percentage = ((float)GameData.instance.fileHandler.playerXP / (float)GameData.instance.fileHandler.xpRequiredThisLevel);


        xpBarFill.fillAmount = percentage;
    }

    public void UpdateHealthBar(float percentage)
    {
        percentage = Mathf.Clamp(percentage, 0, 100);
    }

    public void UpdateEnergyBar()
    {
        float percentage = (float)(GameManager.instance.player.Stats.energy / GameManager.instance.player.Stats.maxEnergy) * 100;
        player.SetEnergyhBar(percentage);
    }

    public void AddComboPoint()
    {
        UpdateComboBar(comboLevel + 1);
    }
    public void UpdateSpecialBar(float val)
    {
        val = Mathf.Clamp(val, 0, 100);
        specialButtonFill.fillAmount = val / 100f;

        if(Globals.autoShootMode && val > 99) GameManager.instance.player.secondaryWeapon.ActivateSpecialAbility();
        if (val > 99 && allowSpecialGlow && canShowSpecialLabel)
        {
            #if UNITY_STANDALONE
            EnableSpecialAbilityButton();
            #endif
            DOTween.Sequence().SetId("SpecialLabelTween").AppendInterval(0.25f).AppendCallback(() => specialLabel.SetActive(true));

            allowSpecialGlow = false;
            AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.powerUpCollected2);
            
            if (Globals.isFTUETutorial)
            {
                if (!Globals.specialAbilityTutorialExecuted)
                {
                    GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Activate_Special_Ability_Mode);
                    Globals.specialAbilityTutorialExecuted = true;

                }

            }
        }
        else
        {
            if (val < 99 && !allowSpecialGlow && canShowSpecialLabel)
            {
                DOTween.Kill("SpecialLabelTween");
                allowSpecialGlow = true;
                //specialLabel.SetActive(false);
                DisableSpecialAbilityButton();
            }
        }
    }

    void UpdateComboBar(int val)
    {
        if (player == null) return;
        if (player.IsInBerzerk)
        {
            return;
        }


        for (int i = 1; i <= 10; i++)
        {

            string slotStr = "KIT/Berserk_Light" + i.ToString();

            if (i <= val)
            {
                if (i == val && val > comboLevel)
                {
                    comboAnims[i - 1].Play("Main", 0, 0);
                    //Sprite* pAnimation = Sprite::createWithSpriteFrameName("comboPointAnimation1.png");
                    //pAnimation->setScale(1.5f);

                    //string boneStr = "BerserkLight" + i.ToString();
                    //Spine.Bone bone = mainHud.Skeleton.FindBone(boneStr);
                    //Vector2 bonePos = bone.GetWorldPosition(mainHud.transform);
                    //pAnimation->setPosition(bone->worldX, bone->worldY);
                }
            }

        }

        comboLevel = val;
        if (val == 10)
        {
            UpdateComboBar(0);
            player.EnterBerzerkMode();
        }
    }

    public void UpdateCoins()
    {
        if (!coinsLabel)
            return;

        coinsLabel.text = GameData.instance.fileHandler.coins.ToString();
    }

    /// <summary>
    /// 更新界面上显示的银币数
    /// </summary>
    public void UpdateYinBi()
    {
        if (!yinbiLabel)
            return;

        yinbiLabel.text = LuaToCshapeManager.Instance.totalYinBi.ToString();
    }

    public void UpdateDiamonds()
    {
        if (!diamondLabel)
            return;

        if (!diamondLabel.gameObject.activeSelf)
        {
            diamondLabel.gameObject.SetActive(true);
            diamondImg.gameObject.SetActive(true);
        }

        diamondLabel.text = LuaToCshapeManager.Instance.totalDiamond.ToString();
    }

    public void SetWhitePortrait()
    {
        //mainHud.Skeleton.SetAttachment("KIT/Explottens_KIT_Pic", "KIT/Explottens_White_KIT_Pic");
    }



    public void InitProgressLabel()
    {
        if (GameData.instance.fileHandler.currentMission == 28)
            return;

        if (Globals.isBossMode)
            return;

            if (!progressLabel)
            return;

        //if(Globals.gameType == GameType.Survival)
        //{
        //    survivalProgressLabel.text = Mathf.Max(0, (survivalTotalPoint - survivalCurPoint)).ToString();// GameManager.instance.missionManager.totalPoints.ToString();
        //    residueWaveLabel.text = string.Format("下一波({0}):", survivalResidueWave);
        //    DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
        //    {
        //        survivalProgressRect.DOAnchorPos(new Vector2(survivalProgressRect.anchoredPosition.x, -30), 0.5f);
        //        LuaManager.Instance.RunLuaFunction("BattleManager.InitProgressLabelEnd");
        //    });
        //    return;
        //}


        progressBarFill.value = 0;

        int count = GameManager.instance.missionManager.totalPoints - GameManager.instance.missionManager.autoKillPoints;
        count = Mathf.Max(0,count);
        string str2 = count.ToString();

        //string str3 = GameManager.instance.missionManager.totalPointsRequired.ToString();
        string str3 = GameManager.instance.missionManager.autoKillPoints.ToString();

        string str = (GameData.instance.GetMenuData("InGameHud")["progress"] as string) + " " + str2 + "/" + str3;
        progressLabel.text = str;
        SkillNumLabel.text = str2 + "/" + str3;

        // DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
        // {
        //     progressBarRect.DOAnchorPos(new Vector2(progressBarRect.anchoredPosition.x, 0.4f), 0.5f);
        //     RectTransform levelRect = (RectTransform)LevelText.transform;
        //     levelRect.DOAnchorPos(new Vector2(progressBarRect.anchoredPosition.x, -46f), 0.5f);
        //     titleTran.DOAnchorPos(new Vector2(progressBarRect.anchoredPosition.x, -40.4f), 0.5f);

        //     LevelText.text = Globals.g_currentStageName;
        // });

        //progressBarRect.anchoredPosition = new Vector2(progressBarRect.anchoredPosition.x, 0.4f);
        //RectTransform levelRect = (RectTransform)LevelText.transform;
        //levelRect.anchoredPosition = new Vector2(progressBarRect.anchoredPosition.x, -46f);
        //titleTran.anchoredPosition = new Vector2(progressBarRect.anchoredPosition.x, -40.4f);
    }


    public void UpdateProgress()
    {
        if (!progressLabel)
            return;

        //if (Globals.gameType == GameType.Survival)
        //{
        //    survivalProgressLabel.text = Mathf.Max(0, (survivalTotalPoint - survivalCurPoint)).ToString();// GameManager.instance.killsThisRun.ToString();
        //    residueWaveLabel.text = string.Format("下一波({0}):", survivalResidueWave);
        //    return;
        //}
        int count = GameManager.instance.missionManager.totalPoints - GameManager.instance.missionManager.autoKillPoints;
        count = Mathf.Max(0, count);
        string str0 = count.ToString();
        string str1 = GameManager.instance.missionManager.autoKillPoints.ToString();

        string str2 = GameManager.instance.missionManager.totalPoints.ToString();
        string str3 = GameManager.instance.missionManager.totalPointsRequired.ToString();
        string str = (GameData.instance.GetMenuData("InGameHud")["progress"] as string) + " " + str2 + "/" + str3;

        progressLabel.text = str;
        SkillNumLabel.text = str0 + "/" + str1;
        float tp = GameManager.instance.missionManager.totalPoints;
        float tpr = GameManager.instance.missionManager.totalPointsRequired;

        float a = tp / tpr;
        LuaManager.Instance.RunLuaFunction("BattleManager.GameProgress", a);
        progressBarFill.value = a;
        //if(a >= 0.99)
        //{
        //    player.canHit = false;
        //}
    }

    public void HideHud()
    {
        rectTransform.DOAnchorPos(new Vector2(initialX, rectTransform.anchoredPosition.y), 0.1f);
        
    }


    public void ShowHud()
    {
        rectTransform.DOAnchorPos(new Vector2(initialX, rectTransform.anchoredPosition.y), 0.1f);
        pauseButton.SetActive(true);
    }

    private void HideProgressBar()
    {
        progressBarRect.gameObject.SetActive(false);
    }

    public void EnableSpecialAbilityButton()
    {
        if (Globals.mobileControls)
        {
            GameManager.instance.SetSpecialGlow(true);
            return;
        }

        desktopSpecialAbility.gameObject.SetActive(true);
        desktopSpecialAbility.CallForAttention(1);
    }

    public void DisableSpecialAbilityButton()
    {
        if (Globals.mobileControls)
        {
            GameManager.instance.SetSpecialGlow(false);
            return;
        }

        desktopSpecialAbility.StopAttention();
        desktopSpecialAbility.gameObject.SetActive(false);
    }

    public void ClickPauseGame()
    {
        //Time.timeScale = 0;
        LuaToCshapeManager.Instance.PauseOrResumeBattle(0f);
        int coin = LuaToCshapeManager.Instance.BattleAddCoin;
        int killNum = GameManager.instance.missionManager.totalPoints - GameManager.instance.missionManager.autoKillPoints;
        int explodeNum = GameManager.instance.missionManager.autoKillPoints;
        string content = coin + "|" + killNum + "|" + explodeNum;
        LuaManager.Instance.RunLuaFunction<string>("BattleManager.OpenPauseView", content);
    }

    public void InitSwitchAutoShoot()
    {
        //autoShootOpen.SetActive(Globals.autoShootMode);
        //autoShootClose.SetActive(!Globals.autoShootMode);
    }

    public void ClickSwitchAutoShoot()
    {
        if (Globals.autoShootMode)
        {
            PlayerPrefs.SetInt("SYSTEM_AUTO_SHOOT", 0);
            GameManager.instance.EndAutoShoot();
            GameManager.instance.EndAutoShootAB();
        }
        else
        {
            PlayerPrefs.SetInt("SYSTEM_AUTO_SHOOT", 1);
        }
        LuaToCshapeManager.Instance.CheckAutoShootMode();
        InitSwitchAutoShoot();
        InputController.instance.SetShootButton(true);

    }


    /// <summary>
    /// 核弹伤害
    /// </summary>
    
    public void FullScreenDamage(bool playMusic = true)
    {
        if (playMusic)
        {
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 5008);
        }
        FlashImage.gameObject.SetActive(true);
        FlashImage.SetOpacity(0);
        //flashSpine.gameObject.SetActive(true);
        //flashSpine.AnimationState.SetAnimation(0, "end_s", false);
        Sequence seq = DOTween.Sequence();
        seq.SetUpdate(true);
        if (playMusic)
        {
            seq.AppendInterval(1.7f);
        }
        seq.Append(FlashImage.DOFade(1f, 0.3f));
        seq.AppendInterval(0.4f);
        seq.Append(FlashImage.DOFade(0f, 0.3f));
        seq.AppendCallback(() => {
            LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 5009);
            Globals.skillallDamage = true;
        });

    }

    public void ShowAutoUpgradeView(string result)
    {
        if(noticSeq != null)
        {
            noticSeq.Kill();
            noticSeq = null;
        }
        noticSeq = DOTween.Sequence();
        noticText.text = result;
        Helper.RebuilRT(notice);
        notice.gameObject.SetActive(true);
        notice.localPosition = Vector2.zero;
        noticeBg.color = new Color(1, 1, 1, 1);
        noticText.color = new Color(1, 1, 1, 1);
        noticSeq.AppendInterval(1.5f);
        noticSeq.Append(notice.DOLocalMoveY(100f, 1f));
        //noticSeq.Join(noticeBg.DOFade(0, 1));
        //noticSeq.Join(noticText.DOFade(0, 1));
        noticSeq.AppendCallback(() =>
        {
            //Debug.Log("影藏");
            notice.gameObject.SetActive(false);
        });

    }


}
