using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

public class SurvivalMenu : MonoBehaviour
{
    [SerializeField] private RectTransform canvasRect, bgRect, survivalPanelRect, merlinRect, playButtonRect, hqButtonRect,
        survivalLockedPanelRect, lockedButtonRect;
    [SerializeField] private GameObject statsTabActive, statsTabInactive, leaderboardTabActive, leaderboardTabInactive;
    [SerializeField] private CustomButton playButton, lockedButton;
    [SerializeField] private NavigationButton defaultNavButton, hqButton, leftTabNavButton, rightTabNavButton;
    [SerializeField] private TextMeshProUGUI statsTabTMP, lbTabTMP;
    [SerializeField] private Color selectedColor, unselectedColor;
    [SerializeField] private Vector2 bgSize;

    // Start is called before the first frame update
    void Start()
    {
        SetSize(bgRect, bgSize, 0.8f);
        ResizeItem(survivalPanelRect);
        ResizeItem(survivalLockedPanelRect);
        ResizeItem(merlinRect);
        ResizeItem(playButtonRect);
        ResizeItem(lockedButtonRect);
        ResizeItem(hqButtonRect);
        // AudioManager.instance.PlayMusic(Track.trainingMusic, false, 0.25f);
        AudioManager.instance.PlayMusic(7014);
        bool locked = GameData.instance.fileHandler.missionsCompleted <= 6;

        lockedButton.SetInteractable(false);
        lockedButton.gameObject.SetActive(locked);

        survivalLockedPanelRect.gameObject.SetActive(locked);
        leftTabNavButton.gameObject.SetActive(!locked);
        rightTabNavButton.gameObject.SetActive(!locked);

        playButton.defaultAction = StartSurvivalMode;
        playButton.gameObject.SetActive(!locked);

        NavigationButton.currentlySelected = locked ? hqButton : defaultNavButton;
        NavigationButton.ChangeCurrentlySelected(NavigationButton.currentlySelected, true);

        ChangeTab(0);
    }

    public void ChangeTab(int tabIndex)
    {
        statsTabActive.SetActive(tabIndex == 0);
        //statsTabInactive.SetActive(tabIndex != 0);
        leaderboardTabActive.SetActive(tabIndex != 0);
        //leaderboardTabInactive.SetActive(tabIndex == 0);
    }

    void SetSize(RectTransform imgRect, Vector2 imgSizeInPixels, float factor = 1)
    {
        float scale = Mathf.Max(canvasRect.sizeDelta.x / imgSizeInPixels.x, canvasRect.sizeDelta.y / imgSizeInPixels.y);
        scale = (scale * 10) % (int)(scale * 10) > 0.5f ? Mathf.Round(scale * 10) / 10f : scale;

        imgRect.localScale = new Vector3(scale * factor, scale * factor, 1);

        //float bgPosOffsetX = 0;
        //float bgPosOffsetY = 0;
        //float aspectRatio = canvasRect.sizeDelta.x / canvasRect.sizeDelta.y;

        //bgPosOffsetX = (scale * imgSizeInPixels.x - canvasRect.sizeDelta.x) / 3f;
        //bgPosOffsetY = (scale * 750 - 750) / 2;

        //imgRect.anchoredPosition = new Vector2(bgPosOffsetX, bgPosOffsetY);
    }

    void ResizeItem(RectTransform itemRect)
    {
        float initialScale = itemRect.localScale.x;
        float scale = Mathf.Min(canvasRect.sizeDelta.x / 960f, canvasRect.sizeDelta.y / 600f);
        itemRect.localScale = new Vector3(scale * initialScale, scale * initialScale, 1);
        itemRect.anchoredPosition *= scale;
    }

    public void SelectStatsTab()
    {
        ChangeTab(0);
    }

    public void SelectLBTab()
    {
        ChangeTab(1);
    }

    public void StartSurvivalMode()
    {
        Globals.gameType = GameType.Survival;
        SceneManager.LoadScene("GameScene");
    }

    public void MainMenu()
    {
        SceneManager.LoadScene("MainMenu");
    }
}
