﻿using Apq.Net.JsonParams;

namespace GsProxy.JsonParams
{
    /// <summary>
    /// 用于登录的参数
    /// </summary>
    public class LoginParam : ParamBase
    {
        /// <summary>
        /// 命令
        /// </summary>
        public override string Cmd { get; set; } = "Login";

        /// <summary>
        /// 服务器(域名/IP)
        /// </summary>
        public string Server { get; set; } = string.Empty;
        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; set; }
        /// <summary>
        /// 转发器使用的局域网IP
        /// </summary>
        public string LanIp { get; set; } = string.Empty;
        
        /// <summary>
        /// 角色已出生(未出生则必须先登录)
        /// </summary>
        public bool ActorHadBorn { get; set; }
        /// <summary>
        /// 角色ID
        /// </summary>
        public long ActorID { get; set; }
        /// <summary>
        /// 角色名
        /// </summary>
        public string ActorName { get; set; } = string.Empty;
        public long UserID { get; set; }
        public int ZoneID { get; set; }
        /// <summary>
        /// 客户端唯一标识
        /// </summary>
        public string Token { get; set; } = string.Empty;
    }
}