﻿using UnityEngine;

namespace Apq.Extension
{
    public static class Ext_UniEngine
    {
        /// <summary>
        /// 围绕指定的点进行旋转
        /// </summary>
        /// <param name="me">自身坐标</param>
        /// <param name="center">中心点</param>
        /// <param name="axis">旋转轴</param>
        /// <param name="angle">旋转角度</param>
        public static Vector3 RotateAround(this Vector3 me, Vector3 center, Vector3 axis, float angle)
        {
            return Quaternion.AngleAxis(angle, axis) * (me - center) + center;
        }

        /// <summary>
        /// 判断矩形与圆有没有碰到
        /// </summary>
        /// <param name="me">矩形中心</param>
        /// <param name="d">矩形中心到矩形某个顶点的向量</param>
        /// <param name="p">圆心</param>
        /// <param name="r">半径</param>
        public static bool RectCircleIntersect2D(this Vector2 me, Vector2 d, Vector2 p, float r)
        {
            var v = p - me;
            var vf = new Vector2(Mathf.Abs(v.x), Mathf.Abs(v.y));
            var df = new Vector2(Mathf.Abs(d.x), Mathf.Abs(d.y));
            var u = vf - df;
            if (u.x < 0) u.x = 0;
            if (u.y < 0) v.y = 0;
            return u.x * u.x + u.y * u.y <= r * r;
        }

        /// <summary>
        /// 判断矩形与圆有没有碰到
        /// </summary>
        /// <param name="me">我的position作为矩形左边的中点</param>
        /// <param name="width">矩形宽度</param>
        /// <param name="height">矩形高度</param>
        /// <param name="point">圆心(世界坐标)</param>
        /// <param name="radius">半径</param>
        public static bool LaserToCircleIntersect2D(this Transform me, float width, float height, Vector2 point, float radius)
        {
            // 计算 point 在 me局部坐标系中 的坐标
            Vector2 p = me.InverseTransformPoint(point);

            //Debug.Log($"敌人坐标:{point} 转换后:{p}");

            // 激光的矩形范围
#pragma warning disable IDE0017 // 简化对象初始化
            var rect = new Rect
            {
                width = width,
                height = height
            };
#pragma warning restore IDE0017 // 简化对象初始化

            // 不能简化,必须先设置宽高,后设置中心点
            rect.center = new Vector2(width / 2, 0);

            var d = new Vector2(width, height / 2) - rect.center;
            return rect.center.RectCircleIntersect2D(d, p, radius);
        }

        /// <summary>
        /// 计算点到圆的距离
        /// </summary>
        /// <param name="me">点</param>
        /// <param name="point">圆心</param>
        /// <param name="radius">半径</param>
        public static DistancePointToCircle CalcDistance2D_PointToCircle(this Vector3 me, Vector2 point, float radius)
        {
            var distance = new DistancePointToCircle
            {
                // 点到圆心的距离
                DistanceToCenter = ((Vector3)point - me).magnitude
            };

            // 点到圆边的距离(<0时取0)
            var d = distance.DistanceToCenter - radius;
            if (d < 0) d = 0;
            distance.DistanceToEdge = d;

            return distance;
        }
    }
}
