using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;

public class Coin : MonoBehaviour
{
    private float mult = 0;

    public bool follow = false;
    public float speed = 15;
    public bool _isOrb = false;
    private bool scheduleUpdate = false;

    [HideInInspector] public bool isRemovable = true;
    [HideInInspector] public bool isInUse = false;
    [SerializeField] GameObject coin;
    [SerializeField] SkeletonAnimation multiplier;
    [SerializeField] GameObject comboAnimation;
    [SerializeField] GameObject orb;
    private bool isCollected = false;

    private int _goodID;
    private int _soundID;
    private Medicament.Item _medicamentData;

    private bool _needSpecialMove;
    private float _topBorder;
    private float _downBorder;
    private float _leftBorder;
    private float _rightBorder;
    private PlayerController _player;

    private Vector3 _reflectDirction;
    public SpriteRenderer GoodsImage;
    public float followCheckDistance;
    //本次生成的总数（用来判断刷新距离的区间）
    int _totalNum = 1;
    int _numIndex = 0;

    private bool isMoved = false;
    private bool isCaculate = false;
    public void CreateAsOrb(Vector2 location)
    {
        _isOrb = true;
        Init();
        transform.position = location;
        //Globals.coinsList.Add(this);
    }

    public void CreateWithLocation(Vector2 pos)
    {
        Init();
        transform.position = pos;
        //Globals.coinsList.Add(this);
    }

    public void CreateWithLocationAndMultiplier(Vector2 pos, float multiplier)
    {
        mult = multiplier;
        Init();
        transform.position = pos;
        //Globals.coinsList.Add(this);
    }

    private void Init()
    {
        follow = false;
        isMoved = false;
        if (!_isOrb)
        {
            coin.gameObject.SetActive(true);
            coin.transform.SetScale(0.5f);
            SkeletonAnimation skeletonAnimation = coin.GetComponent<SkeletonAnimation>();
            if (GameData.instance.fileHandler.currentMission != 0)
            {
                if (skeletonAnimation != null && _medicamentData.SubType == 8)
                {
                    skeletonAnimation.state.SetAnimation(0, "coin", true);
                }
            }
            else
            {
                if (skeletonAnimation != null)
                {
                    skeletonAnimation.state.SetAnimation(0, "coin", true);
                }

            }
        }
        else
        {
            if (multiplier)
            {
                multiplier.gameObject.SetActive(true);
                multiplier.state.SetAnimation(0, "berserk", true);
                multiplier.transform.SetScale(0.25f);
                orb.transform.SetScale(1.35f);
            }
        }

        float value = Random.value * 100;
        int a = 3;

        if(Globals.isFTUETutorial)
        {
            a = (int)Globals.CocosToUnity(700);
        }

        float distance = 0.5f;

        if(_totalNum < 20)
        {
            distance = 0.3f + Random.value * a;
        }
        else if (_totalNum < 30)
        {
            distance = 0.5f + Random.value * 2f;
        }
        else if (_totalNum < 50)
        {
            distance = 0.5f + Random.value * 4f;
        }
        else
        {
            distance = 0.5f + Random.value * 5;
        }

        


        float duration = 1;

        if(mult!=0)
        { 
            value = -Globals.CocosToUnity(90) + Random.value * Globals.CocosToUnity(180);
            a = (int)Globals.CocosToUnity(300);

            if (Globals.isFTUETutorial)
            {
                a = (int)Globals.CocosToUnity(700);
            }

            distance = mult * (Globals.CocosToUnity(50) + Random.value * a);
        }

        transform.DOBlendableMoveBy(new Vector2(distance * Mathf.Sin(Mathf.Deg2Rad * value), distance * Mathf.Cos(Mathf.Deg2Rad * value)), duration).SetEase(Ease.OutExpo);

        if(!Globals.isFTUETutorial)
        {
            //10秒自动消失
            //DOTween.Sequence().AppendInterval(10).AppendCallback(() =>
            //{
            //    RemoveCoin();
            //});
        }
        scheduleUpdate = true;
        followCheckDistance = Globals.CocosToUnity(612);
        speed = 10;

        StartCoroutine(AutoFollowCheck());
    }

    private IEnumerator AutoFollowCheck()
    {
        yield return new WaitForSeconds(0.5f);
        SetDistanceFollow(Globals.CocosToUnity(612000));
        StopCoroutine(AutoFollowCheck());
    }

    public void SetDistanceFollow(float dis)
    {
        follow = false;
        followCheckDistance = dis;
        isMoved = true;
    }

    void Update()
    {
        if(scheduleUpdate && (LuaToCshapeManager.Instance.canCollected || GameData.instance.fileHandler.currentMission == 0))
        {
            //float dis = Globals.CocosToUnity(612);
            //if (Globals.isFTUETutorial)
            //{
            //    followCheckDistance = Globals.CocosToUnity(552);
            //}
            if (!follow)
            {
                if (Vector2.SqrMagnitude(transform.position - GameManager.instance.player.transform.position) < followCheckDistance)
                {
                    follow = true;
                }
            }
            else
            {
                transform.position = new Vector2(transform.position.x + (GameManager.instance.player.transform.position.x - coin.transform.position.x) * Time.deltaTime * speed, coin.transform.position.y + (GameManager.instance.player.transform.position.y - coin.transform.position.y) * Time.deltaTime * speed);

                //coin->setPosition(coin->getPosition().x + (Player::getInstance()->getPosition().x - coin->getPosition().x) * dt * speed, coin->getPosition().y + (Player::getInstance()->getPosition().y - coin->getPosition().y) * dt * speed);
            }
        }
        if (_needSpecialMove)
        {

            ReflectMove();
        }
    }
    
    public void Collected()
    {
        if(!isCollected)
        {
            if (GameData.instance.fileHandler.currentMission != 0)
            {
                gameObject.SetActive(false);
                if (_medicamentData.SubType == 8)
                {      
                    LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", _soundID);
                    GameData.instance.fileHandler.coins += _medicamentData.SmeltExp;
                    LuaToCshapeManager.Instance.BattleAddCoin += _medicamentData.SmeltExp;
                    GameManager.instance.playerHud.UpdateCoins();
                }
                else if (_medicamentData.SubType == 13 && !Globals.resetControls) //升级卷轴
                {
                    //Time.timeScale = 0;
                    LuaToCshapeManager.Instance.PauseOrResumeBattle(0);
                    LuaToCshapeManager.Instance.StopShoot();
                    //bool isAutoUpgrade = Globals.gameType == GameType.Survival;
                    //暂时屏蔽掉自动升级
                    bool isAutoUpgrade = false;
                    LuaManager.Instance.RunLuaFunction<bool>("BattleManager.UpgradeSkillHandle", isAutoUpgrade);
                }
                else if (_medicamentData.SubType == 12)
                {
                    GameManager.instance.playerHud.AddKillToComboManager();
                }
                //血包
                else if (_medicamentData.SubType == 9) 
                {
                    LuaManager.Instance.RunLuaFunction<int>("SoundManager.CSharpPlaySound", 6021);
                    _player.Stats.health = _player.Stats.maxHealth.Value;
                    _player.UpdateHealthBar(true);
                    _player.healthRegenAnimator.Play("Main", 0, 0);
                }
                //核弹
                else if (_medicamentData.SubType == 11)
                {
                    //GameManager.instance.player.weapon.SetShootMode(false);
                    GameManager.instance.playerHud.FullScreenDamage();
                }
                //磁铁，只吸金币和物品、钻石
                else if (_medicamentData.SubType == 10)
                {
                    foreach (var dropItem in GameSharedData.Instance.coinsInUse)
                    {
                        if(dropItem._medicamentData.SubType == 8 || dropItem._medicamentData.Id > 1000 || dropItem._medicamentData.Id == 3)
                        {
                            dropItem.followCheckDistance = int.MaxValue;
                            dropItem.speed = 8;
                        }
                    }
                }
                //id大于1000的是物品类直接发(钻石)
                if (_medicamentData.Id > 1000 || _medicamentData.Id == 3)
                {
                    if (LuaToCshapeManager.Instance.GameDropGoodsData.ContainsKey(_medicamentData.Id))
                    {
                        if (_medicamentData.Id == 3 && LuaToCshapeManager.Instance.GameDropGoodsData[_medicamentData.Id] >= 100)
                        {
                            return;
                        }
                        else
                        {
                            LuaToCshapeManager.Instance.GameDropGoodsData[_medicamentData.Id]++;
                        }
                    }
                    else
                    {
                        LuaToCshapeManager.Instance.GameDropGoodsData[_medicamentData.Id] = 1;
                    }
                    if (_medicamentData.Id != 3)
                    {

                        string color = "FFFFFF";
                        if (_medicamentData.Quality == 1)
                        {
                            color = "00C75B";
                        }
                        else if (_medicamentData.Quality == 2)
                        {
                            color = "00ABC7";
                        }
                        else if (_medicamentData.Quality == 3)
                        {
                            color = "D200FF";
                        }
                        else if (_medicamentData.Quality == 4)
                        {
                            color = "C77800";
                        }
                        else if (_medicamentData.Quality == 5)
                        {
                            color = "FF7E00";
                        }
                        else if (_medicamentData.Quality == 6)
                        {
                            color = "FF3434";
                        }
                        string result = $"获得了<color=#{color}>{_medicamentData.GoodsName}</color>";
                        GameManager.instance.playerHud.ShowAutoUpgradeView(result);
                    }
                    else
                    {
                        LuaToCshapeManager.Instance.totalDiamond++;
                        GameManager.instance.playerHud.UpdateDiamonds();
                    }
                }
                else if (_medicamentData.SubType == 14)//银币
                {
                    if (isCaculate)
                    {
                        int num = 1;
                        int baseCount = num + LuaToCshapeManager.Instance.GetSkillAttributeCount(Globals.UpgradeSkillAttibute.金币加成);
                        float basePercent = 1f + LuaToCshapeManager.Instance.GetSkillAttributePercent(Globals.UpgradeSkillAttibute.金币加成) / 10000f;
                        num = (int)(baseCount * basePercent);
                        //Debug.Log("baseCount=" + baseCount + "   basePercent=" + basePercent);
                        LuaToCshapeManager.Instance.totalYinBi += num;
                        LuaManager.Instance.RunLuaFunction<int>("BattleManager.UpdateYinBi", LuaToCshapeManager.Instance.totalYinBi);
                        GameManager.instance.playerHud.UpdateYinBi();
                    }

                }

            }
            else
            {
                GameManager.instance.playerHud.AddKillToComboManager();
            }


            if(_numIndex <= 1) AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.coinCollected,0.7f);
            isCollected = true;
        }

        RemoveCoin();
    }

    public bool IsCollected()
    {
        return isCollected;
    }

    //是否移动完成
    public bool IsMoved()
    {
        return isMoved;
    }


    //是否参与计算
    public bool IsCaculate()
    {
        return isCaculate;
    }
    public void SetIsCaculate(bool isAddCaculate)
    {
        isCaculate = isAddCaculate;
    }

    public void RemoveCoin()
    {
        if (!_isOrb)
        {
            scheduleUpdate = false;
            //coin->stopAllActions();
            //coin.DOKill();
            if (coin.gameObject.activeInHierarchy)
            {
                SkeletonAnimation skeletonAnimation = coin.GetComponent<SkeletonAnimation>();
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    if (skeletonAnimation != null && _medicamentData.SubType == 8)
                    {
                        skeletonAnimation.state.SetAnimation(0, "coinCollect", false);
                    }
                }
            }

        }
        else
        {
            //coin->setOpacity(1);
            //coin->removeAllChildrenWithCleanup(true);
            multiplier.gameObject.SetActive(false);
            scheduleUpdate = false;
            comboAnimation.SetActive(true);
            
            //Sprite* pAnimation = Sprite::createWithSpriteFrameName("comboPointAnimation1.png");
            //coin->addChild(pAnimation, 2);
            //pAnimation->setCameraMask(GAMECAMERA);
            //pAnimation->runAction(Sequence::create(Shared::createAnimation("comboPointAnimation%d.png", 1, 13, false), RemoveSelf::create(), NULL));
            //pAnimation->setScale(3.0f);
        }
        //if (_medicamentData.SubType == 8 )
        //{

            DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() =>
            {
                _isOrb = false;
                GameSharedData.Instance.RemoveCoinsFromList(this);
                gameObject.SetActive(false);
                isCollected = false;
                //Destroy(gameObject);
            });
        //}
        //else
        //{
        //    _isOrb = false;
        //    GameSharedData.Instance.RemoveCoinsFromList(this);
        //    gameObject.SetActive(false);
        //    isCollected = false;
        //    //Destroy(gameObject);
        //}

        //this->runAction(Sequence::create(DelayTime::create(0.5), RemoveSelf::create(), NULL));

        //coinsArray.eraseObject(this);
    }


    public void SetCollectData(int goodID, int soundID, Medicament.Item item, PlayerController player, int totalNum = 1,int numIndex = 0)
    {
        _goodID = goodID;
        _soundID = soundID;
        _medicamentData = item;
        _player = player;
        //技能卷轴需要有特殊移动
        _needSpecialMove = false;// _medicamentData.SubType == 13;
        _reflectDirction = new Vector3(Random.Range(-100, 100), Random.Range(-100, 100), 0);
        _totalNum = totalNum;
        _numIndex = numIndex;
    }

    public void ReflectMove()
    {
        if (isCollected)
        {
            return;
        }
        //reflectBulletDuration -= Time.deltaTime;
        //if (reflectBulletDuration <= 0f)
        //{
        //    ResetBullet();
        //}
        //Vector3 dis = _player.transform.position - transform.position;
        //屏幕边缘转向
        float[] border = Globals.GetScreenBorder();
        _topBorder = border[0];
        _downBorder = border[1];
        _leftBorder = border[2];
        _rightBorder = border[3];
        if (transform.position.y >= _topBorder || transform.position.y <= _downBorder || transform.position.x >= _rightBorder || transform.position.x <= _leftBorder)
        {
            DoReflection();
        }
        transform.Translate(_reflectDirction.normalized * Time.deltaTime * 10, Space.World);
        //raflectTrail.SetActive(true);
    }
    public void DoReflection()
    {
        _reflectDirction = _player.transform.position - transform.position;


        Vector3 i = _reflectDirction.normalized;
        Vector3 n = -_reflectDirction.normalized;

        i.x = UnityEngine.Random.Range(-10f, 10f);
        i.y = UnityEngine.Random.Range(-10f, 10f);

        float a = Vector3.Dot(i, n);
        float h = 0.5f;
        float b = 1.0f - h * h * (1.0f - a * a);
        Vector3 t = h * i - (h * a + Mathf.Sqrt(b)) * n;
        if (b > 0)
        {
            _reflectDirction = t;
        }
        else
        {
            _reflectDirction = Vector3.zero;
        }
        transform.right = _reflectDirction.normalized;
    }
}
