using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Video;
using UnityEngine.SceneManagement;
public class VideoPlayerSceneManager : MonoBehaviour
{
    public static int videoIndex = -1;

    [SerializeField] private VideoPlayer videoPlayer;
    [SerializeField] private GameObject blackScreen;
    [SerializeField] private VideoClip[] videoClips;
    [SerializeField] private RectTransform VideoBG_TRF;

    void Awake()
    {
        AudioManager.instance.StopMusic();
        Debug.Log("VideoPlayerSceneManager awake");
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            if (Globals.introComplete)
            {
                videoPlayer.clip = videoClips[1];
                 Debug.Log("设置1");
            }
            else
            {
                Debug.Log("设置0");
                videoPlayer.clip = videoClips[0];
            }
            Debug.Log(videoPlayer.clip == null);
        }
        else
        {
            Debug.Log("设置玩-1clip,判断是否为空");
            videoPlayer.clip = videoClips[videoIndex];
        }
        Debug.Log("设置玩背景大小");
        VideoBG_TRF.sizeDelta = new Vector2(Screen.width, Screen.height);




        videoPlayer.sendFrameReadyEvents = true;
        videoPlayer.loopPointReached += OnVideoComplete;
        //videoPlayer.frameReady += OnFirstFrameReady;
        videoPlayer.Play();
        blackScreen.SetActive(false);
        Debug.Log("运行了播放");
    }

    private void OnFirstFrameReady(VideoPlayer vp, long id)
    {
        blackScreen.SetActive(false);
        videoPlayer.sendFrameReadyEvents = false;
    }

    private void OnVideoComplete(VideoPlayer vp)
    {
        //blackScreen.SetActive(true);
        videoPlayer.loopPointReached -= OnVideoComplete;
        EndVideo();
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            EndVideo();
        }
    }

    public void SkipVideo()
    {
        EndVideo();
    }

    private void EndVideo()
    {
        Debug.Log("播放结束了");
        AudioManager.instance.StopMusic();
        if (!Globals.introComplete)
        {
            Debug.Log("去战斗场景");
            SceneManager.LoadScene("GameScene");
        }
        else
        {
            Debug.Log("去登陆场景");
            SceneManager.LoadScene("GameStart");
        }
    }
}
