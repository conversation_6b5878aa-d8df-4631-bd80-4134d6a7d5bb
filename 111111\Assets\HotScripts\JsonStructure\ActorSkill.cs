﻿using X.PB;

namespace JsonStructure
{
    /// <summary>
    /// 角色属性对某个技能的提升
    /// </summary>
    public class ActorSkill : CatSkillEffect.Item
    {
        /// <summary>
        /// 技能ID(1级)
        /// </summary>
        public int SkillID { get; set; }
        
        /// <summary>
        /// Buff的概率提升(同时提升所有可出Buff的概率)
        /// </summary>
        public int BuffPriority { get; set; }
    }
}
