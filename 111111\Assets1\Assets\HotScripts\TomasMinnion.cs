using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;

public enum MinionType
{
    TURBO,
    SPARKY
};

public class TomasMinnion : Enemy
{
    private const float ENEMYSCALE = 0.18f;

    [SerializeField] private BoundingBoxFollower boundingBox;

    [SerializeField] GameObject boost1 = null, boost2 = null;
    [SerializeField] GameObject jetPackLayer = null;
    private Bounds bounds;
    public GameObject nodeToFollow;
    [SerializeField] Sprite bulletImage;
    private Bone bone;
    MinionType type;

    private Vector2 positionToFollow;

    public float angleOffset = 90;
    private int soundCounter = 0;
    public static int enemyDeadCount = 0;

    private bool isFlipped = false;
    private bool allowMovement = true;
    private bool isDead = false;
    public bool isCheckCollision = false;
    Sequence seq;
    Sequence healthBarSeq;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowRelocate = false;
        scheduleUpdate = true;
        _allowKillPoint = false;
        InitStats();
        InitializeEnemyParameters();
    }

    private void InitializeEnemyParameters()
    {
        if (nodeToFollow)
        {
            //enemySprite->setPosition(cocos2d::Point(_nodeToFollow->getPosition().x + 1400, _nodeToFollow->getPosition().y + 50));
            transform.position = new Vector2(nodeToFollow.transform.position.x + Globals.CocosToUnity(1400), nodeToFollow.transform.position.y + Globals.CocosToUnity(50));

        }
        else
        {
            transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1400), player.transform.position.y + Globals.CocosToUnity(50));
            //enemySprite->setPosition(cocos2d::Point(Player::getInstance()->getPosition().x + 1400, Player::getInstance()->getPosition().y + 50));
        }

        enemySprite.state.Event += HandleSpineEvent;
        enemySprite.state.Data.SetMix("idle", "shoot", 0.2f);
        enemySprite.state.Data.SetMix("shoot", "idle", 0.2f);
        enemySprite.state.SetAnimation(0, "idle", true);
        //ounds = boundingBox.bounds;
        HandleBoost();
    }

    private void HandleSpineEvent(TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name == "shoot")
        {
            Shoot();
            //call shoot func here
        }
        if (spineEvent.Data.Name == "idleStart")
        {
            allowMovement = true;
        }
    }

    private void HandleBoost()
    {
        //boost1
        //boost1.transform.position = new Vector2(enemySprite.skeleton.FindBone("jetpack1").x, enemySprite.skeleton.FindBone("jetpack1").y);
        //boost1.transform.SetRotation(-90);
        //boost2
        //boost2.transform.position = new Vector2(enemySprite.skeleton.FindBone("jetpack2").x , enemySprite.skeleton.FindBone("jetpack2").y);
        //boost2.transform.SetRotation(-90);
        jetPackLayer.SetActive(true);
    }

    public void CycleShoot()
    {
        seq = DOTween.Sequence().AppendInterval(2.5f).AppendCallback(() =>
        {
            allowMovement = false;
            enemySprite.state.SetAnimation(0, "shoot", false);
            enemySprite.state.AddAnimation(0, "shoot", true);
            enemySprite.state.AddAnimation(0, "shoot", true);
            enemySprite.state.AddAnimation(0, "idle", true);
            enemySprite.state.AddAnimation(0, "idle", true);
        }).SetLoops(-1,LoopType.Yoyo);
    }

    public void SetMinionType(MinionType minionType)
    {
        type = minionType;
        switch (type)
        {
            case MinionType.TURBO:
                enemySprite.skeleton.SetSkin("turbo");
                break;
            case MinionType.SPARKY:
                enemySprite.skeleton.SetSkin("sparky");
                break;

            default:
                break;
        }
    }

    public void SetAttributeByLord(double damage, double health)
    {
        stats.bulletDamage = damage;
        stats.health = stats.maxHealth.Value = health;
    }

    private void InitStats()
    {
        stats = new Attributes();
        stats.speed = 2;

        stats.health = 5000;
        stats.turnSpeed = 2;
        stats.bulletDamage = 20;
        stats.regen = 0;
        stats.xp = 50; ;
        stats.coinAwarded = 10;
        stats.missileDamage = 4;
        stats.maxHealth.Value = stats.health;
    }

    private void Shoot()
    {
        Bullet bullet = null;
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.setDamage(stats.bulletDamage);
        bullet.SetSpriteFrame(bulletImage);
        bullet.setRadiusEffectSquared(1);
        bone = enemySprite.skeleton.FindBone("fire");
        bullet.transform.position = bone.GetWorldPosition(enemySprite.transform);//new Vector2(enemySprite.transform.position.x + bone.WorldX, enemySprite.transform.position.y + bone.WorldY);
        bullet.duration = 4.0f;
        bullet.transform.SetRotation(-bone.WorldRotationX + 90);
        Vector2 dest = new Vector2(Globals.CocosToUnity(4000) * Mathf.Sin(Mathf.Deg2Rad * bullet.transform.GetRotation()), Globals.CocosToUnity(4000) * Mathf.Cos(Mathf.Deg2Rad * bullet.transform.GetRotation()));
        bullet.transform.localScale = Vector3.one * 2f;
        bullet.PlayBulletAnim(bullet.duration, dest);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);

        if (soundCounter % 2 == 0)
        {
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.spikyShoot);
            //Shared::playSound("res/Sounds/Bosses/Boss1/spikyShoot.mp3");
        }

        soundCounter++;
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (isCheckCollision && !isDead)
        {
            if (Vector2.Distance(P1, transform.position) < Globals.CocosToUnity(1600))
            {
                return boundingBox.CurrentCollider.bounds.Contains(P1);
            }
        }
        return false;
    }

    public override bool TakeHit(double damage)
    {
        if (!isDead)
        {
            if (healthBar)
            {
                stats.health -= damage * 6; // REMOVE TODO
                healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
                healthBar.gameObject.SetActive(true);
                healthBarSeq = DOTween.Sequence().AppendInterval(3).AppendCallback(() =>
                {
                    if (healthBar)
                    {
                        healthBar.gameObject.SetActive(false);
                    }
                });
                enemySprite.GetComponent<Renderer>().material.DOKill();
                enemySprite.GetComponent<Renderer>().material.color = Color.red;
                enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
            }
        }

        if (stats.health < 0)
        {
            healthBarSeq.Kill();
            return true;
        }

        return false;
    }
    // Update is called once per frame
    void Update()
    {
        
            //if (healthBar)
            //{
            //    //healthBar->setPosition(enemySprite->getPosition().x, enemySprite->getPosition().y - 100);
            //}

            //spSkeletonBounds_update(bounds, enemySprite->getSkeleton(), true);

            if (nodeToFollow)
            {
                transform.SetWorldPositionX(transform.GetWorldPositionX() + ((nodeToFollow.transform.GetWorldPositionX() + Globals.CocosToUnity(50) - Globals.CocosToUnity(150) * Mathf.Sin(Mathf.Deg2Rad * (nodeToFollow.transform.GetRotation() - angleOffset))) - transform.GetWorldPositionX()) * Time.deltaTime * 2.2f);
                transform.SetWorldPositionY(transform.GetWorldPositionY() + ((nodeToFollow.transform.GetWorldPositionY() - Globals.CocosToUnity(10) - Globals.CocosToUnity(150) * Mathf.Cos(Mathf.Deg2Rad * (nodeToFollow.transform.GetRotation() - angleOffset))) - transform.GetWorldPositionY()) * Time.deltaTime * 2.2f);
            }

            if (transform.GetWorldPositionX() > player.transform.position.x)
            {
                if (isFlipped)
                {
                    enemySprite.skeleton.FindBone("root").ScaleX = 1.0f;
                    jetPackLayer.transform.SetScaleX(1);
                    isFlipped = false;
                }
            }
            else
            {
                if (!isFlipped)
                {
                    enemySprite.skeleton.FindBone("root").ScaleX = -1.0f;
                    jetPackLayer.transform.SetScaleX(-1);
                    isFlipped = true;
                }
            }

           Positioning();
    }
	
    private void Positioning()
    {
        if (type == MinionType.TURBO)
        {
            positionToFollow = new Vector2(player.transform.position.x - 10, player.transform.position.y);         //cocos2d::Point(LEFTBOUNDARY, Player::getInstance()->getPosition().y);
        }
        else if (type == MinionType.SPARKY)
        {
            positionToFollow = new Vector2(player.transform.position.x + 10, player.transform.position.y);                                                                             //cocos2d::Point(RIGHTBOUNDARY, Player::getInstance()->getPosition().y);
        }
        if (!nodeToFollow)
        {
            if (positionToFollow != Vector2.zero)
            {
                transform.SetWorldPositionX(transform.GetWorldPositionX() + (positionToFollow.x - transform.GetWorldPositionX()) * Time.deltaTime * 1);

                if (allowMovement)
                {
                    transform.SetWorldPositionY(transform.GetWorldPositionY() + (player.transform.position.y - transform.GetWorldPositionY()) * Time.deltaTime * 2.5f);

                    if (transform.position.y < player.transform.position.y + Globals.CocosToUnity(100))
                    {
                        jetPackLayer.SetActive(true);
                    }
                    else
                    {
                        jetPackLayer.SetActive(false);
                    }
                }
            }
        }
    }

    public override void Destroy()
    {
        if (type == MinionType.TURBO)
        {
            Observer.DispatchCustomEvent("TURBO_DEAD");
            //Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("TURBO_DEAD");
        }
        else if (type == MinionType.SPARKY)
        {
            Observer.DispatchCustomEvent("SPARKY_DEAD");
            //Director::getInstance()->getEventDispatcher()->dispatchCustomEvent("SPARKY_DEAD");
        }

        isDead = true;
        
        seq.Kill();
        ////std::this_thread::sleep_for(std::chrono::milliseconds(15));

        ////if (AdditionalOnDestroy)
        ////{
        ////    log("Additional On Destroy called");
        ////    AdditionalOnDestroy();
        ////}

        if (_isFiller)
        {
            Globals.numberOfEnemies--;
        }
        //try
        //{
        //    if (this->getReferenceCount() != 2)
        //    {
        //        if (this->getReferenceCount() == 3)
        //        {


        //        }
        //        else
        //        {
        //            CCASSERT(0, "ref count must = to 2");
        //        }
        //    }

        //    if (this->getReferenceCount() == 1)
        //    {
        //        GameSharedData::getInstance()->g_enemyArray.eraseObject(this);
        //        return;
        //    }
        //    GameSharedData::getInstance()->g_enemyArray.eraseObject(this);
        //    if (this->getReferenceCount() == 0)
        //    {
        //        return;
        //    }
        //    this->removeFromParentAndCleanup(true);


        //    if (this->getReferenceCount() != 0)
        //    {
        //    }
        //    else
        //    {
        //    }

        //}
        //catch (const std::exception&e){

        //}

        //}
        base.Destroy();
    }

    public void SetNodeToFollow(GameObject nodeTofollow)
    {
        nodeToFollow = nodeTofollow;
    }

    public void CreateHealthbars()
    {
        //healthBar.transform.SetWorldPositionY(-Globals.CocosToUnity(10));
        healthBar.gameObject.SetActive(false);
    }
}
 