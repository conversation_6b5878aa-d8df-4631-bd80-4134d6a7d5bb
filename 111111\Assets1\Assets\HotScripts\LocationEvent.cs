using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LocationEvent : MonoBehaviour
{

    public float DistanceToActivate { get { return distance; } set { distance = value; } }

    private float distance;
    private System.Action mainFunc;
    private GameObject mainObject;
    private List<string> eventList = new List<string>();
    private PlayerPing ping;
    private bool scheduleUpdate = false;

    private void Init(Vector2 position, bool allowLocationPointer)
    {
        DistanceToActivate = Globals.CocosToUnity(500);

        transform.position= position;
        if (allowLocationPointer)
        {
            //PlayerPing* p = PlayerPing::createWithPingNode(mainNode);
            //this->addChild(p);
        }

        scheduleUpdate = true;
    }

    public static LocationEvent Create(Vector2 position, bool allowLocationPointer, System.Action func)
    {
        LocationEvent le = new GameObject().AddComponent<LocationEvent>();
        le.mainFunc = func;
        le.Init(position, allowLocationPointer);
        return le;
    }


    public void AddEvent(string eventName)
    {
        eventList.Add(eventName);
    }

    private void Update()
    {
        if (!scheduleUpdate)
            return;

        if (  Vector2.Distance(GameManager.instance.player.transform.position,transform.position)< distance)
        {

            foreach (string str in eventList)
            {
                Observer.DispatchCustomEvent(str);
            }
            mainFunc();
            scheduleUpdate = false;
            Destroy(gameObject);

        }
    }

}
