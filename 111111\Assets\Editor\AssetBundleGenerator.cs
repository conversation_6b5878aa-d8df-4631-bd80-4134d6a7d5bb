// 热更包打包工具
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;

using UnityEditor;

using UnityEngine;

struct BatchABInfo
{
    public string name;
    public string f;
    public BuildAssetBundleOptions bbo;

    public BatchABInfo(string strName, string strF, BuildAssetBundleOptions b)
    {
        name = strName;
        f = strF;
        bbo = b;
    }
}

public class AssetBundleGenerator
{
    //[MenuItem("Zen/texture compression/ASTC4x4-Android")]
    public static void TextureCompressASTC()
    {
        string[] fs = AssetDatabase.FindAssets("t:texture2D", null);
        foreach (var s in fs)
        {
            string f = AssetDatabase.GUIDToAssetPath(s);
            TextureImporter textureImporter = AssetImporter.GetAtPath(f) as TextureImporter;
            if (textureImporter == null) continue;
            textureImporter.wrapMode = TextureWrapMode.Repeat;
            textureImporter.filterMode = FilterMode.Bilinear;
            TextureImporterPlatformSettings ios = textureImporter.GetPlatformTextureSettings("Android");
            ios.overridden = true;
            ios.maxTextureSize = 1024;
            if (textureImporter.DoesSourceTextureHaveAlpha())
            {
                ios.format = TextureImporterFormat.ASTC_4x4;
            }
            else
            {
                ios.format = TextureImporterFormat.ASTC_4x4;
            }

            ios.compressionQuality = (int)TextureCompressionQuality.Best;
            textureImporter.SetPlatformTextureSettings(ios);
            AssetDatabase.ImportAsset(f, ImportAssetOptions.ForceUpdate);
        }
        AssetDatabase.Refresh();
        UnityEngine.Debug.Log("done!");
    }
    //[MenuItem("Zen/dds->png")]
    public static void DDS2PNG()
    {
        string[] fs = AssetDatabase.FindAssets("t:texture2D", null);
        foreach (var s in fs)
        {
            string f = AssetDatabase.GUIDToAssetPath(s);
            if (!f.Contains(".dds"))
            {
                continue;
            }
            File.Copy(f, "Assets/Temp/Bat/a.dds");
            string nf = Path.ChangeExtension(f, ".tga");
            AssetDatabase.MoveAsset(f, nf);
            AssetDatabase.Refresh();
            System.Diagnostics.Process foo = new System.Diagnostics.Process();
            foo.StartInfo.FileName = Application.dataPath + "/Temp/Bat/dds.bat";
            foo.StartInfo.Arguments = Application.dataPath + nf.Substring(6, nf.Length - 6);
            foo.StartInfo.WorkingDirectory = Application.dataPath + "/Temp/Bat";
            foo.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
            if (!foo.Start())
            {
                UnityEngine.Debug.LogError("failed to run bat");
            }
            AssetDatabase.Refresh();
        }
        AssetDatabase.Refresh();
        //ReImportTGA();
        UnityEngine.Debug.Log("done!");
    }
    public static void ReImportTGA()
    {
        string[] fs = AssetDatabase.FindAssets("t:texture2D", null);
        foreach (var s in fs)
        {
            string f = AssetDatabase.GUIDToAssetPath(s);
            if (!f.Contains(".tga"))
            {
                continue;
            }
            AssetDatabase.ImportAsset(f, ImportAssetOptions.ForceUpdate);
            //TextureImporter textureImporter = AssetImporter.GetAtPath(f) as TextureImporter;
            //textureImporter.textureShape = TextureImporterShape.Texture2D;
        }
        AssetDatabase.Refresh();
        UnityEngine.Debug.Log("done!");
    }
    enum ResourceType
    {
        Atlas,
        Sound,
        Font
    }
    static Dictionary<string, string> ab2path_ = new Dictionary<string, string>();
    static string zipExt = ".zip";
    private static void ab(string name, string f, string subfolder, BuildAssetBundleOptions bbo = BuildAssetBundleOptions.None)
    {

        // Create the array of bundle build details.
        AssetBundleBuild[] buildMap = new AssetBundleBuild[1];
        buildMap[0].assetBundleName = name;
        // #if UNITY_IPHONE
        //         names[0] = f;
        //         string[] names = new string[2];
        //         var magicfile = "Assets/Temp/ilikeplayinggames/" + name + ".txt";
        //         if (!File.Exists(magicfile))
        //         {
        //             File.AppendAllText(magicfile, Guid.NewGuid().ToString());
        //             AssetDatabase.Refresh();
        //         }
        //         // names[1] = magicfile;
        // #else
        //         string[] names = new string[1];
        //         names[0] = f;
        // #endif
        string[] names = new string[1];
        names[0] = f;

        buildMap[0].assetNames = names;
        if (!Directory.Exists("Assets/StreamingAssets/" + subfolder))
        {
            Debug.Log("new folder = " + subfolder);
            AssetDatabase.CreateFolder("Assets/StreamingAssets", subfolder);
            AssetDatabase.Refresh();
        }

        AssetBundleManifest m = BuildPipeline.BuildAssetBundles("Assets/StreamingAssets/" + subfolder, buildMap,
            //BuildAssetBundleOptions.ChunkBasedCompression,//包体600M，加装快
            //BuildAssetBundleOptions.None,//包体400M，加装慢
            bbo,
#if UNITY_ANDROID
            BuildTarget.Android
#elif UNITY_IPHONE
            BuildTarget.iOS
#else
            BuildTarget.StandaloneWindows
#endif

            );
        if (m && m.GetAllAssetBundles().Length > 0)
        {
            if (ab2path_.ContainsKey(name.ToLower()))
            {
                Debug.LogWarning(name.ToLower() + " already exists");
            }
            else
            {
                ab2path_.Add(name.ToLower(), f);
            }


            SetFileVersion("Assets/StreamingAssets/" + name);
        }
        else
        {
            Debug.LogError(name + " failed");
        }
    }
    private static void abBatch(List<BatchABInfo> infoList, bool mix)
    {
        var tempCount = new Dictionary<BuildAssetBundleOptions, int>();
        foreach (var info in infoList)
        {
            if (tempCount.ContainsKey(info.bbo))
                tempCount[info.bbo]++;
            else
                tempCount.Add(info.bbo, 1);
        }

        foreach (var count in tempCount)
        {
            var buildMap = new AssetBundleBuild[count.Value];
            int nIndex = 0;
            foreach (var info in infoList)
            {
                if (info.bbo == count.Key)
                {
                    buildMap[nIndex].assetBundleName = info.name;
                    string[] names;
                    if (mix)
                    {
                        names = new string[2];
                        names[0] = info.f;
                        var magicfile = "Assets/Temp/ilikeplayinggames/" + info.name + ".txt";
                        if (!File.Exists(magicfile))
                        {
                            File.AppendAllText(magicfile, Guid.NewGuid().ToString());
                            AssetDatabase.Refresh();
                        }
                        names[1] = magicfile;
                    }
                    else
                    {
                        names = new string[1];
                        names[0] = info.f;
                    }
                    buildMap[nIndex].assetNames = names;
                    ++nIndex;
                }
            }

            string subfolder = "";
            if (!Directory.Exists("Assets/StreamingAssets/" + subfolder))
            {
                Debug.Log("new folder = " + subfolder);
                AssetDatabase.CreateFolder("Assets/StreamingAssets", subfolder);
                AssetDatabase.Refresh();
            }

            var m = BuildPipeline.BuildAssetBundles("Assets/StreamingAssets/" + subfolder, buildMap,
                //BuildAssetBundleOptions.ChunkBasedCompression,//包体600M，加装快
                //BuildAssetBundleOptions.None,//包体400M，加装慢
                count.Key,
#if UNITY_ANDROID
            BuildTarget.Android
#elif UNITY_IPHONE
            BuildTarget.iOS
#else
            BuildTarget.StandaloneWindows
#endif
            );

            if (m && m.GetAllAssetBundles().Length > 0)
            {
                foreach (var info in infoList)
                {
                    if (info.bbo == count.Key)
                    {
                        ab2path_.Add(info.name.ToLower(), info.f);

                        SetFileVersion("Assets/StreamingAssets/" + info.name);
                    }
                }
            }
            else
            {
                Debug.LogError("abBatch big failed");
            }
        }
    }
    private static string GetTempDirRelative()
    {
        return "Assets\\Temp";
    }
    private static string GetABPrefix()
    {
        return "";
    }

    private static void ui()
    {
        //InitVerXml();

        var infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetDirectories(Path.Combine(GetTempDirRelative(), "ui"));
        foreach (var d in dirs)
        {
            //Debug.Log(d);
            string name = "ui_" + Path.GetFileName(d);
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
            //ab(name, d, "", GetBboByName(name));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }

    private static void atlas()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetDirectories(Path.Combine(GetTempDirRelative(), "uiatlas"));
        foreach (var d in dirs)
        {
            string name = "atlas_" + Path.GetFileNameWithoutExtension(d);
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }

        dirs = Directory.GetFiles(Path.Combine(GetTempDirRelative(), "uiatlas"));
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "atlas_" + Path.GetFileNameWithoutExtension(d);
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }

    private static void model()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetDirectories("Assets/Temp/model/prefab");
        foreach (var d in dirs)
        {
            string name = "model_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        dirs = Directory.GetFiles("Assets/Temp/model/prefab");
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "model_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }
    private static void sound()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetFiles("Assets/Temp/sound");
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "sound_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }

    private static void pb()
    {
        //InitVerXml();
        string name = "pb";
        ab(name, Path.Combine(GetTempDirRelative(), "pb"), "", GetBboByName(name));
        //SaveVerXml();
    }

    private static void image()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetDirectories("Assets/Temp/image");
        foreach (var d in dirs)
        {
            string name = "image_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        dirs = Directory.GetFiles("Assets/Temp/image");
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "image_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }
    private static void txt()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetDirectories("Assets/Temp/txt");
        foreach (var d in dirs)
        {
            string name = "txt_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        dirs = Directory.GetFiles("Assets/Temp/txt");
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "txt_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }
    private static void AssemblyPatch()
    {
        string dllPath = Path.Combine(Application.streamingAssetsPath, "Assembly-CSharp.patch.bytes");
        string versionFilePath = Path.Combine(Application.streamingAssetsPath, GameGlobal.dllVersionFileName);
        string url = GameGlobal.cdnUrl + "Assembly-CSharp.patch.bytes";
        string versionUrl = GameGlobal.cdnUrl + GameGlobal.dllVersionFileName;
        if (!File.Exists(dllPath) || !File.Exists(versionFilePath))
        {
            JsonData jsonData1 = new()
            {
                ["md5"] = "0",
                ["dllVersionNumber"] = "1",
                ["dllUrl"] = url,
                ["dllVersionUrl"] = versionUrl
            };
            File.WriteAllText(versionFilePath, jsonData1.ToJson());
            return;
        }

        string currentMD5 = GetFileMd5(dllPath);
        int versionNumber = 1;
        string jsonString = File.ReadAllText(versionFilePath);
        JsonData jsonData = JsonMapper.ToObject(jsonString);
        string storedMD5 = jsonData["md5"].ToString();
        versionNumber = int.Parse(jsonData["dllVersionNumber"].ToString());
        if (currentMD5 != storedMD5)
        {
            versionNumber++;
            jsonData["md5"] = currentMD5;
            jsonData["dllVersionNumber"] = versionNumber.ToString();
            File.WriteAllText(versionFilePath, jsonData.ToJson());
        }

    }

    private static void lua()
    {
        //Debug.Log(Directory.GetCurrentDirectory());
        //InitVerXml();
        //RemoveLuaFiles();
        CopyLuaFiles();
        string name = "lua";
        ab(name, Path.Combine(GetTempDirRelative(), "GameLua_"), "", GetBboByName(name));
        //RemoveLuaFiles();
        //SaveVerXml();
    }

    private static void resources()
    {
        //InitVerXml();
        string name = "res";
        ab(name, Path.Combine(GetTempDirRelative(), "res"), "", GetBboByName(name));
        //SaveVerXml();
    }
    static void RemoveLuaFiles()
    {
        AssetDatabase.DeleteAsset(Path.Combine(GetTempDirRelative(), "GameLua_"));
        AssetDatabase.Refresh();
    }
    static void CopyLuaFiles()
    {
        if (!AssetDatabase.IsValidFolder(GetTempDirRelative() + "\\GameLua_"))
        {
            AssetDatabase.CreateFolder(GetTempDirRelative(), "GameLua_");
            AssetDatabase.Refresh();
        }

        StringBuilder sb = new StringBuilder();
        sb.Append("Changed luas\n");

        bool bCopyed = false;
        string[] fileEntries = Directory.GetFiles(Path.Combine(GetTempDirRelative(), "GameLua"));
        foreach (string fileName in fileEntries)
        {
            if (null != AssetDatabase.LoadMainAssetAtPath(fileName))
            {
                string dst = fileName.Replace(".lua", ".txt").Replace("GameLua", "GameLua_");
                if (File.Exists(dst))
                {
                    string srcMd5 = GetFileMd5(fileName);
                    string dstMd5 = GetFileMd5(dst);
                    if (srcMd5 == dstMd5)
                    {
                        // 跳过没变化的
                        continue;
                    }
                }
                File.Copy(fileName, dst, true);
                bCopyed = true;
                sb.Append(fileName);
                sb.Append("\n");
            }
        }
        if (bCopyed)
            AssetDatabase.Refresh();
        Debug.Log(sb.ToString());
    }
    private static void scenemodify(bool b)
    {
        fillsn();
        EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;
        foreach (var s in scenes)
        {
            if (!s.path.Contains("Scenes_data"))
            {
                continue;
            }
            string f = Path.GetFileNameWithoutExtension(s.path);
            if (sn.Contains(f.ToLower()))
            {
                continue;
            }
            s.enabled = b;
        }
        EditorBuildSettings.scenes = scenes;
    }
    static List<string> sn = new List<string>();
    static void fillsn()
    {
        sn.Clear();
        //有些场景不热更，先硬编码吧
        //sn.Add("cj_xinshoucun02");
        //sn.Add("cj_xinshoucun03");
    }
    private static void scene()
    {
        //scene 是最后一次写入，将大版本写入
        //InitVerXml();

        fillsn();

        var infoList = EditorBuildSettings.scenes.Where(s => s.path.Contains("Scenes_data"))
            .Where(s => !sn.Contains(Path.GetFileNameWithoutExtension(s.path).ToLower()))
            .Select(s =>
            {
                var name = $"scene_{Path.GetFileName(s.path)}";
                return new BatchABInfo(name, s.path, GetBboByName(name));
            })
            .ToList();

        abBatch(infoList, false);

        //SaveVerXml();
    }

    private static void fx()
    {
        //InitVerXml();

        List<BatchABInfo> infoList = new List<BatchABInfo>();
        string[] dirs = Directory.GetFiles("Assets/Temp/fx");
        foreach (var d in dirs)
        {
            if (d.EndsWith(".meta"))
            {
                continue;
            }
            string name = "fx_" + Path.GetFileName(d);
            //ab(name, d, "", GetBboByName(name));
            infoList.Add(new BatchABInfo(name, d, GetBboByName(name)));
        }
        abBatch(infoList, GameGlobal.needMix);

        //SaveVerXml();
    }
    //[MenuItem("Zen/AssetBundleGenerator")]
    static void ABGenerator()
    {
        InitVerXml();
        changedFile.Clear();
        ab2path_.Clear();
        pb();
        lua();
        resources();
        ui();
        atlas();
        image();
        txt();
        AssemblyPatch();
        model();
        sound();
        //fx();
        scene();

        SaveVerXml();
    }

    // 命令行调用单独打热更包
    static void ABGeneratorEx()
    {
        ABGenerator();

        string outPath = BuildClientWizard.GetArg("-output");
        if (outPath == null)
        {
            Debug.LogError("ABGeneratorEx outPath == null");
            return;
        }
        if (!Directory.Exists(outPath))
            Directory.CreateDirectory(outPath);
        string outABPath = outPath + "/HotBundle";
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        DateTime curTime = System.DateTime.Now;
        outABPath = outABPath + "/" + curTime.ToString("yyyy-MM-dd-HH-mm");
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        Debug.Log("outABPath = " + outABPath);
        // 清空HotBundle目录
        string[] filePaths = Directory.GetFiles(outABPath);
        foreach (string filePath in filePaths)
            File.Delete(filePath);

        if (verXml == null)
        {
            Debug.LogError("ABGeneratorEx verXml == null");
            return;
        }

        string assetsPath = Application.dataPath + "/StreamingAssets";
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        foreach (XmlNode node in childList)
        {
            XmlElement child = node as XmlElement;
            if (child != null)
            {
                string name = child.GetAttribute("name");
                if (name != null)
                {
                    string zip = child.GetAttribute("zip");
                    bool needZip = (zip != null && zip.Length > 0 && zip != "0");
                    if (needZip)
                    {
                        string srcPath = Path.Combine(assetsPath, name);
                        string tarPath = srcPath + zipExt;
                        bool hasNewZip = false;
                        if (changedFile.ContainsKey(name) || !File.Exists(tarPath))
                        {
                            if (File.Exists(tarPath))
                                File.Delete(tarPath);
                            ZipFile(srcPath, tarPath);
                            hasNewZip = true;
                        }
                        string oldMd5 = "";
                        if (child.HasAttribute("md5zip"))
                            oldMd5 = child.GetAttribute("md5zip");
                        if (hasNewZip || oldMd5.Length == 0)
                        {
                            string md5zip = GetFileMd5(tarPath);
                            child.SetAttribute("md5zip", md5zip);
                            if (File.Exists(tarPath))
                            {
                                string fileSize = new FileInfo(tarPath).Length.ToString();
                                child.SetAttribute("zipsize", fileSize);
                            }
                            Debug.Log(string.Format("{0} changed md5 from {1} to {2}", name, oldMd5, md5zip));
                        }
                    }
                    else
                    {
                        if (child.HasAttribute("md5zip"))
                        {
                            child.RemoveAttribute("md5zip");
                        }
                        if (child.HasAttribute("zipsize"))
                        {
                            child.RemoveAttribute("zipsize");
                        }
                    }
                }
            }
        }
        string xmlPath = Path.Combine(assetsPath, GameGlobal.versionFileName);
        verXml.Save(xmlPath);
        /*
        foreach (var name in changedFile)
        {
            string fileName = name.Key;
            string srcPath = Path.Combine(assetsPath, fileName);
            if (File.Exists(srcPath))
            {
                string tarPath = Path.Combine(outABPath, fileName);
                if (name.Value != "0")
                {
                    string zipFilePath = srcPath + zipExt;
                    if (File.Exists(zipFilePath))
                        File.Delete(zipFilePath);
                    ZipFile(srcPath, zipFilePath);
                    srcPath += zipExt;
                    tarPath += zipExt;
                }
                File.Copy(srcPath, tarPath);

                sb.Append(srcPath);
                sb.Append("   =>   ");
                sb.Append(tarPath);
                sb.Append("\n");
            }
        }
        sb.Append("Total copy changed " + changedFile.Count.ToString() + " files");
        */
        Debug.Log(sb.ToString());
        changedFile.Clear();

        File.Copy(Path.Combine(assetsPath, GameGlobal.versionFileName), Path.Combine(outABPath, GameGlobal.versionFileName));
        CopyOutFile(outABPath);

        CopyDirectory("Assets/Temp/GameLua", outABPath + "/GameLua/");
        CopyDirectory("Assets/Temp/scp", outABPath + "/scp/");
    }

    static void CopyDirectory(string srcDir, string dstDir)
    {
        if (!Directory.Exists(dstDir))
            Directory.CreateDirectory(dstDir);
        string[] files = Directory.GetFiles(srcDir);
        for (int i = 0; i < files.Length; i++)
        {
            if (Path.GetExtension(files[i]) == ".meta")
                continue;
            string fileName = Path.GetFileName(files[i]);
            File.Copy(files[i], dstDir + fileName);
        }
    }

    private static XmlDocument verXml;
    private static int xmlInitCount = 0;
    private static Dictionary<string, string> changedFile = new();
    private static Dictionary<string, BuildAssetBundleOptions> nameToBabo = new();
    private static string apkBuildVersion = "";
    private static string apkResouceDirectory = "None";//打包资源的文件夹
    private static void InitVerXml()
    {
        ++xmlInitCount;
        if (xmlInitCount > 1)
            return;
        Debug.Log("InitVerXml");
        verXml = new XmlDocument();
        string xmlPath = Path.Combine(Application.dataPath + "/StreamingAssets", GameGlobal.versionFileName);
        if (File.Exists(xmlPath))
        {
            verXml.Load(xmlPath);
            XmlElement r = (XmlElement)verXml.SelectSingleNode("root");
            r.SetAttribute("url", GameGlobal.cdnUrl);
            string url2 = BuildClientWizard.GetArg("-url2");
            if (url2 != null)
                r.SetAttribute("url2", url2);
            string url3 = BuildClientWizard.GetArg("-url3");
            if (url3 != null)
                r.SetAttribute("url3", url3);


            string bigVersion = r.GetAttribute("bigVer");
            if (!isHotBuild)
            {
                if (bigVersion == null)
                {
                    bigVersion = "1";
                }
                else
                {
                    int bigInt = int.Parse(bigVersion);
                    bigInt++;
                    bigVersion = bigInt.ToString();
                }
            }
            else
            {
                if (bigVersion == null)
                {
                    bigVersion = "1";
                }
            }
            string apkName;
            if (GameGlobal.subChannelStr.Length > 0)
            {
                apkName = "Explottens-" + GameGlobal.channelStr + GameGlobal.subChannelStr + "1." + bigVersion + ".1.apk";
            }
            else
            {
                apkName = "Explottens-" + GameGlobal.channelStr + "1." + bigVersion + ".1.apk";
            }
            apkBuildVersion = "1." + bigVersion + ".1";
            r.SetAttribute("bigVer", bigVersion);
            r.SetAttribute("apk", GameGlobal.cdnUrl + apkName);


            XmlElement root = verXml.DocumentElement;
            XmlNodeList childList = root.ChildNodes;
            foreach (XmlNode node in childList)
            {
                XmlElement child = node as XmlElement;
                if (child != null)
                {
                    string name = child.GetAttribute("name");
                    string bbo = child.GetAttribute("bbo");
                    if (name != null && bbo != null && bbo.Length > 0)
                    {
                        int nBBO = Convert.ToInt32(bbo);
                        if (nBBO == 0)
                            nameToBabo[name] = BuildAssetBundleOptions.None;
                        else if (nBBO == 1)
                            nameToBabo[name] = BuildAssetBundleOptions.ChunkBasedCompression;
                        else if (nBBO == 2)
                            nameToBabo[name] = BuildAssetBundleOptions.UncompressedAssetBundle;
                    }
                }
            }
        }
        else
        {
            XmlDeclaration xmldecl = verXml.CreateXmlDeclaration("1.0", "utf-8", "yes");
            XmlElement root = verXml.DocumentElement;
            verXml.InsertBefore(xmldecl, root);
            XmlElement r = verXml.CreateElement("root");
            r.SetAttribute("url", GameGlobal.cdnUrl);
            string url2 = BuildClientWizard.GetArg("-url2");
            if (url2 != null)
                r.SetAttribute("url2", url2);
            string url3 = BuildClientWizard.GetArg("-url3");
            if (url3 != null)
                r.SetAttribute("url3", url3);

            r.SetAttribute("bulletin", "");

            string bigVersion = "1";
            r.SetAttribute("bigVer", bigVersion);
            apkBuildVersion = "1." + bigVersion + ".1";
            if (GameGlobal.subChannelStr.Length > 0)
            {
                r.SetAttribute("apk", GameGlobal.cdnUrl + "Explottens-" + GameGlobal.channelStr + GameGlobal.subChannelStr + apkBuildVersion + ".apk");
            }
            else
            {
                r.SetAttribute("apk", GameGlobal.cdnUrl + "Explottens-" + GameGlobal.channelStr + apkBuildVersion + ".apk");
            }

            verXml.AppendChild(r);
        }
    }
    private static void SaveVerXml()
    {
        --xmlInitCount;
        if (xmlInitCount > 0)
            return;
        if (verXml == null)
        {
            Debug.LogError("SaveVerXml verXml == null");
            return;
        }
        Debug.Log("SaveVerXml");
        string xmlPath = Path.Combine(Application.dataPath + "/StreamingAssets", GameGlobal.versionFileName);
        verXml.Save(xmlPath);
    }
    private static BuildAssetBundleOptions GetBboByName(string name)
    {
        string n = Path.GetFileName(name).ToLower();
        if (nameToBabo.ContainsKey(n))
            return nameToBabo[n];
        return BuildAssetBundleOptions.None;
    }
    public static string GetFileMd5(string filePath)
    {
        if (filePath == null)
        {
            Debug.LogError("GetFileMd5 filePath == null");
            return "";
        }

        if (!File.Exists(filePath))
        {
            Debug.LogError("GetFileMd5 file not exists " + filePath);
            return "";
        }

        byte[] retVal = null;
        using (FileStream file = new FileStream(filePath, FileMode.Open))
        {
            if (file == null)
            {
                Debug.LogError("GetFileMd5 can not open file " + filePath);
                return "";
            }

            using (System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider())
            {
                retVal = md5.ComputeHash(file);
                file.Close();
            }
        }
        if (retVal == null)
        {
            Debug.LogError("GetFileMd5 retVal == null " + filePath);
            return "";
        }

        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        for (int i = 0; i < retVal.Length; i++)
        {
            sb.Append(retVal[i].ToString("x2"));
        }
        return sb.ToString().ToLower();
    }
    private static void SetFileVersion(string filePath)
    {
        if (verXml == null)
        {
            Debug.LogError("SetFileVersion verXml == null  " + filePath);
            return;
        }

        string e = Path.GetExtension(filePath);
        if (e.Equals(".manifest") || e.Equals(".meta") || e.Equals(".xml"))
            return;

        string n = Path.GetFileName(filePath);
        if (n.Equals("StreamingAssets"))
            return;

        n = n.ToLower();
        if (ab2path_.Count > 0 && !ab2path_.ContainsKey(n))
        {
            File.Delete(filePath);
            return;
        }

        int index = n.IndexOf('_');
        string t = n;
        if (index > 0)
        {
            t = n[..index];
        }

        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        XmlElement targetNode = null;
        foreach (XmlNode node in childList)
        {
            var child = node as XmlElement;
            if (child != null)
            {
                string name = child.GetAttribute("name");
                if (name != null && name == n)
                {
                    targetNode = child;
                    break;
                }
            }
        }

        string md5 = GetFileMd5(filePath);
        string fileSize = "0";
        if (File.Exists(filePath))
            fileSize = new FileInfo(filePath).Length.ToString();
        if (targetNode == null)
        {
            targetNode = verXml.CreateElement("file");
            targetNode.SetAttribute("pack", "1");
            targetNode.SetAttribute("bbo", "0");
            targetNode.SetAttribute("zip", "0");
            targetNode.SetAttribute("type", t);
            targetNode.SetAttribute("name", n);
            targetNode.SetAttribute("flag", "0");
            targetNode.SetAttribute("lday", "0");
            targetNode.SetAttribute("size", fileSize);
            targetNode.SetAttribute("md5", md5);
            targetNode.SetAttribute("ver", "1");
            root.AppendChild(targetNode);

            changedFile.Add(n, "0");
        }
        else
        {
            if (!targetNode.HasAttribute("zip"))
            {
                // 升级到新格式
                string packBak = (targetNode.HasAttribute("pack") ? targetNode.GetAttribute("pack") : "999999");
                string bboBak = (targetNode.HasAttribute("bbo") ? targetNode.GetAttribute("bbo") : "0");
                string zipBak = (targetNode.HasAttribute("zip") ? targetNode.GetAttribute("zip") : "0");
                string typeBak = (targetNode.HasAttribute("type") ? targetNode.GetAttribute("type") : "unknown");
                string nameBak = (targetNode.HasAttribute("name") ? targetNode.GetAttribute("name") : "unknown");
                string flagBak = (targetNode.HasAttribute("flag") ? targetNode.GetAttribute("flag") : "0");
                string ldayBak = (targetNode.HasAttribute("lday") ? targetNode.GetAttribute("lday") : "0");
                string sizeBak = (targetNode.HasAttribute("size") ? targetNode.GetAttribute("size") : "unknown");
                string md5Bak = (targetNode.HasAttribute("md5") ? targetNode.GetAttribute("md5") : "unknown");
                string verBak = (targetNode.HasAttribute("ver") ? targetNode.GetAttribute("ver") : "1");

                targetNode.RemoveAllAttributes();

                targetNode.SetAttribute("pack", packBak);
                targetNode.SetAttribute("bbo", bboBak);
                targetNode.SetAttribute("zip", zipBak);
                targetNode.SetAttribute("type", typeBak);
                targetNode.SetAttribute("name", nameBak);
                targetNode.SetAttribute("flag", flagBak);
                targetNode.SetAttribute("lday", ldayBak);
                targetNode.SetAttribute("size", sizeBak);
                targetNode.SetAttribute("md5", md5Bak);
                targetNode.SetAttribute("ver", verBak);
            }

            string curMd5 = targetNode.GetAttribute("md5");
            if (curMd5 != null && md5 == curMd5)
                return;
            targetNode.SetAttribute("md5", md5);
            string verStr = targetNode.GetAttribute("ver");
            int curVer = Convert.ToInt32(verStr);
            curVer++;
            targetNode.SetAttribute("ver", curVer.ToString());
            targetNode.SetAttribute("size", fileSize);

            changedFile.Add(n, targetNode.GetAttribute("zip"));
        }
    }

    private static bool isHotBuild = false;
    public static bool isExportAndroidProject = false;

    [MenuItem("Zen/无sdk的空apk")]
    public static void OnClickBuildNoSDK()
    {
        GameGlobal.channelStr = GameLuaAPI.GetChannelString(GameLuaAPI.eChannel.eChannel_None);
        GameGlobal.cdnUrl = "http://************/deer/wangxingren/xiaoheiren/android/xibao1/";
        GameGlobal.curPackageName = "com.deer.xyz";
        apkResouceDirectory = "XHRNew-NotSdk";
        WriteChannelFile();
        OnClickBuild();
        BuildClientWizard.BulidAPK(apkBuildVersion);
    }

    [MenuItem("Zen/打安卓资源/YiMei/TapTap")]
    public static void BuildTAPTAP()
    {
        GameGlobal.channelStr = GameLuaAPI.GetChannelString(GameLuaAPI.eChannel.eChannel_YiMei);
        GameGlobal.cdnUrl = "http://************/deer/wangxingren/xiaoheiren/android/xibao1/";
        GameGlobal.subChannelStr = "TapTap";
        GameGlobal.curPackageName = "com.deer.xyzym";
        apkResouceDirectory = "XiBao2021-YiMei-"+ GameGlobal.subChannelStr;
        WriteChannelFile("0");
        OnClickBuild();
    }

    [MenuItem("Zen/打包资源-细胞-TapTap-12")]
    public static void BuildTAPTAP_12() {
        GameGlobal.channelStr = GameLuaAPI.GetChannelString(GameLuaAPI.eChannel.eChannel_YiMei);
        GameGlobal.cdnUrl = "http://************/deer/wangxingren/xiaoheiren/android/xibao1/";
        GameGlobal.subChannelStr = "TapTap";
        GameGlobal.curPackageName = "com.deer.xyzym";
        apkResouceDirectory = "xibao12-TapTap" + GameGlobal.subChannelStr;
        WriteChannelFile("0");
        OnClickBuild();
    }

    /// <summary>
    /// 是否打开打印日志以及指令按钮
    /// </summary>
    /// <param name="isOpenConsole"></param>
    public static void WriteChannelFile(string isOpenConsole = "1")
    {
        GameGlobal.versionFileName = "versionFileName-" + GameGlobal.channelStr + GameGlobal.subChannelStr;
        GameGlobal.dllVersionFileName = "dllVersion-" + GameGlobal.channelStr + GameGlobal.subChannelStr;
        string channelFilePath = Path.Combine(Application.streamingAssetsPath, GameGlobal.channelFileName);
        if (File.Exists(channelFilePath))
        {
            File.Delete(channelFilePath);
        }
        //渠道名；cdn；versionFileName;是否打开指令日志;子渠道
        JsonData jsonData = new()
        {
            ["channelStr"] = GameGlobal.channelStr,
            ["cdnUrl"] = GameGlobal.cdnUrl,
            ["versionFileName"] = GameGlobal.versionFileName,
            ["isOpenConsole"] = isOpenConsole,
            ["dllVersionFileName"] = GameGlobal.dllVersionFileName,
            ["curPackageName"] = GameGlobal.curPackageName
        };

        if (GameGlobal.subChannelStr.Length > 0)
        {
            //有子渠道的情况
            jsonData["subChannelStr"] = GameGlobal.subChannelStr;
        }
        File.WriteAllText(channelFilePath, jsonData.ToJson());
    }

    public static void OnClickBuild()
    {
        isHotBuild = false;
        isExportAndroidProject = false;
        EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;
        if (!scenes[0].path.Contains("GameStart"))
        {
            Debug.LogError("GameStart.unity的index必须是0");
            return;
        }
        ABGenerator();
        PreDealWithAssetBundle();

        scenedisenable();
        sceneenable();

        PostDealWithAssetBundle();
        //BuildClientWizard.BulidAPK(apkBuildVersion);
    }



    public static void OnClickHotBuild()
    {
        isHotBuild = true;
        EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;
        ABGenerator();
        PreDealWithAssetBundle();
        scenedisenable();
        sceneenable();
        PostDealWithAssetBundle();

    }

    public static void ABGeneratorMix()
    {
        ABGenerator();
        PreDealWithAssetBundleEx();
        PostDealWithAssetBundleEx();
    }

    public static void PreDealWithAssetBundleMix()
    {
        InitVerXml();
        PreDealWithAssetBundle();
    }

    public static void BuildAPKNoGenre()
    {
        scenedisenable();
        BuildClientWizard.BulidAPK("1.0");
        sceneenable();
    }

    public static void ExportAndroid()
    {
        ABGenerator();
        PreDealWithAssetBundle();

        scenedisenable();
        BuildClientWizard.ExportProject();
        sceneenable();

        PostDealWithAssetBundle();
    }

    public static void BuildTargetAB()
    {
        string subAB = BuildClientWizard.GetArg("-subab");
        if (subAB == null || subAB.Length == 0)
        {
            Debug.LogError("BuildTargetAB not found subab param");
            return;
        }

        string[] splitResult = subAB.Split('|');
        if (splitResult == null || splitResult.Length == 0)
        {
            Debug.LogError("BuildTargetAB Split Result empty");
            return;
        }

        InitVerXml();
        changedFile.Clear();

        ab2path_.Clear();
        foreach (string sub in splitResult)
        {
            if (sub == "pb")
            {
                pb();
            }
            else if (sub == "lua")
            {
                lua();
            }
            else if (sub == "res")
            {
                resources();
            }
            else if (sub == "ui")
            {
                //先分离
                ui();
            }
            else if (sub == "atlas")
            {
                atlas();
            }
            else if (sub == "image")
            {
                image();
            }
            else if (sub == "model")
            {
                model();
            }
            else if (sub == "sound")
            {
                sound();
            }
            else if (sub == "fx")
            {
                fx();
            }
            else if (sub == "scene")
            {
                scene();
            }
        }

        // version();
        SaveVerXml();

        PreDealWithAssetBundleEx();
        PostDealWithAssetBundleEx();
    }

    //[MenuItem("Zen/打包/scene enable")]
    public static void sceneenable()
    {
        scenemodify(true);
    }
    //[MenuItem("Zen/打包/scene disenable")]
    public static void scenedisenable()
    {
        scenemodify(false);
    }
    //[MenuItem("Zen/检测Lua格式UTF8BOM")]
    public static void RemoveUTF8BOM()
    {
        string path = "Temp/GameLua/";
        string[] fileEntries = Directory.GetFiles(Application.dataPath + "/" + path);
        string localPath = "Assets/" + path;
        foreach (string fileName in fileEntries)
        {
            string filePath = fileName.Replace("\\", "/");
            int index = filePath.LastIndexOf("/");
            filePath = localPath + filePath.Substring(index + 1);
            if (null != AssetDatabase.LoadMainAssetAtPath(filePath))
            {
                byte[] buffer = File.ReadAllBytes(filePath);
                byte[] bomBuffer = new byte[] { 0xef, 0xbb, 0xbf };
                if (buffer[0] == bomBuffer[0]
                    && buffer[1] == bomBuffer[1]
                    && buffer[2] == bomBuffer[2])
                {
                    byte[] dst = new byte[buffer.Length - 3];
                    Array.Copy(buffer, 3, dst, 0, buffer.Length - 3);
                    File.WriteAllBytes(filePath, dst);
                }
            }
        }
        Debug.Log("done!");
    }
    struct SMovedFileInfo
    {
        public string srcPath;
        public string tarPath;
        public DateTime cTime;
        public DateTime laTime;
        public DateTime wTime;
    }
    private static List<SMovedFileInfo> moveFileList = new List<SMovedFileInfo>();
    private static void MoveFileSaveInfo(string srcPath, string tarPath)
    {
        if (File.Exists(srcPath))
        {
            SMovedFileInfo moveInfo;
            moveInfo.srcPath = srcPath;
            moveInfo.tarPath = tarPath;
            moveInfo.cTime = File.GetCreationTime(srcPath);
            moveInfo.laTime = File.GetLastAccessTime(srcPath);
            moveInfo.wTime = File.GetLastWriteTime(srcPath);
            moveFileList.Add(moveInfo);

            File.Move(srcPath, tarPath);
        }
    }
    private static void ZipFile(string srcPath, string tarPath)
    {
        if (File.Exists(srcPath))
        {
            ZIPWrapper.CompressFileLZMA(srcPath, tarPath);
        }
        else
        {
            Debug.LogError(string.Format("ZipFile {0} not exists", srcPath));
        }
    }
    public static void PreDealWithAssetBundle()
    {
        Debug.Log("PreDealWithAssetBundle begin");
        if (verXml == null)
        {
            Debug.LogError("PreDealWithAssetBundle verXml == null");
            return;
        }

        string zipPath = "D://apk/zipCache"; //Application.temporaryCachePath + "/zipCache";
        zipPath = Application.dataPath.Replace("Assets", "apk/zipCache");
        if (!Directory.Exists(zipPath))
            Directory.CreateDirectory(zipPath);
        string tempPath = "D://apk/ABCache";//Application.temporaryCachePath + "/ABCache";
        tempPath = Application.dataPath.Replace("Assets", "apk/ABCache");
        if (!Directory.Exists(tempPath))
            Directory.CreateDirectory(tempPath);
        // 清空zipCache、ABCache目录
        string[] filePaths = Directory.GetFiles(zipPath);
        foreach (string filePath in filePaths)
            File.Delete(filePath);
        filePaths = Directory.GetFiles(tempPath);
        foreach (string filePath in filePaths)
            File.Delete(filePath);

        moveFileList.Clear();
        HashSet<string> stayZipFile = new HashSet<string>();
        string assetsPath = Application.dataPath + "/StreamingAssets";
        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        foreach (XmlNode node in childList)
        {
            XmlElement child = node as XmlElement;
            if (child != null)
            {
                string name = child.GetAttribute("name");
                if (name != null)
                {
                    string zip = child.GetAttribute("zip");
                    bool needZip = (zip != null && zip.Length > 0 && zip != "0");
                    string pack = child.GetAttribute("pack");
                    //0打到包里
                    bool needPack = (pack != null && pack.Length > 0 && pack != "0");
                    if (needZip)
                    {
                        string srcPath = Path.Combine(assetsPath, name);
                        string tarPath = srcPath + zipExt;
                        bool hasNewZip = false;
                        if (changedFile.ContainsKey(name) || !File.Exists(tarPath))
                        {
                            if (File.Exists(tarPath))
                                File.Delete(tarPath);
                            ZipFile(srcPath, tarPath);
                            hasNewZip = true;
                        }
                        if (hasNewZip || !child.HasAttribute("md5zip") || child.GetAttribute("md5zip").Length == 0)
                        {
                            string md5zip = GetFileMd5(tarPath);
                            child.SetAttribute("md5zip", md5zip);
                            if (File.Exists(tarPath))
                            {
                                string fileSize = new FileInfo(tarPath).Length.ToString();
                                child.SetAttribute("zipsize", fileSize);
                            }
                        }
                        if (!needPack)
                            stayZipFile.Add(name);
                    }
                    else
                    {
                        if (child.HasAttribute("md5zip"))
                        {
                            child.RemoveAttribute("md5zip");
                        }
                        if (child.HasAttribute("zipsize"))
                        {
                            child.RemoveAttribute("zipsize");
                        }
                    }

                    if (needZip || needPack)
                    {
                        string fileName = name;
                        MoveFileSaveInfo(Path.Combine(assetsPath, fileName), Path.Combine(tempPath, fileName));
                        fileName = name + ".manifest";
                        MoveFileSaveInfo(Path.Combine(assetsPath, fileName), Path.Combine(tempPath, fileName));
                        fileName = name + ".meta";
                        MoveFileSaveInfo(Path.Combine(assetsPath, fileName), Path.Combine(tempPath, fileName));
                        fileName = name + ".manifest.meta";
                        MoveFileSaveInfo(Path.Combine(assetsPath, fileName), Path.Combine(tempPath, fileName));
                    }
                }
            }
        }

        // 挪走多余zip
        filePaths = Directory.GetFiles(assetsPath);
        foreach (string filePath in filePaths)
        {
            if (Path.GetExtension(filePath) == zipExt)
            {
                if (!stayZipFile.Contains(Path.GetFileNameWithoutExtension(filePath)))
                {
                    MoveFileSaveInfo(filePath, Path.Combine(zipPath, Path.GetFileName(filePath)));
                }
            }
        }

        XmlElement r = (XmlElement)verXml.SelectSingleNode("root");
        int nXmlVer = 1;
        string strVer = r.GetAttribute("ver");
        if (strVer != null)
        {
            int.TryParse(strVer, out nXmlVer);
            if (nXmlVer <= 0)
                nXmlVer = 1;
            else
                ++nXmlVer;
        }
        r.SetAttribute("ver", isHotBuild ? nXmlVer.ToString() : "1");
        SaveVerXml();
        Debug.Log("PreDealWithAssetBundle end");
    }
    public static void PreDealWithAssetBundleEx()
    {
        Debug.Log("PreDealWithAssetBundleEx begin");
        if (verXml == null)
        {
            Debug.LogError("PreDealWithAssetBundleEx verXml == null");
            return;
        }

        string assetsPath = Application.dataPath + "/StreamingAssets";
        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        foreach (XmlNode node in childList)
        {
            XmlElement child = node as XmlElement;
            if (child != null)
            {
                string name = child.GetAttribute("name");
                if (name != null)
                {
                    string zip = child.GetAttribute("zip");
                    bool needZip = (zip != null && zip.Length > 0 && zip != "0");
                    //string pack = child.GetAttribute("pack");
                    //0打到包里
                    //bool needPack = (pack != null && pack.Length > 0 && pack != "0");
                    if (needZip)
                    {
                        string srcPath = Path.Combine(assetsPath, name);
                        string tarPath = srcPath + zipExt;
                        bool hasNewZip = false;
                        if (changedFile.ContainsKey(name) || !File.Exists(tarPath))
                        {
                            if (File.Exists(tarPath))
                                File.Delete(tarPath);
                            ZipFile(srcPath, tarPath);
                            hasNewZip = true;
                        }
                        if (hasNewZip || !child.HasAttribute("md5zip") || child.GetAttribute("md5zip").Length == 0)
                        {
                            string md5zip = GetFileMd5(tarPath);
                            child.SetAttribute("md5zip", md5zip);
                            if (File.Exists(tarPath))
                            {
                                string fileSize = new FileInfo(tarPath).Length.ToString();
                                child.SetAttribute("zipsize", fileSize);
                            }
                        }
                    }
                    else
                    {
                        if (child.HasAttribute("md5zip"))
                        {
                            child.RemoveAttribute("md5zip");
                        }
                        if (child.HasAttribute("zipsize"))
                        {
                            child.RemoveAttribute("zipsize");
                        }
                    }
                }
            }
        }

        XmlElement r = (XmlElement)verXml.SelectSingleNode("root");
        int nXmlVer = 1;
        string strVer = r.GetAttribute("ver");
        if (strVer != null)
        {
            int.TryParse(strVer, out nXmlVer);
            if (nXmlVer <= 0)
                nXmlVer = 1;
            else
                ++nXmlVer;
        }
        r.SetAttribute("ver", nXmlVer.ToString());
        SaveVerXml();
        Debug.Log("PreDealWithAssetBundleEx end");
    }
    public static void PostDealWithAssetBundle()
    {
        Debug.Log("PostDealWithAssetBundle begin");
        string outPath = BuildClientWizard.GetArg("-output");
        if (outPath == null)
        {
            //Debug.LogError("PostDealWithAssetBundle outPath == null");
            outPath = "D://apk/"+ apkResouceDirectory;
            outPath = Application.dataPath.Replace("Assets", "apk/") + apkResouceDirectory;
            // return;
        }
        if (!Directory.Exists(outPath))
            Directory.CreateDirectory(outPath);
        string outABPath;
        if (GameGlobal.subChannelStr.Length > 0)
        {
            outABPath = outPath + "/HotBundle/" + GameGlobal.channelStr + GameGlobal.subChannelStr;
        }
        else
        {
            outABPath = outPath + "/HotBundle/" + GameGlobal.channelStr;
        }
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        //string[] filePaths1 = Directory.GetFiles(outABPath);
        //foreach (string filePath in filePaths1)
        //{
        //    File.Delete(filePath);
        //}
        DateTime curTime = System.DateTime.Now;
        outABPath = outABPath + "/" + curTime.ToString("yyyy-MM-dd-HH-mm");
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        Debug.Log("outABPath = " + outABPath);
        // 清空HotBundle目录
        string[] filePaths = Directory.GetFiles(outABPath);
        foreach (string filePath in filePaths)
        {
            File.Delete(filePath);
        }

        if (verXml == null)
        {
            Debug.LogError("PostDealWithAssetBundle verXml == null");
            return;
        }

        //string tempPath = Application.temporaryCachePath + "/ABCache";
        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        string assetsPath = Application.dataPath + "/StreamingAssets";
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        foreach (var moveInfo in moveFileList)
        {
            if (File.Exists(moveInfo.tarPath))
            {
                File.Move(moveInfo.tarPath, moveInfo.srcPath);
                File.SetCreationTime(moveInfo.srcPath, moveInfo.cTime);
                File.SetLastAccessTime(moveInfo.srcPath, moveInfo.laTime);
                File.SetLastWriteTime(moveInfo.srcPath, moveInfo.wTime);

                sb.Append(moveInfo.tarPath);
                sb.Append("   =>   ");
                sb.Append(moveInfo.srcPath);
                sb.Append("\n");
            }
        }
        sb.Append("Total move packed " + moveFileList.Count.ToString() + " files");
        Debug.Log(sb.ToString());
        moveFileList.Clear();

        //sb.Clear();
        sb.Length = 0;
        foreach (var name in changedFile)
        {
            string fileName = name.Key;
            string srcPath = Path.Combine(assetsPath, fileName);
            if (File.Exists(srcPath))
            {
                string tarPath = Path.Combine(outABPath, fileName);
                if (name.Value != "0")
                {
                    srcPath += zipExt;
                    tarPath += zipExt;
                }
                File.Copy(srcPath, tarPath);

                sb.Append(srcPath);
                sb.Append("   =>   ");
                sb.Append(tarPath);
                sb.Append("\n");
            }
        }
        sb.Append("Total copy changed " + changedFile.Count.ToString() + " files");
        Debug.Log(sb.ToString());
        changedFile.Clear();
        //移动生成的dllversion 和 dll文件
        if (File.Exists(Path.Combine(assetsPath, GameGlobal.dllVersionFileName)))
        {
            File.Copy(Path.Combine(assetsPath, GameGlobal.dllVersionFileName), Path.Combine(outABPath, GameGlobal.dllVersionFileName));
        }
        if (File.Exists(Path.Combine(assetsPath, "Assembly-CSharp.patch.bytes")))
        {
            File.Copy(Path.Combine(assetsPath, "Assembly-CSharp.patch.bytes"), Path.Combine(outABPath, "Assembly-CSharp.patch.bytes"));
        }

        File.Copy(Path.Combine(assetsPath, GameGlobal.versionFileName), Path.Combine(outABPath, GameGlobal.versionFileName));
        CopyOutFile(outABPath);

        CopyDirectory("Assets/Temp/GameLua", outABPath + "/GameLua/");
        CopyDirectory("Assets/Temp/scp", outABPath + "/scp/");
        Debug.Log("PostDealWithAssetBundle end");
    }
    public static void PostDealWithAssetBundleEx()
    {
        Debug.Log("PostDealWithAssetBundleEx begin");
        string outPath = BuildClientWizard.GetArg("-output");
        if (outPath == null)
        {
            Debug.LogError("PostDealWithAssetBundleEx outPath == null");
            return;
        }
        if (!Directory.Exists(outPath))
            Directory.CreateDirectory(outPath);
        string outABPath = outPath + "/HotBundle";
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        DateTime curTime = System.DateTime.Now;
        outABPath = outABPath + "/" + curTime.ToString("yyyy-MM-dd-HH-mm");
        if (!Directory.Exists(outABPath))
            Directory.CreateDirectory(outABPath);
        Debug.Log("outABPath = " + outABPath);
        // 清空HotBundle目录
        string[] filePaths = Directory.GetFiles(outABPath);
        foreach (string filePath in filePaths)
            File.Delete(filePath);

        if (verXml == null)
        {
            Debug.LogError("PostDealWithAssetBundleEx verXml == null");
            return;
        }

        XmlElement root = verXml.DocumentElement;
        XmlNodeList childList = root.ChildNodes;
        string assetsPath = Application.dataPath + "/StreamingAssets";
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        foreach (var name in changedFile)
        {
            string fileName = name.Key;
            string srcPath = Path.Combine(assetsPath, fileName);
            if (File.Exists(srcPath))
            {
                string tarPath = Path.Combine(outABPath, fileName);
                if (name.Value != "0")
                {
                    srcPath += zipExt;
                    tarPath += zipExt;
                }
                File.Copy(srcPath, tarPath);

                sb.Append(srcPath);
                sb.Append("   =>   ");
                sb.Append(tarPath);
                sb.Append("\n");
            }
        }
        sb.Append("Total copy changed " + changedFile.Count.ToString() + " files");
        Debug.Log(sb.ToString());
        changedFile.Clear();

        File.Copy(Path.Combine(assetsPath, GameGlobal.versionFileName), Path.Combine(outABPath, GameGlobal.versionFileName));
        CopyOutFile(outABPath);

        CopyDirectory("Assets/Temp/GameLua", outABPath + "/GameLua/");
        CopyDirectory("Assets/Temp/scp", outABPath + "/scp/");
        Debug.Log("PostDealWithAssetBundleEx end");
    }

    static void CopyOutFile(string outABPath)
    {
        string assetsPath = Application.dataPath + "/StreamingAssets";
        string verOutputPath = outABPath;
        verOutputPath += "/out-version";
        if (!Directory.Exists(verOutputPath))
            Directory.CreateDirectory(verOutputPath);
        File.Copy(Path.Combine(assetsPath, GameGlobal.versionFileName), Path.Combine(verOutputPath, GameGlobal.versionFileName), true);

        string assetOutputPath = outABPath;
        assetOutputPath += "/out-StreamingAssets";
        if (!Directory.Exists(assetOutputPath))
            Directory.CreateDirectory(assetOutputPath);
        string[] files = Directory.GetFiles(assetsPath);
        //新增空底包功能（生成完后，清空streamingAssets）
        foreach (var e in files)
        {
            if (e.EndsWith(".xml") || e.EndsWith(".meta") || e.EndsWith(".manifest"))
            {
                // 这几种类型不处理
            }
            else
            {
                string fileName = Path.GetFileName(e);
                File.Copy(e, Path.Combine(assetOutputPath, fileName), true);
            }
            if (!e.Contains("versionFileName") && !e.Contains(GameGlobal.channelFileName) && !e.Contains(GameGlobal.dllVersionFileName) && !e.Contains("Assembly-CSharp"))
            {
                File.Delete(e);
            }
        }
        AssetDatabase.Refresh();
    }

    [MenuItem("工具/搜索ui未剥离字体")]
    public static void SearchUIInvalidFont()
    {
        BrowseAllFiles("Assets/Temp/ui");
        Debug.Log("搜索完毕");
    }

    private static string GetGameObjectPath(GameObject obj)
    {
        string path = "/" + obj.name;
        while (obj.transform.parent != null)
        {
            obj = obj.transform.parent.gameObject;
            path = "/" + obj.name + path;
        }
        return path;
    }

    private static void BrowseAllFiles(string path)
    {
        string[] dirs = Directory.GetDirectories(path);
        foreach (var d in dirs)
        {
            if (d != path)
                BrowseAllFiles(d);
        }

        string[] files = Directory.GetFiles(path);
        foreach (var e in files)
        {
            if (e.EndsWith(".prefab"))
            {
                CheckPrefabFile(e);
            }
        }
    }

    // 递归处理子节点
    public static void CheckChildPrefabFile(Transform go, StringBuilder sb)
    {
        if (!go)
            return;
        UnityEngine.UI.Text txt = go.GetComponent<UnityEngine.UI.Text>();
        if (txt)
        {
            if (txt.font != null)
                sb.Append(txt.name + "\n");
        }

        for (int i = 0; i < go.childCount; ++i)
        {
            CheckChildPrefabFile(go.GetChild(i), sb);
        }
    }

    private static bool CheckPrefabFile(string path)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
        if (!prefab)
        {
            Debug.LogWarning("CheckPrefabFile 找不到路径 " + path);
            return false;
        }

        StringBuilder sbErr = new StringBuilder();
        CheckChildPrefabFile(prefab.transform, sbErr);
        if (sbErr.Length > 0)
        {
            Debug.LogError("有字体未剥离! " + path);
            Debug.Log(sbErr.ToString());
        }

        return true;
    }
}