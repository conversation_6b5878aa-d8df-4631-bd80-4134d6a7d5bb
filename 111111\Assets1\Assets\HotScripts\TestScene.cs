using UnityEngine;
using System;
using System.Xml.Linq;
using System.Globalization;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
public class TestScene : MonoBehaviour
{
    public void LoadData()
    {
        PList mainValueMap = new PList("Test");

        PList pd = mainValueMap.GetValueOrDefault("PlayerData") as PList;

        for (int i = 0; i < pd.Count; i++)
        {
            Debug.Log(pd.ElementAt(i).Key);
            Debug.Log(pd.ElementAt(i).Value);
        }

        string name = pd["name"] as string;
        float num1 = (float)pd["num1"];
        int num2 = (int)pd["num2"];

        Debug.Log("String: " + name);
        Debug.Log("Float: " + num1);
        Debug.Log("Int: " + num2);

    }
}
