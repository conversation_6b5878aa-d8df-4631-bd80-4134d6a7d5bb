﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Extension;

using Cysharp.Threading.Tasks;
using HotScripts;
using TMPro;

using UnityEngine;

namespace CreatureSkills
{
    /// <summary>
    /// 激光扫描
    /// </summary>
    public class CreatureSkill_2 : CreatureSkillBase
    {
        ///// <summary>
        ///// 预制件:激光端点
        ///// </summary>
        //public GameObject LaserPrefab_Impact { get; set; }
        ///// <summary>
        ///// 预制件:激光线条(矩形)
        ///// </summary>
        //public GameObject LaserPrefab_Rect { get; set; }

        /// <summary>
        /// 发出的激光
        /// </summary>
        public List<BulletProps> Lasers { get; } = new();

        public override void ClearSkill()
        {
            base.ClearSkill();

            foreach (var laser in Lasers)
            {
                laser.Dispose();
            }
        }

        public override async UniTaskVoid DoSkill()
        {
            base.DoSkill().Forget();
            try
            {
                await UniTask.SwitchToMainThread();

                // 持续时长：秒
                var attackDuration = Skill.持续时长.Value;

                // 激光条数
                var laserQty = Skill.连射次数.Value;
                // 环绕角速度(或摆动)
                var surroundSpeed = Skill.环绕角速度.Value;

                // 单向摆动一次的时长：秒
                var surroundDuration = Skill.MinAttackDuration.Value;
                // 摆动次数(至少1次)
                var maxSwingTimes = Skill.最大摆动次数.Value;
                if (maxSwingTimes < 1) maxSwingTimes = 1;

                // 摆动一次绕过的角度
                var surroundAngle = surroundSpeed * surroundDuration;

                // 依次初始角度
                var presetAngles = Skill.CsvRow_CatSkill.ShootAngles;

                // 激光最大长度、宽度
                var maxLength = Skill.攻击距离.Value;
                var width = 2 * Skill.Get子弹半径();

                // 反弹次数
                var maxBounceTimes = Skill.子弹最大反弹次数.Value;

                // 造成伤害的最小间隔时长:秒
                var minDamageInterval = Skill.MinDamageInterval.Value;

                // 超时令牌
                var CTS_timeout = new CancellationTokenSource();
                CTS_timeout.CancelAfterSlim(System.TimeSpan.FromSeconds(attackDuration));

                // 当持续时长够了或玩家死了，自动结束。
                CancellationTokenSource cts_Shoot = CancellationTokenSource.CreateLinkedTokenSource(
                    CTS_timeout.Token,
                    Creature.GetCancellationTokenOnDestroy()
                    );
                CTS_Shoots.Add(cts_Shoot);

                // 基准方向
                var baseDir = Vector3.right;
                var enemy = GameSharedData.Instance.enemyList.Where(x => !x.isDestroyed)
                    .Select(x => new
                    {
                        Enemy = x,
                        Distance = x ? Creature.transform.position.CalcDistance2D_PointToSolidCircle(x.transform.position, x.FightProp.Radius)
                            : new DistancePointToSolidCircle()
                            {
                                DistanceToCenter = float.PositiveInfinity,
                                DistanceToEdge = float.PositiveInfinity,
                            },
                    })
                    .OrderBy(x => x.Distance)
                    .FirstOrDefault()?.Enemy;
                if (enemy)
                {
                    baseDir = (enemy.transform.position - Creature.transform.position).normalized;
                }

                // 应用对基准方向的限制
                if (Skill.CsvRow_CatSkill.Laser2Angle is > 0 and < 90)
                {
                    var baseDir2D = (Vector2)baseDir;
                    var q = baseDir2D.GetQuadrant();
                    var q1 = baseDir2D.MirrorToQuadrant(1);
                    var angle = Vector2.Angle(Vector2.right, q1);
                    if (angle > Skill.CsvRow_CatSkill.Laser2Angle)
                    {
                        q1 = Vector3.right.RotateAround(Vector3.zero, Vector3.forward, Skill.CsvRow_CatSkill.Laser2Angle);
                        baseDir = q1.MirrorToQuadrant(q);
                    }
                }

                // 开火声音
                AudioPlayer.Instance.PlaySound(Skill.CsvRow_CatSkill.ShootSound).Forget();

                // 每条激光开启一个任务
                for (var i = 0; i < laserQty; i++)
                {
                    var angle = float.Parse(presetAngles.IndexOf_ByCycle(i));
                    DoLaser(cts_Shoot.Token, i, baseDir,
                        surroundSpeed, surroundDuration, surroundAngle,
                        maxSwingTimes, angle, maxLength, width, maxBounceTimes,
                        attackDuration, minDamageInterval).Forget();
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        /// 一条激光
        /// </summary>
        /// <param name="laserNo">激光编号(0开始)</param>
        /// <param name="baseDir">基准方向</param>
        /// <param name="surroundSpeed">环绕角速度(或摆动)</param>
        /// <param name="surroundDuration">单向摆动一次的时长：秒</param>
        /// <param name="surroundAngle">摆动一次绕过的角度</param>
        /// <param name="maxSwingTimes">摆动次数</param>
        /// <param name="angle">初始角度</param>
        /// <param name="maxLength">激光最大长度</param>
        /// <param name="width">激光宽度</param>
        /// <param name="maxBounceTimes">反弹次数</param>
        /// <param name="attackDuration">持续时长：秒</param>
        /// <param name="minDamageInterval">造成伤害的最小间隔时长:秒</param>
        protected async UniTaskVoid DoLaser(CancellationToken token,
            int laserNo,
            Vector3 baseDir,
            float surroundSpeed,
            float surroundDuration,
            float surroundAngle,
            float maxSwingTimes,
            float angle,
            float maxLength,
            float width,
            float maxBounceTimes,
            float attackDuration,
            float minDamageInterval)
        {
            await UniTask.SwitchToMainThread();

            // 代表一条激光
            var laser = new BulletProps()
            {
                CreatureSkill = this,
                LaserDamageType = 2,
                //将发出的激光保存到LaserLines中
                //LaserLines,
            };
            Lasers.Add(laser);

            // 攻击的结束时间
            var endTime = Time.time + attackDuration;

            // 激光的初始发射角度
            float ejectAngle = 0;
            if (surroundAngle < 360)
            {
                ejectAngle = angle - (surroundAngle / 2);
            }
            // 激光的初始发射方向
            var ejectDir = baseDir.RotateAround(Vector3.zero, Vector3.forward, ejectAngle);

            // 是否应该摆动
            var shouldSwing = surroundSpeed != 0;

            // 摆动方向系数(负数就是反向摆)
            var swingRotato = 1;

            // 激光当前发射方向
            var currentDir = ejectDir;

            try
            {
                for (var i = 0; i < maxSwingTimes; i++)
                {
                    if (token.IsCancellationRequested) break;

                    if (shouldSwing && maxSwingTimes > 1)
                    {
                        swingRotato = i % 2 == 0 ? 1 : -1;
                    }
                    // 本轮摆动的角速度
                    var swingAngleSpeed = surroundSpeed * swingRotato;

                    // 不是往回摆,当前方向就回到初始方向
                    if (swingRotato > 0)
                    {
                        currentDir = ejectDir;
                    }

                    // 本轮摆动的结束时间
                    var eTime = Time.time + surroundDuration;
                    if (eTime > endTime) eTime = endTime;

                    for (; Time.time < eTime; await UniTask.NextFrame())
                    {
                        if (token.IsCancellationRequested) break;

                        if (Time.deltaTime < float.Epsilon) continue;
                        
                        // 将方向按角速度转一点
                        if (swingAngleSpeed > 0)
                        {
                            currentDir = currentDir.RotateAround(Vector3.zero, Vector3.forward, swingAngleSpeed * Time.deltaTime);
                        }

                        // 发出激光并伤害碰到的怪物
                        await DoLaser_Eject(token, Globals.GetAroundBattleArea(), laser,
                            Creature.transform.position,
                            currentDir,
                            maxLength, width, maxBounceTimes,
                            minDamageInterval);
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
            {
                laser.Dispose();
                Lasers.Remove(laser);
            }
        }

        /// <summary>
        /// 向发射方向射出激光,并反弹。
        /// </summary>
        /// <param name="bounds">四个边界线段</param>
        /// <param name="pBegin">激光的起点位置</param>
        /// <param name="ejectDir">激光的发射方向</param>
        /// <param name="maxLength">激光最大长度</param>
        /// <param name="width">激光宽度</param>
        /// <param name="maxBounceTimes">反弹次数</param>
        /// <param name="minDamageInterval">造成伤害的最小间隔时长:秒</param>
        protected async UniTask DoLaser_Eject(CancellationToken token,
            IList<LineSegment> bounds,
            BulletProps laser,
            Vector3 pBegin,
            Vector3 ejectDir,
            float maxLength,
            float width,
            float maxBounceTimes,
            float minDamageInterval
            )
        {
            await UniTask.SwitchToMainThread();
            if (token.IsCancellationRequested) return;

            try
            {
                // 构建激光反弹后的所有顶点
                var points = BuildPoints(bounds, pBegin, ejectDir, maxLength, maxBounceTimes);

                // 如果激光不够,创建激光
                {
                    var t = points.Count - laser.LaserLines.Count;
                    for (var i = 1; i < t; i++)
                    {
                        var laserLine = CreatureBase.CreateLaser(
                            pBegin,
                            Vector3.one,
                            GameManager.instance.player.weapon.laserPrefab,
                            GameManager.instance.player.weapon.laserImpactPrefab);
                        laserLine.SetLaserActive(false);
                        var sr = laserLine.LaserMiddle.GetComponent<SpriteRenderer>();
                        sr.size = new Vector2(maxLength, width);// 设置激光宽度
                        laserLine.LaserMiddle.GetComponent<MaterialMovement>().ResetOffset();
                        laser.LaserLines.Add(laserLine);
                    }
                }

                // 显示激光
                if (points.Count > 1)
                {
                    ShowLaser(points, laser, width);

                    // 如果可以造成伤害的时间到了,则造成一次伤害
                    if (laser.NextHitEnemyTime.Value <= Time.time)
                    {
                        var enemies = LaserColliderEnemy(laser, width);
                        if (enemies.Count > 0)
                        {
                            laser.NextHitEnemyTime.Value = Time.time + minDamageInterval;
                            //Debug.Log($"激光2下次伤害时间: {laser.NextHitEnemyTime.Value}");

                            var maxRadius = laser.DamageEnemy(enemies.ConvertAll(x => x as CreatureBase));
                        }
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
            finally
            {
	            try
	            {
		            // 隐藏所有用到的激光段
		            foreach (BulletLaser laserLine in laser.LaserLines)
		            {
			            laserLine.gameObject.SetActive(false);
		            }
	            }
	            catch
	            {
                    // ignore
	            }
            }
        }

        /// <summary>
        /// 根据激光的反弹点显示激光链
        /// </summary>
        /// <param name="points">激光的反弹点(包含起点)</param>
        /// <param name="laser">哪条激光</param>
        /// <param name="width">激光宽度</param>
        protected void ShowLaser(IList<Vector3> points, BulletProps laser, float width)
        {
            for (int i = 0; i < points.Count - 1; i++)
            {
                Vector3 p1 = points[i];
                Vector3 p2 = points[i + 1];

                var laserLine = laser.LaserLines[i];
                // 更新 起止位置 和 激光的方向、长度
                laserLine.LaserBegin.transform.position = p1;
                laserLine.LaserMiddle.transform.position = p1;
                laserLine.LaserEnd.transform.position = p2;
                var dir = p2 - p1;
                var dir_1 = dir.normalized;
                laserLine.LaserBegin.transform.right = dir_1;
                laserLine.LaserMiddle.transform.right = dir_1;
                laserLine.LaserEnd.transform.right = -dir_1;
                var distance = dir.magnitude;
                laserLine.LaserMiddle.GetComponent<SpriteRenderer>().size = new Vector2(distance, width);
                laserLine.LaserMiddle.GetComponent<MaterialMovement>().OffsetUpdate();
                laserLine.SetLaserActive(true);

#if UNITY_EDITOR
                var txtMesh = laserLine.LaserMiddle.transform.GetComponentInChildren<TextMeshPro>();
                if (txtMesh)
                {
                    txtMesh.text = i.ToString();
                }
#endif
            }
        }

        /// <summary>
        /// 构建激光反弹后的所有顶点
        /// </summary>
        /// <param name="bounds">四个边界线段</param>
        /// <param name="pBegin">激光的起点位置</param>
        /// <param name="ejectDir">激光的发射方向</param>
        /// <param name="maxLength">激光最大长度</param>
        /// <param name="maxBounceTimes">反弹次数</param>
        private static List<Vector3> BuildPoints(IList<LineSegment> bounds,
            Vector3 pBegin, Vector3 ejectDir, float maxLength, float maxBounceTimes)
        {
            List<Vector3> rtn = new()
            {
                pBegin
            };

            FillReflex(rtn, pBegin, ejectDir, maxLength, bounds, (int)maxBounceTimes, 0);

            return rtn;
        }

        /// <summary>
        /// [递归]将一个射线在边界处反弹
        /// </summary>
        /// <param name="points">交点或终点</param>
        /// <param name="p">起点</param>
        /// <param name="dir">方向</param>
        /// <param name="length">剩余长度</param>
        /// <param name="bounds">四个边界线段</param>
        /// <param name="maxBounceTimes">最大反弹次数</param>
        /// <param name="bounceTimes">已反弹次数</param>
        private static void FillReflex(IList<Vector3> points, Vector3 p, Vector3 dir, float length,
            IList<LineSegment> bounds, int maxBounceTimes, int bounceTimes)
        {
            // 反弹次数用完或太短时直接算出终点
            if (bounceTimes >= maxBounceTimes || length < 0.01f)
            {
                points.Add(p + dir * length);
                return;
            }

            // 起点向前移动一点,避免反弹点再次被判为相交
            p += dir * 0.01f;
            length -= 0.01f;

            var (hasIntersect, p1, dirR, lengthL) = ReflexByBounds(p, dir, length, bounds);
            // 没有和边界相交
            if (!hasIntersect)
            {
                points.Add(p + dir * length);
                return;
            }

            // 加入交点后,继续反弹
            points.Add(p1);
            FillReflex(points, p1, dirR, lengthL, bounds, maxBounceTimes, bounceTimes + 1);
        }

        /// <summary>
        /// 将一个射线在边界处反弹一次
        /// </summary>
        /// <param name="p">起点</param>
        /// <param name="dir">方向</param>
        /// <param name="length">剩余长度</param>
        /// <param name="bounds">边界</param>
        /// <returns>有没有相交, 交点, 反弹后的方向, 反弹后的剩余长度</returns>
        private static (bool, Vector3, Vector3, float) ReflexByBounds(Vector3 p, Vector3 dir,
            float length, IList<LineSegment> bounds)
        {
            var hasIntersect = false;
            Vector3 pIntersect = Vector3.zero, dirR = Vector3.zero;
            float lengthL = 0;

            var pEnd = p + dir * length;
            for (var i = 0; i < bounds.Count; i++)
            {
                (hasIntersect, pIntersect) = Ext_UniEngine.TryGetIntersectPoint(p, pEnd, bounds[i].P1, bounds[i].P2);
                if (!hasIntersect) continue;
                
                lengthL = length - (pIntersect - p).magnitude;
                dirR = Vector3.Reflect(dir, Globals.BattleArea_NormalLines[i]);
                break;
            }

            return (hasIntersect, pIntersect, dirR, lengthL);
        }

        /// <summary>
        /// 反弹的激光碰到了哪些敌人
        /// </summary>
        /// <param name="laser"></param>
        /// <param name="width"></param>
        private static List<Enemy> LaserColliderEnemy(BulletProps laser, float width)
        {
            List<Enemy> rtn = new();
            foreach (var laserLine in laser.LaserLines)
            {
                var length = (laserLine.LaserEnd.transform.position - laserLine.LaserBegin.transform.position).magnitude;
                rtn.AddRange(GameSharedData.Instance.GetEnemiesTouchLaser(laserLine.LaserBegin.transform, length, width));
            }
            return rtn.Distinct().ToList();
        }
    }
}
