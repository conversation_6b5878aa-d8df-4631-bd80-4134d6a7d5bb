﻿using System.Collections.Generic;
using System.Linq;
using System;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Apq.Extension
{
    /// <summary>
    /// 反射扩展
    /// </summary>
    public static class Ext_Reflection
    {
        #region MemberInfo
        /// <summary>
        /// 是否为匿名代码(如:匿名类、Lambda块)
        /// </summary>
        public static bool IsAnonymous(this MemberInfo me)
        {
            return me.Name.StartsWith("<");
        }

        /// <summary>
        /// 获取友好名称
        /// </summary>
        public static string GetFriendlyName(this MemberInfo me)
        {
            if (!me.Name.StartsWith("<")) return me.Name;

            // 提取匿名方法所在的手工方法名
            var pEnd = me.Name.IndexOf(">", StringComparison.Ordinal);
            return me.Name[1..pEnd];
        }
        #endregion

        #region Class
        /// <summary>
        /// 是否由编译器自动生成
        /// </summary>
        public static bool IsCompilerGenerated(this Type me)
        {
            return me.CustomAttributes.Any(x => typeof(CompilerGeneratedAttribute).IsAssignableFrom(x.AttributeType));
        }

        /// <summary>
        /// 获取类的定义
        /// </summary>
        public static Type GetClassDeclare(this Type me)
        {
            Type type = me;
            if (type.IsGenericType)
            {
                while (!type.IsGenericTypeDefinition)
                {
                    type = type.GetGenericTypeDefinition();
                }
                return type;
            }

            // 匿名类和编译器自动生成的类,取其定义所在的类
            while (type.DeclaringType != null
                && (type.IsAnonymous() || type.IsCompilerGenerated()))
            {
                type = type.DeclaringType;
            }

            return type;
        }

        /// <summary>
        /// 获取类的命名空间
        /// </summary>
        public static string GetNameSpace(this Type me)
        {
            return me.GetClassDeclare().Namespace;
        }

        /// <summary>
        /// 获取类自己的名称(不含嵌套类的上级类名)
        /// </summary>
        private static string getClassNameSelf(this Type me)
        {
            var clsName = me.Name;
            if (me.IsGenericType)
            {
                var lst = new List<string>();
                // 泛型参数数组
                var tgpAry = me.GetGenericArguments();
                var tgpCount = tgpAry.Length;
                for (var i = 1; i <= tgpCount; i++)
                {
                    var tgp = tgpAry[i - 1];
                    if (tgp.IsGenericParameter)
                    {
                        lst.Add(tgp.Name);
                    }
                    else
                    {
                        lst.Add(tgp.GetClassName());
                    }
                }
                clsName += $"<{string.Join(", ", lst)}>";
            }
            return clsName;
        }

        /// <summary>
        /// 获取类名(嵌套类包含上级类名)
        /// </summary>
        public static string GetClassName(this Type me)
        {
            var lst = new List<string>();

            var type = me;
            do
            {
                lst.Insert(0, type.getClassNameSelf());
            }
            while (type.IsNested && type.DeclaringType != null);

            return string.Join(".", lst);
        }
        #endregion

        #region Method
        /// <summary>
        /// 是否由编译器自动生成
        /// </summary>
        public static bool IsCompilerGenerated(this MethodBase me)
        {
            if (me.CustomAttributes.Any(x => typeof(CompilerGeneratedAttribute).IsAssignableFrom(x.AttributeType)))
            {
                return true;
            }

            if (me.DeclaringType != null)
            {
                return me.DeclaringType.IsCompilerGenerated();
            }

            return false;
        }

        /// <summary>
        /// 获取方法定义所在的类和方法名(不含泛型的形参列表)
        /// </summary>
        private static (Type, string) getMethodNameDeclare(this MethodBase me)
        {
            var (meType, name) = (me.DeclaringType, me.GetFriendlyName());
            var type = meType;
            if (type != null)
            {
                type = type.GetClassDeclare();
            }

            // 1、匿名方法
            // 2、定义方法的类是匿名类或由编译器自动生成
            // 3、name为空
            // 就一直往更上层的定义取,直到取到或遍历完所有上层。
            if (meType != null && meType.DeclaringType != null &&
                (me.IsAnonymous() || meType.IsAnonymous()
                    || me.IsCompilerGenerated()
                    || string.IsNullOrEmpty(name)))
            {
                var mType = meType;
                var pType = mType.DeclaringType;
                var tName = meType.GetFriendlyName();
                while (pType.DeclaringType != null
                    && (pType.IsAnonymous() || pType.IsCompilerGenerated()
                        || string.IsNullOrEmpty(tName)))
                {
                    mType = pType;
                    pType = pType.DeclaringType;
                    tName = mType.GetFriendlyName();
                }

                if (!string.IsNullOrEmpty(tName))
                {
                    name = tName;
                }
            }
            return (type, name);
        }

        /// <summary>
        /// 获取方法定义所在的命名空间、类名和方法名
        /// </summary>
        public static (string ns, string clsName, string methodName) GetMethodName(this MethodBase me)
        {
            var (type, name) = me.getMethodNameDeclare();

            string ns = null;
            if (type != null)
            {
                ns = type.Namespace;
            }

            var clsName = me.ReflectedType?.Name;
            if (type == null)
            {
                type = me.DeclaringType;
            }
            if (type != null)
            {
                clsName = type.GetClassName();
            }

            return (ns, clsName, name);
        }
        #endregion

        /// <summary>
        /// 将名称中的英文逗号换为中文逗号
        /// </summary>
        public static string FomatNameForCSV(string name)
        {
            return name.Replace(",", "，");
        }

        /// <summary>
        /// 获取继承层次中的所有字段
        /// </summary>
        /// <param name="me">当前类型</param>
        /// <param name="flags">标志</param>
        /// <param name="names">字段名范围</param>
        public static Dictionary<string, FieldInfo> GetAllFields(this Type me, BindingFlags flags, IEnumerable<string> names = null)
        {
            var rtn = new Dictionary<string, FieldInfo>();

            for (var t = me; t != null; t = t.BaseType)
            {
                var fields = t.GetFields(flags);//.Where(f => !f.Name.EndsWith("k__BackingField"));
                foreach (var field in fields)
                {
                    if (rtn.Keys.Contains(field.GetFriendlyName())) continue;
                    // 未包含
                    if (names == null || names.Contains(field.GetFriendlyName()))
                    {// 在范围内
                        rtn.Add(field.GetFriendlyName(), field);
                    }
                }
            }

            return rtn;
        }

        /// <summary>
        /// 获取继承层次中的所有属性
        /// </summary>
        /// <param name="me">当前类型</param>
        /// <param name="flags">标志</param>
        /// <param name="names">字段名范围</param>
        public static Dictionary<string, PropertyInfo> GetAllProperties(this Type me, BindingFlags flags, IEnumerable<string> names = null)
        {
            var rtn = new Dictionary<string, PropertyInfo>();

            for (var t = me; t != null; t = t.BaseType)
            {
                var properties = t.GetProperties(flags);
                foreach (var property in properties)
                {
                    if (rtn.Keys.Contains(property.Name)) continue;
                    // 未包含
                    if (names == null || names.Contains(property.Name))
                    {// 在范围内
                        rtn.Add(property.GetFriendlyName(), property);
                    }
                }
            }

            return rtn;
        }
    }
}
