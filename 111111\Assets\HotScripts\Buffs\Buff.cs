﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using Newtonsoft.Json;

using UnityEngine;

namespace Buffs
{
    /// <summary>
    /// Buff类
    /// </summary>
    /// <remarks>新建对象时需要赋值的属性:CsvRow_Buff,LoadEffects之后才可加入调度器</remarks>
    public class Buff : System.IDisposable
    {
        /// <summary>
        /// Buff施加者
        /// </summary>
        public CreatureBase Imposer { get; set; }
        
        /// <summary>
        /// 所属调度器
        /// </summary>
        public BuffScheduler BuffScheduler { get; set; }
        /// <summary>
        /// Buff配置行
        /// </summary>
        public X.PB.Buff.Item CsvRow_Buff { get; set; }

        /// <summary>
        /// 包含的效果
        /// </summary>
        public List<BuffEffectBase> BuffEffects { get; } = new();

        /// <summary>
        /// 是否由调度器定时循环执行
        /// </summary>
        /// <remarks>注意:即使不由调度器定时循环执行,也要加入某个调度器才能执行,因为需要从调度器中获取受体</remarks>
        public bool UseScheduler { get; set; } = true;

        /// <summary>
        /// Buff开始时间
        /// </summary>
        public float? BeginTime { get; set; }
        public CancellationTokenSource CTS_Buff { get; } = new();

        /// <summary>
        /// 即将启动Buff
        /// </summary>
        public event System.Action<Buff> BeforeStart;
        /// <summary>
        /// Buff启动后
        /// </summary>
        public event System.Action<Buff> AfterStart;
        /// <summary>
        /// 即将终止Buff
        /// </summary>
        public event System.Action<Buff> BeforeStop;
        /// <summary>
        /// Buff终止后
        /// </summary>
        public event System.Action<Buff> AfterStop;
        /// <summary>
        /// 即将开始一次Buff工作
        /// </summary>
        public event System.Action<Buff> BeforeDoBuff;
        /// <summary>
        /// Buff工作一次后
        /// </summary>
        public event System.Action<Buff> AfterDoBuff;

        /// <summary>
        /// 初始化BuffEffects列表
        /// </summary>
        /// <remarks>根据配置新建BuffEffect对象并加入列表</remarks>
        public void InitEffects()
        {
            if (CsvRow_Buff.EffectID1 > 0)
            {
                var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(CsvRow_Buff.EffectID1);
                var buffEffect = BuffEffectBase.Create(csvRow_BuffEffect);
                buffEffect.Buff = this;
                BuffEffects.Add(buffEffect);
            }
            if (CsvRow_Buff.EffectID2 > 0)
            {
                var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(CsvRow_Buff.EffectID2);
                var buffEffect = BuffEffectBase.Create(csvRow_BuffEffect);
                buffEffect.Buff = this;
                BuffEffects.Add(buffEffect);
            }
            if (CsvRow_Buff.EffectID3 > 0)
            {
                var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(CsvRow_Buff.EffectID3);
                var buffEffect = BuffEffectBase.Create(csvRow_BuffEffect);
                buffEffect.Buff = this;
                BuffEffects.Add(buffEffect);
            }
            if (CsvRow_Buff.EffectID4 > 0)
            {
                var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(CsvRow_Buff.EffectID4);
                var buffEffect = BuffEffectBase.Create(csvRow_BuffEffect);
                buffEffect.Buff = this;
                BuffEffects.Add(buffEffect);
            }
            if (CsvRow_Buff.EffectID5 > 0)
            {
                var csvRow_BuffEffect = BuffEffectScheme.Instance.GetItem(CsvRow_Buff.EffectID5);
                var buffEffect = BuffEffectBase.Create(csvRow_BuffEffect);
                buffEffect.Buff = this;
                BuffEffects.Add(buffEffect);
            }
        }

        ///// <summary>
        ///// 是否可添加到指定的调度器
        ///// </summary>
        //public virtual bool CanAddTo(BuffScheduler buffScheduler)
        //{
        //    return true;
        //}

        #region DoBuff
        /// <summary>
        /// 该Buff要干的活
        /// </summary>
        public virtual async UniTaskVoid DoBuff(CancellationToken cancel)
        {
            await UniTask.SwitchToMainThread(cancel);
            DoBuff_Before(cancel);
            DoBuff_Do(cancel);
            DoBuff_After(cancel);

            // 每次执行后判断是否应该终止
            if (ShouldStop(cancel))
            {
                StopBuff();
            }
        }

        protected virtual void DoBuff_Before(CancellationToken cancel)
        {
            OnBeforeDoBuff(cancel);
        }
        /// <summary>
        /// 默认Buff的操作就是每个效果都执行一遍
        /// </summary>
        protected virtual void DoBuff_Do(CancellationToken cancel)
        {
            foreach (var buffEffect in BuffEffects)
            {
                buffEffect.DoWork(cancel).Forget();
            }
        }
        protected virtual void DoBuff_After(CancellationToken cancel)
        {
            OnAfterDoBuff(cancel);
        }

        /// <summary>
        /// 每次循环任务前
        /// </summary>
        public virtual void OnBeforeDoBuff(CancellationToken cancel)
        {
            FireBeforeDoBuff();
        }

        /// <summary>
        /// 每次循环任务后
        /// </summary>
        public virtual void OnAfterDoBuff(CancellationToken cancel)
        {
            //NextDoTime = Time.time + CsvRow_Buff.DayLimit;
            FireAfterDoBuff();
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireBeforeDoBuff()
        {
            BeforeDoBuff?.Invoke(this);
        }
        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireAfterDoBuff()
        {
            AfterDoBuff?.Invoke(this);
        }
        #endregion

        /// <summary>
        /// 判断Buff是否应该终止
        /// </summary>
        protected virtual bool ShouldStop(CancellationToken cancel)
        {
            if (cancel.IsCancellationRequested)
            {
                return true;
            }

            // 满足Buff终止条件时返回true
            switch (CsvRow_Buff.CatSkillComb)
            {
                // 执行过就终止
                case 1:
                    {
                        return BeginTime.HasValue;
                    }
                // 定时终止(定身、击退、减速)
                case 100:
                case 101:
                case 102:
                    {
                        var keepTime = GenKeepTime();

                        if (BeginTime.HasValue)
                        {
                            if (BeginTime.Value + keepTime <= Time.time)
                            {
                                return true;
                            }
                        }
                    }
                    break;
            }

            return false;
        }

        /// <summary>
        /// 计算定时类Buff的时长(秒)
        /// </summary>
        public float GenKeepTime()
        {
            float rtn = 1000000;
            if (!BeginTime.HasValue) return rtn;

            switch (CsvRow_Buff.CatSkillComb)
            {
                // 定身Buff
                case 102:
                    {
                        var denyMoveTime = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.DenyMoveTime.Value);
                        var denyMoveTimePct = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.DenyMoveTimePct.Value);
                        rtn = CsvRow_Buff.KeepTime / 1000f;
                        rtn += denyMoveTime;
                        rtn *= 1 + denyMoveTimePct;
                        break;
                    }
                // 击退Buff
                case 101:
                    {
                        var HitBackTime = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.HitBackTime.Value);
                        var HitBackTimePct = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.HitBackTimePct.Value);
                        rtn = CsvRow_Buff.KeepTime / 1000f;
                        rtn += HitBackTime;
                        rtn *= 1 + HitBackTimePct;
                        break;
                    }
                // 减速Buff
                case 100:
                    {
                        var SlowTime = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.SlowTime.Value);
                        var SlowTimePct = (float)GameManager.instance.player.SkillScheduler.Skills.Sum(x => (double)x.SlowTimePct.Value);
                        rtn = CsvRow_Buff.KeepTime / 1000f;
                        rtn += SlowTime;
                        rtn *= 1 + SlowTimePct;

                        //Debug.Log($"减速Buff的时长:{rtn}秒");

                        break;
                    }
            }

            return rtn;
        }

        #region StartBuff
        /// <summary>
        /// Buff自己启动任务
        /// </summary>
        public async UniTaskVoid StartBuff(CancellationToken cancel)
        {
            await UniTask.SwitchToMainThread(cancel);
            StartBuff_Before(cancel);
            StartBuff_Do(cancel).Forget();
            StartBuff_After(cancel);
        }

        protected virtual void StartBuff_Before(CancellationToken cancel)
        {
            OnBeforeStart(cancel);
        }

        /// <summary>
        /// Buff自己负责Buff的调度(功能和停止)
        /// </summary>
        protected virtual async UniTaskVoid StartBuff_Do(CancellationToken cancel)
        {
            //// 先执行一次,再启动定时器循环
            //DoBuff(CTS_Buff.Token).Forget();

            //if (CsvRow_Buff.DayLimit > 0)
            //{
            //    if (CsvRow_Buff.DayLimit < 40)
            //    {
            //        CsvRow_Buff.DayLimit = 40;
            //    }

            //    BuffTask ??= Observable.Interval(System.TimeSpan.FromMilliseconds(CsvRow_Buff.DayLimit))
            //        .Where(_ => Time.deltaTime > 0)
            //        .Subscribe(_ =>
            //        {
            //            DoBuff(CTS_Buff.Token).Forget();
            //        })
            //        .AddTo(CTS_Buff.Token);
            //}

            try
            {
                CancellationTokenSource cts = CancellationTokenSource.CreateLinkedTokenSource(cancel,
                    CTS_Buff.Token);
                var token = cts.Token;

                while (!token.IsCancellationRequested)
                {
                    await UniTask.NextFrame();

                    if (Time.deltaTime > 0)
                    {
                        DoBuff(token).Forget();
                    }
                }
            }
            catch { }
            finally
            {
                //try
                //{
                //    StopBuff();
                //}
                //catch { }
            }
        }
        protected virtual void StartBuff_After(CancellationToken cancel)
        {
            OnAfterStart(cancel);
        }

        /// <summary>
        /// 启动前(已加入调度器)
        /// </summary>
        public virtual void OnBeforeStart(CancellationToken cancel)
        {
            BeginTime = Time.time;
            FireBeforeStart();
        }

        /// <summary>
        /// 启动后(已启动循环任务)
        /// </summary>
        public virtual void OnAfterStart(CancellationToken cancel)
        {
            FireAfterStart();
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireBeforeStart()
        {
            BeforeStart?.Invoke(this);
        }
        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireAfterStart()
        {
            AfterStart?.Invoke(this);
        }
        #endregion

        #region StopBuff
        /// <summary>
        /// 销毁就是终止Buff
        /// </summary>
        /// <remarks>销毁时</remarks>
        public virtual void Dispose()
        {
            StopBuff_Before();
            StopBuff_Do();
            StopBuff_After();
        }

        /// <summary>
        /// 终止Buff,销毁的同义词
        /// </summary>
        public virtual void StopBuff()
        {
            Dispose();
        }

        protected virtual void StopBuff_Before()
        {
            OnBeforeStop();
        }
        protected virtual void StopBuff_Do()
        {
            if (BuffScheduler)
            {
                BuffScheduler.Buffs.Remove(this);
            }
            CTS_Buff?.Cancel();
        }
        protected virtual void StopBuff_After()
        {
            OnAfterStop();
        }

        /// <summary>
        /// 停止前
        /// </summary>
        public virtual void OnBeforeStop()
        {
            FireBeforeStop();

            // 停止Buff前先杀掉它的所有Effect(避免临时改变量的持续生效)
            BuffEffects.ForEach(x => x.DoKill());
        }

        /// <summary>
        /// 停止后(子任务已停,已移出调度器)
        /// </summary>
        public virtual void OnAfterStop()
        {
            FireAfterStop();
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireBeforeStop()
        {
            BeforeStop?.Invoke(this);
        }

        /// <summary>
        /// 仅触发事件
        /// </summary>
        protected virtual void FireAfterStop()
        {
            AfterStop?.Invoke(this);
        }
        #endregion

        /// <summary>
        /// 转为传给Lua的Json { CsvRow_Buff:{配置行}, BuffEffects: {配置行,...}}
        /// </summary>
        public string ToJson_Lua()
        {
            JsonStructure.Buff rtn = new()
            {
                BuffID = CsvRow_Buff.Id - CsvRow_Buff.Level + 1,
                CsvRow_Buff = CsvRow_Buff,
                BuffEffects = BuffEffects.Select(x => x.CsvRow_BuffEffect).ToList(),
            };
            return JsonConvert.SerializeObject(rtn);
        }
    }
}
