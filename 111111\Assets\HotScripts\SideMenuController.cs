using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;
using UnityEngine.UI;
using Spine;
using Spine.Unity;
using TMPro;
using DG.Tweening;
using UnityEngine.SceneManagement;
using System.Linq;
public class SideMenuController : MonoBehaviour
{

    [SerializeField] private Animator anim;
    [SerializeField] private RectTransform rect;
    [SerializeField] private TextMeshProUGUI missionName;
    [SerializeField] private TextMeshProUGUI missionDescription;
    [SerializeField] private TextMeshProUGUI missionNumber;
    [SerializeField] private SkeletonGraphic[] missionImageSpineData = null;
    [SerializeField] private Sprite[] abilitySprites = null;
    [SerializeField] private Sprite[] sidekickSprites = null;
    [SerializeField] private Image[] abilityRenderer = null;

    private SkeletonGraphic missionImageSpineObject = null;
    public Action addedCloseFunction;
    [HideInInspector] public Vector2 _mousePosition;
    [SerializeField] private Button playButton;


    [SerializeField] private Image fadeImage;   

    private DG.Tweening.Sequence seq;

    public void OnPlayButtonCall()
    {
        playButton.interactable =false;
        fadeImage.raycastTarget = true;
        fadeImage.DOFade(1, 0.25f).OnComplete(() => { SceneManager.LoadScene("GameScene"); });
    }

    private void ShowUnlocks()
    {
        foreach (Image i in abilityRenderer)
        {
            i.gameObject.SetActive(false);
        }
        int index = 0;
        
        string sideKickName;
        
    }

    private void UpdateSpineImage()
    {
        foreach (SkeletonGraphic s in missionImageSpineData)
        {
            s.gameObject.SetActive(false);
        }

        missionImageSpineObject = missionImageSpineData[GameData.instance.fileHandler.currentMission - 1];
        //ValueMap missionMap = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission);

        //missionImageSpineObject = SkeletonAnimation::createWithJsonFile(missionMap.at("Image").asString() + ".json", missionMap.at("Image").asString() + ".atlas");TODO

        //_clipNode.addChild(missionImageSpineObject, 1);
        missionImageSpineObject.gameObject.SetActive(true);
//        missionImageSpineObject.AnimationState.SetAnimation(0, "mapAnim", true);

        if (GameData.instance.fileHandler.currentMission == 2)//FileHandler::getInstance().currentMission == 2)
        {
            //missionImageSpineObject.Skeleton.SetSkin("enemyPlaneLevel6");
        }
        if (GameData.instance.fileHandler.currentMission == 29)//FileHandler::getInstance().currentMission == 29)
        {
           // missionImageSpineObject.Skeleton.SetSkin("dragonGreen");
        }
        if (GameData.instance.fileHandler.currentMission == 24)// FileHandler::getInstance().currentMission == 24)
        {
            //missionImageSpineObject.transform.SetScale(0.185f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 21)// FileHandler::getInstance().currentMission == 21)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 27)// FileHandler::getInstance().currentMission == 27)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "arenaIdle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.42f);
        }
        if (GameData.instance.fileHandler.currentMission == 29)// FileHandler::getInstance().currentMission == 29)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "idle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 50);
        }
        if (GameData.instance.fileHandler.currentMission == 30)// FileHandler::getInstance().currentMission == 30)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.3f);
        }
        Globals.Rescale(missionImageSpineObject.gameObject, missionImageSpineObject.transform.localScale.x);
    }

    public void ShowMissionInfo()
    {
        

        


        
        //if (missionName.getLabelType() == Label::LabelType::TTF) tODO
        //{
        //    TTFConfig config = missionName.getTTFConfig();
        //    config.fontSize = 100;
        //    missionName.setTTFConfig(config);
        //}
        //else
        //{
        //    //        missionName.setSystemFontSize(80);
        //}

        seq.Kill();
        anim.SetInteger("In", 1);
        if (rect.anchoredPosition.x < 50)// this.getPosition().x > Director::getInstance().getWinSize().width - 5)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

            //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
        }

        Globals.gameType = GameType.Arena;

        //GameData.instance.fileHandler.sa FileHandler::getInstance().saveData(); TODO
        //if (DIFFICULTYMENU)
        //{
        //    int rewardInCoins = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Coins").asInt();
        //    int rewardInXp = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Xp").asInt(); ;

        //    df1.changeStats(rewardInCoins / 2, rewardInXp / 2);
        //    df2.changeStats(rewardInCoins, rewardInXp);
        //    df3.changeStats(rewardInCoins * 2, rewardInXp * 2);
        //}
        UpdateSpineImage();
        ShowUnlocks();
    }

    public void CloseButtonCall()
    {
        if (!IsOpen())
        {
            return;
        }
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp,  0.15f);

        //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);

        addedCloseFunction();
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        anim.SetInteger("In", 0);
    }

    public bool IsOpen()
    {
        if (rect.anchoredPosition.x<50)
        {
            return true;
        }
        return false;
    }
}
