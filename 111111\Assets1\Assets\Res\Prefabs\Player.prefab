%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &242604390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 242604391}
  m_Layer: 0
  m_Name: DashParent
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &242604391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242604390}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 4.7619042, y: 4.7619042, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1113248976}
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &989503944
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 989503945}
  m_Layer: 0
  m_Name: DryShotSmokes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &989503945
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 989503944}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5173995207649535162}
  - {fileID: 5173995206940880932}
  - {fileID: 5173995207662050323}
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1113248975
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1113248976}
  - component: {fileID: 1113248978}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: dashvfx1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1113248976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1113248975}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.4238003, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 242604391}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &1113248978
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1113248975}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: -5228462940366063914, guid: fd0e509e0bcb94aa8acdd9eceb6972ce,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.78, y: 0.71}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &**********
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1113248975}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: aba5dbcf5c7524c459559de9f801c775, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!1 &1662194527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1662194528}
  - component: {fileID: 1662194531}
  - component: {fileID: 1662194530}
  - component: {fileID: 1662194529}
  m_Layer: 0
  m_Name: Spine GameObject (enemyFlame)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1662194528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662194527}
  m_LocalRotation: {x: 0, y: 0, z: -0.5735764, w: 0.8191521}
  m_LocalPosition: {x: -1.1, y: 1.29, z: 0}
  m_LocalScale: {x: -4.97, y: -4.97, z: 0.8875}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 104557257342605396}
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -70}
--- !u!33 &1662194531
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662194527}
  m_Mesh: {fileID: 0}
--- !u!23 &1662194530
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662194527}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6245cd99f388a41b79ca8dd9e31a41b0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: -1
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &1662194529
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662194527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 65e3cdde38e52429b90d25f64551a2ae, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: 
  loop: 1
  timeScale: 1
--- !u!1 &428655155329707289
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5731993262811258560}
  - component: {fileID: 6787120************}
  m_Layer: 0
  m_Name: Bar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5731993262811258560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 428655155329707289}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.8105, y: 0.002, z: 0}
  m_LocalScale: {x: 1.0782706, y: 1.3669151, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2884121244670542002}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &6787120************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 428655155329707289}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 164d26bce6c814bbcaf1f025792dc08c, type: 3}
  m_Color: {r: 1, g: 0.7529564, b: 0, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.98, y: 0.15}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &596532924683668680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2884121244670542002}
  - component: {fileID: 8626623************}
  m_Layer: 0
  m_Name: EnergyBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2884121244670542002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596532924683668680}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 6.25, z: 0}
  m_LocalScale: {x: 7.5, y: 3, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5731993262811258560}
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &8626623************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596532924683668680}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3a7c4ebeb4345483dabc96707cc1fd1d, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.68, y: 0.33}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &667309762435830928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762435830932}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762435830930}
  - component: {fileID: 667309762435830929}
  m_Layer: 0
  m_Name: Flash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762435830932
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762435830928}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762435830928}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762435830930
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762435830928}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762435830929
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762435830928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762436593054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762436593055}
  - component: {fileID: 667309762436593040}
  m_Layer: 0
  m_Name: bone15
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762436593055
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762436593054}
  m_LocalRotation: {x: 0, y: 0, z: -0.20509396, w: 0.9787423}
  m_LocalPosition: {x: 0.5722, y: 0.0042, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763103978277}
  m_Father: {fileID: 667309762664136270}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762436593040
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762436593054}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone15
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762442842889
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762442842890}
  - component: {fileID: 667309762442842891}
  m_Layer: 0
  m_Name: mg2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762442842890
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762442842889}
  m_LocalRotation: {x: 0, y: 0, z: 0.06984354, w: 0.997558}
  m_LocalPosition: {x: 3.0016, y: 1.1622, z: 0}
  m_LocalScale: {x: 0.575, y: 0.575, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762442842891
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762442842889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: mg2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762444435494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762444435495}
  - component: {fileID: 667309762444435544}
  m_Layer: 0
  m_Name: bone21
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762444435495
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762444435494}
  m_LocalRotation: {x: 0, y: 0, z: -0.00043633234, w: 0.99999994}
  m_LocalPosition: {x: 0.5593, y: -0.00090000004, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763358248134}
  m_Father: {fileID: 667309764409736437}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762444435544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762444435494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone21
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762464599689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762464599690}
  - component: {fileID: 667309762464599691}
  m_Layer: 0
  m_Name: pipe
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762464599690
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762464599689}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -1.0985, y: -0.8062999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762929054618}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762464599691
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762464599689}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: pipe
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762483490818
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762483490819}
  - component: {fileID: 667309762483490820}
  m_Layer: 0
  m_Name: backwing
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762483490819
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762483490818}
  m_LocalRotation: {x: 0, y: 0, z: 0.30835295, w: 0.951272}
  m_LocalPosition: {x: 1.4925, y: 0.77, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762483490820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762483490818}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: backwing
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762490536416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762490536417}
  - component: {fileID: 667309762490536418}
  m_Layer: 0
  m_Name: bone23
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762490536417
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762490536416}
  m_LocalRotation: {x: 0, y: 0, z: -0.86772203, w: 0.49704975}
  m_LocalPosition: {x: -0.80660003, y: -0.7039, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764555009544}
  m_Father: {fileID: 667309763404838297}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762490536418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762490536416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone23
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762522192958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762522192959}
  - component: {fileID: 667309762522192944}
  m_Layer: 0
  m_Name: plasmaCannon
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762522192959
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762522192958}
  m_LocalRotation: {x: 0, y: 0, z: 0.75762194, w: 0.6526936}
  m_LocalPosition: {x: 1.7416999, y: 0.83919996, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764341450173}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762522192944
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762522192958}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: plasmaCannon
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762532953937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762532953938}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762532953940}
  - component: {fileID: 667309762532953939}
  m_Layer: 0
  m_Name: Flash (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762532953938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762532953937}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762532953937}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762532953940
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762532953937}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762532953939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762532953937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762589599230
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762589599231}
  - component: {fileID: 667309762589599216}
  m_Layer: 0
  m_Name: muffler
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762589599231
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762589599230}
  m_LocalRotation: {x: 0, y: 0, z: 0.74999565, w: 0.66144276}
  m_LocalPosition: {x: 0.2435, y: 0.083100006, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763886528358}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762589599216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762589599230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: muffler
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762610630323
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762610630324}
  - component: {fileID: 667309762610630325}
  m_Layer: 0
  m_Name: fan
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762610630324
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762610630323}
  m_LocalRotation: {x: 0, y: 0, z: 0.03167243, w: 0.9994983}
  m_LocalPosition: {x: 3.2676997, y: -0.011499999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762610630325
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762610630323}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: fan
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762612685620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762612685621}
  - component: {fileID: 667309762612685622}
  m_Layer: 0
  m_Name: LOOSECANNON
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762612685621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762612685620}
  m_LocalRotation: {x: 0, y: 0, z: -0.010733569, w: 0.9999424}
  m_LocalPosition: {x: 1.3786, y: -0.0861, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764096244749}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762612685622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762612685620}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: LOOSECANNON
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762631197933
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762631197921}
  - component: {fileID: 667309762631197920}
  - component: {fileID: 667309762631197935}
  - component: {fileID: 667309762631197934}
  - component: {fileID: 667309762631197922}
  m_Layer: 0
  m_Name: Spine GameObject (player)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762631197921
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762631197933}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763103698570}
  - {fileID: 667309764442415354}
  - {fileID: 667309764337975842}
  - {fileID: 1662194528}
  - {fileID: 4007464801609088638}
  - {fileID: 2724011708693498481}
  - {fileID: 3801057937239887183}
  - {fileID: 8043039907547388829}
  - {fileID: 4910560111926559989}
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &667309762631197920
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762631197933}
  m_Mesh: {fileID: 0}
--- !u!23 &667309762631197935
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762631197933}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &667309762631197934
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762631197933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 6d2ef3cbf166140c29ab0128ff4db500, type: 2}
  initialSkinName: playerPlane1
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: 
  loop: 1
  timeScale: 1
--- !u!114 &667309762631197922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762631197933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7f726fb798ad621458c431cb9966d91d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneRoot: {fileID: 667309763103698570}
  skeletonRenderer: {fileID: 667309762631197934}
--- !u!1 &667309762664136269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762664136270}
  - component: {fileID: 667309762664136271}
  m_Layer: 0
  m_Name: bone14
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762664136270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762664136269}
  m_LocalRotation: {x: 0, y: 0, z: 0.837051, w: 0.54712504}
  m_LocalPosition: {x: -0.1601, y: -0.4455, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762436593055}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762664136271
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762664136269}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone14
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762667797734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762667797735}
  - component: {fileID: 667309762667797528}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762667797735
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762667797734}
  m_LocalRotation: {x: 0, y: 0, z: 0.020680675, w: 0.99978614}
  m_LocalPosition: {x: 0.54109997, y: 0.1543, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763404838297}
  - {fileID: 667309764314841986}
  - {fileID: 667309762818811608}
  - {fileID: 667309763692245739}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762667797528
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762667797734}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: head
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762668074908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762668074909}
  - component: {fileID: 667309762668074896}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762668074910}
  m_Layer: 0
  m_Name: ProtonUlt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &667309762668074909
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762668074908}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.09, z: 0}
  m_LocalScale: {x: 4.7619047, y: 4.7619047, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &667309762668074896
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762668074908}
  m_Mesh: {fileID: 0}
--- !u!23 &667309************
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762668074908}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f78ac61fba5e34f06987d72c3fabc13e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: -1
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &667309762668074910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762668074908}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: cd35c1ec579034ad9b15582e799d3e7d, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: idle
  loop: 1
  timeScale: 1
--- !u!1 &667309762670609584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762670609585}
  - component: {fileID: 667309762670609586}
  m_Layer: 0
  m_Name: bone3
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762670609585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762670609584}
  m_LocalRotation: {x: 0, y: 0, z: 0.658164, w: 0.75287455}
  m_LocalPosition: {x: 0.3069, y: 0.3419, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764109322655}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762670609586
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762670609584}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone3
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762704178322
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762704178323}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762704178325}
  - component: {fileID: 667309762704178324}
  m_Layer: 0
  m_Name: Flash (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762704178323
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762704178322}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762704178322}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762704178325
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762704178322}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762704178324
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762704178322}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762705573656
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762705573657}
  - component: {fileID: 667309762705573658}
  m_Layer: 0
  m_Name: bone5
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762705573657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762705573656}
  m_LocalRotation: {x: 0, y: 0, z: -0.105483085, w: 0.9944211}
  m_LocalPosition: {x: 0.26819998, y: -0.0018999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763407509105}
  m_Father: {fileID: 667309763404838297}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762705573658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762705573656}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone5
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762794032579
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762794032580}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762794032582}
  - component: {fileID: 667309762794032581}
  m_Layer: 0
  m_Name: Flash (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762794032580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762794032579}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762794032579}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762794032582
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762794032579}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762794032581
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762794032579}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762807641378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762807641379}
  - component: {fileID: 667309762807641380}
  m_Layer: 0
  m_Name: flame
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762807641379
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762807641378}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.7012, y: -0.235, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764060531493}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762807641380
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762807641378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: flame
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762818811559
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762818811608}
  - component: {fileID: 667309762818811609}
  m_Layer: 0
  m_Name: bone17
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762818811608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762818811559}
  m_LocalRotation: {x: 0, y: 0, z: -0.7043246, w: 0.70987815}
  m_LocalPosition: {x: -0.0419, y: -0.4732, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762667797735}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762818811609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762818811559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone17
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762826246352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762826246353}
  - component: {fileID: 667309762826246354}
  m_Layer: 0
  m_Name: mg4
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762826246353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762826246352}
  m_LocalRotation: {x: 0, y: 0, z: 0.06984354, w: 0.997558}
  m_LocalPosition: {x: 1.7484, y: -0.32869998, z: 0}
  m_LocalScale: {x: 0.584, y: 0.584, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762826246354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762826246352}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: mg4
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762883398626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762883398627}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762883398629}
  - component: {fileID: 667309762883398628}
  m_Layer: 0
  m_Name: Flash (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762883398627
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762883398626}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762883398626}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762883398629
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762883398626}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762883398628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762883398626}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762888709738
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762888709739}
  - component: {fileID: 667309************}
  - component: {fileID: 667309762888709741}
  - component: {fileID: 667309762888709740}
  m_Layer: 0
  m_Name: Flash (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762888709739
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762888709738}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762888709738}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309762888709741
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762888709738}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309762888709740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762888709738}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309762929054617
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762929054618}
  - component: {fileID: 667309762929054619}
  m_Layer: 0
  m_Name: main2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762929054618
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762929054617}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.36740002, y: 1.2842, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763117644644}
  - {fileID: 667309762957543107}
  - {fileID: 667309762464599690}
  m_Father: {fileID: 667309764485725370}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762929054619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762929054617}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: main2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762952218065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762952218066}
  - component: {fileID: 667309762952218067}
  m_Layer: 0
  m_Name: bone26
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762952218066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762952218065}
  m_LocalRotation: {x: 0, y: 0, z: 0.32960826, w: 0.9441178}
  m_LocalPosition: {x: 0.2086, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763846216696}
  m_Father: {fileID: 667309763076043819}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762952218067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762952218065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone26
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762957543106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762957543107}
  - component: {fileID: 667309762957543108}
  m_Layer: 0
  m_Name: wing2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762957543107
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762957543106}
  m_LocalRotation: {x: 0, y: 0, z: -0.9555875, w: 0.29470757}
  m_LocalPosition: {x: -0.18139999, y: -0.1572, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762929054618}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762957543108
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762957543106}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: wing2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762967657521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762967657522}
  - component: {fileID: 667309762967657523}
  m_Layer: 0
  m_Name: mg1
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762967657522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762967657521}
  m_LocalRotation: {x: 0, y: 0, z: 0.07010469, w: 0.99753964}
  m_LocalPosition: {x: 3.0604, y: 0.84739995, z: 0}
  m_LocalScale: {x: 0.419, y: 0.419, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762967657523
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762967657521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: mg1
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309762981822891
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309762981822892}
  - component: {fileID: 667309762981822893}
  m_Layer: 0
  m_Name: laserGun2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309762981822892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762981822891}
  m_LocalRotation: {x: 0, y: 0, z: 0.0034033854, w: 0.9999942}
  m_LocalPosition: {x: 1.5864999, y: -0.7211, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763229488963}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309762981822893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309762981822891}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: laserGun2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763024944849
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763024944850}
  - component: {fileID: 667309************}
  m_Layer: 0
  m_Name: Bar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763024944850
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763024944849}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.8105, y: -0.0006, z: 0}
  m_LocalScale: {x: 1.0782706, y: 1.4782016, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764506223790}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763024944849}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 164d26bce6c814bbcaf1f025792dc08c, type: 3}
  m_Color: {r: 0, g: 1, b: 0, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.98, y: 0.15}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &667309763076043818
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763076043819}
  - component: {fileID: 667309763076043820}
  m_Layer: 0
  m_Name: bone25
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763076043819
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763076043818}
  m_LocalRotation: {x: 0, y: 0, z: 0.16702689, w: 0.9859524}
  m_LocalPosition: {x: 0.2672, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762952218066}
  m_Father: {fileID: 667309764555009544}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763076043820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763076043818}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone25
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763103698569
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763103698570}
  m_Layer: 0
  m_Name: SkeletonUtility-SkeletonRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763103698570
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763103698569}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763311740204}
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &667309763103978276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763103978277}
  - component: {fileID: 667309763103978278}
  m_Layer: 0
  m_Name: bone16
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763103978277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763103978276}
  m_LocalRotation: {x: 0, y: 0, z: -0.28786203, w: 0.95767194}
  m_LocalPosition: {x: 0.5397, y: 0.0011999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762436593055}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763103978278
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763103978276}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone16
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763117644643
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763117644644}
  - component: {fileID: 667309763117644645}
  m_Layer: 0
  m_Name: wing1
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763117644644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763117644643}
  m_LocalRotation: {x: 0, y: 0, z: -0.95566463, w: 0.29445738}
  m_LocalPosition: {x: -0.2727, y: 0.8419, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762929054618}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763117644645
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763117644643}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: wing1
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763121978623
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763121978608}
  - component: {fileID: 667309763121978611}
  - component: {fileID: 667309************}
  - component: {fileID: 667309763121978609}
  m_Layer: 0
  m_Name: BerserkSkeleton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &667309763121978608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763121978623}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 4.7619047, y: 4.7619047, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &667309763121978611
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763121978623}
  m_Mesh: {fileID: 0}
--- !u!23 &667309************
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763121978623}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33084a349a5a949e79f32df3a827bcaf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &667309763121978609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763121978623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 6471575adc84545968dce751a2c420b3, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 0
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: berserk
  loop: 1
  timeScale: 1
--- !u!1 &667309763147427141
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763147427142}
  - component: {fileID: 667309763147427143}
  m_Layer: 0
  m_Name: rocketLauncher
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763147427142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763147427141}
  m_LocalRotation: {x: 0, y: 0, z: 0.13951877, w: 0.9902194}
  m_LocalPosition: {x: -0.1206, y: -1.7864999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763682580325}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763147427143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763147427141}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: rocketLauncher
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763174108539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763174108540}
  - component: {fileID: 667309763174108541}
  m_Layer: 0
  m_Name: machineGun
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763174108540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763174108539}
  m_LocalRotation: {x: 0, y: 0, z: 0.02949179, w: 0.99956506}
  m_LocalPosition: {x: 2.2833998, y: 1.0236, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764096244749}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763174108541
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763174108539}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: machineGun
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763225369386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763225369387}
  - component: {fileID: 667309763225369388}
  m_Layer: 0
  m_Name: bone13
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763225369387
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763225369386}
  m_LocalRotation: {x: 0, y: 0, z: -0.24826725, w: 0.9686916}
  m_LocalPosition: {x: 0.566, y: 0.0005, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764473399302}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763225369388
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763225369386}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone13
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763229488962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763229488963}
  - component: {fileID: 667309763229488964}
  m_Layer: 0
  m_Name: laserGun
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763229488963
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763229488962}
  m_LocalRotation: {x: 0, y: 0, z: 0.093673915, w: 0.99560297}
  m_LocalPosition: {x: 1.1453999, y: -1.5069, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762981822892}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763229488964
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763229488962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: laserGun
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763270795458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763270795459}
  - component: {fileID: 667309763270795460}
  m_Layer: 0
  m_Name: planeBody
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763270795459
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763270795458}
  m_LocalRotation: {x: 0, y: 0, z: -0.09706179, w: 0.99527836}
  m_LocalPosition: {x: -1.2997999, y: 2.129, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763322061715}
  - {fileID: 667309762483490819}
  - {fileID: 667309764500009122}
  - {fileID: 667309764384555027}
  - {fileID: 667309763985471522}
  - {fileID: 667309762826246353}
  - {fileID: 667309762967657522}
  - {fileID: 667309762610630324}
  - {fileID: 667309764060531493}
  - {fileID: 667309762442842890}
  - {fileID: 667309763625051951}
  - {fileID: 667309763229488963}
  - {fileID: 667309763174108540}
  - {fileID: 667309762522192959}
  - {fileID: 667309763938354759}
  - {fileID: 667309763147427142}
  m_Father: {fileID: 667309764485725370}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763270795460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763270795458}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: planeBody
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763287736267
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763287736268}
  - component: {fileID: 667309763287736269}
  m_Layer: 0
  m_Name: inplace
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763287736268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763287736267}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.25239998, y: -2.3319, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764485725370}
  m_Father: {fileID: 667309763311740204}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763287736269
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763287736267}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: inplace
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763311740203
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763311740204}
  - component: {fileID: 667309763311740205}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763311740204
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763311740203}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763287736268}
  m_Father: {fileID: 667309763103698570}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763311740205
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763311740203}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: root
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763322061714
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763322061715}
  - component: {fileID: 667309763322061716}
  m_Layer: 0
  m_Name: backFire
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763322061715
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763322061714}
  m_LocalRotation: {x: 0, y: 0, z: -0.99527836, w: 0.09706184}
  m_LocalPosition: {x: -0.30159998, y: -0.5699, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763322061716
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763322061714}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: backFire
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763339717806
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763339717807}
  - component: {fileID: 667309763339717792}
  m_Layer: 0
  m_Name: move
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763339717807
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763339717806}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.0047, y: -1.1969, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764485725370}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763339717792
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763339717806}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: move
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763358248133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763358248134}
  - component: {fileID: 667309763358248135}
  m_Layer: 0
  m_Name: bone22
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763358248134
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763358248133}
  m_LocalRotation: {x: 0, y: 0, z: 0.017103393, w: 0.99985373}
  m_LocalPosition: {x: 0.5539, y: -0.00029999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762444435495}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763358248135
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763358248133}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone22
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763404838296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763404838297}
  - component: {fileID: 667309763404838298}
  m_Layer: 0
  m_Name: bone
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763404838297
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763404838296}
  m_LocalRotation: {x: 0, y: 0, z: 0.53891784, w: 0.84235835}
  m_LocalPosition: {x: 1.3281, y: 0.65269995, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762705573657}
  - {fileID: 667309762490536417}
  m_Father: {fileID: 667309762667797735}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763404838298
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763404838296}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763407509104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763407509105}
  - component: {fileID: 667309763407509106}
  m_Layer: 0
  m_Name: bone6
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763407509105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763407509104}
  m_LocalRotation: {x: 0, y: 0, z: -0.090719484, w: 0.9958765}
  m_LocalPosition: {x: 0.2076, y: -0.003, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763786751709}
  m_Father: {fileID: 667309762705573657}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763407509106
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763407509104}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone6
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763440122282
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763440122284}
  - component: {fileID: 667309763440122283}
  - component: {fileID: 667309763440122286}
  - component: {fileID: 667309763440122285}
  - component: {fileID: 667309763440122287}
  - component: {fileID: 667309762899849626}
  - component: {fileID: 667309762899849627}
  m_Layer: 0
  m_Name: Player
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763440122284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.21, y: 0.21, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762631197921}
  - {fileID: 667309763121978608}
  - {fileID: 667309764506223790}
  - {fileID: 2884121244670542002}
  - {fileID: 667309762668074909}
  - {fileID: 667309764526756296}
  - {fileID: 989503945}
  - {fileID: 242604391}
  - {fileID: 8603529748099687706}
  - {fileID: 1764865220518673375}
  - {fileID: 7716261333639564909}
  - {fileID: 498897710905548548}
  - {fileID: 8018116828521727138}
  - {fileID: 2212650323664856092}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763440122283
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a412a5adad72c4e9bb438f7ccaed9efa, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonAnimationTran: {fileID: 0}
  skeletonAnimation: {fileID: 667309762631197934}
  berserkSkeleton: {fileID: 667309763121978609}
  effectsSkeleton: {fileID: 3644379714666109128}
  dryShotSmokes:
  - {fileID: 5173995207649535140}
  - {fileID: 5173995206940880954}
  - {fileID: 5173995207662050317}
  idle: idle
  flying: flying
  flip: flip
  dash: dash
  shooting: shoot
  plasmaShooting: plasmaShoot
  hitTextGO: {fileID: 433070136442887909, guid: 81489800b61834cca92aec6a57312e78,
    type: 3}
  musicParticle: {fileID: 4007464801609088637}
  berzerkTrail: {fileID: 8603529748099687705}
  mechParticle: {fileID: 2724011708693498480}
  lowHealthSmoke: {fileID: 1764865220518673372}
  boostStartingParticle: {fileID: 8043039907547388830}
  dashTransform: {fileID: 242604391}
  boostTransform: {fileID: 4910560111926559989}
  dashAnimator: {fileID: **********}
  healthRegenAnimator: {fileID: 7716261333639564899}
  playerHud: {fileID: 0}
  weapon: {fileID: 0}
  playerMovement: {fileID: 0}
  playerPowerUp: {fileID: 0}
  secondaryWeapon: {fileID: 0}
  poisonPlayer: 0
  isBerzerkMode: 0
  healedAmount: 0
  attackSpeedMultiplier: 0
  canHit: 1
  tweenId: 
  schedulerId: 
  dashSmall: {fileID: 667309764337975841}
  defaultMaxHealth: 0
  defaultAttack: 0
  defaultSpeed: 0
  defaultEnergy: 0
  defaultEnergyRegen: 0
  defaultAttackScope: 0
  defaultAttaceDistance: 0
  defaultDamageReduction: 0
  defaultAddDamage: 0
  defaultBulletSpeedAddRate: 0
  defaultAddAttack: 0
  defaultBulletDamageAddRate: 0
  defaultRageModeTime: 0
  defaultMaxEnergy: 0
  defaultRageAddDamage: 0
  defaultAttackDistance: 0
  tempSaveSpeed: 0
  tempSaveAttackSpeed: 0
  enemyCountMulte: 0
  enemyScreenCountMulte: 0
  curWhiteCatIndex: 0
  healthBarRect: {fileID: 667309763024944850}
  energyBarRect: {fileID: 5731993262811258560}
  playerCopyTran: {fileID: 0}
  Sprites:
  - Name: laserParticles01
    Sprite: {fileID: 21300000, guid: 369eef41660eaca4b84459e51d758829, type: 3}
  - Name: laserParticles04
    Sprite: {fileID: 21300000, guid: 0ee46f30f25fd594190d3caa3aa37102, type: 3}
  - Name: daodan
    Sprite: {fileID: 21300000, guid: 4cb30d45d740a1140a2fb17ffe9ff3d8, type: 3}
  isWhiteCatType: 0
  ListIndex: 0
--- !u!114 &667309763440122286
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 618749c67684c4681b843908e9d2faa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inDash: 0
  allowDash: 1
  playerDir: 0
  rotationDir: 0
  rightStickVector: {x: 0, y: 0}
  virtualJoystick: {fileID: 0}
  rightJoystick: {fileID: 0}
  movement:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  scheduleUpdate: 0
  assistDirAngle: 0
--- !u!114 &667309763440122285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d411925d6779f47a78d03160e00daf61, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gunType: 0
  player: {fileID: 667309763440122283}
  plasmaAnimatorController: {fileID: 9100000, guid: c123d731dcd0e4dad947c0773b6249af,
    type: 2}
  bulletSprites:
  - {fileID: 820357171280843453, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
  - {fileID: 3589697798265019642, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
  - {fileID: 4419726799460948409, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
  - {fileID: 21300000, guid: bd4df9f7718b84f4089656ed572a9606, type: 3}
  - {fileID: 21300000, guid: 097825ed6de02874a9e9147d876a1d50, type: 3}
  - {fileID: 21300000, guid: 00546bab6a46a0948b1a7896e580fb1d, type: 3}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 21300000, guid: b4ce44c6ad629fd4abcf7ece0b03c926, type: 3}
  - {fileID: 21300000, guid: e213d321338ac814eb75d35463a4f207, type: 3}
  bulletFlashParent: {fileID: 667309764442415353}
  multiCanonEffectPrefab: {fileID: 5287985393901670079, guid: 7f58bb5f5d8bf427289ff8a2dfdb2af8,
    type: 3}
  plasmaEffectPrefab: {fileID: 2181032571865097286, guid: af171ef483e844191b88590ea5bd746a,
    type: 3}
  rocketEffectPrefab: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
    type: 3}
  energyCostFromPlist: 0
  areaFromPlist: 0
  bulletDamageFromPlist: 0
  isShooting: 0
  skyBoomPrefab: {fileID: 9212551931481596051, guid: 5e80c4bca9a69bf428bade8486a2a908,
    type: 3}
  skyBoomMaxPrefab: {fileID: 7983814382682081620, guid: c5d17ff03fab92b43bf4672b372a6af8,
    type: 3}
  skyMissilePrefab: {fileID: 1539955078420650, guid: 0a1de1125d50cc24db82974085ced17d,
    type: 3}
  realSkillSpeed: 0
  defaultDamege: 0
  laserStartPrefab: {fileID: 8012445280604743254, guid: 6ec483483b9b740709158a3ffd15a99c,
    type: 3}
  laserPrefab: {fileID: 6191478190310119919, guid: ********************************,
    type: 3}
  laserImpactPrefab: {fileID: 3959906526475236273, guid: cf2b774e04952406496b9226a6f17983,
    type: 3}
  laserStartParticlePrefab: {fileID: 4322016676894815937, guid: 32b40adbd84f247c4bd318ed2276c67f,
    type: 3}
  laserStartGlowPrefab: {fileID: 5809100867784736880, guid: 3ffba8cc63fc4449da48a44d2b0b4b45,
    type: 3}
--- !u!114 &667309763440122287
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70e2c4ff3031c4fc793d2b5737bd97ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gunType: 0
  player: {fileID: 667309763440122283}
  flameThrowerEffectPrefab: {fileID: 7723449546038878322, guid: 79e71cc72701c496cb75ceedf9bc4f66,
    type: 3}
  flameThrowerPrefab: {fileID: 2142410227211267765, guid: 052e41777c3724f2ab6855de6b42b363,
    type: 3}
  backfireEffectPrefab: {fileID: 6442452967579424500, guid: 0a04ab543e5104a57a9ed7203cf0c259,
    type: 3}
  specialWingsAnim: {fileID: 667309764526756297}
  protonUlti: {fileID: 667309762668074910}
  coinsPowerupPrefab: {fileID: 590399828980730483, guid: b3c447dfdfd5741cea8f9486a902a2ac,
    type: 3}
  bulletSprites:
  - {fileID: 21300000, guid: bd4df9f7718b84f4089656ed572a9606, type: 3}
--- !u!114 &667309762899849626
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &667309762899849627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763440122282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfce4f7dc7cc1402c9980f17c6ab74c9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  bulletFlashParent: {fileID: 667309764442415353}
  flameThrowerPrefab: {fileID: 2142410227211267765, guid: 052e41777c3724f2ab6855de6b42b363,
    type: 3}
  backfireEffectPrefab: {fileID: 6442452967579424500, guid: 0a04ab543e5104a57a9ed7203cf0c259,
    type: 3}
  laserPrefab: {fileID: 6191478190310119919, guid: ********************************,
    type: 3}
  laserImpactPrefab: {fileID: 3959906526475236273, guid: cf2b774e04952406496b9226a6f17983,
    type: 3}
  flameThrowerEffectPrefab: {fileID: 6442452967579424500, guid: 0a04ab543e5104a57a9ed7203cf0c259,
    type: 3}
  protonBulletSprite: {fileID: 21300000, guid: bd4df9f7718b84f4089656ed572a9606, type: 3}
--- !u!1 &667309763544839753
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763544839757}
  - component: {fileID: 667309************}
  - component: {fileID: 667309763544839755}
  - component: {fileID: 667309763544839754}
  m_Layer: 0
  m_Name: Flash (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763544839757
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763544839753}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763544839753}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309763544839755
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763544839753}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309763544839754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763544839753}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309763582241475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763582241476}
  - component: {fileID: 667309763582241477}
  m_Layer: 0
  m_Name: glassis2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763582241476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763582241475}
  m_LocalRotation: {x: 0, y: 0, z: -0.19603172, w: 0.98059756}
  m_LocalPosition: {x: 0.559, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763692245739}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763582241477
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763582241475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: glassis2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763625051950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763625051951}
  - component: {fileID: 667309763625051936}
  m_Layer: 0
  m_Name: glass
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763625051951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763625051950}
  m_LocalRotation: {x: 0, y: 0, z: 0.8043238, w: 0.59419125}
  m_LocalPosition: {x: 1.2958, y: 0.6507, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763625051936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763625051950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: glass
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763682580324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763682580325}
  - component: {fileID: 667309763682580326}
  m_Layer: 0
  m_Name: ROCKET
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763682580325
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763682580324}
  m_LocalRotation: {x: 0, y: 0, z: -0.042834725, w: 0.9990822}
  m_LocalPosition: {x: 2.7308998, y: -0.3123, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763147427142}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763682580326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763682580324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: ROCKET
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763692245738
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763692245739}
  - component: {fileID: 667309763692245740}
  m_Layer: 0
  m_Name: glassis
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763692245739
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763692245738}
  m_LocalRotation: {x: 0, y: 0, z: -0.53663707, w: 0.8438132}
  m_LocalPosition: {x: 0.2922, y: 1.0058999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763582241476}
  m_Father: {fileID: 667309762667797735}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763692245740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763692245738}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: glassis
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763786751708
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763786751709}
  - component: {fileID: 667309763786751710}
  m_Layer: 0
  m_Name: bone9
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763786751709
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763786751708}
  m_LocalRotation: {x: 0, y: 0, z: -0.11502384, w: 0.9933627}
  m_LocalPosition: {x: 0.18409999, y: 0.0027, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763407509105}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763786751710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763786751708}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone9
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763827171338
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763827171339}
  - component: {fileID: 667309************}
  - component: {fileID: 667309763827171341}
  - component: {fileID: 667309763827171340}
  m_Layer: 0
  m_Name: Flash (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763827171339
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763827171338}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763827171338}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309763827171341
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763827171338}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309763827171340
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763827171338}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309763846216647
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763846216696}
  - component: {fileID: 667309763846216697}
  m_Layer: 0
  m_Name: bone27
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763846216696
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763846216647}
  m_LocalRotation: {x: 0, y: 0, z: 0.33882004, w: 0.9408512}
  m_LocalPosition: {x: 0.22399999, y: 0.00029999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762952218066}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763846216697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763846216647}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone27
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763886528357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763886528358}
  - component: {fileID: 667309763886528359}
  m_Layer: 0
  m_Name: bone18
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763886528358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763886528357}
  m_LocalRotation: {x: 0, y: 0, z: -0.23471801, w: 0.97206354}
  m_LocalPosition: {x: -0.41799998, y: 0.2891, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764517461834}
  m_Father: {fileID: 667309762589599231}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763886528359
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763886528357}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone18
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763900886873
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763900886874}
  - component: {fileID: 667309************}
  - component: {fileID: 667309763900886876}
  - component: {fileID: 667309763900886875}
  m_Layer: 0
  m_Name: Flash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763900886874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763900886873}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763900886873}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309763900886876
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763900886873}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309763900886875
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763900886873}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309763907217661
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763907217662}
  - component: {fileID: 667309763907217663}
  m_Layer: 0
  m_Name: bone11
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763907217662
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763907217661}
  m_LocalRotation: {x: 0, y: 0, z: 0.7004734, w: 0.7136785}
  m_LocalPosition: {x: 0.0751, y: -0.6332, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764473399302}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763907217663
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763907217661}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone11
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763926758619
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763926758620}
  - component: {fileID: 667309763926758621}
  m_Layer: 0
  m_Name: rightArm
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763926758620
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763926758619}
  m_LocalRotation: {x: 0, y: 0, z: 0.94330955, w: 0.33191425}
  m_LocalPosition: {x: -0.0011, y: 0.5118, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764564057660}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763926758621
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763926758619}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: rightArm
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763938354758
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763938354759}
  - component: {fileID: 667309763938354808}
  m_Layer: 0
  m_Name: protonShield
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763938354759
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763938354758}
  m_LocalRotation: {x: 0, y: 0, z: 0.08811198, w: 0.99611056}
  m_LocalPosition: {x: -0.617, y: -0.1754, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763938354808
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763938354758}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: protonShield
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309763985471521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309763985471522}
  - component: {fileID: 667309763985471523}
  m_Layer: 0
  m_Name: mg3
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309763985471522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763985471521}
  m_LocalRotation: {x: 0, y: 0, z: 0.06984354, w: 0.997558}
  m_LocalPosition: {x: 1.0968, y: -0.4788, z: 0}
  m_LocalScale: {x: 0.818, y: 0.818, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309763985471523
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309763985471521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: mg3
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764052602379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764052602380}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764052602382}
  - component: {fileID: 667309764052602381}
  m_Layer: 0
  m_Name: Flash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764052602380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764052602379}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764052602379}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764052602382
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764052602379}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764052602381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764052602379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764060531492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764060531493}
  - component: {fileID: 667309764060531494}
  m_Layer: 0
  m_Name: flameGun
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764060531493
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764060531492}
  m_LocalRotation: {x: 0, y: 0, z: 0.11268296, w: 0.993631}
  m_LocalPosition: {x: 1.5805999, y: -1.5311999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762807641379}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764060531494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764060531492}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: flameGun
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764070764571
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764070764575}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764070764573}
  - component: {fileID: 667309764070764572}
  m_Layer: 0
  m_Name: Flash (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764070764575
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764070764571}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764070764571}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764070764573
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764070764571}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764070764572
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764070764571}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764096244748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764096244749}
  - component: {fileID: 667309764096244750}
  m_Layer: 0
  m_Name: machineGun2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764096244749
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764096244748}
  m_LocalRotation: {x: 0, y: 0, z: 0.06845061, w: 0.99765456}
  m_LocalPosition: {x: 0.0858, y: 0.56439996, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762612685621}
  m_Father: {fileID: 667309763174108540}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764096244750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764096244748}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: machineGun2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764109322654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764109322655}
  - component: {fileID: 667309764109322640}
  m_Layer: 0
  m_Name: leftArm
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764109322655
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764109322654}
  m_LocalRotation: {x: 0, y: 0, z: -0.72356975, w: 0.6902513}
  m_LocalPosition: {x: 0.0148, y: -0.475, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762670609585}
  m_Father: {fileID: 667309764500009122}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764109322640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764109322654}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: leftArm
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764246858309
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764246858310}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764246858360}
  - component: {fileID: 667309764246858311}
  m_Layer: 0
  m_Name: Flash (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764246858310
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764246858309}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764246858309}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764246858360
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764246858309}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764246858311
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764246858309}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764256894222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764256894223}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764256894209}
  - component: {fileID: 667309764256894208}
  m_Layer: 0
  m_Name: Flash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764256894223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764256894222}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764256894222}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764256894209
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764256894222}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764256894208
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764256894222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764314841985
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764314841986}
  - component: {fileID: 667309764314841987}
  m_Layer: 0
  m_Name: bone2
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764314841986
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764314841985}
  m_LocalRotation: {x: 0, y: 0, z: -0.27588898, w: 0.9611895}
  m_LocalPosition: {x: -0.0513, y: -0.7589, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762667797735}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764314841987
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764314841985}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone2
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764337975841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764337975842}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764337975843}
  m_Layer: 0
  m_Name: Dash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &667309764337975842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764337975841}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -14.8, y: 1.5, z: 0}
  m_LocalScale: {x: 1, y: 7.5, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764337975841}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.48, y: 3.5}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 0
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764337975843
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764337975841}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 1676fc79c1a4c4078a2be8c4666715c4, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!1 &667309764341450172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764341450173}
  - component: {fileID: 667309764341450174}
  m_Layer: 0
  m_Name: DEATOMIZER
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764341450173
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764341450172}
  m_LocalRotation: {x: 0, y: 0, z: -0.69069314, w: 0.723148}
  m_LocalPosition: {x: 0.9371, y: -1.3253, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309762522192959}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764341450174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764341450172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: DEATOMIZER
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764355875666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764355875667}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764355875669}
  - component: {fileID: 667309764355875668}
  m_Layer: 0
  m_Name: Flash (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764355875667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764355875666}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764355875666}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764355875669
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764355875666}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764355875668
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764355875666}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764374716771
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764374716775}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764374716773}
  - component: {fileID: 667309764374716772}
  m_Layer: 0
  m_Name: Flash (12)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764374716775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764374716771}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764374716771}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764374716773
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764374716771}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764374716772
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764374716771}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764384555026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764384555027}
  - component: {fileID: 667309764384555028}
  m_Layer: 0
  m_Name: frontwing
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764384555027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764384555026}
  m_LocalRotation: {x: 0, y: 0, z: -0.90575385, w: 0.42380416}
  m_LocalPosition: {x: -0.0409, y: -0.053299997, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764384555028
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764384555026}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: frontwing
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764409736436
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764409736437}
  - component: {fileID: 667309764409736438}
  m_Layer: 0
  m_Name: bone20
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764409736437
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764409736436}
  m_LocalRotation: {x: 0, y: 0, z: 0.12282206, w: 0.9924287}
  m_LocalPosition: {x: 0.4063, y: 0.0088, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762444435495}
  m_Father: {fileID: 667309764517461834}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764409736438
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764409736436}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone20
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764442415353
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764442415354}
  m_Layer: 0
  m_Name: GunFlashPool
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764442415354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764442415353}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764256894223}
  - {fileID: 667309764052602380}
  - {fileID: 667309763900886874}
  - {fileID: 667309762435830932}
  - {fileID: 667309762532953938}
  - {fileID: 667309764355875667}
  - {fileID: 667309764490747493}
  - {fileID: 667309763544839757}
  - {fileID: 667309762888709739}
  - {fileID: 667309763827171339}
  - {fileID: 667309762704178323}
  - {fileID: 667309764070764575}
  - {fileID: 667309762883398627}
  - {fileID: 667309764246858310}
  - {fileID: 667309762794032580}
  - {fileID: 667309764374716775}
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &667309764473399301
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764473399302}
  - component: {fileID: 667309764473399303}
  m_Layer: 0
  m_Name: bone12
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764473399302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764473399301}
  m_LocalRotation: {x: 0, y: 0, z: -0.13623431, w: 0.99067664}
  m_LocalPosition: {x: 0.60139996, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763225369387}
  m_Father: {fileID: 667309763907217662}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764473399303
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764473399301}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone12
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764485725369
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764485725370}
  - component: {fileID: 667309764485725371}
  m_Layer: 0
  m_Name: main
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: d1de1604dfe4cb64c9d31246a8e43c78, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764485725370
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764485725369}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.2594, y: 0.8022, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763270795459}
  - {fileID: 667309762929054618}
  - {fileID: 667309763339717807}
  m_Father: {fileID: 667309763287736268}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764485725371
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764485725369}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: main
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764490747492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764490747493}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764490747495}
  - component: {fileID: 667309764490747494}
  m_Layer: 0
  m_Name: Flash (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764490747493
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764490747492}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309764442415354}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764490747492}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 1
  m_Sprite: {fileID: 1493436792003014023, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.37, y: 0.25}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764490747495
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764490747492}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 43d901645f94c4f1a9dc657ad2eb3d06, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &667309764490747494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764490747492}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 932000e8f0a3244b1ba6baac3b91cb2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &667309764500009121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764500009122}
  - component: {fileID: 667309764500009123}
  m_Layer: 0
  m_Name: kitBody
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764500009122
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764500009121}
  m_LocalRotation: {x: 0, y: 0, z: 0.7673331, w: 0.6412488}
  m_LocalPosition: {x: 0.6234, y: 0.9736, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309762667797735}
  - {fileID: 667309764109322655}
  - {fileID: 667309763926758620}
  - {fileID: 667309763907217662}
  - {fileID: 667309762664136270}
  - {fileID: 667309762589599231}
  m_Father: {fileID: 667309763270795459}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764500009123
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764500009121}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: kitBody
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764506223789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764506223790}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764506223791}
  m_Layer: 0
  m_Name: HealthBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764506223790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764506223789}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 7.28, z: 0}
  m_LocalScale: {x: 7.5, y: 3, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763024944850}
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764506223789}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3a7c4ebeb4345483dabc96707cc1fd1d, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.68, y: 0.33}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!114 &667309764506223791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764506223789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4137e06c52a124d22ae110c486f87916, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  health: {fileID: 667309************}
  bar: {fileID: 667309763024944849}
  ScaleRatio: 0
--- !u!1 &667309764517461833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764517461834}
  - component: {fileID: 667309764517461835}
  m_Layer: 0
  m_Name: bone19
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764517461834
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764517461833}
  m_LocalRotation: {x: 0, y: 0, z: 0.09801714, w: 0.9951847}
  m_LocalPosition: {x: 0.4411, y: 0.0015, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309764409736437}
  m_Father: {fileID: 667309763886528358}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764517461835
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764517461833}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone19
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764526756311
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764526756296}
  - component: {fileID: 667309************}
  - component: {fileID: 667309764526756297}
  m_Layer: 0
  m_Name: wings1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764526756296
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764526756311}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 9.523809, y: 9.523809, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &667309************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764526756311}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 6562703486219835928, guid: fd0e509e0bcb94aa8acdd9eceb6972ce,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.01, y: 0.01}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &667309764526756297
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764526756311}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 6b60a5983315c4814bc967f2b65d23ac, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!1 &667309764555009559
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764555009544}
  - component: {fileID: 667309764555009545}
  m_Layer: 0
  m_Name: bone24
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764555009544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764555009559}
  m_LocalRotation: {x: 0, y: 0, z: 0.32168734, w: 0.94684595}
  m_LocalPosition: {x: 0.2262, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 667309763076043819}
  m_Father: {fileID: 667309762490536417}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764555009545
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764555009559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone24
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &667309764564057659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 667309764564057660}
  - component: {fileID: 667309764564057661}
  m_Layer: 0
  m_Name: bone4
  m_TagString: Untagged
  m_Icon: {fileID: 2800000, guid: 97a43f11e00735147a9dc3dff6d68191, type: 3}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &667309764564057660
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764564057659}
  m_LocalRotation: {x: 0, y: 0, z: 0.87440807, w: 0.48519123}
  m_LocalPosition: {x: 0.2341, y: 0.49179998, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763926758620}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &667309764564057661
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 667309764564057659}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b238dfcde8209044b97d23f62bcaadf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneName: bone4
  parentReference: {fileID: 0}
  mode: 0
  position: 1
  rotation: 1
  scale: 1
  zPosition: 1
  overrideAlpha: 1
--- !u!1 &837830512336057586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 608398270575094101}
  - component: {fileID: 8633645************}
  - component: {fileID: 7003440184483112589}
  m_Layer: 0
  m_Name: Boost
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &608398270575094101
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 837830512336057586}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.59400004, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4910560111926559989}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &8633645************
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 837830512336057586}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: -1039792837012191275, guid: a1de3c56433ec498f8971ffc07fa46ba,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.54, y: 0.67}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &7003440184483112589
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 837830512336057586}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: fb75b466c39a5471bbb0ba4efae01d74, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!1 &1661979660847848752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4910560111926559989}
  m_Layer: 0
  m_Name: PlayerBoost
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4910560111926559989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1661979660847848752}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.85, y: 0.05, z: 0}
  m_LocalScale: {x: 4, y: 4, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 608398270575094101}
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2412576488352192213
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 498897710905548548}
  - component: {fileID: 6757753795844964247}
  - component: {fileID: 1580028654237889014}
  - component: {fileID: 1257901912595737825}
  m_Layer: 0
  m_Name: Spine GameObject (shopGlows)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &498897710905548548
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2412576488352192213}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.37142858, y: 0.9333333, z: 0}
  m_LocalScale: {x: 11.904762, y: 11.904762, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6757753795844964247
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2412576488352192213}
  m_Mesh: {fileID: 0}
--- !u!23 &1580028654237889014
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2412576488352192213}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &1257901912595737825
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2412576488352192213}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: a6c0a895ab0144555be9ae6c8cce845f, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: upgradeGlow
  loop: 1
  timeScale: 1
--- !u!1 &5587493196797416898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3801057937239887183}
  - component: {fileID: 888956982000425575}
  - component: {fileID: 8708028310705024381}
  - component: {fileID: 3807972162293393503}
  m_Layer: 0
  m_Name: Spine GameObject (enemyFlame)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3801057937239887183
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5587493196797416898}
  m_LocalRotation: {x: 0, y: 0, z: -0.5735764, w: 0.8191521}
  m_LocalPosition: {x: -1.1, y: 1.29, z: 0}
  m_LocalScale: {x: -4.97, y: -4.97, z: 0.8875}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 7986706981185647725}
  m_Father: {fileID: 667309762631197921}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -70}
--- !u!33 &888956982000425575
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5587493196797416898}
  m_Mesh: {fileID: 0}
--- !u!23 &8708028310705024381
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5587493196797416898}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6245cd99f388a41b79ca8dd9e31a41b0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 1356482057
  m_SortingLayer: 5
  m_SortingOrder: -1
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &3807972162293393503
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5587493196797416898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 65e3cdde38e52429b90d25f64551a2ae, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: 
  loop: 1
  timeScale: 1
--- !u!1 &5992082912259210118
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2212650323664856092}
  - component: {fileID: 1744335187672231639}
  - component: {fileID: 5163950595159201753}
  m_Layer: 0
  m_Name: upgrade
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &2212650323664856092
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5992082912259210118}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 7.79}
  m_SizeDelta: {x: 20, y: 5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &1744335187672231639
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5992082912259210118}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 203101340036515291, guid: 3857de423a8f5fb4dba2294d82c681ab, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &5163950595159201753
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5992082912259210118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: dddd
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 3857de423a8f5fb4dba2294d82c681ab, type: 2}
  m_sharedMaterial: {fileID: 203101340036515291, guid: 3857de423a8f5fb4dba2294d82c681ab,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 1744335187672231639}
  m_maskType: 0
--- !u!1 &6585301781880948589
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8018116828521727138}
  - component: {fileID: 6175416765185208480}
  - component: {fileID: 2005450795302581907}
  - component: {fileID: 3644379714666109128}
  m_Layer: 0
  m_Name: Spine GameObject (effects)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &8018116828521727138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6585301781880948589}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 4.7619047, y: 4.7619047, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 667309763440122284}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6175416765185208480
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6585301781880948589}
  m_Mesh: {fileID: 0}
--- !u!23 &2005450795302581907
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6585301781880948589}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6c7893ccc1193440cb647e33d99e6249, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &3644379714666109128
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6585301781880948589}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 30034dee3eb0b43f2adf4fa552e17843, type: 2}
  initialSkinName: 
  initialFlipX: 0
  initialFlipY: 0
  separatorSlotNames: []
  zSpacing: 0
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  _animationName: levelUp
  loop: 1
  timeScale: 1
--- !u!1001 &667309763592167872
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309763440122284}
    m_Modifications:
    - target: {fileID: 7086247425570287520, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_Name
      value: HealthRegen
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.56
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0744ece0745f54bebbf4189b13590c25, type: 3}
--- !u!95 &7716261333639564899 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 7086247425570287523, guid: 0744ece0745f54bebbf4189b13590c25,
    type: 3}
  m_PrefabInstance: {fileID: 667309763592167872}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7716261333639564909 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7086247425570287533, guid: 0744ece0745f54bebbf4189b13590c25,
    type: 3}
  m_PrefabInstance: {fileID: 667309763592167872}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309763757191686
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1662194528}
    m_Modifications:
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingLayer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingOrder
      value: -3
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 1356482057
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: playOnAwake
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.frequency
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.y.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.z.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strength.scalar
      value: 0.19417
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strengthY.scalar
      value: 0.097087376
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 1.4
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strength.minScalar
      value: -0.19417
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strengthY.minScalar
      value: -0.097087
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 0.78
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.5019608
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.b
      value: 0.2509804
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.g
      value: 0.3882353
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.r
      value: 0.43137255
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.a
      value: 0.29803923
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.b
      value: 0.2509804
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.g
      value: 0.3882353
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.r
      value: 0.43137255
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].time
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].value
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inSlope
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].inSlope
      value: -1.3333334
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].inWeight
      value: 0.113358736
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].outSlope
      value: -1.6285939
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].tangentMode
      value: 69
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[0].tangentMode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].tangentMode
      value: 69
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.30181086
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.y
      value: -0.30181086
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.33802816
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8191521
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.5735764
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254303, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_Name
      value: SmokeTrail
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254303, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f6a7bcae63318481db4c649bde05d396, type: 3}
--- !u!4 &104557257342605396 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
    type: 3}
  m_PrefabInstance: {fileID: 667309763757191686}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309763916781769
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 989503945}
    m_Modifications:
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5142856
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_Name
      value: DryShotSmoke (1)
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f446a4585cf434504995941c50631226, type: 3}
--- !u!4 &5173995206940880932 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309763916781769}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &5173995206940880954 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 5660879096010254579, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309763916781769}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309763974705403
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309763440122284}
    m_Modifications:
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1242093137215114025, guid: 391d2fc412ff6416e8fc0108c3d8686a,
        type: 3}
      propertyPath: m_Name
      value: LowHealthSmoke
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 391d2fc412ff6416e8fc0108c3d8686a, type: 3}
--- !u!198 &1764865220518673372 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 1242093137215114023, guid: 391d2fc412ff6416e8fc0108c3d8686a,
    type: 3}
  m_PrefabInstance: {fileID: 667309763974705403}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1764865220518673375 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1242093137215114020, guid: 391d2fc412ff6416e8fc0108c3d8686a,
    type: 3}
  m_PrefabInstance: {fileID: 667309763974705403}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309764101092606
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 989503945}
    m_Modifications:
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5142856
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_Name
      value: DryShotSmoke (2)
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f446a4585cf434504995941c50631226, type: 3}
--- !u!95 &5173995207662050317 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 5660879096010254579, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309764101092606}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5173995207662050323 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309764101092606}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309764155673687
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 989503945}
    m_Modifications:
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 4.7619047
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.5142856
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_Name
      value: DryShotSmoke
      objectReference: {fileID: 0}
    - target: {fileID: 5660879096010254578, guid: f446a4585cf434504995941c50631226,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f446a4585cf434504995941c50631226, type: 3}
--- !u!95 &5173995207649535140 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 5660879096010254579, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309764155673687}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5173995207649535162 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5660879096010254573, guid: f446a4585cf434504995941c50631226,
    type: 3}
  m_PrefabInstance: {fileID: 667309764155673687}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &667309764175900650
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309763440122284}
    m_Modifications:
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625521, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625521, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: frameDelay
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625522, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Name
      value: BerzerkTrail
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625522, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625523, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Time
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625523, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Parameters.widthMultiplier
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625523, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Parameters.widthCurve.m_Curve.Array.data[0].time
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9090271795342625523, guid: a80727f7a6958451697ce4924ba8bfc9,
        type: 3}
      propertyPath: m_Parameters.widthCurve.m_Curve.Array.data[0].value
      value: 0.859375
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a80727f7a6958451697ce4924ba8bfc9, type: 3}
--- !u!96 &8603529748099687705 stripped
TrailRenderer:
  m_CorrespondingSourceObject: {fileID: 9090271795342625523, guid: a80727f7a6958451697ce4924ba8bfc9,
    type: 3}
  m_PrefabInstance: {fileID: 667309764175900650}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &8603529748099687706 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9090271795342625520, guid: a80727f7a6958451697ce4924ba8bfc9,
    type: 3}
  m_PrefabInstance: {fileID: 667309764175900650}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1656014208823992927
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309762631197921}
    m_Modifications:
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: looping
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: lengthInSec
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: playOnAwake
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: moveWithTransform
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startSpeed.scalar
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startRotation.scalar
      value: 6.283185
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startRotation.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startRotationX.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: InitialModule.startRotationY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.77
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 8747615591991556559, guid: 859a3a51c553548f58a22e137b52a6d7,
        type: 3}
      propertyPath: m_Name
      value: BoostStarting
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 859a3a51c553548f58a22e137b52a6d7, type: 3}
--- !u!4 &8043039907547388829 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8747615591991556546, guid: 859a3a51c553548f58a22e137b52a6d7,
    type: 3}
  m_PrefabInstance: {fileID: 1656014208823992927}
  m_PrefabAsset: {fileID: 0}
--- !u!198 &8043039907547388830 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 8747615591991556545, guid: 859a3a51c553548f58a22e137b52a6d7,
    type: 3}
  m_PrefabInstance: {fileID: 1656014208823992927}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3235776964911231042
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309762631197921}
    m_Modifications:
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.104
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398015, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_Name
      value: MusicParticlePlayer
      objectReference: {fileID: 0}
    - target: {fileID: 1980081904638398015, guid: d55e7d297e37347d9a43380b21dcd60c,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d55e7d297e37347d9a43380b21dcd60c, type: 3}
--- !u!1 &4007464801609088637 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1980081904638398015, guid: d55e7d297e37347d9a43380b21dcd60c,
    type: 3}
  m_PrefabInstance: {fileID: 3235776964911231042}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &4007464801609088638 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1980081904638398012, guid: d55e7d297e37347d9a43380b21dcd60c,
    type: 3}
  m_PrefabInstance: {fileID: 3235776964911231042}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3931050843044328427
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 667309762631197921}
    m_Modifications:
    - target: {fileID: 1387187701639707033, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_Name
      value: MechParticlePlayer
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.4
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.4
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.4
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.104
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: InitialModule.startSpeed.scalar
      value: -3
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 1.2
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
        type: 3}
      propertyPath: InitialModule.startSpeed.minScalar
      value: -6
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: af12168bc54584aa58d2568d1553fcd4, type: 3}
--- !u!198 &2724011708693498480 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 1387187701639707035, guid: af12168bc54584aa58d2568d1553fcd4,
    type: 3}
  m_PrefabInstance: {fileID: 3931050843044328427}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &2724011708693498481 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1387187701639707034, guid: af12168bc54584aa58d2568d1553fcd4,
    type: 3}
  m_PrefabInstance: {fileID: 3931050843044328427}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7415123302643801663
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 3801057937239887183}
    m_Modifications:
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingLayer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingOrder
      value: -3
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254288, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 1356482057
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: playOnAwake
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.frequency
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.y.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.z.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strength.scalar
      value: 0.19417
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strengthY.scalar
      value: 0.097087376
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minMaxState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 1.4
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strength.minScalar
      value: -0.19417
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: NoiseModule.strengthY.minScalar
      value: -0.097087
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 0.78
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.5019608
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.b
      value: 0.2509804
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.g
      value: 0.3882353
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.r
      value: 0.43137255
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.a
      value: 0.29803923
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.b
      value: 0.2509804
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.g
      value: 0.3882353
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.r
      value: 0.43137255
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].time
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].value
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inSlope
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].inSlope
      value: -1.3333334
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].inWeight
      value: 0.113358736
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].outSlope
      value: -1.6285939
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].tangentMode
      value: 69
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[0].tangentMode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254289, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: SizeModule.curve.minCurve.m_Curve.Array.data[1].tangentMode
      value: 69
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.30181086
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.y
      value: -0.30181086
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.33802816
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8191521
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.5735764
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254303, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_Name
      value: SmokeTrail
      objectReference: {fileID: 0}
    - target: {fileID: 590451987460254303, guid: f6a7bcae63318481db4c649bde05d396,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f6a7bcae63318481db4c649bde05d396, type: 3}
--- !u!4 &7986706981185647725 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 590451987460254290, guid: f6a7bcae63318481db4c649bde05d396,
    type: 3}
  m_PrefabInstance: {fileID: 7415123302643801663}
  m_PrefabAsset: {fileID: 0}
