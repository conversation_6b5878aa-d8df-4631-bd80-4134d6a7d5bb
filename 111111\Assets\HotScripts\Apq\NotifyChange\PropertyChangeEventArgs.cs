﻿using System;

namespace Apq.NotifyChange
{
    /// <summary>
    /// 属性更改事件参数
    /// </summary>
    public class PropertyChangeEventArgs : EventArgs
    {
        public PropertyChangeEventArgs(string propertyName)
        {
            PropertyName = propertyName;
        }

        /// <summary>
        /// 属性名
        /// </summary>
        public string PropertyName { get; }
        /// <summary>
        /// 指示是否取消(中断后续流程)
        /// </summary>
        public bool Cancel { get; set; }

        /// <summary>
        /// 原值
        /// </summary>
        public object OriginalValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public object NewValue { get; set; }
    }
}
