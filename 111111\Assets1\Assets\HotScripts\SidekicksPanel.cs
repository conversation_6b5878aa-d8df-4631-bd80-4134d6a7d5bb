using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;
using DG.Tweening;
public class SidekicksPanel : MonoBehaviour
{
    [SerializeField] RectTransform background, sidekickScreenRect;
    [SerializeField] Vector2 bgSize;
    [SerializeField] Animator cardAnimator, cardPushAnimator;
    [SerializeField] GameObject equipButton, buyButton, cardCloneButton;
    [SerializeField] Image cardImage, cardCloneImage, recruitImage, recruitCloneImage;
    [SerializeField] Animator sideKicksAnimator;
    [SerializeField] SkeletonGraphic cardEffectSkeleton;
    [SerializeField] SkeletonAnimation[] sidekickSkeletons;
    [SerializeField] CustomButton normalButton, coinsButton;
    [SerializeField] TextMeshProUGUI priceTMP, descriptionTMP, coinsTMP, diamondTMP;
    [SerializeField] SidekickButton[] sidekickButtons;
    [SerializeField] Sprite[] sidekickCards;
    [SerializeField] Sprite noSidekickSprite;
    [SerializeField] Material normalMaterial, greyscaleMaterial;

    public GameObject CoinGo;
    public GameObject DiamondGo;

    private int g_money;
    private int g_diamond;

    int buyAmount;
    private bool isInitialized, unlockAnimationShown;

    SidekickType currentSidekick, selectedSidekick;


    #region
    [HideInInspector]public int CurrentGroupID;
    [HideInInspector] public Equipment.Item CurrentEquipment;
    public GameObject TipPrefab;
    public GameObject TipParent;

    public void ShowTip(string str)
    {
        GameObject clone = Instantiate<GameObject>(TipPrefab, TipParent.transform);
        clone.GetComponent<TextMeshProUGUI>().text = str;
        clone.SetActive(true);
        clone.transform.DOMoveY(3, 1f).OnComplete(()=> { Destroy(clone); });
        
    }

    public void LockTip(int index)
    {
        int unlockLevel = sidekickButtons[index].unlockLevel;
        string str = string.Format("通关主线第{0}关后解锁", unlockLevel);
        ShowTip(str);
    }
    #endregion


    //groupID 1001 - 1006
    // 选第几个，升级第几个
    void Initialize()
    {
        if (LuaToCshapeManager.Instance.CurrentSidekickID > 0)
        {
            CurrentEquipment = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.CurrentSidekickID);
            CurrentGroupID = CurrentEquipment.GroupID - 10001;
        }
        MainMenuController.instance.SetSize(background, bgSize);
        MainMenuController.instance.SetScreenSize(sidekickScreenRect);
        Globals.sidekickType = (SidekickType)CurrentGroupID;  //   PlayerPrefs.GetInt("Category5");
        selectedSidekick = Globals.sidekickType;

        coinsButton.SetButtonColor(coinsButton.YELLOW);

        normalButton.defaultAction = Equip;
        coinsButton.defaultAction = BuyTapped;

        Spine.TrackEntry trackEntry = cardEffectSkeleton.AnimationState.SetAnimation(0, "powerUp2", false);
        trackEntry.TrackTime = 1.9f;

        var missionMap = GameData.instance.GetMissions();
        for (int i = 0; i < missionMap.Count; i++)
        {
            if (!missionMap.ContainsKey("Mission" + i))
                continue;

            int rewardSK = (int)((missionMap["Mission" + i.ToString()] as PList)["Rewards"] as PList)["SideKick"];

            if(rewardSK > 0)
            {
                sidekickButtons[rewardSK - 1].unlockLevel = i;
            }
        }
    }

    public void Init()
    {
        if (!isInitialized)
        {
            isInitialized = true;
            Initialize();
        }

        g_money = LuaManager.Instance.InvokeLuaFunction<int>("BattleManager.GetMoney");
        g_diamond = LuaManager.Instance.InvokeLuaFunction<int>("BattleManager.GetDiamond");
        LuaToCshapeManager.Instance.SidekicksPanel = this;

        UpdateCoins();

        foreach (SkeletonAnimation sidekickSkeleton in sidekickSkeletons)
            sidekickSkeleton.gameObject.SetActive(false);
        currentSidekick = selectedSidekick;
        sidekickSkeletons[(int)currentSidekick].transform.localPosition = Vector3.zero;

        InitSidekickButtons();
        ChangeSidekick((int)currentSidekick);

        bool bought = false;
        if (CurrentEquipment != null && CurrentEquipment.GroupID > 0)
        {
            //Equipment.Item CurrentEquipment = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.CurrentSidekickID);
            //int CurrentGroupID = CurrentEquipment.GroupID - 10001;
            bought = LuaToCshapeManager.Instance.AllHadBuyEquipmentID.Contains(CurrentEquipment.GroupID);
        }

         //PlayerPrefs.GetInt(((SidekickType)0).ToString(), 0) == 1;
        recruitImage.sprite = sidekickButtons[0].isLocked || !bought ? noSidekickSprite
            : sidekickCards[(int)currentSidekick];

        NavigationButton.currentlySelected = sidekickButtons[(int)currentSidekick].button.GetComponent<NavigationButton>();
        NavigationButton.ChangeCurrentlySelected(NavigationButton.currentlySelected, true);
    }

    void InitSidekickButtons()
    {
        for (int i = 0; i < sidekickButtons.Length; i++)
        {
            sidekickButtons[i].button.interactable = true;

            int unlockLevel = sidekickButtons[i].unlockLevel;
            bool isLocked = unlockLevel > GameData.instance.fileHandler.missionsCompleted;

            sidekickButtons[i].lockSkeleton.gameObject.SetActive(isLocked);
            sidekickButtons[i].isLocked = isLocked;
            sidekickButtons[i].button.image.material = isLocked ? greyscaleMaterial : normalMaterial;

            if (unlockLevel <= GameData.instance.fileHandler.missionsCompleted
                && GameData.instance.fileHandler.lastLevelVisitingShop < unlockLevel
                && !sidekickButtons[i].unlockAnimationShown)
            {
                sidekickButtons[i].unlockAnimationShown = true;
                sidekickButtons[i].lockSkeleton.gameObject.SetActive(true);
                sidekickButtons[i].lockSkeleton.transform.localScale *= 0.5f;
                sidekickButtons[i].lockSkeleton.AnimationState.SetAnimation(0, "unlock", false);
                DOTween.Sequence().AppendInterval(0.1f).AppendCallback(()=> { AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.boss_unlock); }).Play();
            }
        }
    }

    public int CheckUnlockedSidekicks()
    {
        if (!isInitialized)
        {
            isInitialized = true;
            Initialize();
        }

        int unlockedSidekicks = 0;

        for (int i = 0; i < sidekickButtons.Length; i++)
        {
            int unlockLevel = sidekickButtons[i].unlockLevel;
            if (unlockLevel <= GameData.instance.fileHandler.missionsCompleted
                && GameData.instance.fileHandler.lastLevelVisitingShop < unlockLevel
                && !sidekickButtons[i].unlockAnimationShown)
            {
                unlockedSidekicks += 1;
            }
        }

        return unlockedSidekicks;
    }

    public void HideSidekick() => sidekickSkeletons[(int)currentSidekick].gameObject.SetActive(false);

    public void SetSidekick(int index)
    {
        sidekickButtons[index].button.Select();

        if (sidekickButtons[index].isLocked || index == (int)currentSidekick)
            return;

        ChangeSidekick(index);
    }

    public void Deselect() => EventSystem.current.SetSelectedGameObject(null);

    void ChangeSidekick(int index)
    {
        sidekickButtons[(int)currentSidekick].button.interactable = true;
        sidekickSkeletons[(int)currentSidekick].gameObject.SetActive(false);
        currentSidekick = (SidekickType)index;
        sidekickButtons[(int)currentSidekick].button.interactable = false;

        string ch = "Gun" + (int)(currentSidekick + 1);
        PList sidekicksData = (GameData.instance.GetShop()["Category5"] as PList)[ch] as PList;

        cardImage.sprite = sidekickCards[(int)currentSidekick];
        cardAnimator.Play("Main", 0, 0);

        sideKicksAnimator.Play("Main", 0, 0);
        GameObject sidekickGameObject = sidekickSkeletons[(int)currentSidekick].gameObject;
        sidekickGameObject.SetActive(true);
        sidekickGameObject.transform.localPosition = Vector3.zero;
        sidekickGameObject.transform.localScale = Vector3.one;
        sidekickSkeletons[(int)currentSidekick].state.SetAnimation(0, "menuIdle", true);

        //buyAmount = (int)(sidekicksData["Price"] as PList)["L1"];
        priceTMP.text = LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].price.ToString();// buyAmount.ToString();
        CoinGo.SetActive(LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].priceType == 2);
        DiamondGo.SetActive(LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].priceType == 1);

        descriptionTMP.text = sidekicksData["Description"] as string;

        bool bought = LuaToCshapeManager.Instance.AllHadBuyEquipmentID.Contains(index + 10001); // PlayerPrefs.GetInt(currentSidekick.ToString(), 0) == 1;
        normalButton.gameObject.SetActive(bought && currentSidekick != selectedSidekick);
        coinsButton.gameObject.SetActive(!bought);
        coinsButton.SetInteractable(!sidekickButtons[(int)currentSidekick].isLocked);
    }
    //装备
    public void SelectSidekick()
    {
        cardCloneImage.sprite = sidekickCards[(int)currentSidekick];
        recruitImage.sprite = sidekickCards[(int)currentSidekick];
        recruitCloneImage.sprite = sidekickCards[(int)currentSidekick];
        PlayerPrefs.SetInt("Category5", (int)currentSidekick);
        LuaManager.Instance.RunLuaFunction<int>("BattleManager.OnClickEuip", (int)currentSidekick);

        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.powerUpCollected);
        selectedSidekick = currentSidekick;
        Globals.sidekickType = selectedSidekick;
        int index = (int)selectedSidekick;
        sidekickSkeletons[index].state.SetAnimation(0, "select", false);

        switch (selectedSidekick)
        {
            case SidekickType.GunKitty:
                sidekickSkeletons[index].state.AddAnimation(0, "idle", true, 0);
                break;
            case SidekickType.Fixit:
                sidekickSkeletons[index].state.AddAnimation(0, "select2", true, 0);
                break;
            case SidekickType.Bowie:
                sidekickSkeletons[index].state.AddAnimation(0, "idle", true, 0);
                break;
            case SidekickType.Katlando:
                sidekickSkeletons[index].state.AddAnimation(0, "idle", true, 0);
                break;
            case SidekickType.Rockem:
                sidekickSkeletons[index].state.AddAnimation(0, "shoot", true, 0);
                break;
            case SidekickType.Zapper:
                sidekickSkeletons[index].state.AddAnimation(0, "idle", true, 0);
                break;
            default:
                break;
        }

        cardAnimator.Play("Default", 0, 0);
        cardPushAnimator.Play("Main", 0, 0);
        StopCoroutine(nameof(PlayCardPushEffect));
        StartCoroutine(nameof(PlayCardPushEffect));

        GameData.instance.fileHandler.SaveData();
    }

    IEnumerator PlayCardPushEffect()
    {
        yield return new WaitForSeconds(0.3f);
        Spine.TrackEntry trackEntry = cardEffectSkeleton.AnimationState.SetAnimation(0, "powerUp2", false);
        trackEntry.TrackTime = 0.7f;
    }
    //购买确认弹窗
    void BuyTapped()
    {
        //判断金币和钻石
        if (LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].priceType == 1)
        {
            if (g_diamond < LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].price)
            {
                //LuaManager.Instance.RunLuaFunction<string>("BattleManager.ShowMessage", "钻石不足");
                MainMenuController.instance.CreatePopup("", "钻石不足，是否前往充值", GoToStore);
                return;
            }
        }
        else
        {
            if (g_money < LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].price)
            {
                //LuaManager.Instance.RunLuaFunction<string>("BattleManager.ShowMessage", "金币不足");
                MainMenuController.instance.CreatePopup("", "金币不足，是否前往充值", GoToStore);
                return;
            }
        }
        string popupString = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["buyPopup"] as string;
        string label = ((GameData.instance.GetShop()["Category5"] as PList)["Gun" + ((int)currentSidekick+1)] as PList)["Label"] as string;

        if (GameData.instance.fileHandler.coins < buyAmount)
        {
            popupString = GameData.instance.GetMenuData(Globals.SHOP_SCENE)["brokePopup"] as string;
            MainMenuController.instance.CreateMessagePopup(label, popupString);
            return;
        }

        MainMenuController.instance.CreatePopup(label, popupString, BuySidekick);

        var returnNavButton = NavigationButton.currentlySelected;
        MainMenuController.instance.popup.addedNoCallback = () =>
        {
            NavigationButton.ChangeCurrentlySelected(returnNavButton, true);
        };
    }
    //跳转到商店
    public void GoToStore()
    {
        LuaManager.Instance.RunLuaFunction<int>("BattleManager.goToMain", 1);
    }

    public void LuaBuySuccess()
    {
        SelectSidekick();
        cardCloneButton.SetActive(false);
        UpdateCoins();
        LuaToCshapeManager.Instance.AllHadBuyEquipmentID.Add(10001 + (int)currentSidekick);
    }

    //点击购买
    public void BuySidekick()
    {
        //PlayerPrefs.SetInt(currentSidekick.ToString(), 1);

        
        GameData.instance.fileHandler.coins -= buyAmount;

        LuaManager.Instance.RunLuaFunction<int>("BattleManager.BuySidekick", LuaToCshapeManager.Instance.StoreData[(int)currentSidekick].itemID);

        UpdateCoins();
        //购买后立即装备是会用问题的
        //SelectSidekick();

        
    }

    public void Equip()
    {
        SelectSidekick();
        cardCloneButton.SetActive(true);
    }

    public void Submit()
    {
        if (sidekickButtons[(int)currentSidekick].isLocked)
            return;

        bool bought = PlayerPrefs.GetInt(currentSidekick.ToString(), 0) == 1;
        if (bought)
        {
            if(currentSidekick != selectedSidekick)
            {
                SelectSidekick();
                cardCloneButton.SetActive(true);
            }
        }
        else
        {
            BuyTapped();
        }
    }

    public void UpdateCoins()
    {
        g_money = LuaManager.Instance.InvokeLuaFunction<int>("BattleManager.GetMoney");
        g_diamond = LuaManager.Instance.InvokeLuaFunction<int>("BattleManager.GetDiamond");
        coinsTMP.text = g_money.ToString();
        diamondTMP.text = g_diamond.ToString();
    }

    public void ShowSidekickWithPlayer(Vector2 playerPosition)
    {
        Transform sidekickTransform = sidekickSkeletons[(int)selectedSidekick].transform;

        sidekickTransform.position = playerPosition
            + new Vector2(2 * Mathf.Cos(200 * Mathf.Deg2Rad), 2 * Mathf.Sin(200 * Mathf.Deg2Rad));
        sidekickTransform.localScale = new Vector3(0.6f, 0.6f, 1);
        sidekickTransform.gameObject.SetActive(true);
        sidekickSkeletons[(int)selectedSidekick].state.SetAnimation(0, "menuIdle", true);
    }

    public void HideSidekickWithPlayer() => sidekickSkeletons[(int)selectedSidekick].gameObject.SetActive(false);
    public void SetSelectedSidekick() => selectedSidekick = (SidekickType)PlayerPrefs.GetInt("Category5");

}

[System.Serializable]
class SidekickButton
{
    public Button button;
    public SkeletonGraphic lockSkeleton;
    [HideInInspector] public int unlockLevel;
    [HideInInspector] public bool isLocked = false;
    [HideInInspector] public bool unlockAnimationShown = false;
}
