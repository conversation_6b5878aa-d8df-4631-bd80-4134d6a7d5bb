using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;
using System;

public class CuteusShip : Enemy
{
    public enum ShipStates
    {
        State1,
        State2,
        State3,
        State4
    };

    [SerializeField] private Sprite arrowSprite;
    [SerializeField] private CuteusMaximus cuteus;
    [SerializeField] private SkeletonAnimation shipTop = null;
    [SerializeField] public SkeletonAnimation captain = null;
    [SerializeField] private SkeletonAnimation flameAnimBig;
    [SerializeField] private SkeletonAnimation flameAnimSmall;
    [SerializeField] private LightningAttack[] lightning;
    [SerializeField] private GameObject lightningContainer;

    [SerializeField] private GameObject bladeContainer;

    [SerializeField] private List<Blade> bladeList = new List<Blade>();
    [SerializeField] private List<SkeletonAnimation> archersList = new List<SkeletonAnimation>();
    [SerializeField] private List<SkeletonAnimation> extraArchersList = new List<SkeletonAnimation>();

    [SerializeField] private Collider2D topCollider;
    [SerializeField] private Collider2D bottomCollider;

    private int lightningCount;
    private Bone bone;
    private Bone bone2;
    private Collider2D bladeCollider;
    private Bounds boundsTop;
    private Bounds boundsBottom;
    private Bounds bladeBound;
    private ShipStateMachine shipStateMachine = null;
    private ShipStates currentState = ShipStates.State1;
    private bool movingLeft;
    private bool lowHealth;
    private bool isDead = false;




    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        InitStateMachine();
        InitStats();
        CreateSprite();
        InitArchers();
        scheduleUpdate = true;
        lowHealth = false;
        bladeContainer.transform.parent = null;
        lightningContainer.transform.parent = null;
    }

    private void InitStateMachine()
    {
        shipStateMachine = ShipStateMachine.Create(this);
        shipStateMachine.AddState<CapStateIdle>();
        shipStateMachine.AddState<CapStateBlue>();
        shipStateMachine.AddState<CapStateRed>();
        shipStateMachine.AddState<CapStateDeath>();
    }

    public void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();
        isBoss = true;
        int bossNumber = 6;
        if (GameManager.instance.missionManager.missionType == "Boss")
        {
            PList vMap = GameData.instance.GetMissions();
            string str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            PList plist = (vMap[str] as PList);
            Globals.gameType = GameType.Arena;
            string bn = System.Convert.ToString(plist["Boss Number"]);
            bossNumber = System.Convert.ToInt32(bn);
            GameData.instance.fileHandler.currentEvent = bossNumber;
            // bossNumber = (int)GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Boss Number"];
        }

        if (Globals.boosLevel != 0) //挑战普通模式里面读Level  (注意第0关)
        {
            bossNumber = Globals.boosLevel;
        }
        PList bossStats = GameData.instance.GetBoss(bossNumber)["Stats"] as PList;

        stats.speed = baseStats.speed = Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]);
        stats.health = baseStats.health = Convert.ToSingle((bossStats["health"] as PList)["value"]);
        stats.turnSpeed = baseStats.turnSpeed = Convert.ToSingle((bossStats["turnSpeed"] as PList)["value"]);
        stats.bulletDamage = baseStats.bulletDamage = Convert.ToSingle((bossStats["attack"] as PList)["value"]);
        stats.regen = baseStats.regen = Convert.ToSingle((bossStats["regen"] as PList)["value"]);
        stats.xp = baseStats.xp = Convert.ToSingle((bossStats["xp"] as PList)["value"]);
        stats.coinAwarded = baseStats.coinAwarded = (int)Convert.ToSingle((bossStats["coins"] as PList)["value"]);
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.bulletSpeed = 3;
        if (bossStats.ContainsKey("CatDropID"))
        {
            prizeID = System.Convert.ToInt32((bossStats["CatDropID"] as PList)["value"]);
        }
        else
        {
            prizeID = 0;
        }
    }

    public void CreateSprite()
    {

        allowRelocate = false;
        enemyCollisionRadius = 15;
        transform.position = new Vector2(player.transform.position.x + 5, Globals.LOWERBOUNDARY - 0.5f);
        explosionType = Explosions.ExplosionType.ExplosionTypeShip;

        shipTop.state.SetAnimation(0, "moving", true);
        enemySprite.state.SetAnimation(0, "moving", true);
        captain.state.SetAnimation(0, "moving", true);

        boundsTop = topCollider.bounds;
        boundsBottom = bottomCollider.bounds;

        captain.state.Event += (TrackEntry track, Spine.Event spineEvent) =>
        {
            if (spineEvent.Data.Name == "redShoot")
            {
                CreateBlades();
            }

            if (spineEvent.Data.Name == "blueShoot")
            {
                Schedule(CreateLightning, 0.15f, 7, 0);
            }
        };

        Schedule(UpdateBounds, 0.3f, -1, 0);
    }

    public void InitArchers()
    {
        foreach (SkeletonAnimation archer in archersList)
        {
            archer.state.SetAnimation(0, "shoot", true);
            archer.transform.SetLocalPositionX(UnityEngine.Random.Range(-3f, 3f));
        }
        archersList[0].state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        {
            if (spineEvent.Data.Name == "archer1Shoot")
            {
                ShootArrow();
            }
        };

    }


    private void UpdateBounds()
    {
        boundsTop = topCollider.bounds;
        boundsBottom = bottomCollider.bounds;
    }

    public void ChangeCaptainStates()
    {
        if (shipStateMachine.GetState().GetStateType() == "idle")
        {
            if (UnityEngine.Random.value < 0.5f)
            {
                shipStateMachine.SetState<CapStateBlue>();
                DOTween.Sequence().AppendInterval(2.0f).AppendCallback(() =>
                {
                    shipStateMachine.SetState<CapStateIdle>();
                }).Play();
            }
            else
            {
                shipStateMachine.SetState<CapStateRed>();
                DOTween.Sequence().AppendInterval(2.0f).AppendCallback(() =>
                {
                    shipStateMachine.SetState<CapStateIdle>();
                }).Play(); ;
            }
        }
    }


    //initializing

    public void Update()
    {
        if (!scheduleUpdate)
            return;


        foreach (SkeletonAnimation archer in archersList)
        {
            bone = archer.skeleton.FindBone("rightArm");
            bone2 = archer.skeleton.FindBone("leftArm");

            if (player.transform.position.x < archer.transform.position.x)
            {
                archer.Skeleton.ScaleX = -1;
                bone.Rotation = Globals.CalcAngle(player.transform.position, archer.transform.position) - 90;
                bone2.Rotation = Globals.CalcAngle(player.transform.position, archer.transform.position) - 90 + 180;




            }
            else
            {
                archer.Skeleton.ScaleX = 1;
                bone.Rotation = -Globals.CalcAngle(player.transform.position, archer.transform.position) + 90;
                bone2.Rotation = -Globals.CalcAngle(player.transform.position, archer.transform.position) + 90 + 180;
            }
        }
        CheckPlayerCollisions();

        if (!isDead)
        {
            if (transform.position.x - player.transform.position.x < Globals.CocosToUnity(-1400) || transform.position.x - player.transform.position.x > Globals.CocosToUnity(1400))
            {
                stats.speed += 0.3f;
                stats.speed = Mathf.Clamp(stats.speed, 4, Globals.SPEEDLIMIT);
                enemySprite.state.TimeScale = 2;


            }
            else
            {

                stats.speed -= 0.5f;

                PList bossStats = GameData.instance.GetBoss(7)["Stats"] as PList;
                stats.speed = Mathf.Clamp(stats.speed, Convert.ToSingle((bossStats["moveSpeed"] as PList)["value"]), 20);
                enemySprite.state.TimeScale = 1;

            }
        }


        if (movingLeft)
        {
            transform.SetWorldPositionX(transform.GetWorldPositionX() - stats.speed * Time.deltaTime * 0.60f);
            if (transform.GetWorldPositionX() - player.transform.GetWorldPositionX() < Globals.CocosToUnity(-1500))
            {

                movingLeft = false;

            }
        }
        else
        {
            transform.SetWorldPositionX(transform.GetWorldPositionX() + stats.speed * Time.deltaTime * 0.60f);
            if (transform.GetWorldPositionX() - player.transform.GetWorldPositionX() > Globals.CocosToUnity(1500))
            {

                movingLeft = true;

            }


        }
        bone = shipTop.skeleton.FindBone("captain");
        //_captain.setPosition(shipTop.getPosition().x + bone.WorldX + enemySprite.getPosition().x, shipTop.getPosition().y + bone.WorldY + enemySprite.getPosition().y);

        if (!isDead)
        {
            if (currentState != ShipStates.State3)
            {
                captain.gameObject.SetActive(true);

            }
            else
            {
                if (shipStateMachine.GetState().GetStateType() == "idle")
                {
                    cuteus.gameObject.SetActive(true);
                    cuteus.allowDefelection = true;
                    cuteus.CuteusUpdate();

                    //captain.transform.position=new Vector2(shipTop.getPosition().x + bone.WorldX + enemySprite.getPosition().x, shipTop.getPosition().y + bone.WorldY + enemySprite.getPosition().y);

                    if (cuteus)
                    {
                        //captain.setPosition(cuteus.enemySprite.getPosition());
                        if (cuteus.waitCheckBetweenAttack == false)
                        {
                            //cuteus.transform.setPosition(shipTop.getPosition().x + bone.WorldX + enemySprite.getPosition().x, shipTop.getPosition().y + bone.WorldY + enemySprite.getPosition().y);
                            //cuteus.transform.position = bone.GetWorldPosition(shipTop.transform);
                            cuteus.transform.localPosition = Vector3.zero;
                            cuteus.transform.SetLocalPositionZ(-1);
                            cuteus.gameObject.SetActive(false);
                            captain.gameObject.SetActive(true);
                        }
                        else
                        {
                            cuteus.gameObject.SetActive(true);
                            captain.gameObject.SetActive(false);
                        }
                    }
                }
                else
                {
                    //captain.setPosition(shipTop.getPosition().x + bone.WorldX + enemySprite.getPosition().x, shipTop.getPosition().y + bone.WorldY + enemySprite.getPosition().y);

                    cuteus.transform.position = captain.transform.position;
                    cuteus.transform.SetLocalPositionZ(-1);
                    captain.gameObject.SetActive(true);
                    cuteus.gameObject.SetActive(false);
                    cuteus.allowDefelection = false;

                }
            }
        }

        if (lowHealth == false)
        {
            if (stats.health < stats.maxHealth.Value * 0.3)
            {
                lowHealth = true;
                foreach (SkeletonAnimation archer in extraArchersList)
                {
                    archer.gameObject.SetActive(true);
                    archer.state.SetAnimation(0, "run", true);
                    archer.transform.SetLocalPositionX(UnityEngine.Random.Range(-0.3f, 0.3f));
                    //DOTween.Sequence().Append(archer.transform.DOScale(new Vector2(-1, 1), 0.02f))
                    //    .Append(archer.transform.DOBlendableLocalMoveBy(new Vector2(-3f, 0), 1 + UnityEngine.Random.value * 2))
                    //    .Append(archer.transform.DOScale(new Vector2(1, 1), 0.02f))
                    //    .Append(archer.transform.DOBlendableLocalMoveBy(new Vector2(3f, 0), 1 + UnityEngine.Random.value * 2)).SetLoops(-1).Play();
                    GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAboveWater, new Vector2(0, 0.175f), false, 1, 3.5f, 0);
                    //enemySprite.addChild(explosion, 2);
                    {
                        //SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
                        //enemySprite.addChild(node, -14);
                        //node.setGlobalZOrder(-3);
                        //node.setPosition(0, 80);
                        //node.setScale(2.5f);
                        //node.state.TimeScale(1.3f);
                        //node.setCameraMask(GAMECAMERA);
                        flameAnimBig.transform.gameObject.SetActive(true);
                        flameAnimBig.state.TimeScale = 2.5f;
                    }
                    {
                        //SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
                        //enemySprite.addChild(node, 1);
                        //node.setPosition(0, 80);
                        //node.setScale(1.25f);
                        //node.setCameraMask(GAMECAMERA);
                        //node.setAnimation(0, "idle", true);
                        flameAnimSmall.transform.gameObject.SetActive(true);
                        flameAnimSmall.state.TimeScale = 1f;
                    }
                }
            }
        }

        if (isBoss)
        {
            Globals.bossPosition = new Vector2(enemySprite.transform.position.x, enemySprite.transform.position.y + 0.150f);
        }

    }

    public void CreateBlades()
    {
        int bladeCount = 0;
        for (int i = 0; i < bladeList.Count; i++)
        {
            if (!bladeList[i].isInUse)
            {
                bladeList[i].isInUse = true;
                ;                //bone = bladeList[i].skeletonAnimation.skeleton.FindBone("root");
                //float originalRotation = bone.Rotation;
                bladeList[i].transform.SetRotation(-85 + (i * 85));
                bladeList[i].transform.position = (Vector2)transform.position + new Vector2(-0.187f, 0.337f);
                bladeList[i].meshRenderer.enabled = true;
                bladeList[i].gameObject.SetActive(true);
                bladeList[i].skeletonAnimation.state.SetAnimation(0, "idle", false);
                PlayBladeAnim(bladeList[i]);

                bladeCount++;
            }
            if (bladeCount == 3)
            {
                break;
            }
        }
    }

    private void PlayBladeAnim(Blade obj)
    {
        Sequence seq = DOTween.Sequence()
                    .Append(obj.transform.DOBlendableMoveBy(new Vector3(Globals.CocosToUnity(-3000) * Mathf.Sin(Mathf.Deg2Rad * obj.transform.eulerAngles.z),
                    Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * obj.transform.eulerAngles.z)), stats.bulletSpeed))
                    .OnComplete(() =>
                    {
                        obj.skeletonAnimation.state.SetEmptyAnimation(0, 0);
                        obj.transform.eulerAngles = Vector3.zero;
                        obj.transform.localPosition = Vector3.zero;
                        obj.isInUse = false;
                        obj.meshRenderer.enabled = false;
                        //obj.gameObject.SetActive(false);
                        //bone.Rotation = rotation;

                    }).Play();
    }

    public override bool TakeHit(double damage)
    {
        if (takeDamage)
        {
            if (healthBar)
            {
                stats.health = stats.health - damage;
                healthBar.SetDisplayHealth((float)(stats.health / stats.maxHealth.Value));
                healthBar.gameObject.SetActive(true);
                //DOTween.Sequence().AppendInterval(3).AppendCallback(() => { healthBar.gameObject.SetActive(false); }).Play();

                enemySprite.GetComponent<Renderer>().material.DOKill();
                enemySprite.GetComponent<Renderer>().material.color = Color.red;
                enemySprite.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);

                shipTop.GetComponent<Renderer>().material.DOKill();
                shipTop.GetComponent<Renderer>().material.color = Color.red;
                shipTop.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);

                //shipBottom.GetComponent<Renderer>().material.DOKill();
                //shipBottom.GetComponent<Renderer>().material.color = Color.red;
                //shipBottom.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.2f);
            }

            if (stats.health < stats.maxHealth.Value * 0.8f && currentState == ShipStates.State1) // 0.8
            {
                currentState = ShipStates.State2;
                Observer.DispatchCustomEvent("ChangeBossState");

                shipStateMachine.SetState<CapStateIdle>();
                Schedule(ChangeCaptainStates, 6, -1, 6);
            }
            else if (stats.health < stats.maxHealth.Value * 0.3f && currentState == ShipStates.State2)
            {
                currentState = ShipStates.State3;
                captain.skeleton.SetAttachment("captain/cape2", "captain/cape");
                captain.skeleton.SetAttachment("captain/helmet2", "captain/helmet");
                Observer.DispatchCustomEvent("ChangeBossState");
                if (!gameObject.activeInHierarchy) return false;
                StartCoroutine(CreateCuteus());
                IEnumerator CreateCuteus()
                {
                    yield return new WaitForEndOfFrame();
                    cuteus.gameObject.SetActive(true);
                    cuteus.Init();
                    cuteus.isDestructable = false;
                }
                //        _darth.enemySprite.setVisible(false);
            }


            if (stats.health < 0)
            {
                if (isBoss)
                {
                    Globals.isBossMode = false;
                    GameSharedData.Instance.enemyList.Clear();
                    //            this.unscheduleUpdate();
                    Globals.bossPosition = Vector2.zero;
                }
                takeDamage = false;
                return true;
            }
        }
        return false;
    }


    public void CreateLightning()
    {
        lightning[lightningCount].PlayLightningAnim();
        lightningCount++;
        if (lightningCount == 7)
        {
            lightningCount = 0;
        }
    }


    public void CheckPlayerCollisions()
    {
        foreach (Blade blade in bladeList)
        {
            if (Vector2.SqrMagnitude(player.transform.position - blade.transform.position) < Globals.CocosToUnity(10000))
            {
                bladeBound = blade.boundingBox.bounds;
                if (bladeBound.Contains(player.transform.position)
                    && player.canHit)//(_bladeBound, Player::getInstance().getPosition().x - bullet.getPosition().x, Player::getInstance().getPosition().y - bullet.getPosition().y))
                {
                    player.GotHit(15.0f, false);
                }
            }
        }
    }


    public void ShootArrow()
    {
        foreach (SkeletonAnimation archer in archersList)
        {
            //if (player.transform.position.x < archer.transform.position.x)
            //{

            bone = archer.skeleton.FindBone("rightArm");

            Bullet bullet = null;
            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }


            bullet.setDamage(stats.bulletDamage * 10);
            bullet.SetSpriteFrame(arrowSprite);
            bullet.transform.SetScale(2.5f);
            bullet.setRadiusEffectSquared(1f);

            bullet.transform.eulerAngles = player.transform.position.x < archer.transform.position.x ? -bone.GetLocalQuaternion().eulerAngles : bone.GetLocalQuaternion().eulerAngles;


            GameSharedData.Instance.enemyBulletInUse.Add(bullet);

            bone = archer.skeleton.FindBone("arrow");


            bullet.transform.position = bone.GetWorldPosition(archer.transform);

            float weight;
            if (player.transform.position.x < transform.position.x)
            {
                weight = -3 + UnityEngine.Random.value * -6;
            }
            else
            {
                weight = 3 + UnityEngine.Random.value * 6;
            }
            bullet.transform.GetChild(3).gameObject.SetActive(true);
            float rotation = bullet.transform.eulerAngles.z + weight - 5 + (UnityEngine.Random.value * 10);
            bullet.PlayBulletAnim(stats.bulletSpeed + stats.speed / 4, new Vector2((stats.speed * Globals.CocosToUnity(1000) * Mathf.Sin(Mathf.Deg2Rad * rotation)) * -1, stats.speed * Globals.CocosToUnity(1000) * Mathf.Cos(Mathf.Deg2Rad * rotation)));

            //bullet.runAction(Sequence::create(MoveBy::create(stats.bulletSpeed + stats.speed / 4, cocos2d::Point(stats.speed * 1000 * sinf(CC_DEGREES_TO_RADIANS(rotation)), stats.speed * 1000 * cosf(CC_DEGREES_TO_RADIANS(rotation)))), NULL));

            //SkeletonAnimation* flame = SkeletonAnimation::createWithJsonFile("res/Enemy/flame.json", "res/Enemy/flame.atlas");
            //bullet.addChild(flame, 2);
            //flame.setCameraMask(GAMECAMERA);
            //flame.setAnimation(0, "flame", true);
            //flame.setPosition(bullet.getContentSize().width / 2, bullet.getContentSize().height);
            //flame.setScale(3);
            //flame.setRotation(180);


            //}
            //else
            //{
            //    bone = spSkeleton_findBone(archer.getSkeleton(), "rightArm");

            //    Bullets* bulletLayer = Bullets::create();


            //    Sprite* bullet = bulletLayer.bulletSprite;
            //    bulletLayer.setDamage(stats.bulletDamage * 10);
            //    bullet.setSpriteFrame("arrow.png");
            //    bullet.setScale(0.6f);


            //    bullet.setCameraMask(GAMECAMERA);

            //    bullet.setRotation(-bone.Rotation);



            //    GameSharedData::getInstance().g_bulletsToAddArray.pushBack(bulletLayer);
            //    bone = spSkeleton_findBone(archer.getSkeleton(), "arrow");
            //    bullet.setPosition(enemySprite.getPosition() + archer.getPosition() + cocos2d::Point(bone.WorldX, bone.WorldY));

            //    float weight;
            //    if (player.transform.position.x < enemySprite.getPosition().x)
            //    {
            //        weight = -3 + rand_0_1() * -6;
            //    }
            //    else
            //    {
            //        weight = 3 + rand_0_1() * 6;
            //    }
            //    float rotation = bullet.getRotation() + weight - 5 + (rand_0_1() * 10);
            //    bullet.runAction(Sequence::create(MoveBy::create(stats.bulletSpeed + stats.speed / 4, cocos2d::Point(stats.speed * 1000 * sinf(CC_DEGREES_TO_RADIANS(rotation)), stats.speed * 1000 * cosf(CC_DEGREES_TO_RADIANS(rotation)))), NULL));
            //    SkeletonAnimation* flame = SkeletonAnimation::createWithJsonFile("res/Enemy/flame.json", "res/Enemy/flame.atlas");
            //    bullet.addChild(flame, 2);
            //    flame.setCameraMask(GAMECAMERA);
            //    flame.setAnimation(0, "flame", true);
            //    flame.setPosition(bullet.getContentSize().width / 2, bullet.getContentSize().height);
            //    flame.setScale(3);
            //    flame.setRotation(180);


            //}
        }
    }

    public override bool CheckCollision(Vector2 P1)
    {
        if (Vector2.SqrMagnitude(P1 - (Vector2)transform.position) < enemyCollisionRadius)
        {
            if (boundsTop.Contains(P1))
            {
                return true;
            }
            else if (boundsBottom.Contains(P1))
            {
                return true;

            }
        }
        return false;
    }


    public override void Destroy()
    {
        //{
        //    StopAllCoroutines();
        //    //    this.healthBar.removeFromParentAndCleanup(true);
        healthBar.gameObject.SetActive(false);
        takeDamage = false;
        //    foreach (SkeletonAnimation sk in archersList)
        //    {
        //        sk.gameObject.SetActive(false);
        //    }



        //    shipTop.state.TimeScale = 0.5f;
        //    enemySprite.state.TimeScale = (0.5f);
        //    captain.state.TimeScale = (0.5f);

        //    shipTop.state.SetAnimation(0, "death", false);
        //    enemySprite.state.SetAnimation(0, "death", false);
        //    captain.state.SetAnimation(0, "death", false);

        //    StartCoroutine(RemoveFromList());

        //    DOTween.Sequence().AppendInterval(5).AppendCallback(() =>
        //    {
        //        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + Globals.CocosToUnity(25), transform.position.y + Globals.CocosToUnity(25)), false, 1, 2, 0);

        //    });

        //    shipStateMachine.SetState<CapStateDeath>();
        //    isDead = true;

        //    DOTween.To(() => stats.speed, x => stats.speed = x, 0, 2).OnUpdate(()=>
        //    {
        //        //print(stats.speed);
        //    }
        //    );


        //    shipTop.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        //      {

        //          if (spineEvent.Data.Name == "explosion")
        //          {
        //              {
        //                  GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeShip, transform.position, false, 1, 2, 0);

        //              }

        //              {

        //                  GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeShip, transform.position, false, 1, -0.25f, 0);
        //              }
        //              GameManager.instance.ShakeCamera(0.3f, 15);
        //          }



        //      };


        //    if (cuteus)
        //    {
        //        cuteus.gameObject.SetActive(false);
        //        cuteus.allowDefelection = false;
        //    }
        //    captain.gameObject.SetActive(true);
        //}
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeBoss, new Vector2(transform.position.x + 0.025f, transform.position.y + 0.025f), false, 1, 2, 0);
        base.Destroy();
        Globals.ResetZoomValue();
        Globals.ResetBoundary();
    }

    private IEnumerator RemoveFromList()
    {
        yield return new WaitForEndOfFrame();
        GameSharedData.Instance.enemyList.Remove(this);
    }

    private void Schedule(System.Action action, float interval = 0, int repeat = 0, float delay = 0)
    {
        StartCoroutine(ScheduleCoroutine(action, interval, repeat, delay));
    }

    private IEnumerator ScheduleCoroutine(System.Action action, float interval = 0, int repeat = 0, float startDelay = 0)
    {
        yield return new WaitForSeconds(startDelay);
        int repeatCount = 0;
        do
        {
            action?.Invoke();
            repeatCount++;
            yield return new WaitForSeconds(interval);
        }
        while (repeatCount < repeat || repeat == -1);
    }

}

public class ShipStateMachine : StateMachine
{
    public CuteusShip ship;

    ShipStateMachine(CuteusShip c)
    {
        ship = c;
    }

    public static ShipStateMachine Create(CuteusShip c)
    {
        ShipStateMachine ssm = new ShipStateMachine(c);

        return ssm;
    }
}

public class CapStateIdle : State
{
    ShipStateMachine shipStateMachine;
    CuteusShip ship;

    void Init()
    {
        shipStateMachine = stateMachine as ShipStateMachine;
        ship = shipStateMachine.ship;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (shipStateMachine == null)
            Init();


    }

    public override void UpdateState()
    {

    }

    public override string GetStateType()
    {
        return "idle";
    }
}

public class CapStateRed : State
{
    ShipStateMachine shipStateMachine;
    CuteusShip ship;

    void Init()
    {
        shipStateMachine = stateMachine as ShipStateMachine;
        ship = shipStateMachine.ship;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (shipStateMachine == null)
            Init();

        ship.captain.state.SetAnimation(0, "redShoot", false);
        ship.captain.state.AddAnimation(0, "moving", true);
    }

    public override void UpdateState()
    {

    }

    public override string GetStateType()
    {
        return "red";
    }
}

public class CapStateBlue : State
{
    ShipStateMachine shipStateMachine;
    CuteusShip ship;

    void Init()
    {
        shipStateMachine = stateMachine as ShipStateMachine;
        ship = shipStateMachine.ship;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (shipStateMachine == null)
            Init();

        ship.captain.state.SetAnimation(0, "blueShoot", false);
        ship.captain.state.AddAnimation(0, "moving", true);
    }

    public override void UpdateState()
    {

    }

    public override string GetStateType()
    {
        return "blue";
    }
}

public class CapStateDeath : State
{
    ShipStateMachine shipStateMachine;
    CuteusShip ship;

    void Init()
    {
        shipStateMachine = stateMachine as ShipStateMachine;
        ship = shipStateMachine.ship;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (shipStateMachine == null)
            Init();

        //static_cast<spine::SkeletonAnimation*>(_actor).setAnimation(0, "death", false);
    }

    public override void UpdateState()
    {

    }

    public override string GetStateType()
    {
        return "death";
    }
}