<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CString</name>
    </assembly>
    <members>
        <member name="M:CString.Substring(System.Int32)">
            <summary>
            分配新的CString为当前字符串从startIndex开始的一部分
            </summary>        
        </member>
        <member name="M:CString.Trim">
            <summary>
            去除字符串前后的空格, 注意不同于string, 这个函数修改的是字符串自身.
            </summary>
            <returns>返回当前字符串</returns>
        </member>
        <member name="M:CString.Trim(System.Char[])">
            <summary>
            从字符串两端去除trimChars包含的单个字符, 注意不同于string, 这个函数修改的是字符串自身.
            </summary>
            <returns>返回当前字符串</returns>
        </member>
        <member name="M:CString.PadLeft(System.Int32,System.Char)">
            <summary>
            在当前字符串前插入totalWidth个paddingChar
            </summary>
            <param name="totalWidth"></param>
            <param name="paddingChar"></param>
            <returns></returns>
        </member>
        <member name="M:ExtensionLibs.ReplaceEx(System.String,CString,System.Int32)">
            <summary>
            使用CString src覆盖dest字符串len长度内容
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
