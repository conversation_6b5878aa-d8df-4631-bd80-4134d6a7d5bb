%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5892744512543873083
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 567397669417096961}
  m_Layer: 0
  m_Name: Rocketeer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &567397669417096961
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5892744512543873083}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5046533346229891336}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 514, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &8903034702471364398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5046533346229891336}
  - component: {fileID: 3268489648676185614}
  - component: {fileID: 3202616435341993049}
  - component: {fileID: 5287871324076164871}
  m_Layer: 0
  m_Name: SkeletonGraphic (rocketeer)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5046533346229891336
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903034702471364398}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 567397669417096961}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -362.73987, y: 219}
  m_SizeDelta: {x: 565.7562, y: 514.73254}
  m_Pivot: {x: 0.4084551, y: 0.5883148}
--- !u!222 &3268489648676185614
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903034702471364398}
  m_CullTransparentMesh: 1
--- !u!114 &3202616435341993049
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903034702471364398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d85b887af7e6c3f45a2e2d2920d641bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: b66cf7a186d13054989b33a5c90044e4, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  skeletonDataAsset: {fileID: 11400000, guid: 1f588dd021c9844b2a45ab6abadadf04, type: 2}
  initialSkinName: default
  initialFlipX: 0
  initialFlipY: 0
  startingAnimation: talk
  startingLoop: 1
  timeScale: 1
  freeze: 0
  unscaledTime: 1
  meshGenerator:
    settings:
      useClipping: 1
      zSpacing: 0
      pmaVertexColors: 1
      tintBlack: 0
      calculateTangents: 0
      addNormals: 0
      immutableTriangles: 0
--- !u!114 &5287871324076164871
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903034702471364398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 11cd9de1340b642ed96ebef70d3158c8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonGraphic: {fileID: 3202616435341993049}
  m_preserveAspectRatio: 0
  m_SetToSetupPoseWhenScaling: 0
  m_isEditorUpdateStopped: 0
  m_initialRectWithBounds:
    serializedVersion: 2
    x: -231.08601
    y: -302.82477
    width: 565.7562
    height: 514.73254
  m_initialSpineSkeletonScale: {x: 1, y: 1}
