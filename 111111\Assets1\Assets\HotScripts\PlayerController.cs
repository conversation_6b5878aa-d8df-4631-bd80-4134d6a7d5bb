﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;

using DG.Tweening;

using Spine.Unity;

using TMPro;

using UniRx;

using UnityEngine;

using X;

public class PlayerController : MonoBehaviour
{
    public enum PlayerAnimation { None, Idle, Flying, Flip, Dash, Shoot }

    public enum PLAYERMODE
    {
        PLAYER_MODE_FLYING,
        PLAYER_MODE_DEATHDROP,
        PLAYER_MODE_DEAD,
        PLAYER_MODE_BEFORECHUTE,
        PLAYER_MODE_ENDMISSION
    }
    public Transform skeletonAnimationTran;
    [SerializeField] SkeletonAnimation skeletonAnimation, berserkSkeleton, effectsSkeleton;
    [SerializeField] Animator[] dryShotSmokes;
    [SerializeField] string idle, flying, flip, dash, shooting, plasmaShooting;
    //[SerializeField] private HealthBar healthBar;
    [SerializeField] private GameObject hitTextGO, musicParticle;
    [SerializeField] private TrailRenderer berzerkTrail;
    [SerializeField] private ParticleSystem mechParticle, lowHealthSmoke, boostStartingParticle;
    [SerializeField] private Transform dashTransform, boostTransform;
    public Animator dashAnimator, healthRegenAnimator;
    public PlayerHud playerHud;

    //public GameObject lowHealthSprite;

    Transform planeTransform;
    [HideInInspector] public Weapon weapon;
    [HideInInspector] public PlayerMovement playerMovement;
    [HideInInspector] public PlayerPowerUp playerPowerUp;
    PlayerAnimation currentAnimation;
    PlayerStateMachine stateMachine;
    PlayerInputActions playerInputActions;
    [HideInInspector] public SecondaryWeapon secondaryWeapon;
    private int planeIndex;
    private float energyCooldownDt = 0, smokeOffset;
    public bool poisonPlayer = false, isBerzerkMode = false;
    private bool thrust = false, allowDryShot = true;
    /// <summary>
    /// 总回血量(偏小)
    /// </summary>
    [HideInInspector] public int healedAmount;
    [HideInInspector] public float attackSpeedMultiplier;
    [HideInInspector] public bool canHit = true;

    private bool isTutorialGunCooldownUpdate = false, isBoostOn = false;

    /// <summary>
    /// 玩家属性
    /// </summary>
    public Attributes Stats { get; protected set; }
    /// <summary>
    /// 基础属性
    /// </summary>
    public Attributes baseStats;

    public float GetPlaneScale() => planeTransform.localScale.y;
    public SkeletonAnimation GetSkeletonAnimation() => skeletonAnimation;

    public PLAYERMODE Mode { get; set; }
    public bool IsShooting { get { return weapon.isShooting; } }
    public bool IsInBerzerk { get { return isBerzerkMode; } }

    [HideInInspector] public string tweenId;
    [HideInInspector] public string schedulerId;

    public GameObject dashSmall;
    private bool regenHealth = true;


    [HideInInspector] public double defaultMaxHealth;
    [HideInInspector] public double defaultAttack;
    [HideInInspector] public float defaultSpeed;
    [HideInInspector] public double defaultEnergy;
    [HideInInspector] public double defaultEnergyRegen;
    [HideInInspector] public float defaultAttackScope;
    [HideInInspector] public float defaultAttaceDistance;
    [HideInInspector] public double defaultDamageReduction;
    [HideInInspector] public double defaultAddDamage;
    [HideInInspector] public float defaultBulletSpeedAddRate;
    [HideInInspector] public double defaultAddAttack;
    [HideInInspector] public float defaultBulletDamageAddRate;
    [HideInInspector] public float defaultRageModeTime;
    [HideInInspector] public double defaultMaxEnergy;
    [HideInInspector] public double defaultRageAddDamage;
    [HideInInspector] public float defaultAttackDistance;
    //临时保存暴走前的
    [HideInInspector] public float tempSaveSpeed;
    [HideInInspector] public float tempSaveAttackSpeed;

    [HideInInspector] public float enemyCountMulte;
    [HideInInspector] public float enemyScreenCountMulte;
    [HideInInspector] public int curWhiteCatIndex;

    Vector2 playerMovementValue;
    float TempMovementValueX;

    ///// <summary>
    ///// 战斗内加成：生命回复点数
    ///// </summary>
    //private int regenHealthCount = 0;
    ///// <summary>
    ///// 战斗内加成：生命回复百分比
    ///// </summary>
    //private int regenHealthPercent = 0;
    /// <summary>
    /// 玩家拥有的BuffEffect
    /// </summary>
    public BuffEffectList BuffEffects { get; } = new();

    // 主角的碰撞半径
    public float CollisionRadius => 1.25f;

    public Transform healthBarRect;
    public Transform energyBarRect;

    private Transform shootTarget;

    public Transform[] playerTransList;
    public Dictionary<int, string> playerSkipList = new Dictionary<int, string>();
    public Transform playerCopyTran;//复制一份模型，仅当显示用

    public EmptyMonoBehaviour UniTaskWaiter { get; private set; }

    #region Sprites
    public List<NamedSprite> Sprites;
    #endregion
    public string State
    {
        get
        {
            return stateMachine.GetState().GetStateType();
        }
    }

    public float RotationInDegrees
    {
        get
        {
            return playerMovement.playerDir;
        }
    }

    public Vector2 Acceleration
    {
        get
        {
            return playerMovement.GetAcceleration();
        }
    }
    public void SetShootGarget(Transform target)
    {
        shootTarget = target;
    }
    private void Awake()
    {
        UniTaskWaiter = transform.parent.gameObject.GetOrAddComponent<EmptyMonoBehaviour>();

        weapon = GetComponent<Weapon>();
        secondaryWeapon = GetComponent<SecondaryWeapon>();
        playerMovement = GetComponent<PlayerMovement>();
        playerPowerUp = GetComponent<PlayerPowerUp>();

        int baseID = 101;
        for (int i = 0; i < playerTransList.Length; i++)
        {
            Transform t = playerTransList[i];
            playerSkipList.Add(baseID, t.name);
            baseID++;
        }
        int planeID = LuaToCshapeManager.Instance.AircraftSkinID;
        var e_item = EquipmentScheme.Instance.GetItem(planeID);
        string planeName = "Spine_WXR01";
        if (playerSkipList.ContainsKey(e_item.RightWeapon))
        {
            planeName = playerSkipList[e_item.RightWeapon];
        }
        skeletonAnimationTran = transform.Find(planeName);
        if (skeletonAnimationTran != null)
        {
            //skeletonAnimationTran.gameObject.SetActive(true);
        }
        else
        {
            skeletonAnimationTran = skeletonAnimation.transform;
        }
        if (playerCopyTran)
        {
            Transform playerCopyTranChild = playerCopyTran.Find(planeName);
            playerCopyTranChild.gameObject.SetActive(true);
        }
        //Creature.Item c_item = CreatureScheme.Instance.GetItem(e_item.RightWeapon);
        //Debug.Log("planeID=" + planeID+"   e_item.RightWeapon=" + e_item.RightWeapon+ "   c_item=" + c_item.Prefab);
        planeTransform = transform.GetChild(0);
        InitStats();
    }

    private void Start()
    {
        Mode = PLAYERMODE.PLAYER_MODE_FLYING;
        if (GameData.instance.fileHandler.currentMission == 0 || isWhiteCatType)
        {
            planeIndex = 5;
        }
        else
        {
            var air = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.AircraftSkinID);
            planeIndex = air.GroupID - 200;
        }

        stateMachine = PlayerStateMachine.Create(this);
        stateMachine.AddState<PlayerIdle>();
        stateMachine.AddState<PlayerFlying>();
        stateMachine.AddState<PlayerFlip>();
        stateMachine.AddState<PlayerDash>();
        stateMachine.AddState<PlayerShoot>();
        stateMachine.AddState<PlayerDeath>();

        stateMachine.SetState<PlayerIdle>();
        tweenId = "Player" + GetInstanceID();
        schedulerId = "PlayerS" + GetInstanceID();

        canHit = true;

        //dryShotSmokes[0].transform.parent.parent = null;
        //smokeOffset = dryShotSmokes[0].transform.localPosition.magnitude;
        lowHealthSmoke.Stop();
        if (isWhiteCatType)
        {
            boostTransform.localScale = Vector3.one * 4;
            boostTransform.gameObject.SetActive(true);
        }
        else
        {
            boostTransform.localScale = Vector3.zero;
            boostTransform.gameObject.SetActive(false);
        }

        dashAnimator.Play("Main", 0, 1);
        //healthRegenAnimator.Play("Main", 0, 1);

        //foreach (Animator anim in dryShotSmokes)
        //{
        //    if (anim.gameObject.activeInHierarchy)
        //    {
        //        anim.Play("Main", 0, 1);
        //        anim.gameObject.SetActive(false);
        //    }
        //}

        if (Globals.g_currentStageData != null && (Globals.g_currentStageData.FrontType != 3 || Globals.g_currentStageData.FrontType != 4))
        {
            // 每秒回血
            Observable.Interval(System.TimeSpan.FromSeconds(1)).Where(_ => Time.deltaTime > 0)
                .Subscribe(_ => { RegenerateHealth(); })
                .AddTo(this);
            //DOTween.Sequence().AppendInterval(1).AppendCallback(RegenerateHealth).SetLoops(-1);
        }

        berzerkTrail.emitting = false;
        //shopGlowSkeleton.gameObject.SetActive(false);
        effectsSkeleton.gameObject.SetActive(false);

        //regenHealthCount = 0;
        //regenHealthPercent = 0;

        DOTween.Sequence().AppendInterval(5f).AppendCallback(() => { UpdateHealthBar(); });//初始延时显示血量

    }

    private void InitStats()
    {
        Stats = new Attributes();
        baseStats = new Attributes();

        Stats.maxHealth.Subscribe(_ =>
        {
            //重新计算回血
            if (GameManager.instance && GameManager.instance.player)
            {
                GameManager.instance.player.ReCalcRegen();
            }
        });

        //PlayerAttributeManager.Instance.PlayerController = this;
        //_damageTaken = 0;
        //_healedAmount = 0;
        PList vm = GameData.instance.GetPlayerData()["Stats"] as PList;
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            Stats.maxHealth.Value = System.Convert.ToDouble((vm["health"] as PList)["value"]) + (GameData.instance.fileHandler.playerLevel * System.Convert.ToInt32((vm["health"] as PList)["multiplier"]));
            Stats.attack = System.Convert.ToDouble((vm["attack"] as PList)["value"]) + (GameData.instance.fileHandler.playerLevel * System.Convert.ToSingle((vm["attack"] as PList)["multiplier"]));
            Stats.speed = 1;
            Stats.energy = Stats.maxEnergy;
            GameData.instance.fileHandler.playerXP = 1;
            GameData.instance.fileHandler.xpRequiredThisLevel = 1;
            Stats.bulletDamageAddRate = 1;
            Stats.damageReductionPoint = 0;
            Stats.damageAddPoint = 0;
            Stats.bulletDamage = 10;
        }
        else
        {
            //stats.bulletDamageAddRate = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_DAMAGE_ADD)]);
            //stats.maxHealth = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(PLAYER_FIELD.PLAYER_FIELD_MAXHP)]);
            //stats.attack = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_PHYSICS_ATTACK)]);
            //stats.speed = System.Convert.ToSingle(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_MOVE_SPEED)]) / 10000;
            //stats.energy = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_ANTI_ARMOR)]);
            //GameData.instance.fileHandler.playerLevel = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.CREATURE_FIELD_LEVEL)]);
            //GameData.instance.fileHandler.playerXP = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_CUREXP)]);
            //GameData.instance.fileHandler.xpRequiredThisLevel = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute[(int)(object)(PLAYER_FIELD.PLAYER_FIELD_MAXEXP)]);

            Stats.damageAddPoint = LuaToCshapeManager.Instance.AllPlayerAttribute2.DamageAdd;
            Stats.maxHealth.Value = LuaToCshapeManager.Instance.AllPlayerAttribute2.HP;
            Stats.attack = LuaToCshapeManager.Instance.AllPlayerAttribute2.PhysicsAttack;
            Stats.speed = LuaToCshapeManager.Instance.AllPlayerAttribute2.MoveSpeed / 10000f;
            Stats.energy = LuaToCshapeManager.Instance.AllPlayerAttribute2.AntiArmor;
            GameData.instance.fileHandler.playerLevel = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute2.level);
            GameData.instance.fileHandler.playerXP = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute2.curEXP);
            GameData.instance.fileHandler.xpRequiredThisLevel = System.Convert.ToInt32(LuaToCshapeManager.Instance.AllPlayerAttribute2.maxEXP);
            Stats.damageReductionPoint = LuaToCshapeManager.Instance.AllPlayerAttribute2.DamageReduction;
            Stats.criticalHitRate = LuaToCshapeManager.Instance.AllPlayerAttribute2.CriticalHitRate;
            Stats.bulletDamage = LuaToCshapeManager.Instance.AllPlayerAttribute2.CriticalStrike;
            Stats.parry = LuaToCshapeManager.Instance.AllPlayerAttribute2.Parry;
            Stats.antiCriticalStrike = LuaToCshapeManager.Instance.AllPlayerAttribute2.AntiCriticalStrike;
            Stats.damageBossAddPct = LuaToCshapeManager.Instance.AllPlayerAttribute2.DamageBossAddPct;
            Stats.damageCritical = LuaToCshapeManager.Instance.AllPlayerAttribute2.DamageCritical;
            Stats.AbsorbHP = LuaToCshapeManager.Instance.AllPlayerAttribute2.AbsorbHP;
            baseStats.regen = Stats.regen = LuaToCshapeManager.Instance.AllPlayerAttribute2.Defense;
        }
        Stats.addBulletNum = 0;
        Stats.halfHealthAddDamage = 1;
        Stats.boostSpeed = 0;
        //stats.bulletDamage = 10;
        Stats.missileDamage = 0;
        Stats.turnSpeed = 1;
        Stats.attackSpeed = 1;
        Stats.bulletSpeed = 0;
        Stats.absorb = 0;
        Stats.shield = GameData.instance.fileHandler.PlayerShield;
        Stats.consumption = 1;
        Stats.energyRegen = System.Convert.ToDouble((vm["energyRegen"] as PList)["value"]);
        attackSpeedMultiplier = 1;
        Stats.maxEnergy = Stats.energy;
        Stats.rageModeTime = 10;

        Stats.attackScope = 1;
        Stats.damageReduction = 0;
        Stats.addDamage = 0;
        Stats.bulletSpeedAddRate = 1;
        Stats.addAttack = 0;
        Stats.rageAddDamage = 1;
        Stats.attackDistance = 1;
        Stats.rearGunCD = 1;
        Stats.rearGunDamage = 1;
        Stats.mainWeaponUltimateSkills = -1;
        //if (GameData.instance.fileHandler.currentMission != 0)
        //{
        //    ApplyEquipBuff();
        //}
        Stats.health = Stats.maxHealth.Value;

        //Debug.LogWarning("玩家的最大生命：" + stats.maxHealth.ToString());
        //Debug.LogWarning("玩家的速度：" + stats.speed.ToString());
        //Debug.LogWarning("玩家的基础攻击力:" + stats.attack.ToString());
        //Debug.LogWarning("玩家的能量:" + stats.energy.ToString());
        //Debug.LogWarning("玩家的能量energyRegen:" + stats.energyRegen.ToString());

        defaultMaxHealth = Stats.maxHealth.Value;
        defaultAttack = Stats.attack;
        defaultSpeed = Stats.speed;
        tempSaveSpeed = defaultSpeed;
        defaultAttackScope = Stats.attackScope;
        defaultEnergyRegen = Stats.energyRegen;
        defaultDamageReduction = Stats.damageReduction;
        defaultAddDamage = Stats.addDamage;
        defaultBulletSpeedAddRate = Stats.bulletSpeedAddRate;
        defaultAddAttack = Stats.addAttack;
        defaultRageModeTime = Stats.rageModeTime;
        defaultMaxEnergy = Stats.maxEnergy;
        defaultBulletDamageAddRate = Stats.bulletDamageAddRate;
        defaultRageAddDamage = Stats.rageAddDamage;
        defaultAttaceDistance = Stats.attackDistance;

        enemyCountMulte = 1;
        enemyScreenCountMulte = 1;

        if (Globals.DIEQUICK)
        {
            Stats.maxHealth.Value = 1;
            Stats.regen = 0;
            Stats.health = 1;
        }
        if (Globals.DONTDIE)
        {
            Stats.maxHealth.Value = 10000000;
            Stats.health = 10000000;
            Stats.armor = 10000;
        }
    }

    /// <summary>
    /// 重新计算最大生命
    /// </summary>
    /// <returns>旧新最大生命</returns>
    public (double, double) ReCalcMaxHP()
    {
        var old = Stats.maxHealth.Value;

        // 获取Buff中的生命加成
        var p1 = BuffEffects.Where(x => x.Param1 == 1 && x.Param2 == 1)
            .Sum(x => x.Param3);
        var p2 = BuffEffects.Where(x => x.Param1 == 1 && x.Param2 == 2)
            .Sum(x => x.Param3);

        p1 += LuaToCshapeManager.Instance.SkillAttributeCountList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.生命加成)
            .Sum(x => x.Value);
        p2 += LuaToCshapeManager.Instance.SkillAttributePercentList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.生命加成)
            .Sum(x => x.Value);

        // 总加成
        var addition = (defaultMaxHealth + p1) * p2 / 10000f;

        // 新的生命上限
        Stats.maxHealth.Value = defaultMaxHealth + p1 + addition;

        // 比之前的增量
        var inc = Stats.maxHealth.Value - old;
        if (inc > 0)
        {
            Stats.health += inc;
        }

        return (old, Stats.maxHealth.Value);
    }

    /// <summary>
    /// 重新计算攻击
    /// </summary>
    /// <returns>旧新攻击</returns>
    public (double, double) ReCalcAttack()
    {
        var old = Stats.attack;

        // 获取Buff中的攻击加成
        var p1 = BuffEffects.Where(x => x.Param1 == 2 && x.Param2 == 1)
            .Sum(x => x.Param3);
        var p2 = BuffEffects.Where(x => x.Param1 == 2 && x.Param2 == 2)
            .Sum(x => x.Param3);

        p1 += LuaToCshapeManager.Instance.SkillAttributeCountList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.攻击加成)
            .Sum(x => x.Value);
        p2 += LuaToCshapeManager.Instance.SkillAttributePercentList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.攻击加成)
            .Sum(x => x.Value);

        // 总加成
        var addition = (defaultAttack + p1) * p2 / 10000f;

        // 新的攻击
        Stats.attack = defaultAttack + p1 + addition;

        //Debug.LogError($"旧攻击:{old}, 加成后{Stats.attack}");

        return (old, Stats.maxHealth.Value);
    }

    /// <summary>
    /// 重新计算回血
    /// </summary>
    /// <returns>旧新回血</returns>
    public (double, double) ReCalcRegen()
    {
        var old = Stats.regen;

        // 获取Buff中的回血
        var p1 = BuffEffects.Where(x => x.Param1 == 24 && x.Param2 == 1)
            .Sum(x => x.Param3);
        var p2 = BuffEffects.Where(x => x.Param1 == 24 && x.Param2 == 2)
            .Sum(x => x.Param3);

        // 临时技能中的回血
        p1 += LuaToCshapeManager.Instance.SkillAttributeCountList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.生命恢复)
            .Sum(x => x.Value);
        p2 += LuaToCshapeManager.Instance.SkillAttributePercentList.Where(x => x.Key == (int)Globals.UpgradeSkillAttibute.生命恢复)
            .Sum(x => x.Value);


        // 新的回血
        Stats.regen = baseStats.regen + p1 + (Stats.maxHealth.Value * p2 / 10000);

        return (old, Stats.regen);
    }

    /// <summary>
    /// 瞬间一次性回血
    /// </summary>
    public void CalcRegenMoment(BuffEffect.Item buffEffectItem)
    {

        if (buffEffectItem.Param2 == 2)
        {
            double addValue = Stats.maxHealth.Value * Globals.UnityValueTransform(buffEffectItem.Param3);
            //Debug.Log("addValue:" + addValue);
            Stats.health += addValue;
            if (Stats.health > Stats.maxHealth.Value)
            {
                Stats.health = Stats.maxHealth.Value;
            }
            UpdateHealthBar();
        }
    }
    /// <summary>
    /// 战斗内生命属性加成(用金币购买的)
    /// </summary>
    /// <param name="id"></param>
    /// <param name="count"></param>
    /// <param name="percent"></param>
    public void SetUpgradeSkillAttributeHealth(int id, int count, int percent)
    {
        bool needAdd = false;
        if (id == (int)Globals.UpgradeSkillAttibute.生命加成)
        {
            needAdd = true;
        }
        if (id == (int)Globals.UpgradeSkillAttibute.攻击加成)
        {
            ReCalcAttack();
        }
        //Debug.Log("id="+ id+ "     (int)Globals.UpgradeSkillAttibute.GongJiJuLi=" + (int)Globals.UpgradeSkillAttibute.GongJiJuLi);
        if (id == (int)Globals.UpgradeSkillAttibute.攻击距离)
        {
            playerHud.SetAttributeSkillAddDistance();

        }
        if (id == (int)Globals.UpgradeSkillAttibute.生命恢复)
        {
            ReCalcRegen();
        }

        if (!needAdd) return;
        if (defaultMaxHealth <= 0) return;

        ReCalcMaxHP();
        UpdateHealthBar();
    }


    /// <summary>
    /// 应用装备buff属性
    /// 其中的（生命、攻击、减伤、回血、吸血，放在基础属性里面去）
    /// </summary>

    private void ApplyEquipBuff()
    {
        //0   生命点数增加
        //1   攻击百分比
        //2   改生命恢复量百分比
        //3   攻击力
        //4   改伤害半径百分比
        //5   改狂暴时长百分比
        //6   改能量上限百分比
        //7   改子弹伤害百分比
        //8   伤害加成点数
        //9   伤害减免点数
        //10  改狂暴伤害百分比
        //11  改射程
        //12  改子弹速度
        //13  改XP值恢复速度百分比
        //14  改能量值
        //15  减伤百分比
        //16  追伤百分比
        //17  改变自己的移动速度百分比
        //18  改生命上限百分比
        //19  主武器是否激活特殊buff的词条（重机枪-燃烧（需要读buff），霰弹枪穿透，激光炮传导， 光子炮二次分裂，火箭炮跟踪敌人）
        //20  改XP技能伤害百分比
        //21  暴击率触发就伤害结果*1.5倍(只有主角的主武器有暴击效果)
        //90  改命中吸血百分比

        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(0))
        //{
        //    //Debug.LogWarning("改生命上限点数 before:" + stats.maxHealth.ToString());
        //    //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[18]).ToString());
        //    stats.maxHealth = Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[0]) + stats.maxHealth;
        //    //Debug.LogWarning("改生命上限点数 before:" + stats.maxHealth.ToString());
        //}
        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(1))
        //{
        //    //Debug.LogWarning("攻击百分比 before:" + stats.attack.ToString());
        //    //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[1]).ToString());
        //    stats.attack = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[1])) * stats.attack;
        //    //Debug.LogWarning("攻击百分比 before:" + stats.attack.ToString());
        //}
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(4))
        {
            //Debug.LogWarning("改伤害半径百分比 before:" + stats.attackScope.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[4]).ToString());
            Stats.attackScope = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[4])) * Stats.attackScope;
            //Debug.LogWarning("改伤害半径百分比 before:" + stats.attackScope.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(5))
        {
            //Debug.LogWarning("改狂暴时长百分比 before:" + stats.rageModeTime.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[5]).ToString());
            Stats.rageModeTime = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[5])) * Stats.rageModeTime;
            //Debug.LogWarning("改狂暴时长百分比 before:" + stats.rageModeTime.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(6))
        {
            Debug.LogWarning("改能量上限百分比 before:" + Stats.maxEnergy.ToString());
            Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[6]).ToString());
            Stats.maxEnergy = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[6])) * Stats.maxEnergy;
            Debug.LogWarning("改能量上限百分比 before:" + Stats.maxEnergy.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(7))
        {
            //Debug.LogWarning("改子弹伤害百分比 before:" + stats.bulletDamageAddRate.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[7]).ToString());
            Stats.bulletDamageAddRate = Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[7]);
            //Debug.LogWarning("改子弹伤害百分比 before:" + stats.bulletDamageAddRate.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(10))
        {
            //Debug.LogWarning("改狂暴伤害百分比 before:" + stats.rageAddDamage.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[10]).ToString());
            Stats.rageAddDamage = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[10])) * Stats.rageAddDamage;
            //Debug.LogWarning("改狂暴伤害百分比 before:" + stats.rageAddDamage.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(11))
        {
            //Debug.LogWarning("改射程 before:" + stats.attackDistance.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[11]).ToString());
            Stats.attackDistance = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[11])) * Stats.attackDistance;
            //Debug.LogWarning("改射程 before:" + stats.attackDistance.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(12))
        {
            //Debug.LogWarning("改子弹速度 before:" + stats.bulletSpeedAddRate.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[12]).ToString());
            Stats.bulletSpeedAddRate = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[12])) * Stats.bulletSpeedAddRate;
            //Debug.LogWarning("改子弹速度 before:" + stats.bulletSpeedAddRate.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(13))
        {
            //Debug.LogWarning("改XP值恢复速度百分比 before:" + stats.rearGunCD.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[13]).ToString());
            Stats.rearGunCD = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[13])) * Stats.rearGunCD;
            //Debug.LogWarning("改XP值恢复速度百分比 before:" + stats.rearGunCD.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(14))
        {
            //Debug.LogWarning("改能量值 before:" + stats.energyRegen.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[14]).ToString());
            Stats.energyRegen = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[14])) * Stats.energyRegen;
            //Debug.LogWarning("改能量值 before:" + stats.energyRegen.ToString());
        }
        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(15))
        //{
        //    //Debug.LogWarning("减伤百分比 before:" + stats.damageReduction.ToString());
        //    //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[15]).ToString());
        //    stats.damageReduction += Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[15]);
        //    //Debug.LogWarning("减伤百分比 before:" + stats.damageReduction.ToString());
        //}
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(16))
        {
            //Debug.LogWarning("追伤百分比 before:" + stats.addDamage.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[16]).ToString());
            Stats.addDamage = Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[16]) + Stats.addDamage;
            //Debug.LogWarning("追伤百分比 before:" + stats.addDamage.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(17))
        {
            //Debug.LogWarning("改变自己的移动速度百分比 before:" + stats.speed.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[17]).ToString());
            Stats.speed = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[17])) * Stats.speed;
            //Debug.LogWarning("改变自己的移动速度百分比 before:" + stats.speed.ToString());
        }
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(20))
        {
            //Debug.LogWarning("改XP技能伤害百分比 before:" + stats.rearGunDamage.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[20]).ToString());
            Stats.rearGunDamage = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[20])) * Stats.rearGunDamage;
            //Debug.LogWarning("改XP技能伤害百分比 before:" + stats.rearGunDamage.ToString());
        }

        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(18))
        //{
        //    //Debug.LogWarning("改生命上限百分比 before:" + stats.maxHealth.ToString());
        //    //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[18]).ToString());
        //    stats.maxHealth = (1 + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[18])) * stats.maxHealth;
        //    //Debug.LogWarning("改生命上限百分比 before:" + stats.maxHealth.ToString());
        //}
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(90))
        {
            //Debug.LogWarning("改命中吸血百分比 before:" + stats.suckBlood.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[90]).ToString());
            Stats.AbsorbHP += Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[90]);
            //Debug.LogWarning("改命中吸血百分比 before:" + stats.suckBlood.ToString());
        }
        //点数
        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(3))
        //{
        //    //Debug.LogWarning("攻击力 before:" + stats.attack.ToString());
        //    //Debug.LogWarning("value:" + LuaToCshapeManager.Instance.AllEquipmentBuff[3].ToString());
        //    stats.attack = LuaToCshapeManager.Instance.AllEquipmentBuff[3] + stats.attack;
        //    //Debug.LogWarning("攻击力 before:" + stats.attack.ToString());
        //}
        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(8))
        {
            //Debug.LogWarning("伤害加成点数 before:" + stats.damageAddPoint.ToString());
            //Debug.LogWarning("value:" + LuaToCshapeManager.Instance.AllEquipmentBuff[8].ToString());
            Stats.damageAddPoint = Stats.damageAddPoint + LuaToCshapeManager.Instance.AllEquipmentBuff[8];
            //Debug.LogWarning("伤害加成点数 before:" + stats.damageAddPoint.ToString());
        }

        //if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(9))
        //{
        //    //Debug.LogWarning("伤害减免点数 before:" + stats.damageReductionPoint.ToString());
        //    //Debug.LogWarning("value:" + LuaToCshapeManager.Instance.AllEquipmentBuff[9].ToString());
        //    stats.damageReductionPoint = stats.damageReductionPoint + LuaToCshapeManager.Instance.AllEquipmentBuff[9];
        //    //Debug.LogWarning("伤害减免点数 before:" + stats.damageReductionPoint.ToString());
        //}

        if (LuaToCshapeManager.Instance.AllEquipmentBuff.ContainsKey(19))
        {
            //Debug.LogWarning("主武器终极buff before:" + stats.maxHealth.ToString());
            //Debug.LogWarning("value:" + Globals.UnityValueTransform(LuaToCshapeManager.Instance.AllEquipmentBuff[18]).ToString());
            Stats.mainWeaponUltimateSkills = (int)LuaToCshapeManager.Instance.AllEquipmentBuff[19];
            //Debug.LogWarning("主武器终极buff before:" + stats.maxHealth.ToString());
        }
    }

    public void PerformDash()
    {
        if (PlayerPrefs.GetInt("DashTutorial2") == 1)
        {
            GameManager.instance.timeManager.SetTimescale(1.0f);
            //TODO Mobile and Console Controls
            //InputController.instance.dashButtonGlow.SetActive(false);
            //InputController.instance.gameObject.SetActive(false);
            PlayerPrefs.SetInt("DashTutorial2", 0);
        }

        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING || !playerMovement.allowDash || !InputController.instance.dashUnlocked)
            return;

        State state = stateMachine.GetState();

        bool isValidState = true;//可以空冲 (state.GetStateType() == "Flying" || state.GetStateType() == "Shoot") && playerMovement.GotMovementInput; 


        if (!isValidState)
            return;

        stateMachine.SetState<PlayerDash>();
    }

    public void PlayerDash()
    {
        //playerMovement.PlayerDash();

        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.playerBoostStart);
        dashTransform.transform.SetRotation(playerMovement.rotationDir + 90);
        dashAnimator.Play("Main", 0, 0);
    }

    public void StartShooting()
    {
        regenHealth = false;
        weapon.SetShootMode(true);
        if (!isWhiteCatType) Globals.beginSidekickShoot = weapon.isShooting;

    }
    public void EndShooting()
    {
        regenHealth = true;
        weapon.SetShootMode(false);
        allowDryShot = true;
        if (!isWhiteCatType) Globals.beginSidekickShoot = weapon.isShooting;
    }

    public void SetState<T>() where T : State
    {
        stateMachine.SetState<T>();
    }

    private void Update()
    {
        stateMachine.UpdateWithDeltaTime();

        if (playerMovement.GotMovementInput && !isBoostOn)
            TurnOnBoost(true, false);
        if (!playerMovement.GotMovementInput && isBoostOn)
            TurnOnBoost(false);

        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
            return;

        if (!Globals.allowShoot && !weapon.isShooting) // && allowDryShot
        {
            energyCooldownDt += Time.deltaTime;
            energyCooldownDt = Mathf.Clamp(energyCooldownDt, 0, 1);
            if (energyCooldownDt > 0.15f)
            {
                Stats.energy += Stats.energyRegen * Time.deltaTime * 60;
                //这个地方频繁调用在scaletime 小于1的时候会崩溃
                UpdateEnergyBar();
            }
        }

        if (isTutorialGunCooldownUpdate)
        {
            ShootTutorialUpdate();
        }
        if (isWhiteCatType)
        {
            WhiteCatUpdate();
        }
    }

    void SetAnimation(string animationName, bool loop, float mixDuration = 0.25f, int trackIndex = 0, float timeScale = 1)
    {

    }

    public void SetPlayerAnimation(PlayerAnimation anim, float mixDuration = 0.25f)
    {
        if (currentAnimation.Equals(anim))
            return;

        currentAnimation = anim;

        if (anim.Equals(PlayerAnimation.Idle))
        {
            SetAnimation(idle + (planeIndex + 1).ToString(), true, mixDuration);
        }
        else if (anim.Equals(PlayerAnimation.Flying))
        {
            SetAnimation(flying + (planeIndex + 1).ToString(), true, mixDuration);
        }
        else if (anim.Equals(PlayerAnimation.Flip))
        {
            SetAnimation(flip + (planeIndex + 1).ToString(), false, mixDuration);
        }
        else if (anim.Equals(PlayerAnimation.Dash))
        {
            SetAnimation(dash + (planeIndex + 1).ToString(), false, mixDuration);
        }
        else if (anim.Equals(PlayerAnimation.Shoot))
        {
            if (weapon.csvRow_CatSkill.Type == ((int)SkillType.Laser))
            {
                return;
            }

            if (weapon.csvRow_CatSkill.Type == ((int)SkillType.Plasma) || weapon.csvRow_CatSkill.Type == ((int)SkillType.Rocket))
            {
                SetAnimation(plasmaShooting, true, mixDuration);
                return;
            }

            SetAnimation(shooting + (planeIndex + 1).ToString() + "b", true, mixDuration);
        }
    }

    IEnumerator CompleteFlipAnimation(float duration)
    {
        yield return new WaitForSeconds(duration * 0.5f);

        float scale = planeTransform.localScale.y;
        if (scale == 1)
        {
            planeTransform.localScale = new Vector3(1, -1, 1);
        }
        else
        {
            planeTransform.localScale = new Vector3(1, 1, 1);
        }

        yield return new WaitForSeconds(duration * 0.5f);

        if (weapon.isShooting)
        {
            stateMachine.SetState<PlayerShoot>();
        }
        else
        {
            if (playerMovement.GotMovementInput)
            {
                stateMachine.SetState<PlayerFlying>();
            }
            else
            {
                stateMachine.SetState<PlayerIdle>();
            }
        }
    }

    public void PushBack(float intensity)
    {
        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
            return;

        //playerMovement.PushBack(intensity);
    }

    public void Knockback(float direction, float intensity, float damage)
    {
        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
            return;

        //playerMovement.KnockBack(intensity, direction); 

        if (damage > 0)
        {
            GotHit(damage);
        }

    }

    public bool ConsumeEnergy(double energy)
    {
        if (isWhiteCatType)
        {
            return true;
        }
        energy = energy * Stats.consumption;
        if (Stats.energy <= 0 && Globals.autoShootMode && !isWhiteCatType)
        {
            Globals.antoShootRestoredEnergying = true;
            GameManager.instance.EndAutoShoot();
            GameManager.instance.EndAutoShootAB();
        }
        if (Stats.energy > 0)
        {
            Stats.energy -= energy;
            if (Stats.energy < 0) Stats.energy = 0;
#if !UNITY_STANDALONE 



#endif


            if (Stats.energy <= 0 && !Globals.shootingEnergyTutorialExecuted)
            {
                GameManager.instance.backgroundFadeLayer.DOFade(70 / 255, 0.2f);
                Globals.shootingEnergyTutorialExecuted = true;
                Globals.allowShoot = false;
                DOTween.Sequence().SetId(schedulerId).AppendInterval(0.3f).AppendCallback(() =>
                {
                    Observer.DispatchCustomEvent("Gun_Cool_Down");
                }).Play();
            }
            energyCooldownDt = 0;
            UpdateEnergyBar();
            if (Globals.autoShootMode)
            {
                allowDryShot = false;
            }
            else
            {
                allowDryShot = true;
            }

            return true;
        }
        else
        {
            if (!allowDryShot)
            {
                AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.empty);
                //foreach (Animator anim in dryShotSmokes)
                //{
                //    if (!anim.gameObject.activeInHierarchy)
                //    {
                //        anim.gameObject.SetActive(true);
                //        anim.Play("Main", 0, 0);
                //        anim.transform.SetScale(1 / transform.localScale.x);
                //        anim.transform.SetWorldPosition(transform.position.x + Mathf.Cos(RotationInDegrees * Mathf.Deg2Rad),
                //            transform.position.y + Mathf.Sin(RotationInDegrees * Mathf.Deg2Rad));

                //        float angle = RotationInDegrees - 90;
                //        anim.transform.SetRotation(angle);

                //        DOTween.Sequence().AppendInterval(0.3f).AppendCallback(() => anim.gameObject.SetActive(false));

                //        break;
                //    }
                //}
            }
        }


        if (allowDryShot)
        {
            //Shared::playSound("res/Sounds/SFX/empty.mp3", false, 1.0f);
            allowDryShot = false;

            //setPlaneIdle(); TODO

            //foreach(Animator anim in dryShotSmokes)
            //{
            //    if (!anim.gameObject.activeInHierarchy)
            //    {
            //        anim.gameObject.SetActive(true);
            //        anim.Play("Main", 0, 0);
            //        anim.transform.SetScale(1 / transform.localScale.x * 2);
            //        anim.transform.SetWorldPosition(transform.position.x + Mathf.Cos(RotationInDegrees * Mathf.Deg2Rad),
            //            transform.position.y + Mathf.Sin(RotationInDegrees * Mathf.Deg2Rad));

            //        float angle = RotationInDegrees - 90;
            //        anim.transform.SetRotation(angle);

            //        DOTween.Sequence().AppendInterval(0.3f).AppendCallback(() => anim.gameObject.SetActive(false));

            //        break;
            //    }
            //}
        }

        return false;
    }

    public void UpdateEnergyBar()
    {
        if (isWhiteCatType)
        {
            return;
        }
        Stats.energy = System.Math.Clamp(Stats.energy, 0, Stats.maxEnergy);
        if (Globals.autoShootMode && Globals.antoShootRestoredEnergying && Stats.energy == Stats.maxEnergy)
        {
            Globals.antoShootRestoredEnergying = false;
        }
        if (isBerzerkMode)
            Stats.energy = Stats.maxEnergy;
    }

    public void GotHit(double damage, bool playHitSound = true)
    {
        //damage = damage / 10f;
        if (!canHit)
        {
            return;
        }
        if (isWhiteCatType)
        {
            return;
        }
#if !UNITY_EDITOR
        int openVibrate = PlayerPrefs.GetInt("SYSTEM_MOBILE_VIBATE", 1);
        if(openVibrate == 1) Handheld.Vibrate();
#endif
        if (LuaToCshapeManager.Instance.OpenReviveDialog)
        {
            return;
        }
        //Debug.Log("运算前造成的伤害(敌人攻击力)" + damage.ToString());
        //Debug.Log("玩家减伤百分比" + stats.damageReduction);
        //Debug.Log("玩家减伤点数" + stats.damageReductionPoint);
        //damage = damage * (1 - stats.damageReduction) - stats.damageReductionPoint;
        //新版伤害 实际伤害=怪物的攻击力-怪物攻击力*（减伤点数/10000）*（1+减伤百分比）
        damage -= damage * (Stats.damageReductionPoint / 10000) * (1 + Stats.damageReduction);
        damage = damage <= 0 ? 1 : damage;
        //Debug.Log("造成的伤害（减伤运算后）" + damage.ToString());
        if (!Globals.introComplete && GameData.instance.fileHandler.currentMission == 0)
        {
            if (damage >= Stats.health)
            {

                return;

            }
        }

        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
            return;

        if (Globals.DONTDIE)
            return;

        if (isBerzerkMode)
            return;

        var newDamage = damage;
        if (newDamage < 0)
        {
            newDamage = 0;
        }
        else
        {
            newDamage = System.Math.Max(1f, damage);
        }

        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.playerHit, 0.6f);
        Stats.health -= newDamage;
        DisplayHitText(-(int)newDamage);
        //healthBar.SetDisplayHealth(stats.health / stats.maxHealth);
        if (Stats.health <= 0)
        {
            GameManager.instance.EndAutoShoot();
            GameManager.instance.EndAutoShootAB();
            if (Globals.g_currentStageData != null && (Globals.g_currentStageData.FrontType != 3 || Globals.g_currentStageData.FrontType != 4)) //挑战关卡 和普通关卡才复活，挑战关卡没有广告
            {
                //if (GameData.instance.fileHandler.currentMission != 0 && LuaToCshapeManager.Instance.curReviveNum < Globals.g_currentStageData.Rebirthtimes[(int)Globals.gameModeType])
                if (GameData.instance.fileHandler.currentMission != 0)
                {
                    canHit = false;
                    LuaToCshapeManager.Instance.OpenReviveDialog = true;
                    //Time.timeScale = 0;
                    LuaToCshapeManager.Instance.PauseOrResumeBattle(0);
                    LuaManager.Instance.RunLuaFunction<int>("BattleManager.OpenReviveDialog", (int)Globals.gameModeType);
                    return;
                }
                else
                {
                    ChangePlayerDeath();
                }
            }
            else
            {
                ChangePlayerDeath();
            }
        }

        UpdateHealthBar(true);
    }
    //玩家死亡
    public void ChangePlayerDeath()
    {
        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.playerDeath);
        Globals.resetControls = true;
        stateMachine.SetState<PlayerDeath>();
        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypePlayer,
            transform.position, false, 5, 2.5f, Globals.CocosToUnity(150));
        weapon.RemoveAttachments();
        secondaryWeapon.RemoveAttachments();

    }

    public void UpdateHealthBar(bool flash = false)
    {
        if (playerHud && !isWhiteCatType)
        {
            float percentage = (float)(Stats.health / Stats.maxHealth.Value) * 100;

            if (percentage < 35 && percentage > 0)
            {
                if (!lowHealthSmoke.isEmitting) lowHealthSmoke.Play();
                //this.schedule(schedule_selector(Player::createPlayerLowHealthSmokeNonAttached), 0.03f);

                //_lowHealthSprite.setVisible(true);
                //_lowHealthSprite.runAction(RepeatForever::create(Sequence::create(FadeTo::create(0.25f, 100), FadeTo::create(0.25f, 200), NULL)));
            }
            else
            {
                if (lowHealthSmoke.isEmitting) lowHealthSmoke.Stop();
                //this.unschedule(schedule_selector(Player::createPlayerLowHealthSmokeNonAttached));
                //_lowHealthSprite.stopAllActions();
                //_lowHealthSprite.setOpacity(0);
                //if (flash)
                //{
                //    _lowHealthSprite.setOpacity(70);
                //    _lowHealthSprite.runAction(Sequence::create(Show::create(), FadeTo::create(0.2f, 0), Hide::create(), NULL));
                //}
            }
            playerHud.UpdateHealthBar(percentage);
            SetHealthBar(percentage);
        }
    }

    private void SetHealthBar(float percentage)
    {
        percentage = Mathf.Clamp(percentage, 0, 100);
        percentage /= 100f;
        if (percentage < 0)
        {
            percentage = 0;
        }
        if (percentage > 1)
        {
            percentage = 1;
        }
        //Debug.Log("healthBarRect =" + percentage);
        if (!float.IsNaN(percentage))
        {
            healthBarRect.transform.localScale = new Vector2(percentage, 1);
        }
        //Debug.Log("prePercentage=" + prePercentage+"   percentage=" + percentage);
        //Debug.Log("percentage=" + percentage);
        Stats.health = Stats.health < 0 ? 0 : Stats.health;
        string healthNumStr = Stats.health.ToString("f0") + "/" + Stats.maxHealth.Value.ToString("f0");
        LuaManager.Instance.RunLuaFunction("BattleManager.UpdatePlayerHealth", percentage, healthNumStr);
    }
    public void SetEnergyhBar(float percentage)
    {
        percentage = Mathf.Clamp(percentage, 0, 100);
        percentage /= 100f;
        if (percentage < 0)
        {
            percentage = 0;
        }
        if (percentage > 1)
        {
            percentage = 1;
        }
        if (!float.IsNaN(percentage))
        {
            energyBarRect.transform.localScale = new Vector2(percentage, 1);
        }
    }

    /// <summary>
    /// 回血一次
    /// </summary>
    public void RegenerateHealth()
    {
        if (!Globals.canRegenerateHealth)
        {
            return;
        }

        if (Globals.allowShoot)
        {
            return;
        }

        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
        {
            return;
        }

        if (!regenHealth)
        {

        }


        if (Stats.health < Stats.maxHealth.Value && !isWhiteCatType)
        {
            Stats.health = Stats.health + Stats.regen;
            if (Stats.health > Stats.maxHealth.Value)
            {
                Stats.health = Stats.maxHealth.Value;
            }

            //Debug.Log($"回血:{Stats.regen}");
            healedAmount += (int)Stats.regen;

            UpdateHealthBar();
            DisplayHitText((int)Stats.regen);
            //healthRegenAnimator.Play("Main", 0, 0);
        }


        //if (stats.health > stats.maxHealth)
        //{
        //    stats.health = stats.maxHealth;
        //    // stop here //
        //    //HideHealthRegenAnimation(); TODO
        //    // if (Globals.isTutorial)
        //    // {
        //    //     Observer.DispatchCustomEvent("Health_Regen_Complete");
        //    // }
        //}


    }

    public void DisableHealthBar()
    {
        //healthBar.gameObject.SetActive(false);
    }

    public void LevelUp()
    {
        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.levelUp);

        GameData.instance.fileHandler.playerLevel += 1;
        GameData.instance.fileHandler.playerXP = 0;

        GameData.instance.fileHandler.xpRequiredThisLevel = 300 + 100 * ((GameData.instance.fileHandler.playerLevel - 1) * (GameData.instance.fileHandler.playerLevel - 1));

        PlayerPrefs.SetInt("playerLevel", GameData.instance.fileHandler.playerLevel);
        PlayerPrefs.SetInt("playerXP", GameData.instance.fileHandler.playerXP);
        PlayerPrefs.SetInt("totalXP", GameData.instance.fileHandler.totalXP);
        GameData.instance.fileHandler.PlayerAttack = GameData.instance.fileHandler.PlayerAttack + ((int)System.Convert.ToDouble(((GameData.instance.GetPlayerData()["Stats"] as PList)["attack"] as PList)["multiplier"]));

        GameData.instance.fileHandler.PlayerArmor = GameData.instance.fileHandler.PlayerArmor + 1;

        GameData.instance.fileHandler.PlayerHealth += (int)System.Convert.ToDouble(((GameData.instance.GetPlayerData()["Stats"] as PList)["health"] as PList)["multiplier"]) + GameData.instance.fileHandler.playerLevel;

        Globals.didLevelUp++;

        PlayerPrefs.SetInt("PlayerAttack", GameData.instance.fileHandler.PlayerAttack);
        PlayerPrefs.SetInt("PlayerArmor", GameData.instance.fileHandler.PlayerArmor);
        PlayerPrefs.SetInt("PlayerHealth", GameData.instance.fileHandler.PlayerHealth);

        float healthPercentage = (float)(Stats.health / Stats.maxHealth.Value);

        Stats.attack = GameData.instance.fileHandler.PlayerAttack;
        Stats.armor = GameData.instance.fileHandler.PlayerArmor;
        Stats.maxHealth.Value = GameData.instance.fileHandler.PlayerHealth;
        Stats.health = healthPercentage * Stats.maxHealth.Value;
        if (Stats.health > Stats.maxHealth.Value)
        {
            Stats.health = Stats.maxHealth.Value;
        }

        UpdateHealthBar(false);

        //shopGlowSkeleton.gameObject.SetActive(true);
        //shopGlowSkeleton.state.SetAnimation(0, "upgradeGlow", true);
        //shopGlowSkeleton.timeScale = 0.5f;
        //DOTween.Sequence().AppendInterval(0.75f).AppendCallback(() => shopGlowSkeleton.gameObject.SetActive(false));

        effectsSkeleton.gameObject.SetActive(true);
        effectsSkeleton.state.SetAnimation(0, "levelUp", false);
        DOTween.Sequence().AppendInterval(0.75f).AppendCallback(() => effectsSkeleton.gameObject.SetActive(false));
    }

    public void PoisonPlayer(float time, bool shake)
    {
        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
            return;

        poisonPlayer = true;


        if (shake == true)
        {
            Globals.ShakeScreenFunc(gameObject, 10, 0);
        }

        DOTween.Sequence().AppendInterval(time).AppendCallback(() =>
        {
            poisonPlayer = false;
        });
    }


    public void SetSideKickShoot(bool on)
    {
        foreach (Sidekick sk in GameSharedData.Instance.sidekicksList)
        {
            //if (on)
            //{
            //    sk.SideKickSkeleton.state.SetAnimation(0, "idle", true);

            //}
            //else
            //{
            //    sk.SideKickSkeleton.state.SetAnimation(0, "menuIdle", true);
            //}
        }
    }

    public void TurnOnBoost(bool val, bool shake = true)
    {
        if (isWhiteCatType)
        {
            return;
        }
        if (Mode == PLAYERMODE.PLAYER_MODE_FLYING)
        {
            if (val)
            {
                if (!isBoostOn)
                {
                    boostStartingParticle.Play();
                    boostTransform.DOKill();
                    DOTween.Kill("BoostEnd");
                    boostTransform.gameObject.SetActive(true);

                    DOTween.Sequence().SetId("BoostStart").Append(boostTransform.DOScale(4, 0.15f));

                    if (shake) GameManager.instance.ShakeCamera(0.25f, 1);

                    if (!isWhiteCatType)
                    {
                        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.playerBoostStart, 0.15f);
                        AudioManager.instance.PlaySound(AudioType.Loop, Constants_Audio.Audio.planeBoostLoop, 0.25f);
                    }
                    //char ch[64];
                    //sprintf(ch, "res/Sounds/SFX/planeBoost%d", 1 + rand() % 4);
                    //Shared::playSound("res/Sounds/SFX/playerBoostStart.mp3", false, 0.15f);
                    //_boostSoundId = Shared::playSound("res/Sounds/SFX/planeBoostLoop.mp3", true, 0.25f);
                }
            }
            else
            {
                if (isBoostOn)
                {
                    AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.planeBoostLoop);
                    //experimental::AudioEngine::stop(_boostSoundId);
                    boostTransform.DOKill();
                    DOTween.Kill("BoostStart");
                    DOTween.Sequence().SetId("BoostEnd").Append(boostTransform.DOScale(0, 0.15f)).AppendCallback(() =>
                    {
                        boostTransform.gameObject.SetActive(false);
                    });
                }

            }



            isBoostOn = val;
        }
        else
        {
            boostTransform.DOKill();
            DOTween.Kill("BoostStart");
            DOTween.Sequence().SetId("BoostEnd").Append(boostTransform.DOScale(0, 0.15f)).AppendCallback(() =>
            {
                boostTransform.gameObject.SetActive(false);
            });
        }
    }

    public bool GetThrust()
    {
        return thrust;
    }

    public void SetPlaneIdle()
    {
        if (GetThrust())
        {
            stateMachine.SetState<PlayerFlying>();
        }
        else
        {
            stateMachine.SetState<PlayerIdle>();
        }
    }

    void SetPlaneShoot()
    {


        if (Globals.disableShooting)
            return;
        stateMachine.SetState<PlayerShoot>();
    }

    public void SetThrust(bool val)
    {
        thrust = val;
        if (val)
        {
            if (!Globals.allowShoot)
            {
                stateMachine.SetState<PlayerFlying>();
            }
        }
        else
        {
            if (!Globals.allowShoot)
            {
                stateMachine.SetState<PlayerIdle>();
            }
        }
    }

    public void EnterBerzerkMode()
    {
        if (!Globals.canBerserk)
            return;
        if (Globals.isFTUETutorial)
        {
            GameManager.instance.controlsTutorialManager.ChangeState(TutorialState.Exit_Berserk_Mode);
        }
        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING)
        {
            return;
        }
        if (UnityEngine.Random.value < 0.5f)
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {

                AudioManager.instance.PlayMusic(Track.berserk_mode_1, true, 1);
            }
            else
            {

                AudioManager.instance.PlayMusic(7002);
            }
        }
        else
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {
                AudioManager.instance.PlayMusic(Track.berserk_mode_2, true, 1);
            }
            else
            {
                AudioManager.instance.PlayMusic(7003);

            }
        }
        LuaToCshapeManager.Instance.canCollected = false;
        berserkSkeleton.transform.localScale = new Vector3(30, 30, 1);
        berserkSkeleton.gameObject.SetActive(true);
        berserkSkeleton.state.SetAnimation(0, "berserk", true);
        berserkSkeleton.timeScale = 2;
        Stats.attackSpeed += 4f;
        Stats.turnSpeed = 1.5f;
        Stats.speed = 1.5f;
        GameManager.instance.HideBackground();
        isBerzerkMode = true;
        berzerkTrail.emitting = true;
        DOTween.Sequence().AppendInterval(Stats.rageModeTime).AppendCallback(ExitBerzerkMode);
    }

    void ExitBerzerkMode()
    {

        isBerzerkMode = false;
        berzerkTrail.emitting = false;
        LuaToCshapeManager.Instance.canCollected = true;
        Stats.attackSpeed -= 4f;
        Stats.turnSpeed = 1f;
        Stats.speed = tempSaveSpeed;


        if (Globals.AllowBgMusic)
        {
            if (GameData.instance.fileHandler.currentMission == 0)
            {

                AudioManager.instance.PlayMusic(Track.gamePlayMusic, false, 0.25f);
            }
            else
            {

                AudioManager.instance.PlayMusic(5501);
            }

        }
        berserkSkeleton.gameObject.SetActive(false);
        GameManager.instance.EnableBackground();
    }

    public void DisplayHitText(int value)
    {
        if (Mode != PLAYERMODE.PLAYER_MODE_FLYING || value == 0)
        {
            return;
        }

        var hitTMP = Instantiate(hitTextGO).GetComponent<TextMeshPro>();
        hitTMP.transform.SetParent(transform);

        hitTMP.transform.localPosition = new Vector3(0, 1.4f, 0);
        hitTMP.color = value <= 0 ? Color.red : Color.green;
        string front = value <= 0 ? "" : "+";
        hitTMP.text = front + value.ToString();
        Color c = hitTMP.color;
        c.a = 0;
        float moveY = UnityEngine.Random.Range(4f, 6f);
        DOTween.Sequence().Append(
                hitTMP.transform.DOBlendableLocalMoveBy(
                    new Vector3(UnityEngine.Random.Range(-4f, 4f), moveY), 0.5f)
                        .SetEase(Ease.InSine)
            ).AppendInterval(1f).Append(
                hitTMP.DOColor(c, 0.5f).SetEase(Ease.InSine)
            ).AppendCallback(() => Destroy(hitTMP.gameObject));

    }

    public void SetTutorialCooldownUpdate(bool val)
    {
        isTutorialGunCooldownUpdate = val;
    }

    private void ShootTutorialUpdate()
    {
        if (!Globals.allowShoot)
        {
            energyCooldownDt += 0.009f;
            if (energyCooldownDt > 0.15f)
            {
                Stats.energy += Stats.energyRegen * 0.009f * 60f;
                UpdateEnergyBar();

            }
        }
    }

    public void SetMusicParticle(bool on)
    {
    }
    public void PlayMechParticle() => mechParticle.Play();

    ///////////////////////////////////////////////
    ///白猫无人机相关的
    ///1、围绕着主角旋转类似风火轮
    ///2、到达自动射击功能，读1010的数据
    //////////////////////////////////////////////

    [HideInInspector] public bool isWhiteCatType = false;
    private int skillIndex = -1;
    [HideInInspector] public int ListIndex = 0;
    private Vector2 _direction;
    private float _distance;
    private float _whiteCatmaxDistance = 5;
    private Vector3 _targetPositon;

    public void SetWhiteCatData(int index)
    {
        //healthBar.gameObject.SetActive(false);
        healthBarRect.parent.gameObject.SetActive(false);
        energyBarRect.parent.gameObject.SetActive(false);
        weapon.csvRow_CatSkill = CatSkillScheme.Instance.GetItem(1010);
        //Debug.Log("CD=" + weapon._weaponSkillData.Cd);
        _whiteCatmaxDistance = Globals.UnityValueTransform(weapon.csvRow_CatSkill.AttackRadius);
        //Debug.Log("_whiteCatmaxDistance=" + _whiteCatmaxDistance);
        _whiteCatmaxDistance = 10f;
        weapon.Init();
        isWhiteCatType = true;
        ListIndex = index;
        lowHealthSmoke.gameObject.SetActive(false);

        Globals.numberOfWhiteCat++;
        curWhiteCatIndex = Globals.numberOfWhiteCat;
        TempMovementValueX = GameManager.instance.player.transform.position.x;
        _targetPositon = GameManager.instance.player.transform.position;
    }

    public void SetWhiteCatDataB(int level)
    {
        //healthBar.gameObject.SetActive(false);
        healthBarRect.parent.gameObject.SetActive(false);
        energyBarRect.parent.gameObject.SetActive(false);
        lowHealthSmoke.gameObject.SetActive(false);

        weapon.csvRow_CatSkill = CatSkillScheme.Instance.GetItem(4110 + level);
        weapon.Init();
        isWhiteCatType = true;
        skillIndex = 4;

        _whiteCatmaxDistance = 10;// Globals.UnityValueTransform(weapon._weaponSkillData.AttackRadius);

        ListIndex = -1;
        TempMovementValueX = GameManager.instance.player.transform.position.x;
        _targetPositon = GameManager.instance.player.transform.position;
    }
    public void UpdateWhiteCatDataBSkill(int level)
    {
        weapon.csvRow_CatSkill = CatSkillScheme.Instance.GetItem(4110 + level);
    }

    private void GetTargetPosition()
    {
        //yield return new WaitForSeconds(0.2f);
        playerMovementValue = Globals.mobileControls ? GameManager.instance.player.playerMovement.virtualJoystick.Value : GameManager.instance.player.playerMovement.movement.ReadValue<Vector2>();

        //方向是朝左的，宠物位置要减
        if (ListIndex == 0)
        {
            if (playerMovementValue.x > 0)
            {
                _targetPositon.x =
                 (GameManager.instance.player.transform.position.x - Globals.CocosToUnity(120) * curWhiteCatIndex * 2f);
                TempMovementValueX = _targetPositon.x;
            }
            else if (playerMovementValue.x < 0)
            {
                _targetPositon.x =
                (GameManager.instance.player.transform.position.x + Globals.CocosToUnity(120) * curWhiteCatIndex * 2f);
                TempMovementValueX = _targetPositon.x;
            }
            else
            {
                _targetPositon.x = TempMovementValueX + 3f;
            }
            _targetPositon.y = GameManager.instance.player.transform.position.y;
        }
        //僚机B类型
        else if (ListIndex == -1)
        {

            if (playerMovementValue.x < 0)
            {
                _targetPositon.x =
                 (GameManager.instance.player.transform.position.x + (BattleSkillManager.Instance.whiteCatList == null ? Globals.CocosToUnity(120) * 2f : Globals.CocosToUnity(120) * 3f));
                TempMovementValueX = _targetPositon.x;
            }
            else if (playerMovementValue.x > 0)
            {
                _targetPositon.x =
                 (GameManager.instance.player.transform.position.x - (BattleSkillManager.Instance.whiteCatList == null ? Globals.CocosToUnity(120) * 2f : Globals.CocosToUnity(120) * 3f));
                TempMovementValueX = _targetPositon.x;
            }
            else
            {
                _targetPositon.x = TempMovementValueX + 3f;
            }
            _targetPositon.y = GameManager.instance.player.transform.position.y + 1f;
        }
        else
        {
            _targetPositon.x = GameManager.instance.player.transform.position.x + 3f;
            if (ListIndex == 1)
            {
                _targetPositon.y = GameManager.instance.player.transform.position.y + Globals.CocosToUnity(90) * 2f;
            }
            else if (ListIndex == 2)
            {
                _targetPositon.y = GameManager.instance.player.transform.position.y - Globals.CocosToUnity(90) * 2f;
            }
            else if (ListIndex == 3)
            {
                _targetPositon.y = GameManager.instance.player.transform.position.y + Globals.CocosToUnity(90) * 4f;
            }
            else if (ListIndex == 4)
            {
                _targetPositon.y = GameManager.instance.player.transform.position.y - Globals.CocosToUnity(90) * 4f;
            }
        }
    }

    private void WhiteCatUpdate()
    {
        if (!isWhiteCatType)
        {
            return;
        }

        GetTargetPosition();


        transform.position = Vector3.Lerp(transform.position, _targetPositon, Time.deltaTime * 1.5f);
        if (shootTarget)
        {
            _distance = Vector2.Distance(transform.position, shootTarget.position);
            //Debug.Log("_whiteCatmaxDistance----------_distance=" + _distance + "   _whiteCatmaxDistance=" + _whiteCatmaxDistance);
            if (_distance < _whiteCatmaxDistance)
            {
                _direction = shootTarget.position;
                float dir = Vector2.SignedAngle(Vector2.right, _direction - (Vector2)transform.position);
                dir = dir < 0 ? 360 + dir : dir;
                playerMovement.SetPlayerRotation(dir);
            }
        }

        //if (weapon.isShooting)
        //{
        //    if (shootTarget)
        //    {
        //        _distance = Vector2.Distance(transform.position, shootTarget.position);
        //        if (_distance < _whiteCatmaxDistance)
        //        {
        //            float dir = Vector2.SignedAngle(Vector2.right, _direction - (Vector2)transform.position);
        //            dir = dir < 0 ? 360 + dir : dir;
        //            playerMovement.SetPlayerRotation(dir);
        //        }
        //    }

        //    //_direction = Globals.nearestAliveEnemy;
        //    //_distance = Vector2.Distance(transform.position, Globals.nearestAliveEnemy);
        //    //if (_distance < _whiteCatmaxDistance)
        //    //{
        //    //    float dir = Vector2.SignedAngle(Vector2.right, _direction - (Vector2)transform.position);
        //    //    dir = dir < 0 ? 360 + dir : dir;
        //    //    playerMovement.SetPlayerRotation(dir);
        //    //}
        //}
        //else
        //{
        //    //if (GameManager.instance.player.RotationInDegrees < 90 || GameManager.instance.player.RotationInDegrees > 270)
        //    //{
        //    //    playerMovement.SetPlayerRotation(0f);
        //    //}
        //    //else
        //    //{
        //    //    playerMovement.SetPlayerRotation(180f);
        //    //}

        //    playerMovement.SetPlayerRotation(GameManager.instance.player.RotationInDegrees);
        //}
    }

}
