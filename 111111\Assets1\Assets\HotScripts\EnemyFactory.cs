using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;
using DG.Tweening;
using TMPro;
using System.Linq;

public struct WaveInfoStruct
{
    public int RewardCoins;
    public int EnemyCount;
    public WaveInfoStruct(int rewardCoins, int enemyCount)
    {
        RewardCoins = rewardCoins;
        EnemyCount = enemyCount;
    }
}
public struct StageInfoStruct
{
    public string Mission;
    public int SpawnDistance;
    public int BoundaryDistance;
    public int AllowBoundaryFollow;
    public bool DestroyAll;
    public int EnemyInfoID;
    public StageInfoStruct(string mission, int spawnDistance, int boundaryDistance, int allowBoundaryFollow, bool destroyAll, int enemyInfoID)
    {
        Mission = mission;
        SpawnDistance = spawnDistance;
        BoundaryDistance = boundaryDistance;
        AllowBoundaryFollow = allowBoundaryFollow;
        DestroyAll = destroyAll;
        EnemyInfoID = enemyInfoID;
    }
}
public class EnemyFactory : MonoBehaviour
{
    struct einfo
    {
        string EnemyStruct;
        float dTime;
        bool clearScreen;
        int count;
        int nOnScreen;
    };

    public bool _isEnabled = true;

    public Dictionary<string, BattleBrushEnemy.Item> _eInfo;
    public Dictionary<string, StageInfoStruct> _sInfo;
    public Dictionary<string, WaveInfoStruct> _wInfo;

    int _interEnemyCount = 0;
    bool _repeatable = false;
    uint _eCount = 0;
    int disableReward = 0;
    int currentVal = 0;
    bool isBoosSoloMode = false;
    bool readFromResources = false;
    int enemiesGeneratedInCurrentWave = 0;
    const string trueMark = "TRUE";

    [HideInInspector] public MonsterGold.Item extendMonster;
    int _mapCount;

    /// <summary>
    /// 祖玛怪物下标
    /// </summary>
    private int zumaIndex = 0;

    public static EnemyFactory Create()
    {
        var eFactory = new GameObject().AddComponent<EnemyFactory>();
        eFactory.Init();

        return eFactory;
    }

    IEnumerator ScheduleFunction(float delay, Action callBack)
    {
        yield return new WaitForSeconds(delay);

        callBack?.Invoke();
    }
    IEnumerator ScheduleFunctionUnscale(float delay, Action callBack)
    {
        yield return new WaitForSecondsRealtime(delay);

        callBack?.Invoke();
    }
    /// <summary>
    /// 初始化祖玛怪移动路径点集合
    /// </summary>
    /// <param name="map"></param>
    void InitZumaPath(BattleBrushEnemy.Item map)
    {
        zumaIndex = 0;
        GameSharedData.Instance.ZumaPathsPosList.Clear();
        GameSharedData.Instance.ZumaEnemies.Clear();

        Vector2 playerPos = GameManager.instance.player.transform.position;
        if (map.MoveType == 1)//祖玛路径
        {
            float pointX = UnityEngine.Random.Range(-0.10f, 0.10f);
            float pointY = UnityEngine.Random.Range(-0.10f, 0.10f);
            Vector2 pos = playerPos + new Vector2(pointX, pointY);
            List<Vector2> centers = new List<Vector2>();
            int pathNum = (int)map.MoveParams[0];
            if (pathNum == 1) centers = new List<Vector2>() { { playerPos + new Vector2(pointX, pointY) } };
            else if (pathNum == 2) centers = new List<Vector2>() { { playerPos + new Vector2(pointX, pointY) }, { playerPos + new Vector2(-pointX, -pointY) } };
            else if (pathNum == 3) centers = new List<Vector2>() { { playerPos + new Vector2(0, -0.1f) }, { playerPos + new Vector2(0.1f, 0.1f) }, { playerPos + new Vector2(-0.1f, 0.1f) } };
            else if (pathNum == 4) centers = new List<Vector2>() { { playerPos + new Vector2(0, 0.1f) }, { playerPos + new Vector2(0, -0.1f) }, { playerPos + new Vector2(0.1f, 0) }, { playerPos + new Vector2(-0.1f, 0) } };
            Vector2 dir;
            for (int pathId = 0; pathId < pathNum; pathId++)
            {
                GameSharedData.Instance.ZumaEnemies.Add(new List<Enemy>() { });
                List<Vector2> path = new List<Vector2>();
                pos = centers[pathId];
                path.Add(pos);

                for (int i = 0; i < 300; i++)
                {
                    dir = Quaternion.Euler(0, 0, 180 - map.MoveParams[1]) * (playerPos - pos).normalized * 0.5f;
                    pos = pos + dir;
                    path.Add(pos);
                    path.Reverse();
                    if (Vector2.Distance(playerPos, pos) >= 15) break;
                }
                GameSharedData.Instance.ZumaPathsPosList.Add(path);
            }
        }
        else if(map.MoveType == 2)//蛇形路径
        {
            Quaternion q1 ;
            Quaternion q2 ;
            List<Vector2[]> dirList = new List<Vector2[]>();
            int pathNum = (int)map.MoveParams[0];
            if (pathNum == 1)
            {
                q1 = Quaternion.Euler(0, 0, UnityEngine.Random.Range(0, 360));
                dirList = new List<Vector2[]>()
                {
                    new Vector2[4] { q1*Vector2.up, q1*Vector2.left, q1*Vector2.up, q1*Vector2.right }
                };
            }
            else if (pathNum == 2)
            {
                q1 = Quaternion.Euler(0, 0, UnityEngine.Random.Range(0, 360));
                dirList = new List<Vector2[]>()
                {
                    new Vector2[4] {q1 * Vector2.up, q1 * Vector2.left, q1 * Vector2.up, q1 * Vector2.right } ,
                    new Vector2[4] {q1 * Vector2.down, q1 * Vector2.left, q1 * Vector2.down, q1 * Vector2.right } ,
                };
            }
            else if (pathNum == 3)
            {
                q1 = Quaternion.Euler(0, 0, 45);
                q2 = Quaternion.Euler(0, 0, -45);
                dirList = new List<Vector2[]>()
                {
                    new Vector2[4] { Vector2.up, Vector2.left, Vector2.up, Vector2.right } ,
                    new Vector2[4] { q1*Vector2.left, q1*Vector2.up, q1*Vector2.left, q1*Vector2.down } ,
                    new Vector2[4] { q2*Vector2.right, q2*Vector2.up, q2*Vector2.right, q2*Vector2.down } ,
                };
            }
            else if (pathNum == 4)
            {
                q1 = Quaternion.Euler(0, 0, 45);
                dirList = new List<Vector2[]>()
                {
                    new Vector2[4] { q1*Vector2.up, q1*Vector2.left, q1*Vector2.up, q1*Vector2.right } ,
                    new Vector2[4] { q1*Vector2.down, q1 * Vector2.left, q1 * Vector2.down, q1 * Vector2.right } ,
                    new Vector2[4] { q1*Vector2.left, q1*Vector2.up, q1*Vector2.left, q1 * Vector2.down } ,
                    new Vector2[4] {q1 * Vector2.right, q1 * Vector2.up, q1 * Vector2.right, q1 * Vector2.down } ,
                };
            }

            Vector2[] dirOffset;
            int turnNum = (int)map.MoveParams[1];//变向数
            int dirLenght = 5;//方向长度
            for (int pathId = 0; pathId < pathNum; pathId++)
            {
                GameSharedData.Instance.ZumaEnemies.Add(new List<Enemy>() { });
                List<Vector2> path = new List<Vector2>();
                dirOffset = dirList[pathId];
                Vector3 startPos = playerPos;
                Vector2 pos = startPos;
                path.Add(pos);
                for (int t = 0; t < turnNum; t++)
                {
                    for (int i = 0; i < dirOffset.Length; i++)
                    {
                        if (t == turnNum - 1 && i == dirOffset.Length - 1) break;
                        dirLenght = (int)map.MoveParams[2];
                        if (i % 2 == 0) dirLenght = (int)map.MoveParams[3];
                        for (int j = 0; j < dirLenght; j++)
                        {
                            pos = pos + dirOffset[i].normalized*0.5f;
                            path.Add(pos);
                        }                       
                    }                  
                }
                path.Reverse();
                GameSharedData.Instance.ZumaPathsPosList.Add(path);
            }
        }
    }

    void Init()
    {
        string str = "";

        if (GameData.instance.fileHandler.currentMission == 0)
        {
            str = "Mission" + GameData.instance.fileHandler.currentMission.ToString();
            readFromResources = true;
        }
        else
        {
            str = Globals.g_currentStageData.Maps.Count > 1 ? Globals.g_currentStageData.Maps[((int)Globals.gameModeType)] : Globals.g_currentStageData.Maps[0];
        }

        extendMonster = MonsterGoldScheme.Instance.GetItem(str);

        //PList map = new PList(fileName)[str] as PList;
        var map = MissionInfoScheme.Instance.GetItem(str, readFromResources);
        _repeatable = map.RepeatEnemies == trueMark;// (bool)map[Globals.REPEATABLE];
        //_eInfo = map[Globals.ENEMY_INFO] as PList;
        SetAllEnemyInfoData(map.EnemyInfoes);
        if (map.WaveInfo != "-1")// ContainsKey(Globals.WAVE_INFO))
        {
            //_wInfo = map[Globals.WAVE_INFO] as PList;
            SetAllWaveData(map.WaveInfo);
            Globals.totalEnemiesInCurrentWave = _wInfo["Wave1"].EnemyCount;//(int)(_wInfo["Wave1"] as PList)["EnemyCount"];
            GameManager.instance.playerHud.survivalTotalPoint = Globals.totalEnemiesInCurrentWave;
            GameManager.instance.playerHud.survivalCurPoint = 0;
            GameManager.instance.playerHud._wInfo = _wInfo;
            GameManager.instance.playerHud.survivalResidueWave = _wInfo.Count;//(int)_wInfo["TotalWave"];
            GameManager.instance.playerHud.survivalResidueWave--;
        }
        if (map.StageID != "-1")//map.ContainsKey(Globals.STAGE_INFO))
        {
            //_sInfo = map[Globals.STAGE_INFO] as PList;
            SetAllStageData(map.StageID);
        }
        _eCount = 0;
        _interEnemyCount = 0;
        disableReward = 0;
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            SetEnabled(false);
        }
        //第一波进来三选一
        if (Globals.gameType == GameType.Survival || Globals.g_currentStageData.FrontType == 2)
        {
            StartCoroutine(ScheduleFunction(0.8f, () =>
            {
                LuaToCshapeManager.Instance.StopShoot();
                LuaManager.Instance.RunLuaFunction<bool>("BattleManager.UpgradeSkillHandle", false);
            }));
        }


        StartCoroutine(ScheduleFunction(1.2f, CheckQueue));

        {
            Observer.RegisterCustomEvent(gameObject, "newWave", () =>
            {
                Globals.survivalModeWaveCounter++;

                string cr = "Wave" + (Globals.survivalModeWaveCounter - 1).ToString();
                int a = _wInfo[cr].RewardCoins;//(int)(_wInfo[cr] as PList)["RewardCoins"];
                GameData.instance.fileHandler.coins += a;

                StartCoroutine(ScheduleFunction(1.5f, () =>
                {
                    Globals.totalEnemiesInCurrentWave = 0;

                    string sc = "Wave" + Globals.survivalModeWaveCounter.ToString();
                    Debug.Log("Globals.survivalModeWaveCounter = " + Globals.survivalModeWaveCounter);
                    //if (Globals.survivalModeWaveCounter>=2)
                    //{
                    //    GameManager.instance.GameOver();
                    //    return;
                    //}
                    if (_wInfo.Count > 0 && !_wInfo.ContainsKey(sc))
                    {
                        CheckCanEndGame();
                        return;
                    }
                    Globals.totalEnemiesInCurrentWave = _wInfo[sc].EnemyCount;//(int)(_wInfo[sc] as PList)["EnemyCount"];
                                                                              //每一波重置杀怪数（有bug，会多生成怪）
                    GameManager.instance.playerHud.survivalCurPoint = GameManager.instance.playerHud.survivalTotalPoint;
                    // GameManager.instance.killsThisRun = GameManager.instance.playerHud.survivalTotalPoint;
                    GameManager.instance.playerHud.survivalTotalPoint += Globals.totalEnemiesInCurrentWave;
                    enemiesGeneratedInCurrentWave = 0;
                    GameManager.instance.playerHud.survivalResidueWave--;
                    string str = "Enemy" + (_eCount + 1).ToString();
                    SetTimerOnGeneration(_eInfo[str]);//_eInfo[str] as PList);
                    //生成额外的定时怪
                    if (extendMonster != null && extendMonster.Wave == Globals.survivalModeWaveCounter && (UnityEngine.Random.value * 10000 <= extendMonster.Prob))
                    {
                        for (int i = 0; i < extendMonster.Number; i++)
                        {
                            Enemy enemy = GameManager.instance.SpawnEnemy(Globals.BALLOON);
                            //string newMonsterID = extendMonster.MonsterNew + "_" + extendMonster.Level;
                            int newMonsterID = extendMonster.Level;
                            enemy.SetEnemyLevel(1, Globals.BALLOON, newMonsterID);
                            enemy.timeOfStopMode = extendMonster.StopTime / 1000;
                            enemy.StartTimeOfStopMode();
                        }
                    }


                }));
            });
        }
    }

    public void CheckCanEndGame()
    {
        if (GameSharedData.Instance.enemyList.Count == 0)
        {
            Debug.Log("生存模式结束的时候玩家的血量" + GameManager.instance.player.Stats.health);
            if (GameManager.instance.player.Stats.health <= 0)
            {
                GameManager.instance.player.ChangePlayerDeath();
            }
            else
            {
                //GameManager.instance.GameOver();
            }
        }
        else
        {
            StartCoroutine(RunAction(0.5f, () =>
            {
                CheckCanEndGame();
            }));
        }
    }


    public void Reset()
    {
        _eCount = 0;
        _interEnemyCount = 0;
    }

    public void SetEnabled(bool val)
    {
        _isEnabled = val;

        if (val)
        {
            CheckQueue();
        }
        else
        {
            StopAllCoroutines();
        }
    }

    void CheckQueue()
    {
        if (_isEnabled == false)
            return;

        if (_eInfo.Count == 0)
            return;

        if (_repeatable)
        {
            if (_eCount % _eInfo.Count == 0)
            {
                _eCount = 0;
                if (_interEnemyCount == 0)
                {
                    disableReward++;
                }
            }
            string str = "Enemy" + (_eCount + 1).ToString();
            SetTimerOnGeneration(_eInfo[str]);//_eInfo[str] as PList);

            if (_eInfo[str].EventID != -1 && _interEnemyCount == 0)//((_eInfo[str] as PList).ContainsKey("event") && _interEnemyCount == 0) // TODO
            {
                string eName = BattleEventInfoScheme.Instance.GetItem(_eInfo[str].EventID, readFromResources).Name;
                Observer.DispatchCustomEvent(eName);
                //, (void*)_eInfo.at(str).asValueMap().at("event").asValueMap().at("Data").asString().c_str());
            }
        }
        else
        {
            if (_eCount + 1 <= _eInfo.Count)
            {
                // for survival starts//

                if (currentVal < (int)_eCount + 1)
                {
                    currentVal = (int)_eCount + 1;
                    string stri = "Enemy" + (_eCount + 1).ToString();
                }
                // for survial ends //
                string str = "Enemy" + (_eCount + 1).ToString();
                if (!Globals.canProceedInEnemyFactory)
                {
                    _eCount = 1;
                }

                SetTimerOnGeneration(_eInfo[str]);

                if (_eInfo[str].EventID != -1 && _interEnemyCount == 0)
                {
                    string eName = BattleEventInfoScheme.Instance.GetItem(_eInfo[str].EventID, readFromResources).Name;
                    Observer.DispatchCustomEvent(eName);
                    //Director::getInstance()->getEventDispatcher()->dispatchCustomEvent(_eInfo.at(str).asValueMap().at("event").asValueMap().at("Name").asString()
                    //                                                                   , (void*)_eInfo.at(str).asValueMap().at("event").asValueMap().at("Data").asString().c_str());
                }
            }
        }
    }

    IEnumerator RunAction(float delay, Action action)
    {
        yield return new WaitForSeconds(delay);

        action?.Invoke();
    }


    void SetTimerOnGeneration(BattleBrushEnemy.Item map)
    {
        if (isBoosSoloMode && GameSharedData.Instance.enemyList.Count == 0)
        {
            isBoosSoloMode = false;
        }
        if (isBoosSoloMode)
        {
            StartCoroutine(RunAction(0.5f, () => SetTimerOnGeneration(map)));
            return;
        }
        if (GameSharedData.Instance.enemyList.Count >= (map.CountOnScreen) && _isEnabled)
        {
            StartCoroutine(RunAction(0.5f, () => SetTimerOnGeneration(map)));

            return;
        }

        float delayTime = Convert.ToSingle(map.DelayTime);
        float monsterNum = map.BrushNum;

        Camera cam = GameManager.instance.cameraMovement.GetCamera();
        // 刷怪前调整相机位置
        //cam.transform.localPosition = new Vector3(cam.transform.localPosition.x, cam.transform.localPosition.y, -map.CameraPositionZ);
        if (cam.transform.localPosition.z != -map.CameraPositionZ)
        {
            cam.transform.DOLocalMoveZ(-map.CameraPositionZ,3f);
        }

        if (GameSharedData.Instance.ZumaPathsPosList.Count == 0) InitZumaPath(map);
        //Debug.Log("准备延时执行刷怪" + delayTime.ToString());
        StartCoroutine(RunAction(delayTime, () =>
        {
            //Debug.Log("延时完开始刷");
            for (int i = 0; i < monsterNum; i++)
            {
                if (Globals.gameType == GameType.Survival)
                {
                    if (enemiesGeneratedInCurrentWave < Globals.totalEnemiesInCurrentWave)
                    {
                        string str = "Enemy" + (_eCount + 1).ToString();
                        map = _eInfo[str];
                        GenerateEnemy(map);
                    }
                }
                else
                {
                    GenerateEnemy(map);
                }
            }
            CheckQueue();
        }));
    }

    
    [IFix.Patch]
    void GenerateEnemy(BattleBrushEnemy.Item map)
    {
        //怀疑是延时生成的时候，同时有多个一起进行，导致多生成了怪物
        if (GameSharedData.Instance.enemyList.Count >= (map.CountOnScreen) && _isEnabled)
        {
            return;
        }


        if (map.BossSolo == trueMark)
        {
            isBoosSoloMode = true;
        }

        Globals.boosLevel = map.Level;
        if (Globals.gameType == GameType.Survival)
        {
            enemiesGeneratedInCurrentWave++;
        }

        //怪物类型，6种常规敌人数据
        string nextSpawn = map.EnemyName;// map[Globals.ENEMY_TYPE] as string;
        Enemy enemy = null;
        bool linkLevel = true;

        if (nextSpawn == Globals.SEVERSKY)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn + map.Type);
        }
        else if (nextSpawn == Globals.BOMBERKITTY)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
            //enemy = BomberKitty::create();
            //this->addChild(enemy, 2);
        }
        else if (nextSpawn == Globals.CUTIN)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
        }
        else if (nextSpawn == Globals.BALLOON)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
        }
        else if (nextSpawn == Globals.BALLOONENEMY)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
            enemy.transform.SetWorldPositionX(Globals.CocosToUnity(-1000));
        }
        else if (nextSpawn == Globals.DEATHWING)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
            //enemy = DeathWing::create();
            //this->addChild(enemy, 2);
        }
        else if (nextSpawn == Globals.DEATHBIRD)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
        }
        else if (map.MoveType == 1 || map.MoveType == 2)
        {
            for (int i = 0;i < map.MoveParams[0];i++)
            {
                enemy = GameManager.instance.SpawnEnemy(nextSpawn);
                enemy.gameObject.layer = LayerMask.NameToLayer("ZumaEnemy");
                enemy.ZumaQueneIndex = zumaIndex;
                enemy.ZumaPathPosIndex = 0;
                enemy.zumaPathIndex = i;
                enemy.transform.SetWorldPosition(GameSharedData.Instance.ZumaPathsPosList[enemy.zumaPathIndex][0].x, GameSharedData.Instance.ZumaPathsPosList[enemy.zumaPathIndex][0].y);
                enemy.SetEnemyLevel(1, nextSpawn, map.Id);
                if (i == map.MoveParams[0]-1) zumaIndex++;
                GameSharedData.Instance.ZumaEnemies[i].Add(enemy);
            }
        }
        else if (nextSpawn == Globals.BEE || nextSpawn == Globals.BULLETFAST || nextSpawn == Globals.BULLETSLOW || nextSpawn == Globals.BOMBSLOW || nextSpawn == Globals.LASERATT || nextSpawn == Globals.THUNDERATT
            || nextSpawn == Globals.BEEFAST || nextSpawn == Globals.BEESLOW || nextSpawn == Globals.BEENORMAL || nextSpawn == Globals.BEENORMALSLOW || nextSpawn == Globals.BEENORMALFAST || nextSpawn == Globals.BEEHARDSLOW
            || nextSpawn == Globals.TEST_ENEMY || nextSpawn == Globals.TEST_ENEMY01 || nextSpawn == Globals.TEST_ENEMY02 || nextSpawn == Globals.TEST_ENEMY03 || nextSpawn == Globals.TEST_ENEMY04
            || nextSpawn == Globals.Spine_XiaoGua_001 || nextSpawn == Globals.Spine_XiaoGua_002 || nextSpawn == Globals.Spine_XiaoGua_004 || nextSpawn == Globals.Spine_XiaoGua_005 || nextSpawn == Globals.Spine_XiaoGua_006
            || nextSpawn == Globals.Spine_XiaoGua_007 || nextSpawn == Globals.Spine_XiaoGua_008 || nextSpawn == Globals.Spine_XiaoGua_009 || nextSpawn == Globals.Spine_XiaoGua_010 || nextSpawn == Globals.Spine_XiaoGua_015
            || nextSpawn == Globals.Spine_XiaoGua_016 || nextSpawn == Globals.Spine_XiaoGua_018 || nextSpawn == Globals.Spine_XiaoGua_019 || nextSpawn == Globals.Spine_XiaoGua_020 || nextSpawn == Globals.Spine_XiaoGua_023
            || nextSpawn == Globals.Spine_XiaoGua_027 || nextSpawn == Globals.Spine_XiaoGua_028 || nextSpawn == Globals.Spine_XiaoGua_029 || nextSpawn == Globals.Spine_XiaoGua_030)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);

            //圆形位置   刷怪位置   1500~=15
            float radius = map.DistanceToActor;
            float andgle = UnityEngine.Random.Range(0, 361);
            Vector2 playerPos = GameManager.instance.player.transform.position;
            float pointX = playerPos.x + radius * Mathf.Cos(andgle * Mathf.PI / 180);
            float pointY = playerPos.y + radius * Mathf.Sin(andgle * Mathf.PI / 180);
            enemy.transform.SetWorldPosition(pointX, pointY);
        }
        //7个怪物随从关卡数据

        //BOSS_MINIONS//
        else if (nextSpawn == Globals.TURRET)
        {
            enemy = GameManager.instance.SpawnEnemy("TURRET");
            enemy.transform.SetWorldPositionX(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1200) + Globals.CocosToUnity(UnityEngine.Random.value * 1000));
            //linkLevel = false;
        }

        else if (nextSpawn == Globals.FLYING_TURRETS)
        {
            enemy = GameManager.instance.SpawnEnemy("FLYINGTURRETS");
            //linkLevel = false;
        }

        else if (nextSpawn == Globals.SPIDER_CONSTANT_LASER)
        {

            BoundarySpiders bs = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS", null, true).GetComponent<BoundarySpiders>();
            enemy = bs;
            bs.CreateWithPosition(Globals.CocosToUnity(1000), UnityEngine.Random.Range(1, 3));
            //linkLevel = false;

        }

        else if (nextSpawn == Globals.SPIDER_LASER)
        {
            LittleSpiders ls = GameManager.instance.SpawnEnemy("SPIDERBULLET", null, true).GetComponent<LittleSpiders>();
            ls.CreateWithType(SpiderType.LASER);
            ls.isBoss = false;
            //ls.SetAttribute();
            enemy = ls;
            //linkLevel = false;
        }

        else if (nextSpawn == Globals.SPIDER_BULLET)
        {
            LittleSpiders ls = GameManager.instance.SpawnEnemy(nextSpawn, null, true).GetComponent<LittleSpiders>();
            enemy = ls;
            ls.CreateWithType(SpiderType.GUN);
            //linkLevel = false;
        }

        else if (nextSpawn == Globals.BUG_BOT)
        {
            BugBot bb = GameManager.instance.SpawnEnemy(nextSpawn, null).GetComponent<BugBot>();
            enemy = bb;
            enemy._allowKillPoint = true;
            if (UnityEngine.Random.value < 0.5f)
            {
                enemy.transform.SetWorldPosition(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            else
            {
                enemy.transform.SetWorldPosition(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            //linkLevel = false;
        }

        else if (nextSpawn == Globals.BUG_BOT2)
        {
            enemy = GameManager.instance.SpawnEnemy(nextSpawn);
            //this->addChild(enemy);
            if (Globals.gameType != GameType.Survival)
                enemy._allowKillPoint = false;
            if (UnityEngine.Random.value < 0.5f)
            {
                enemy.transform.SetWorldPosition(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            else
            {
                enemy.transform.SetWorldPosition(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            //linkLevel = false;
        }
        //BOSS_MINIONS//

        //BOSSES//
        else if (nextSpawn == Globals.SPIKY)
        {
            var seversky = GameManager.instance.SpawnEnemy(nextSpawn);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.35f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
                Globals.bossShouldStayOnScreen = true;
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(500));
                seversky.needResetZoom = true;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.3f);
                CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/enemy3"] as string, seversky, 0.5f, Vector2.zero, 0.6f);
            }
            if (UnityEngine.Random.value < 0.5f)
            {
                seversky.transform.SetWorldPosition(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            else
            {
                seversky.transform.SetWorldPosition(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
        }

        else if (nextSpawn == Globals.SUGARPUFF)
        {
            var bomber = GameManager.instance.SpawnEnemy(nextSpawn);
            bomber.healthBar.gameObject.SetActive(true);
            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            Ping.Init(bomber.transform, true);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.65f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(600));
                bomber.needResetZoom = true;
                Globals.maxCameraZoom = Globals.CocosToUnity(1300);
                //Globals.bossShouldStayOnScreen = true;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
                CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/bomber"] as string, bomber, 0.65f, Vector2.zero, 0.65f);
            }
        }
        else if (nextSpawn == Globals.VLADMIRCUTIN)
        {
            var bomber = GameManager.instance.SpawnEnemy(nextSpawn);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.45f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(450);
                Globals.bossShouldStayOnScreen = true;
                bomber.needResetZoom = true;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);

                CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/ship2"] as string,
                    bomber, 0.8f, new Vector2(0, Globals.CocosToUnity(100)), 0.8f, 0.65f);
            }
        }
        else if (nextSpawn == Globals.BARKMEOW)
        {
            var boss = GameManager.instance.SpawnEnemy(nextSpawn);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.5f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
                Globals.maxCameraZoom = Globals.CocosToUnity(1200);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(200));
                Globals.bossShouldStayOnScreen = true;
                boss.needResetZoom = true;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);

                CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/vaderboss"] as string, boss, 0.65f, Vector2.zero, 1.0f, 0.85f);
            }
            if (UnityEngine.Random.value < 0.5f)
            {
                boss.transform.SetWorldPosition(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            else
            {
                boss.transform.SetWorldPosition(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
        }

        else if (nextSpawn == Globals.MAFIAKITTY)
        {
            var boss = GameManager.instance.SpawnEnemy("TOMAS");
            PlayerPing Ping = GameManager.instance.InstantiatePrefab("PlayerPing").GetComponent<PlayerPing>();
            Ping.Init(boss.transform, true);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 1f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(350);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(850));
                boss.needResetZoom = true;
                Globals.bossShouldStayOnScreen = true;
                Globals.LEFTBOUNDARY *= 10;
                Globals.RIGHTBOUNDARY *= 10;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.65f);
                DOTween.Sequence().AppendInterval(0.1f).AppendCallback(() =>
                {
                    CreateBossEntry(
                    (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/mafiaBoss"] as string,
                    boss, 0.65f, Vector2.zero, 1.0f);
                });
            }
        }
        else if (nextSpawn == Globals.CUTEUS)
        {
            var boss = GameManager.instance.SpawnEnemy("CUTEUSMAXIMUS");
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.5f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1000));
                boss.needResetZoom = true;
                Globals.bossShouldStayOnScreen = true;

                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);
                CreateBossEntry(
                    (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/captain"] as string,
                    boss, 1.6f, new Vector2(0, 2), 1.3f, 0.8f);
            }
        }

        else if (nextSpawn == Globals.SPIDERCAT)
        {
            if (GameData.instance.fileHandler.currentMission != 0)
            {
                var boss = GameManager.instance.SpawnEnemy("SPIDERCAT");
                if (map.BossCG == trueMark)
                {
                    Globals.zoomToBossForSec = 0.30f;
                    Globals.zoomValueOnBoss = Globals.CocosToUnity(800);

                    //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.3f);
                    CreateBossEntry(
                (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/tripodBoss"] as string,
                boss, 6, new Vector2(0, -Globals.CocosToUnity(300)), 3f);

                    Globals.SetZoomValueWhileGame(Globals.CocosToUnity(1300));
                    boss.needResetZoom = true;
                }

            }
        }

        else if (nextSpawn == Globals.SENTINEL)
        {
            var boss = GameManager.instance.SpawnEnemy("SENTINEL");
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.5f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(700);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(300));
                boss.needResetZoom = true;
                Globals.maxCameraZoom = Globals.CocosToUnity(1000);

                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);

                CreateBossEntry(
                    (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/sentinel"] as string,
                    boss, 1.3f, new Vector2(0, 0.8f), 1.2f);
                Globals.bossShouldStayOnScreen = true;
            }
        }
        //下面两个boss还未支持
        else if (nextSpawn == Globals.DRAGONSISTERS)
        {
            GameManager.instance.InstantiatePrefab("DRAGONMANAGER");
            //Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
            //Globals.zoomValueWhileGame = Globals.CocosToUnity(600);
            //Globals.maxCameraZoom = Globals.CocosToUnity(1200);
            //Globals.zoomToBossForSec = 1.1f;
            //GameManager.instance.timeManager.SetTimescale(0.1f);
        }

        else if (nextSpawn == Globals.MEOWTHENA)
        {
            var boss = GameManager.instance.SpawnEnemy("MEWOTHENA");
            if (map.BossCG == trueMark)
            {
                Globals.zoomValueOnBoss = Globals.CocosToUnity(600);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(600));
                Globals.maxCameraZoom = Globals.CocosToUnity(1200);
                Globals.zoomToBossForSec = 1.1f;
                GameManager.instance.timeManager.SetTimescale(0.1f);
                boss.needResetZoom = true;
            }

        }
        else if (nextSpawn == Globals.Spine_XiaoGua_011 || nextSpawn == Globals.Spine_XiaoGua_012 || nextSpawn == Globals.Spine_XiaoGua_013 || nextSpawn == Globals.Spine_XiaoGua_014
            || nextSpawn == Globals.Spine_XiaoGua_017 || nextSpawn == Globals.Spine_XiaoGua_021 || nextSpawn == Globals.Spine_XiaoGua_022)
        {
            var boss = GameManager.instance.SpawnEnemy(nextSpawn);
            if (map.BossCG == trueMark)
            {
                Globals.zoomToBossForSec = 0.5f;
                Globals.zoomValueOnBoss = Globals.CocosToUnity(300);
                Globals.maxCameraZoom = Globals.CocosToUnity(1200);
                Globals.SetZoomValueWhileGame(Globals.CocosToUnity(200));
                Globals.bossShouldStayOnScreen = true;
                boss.needResetZoom = true;
                //GameManager.instance.timeManager.SetResetWithDelay(0.05f, 0.1f, 0.5f);

                CreateBossEntry((GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)["res/Enemy/vaderboss"] as string, boss, 0.65f, Vector2.zero, 1.0f, 0.85f);
            }
            if (UnityEngine.Random.value < 0.5f)
            {
                boss.transform.SetWorldPosition(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            else
            {
                boss.transform.SetWorldPosition(GameManager.instance.player.transform.position.x - Globals.CocosToUnity(1500), UnityEngine.Random.value * Globals.CocosToUnity(1000));
            }
            enemy = boss;
        }

        //BOSSES//


        //新增//
        else if (nextSpawn == "STONETOWER")
        {
            StoneTower st = GameManager.instance.SpawnEnemy("STONETOWER", null, true).GetComponent<StoneTower>();
            st.StoneTowerType(1);
            st.Init();
            //st.SetAttribute();
            enemy = st;
        }
        else if (nextSpawn == "ELECTRICTOWER")
        {
            ElectricTower et = GameManager.instance.SpawnEnemy("ELECTRICTOWER", null, true).GetComponent<ElectricTower>();
            et.CreateAsType1();
            //et.SetAttribute();
            enemy = et;
        }
        else if (nextSpawn == "BULLETTOWER")
        {
            BulletTower bt = GameManager.instance.SpawnEnemy("BULLETTOWER").GetComponent<BulletTower>();
            //et.CreateAsType1();
            ////et.SetAttribute();
            enemy = bt;
        }

        //新增//

        if (enemy)
        {
            //if (!enemy.isBoss && linkLevel)
            //{
            //    string newMonsterID;
            //    if (nextSpawn == Globals.SEVERSKY)
            //    {
            //        newMonsterID = map.EnemyName + "_" + map.Type + "_" + map.Level;
            //    }
            //    else
            //    {
            //        newMonsterID = map.EnemyName + "_" + map.Level;
            //    }
            //    //读pb
            //    //Debug.Log("生成怪物的时候monsterID" + newMonsterID);
            //    //Debug.Log("map[EnemyType]" + map.EnemyName);
            //    enemy.SetEnemyLevel(1, nextSpawn, newMonsterID);
            //}
            int newMonsterID = map.Id;
            if (nextSpawn == Globals.SEVERSKY)
            {
                //newMonsterID = map.EnemyName + "_" + map.Type + "_" + map.Level;
            }
            else
            {
                //newMonsterID = map.EnemyName + "_" + map.Level;
            }
            //读pb
            //Debug.Log("生成怪物的时候monsterID" + newMonsterID);
            //Debug.Log("map[EnemyType]" + map.EnemyName);
            enemy.SetEnemyLevel(1, nextSpawn, newMonsterID);
            if (disableReward > 1)
            {
                enemy.stats.xp = 0;
                enemy.stats.coinAwarded = 0;
            }

            enemy.InitSkill();
        }


        _interEnemyCount++;
        _mapCount = map.Count;

        //当前波有额外的定时怪
        //if (extendMonster != null)
        //{
        //    if(Globals.survivalModeWaveCounter == extendMonster.Wave)
        //    {
        //        _mapCount += extendMonster.Number;
        //    }
        //}


        if (_interEnemyCount >= (_mapCount) * GameManager.instance.player.enemyCountMulte)
        {
            _interEnemyCount = 0;
            _eCount++;
        }
        Globals.boosLevel = 0;
        enemy.isBoss = (map.Type == 3);
        //CheckQueue();
    }
    void CreateBossEntry(string str, Enemy enemy, float scale, Vector2 positionOffset, float fontMultiplier = 1,
        float disappearDelayMultiplier = 1)
    {
        return;
        // TODO
        var entry = GameManager.instance.InstantiatePrefab("BossEntry").GetComponent<SkeletonAnimation>();
        //entry->setVisible(false);
        var entryTransform = entry.transform;
        var enemyTransform = enemy.transform;
        entryTransform.localScale = new Vector3(scale, scale, 1);
        //entryTransform.localScale = new Vector3(1 / enemyTransform.localScale.y * 0.4f * scale,
        //    1 / enemyTransform.localScale.y * 0.4f * scale, 1);
        entryTransform.parent = enemyTransform;
        entryTransform.localPosition = positionOffset;
        //entryTransform.position = new Vector3(Globals.CocosToUnity(positionOffset.x),
        //    Globals.CocosToUnity(positionOffset.y), entryTransform.position.z);
        entry.state.SetAnimation(0, "bossEntry", false);
        entry.timeScale = 6;

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossGreek)
        {
            //entry->setGlobalZOrder(-5);
        }

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossOkto)
        {
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));
        }
        // TODO ASK
        //entry->runAction(Sequence::create(Show::create(), DelayTime::create(0.5f), ScaleTo::create(0.1f, 0), RemoveSelf::create(), NULL));
        StartCoroutine(ScheduleFunction(0.5f * disappearDelayMultiplier, () =>
        {
            StartCoroutine(nameof(EntryDisappear), entryTransform);
        }));

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossSugarPuff)
        {
            StopCoroutine(nameof(EntryDisappear));
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));
            SkeletonAnimation heart = GameManager.instance.InstantiatePrefab("HeartExplosion").GetComponent<SkeletonAnimation>();
            heart.GetComponent<MeshRenderer>().sortingLayerName = "Foreground";
            heart.GetComponent<MeshRenderer>().sortingOrder = 50;
            heart.timeScale = 2.5f;
            heart.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            heart.transform.parent = enemyTransform; ;
            heart.transform.localPosition = Vector3.zero;
            heart.state.SetAnimation(0, "entry", false);
            //heart.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            //heart->runAction(Sequence::create(Show::create(), DelayTime::create(0.15f), RemoveSelf::create(), NULL));
            StartCoroutine(ScheduleFunction(0.25f, () =>
            {
                Destroy(heart.gameObject);
            }));

            var heart2 = GameManager.instance.InstantiatePrefab("HeartExplosion").GetComponent<SkeletonAnimation>();
            heart2.timeScale = 2.5f;
            heart2.transform.localScale = new Vector3(0.35f, 0.35f, 1);
            heart2.transform.parent = enemyTransform;
            heart2.transform.localPosition = Vector3.zero;
            heart2.state.SetAnimation(0, "entry", false);
            //heart2.transform.localScale = new Vector3(0.35f, 0.35f, 1);

            StartCoroutine(ScheduleFunction(0.5f, () =>
            {
                Destroy(heart2.gameObject);
            }));
        }

        if (GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossCutin)
        {
            StopCoroutine(nameof(EntryDisappear));
            entry.skeleton.SetColor(new Color(entry.skeleton.GetColor().r,
                entry.skeleton.GetColor().g, entry.skeleton.GetColor().b, 0));

            var waterExplosion = GameManager.instance.InstantiatePrefab("WaterEntry").GetComponent<SkeletonAnimation>();
            waterExplosion.GetComponent<MeshRenderer>().sortingLayerName = "Foreground";
            waterExplosion.GetComponent<MeshRenderer>().sortingOrder = -1;
            waterExplosion.timeScale = 2f;
            waterExplosion.transform.localScale = new Vector3(1.4f, 0.75f, 1);
            waterExplosion.transform.parent = enemyTransform;
            waterExplosion.transform.localPosition = new Vector2(2.99f, -1.16f);
            waterExplosion.state.SetAnimation(0, "entry", false);
            StartCoroutine(ScheduleFunction(0.35f, () =>
            {
                Destroy(waterExplosion.gameObject);
            }));
        }

        var label = GameManager.instance.InstantiatePrefab("BossEntryLabel").GetComponent<TextMeshPro>();
        label.text = str;
        label.transform.SetParent(entryTransform);
        label.transform.localPosition = new Vector3(0, -1.11f, 0);
        label.transform.SetScale(0);
        DOTween.Sequence().AppendInterval(0.2f)
            .Append(label.transform.DOScale(1 / entryTransform.localScale.x * fontMultiplier, 0.03f));

        //if (showDialogue)
        //{
        //    if (GameData.instance.fileHandler.currentMission != 0)
        //    {
        //        DialoguePopup.CreateAsPreBattle();
        //    }
        //}
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            Observer.DispatchCustomEvent("Spawn_SpiderCat_FTUE_Dialogues");
        }

        //文字提示，boss来袭
        GameObject entryTitle = GameManager.instance.InstantiatePrefab("BossEntryTitle");
        entryTitle.transform.localScale = Vector3.one;
        entryTitle.transform.localPosition = new Vector3(0, 200f, 0);
        //entryTitle.transform.parent = enemyTransform;
        StartCoroutine(ScheduleFunctionUnscale(3f, () =>
        {
            Destroy(entryTitle.gameObject);
        }));
    }

    IEnumerator EntryDisappear(Transform entryTransform)
    {
        float duration = 0.1f;
        float scale = entryTransform.localScale.x, dt;

        while (scale > 0 && entryTransform != null && entryTransform.gameObject != null && entryTransform.gameObject.activeInHierarchy)
        {
            dt = Time.deltaTime < duration ? Time.deltaTime : duration;
            scale -= scale * (dt / duration);
            duration -= dt;

            entryTransform.localScale = new Vector3(scale, scale, 1);

            yield return null;
        }
        //TODO：这个地方会报错
        //Destroy(entryTransform.gameObject);
    }

    public void SetAllEnemyInfoData(IEnumerable<int> enemyInfoIDs)
    {
        if (_eInfo == null)
        {
            _eInfo = new Dictionary<string, BattleBrushEnemy.Item>();
        }
        if (enemyInfoIDs.First() != -1)
        {
            foreach (int brushID in enemyInfoIDs)
            {
                var item = BattleBrushEnemyScheme.Instance.GetItem(brushID, readFromResources);
                _eInfo.Add(item.EnemyNumber, item);
            }
        }
    }
    void SetAllWaveData(string waveInfo)
    {
        if (_wInfo == null)
        {
            _wInfo = new Dictionary<string, WaveInfoStruct>();
        }
        string[] allData = waveInfo.Split('|');
        foreach (string item in allData)
        {
            string[] str = item.Split(';');
            _wInfo.Add(str[0], new WaveInfoStruct(Convert.ToInt32(str[2]), Convert.ToInt32(str[1])));
        }
    }
    public void SetAllStageData(string stageInfo)
    {
        if (_sInfo == null)
        {
            _sInfo = new Dictionary<string, StageInfoStruct>();
        }
        string[] allData = stageInfo.Split('|');
        foreach (string item in allData)
        {
            string[] str = item.Split(';');
            _sInfo.Add(str[0], new StageInfoStruct(str[1], Convert.ToInt32(str[2]), Convert.ToInt32(str[3]), Convert.ToInt32(str[4]), str[5] == "TRUE", Convert.ToInt32(str[6])));
        }
    }
    public void SetAllEnemyInfoDataByID(int id)
    {
        var item = BattleEnemyInfoScheme.Instance.GetItem(id);
        SetAllEnemyInfoData(item.EnemyInfoes);
    }
}
