using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using TMPro;
public class UnlockItem : MonoBehaviour
{
    private string type;
    [SerializeField] private Image unlockImage;
    [SerializeField] private Sprite[] itemSprites;
    [SerializeField] private SkeletonGraphic lockImage;
    [SerializeField] private SkeletonGraphic lightsEffect;
    [SerializeField] private TextMeshProUGUI heading;
    [SerializeField] private TextMeshProUGUI description;
    [SerializeField] private Image overLay;
    private bool allowTouch = false;
    private string imageString;

    private string tweenId;
    private string schedulerId;
    PList MainMap;
    private void Awake()
    {
        tweenId = "UI" + GetInstanceID();
        schedulerId = "UIS" + GetInstanceID();
    }

    public void Show(string val,PList map)
    {
        type = val;
        MainMap = map;
        gameObject.SetActive(true);
        Init();
    }

    private void Update()
    {
        CheckInput();
    }

    public void Init()
    {
        ProceedUnlock();
        allowTouch = false;
        //Shared::scaleNode(this);
        // Shared::rescale(this, 1);
        lockImage.transform.SetScale(0);
        lockImage.AnimationState.SetAnimation(0, "idle", true);
        DOTween.Sequence().SetId(tweenId).AppendInterval(0.5f).Append(lockImage.transform.DOScale(Vector3.one, 0.15f)).AppendCallback(() => { lockImage.AnimationState.SetAnimation(0, "unlock", false); }).Play();
        //Shared::rescale(lock, 1.0f);


        foreach (Sprite s in itemSprites)
        {
            string[] str = imageString.Split('.');
            if (s.name == str[0])
            {
                unlockImage.sprite = s;
            }
        }
        unlockImage.gameObject.SetActive(false);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(2f).AppendCallback(() => unlockImage.gameObject.SetActive(true)).Play();
        DOTween.Sequence().SetId(tweenId).Append(unlockImage.transform.DOScale(Vector3.one*1.05f,1f).SetEase(Ease.InOutSine)).Append(unlockImage.transform.DOScale(Vector3.one, 1f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();


        lightsEffect.AnimationState.TimeScale = 0.5f;
        lightsEffect.transform.SetScale(1);
        lightsEffect.AnimationState.SetAnimation(0, "levelUp3", true);
        DOTween.Sequence().SetId(tweenId).Append(lightsEffect.transform.DOScale(Vector3.one*2.5f,1f)).Play();
        DOTween.Sequence().SetId(tweenId).Append(heading.DOFade(1,0.5f)).Play();
        DOTween.Sequence().SetId(tweenId).Append(description.DOFade(1, 0.5f)).Play();
        //Shared::rescale(description, 1.0f);


        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.15f).AppendCallback(()=>{ 
            //Globals.PlaySound("res/Sounds/SFX/sidekickUnlock.mp3");
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.sidekickUnlock);
        }).AppendInterval(0.6f).AppendCallback(()=>{

            //Globals.PlaySound("res/Sounds/SFX/boss_unlock.mp3");
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.boss_unlock);

        }).AppendInterval(1.5f).AppendCallback(()=>{
            allowTouch = true;
        });
    }

    private void ProceedUnlock()
    {
        if (!GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission).ContainsKey("Unlock"))
            return;

        imageString = (MainMap[type] as PList)["imageName"] as string;
        heading.text = (MainMap[type] as PList)["heading"] as string;
        description.text = (MainMap[type]as PList)["description"] as string;

        print(imageString);
        if (type == "DASH")
        {
#if !UNITY_STANDALONE
            description.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.UNLOCK_SCENE)["dashDescriptioniOS"]as string;
#endif
        }

    }

    private void CheckInput()
    {

        if (UnityEngine.Input.GetKeyDown(KeyCode.Escape) || UnityEngine.Input.GetKeyDown(KeyCode.Return)|| UnityEngine.Input.GetMouseButtonUp(0))
        {
            if (allowTouch)
                ExitCallback();
        }
        //};
        //_eventDispatcher.addEventListenerWithSceneGraphPriority(keyListener, this);

        //GamePad_Apple* ga = GamePad_Apple::create();
        //this.addChild(ga);
        //#if Desktop

        //    auto mouseListener = EventListenerMouse::create();
        //    mouseListener.onMouseMove = [=](EventMouse * event){

        //        event.stopPropagation();
        //    };

        //    _eventDispatcher.addEventListenerWithSceneGraphPriority(mouseListener, this);
        //#endif
    }

    private void ExitCallback()
    {
        DOTween.Sequence().SetId(tweenId).Append(overLay.DOFade(1,0.5f)).AppendCallback(()=>{
           Observer.DispatchCustomEvent("UnlockComplete");
        }).Play();
    }
}
