using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using ProtoBuf;

public class MonsterNewScheme : Singleton<MonsterNewScheme>
{
    private MonsterNew _data;
    private Dictionary<string, int> _idIndexMap;
    
    public void initScheme()
    {
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load();
        }
    }
    public bool Load()
    {
        DontDestroyOnLoad(Instance);
        int schemeIndex = (int)SchemeType.MonsterNewScheme;
        string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
        try
        {
            MemoryStream ms = new MemoryStream(HotResManager.ReadPb(pbFileName));
            _data = Serializer.Deserialize<MonsterNew>(ms);
        }
        catch
        {
            throw new Exception(pbFileName + ".pb fail");
        }
        for (int i = 0; i != _data.Items.Count; ++i)
        {
            _idIndexMap[_data.Items[i].Id] = i;

        }
        Debug.LogWarning(pbFileName + "pb succes");
        return true;

    }
    public MonsterNew.Item GetItem(string id)
    {
        // Debug.LogWarning("查询怪物iD:" + id);
        if (_idIndexMap == null)
        {
            _idIndexMap = new Dictionary<string, int>();
            Load();
        }
        if (_idIndexMap.ContainsKey(id))
        {
            return _data.Items[_idIndexMap[id]];
        }
        else
        {
            Debug.Log(id + "查询怪物id dont exist");
            return null;
        }

    }
}

