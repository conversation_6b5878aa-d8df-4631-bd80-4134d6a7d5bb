﻿using System;
using System.Collections.Generic;
using System.IO;

using ProtoBuf;
using X.PB;

namespace CsvTables
{
    public partial class EquipMountCsv : Singleton<EquipMountCsv>
    {
        private readonly Dictionary<int, EquipMount.Item> dic = new();

        protected override void InitializeSingleton()
        {
            DontDestroyOnLoad(this);
            int schemeIndex = (int)SchemeType.EquipEffect;
            string pbFileName = HandlePBManager.Instance.PbNameList[schemeIndex];
            MemoryStream ms = new(HotResManager.ReadPb(pbFileName));
            var _data = Serializer.Deserialize<EquipMount>(ms);
            foreach (var item in _data.Items)
            {
                dic.Add(item.Id, item);
            }
            //Debug.LogWarning(pbFileName + "pb succes");

            base.InitializeSingleton();
        }
        
        public EquipMount.Item GetItem(int id)
        {
            if (dic.TryGetValue(id, out var rtn))
            {
                return rtn;
            }

            throw new Exception("id dont exist");
        }
    }
}