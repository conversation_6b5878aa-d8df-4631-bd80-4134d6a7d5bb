using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;
using UnityEngine.UI;
using Spine;
using Spine.Unity;
using TMPro;
using DG.Tweening;
using UnityEngine.SceneManagement;
using System.Linq;
public class SideMenuController : MonoBehaviour
{

    [SerializeField] private Animator anim;
    [SerializeField] private RectTransform rect;
    [SerializeField] private TextMeshProUGUI missionName;
    [SerializeField] private TextMeshProUGUI missionDescription;
    [SerializeField] private TextMeshProUGUI missionNumber;
    [SerializeField] private SkeletonGraphic[] missionImageSpineData = null;
    [SerializeField] private Sprite[] abilitySprites = null;
    [SerializeField] private Sprite[] sidekickSprites = null;
    [SerializeField] private Image[] abilityRenderer = null;

    private SkeletonGraphic missionImageSpineObject = null;
    public Action addedCloseFunction;
    [HideInInspector] public Vector2 _mousePosition;
    [SerializeField] private Button playButton;


    [SerializeField] private Image fadeImage;   

    private Sequence seq;

    public void OnPlayButtonCall()
    {
        playButton.interactable =false;
        fadeImage.raycastTarget = true;
        fadeImage.DOFade(1, 0.25f).OnComplete(() => { SceneManager.LoadScene("GameScene"); });
    }

    private void ShowUnlocks()
    {
        foreach (Image i in abilityRenderer)
        {
            i.gameObject.SetActive(false);
        }
        int index = 0;
        if (GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission).ContainsKey("Unlock"))
        {
            PList MainMap = GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Unlock"] as PList;
            int abilityCount = MainMap.Count;
            string abilityName;
            for (int i = 0; i < abilityCount; i++)
            {
                index = i;
                //ValueMap MainMap = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission)["Unlock"].asValueMap(); TODO normal abilities
                //string _imageString = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission)["Unlock"].asValueMap()[pair.first].asValueMap()["imageName"].asString();

                abilityRenderer[i].material.SetFloat("_GrayscaleAmount", 0);
                SkeletonGraphic lockAnim = abilityRenderer[i].transform.GetChild(0).GetComponent<SkeletonGraphic>();
                lockAnim.gameObject.SetActive(false);
                abilityName = (MainMap[MainMap.ElementAt(i).Key] as PList)["imageName"] as string;
                foreach (Sprite s in abilitySprites)
                {
                    string[] str = abilityName.Split('.');
                    if (s.name == str[0])
                    {
                        abilityRenderer[i].sprite = s;
                    }
                }

                abilityRenderer[i].gameObject.SetActive(true);
                if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
                {
                    lockAnim.transform.SetScale(0.0f);
                    lockAnim.gameObject.SetActive(true);
                    lockAnim.AnimationState.SetAnimation(0, "idle", true);
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.25f).AppendCallback(() => lockAnim.gameObject.SetActive(true)).Append(lockAnim.transform.DOScale(new Vector3(0.22f, 0.22f, 0.22f), 1.5f).SetEase(Ease.OutElastic));
                    seq.Play();
                    seq = DOTween.Sequence();
                    seq.AppendInterval(1.35f).AppendCallback(() =>
                    {
                        abilityRenderer[i].material.SetFloat("_GrayscaleAmount", 1);
                    });
                    seq.Play();
                }
            }
        }
        PList sideKickMap;
        int sideKickCount = (int)(GameData.instance.GetMissions(GameData.instance.fileHandler.currentMission)["Rewards"] as PList)["SideKick"];
        string sideKickName;
        if (sideKickCount > 0)
        {
            index++;

            abilityRenderer[index].material.SetFloat("_GrayscaleAmount", 0);
            SkeletonGraphic lockAnim = abilityRenderer[index].transform.GetChild(0).GetComponent<SkeletonGraphic>();
            lockAnim.gameObject.SetActive(false);
            sideKickMap = (GameData.instance.GetShop()["Category5"] as PList)["Gun" + sideKickCount.ToString()] as PList;
            sideKickName = sideKickMap["ImagePath"] as string;
            foreach (Sprite s in sidekickSprites)
            {
                if (s.name == sideKickName)
                {
                    abilityRenderer[index].sprite = s;
                }
            }

            abilityRenderer[index].gameObject.SetActive(true);
            if (GameData.instance.fileHandler.currentMission > GameData.instance.fileHandler.missionsCompleted)
            {

                lockAnim.transform.SetScale(0.0f);
                lockAnim.AnimationState.SetAnimation(0, "idle", true);
                seq = DOTween.Sequence();
                seq.AppendInterval(1.25f).AppendCallback(() => lockAnim.gameObject.SetActive(true)).Append(lockAnim.transform.DOScale(new Vector3(0.22f, 0.22f, 0.22f), 1.5f).SetEase(Ease.OutElastic));
                seq.Play();
                seq = DOTween.Sequence();
                seq.AppendInterval(1.35f).AppendCallback(() =>
                {
                    abilityRenderer[index].material.SetFloat("_GrayscaleAmount", 1);
                });
                seq.Play();
            }

        }
    }

    private void UpdateSpineImage()
    {
        foreach (SkeletonGraphic s in missionImageSpineData)
        {
            s.gameObject.SetActive(false);
        }

        missionImageSpineObject = missionImageSpineData[GameData.instance.fileHandler.currentMission - 1];
        //ValueMap missionMap = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission);

        //missionImageSpineObject = SkeletonAnimation::createWithJsonFile(missionMap.at("Image").asString() + ".json", missionMap.at("Image").asString() + ".atlas");TODO

        //_clipNode.addChild(missionImageSpineObject, 1);
        missionImageSpineObject.gameObject.SetActive(true);
//        missionImageSpineObject.AnimationState.SetAnimation(0, "mapAnim", true);

        if (GameData.instance.fileHandler.currentMission == 2)//FileHandler::getInstance().currentMission == 2)
        {
            //missionImageSpineObject.Skeleton.SetSkin("enemyPlaneLevel6");
        }
        if (GameData.instance.fileHandler.currentMission == 29)//FileHandler::getInstance().currentMission == 29)
        {
           // missionImageSpineObject.Skeleton.SetSkin("dragonGreen");
        }
        if (GameData.instance.fileHandler.currentMission == 24)// FileHandler::getInstance().currentMission == 24)
        {
            //missionImageSpineObject.transform.SetScale(0.185f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 21)// FileHandler::getInstance().currentMission == 21)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 150);
        }
        if (GameData.instance.fileHandler.currentMission == 27)// FileHandler::getInstance().currentMission == 27)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "arenaIdle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.42f);
        }
        if (GameData.instance.fileHandler.currentMission == 29)// FileHandler::getInstance().currentMission == 29)
        {
            //missionImageSpineObject.AnimationState.SetAnimation(0, "idle", true);
            //missionImageSpineObject.transform.SetScale(0.5f);
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.45f + 50);
        }
        if (GameData.instance.fileHandler.currentMission == 30)// FileHandler::getInstance().currentMission == 30)
        {
            //missionImageSpineObject.GetComponent<RectTransform>().anchoredPosition = new Vector2(clipRegion.x / 2, clipRegion.y * 0.3f);
        }
        Globals.Rescale(missionImageSpineObject.gameObject, missionImageSpineObject.transform.localScale.x);
    }

    public void ShowMissionInfo()
    {
        PList vMap = GameData.instance.GetMissions();
        string missionVal = "Mission"+GameData.instance.fileHandler.currentMission;

        string c = GameData.instance.fileHandler.currentMission.ToString();
        string missionNum = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MISSION_INFO_MENU)["mission"] as string;
        missionNum+=c;

        string missionTxt = (GameData.instance.GetTextData(Constants.GAME_TEXT.MISSION_DATA)[missionVal] as PList)["Text"] as string;
        string missionType = (vMap[missionVal] as PList) ["Type"] as string;

        if (missionType == "Boss")
        {

            int missionBossNumber = (int)(vMap[missionVal]as PList)["Boss Number"];
            GameData.instance.fileHandler.currentEvent = missionBossNumber;
            PlayerPrefs.SetInt("currentEvent", GameData.instance.fileHandler.currentEvent);
        }


        string missionDescriptionValue = (GameData.instance.GetTextData(Constants.GAME_TEXT.MISSION_DATA)[missionVal] as PList)["Description"] as string;
        missionNumber.text = missionNum;
        //missionNumber.text = (Shared::convertStringNumbersToArabic(missionNumber.getString())); TODO

        missionName.text = missionTxt;
        //if (missionName.getLabelType() == Label::LabelType::TTF) tODO
        //{
        //    TTFConfig config = missionName.getTTFConfig();
        //    config.fontSize = 100;
        //    missionName.setTTFConfig(config);
        //}
        //else
        //{
        //    //        missionName.setSystemFontSize(80);
        //}

        missionDescription.text =missionDescriptionValue;
        seq.Kill();
        anim.SetInteger("In", 1);
        if (rect.anchoredPosition.x < 50)// this.getPosition().x > Director::getInstance().getWinSize().width - 5)
        {
            AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp, 0.15f);

            //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);
        }

        if (missionType == "Boss")
            Globals.gameType = GameType.Arena;
        else
            Globals.gameType = GameType.Training;

        //GameData.instance.fileHandler.sa FileHandler::getInstance().saveData(); TODO
        //if (DIFFICULTYMENU)
        //{
        //    int rewardInCoins = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Coins").asInt();
        //    int rewardInXp = GameData::getInstance().getMissions(FileHandler::getInstance().currentMission).at("Rewards").asValueMap().at("Xp").asInt(); ;

        //    df1.changeStats(rewardInCoins / 2, rewardInXp / 2);
        //    df2.changeStats(rewardInCoins, rewardInXp);
        //    df3.changeStats(rewardInCoins * 2, rewardInXp * 2);
        //}
        UpdateSpineImage();
        ShowUnlocks();
    }

    public void CloseButtonCall()
    {
        if (!IsOpen())
        {
            return;
        }
        AudioManager.instance.PlaySound(AudioType.Menu, Constants_Audio.Audio.popUp,  0.15f);

        //Globals.PlaySound("res/Sounds/SFX/test/popUp.mp3", false, 0.15f);

        addedCloseFunction();
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        anim.SetInteger("In", 0);
    }

    public bool IsOpen()
    {
        if (rect.anchoredPosition.x<50)
        {
            return true;
        }
        return false;
    }
}
