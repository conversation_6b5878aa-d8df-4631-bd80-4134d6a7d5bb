using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

public class Water : MonoBehaviour
{
	[SerializeField] Material waterMat;
	[SerializeField] Texture2D texture;
	[SerializeField] string sortingLayerName;
	[SerializeField] int numberOfSprings, sortingOrder;
	[SerializeField] float maxForce, dampening, tension, spread, waterLengthX, bottomPosition, targetHeightOffset;

	Color color1, color2;
	float targetHeight;
	bool assignUVs;
	WaterColumn[] springs;
	Mesh mesh;

	#region
	float[] lDeltas;
	float[] rDeltas;
	#endregion

	public Color Color1 { set { color1 = value; } }
	public Color Color2 { set { color2 = value; } }

	private void Start()
    {
        if (Globals.GROUND_ENABLED)
        {
			this.enabled = false;
			return;
        }

		targetHeight = Globals.LOWERBOUNDARY + targetHeightOffset;

		assignUVs = false;
		springs = new WaterColumn[numberOfSprings];
		lDeltas = new float[springs.Length];
		rDeltas = new float[springs.Length];

		for (int i = 0; i < numberOfSprings; i++)
        {
			springs[i].targetheight = targetHeight;
			springs[i].height = targetHeight;
			springs[i].speed = 0;
        }

		mesh = new Mesh();

		GameObject waterObj = new GameObject("WaterMesh", typeof(MeshFilter), typeof(MeshRenderer),
			typeof(SortingGroup));
		waterObj.GetComponent<MeshFilter>().mesh = mesh;
		waterObj.GetComponent<MeshRenderer>().material = waterMat;
		waterObj.GetComponent<SortingGroup>().sortingLayerName = sortingLayerName;
		waterObj.GetComponent<SortingGroup>().sortingOrder = sortingOrder;
		waterObj.transform.parent = transform;
		Vector3 scale = waterObj.transform.localScale;
		scale.x = 4;
		waterObj.transform.localScale = scale;

	}

	public void HitWater(Vector3 position, float forceFactor = 1)
    {
		if (Globals.GROUND_ENABLED)
			return;

		if (mesh == null) return;
		Vector3[] vertices = mesh.vertices;
		float distance = 5000;
		int vertexIndex = 0;

		for(int i = 0; i < vertices.Length; i += 2)
        {
			var dis = position.x - transform.TransformPoint(vertices[i]).x;

			if (dis < 0)
				dis = -dis;

			if(dis < distance)
            {
				distance = dis;
				vertexIndex = i;
            }
        }

		Splash(vertexIndex / 2, -forceFactor * maxForce); // Because spring index will be half of vertex index
    }

    void Splash(int index, float speed)
	{
		if (index >= 0 && index < springs.Length)
			springs[index].speed = speed;
	}

    private void Update()
    {
		for (int i = 0; i < springs.Length; i++)
			springs[i].Update(dampening, tension);

		


		for (int j = 0; j < 8; j++)
		{
			for (int i = 0; i < springs.Length; i++)
			{
				if (i > 0)
				{
					lDeltas[i] = spread * (springs[i].height - springs[i - 1].height);
					springs[i - 1].speed += lDeltas[i];
				}
				if (i < springs.Length - 1)
				{
					rDeltas[i] = spread * (springs[i].height - springs[i + 1].height);
					springs[i + 1].speed += rDeltas[i];
				}
			}

			for (int i = 0; i < springs.Length; i++)
			{
				if (i > 0)
					springs[i - 1].height += lDeltas[i];
				if (i < springs.Length - 1)
					springs[i + 1].height += rDeltas[i];
			}
		}

		DrawWater();
	}

    private void DrawWater()
    {
		Vector3[] vertices = new Vector3[numberOfSprings  * 2];
		int[] triangles = new int[(numberOfSprings - 1) * 6];
		int indexTL, indexBL, indexTR, indexBR;
		float xGap = waterLengthX / (numberOfSprings - 1);

		for (int i = 0; i < numberOfSprings; i++)
		{
			vertices[i * 2] = new Vector3(-waterLengthX / 2 + xGap * i,
				springs[i].height, transform.position.z); // Top vertex for the current spring
			vertices[(i * 2) + 1] = new Vector3(-waterLengthX / 2 + xGap * i,
				bottomPosition, transform.position.z); // Bottom vertex for the current spring

			if (i > 0)
			{
				indexTL = (i - 1) * 2; // Top-Left Vertex
				indexBL = (2 * i) - 1; // Bottom-Left Vertex
				indexTR = i * 2; // Top-Right Vertex
				indexBR = (i * 2) + 1; // Bottom-Right Vertex

				triangles[(i - 1) * 6] = indexTL;
				triangles[(i - 1) * 6 + 1] = indexTR;
				triangles[(i - 1) * 6 + 2] = indexBL;

				triangles[(i - 1) * 6 + 3] = indexBL;
				triangles[(i - 1) * 6 + 4] = indexTR;
				triangles[(i - 1) * 6 + 5] = indexBR;
			}
		}

		mesh.vertices = vertices;
		mesh.triangles = triangles;

		if (assignUVs)
			return;

		Vector2[] uv = new Vector2[numberOfSprings * 2];

        for (int i = 0; i < numberOfSprings; i++)
        {
			uv[i * 2] = new Vector2((-waterLengthX / 2 + xGap * i) / waterLengthX,
				1); // Top vertex for the current spring
			uv[(i * 2) + 1] = new Vector2((-waterLengthX / 2 + xGap * i) / waterLengthX,
				0); // Bottom vertex for the current spring
		}

		mesh.uv = uv;

		assignUVs = true;

		ApplyColor();
	}

	void ApplyColor()
    {
		int textureSize = texture.width;
		Gradient gradient = new Gradient();
		GradientColorKey[] colorKey;
		GradientAlphaKey[] alphaKey;

		// Populate the color keys at the relative time 0 and 1 (0 and 100%)
		colorKey = new GradientColorKey[2];
		colorKey[0].color = color2;
		colorKey[0].time = 0.0f;
		colorKey[1].color = color1;
		colorKey[1].time = 1.0f;

		// Populate the alpha  keys at relative time 0 and 1  (0 and 100%)
		alphaKey = new GradientAlphaKey[2];
		alphaKey[0].alpha = color2.a;
		alphaKey[0].time = 0.0f;
		alphaKey[1].alpha = color1.a;
		alphaKey[1].time = 1.0f;

		gradient.SetKeys(colorKey, alphaKey);
		texture.wrapMode = TextureWrapMode.Clamp;
		for (int i = 0; i < textureSize; i++)
		{
			for(int j = 0; j < textureSize; j++)
            {
				float colorTime = (float)j / (textureSize - 1);
				texture.SetPixel(i, j, gradient.Evaluate(colorTime));
            }
		}

		texture.Apply();
		waterMat.SetTexture("_MainTex", texture);
	}


    struct WaterColumn
	{
		public float targetheight;
		public float height;
		public float speed;

		public void Update(float dampening, float tension)
		{
			float x = targetheight - height;
			speed += tension * x - speed * dampening;
			height += speed;
		}
	}
}



