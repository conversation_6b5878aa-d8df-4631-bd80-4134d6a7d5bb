using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using TMPro;
using UnityEngine.SceneManagement;
public class LanguagesMenu : MonoBehaviour
{
    [SerializeField] private CustomButton closeButton;
    [SerializeField] private Button[] languageButton;
    [SerializeField] private Color yellow;
    [SerializeField] private GameObject blur;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    public void Init()
    {
        transform.localScale = Vector3.zero;
        gameObject.SetActive(true);
        closeButton.defaultAction = ClosePopUp;
        transform.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutElastic).OnComplete(()=> { blur.SetActive(true); });
        //        _winSize = cocos2d::Size(1334, 750);
        //        this->setOpacity(0.2f);
        //        clickSound = "res/Sounds/SFX/menuClick.mp3";
        //        exitIndex = -1;


        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "en")
        //            {
        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("ENGLISH(U.S)", [=](){
        //                    GameData::getInstance()->setLanguage("en");

        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }



        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "en-gb")
        //            {
        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("ENGLISH(U.K)", [=](){
        //                    GameData::getInstance()->setLanguage("en-gb");

        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }


        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "en-ca")
        //            {
        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("ENGLISH(CN)", [=](){
        //                    GameData::getInstance()->setLanguage("en-ca");

        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }




        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "ru")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("RUSSIAN",  [=](){
        //                    GameData::getInstance()->setLanguage("ru");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "fr")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("FRENCH", [=](){
        //                    GameData::getInstance()->setLanguage("fr");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                }, "fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }//        offset = node->getMainLabel()->getContentSize().height/2;
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "fr-ca")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("FRENCH(CN)", [=](){
        //                    GameData::getInstance()->setLanguage("fr-ca");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                }, "fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }//        offset = node->getMainLabel()->getContentSize().height/2;
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "es")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("Español",  [=](){
        //                    GameData::getInstance()->setLanguage("es");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "nl")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("Nederlands",  [=](){
        //                    GameData::getInstance()->setLanguage("nl");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "pt")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("português",  [=](){
        //                    GameData::getInstance()->setLanguage("pt");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }
        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "it")
        //            {


        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("ITALIAN", [=](){
        //                    GameData::getInstance()->setLanguage("it");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);

        //            }
        //        }

        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "tr")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("Türk", [=](){
        //                    GameData::getInstance()->setLanguage("tr");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }


        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "zh-Hant")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("繁體中文",  [=](){
        //                    GameData::getInstance()->setLanguage("zh-Hant");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/NotoSansTC-Black.otf",true, 40);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }
        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "de")
        //            {


        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("german",  [=](){
        //                    GameData::getInstance()->setLanguage("de");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/LuckiestGuy-Regular.ttf",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }
        //        //#if !Desktop
        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "ar")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("العربية",  [=](){
        //                    GameData::getInstance()->setLanguage("ar");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"Beirut",true);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }
        //        //#endif
        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "zh-Hans")
        //            {


        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("简体中文",  [=](){
        //                    GameData::getInstance()->setLanguage("zh-Hans");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/NotoSansSC-Black.otf",true, 35);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }


        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "ko")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("한국어", [=](){
        //                    GameData::getInstance()->setLanguage("ko");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/Gugi-Regular.otf",true, 45);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }


        //        {
        //            if (GameData::getInstance()->getCurrentLanguageCode() != "ja")
        //            {

        //                MenuOptionMac* node = MenuOptionMac::createWithLabel("日本人",  [=](){
        //                    GameData::getInstance()->setLanguage("ja");
        //                    Director::getInstance()->replaceScene(MainMenuScene::createScene());
        //                },"fonts/NotoSansJP-Black.otf",true, 40);
        //                this->addChild(node);
        //                _labelArray.pushBack(node);
        //            }
        //        }

        //        alignItemsVerticallyWithPadding(Size(0, 0), 40, 0);

        //        //   updateSelected();



        //        auto keylistener = EventListenerKeyboard::create();
        //        keylistener->onKeyPressed = [=](cocos2d::EventKeyboard::KeyCode keyCode, cocos2d::Event *event){
        //        if (_scrollView)
        //        {
        //            _scrollView->scrollToPercentVertical(((float)_selectedPosition / (_labelArray.size() - 1)) * 100, 0.2f, true);
        //            log("scroll selected position %d perctange %f", _selectedPosition, ((float)_selectedPosition / _labelArray.size()) * 100);
        //        }
        //    };

        //    _eventDispatcher->addEventListenerWithSceneGraphPriority(keylistener, this);


        //    auto mouseListener = EventListenerMouse::create();
        //            mouseListener->onMouseScroll = [=] (EventMouse*event){

        //        if (_scrollView)
        //        {
        //            if (event->getScrollY() > 0)
        //                {
        //                    _scrollView->scrollToPercentVertical(_scrollView->getScrolledPercentVertical() + 5 ,0.2f,true);
        //}
        //                else
        //{
        //    _scrollView->scrollToPercentVertical(_scrollView->getScrolledPercentVertical() - 5, 0.2f, true);

        //}
        //                }
        //            };
        //_eventDispatcher->addEventListenerWithSceneGraphPriority(mouseListener, this);

        //return true;
        Globals.isMouseOverUI = true;
    }


    public void ClosePopUp()
    {
        gameObject.SetActive(false);
    }

    public void OnMouseEnter(TextMeshProUGUI label)
    {
        label.color = yellow;
    }

    public void OnMouseExit(TextMeshProUGUI label)
    {
        label.color = Color.white;
    }
}
