﻿using UnityEngine;

using X.PB;

public class WaveStructure : MonoBeh<PERSON>our
{

    public struct SeverskyInfo
    {
        public int _type;
        public Vector2 _position;
        public float _rotation;
        public float _stickToFormationFor_Sec;
        public bool outOfScreenLeft;
        public bool isGenerated;
    };


    public struct EnemyInfo
    {
        public MonsterNew.Item Csv_MonsterNew;
        public int _numberOfEnemiesOnScreen;
        public int _spawnCount;
        public bool _generateAlert;
    };

    public SeverskyInfo sInfo;
    public EnemyInfo _eInfo;
    public int enemyType;
}
