using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SchemeScript : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {
        PreloadScheme();
    }

    public void PreloadScheme()
    {
        //yield return new WaitForSeconds(0.1f);
        BattleBrushEnemyScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        BuffEffectScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        BuffScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        CatDropScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        CatMainStageScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        CatSkillScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        ConstValueScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        EquipmentScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        MedicamentScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f); ;
        MissionInfoScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        MonsterGoldScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        MonsterNewScheme.Instance.initScheme();
        //yield return new WaitForSeconds(0.1f);
        CreatureScheme.Instance.initScheme();

    }
}
