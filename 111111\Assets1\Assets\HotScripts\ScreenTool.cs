﻿using UnityEngine;

public class ScreenTool
{
#if UNITY_EDITOR
    private static int mSizeFrame = -1;
    private static System.Reflection.MethodInfo s_GetSizeOfMainGameView;
    private static Vector2 mGameSize = Vector2.one;

    /// <summary>
    /// Size of the game view cannot be retrieved from Screen.width and Screen.height when the game view is hidden.
    /// </summary>

    public static Vector2 ScreenSize
    {
        get
        {
            int frame = Time.frameCount;

            if (mSizeFrame != frame || !Application.isPlaying)
            {
                mSizeFrame = frame;

                if (s_GetSizeOfMainGameView == null)
                {
                    System.Type type = System.Type.GetType("UnityEditor.GameView,UnityEditor");
                    s_GetSizeOfMainGameView = type.GetMethod("GetSizeOfMainGameView",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                }
                mGameSize = (Vector2)s_GetSizeOfMainGameView.Invoke(null, null);
            }
            return mGameSize;
        }
    }
#else
    /// <summary>
    /// Size of the game view cannot be retrieved from Screen.width and Screen.height when the game view is hidden.
    /// </summary>

    public static Vector2 ScreenSize { get { return new Vector2(Screen.width, Screen.height); } }
#endif
}
