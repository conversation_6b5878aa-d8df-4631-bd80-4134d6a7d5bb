﻿using System;

namespace Apq
{
    /// <summary>
    /// 表示点到圆形区域的距离
    /// </summary>
    public class DistancePointToSolidCircle : IComparable<DistancePointToSolidCircle>
    {
        /// <summary>
        /// 点到圆心的距离
        /// </summary>
        public float DistanceToCenter { get; set; }
        /// <summary>
        /// 点到圆边的距离(点在圆内时等于0)
        /// </summary>
        public float DistanceToEdge { get; set; }

        /// <summary>
        /// 比较距离
        /// </summary>
        public int CompareTo(DistancePointToSolidCircle other)
        {
            // 点在圆外
            if (DistanceToEdge > 0)
            {
                if (DistanceToEdge > other.DistanceToEdge) return 1;
                if (DistanceToEdge < other.DistanceToEdge) return -1;
            }

            // 点在圆内 或 点到圆边的距离相同时
            return CompareToCenter(other);
        }

        /// <summary>
        /// 比较点到圆心的距离
        /// </summary>
        private int CompareToCenter(DistancePointToSolidCircle other)
        {
            if (DistanceToCenter > other.DistanceToCenter) return 1;
            if (DistanceToCenter == other.DistanceToCenter) return 0;
            return -1;
        }
    }
}
