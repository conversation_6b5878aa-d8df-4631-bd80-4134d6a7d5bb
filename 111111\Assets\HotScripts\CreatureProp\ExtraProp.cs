﻿/// <summary>
/// 额外的属性(金币购买)
/// </summary>
/// <remarks>转化为提升,应用到提升后的属性上</remarks>
public class ExtraProp
{
    /// <summary>
    /// 属性类型
    /// </summary>
    public Globals.UpgradeSkillAttibute ExtraType { get; set; }
    /// <summary>
    /// 点数提升
    /// </summary>
    public long HoistValue { get; set; }
    /// <summary>
    /// 百分比提升
    /// </summary>
    public long HoistPct { get; set; }

    /// <summary>
    /// 应用后对应的提升
    /// </summary>
    public FightHoist Hoist_Value { get; protected set; }
    /// <summary>
    /// 应用后对应的提升
    /// </summary>
    public FightHoist Hoist_Pct { get; protected set; }

    /// <summary>
    /// 将提升应用到指定的属性对象
    /// </summary>
    /// <param name="hoisted">提升后的属性</param>
    public void ApplyTo(CreatureProps hoisted)
    {
        Hoist_Value = new FightHoist();
        Hoist_Pct = new FightHoist();

        var hoistType = Globals.MapToHoistType(ExtraType);

        if (HoistValue > 0)
        {
            Hoist_Value = new FightHoist
            {
                HoistType = hoistType,
                HoistValue = HoistValue,
                IsPct = false,
            };
            hoisted.Hoists.Add(Hoist_Value);
        }
        if (HoistPct > 0)
        {
            Hoist_Pct = new FightHoist
            {
                HoistType = hoistType,
                HoistValue = HoistPct / 10000.0f,
                IsPct = true,
            };
            hoisted.Hoists.Add(Hoist_Pct);
        }
    }

    /// <summary>
    /// 移除对应的提升
    /// </summary>
    public void RemoveFrom(CreatureProps hoisted)
    {
        if (Hoist_Value != null)
        {
            hoisted.Hoists.Remove(Hoist_Value);
            Hoist_Value = null;
        }
        if (Hoist_Pct != null)
        {
            hoisted.Hoists.Remove(Hoist_Pct);
            Hoist_Pct = null;
        }
    }
}
