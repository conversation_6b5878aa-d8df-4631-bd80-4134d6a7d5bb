using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class Constants
{
    public class GameConstants
    {
        public const float cloudTransitionDelay = 2.2f;
#if UNITY_STANDALONE||UNITY_STANDALONE_OSX
        public const float CAMERA_HEIGHT = 750;
        public const float CAMERA_MAX_SCROLL_HEIGHT = 1050;
        public const float CAMERA_MIN_SCROLL_HEIGHT = 700;
        public const float CAMERA_BUTTON_PRESS_HEIGHT = 600;
        public const float CAMERA_HEIGHT_ISLAND = -1.50f;
        public const float CAMERA_HEIGHT_ISLAND_BUTTON = 150;

#else
        public const float CAMERA_HEIGHT_ISLAND = 275;
        public const float CAMERA_HEIGHT_ISLAND_BUTTON = 275;
        public const float CAMERA_HEIGHT = 700;
        public const float CAMERA_MAX_SCROLL_HEIGHT = 700;
        public const float CAMERA_MIN_SCROLL_HEIGHT = 700;
        public const float CAMERA_BUTTON_PRESS_HEIGHT = 700;
#endif
        public const float MAC_MOUSE_SCROLL_HEIGHT = 59;
        public const float MAC_MOUSE_SCROLL_SPEED = 1.5f;
        public const float ZOOM_SCALE = 1.0f;
    }

    public class AudioClips
    {
        public const string SOUND_BUTTON_TAP = "click";
        public const string SOUND_HOVER = "cursorClick";
        public const string SOUND_OPEN_ITEM = "openShopItem";
    }

    public class Colors
    {
        public static Color BLUE = new Color(0, 194/255, 255/255);
        public static Color GREEN = new Color(30/255, 214/255, 2/255);
        public static Color YELLOW = new Color(255/255, 176/255, 1);
        public static Color RED = new Color(220/255, 38/255, 24/255);
        public static Color MUTED_GREEN = new Color(60/255, 214/255, 40/255);
        public static Color PURPLE = new Color(203/255, 35/255, 255/255);
    }

public class GAME_DATA_SCENE
    {
        public const string MAIN_MENU = "Mainmenu";
        public const string SHOP_SCENE = "ShopScene";
        public const string SIDEKICKS_SCENE = "SidekickScene";
        public const string TUTORIAL = "Tutorial";
        public const string PAUSE_MENU = "PauseMenu";
        public const string SETTINGS_MENU_MOBILE = "SettingsMenuMobile";

        public const string INGAME_HUD = "InGameHud";
        public const string MISSION_INFO_MENU = "MissionInfoMenu";
        public const string MAP_MENU = "MapMenu";
        public const string UNLOCK_SCENE = "UnlockScene";
        public const string POWERUPS = "PowerUps";
        public const string SUPPORT_MENU = "SupportMenu";
        public const string CREDITS_MENU = "CreditsMenu";
        public const string INGAME_MESSAGES = "InGameMessage";
        public const string NOTIFICATION = "Notifications";
        public const string SURVIVAL = "SurvivalScene";
        public const string NEW_UPDATE = "NewUpdate";
        public const string New_FTUE = "NewFTUE";
        public const string ACCESSIBILITY = "Accessibility";
        public const string SAVEGAME = "SaveGame";



    }

    public class GAME_TEXT
    {
        public const string DIALOGUES = "Dialogues";
        public const string BOSS_DATA = "BossData";
        public const string MISSION_DATA = "MissionData";
        public const string SHOP_DATA = "ShopData";

    }

    public class STATS
    {
        public const string HEALTH = "health";
        public const string ATTACK = "attack";
        public const string SPEED = "speed";
        public const string TIME_SCALE = "timeScale";
        public const string TURN_SPEED = "turnSpeed";
    }


    public class STATS_DATA
    {
        public const string VALUE = "value";
        public const string MULTIPLIER = "multiplier";
        public const string RANDOM = "random";
    }
}
