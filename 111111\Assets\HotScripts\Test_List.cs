using System.Collections;
using System.Collections.Generic;

using UnityEngine;

public class Test_List : MonoBeh<PERSON>our
{
    public class TestListItem
    {
        public string Name { get; set; }
    }

    public class TestListItemChild : TestListItem
    {
        public string Child { get; set; }
    }

    List<TestListItem> lst = new();

    // Start is called before the first frame update。
    void Start()
    {
        var c1 = new TestListItemChild { Name = "111", Child = "ccc" };
        TestListItem p1 = c1;
        TestListItem p2 = c1;

        lst.Add(p1);

        if (lst.Contains(c1))
        {
            Debug.Log($"识别出了包含 c1");
        }
        if (lst.Contains(p1))
        {
            Debug.Log($"识别出了包含 p1");
        }
        if (lst.Contains(p2))
        {
            Debug.Log($"识别出了包含 p2");
        }
    }

    // Update is called once per frame
    void Update()
    {

    }
}
