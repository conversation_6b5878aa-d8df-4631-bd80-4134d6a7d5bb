﻿using System.Threading;

namespace Buffs
{
    /// <summary>
    /// 一次性回血(需要配置成不使用调度器)
    /// </summary>
    public class Buff一次性回血 : BuffEffectBase
    {
        protected override void DoWork_Do(CancellationToken cancel)
        {
            switch (CsvRow_BuffEffect.Param2)
            {
                case 1:
                    {
                        Buff.BuffScheduler.Creature.FightProp.HP.Value += CsvRow_BuffEffect.Param3;
                    }
                    break;
                case 2:
                    {
                        var hp = Buff.BuffScheduler.Creature.FightProp.MaxHP.Value * Globals.UnityValueTransform(CsvRow_BuffEffect.Param3);
                        Buff.BuffScheduler.Creature.FightProp.HP.Value += hp;
                    }
                    break;
            }
        }
    }
}