using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;


public class TextMeshSizeFitter : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI textMeshUI;
    [SerializeField] private float offsetX;
    [SerializeField] private float offsetY;
    private RectTransform rectTransform;
    private RectTransform textMeshRectTransform;

    private float preferredHeight;
    private float preferredWidth;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        textMeshRectTransform = textMeshUI.rectTransform;
    }

    public void SetSize()
    {
        preferredHeight = textMeshUI.preferredHeight;
        preferredWidth = textMeshUI.preferredWidth;

        rectTransform.sizeDelta = new Vector2(preferredWidth+offsetX, preferredHeight+offsetY);
    }

    private void Update()
    {
        SetSize();
    }

    public void SetOffset(float xVal, float yVal)
    {
        offsetX = xVal;
        offsetY = yVal;

    }

}
