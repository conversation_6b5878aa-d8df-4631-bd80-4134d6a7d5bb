﻿using UniRx;

public class Attributes
{
    /// <summary>
    /// 攻击
    /// </summary>
    public double attack;
    /// <summary>
    /// XP值恢复(可能没有使用)
    /// </summary>
    public double armor;
    /// <summary>
    /// 最大生命
    /// </summary>
    public DoubleReactiveProperty maxHealth = new();
    /// <summary>
    /// 当前生命
    /// </summary>
    public double health;
    public double shield;
    /// <summary>
    /// 移动速度
    /// </summary>
    public float speed;
    /// <summary>
    /// 回血
    /// </summary>
    public double regen;
    public double xp;
    /// <summary>
    /// 能量上限
    /// </summary>
    public double energy;
    /// <summary>
    /// 能量恢复
    /// </summary>
    public double energyRegen;
    public double boostSpeed;
    public double consumption = 1;
    /// <summary>
    /// 子弹伤害
    /// </summary>
    public double bulletDamage;
    /// <summary>
    /// 导弹伤害
    /// </summary>
    public double missileDamage;
    /// <summary>
    /// 子弹速度
    /// </summary>
    public float bulletSpeed; //0 being fastest  >0 slower;
    /// <summary>
    /// 攻击速度
    /// </summary>
    public float attackSpeed;
    public float attackSpeedPecent;
    public int coinAwarded;
    /// <summary>
    /// 转向速度
    /// </summary>
    public float turnSpeed;
    public double absorb;
    public int level = 1;
    public int armorType = 0;

    public int mode;

    #region 新增的
    /// <summary>
    /// 攻击范围
    /// </summary>
    public float attackScope;
    /// <summary>
    /// 攻击距离
    /// </summary>
    public float attackDistance = 1;
    /// <summary>
    /// 减伤
    /// </summary>
    public double damageReduction;
    /// <summary>
    /// 追加伤害
    /// </summary>
    public double addDamage;
    /// <summary>
    /// 子弹速度增加率
    /// </summary>
    public float bulletSpeedAddRate;
    /// <summary>
    /// 攻击力加成(万分比)
    /// </summary>
    public double addAttack;
    /// <summary>
    /// 子弹伤害加成(万分比)
    /// </summary>
    public float bulletDamageAddRate;
    /// <summary>
    /// 狂暴时长
    /// </summary>
    public float rageModeTime;
    public double maxEnergy;
    /// <summary>
    /// 狂暴伤害增加
    /// </summary>
    public double rageAddDamage;
    /// <summary>
    /// xp技能恢复值
    /// </summary>
    public float rearGunCD;
    /// <summary>
    /// xp技能伤害
    /// </summary>
    public double rearGunDamage;
    /// <summary>
    /// 加伤
    /// </summary>
    public double damageAddPoint;
    /// <summary>
    /// 减伤
    /// </summary>
    public double damageReductionPoint;
    /// <summary>
    /// 吸血
    /// </summary>
    public double AbsorbHP;
    /// <summary>
    /// 子弹增加值
    /// </summary>
    public int addBulletNum;
    /// <summary>
    /// 半血伤害加成
    /// </summary>
    public double halfHealthAddDamage;
    /// <summary>
    /// 暴击率
    /// </summary>
    public float criticalHitRate;
    /// <summary>
    /// 小怪开始射击的距离
    /// </summary>
    public float distanceToShoot;
    /// <summary>
    /// 主武器终极buff -1为关 0 为开启 大于0 的是伤害buffID
    /// </summary>
    public int mainWeaponUltimateSkills;

    /// <summary>
    /// 直接伤害
    /// </summary>
    public double antiCriticalStrike;
    /// <summary>
    /// 伤害减免
    /// </summary>
    public double parry;
    /// <summary>
    /// 对BOSS伤害
    /// </summary>
    public double damageBossAddPct;
    /// <summary>
    /// 爆伤伤害
    /// </summary>
    public double damageCritical;
    #endregion

    //public Attributes()
    //{
    //}

    //public Attributes(float _maxHealth, float _health, float _speed, float _boostSpeed, float _armor, float _bulletDamage, float _missileDamage, float _bulletSpeed, float _attackSpeed, float _turnSpeed, float _shield)
    //{
    //    maxHealth = _maxHealth;
    //    health = _health;
    //    speed = _speed;
    //    boostSpeed = _boostSpeed;
    //    armor = _armor;
    //    bulletDamage = _bulletDamage;
    //    missileDamage = _missileDamage;
    //    turnSpeed = _turnSpeed;
    //    attackSpeed = _attackSpeed;
    //    bulletSpeed = _bulletSpeed;
    //    shield = _shield;
    //}

    public static Attributes Create(double _maxHealth, double _health, float _speed,
        double _boostSpeed, double _armor, double _bulletDamage, double _missileDamage,
        float _bulletSpeed, float _attackSpeed, float _turnSpeed, double _shield)
    {
        var rtn = new Attributes
        {
            health = _health,
            speed = _speed,
            boostSpeed = _boostSpeed,
            armor = _armor,
            bulletDamage = _bulletDamage,
            missileDamage = _missileDamage,
            turnSpeed = _turnSpeed,
            attackSpeed = _attackSpeed,
            bulletSpeed = _bulletSpeed,
            shield = _shield
        };

        rtn.maxHealth.Value = _maxHealth;

        return rtn;
    }
}
