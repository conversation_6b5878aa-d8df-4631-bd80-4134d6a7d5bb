using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EffectUnit : MonoBehaviour
{
    public string ResPath;
    public float DefaultTime = 2;
    public float ReleaseWaitTime = 10;
    private float playTime;

    void Start()
    {
        
    }

    private void OnEnable()
    {
        if (playTime <= 0) playTime = DefaultTime;
    }

    // Update is called once per frame
    public void OnUpdate()
    {
        if (playTime > 0) 
        {
            playTime -= Time.deltaTime;
            if (playTime <= 0) Recycle();
        }
        else
        {
            if (playTime >= -ReleaseWaitTime)
            {
                playTime -= Time.deltaTime;
                if (playTime < -ReleaseWaitTime)
                {
                    Release();
                }
            }
        }
        
    }

    private void OnDestroy()
    {
        if(EffectMgr.Instance.UseEffects.Contains(this)) EffectMgr.Instance.UseEffects.Remove(this);
        if (EffectMgr.Instance.UnuseEffects.Contains(this)) EffectMgr.Instance.UnuseEffects.Remove(this);
    }

    public void SetPlayTime(float time) 
    { 
        playTime = time;
    }

    /// <summary>
    /// 回收到对象池
    /// </summary>
    public void Recycle()
    {
        transform.SetParent(EffectMgr.Instance.transform);
        gameObject.SetActive(false);
        EffectMgr.Instance.UseEffects.Remove(this);
        EffectMgr.Instance.UnuseEffects.Add(this);
    }
    /// <summary>
    /// 释放销毁
    /// </summary>
    public void Release()
    {
        EffectMgr.Instance.ReleaseEffect(this);
    }
}
