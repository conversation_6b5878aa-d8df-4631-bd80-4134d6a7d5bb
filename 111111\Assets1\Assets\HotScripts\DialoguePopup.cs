using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Spine.Unity;
using UnityEngine;
using DG.Tweening;

public class DialoguePopup : MonoBehaviour
{
    public struct DialogueInfo
    {
        public int dialogueNumber;
        public string text;
        public string Path;
        public bool alignLeft;
        public string heading;
    }

    List<DialogueInfo> _dialogueArray;
    PrefabsContainer dialoguesPrefabs;
    Dialogue dialogueBoxDialogue;
    RectTransform enemyDialogueRect, playerDialogueRect, merlinDialogueRect, sidekickDialogueRect, rocketeerDialogueRect,
        currentRect;
    float initialEnemyX, initialPlayerX, initialMerlinX, initialSidekickX, initialRocketeerX;


    string battleString;
    //Dialogue _merlin;
    bool disableSkip = false;
    int ftueMidDialogueIndex=1;

    public static void CreateAsPreBattle()
    {
        var dialoguePopup = new GameObject().AddComponent<DialoguePopup>();
        if (dialoguePopup && GameData.instance.fileHandler.currentEvent < 100)
        {
            dialoguePopup.battleString = "PreBattle";
            dialoguePopup.Init();
        }
    }

    public static void CreateForSurvival()
    {
        var dialoguePopup = new GameObject().AddComponent<DialoguePopup>();
        if (dialoguePopup)
        {
            dialoguePopup._dialogueArray = new List<DialogueInfo>();
            DialogueInfo dNode = new DialogueInfo();

            dNode.Path = "res/SideKicks/merlin";
            dNode.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.INGAME_MESSAGES)["Mission18"] as string;
            dNode.alignLeft = true;
            dNode.heading = "";

            dialoguePopup._dialogueArray.Add(dNode);

            dialoguePopup.dialoguesPrefabs = Resources.Load("DialoguesPrefabs") as PrefabsContainer;
            dialoguePopup.dialogueBoxDialogue = Instantiate(dialoguePopup.dialoguesPrefabs.GetObject("DialogueBox") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<Dialogue>();
            dialoguePopup.merlinDialogueRect = Instantiate(dialoguePopup.dialoguesPrefabs.GetObject("Merlin") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>();
            dialoguePopup.initialMerlinX = Mathf.Abs(dialoguePopup.merlinDialogueRect.anchoredPosition.x);

            dialoguePopup.dialogueBoxDialogue.onCloseDialogue = dialoguePopup.ShowNextDialogue;
            dialoguePopup.dialogueBoxDialogue.closeFaceDialogue = dialoguePopup.HideFace;

            dialoguePopup.merlinDialogueRect.DOKill();
            dialoguePopup.merlinDialogueRect.anchoredPosition = new Vector2(0, dialoguePopup.merlinDialogueRect.anchoredPosition.y);

            dialoguePopup.StartPreBattleDialogue();
        }
    }

    void StartPreBattleDialogue()
    {
        battleString = "PreBattle";
        ShowNextDialogue();
    }

    void StartMidBattleDialogue()
    {
        battleString = "MidBattle";
        if (GameData.instance.fileHandler.currentMission == 0)
        {
            battleString= "MidBattle"+ftueMidDialogueIndex;
            ftueMidDialogueIndex++;
        }
        _dialogueArray = new List<DialogueInfo>();
        string eventString = "Event" + GameData.instance.fileHandler.currentEvent;

        for (int i = 1; i <=
            ((GameData.instance.GetTextData(Globals.DIALOGUES)[eventString] as PList)[battleString] as PList).Count; i++)
        {
            AddDialogueToVector(i);
        }

        ResetAllRects();
        ShowNextDialogue();
    }

    void StartPostBattleDialogue()
    {
        battleString = "PostBattle";
        _dialogueArray = new List<DialogueInfo>();
        string eventString = "Event" + GameData.instance.fileHandler.currentEvent;

        for (int i = 1; i <=
            ((GameData.instance.GetTextData(Globals.DIALOGUES)[eventString] as PList)[battleString] as PList).Count; i++)
        {
            AddDialogueToVector(i);
        }

        ResetAllRects();
        ShowNextDialogue();
    }


    void Init()
    {
        _dialogueArray = new List<DialogueInfo>();
        string eventString = "Event" + GameData.instance.fileHandler.currentEvent;

        for (int i = 1; i <=
            ((GameData.instance.GetTextData(Globals.DIALOGUES)[eventString] as PList)[battleString] as PList).Count; i++)
        {
            AddDialogueToVector(i);
        }

        dialoguesPrefabs = Resources.Load("DialoguesPrefabs") as PrefabsContainer;

        dialogueBoxDialogue = Instantiate(dialoguesPrefabs.GetObject("DialogueBox") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<Dialogue>();
        playerDialogueRect = Instantiate(dialoguesPrefabs.GetObject("Player") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>();
        sidekickDialogueRect = Instantiate(dialoguesPrefabs.GetObject("Mechanic") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>();
        rocketeerDialogueRect = Instantiate(dialoguesPrefabs.GetObject("Rocketeer") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>();
        merlinDialogueRect = Instantiate(dialoguesPrefabs.GetObject("Merlin") as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>();
        enemyDialogueRect = GameData.instance.fileHandler.currentEvent != 10
            ? Instantiate(dialoguesPrefabs.GetObject(GameData.instance.fileHandler.currentEvent.ToString()) as GameObject,
            GameManager.instance.dialoguesPanelTransform).GetComponent<RectTransform>()
            : merlinDialogueRect;

        initialEnemyX = Mathf.Abs(enemyDialogueRect.anchoredPosition.x);
        initialMerlinX = Mathf.Abs(merlinDialogueRect.anchoredPosition.x);
        initialPlayerX = Mathf.Abs(playerDialogueRect.anchoredPosition.x);
        initialSidekickX = Mathf.Abs(sidekickDialogueRect.anchoredPosition.x);
        initialRocketeerX = Mathf.Abs(rocketeerDialogueRect.anchoredPosition.x);

        dialogueBoxDialogue.onCloseDialogue = ShowNextDialogue;
        dialogueBoxDialogue.closeFaceDialogue = HideFace;

        Observer.RegisterCustomEvent(gameObject, "SpawnStartingDialogues", StartPreBattleDialogue);
        Observer.RegisterCustomEvent(gameObject, "SpawnMidDialogues", StartMidBattleDialogue);
        Observer.RegisterCustomEvent(gameObject, "SpawnEndingDialogues", StartPostBattleDialogue);

        ResetAllRects();
        //ShowNextDialogue();
        //#if Desktop
        //    auto keylistener = EventListenerKeyboard::create();
        //    keylistener->onKeyPressed = [=](EventKeyboard::KeyCode keyCode, Event * event){


        //    };
        //    keylistener->onKeyReleased = [=](EventKeyboard::KeyCode keyCode, Event * event){

        //    };
        //    _eventDispatcher->addEventListenerWithSceneGraphPriority(keylistener, this);

        //    cocos2d::Size winSize = Director::getInstance()->getWinSize();
        //    if(!isJoystickConnected)
        //    {
        //    skipLabel = Label::createWithTTF(GameData::getInstance()->getMenuData(GAME_DATA_SCENE::TUTORIAL).at("escToSkip").asString(), GAME_FONT, 30);
        //    Shared::fontToCustom(skipLabel);
        //    skipLabel->setPosition(winSize.width - 250, winSize.height - skipLabel->getContentSize().height/2);
        //    this->addChild(skipLabel,50);
        ////    skipLabel->setOpacity(0);

        //    skipLabel->runAction(FadeIn::create(1.0f));
        //    }
        //#endif

    }

    void ResetAllRects()
    {
        enemyDialogueRect.DOKill();
        playerDialogueRect.DOKill();
        sidekickDialogueRect.DOKill();
        rocketeerDialogueRect.DOKill();
        merlinDialogueRect.DOKill();

        enemyDialogueRect.anchoredPosition = new Vector2(0, enemyDialogueRect.anchoredPosition.y);
        playerDialogueRect.anchoredPosition = new Vector2(0, playerDialogueRect.anchoredPosition.y);
        merlinDialogueRect.anchoredPosition = new Vector2(0, merlinDialogueRect.anchoredPosition.y);
        sidekickDialogueRect.anchoredPosition = new Vector2(0, sidekickDialogueRect.anchoredPosition.y);
        rocketeerDialogueRect.anchoredPosition = new Vector2(0, rocketeerDialogueRect.anchoredPosition.y);
    }

    void End()
    {
        _dialogueArray.Clear();
        //_merlin->removingSelf = true;
        //this->unscheduleUpdate();
        //this->removeFromParentAndCleanup(true);
    }

    void AddDialogueToVector(int infoNumber)
    {
        DialogueInfo dNode = new DialogueInfo();

        string eventString = "Event" + GameData.instance.fileHandler.currentEvent;
        string dInfoString = "DialogueInfo" + infoNumber;

        var dInfoMap = ((GameData.instance.GetTextData(Globals.DIALOGUES)[eventString] as PList)[battleString] as PList)[dInfoString] as PList;

        dNode.Path = dInfoMap["Path"] as string;
        dNode.text = dInfoMap["Dialogue"] as string;
        dNode.alignLeft = (dInfoMap["Position"] as string) == "Right" ? false : true;
        dNode.heading =
        (GameData.instance.GetTextData(Globals.DIALOGUES)["Keys"] as PList)[dNode.Path] as string;
        _dialogueArray.Add(dNode);
    }
    
    void ShowNextDialogue()
    {
        if (GameData.instance.fileHandler.currentMission == 10018)
            Observer.DispatchCustomEvent("start_timer_investigate_signal");
        if (_dialogueArray.Count == 0)
        {
            // Resume game here
            if (GameData.instance.fileHandler.currentMission == 0 && Globals.endFtue)
            {
                Globals.introComplete = true;
                GameManager.instance.FtueEnd();
                Globals.endFtue = false;
            }

            if (!Globals.gameplayStarted)
            {
                GameManager.instance.StartGameplay();
            }
            return;
        }

        var dialogue = _dialogueArray.First();
        bool pauseAll = !(GameData.instance.fileHandler.currentMission == 0 && Globals.isTutorial);
        dialogueBoxDialogue.Init(dialogue.heading, dialogue.Path, dialogue.text, !dialogue.alignLeft, pauseAll);
        AudioManager.instance.PlaySound(AudioType.Menu,"a"+ (1 + Random.Range(0,22)));
        if (dialogue.Path == "res/Player/player")
        {
            ShowFace(playerDialogueRect, initialPlayerX);
        }
        else if (dialogue.Path == "res/SideKicks/merlin")
        {
            ShowFace(merlinDialogueRect, initialMerlinX);
        }
        else if (dialogue.Path == "res/SideKicks/mechanic")
        {
            float initialPos = dialogue.alignLeft ? initialSidekickX : -initialSidekickX;
            ShowFace(sidekickDialogueRect, initialPos, dialogue.alignLeft ? 1 : -1, dialogue.alignLeft);
        }
        else if (dialogue.Path == "res/SideKicks/rocketeer")
        {
            float initialPos = dialogue.alignLeft ? initialRocketeerX : -initialRocketeerX;
            ShowFace(rocketeerDialogueRect, initialPos, dialogue.alignLeft ? 1 : -1, dialogue.alignLeft);
        }
        else 
        {
            if(battleString == "PostBattle")
            {
                var enemyDialogueSkeleton = enemyDialogueRect.GetComponentInChildren<SkeletonGraphic>();
                if(enemyDialogueSkeleton) enemyDialogueSkeleton.AnimationState.SetAnimation(0, "defeatTalk", true);
            }
            float initialPos = dialogue.alignLeft ? initialEnemyX : -initialEnemyX;
            ShowFace(enemyDialogueRect, initialPos, dialogue.alignLeft ? -1 : 1, dialogue.alignLeft);
        }
        _dialogueArray.Remove(dialogue);
    }

    void ShowFace(RectTransform rect, float initialX, int scaleMultipier = 1, bool isLeft = true)
    {
        currentRect = rect;
        currentRect.DOKill();

        currentRect.anchorMin = currentRect.anchorMax = isLeft ? new Vector2(0, 0) : new Vector2(1, 0);
        currentRect.anchoredPosition = new Vector2(0, currentRect.anchoredPosition.y);
        currentRect.localScale = new Vector3(scaleMultipier * Mathf.Abs(currentRect.localScale.x),
            currentRect.localScale.y, currentRect.localScale.z);
        currentRect.DOAnchorPos(new Vector2(initialX, currentRect.anchoredPosition.y), 0.2f).SetUpdate(true);
    }

    void HideFace()
    {
        currentRect.DOKill();
        currentRect.DOAnchorPos(new Vector2(0, currentRect.anchoredPosition.y), 0.2f).SetUpdate(true);
    }

    void Update()
    {
        //if (_dialogueArray.Count == 0 && _merlin->removingSelf)
        //{
        //    this->unscheduleUpdate();
        //    this->removeFromParentAndCleanup(true);

        //    return;
        //}

        //if (!_merlin)
        //{
        //    DialogueInfo dNode = _dialogueArray.First();

        //    DialogueConfig config;

        //    config._pauseAll = true;
        //    if (GameData.instance.fileHandler.currentMission == 0 && Globals.isTutorial)
        //    {
        //        config._pauseAll = false;
        //    }

        //    config._path = dNode.Path;
        //    config._battleString = battleString;
        //    config._heading = dNode.heading;
        //    if (dNode.position == "Left")
        //    {
        //        config._hAlign = TextHAlignment::LEFT;
        //    }
        //    else
        //    {
        //        config._hAlign = TextHAlignment::RIGHT;
        //    }
        //    config._exitButton = true;

        //    _merlin = Dialogue::create(config);
        //    _merlin->disableSkip = disableSkip;

        //    _merlin->setCameraMask((unsigned int) CameraFlag::USER5);


        //    if (GameData.instance.fileHandler.currentMission == 0 && Globals.isTutorial)
        //    {
        //        _merlin->ShowText(dNode.text);
        //    }
        //    else
        //    {
        //        _merlin->TypeText(dNode.text);
        //    }
        //    if (dNode.Path == "res/Enemy/enemy3")
        //    {
        //        _merlin->_character->setSkin("enemyPlaneLevel6");
        //    }

        //    if (dNode.Path == "res/Enemy/dragon")
        //    {
        //        _merlin->_character->setSkin("dragonBlue");
        //    }
        //    _dialogueArray.Remove(dNode);
        //    if (dNode.Path == "res/SideKicks/merlin" && config._hAlign == TextHAlignment::RIGHT)
        //    {
        //        _merlin->_character->setScaleX(-1);
        //    }
        //    return;
        //}
        //if (_merlin->removingSelf)
        //{
        //    DialogueInfo dNode = _dialogueArray.First();
        //    DialogueConfig config;

        //    config._pauseAll = true;
        //    config._path = dNode.Path;
        //    config._battleString = battleString;
        //    config._heading = dNode.heading;

        //    if (dNode.position == "Left")
        //    {
        //        config._hAlign = TextHAlignment::LEFT;
        //    }
        //    else
        //    {
        //        config._hAlign = TextHAlignment::RIGHT;
        //    }
        //    config._exitButton = true;


        //    _merlin = Dialogue::create(config);
        //    _merlin->disableSkip = disableSkip;
        //    this->addChild(_merlin);

        //    if (GameData.instance.fileHandler.currentMission == 0 && Globals.isTutorial)
        //    {
        //        _merlin->ShowText(dNode.text);

        //    }
        //    else
        //    {
        //        _merlin->TypeText(dNode.text);

        //    }

        //    if (dNode.Path == "res/Enemy/enemy3")
        //    {
        //        _merlin->_character->setSkin("enemyPlaneLevel6");
        //    }
        //    if (dNode.Path == "res/Enemy/dragon")
        //    {
        //        _merlin->_character->setSkin("dragonBlue");
        //    }
        //    if (dNode.Path == "res/SideKicks/merlin" && config._hAlign == TextHAlignment::RIGHT)
        //    {
        //        _merlin->_character->setScaleX(-1);
        //    }
        //    _dialogueArray.Remove(dNode);

        //    return;
        //}

    }
}
