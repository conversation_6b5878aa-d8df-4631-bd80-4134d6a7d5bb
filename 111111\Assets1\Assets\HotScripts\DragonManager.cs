using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using DG.Tweening;

public class DragonManager : MonoBehaviour
{
    #region CONSTANTS
    public const string RIVER_FIRE_EVENT = "RIVER_FIRE";
    public  const float GROUND_WIDTH = 3200;
    #endregion

    #region REFERENCES
    BoundarySpiders spider1 = null;
    BoundarySpiders spider2 = null;
    DragonManagerStates _dState = DragonManagerStates.State1;
    [SerializeField] SkeletonAnimation _entryAnimation = null;
    [SerializeField] SkeletonAnimation _flameAnim = null;
    [SerializeField] Sprite bulletSprite;
    [SerializeField] GameObject smokeTrail;
    [SerializeField] GameObject flameParent;
    [SerializeField] GameObject smokeParent;
    [SerializeField] Dragon _dragon1 = null;
    [SerializeField] Dragon _dragon2 = null;
    [SerializeField] Coin coins;
    Bone _bone = null;
    private PlayerController player;
    private Sequence rainOfFireSeq;
    private Sequence camFollowSeq;
    #endregion

    #region Variables
    bool _isEntryOver = false;
    bool _allowRiverDmg = false;
    int numberOfDragons = 2;
    private bool scheduleUpdate = true;
    public const string TWEEN_ID = "DragonManager", SCHEDULAR_ID = "DMschedular";
    private bool initialized = false;
    #endregion

    private enum DragonManagerStates
    {
        State1,
        State2,
        State3
    };

    ~DragonManager()
    {
        Observer.DispatchCustomEvent(RIVER_FIRE_EVENT);
    }

    public void Init()
    {
        if (initialized)
            return;
        initialized = true;
        player = GameManager.instance.player;
        DOTween.Sequence().SetId(TWEEN_ID).AppendInterval(0.16f).AppendCallback(() =>
        {
            AudioManager.instance.PlaySound(AudioType.Enemy, Constants_Audio.Audio.dragonSistersIntro );
            //Shared::playSound("res/Sounds/Bosses/boss9/dragonSistersIntro.mp3");todo
        });

        CreateEntryAnimation();
        CreateFirstDragon();
        scheduleUpdate = true;

        camFollowSeq = DOTween.Sequence().AppendCallback(() =>
        {
            FollowEntryCam();
        }).SetLoops(-1).Play();


        //DOTween.Sequence().SetId(SCHEDULAR_ID).AppendInterval(0.25f).AppendCallback(() =>
        //{
        //    RiverDamage();
        //}).SetLoops(-1).Play();

        InvokeRepeating("RiverDamage", 0.25f, 0.25f);
        InvokeRepeating("StateCheck", 0.5f, 0.5f);
        //    StateCheck();
        //}).SetLoops(-1).Play();
        //this->schedule(schedule_selector(DragonManager::followEntryCam), 0, 500, 0);
        //this->schedule(schedule_selector(DragonManager::makeTrail));
        //this->schedule(schedule_selector(DragonManager::riverDamage), 0.25f);
        //this->schedule(schedule_selector(DragonManager::stateCheck), 0.5f); todo

        Observer.RegisterCustomEvent(gameObject, RIVER_FIRE_EVENT, () =>
        {
            RiverFire();
            //call river fire func
        });
    }

    private void StateCheck()
    {
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            DOTween.Kill(SCHEDULAR_ID);
            DOTween.Kill(TWEEN_ID);
            scheduleUpdate = false;
        }
        //if (Player::getStats()->mode != PlayerController.PlayerAnimation.Flying)
        //{
        //    this->unscheduleAllCallbacks();
        //    this->unscheduleUpdate();

        //}todo

        if (_dragon1.stats.health < _dragon1.stats.maxHealth.Value * 0.7f && _dState == DragonManagerStates.State1)
        {
           
            _dState = DragonManagerStates.State2;

            if (_dragon1._dragonPosition == Dragon.DragonPosition.RIGHT)
            {
                _dragon1.MoveLeft();
            }
            _dragon1.setAllowChangeLocations(false);

            DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
            {
                //Dragon node = GameManager.instance.SpawnEnemy("DRAGONS") as Dragon;
                //_dragon2 = node;
                _dragon2.gameObject.SetActive(true);
                _dragon2.Init();
                _dragon2.CreateWithSkin("dragonBlue");
                //this->addChild(_dragon2);
                _dragon2.setAllowChangeLocations(false);
                _dragon2.transform.position = _dragon1._initialPosition;
                _dragon2.Entry();
                _dragon2.SetEnemyDifficulty();

                //BossHud* b = BossHud::createWithEnemy(_dragon2);
                //this->addChild(b);
                //b->setSkin("Purgana2");
            }).Play();
        }

        if (_dState == DragonManagerStates.State2 && _dragon2)
        {
            if(_dragon2.stats == null)
            {
                return;
            }

            if (_dragon1.stats.health + _dragon2.stats.health < _dragon1.stats.maxHealth.Value * 1.45f)
            {
                _dState = DragonManagerStates.State3;
                Shootup();
            }
        }
    }

    private void Shootup()
    {
        if (_dragon1.IsDead || _dragon2.IsDead)
        {
            return;
        }

        _dragon1.ShootUp();
        _dragon2.ShootUp();

        rainOfFireSeq = DOTween.Sequence().SetId(SCHEDULAR_ID).AppendInterval(4).AppendCallback(() =>
        {
            DOTween.Sequence().AppendCallback(() =>
            {
                RainOfFire();
            }).AppendInterval(0.4f).SetLoops(16);
        });
        //this->schedule(schedule_selector(DragonManager::rainOfFire), 0.4f, 16, 4);
        DOTween.Sequence().AppendInterval(20).AppendCallback(() =>
        {
            Shootup();
        });
    }

    private void RainOfFire()
    {
        Bullet bullet = new Bullet();
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(100);
        //bullet->setSpriteFrame("bulletEnemy.png");

        //bullet->setOpacity(1);
        bullet.transform.position = new Vector2(player.transform.position.x - Globals.CocosToUnity(1000) + Random.value * Globals.CocosToUnity(2000), Globals.CocosToUnity(1850));
        bullet.duration = 10;
        Vector2 dest = new Vector2(0, -Globals.CocosToUnity(6500));
        bullet.PlayBulletAnim(3 + 3, dest);
        bullet.GetRedFireballTrail().gameObject.SetActive(true);
        bullet.GetRedFireballTrail().state.SetAnimation(0, "fireball", true);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(150));
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);
        //SkeletonAnimation* fireBall1 = SkeletonAnimation::createWithJsonFile("res/Arsenal/fireball.json", "res/Arsenal/fireball.atlas");

        //fireBall1->setPosition(bullet->getContentSize().width / 2, bullet->getContentSize().height / 2);
        //bullet->addChild(fireBall1, 12);
        //fireBall1->setCameraMask(GAMECAMERA);
        //fireBall1->setAnimation(0, "fireball", true);
        //bulletLayer->setRadiusEffectSquared(150);
        //bulletLayer->bulletType = EnemyBulletFireball;

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);

    }

    private void InstantiateFlames(int index,int offset = 0)
    {
        SkeletonAnimation temp = Instantiate(_flameAnim, flameParent.transform);
        temp.transform.position = new Vector2(_dragon1.transform.position.x - Globals.CocosToUnity(GROUND_WIDTH) + (index * (Globals.CocosToUnity(GROUND_WIDTH) / 4))+ Globals.CocosToUnity(offset), Globals.LOWERBOUNDARY - Globals.CocosToUnity(70));
        temp.transform.SetScale(2);

        temp.state.TimeScale = 0.9f + (Random.value * 0.25f);
        temp.state.SetAnimation(0, "idle", true);
    }

    private void RiverFire()
    {
        camFollowSeq.Kill();
        _allowRiverDmg = true;

        if (!Globals.gameplayStarted)
            GameManager.instance.StartGameplay();

        for (int i = 0; i < 5; ++i)
        {
            //_flameAnim.transform.position = new Vector2(_dragon1.transform.position.x - GROUND_WIDTH + (i * GROUND_WIDTH / 4), Globals.LOWERBOUNDARY - Globals.CocosToUnity(70));
            //_flameAnim.transform.SetScale(2);

            //_flameAnim.state.TimeScale = 0.9f + (Random.value * 0.25f);
            //_flameAnim.state.SetAnimation(0, "idle", true);
            InstantiateFlames(i);
        }

        for (int i = 0; i < 5; ++i)
        {
            InstantiateFlames(i, 400);
            //SkeletonAnimation* node = SkeletonAnimation::createWithJsonFile("res/Explosions/flameAnim.json", "res/Explosions/flameAnim.atlas");
            //this->addChild(node, -14);
            //node->setGlobalZOrder(-26);
            //node->setPosition(_dragon1->enemySprite->getPosition().x - GROUND_WIDTH + (i * (GROUND_WIDTH / 4)) + 400, LOWERBOUNDARY - 70);
            //node->setScale(2);
            ////        node->setScaleX(3);
            //node->setTimeScale(0.9f + (rand_0_1() * 0.25f));
            //node->setCameraMask(GAMECAMERA);
            //node->setAnimation(0, "idle", true);
        }

        DOTween.Sequence().AppendInterval(2).AppendCallback(() =>
        {
            _dragon1.setAllowChangeLocations(true);
        });
    }

    private void CreateFirstDragon()
    {
        //Dragon node = GameManager.instance.SpawnEnemy("DRAGONS") as Dragon;
        //_dragon1 = node;
        _dragon1.gameObject.SetActive(true);
        _dragon1.Init();
        _dragon1.CreateWithSkin("dragonGreen");
        //this->addChild(_dragon1);
        _dragon1.enemySprite.gameObject.SetActive(false);
        _dragon1.SetEnemyDifficulty();
        
        //BossHud* b = BossHud::createWithEnemy(_dragon1);
        //this->addChild(b);
        //b->setSkin("Purgana1");
    }

    private void CreateEntryAnimation()
    {
        //_entryAnimation = SkeletonAnimation::createWithJsonFile("res/Enemy/dragonEntry.json", "res/Enemy/dragonEntry.atlas", 0.5f);
        //this->addChild(_entryAnimation);
        
        _entryAnimation.gameObject.SetActive(true);
        _entryAnimation.transform.position = new Vector2(player.transform.position.x + Globals.CocosToUnity(1200), -3);
        _entryAnimation.state.SetAnimation(0, "animation", false);
        _entryAnimation.state.TimeScale = 3f;
        DOTween.Sequence().AppendInterval(0.45f).AppendCallback(() =>
        {
            _entryAnimation.gameObject.SetActive(false);
            _isEntryOver = true;

            {
                BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                spider1 = node;
                spider1.boundaryPosX = _dragon1.transform.position.x + Globals.CocosToUnity(400);
                spider1._isLeft = 3;
                //spider1.stats.health = 10220000;
                //spider1.stats.maxHealth = 10220000;
                //this->addChild(spider1, -2);
            }

            {
                BoundarySpiders node = GameManager.instance.SpawnEnemy("BOUNDARYSPIDERS") as BoundarySpiders;
                spider2 = node;
                spider2.boundaryPosX = _dragon1.transform.position.x - Globals.CocosToUnity(GROUND_WIDTH) - Globals.CocosToUnity(200);
                spider2._isLeft = 3;
                //spider2 = SpiderBoundaries::createWithPosition(_dragon1->enemySprite->getPosition().x - GROUND_WIDTH - 200, 3);
                //this->addChild(spider2, -2);
                //spider2.stats.health = 10220000;
                //spider2.stats.maxHealth = 10220000;
            }

            Globals.RIGHTBOUNDARY = _entryAnimation.transform.position.x + Globals.CocosToUnity(1000);
            Globals.LEFTBOUNDARY = _entryAnimation.transform.position.x - Globals.CocosToUnity(2500);

            Globals.RIGHTBOUNDARY = Mathf.Lerp(Globals.RIGHTBOUNDARY, _entryAnimation.transform.position.x + Globals.CocosToUnity(100),3);
            Globals.LEFTBOUNDARY = Mathf.Lerp(Globals.LEFTBOUNDARY, _entryAnimation.transform.position.x - Globals.CocosToUnity(GROUND_WIDTH) + Globals.CocosToUnity(50), 3);

            SpawnCoinsOnEntry();

            //Globals.zoomValueOnBoss = Globals.CocosToUnity(750);
            //Globals.slowDownForBoss = true;


            for (int i = 0; i < 35; i++)
            {
                Vector2 position = Vector2.zero;
                position = new Vector2(-Globals.CocosToUnity(400) + _entryAnimation.transform.position.x + Random.value * Globals.CocosToUnity(1200), _entryAnimation.transform.position.y - Globals.CocosToUnity(100));
                InstantiateSmoke(position);

                //Sprite* trail = Sprite::create("res/Arsenal/smokeParticle.png");
                //this->addChild(trail, 3 + rand() % 2);
                ////        trail->setGlobalZOrder(-3);
                //trail->setPosition(-400 + _entryAnimation->getPosition().x + rand_0_1() * 800, _entryAnimation->getPosition().y - 100);
                //trail->setScale(2.5 + CCRANDOM_0_1() * 1.4);
                //trail->runAction(MoveBy::create(0.5, cocos2d::Point(-500 + CCRANDOM_0_1() * 1000, CCRANDOM_0_1() * 1000)));
                //trail->setRotation(CCRANDOM_0_1() * 360);
                //trail->runAction(Sequence::create(ScaleTo::create(0.5 + CCRANDOM_0_1() * 0.3, 0), RemoveSelf::create(), NULL));
                //trail->setColor(Color3B(255, 192, 0));
                //trail->runAction(Sequence::create(TintTo::create(0.25, 255, 12, 0), TintTo::create(0.5, 60, 60, 60), NULL));
            }

            for (int i = 0; i < 25; i++)
            {
                Vector2 position = Vector2.zero;
                position = _entryAnimation.transform.position;
                InstantiateSmoke(position);
                //Sprite* trail = Sprite::create("res/Arsenal/smokeParticle.png");
                //this->addChild(trail, 3 + rand() % 2);
                ////        trail->setGlobalZOrder(-3);
                //trail->setPosition(_entryAnimation->getPosition());
                //trail->setScale(2.5 + CCRANDOM_0_1() * 1.4);
                //trail->setCameraMask(GAMECAMERA);
                //trail->runAction(MoveBy::create(0.5, cocos2d::Point(-500 + CCRANDOM_0_1() * 1000, CCRANDOM_0_1() * 1000)));
                //trail->setRotation(CCRANDOM_0_1() * 360);
                //trail->runAction(Sequence::create(ScaleTo::create(0.5 + CCRANDOM_0_1() * 0.3, 0), RemoveSelf::create(), NULL));
                //trail->setColor(Color3B(255, 192, 0));
                //trail->runAction(Sequence::create(TintTo::create(0.25, 255, 12, 0), TintTo::create(0.5, 60, 60, 60), NULL));
            }

        if (_dragon1)
        {
            _dragon1.EntryFly();
        }
        });

        GameManager.instance.timeManager.SetTimescaleWithDelay(1f, 1.1f);
    }

    private void SpawnCoinsOnEntry()
    {
        for (int i = 0; i < 35; i++)
        {

            Coin coin = null;
            GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Coin);
            coin = go.GetComponent<Coin>();
            coin.isInUse = true;
            //Coin coin = new Coin();
            //bool didFindCoin = false;
            //foreach (Coin c in GameSharedData.Instance.coinsPool)
            //{
            //    if (!c.isInUse)
            //    {
            //        coin = c;
            //        coin.isInUse = true;
            //        didFindCoin = true;
            //        break;
            //    }
            //}
            //if (!didFindCoin)
            //{
            //    return;
            //}

            //Coin temp = Instantiate(coins, transform);
            coin.gameObject.SetActive(true);
            var medicamentData = MedicamentScheme.Instance.GetItem(1231);
            int goodID = medicamentData.Id;
            coin.SetCollectData(goodID, medicamentData.SubType == 7 ? 5006 : 5005, medicamentData, player,35,i);
            coin.CreateWithLocationAndMultiplier(new Vector2(-Globals.CocosToUnity(600) + _entryAnimation.transform.position.x + Random.value * Globals.CocosToUnity(1200), _entryAnimation.transform.position.y + Random.value * Globals.CocosToUnity(100)), 3.0f);
            GameSharedData.Instance.coinsInUse.Add(coin);
            ////Coins coin = Coins::createWithLocationAndMultiplier(cocos2d::Point(-200 + _entryAnimation->getPosition().x + rand_0_1() * 400, _entryAnimation->getPosition().y + rand_0_1() * 50), 3.0);
            //this->addChild(coin, 4);
        }
    }

    private void InstantiateSmoke(Vector2 pos)
    {
        GameObject temp = Instantiate(smokeTrail, smokeParent.transform);
        temp.transform.position = pos;
        //temp.transform.position = new Vector2(-Globals.CocosToUnity(400) + _entryAnimation.transform.position.x + Random.value * Globals.CocosToUnity(800), _entryAnimation.transform.position.y - Globals.CocosToUnity(100));
        temp.transform.SetScale(2.5f + Random.value * 1.4f);
        temp.transform.DOBlendableMoveBy(new Vector2(-Globals.CocosToUnity(500) + Random.value * Globals.CocosToUnity(1000), Random.value * Globals.CocosToUnity(1000)),0.5f);
        
        temp.transform.SetRotation(Random.value * 360);
        DOTween.Sequence().AppendCallback(() =>
        {
            temp.GetComponent<SpriteRenderer>().sortingLayerName = "Enemies";
            temp.GetComponent<SpriteRenderer>().sortingOrder = 1;
            temp.GetComponent<SpriteRenderer>().color = new Color(1, 0.75f, 0);
        }).Append(temp.GetComponent<SpriteRenderer>().DOColor(new Color(1, 0.04f, 0), 0.25f).OnComplete(() =>
        {
            temp.GetComponent<SpriteRenderer>().DOColor(new Color(0.23f, 0.23f, 0.23f), 0.5f);
        })).Append(temp.transform.DOScale(0, 0.5f + Random.value * 0.3f).OnComplete(() =>
        {
            Destroy(temp.gameObject);
        }));
    }

    private void FollowEntryCam()
    {
        if (_isEntryOver == false)
        {
            Globals.bossPosition = _entryAnimation.transform.position;
        }
        else
        {
            if (_dragon1)
            {
                _bone = _dragon1.enemySprite.skeleton.FindBone("hip");
                Globals.bossPosition = _bone.GetWorldPosition(_dragon1.enemySprite.transform) + Vector3.up * Globals.CocosToUnity(100)
                    + Vector3.right * Globals.CocosToUnity(300); 
                //Globals.bossPosition.x = _dragon1.transform.position.x + _bone.WorldX;
                //Globals.bossPosition.y = _dragon1.transform.position.y + _bone.WorldY + Globals.CocosToUnity(100);
            }
        }
    }

    private void RiverDamage()
    {
        if (_dragon1 && _dragon2)
        {
            if (_dragon1.IsDead && _dragon2.IsDead)
            {
                Globals.isBossMode = false;
            }

            if (_dragon1.IsDead)
            {
                if (_dragon2.IsDead == false && _dragon2._killMode == false)
                {
                    _dragon2.EnterKillMode();
                    _dragon2.isBoss = true;
                    spider1.Destroy();
                    spider2.Destroy();
                    //this->stopAllActions();
                    rainOfFireSeq.Kill();
                    //this->unschedule(schedule_selector(DragonManager::rainOfFire));

                }
            }

            if (_dragon2.IsDead)
            {
                if (_dragon1.IsDead == false && _dragon1._killMode == false)
                {
                    _dragon1.EnterKillMode();
                    _dragon1.isBoss = true;
                    spider1.Destroy();
                    spider2.Destroy();
                    //this->stopAllActions();
                    rainOfFireSeq.Kill();
                    //this->unschedule(schedule_selector(DragonManager::rainOfFire));
                }
            }
        }

        if (player.transform.position.y < Globals.LOWERBOUNDARY + Globals.CocosToUnity(250) && _allowRiverDmg
            && player.canHit)
        {
            player.GotHit(25, false);
        }
    }

    public void DragonDead()
    {
        numberOfDragons--;
        if (numberOfDragons == 0)
        {
            Globals.isBossMode = false;
        }
    }
}
