using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
public class TutorialDialogue : MonoBehaviour
{
    public enum TutorialDialogueState
    {
        none,
        Movement_Start,
        LockPlayerMovement,
        Aim_Assist_Rotation,
        Health_Regen,
        SpawnSidekicks,
        Start_Fight,
        Activate_Special_Ability_Mode,
        Enter_Berserk_Mode,
        Building_Destroyed,
        Pre_Berserk_Mode,
        Aim_Assist_Settings

    };

    [HideInInspector] public TutorialDialogueState currentState = TutorialDialogueState.none;
    [SerializeField] private TutorialDialoguePopup tdp;

    [HideInInspector] public bool canCallAttention = true;


    string schedulerId;

    public void Start()
    {
        schedulerId = "TDS" + GetInstanceID();
    }

    public void HideDialogue()
    {
        if (!Globals.popupDestroyedOnSkip)
        {
            currentState = TutorialDialogueState.none;
            tdp.HidePopup();
        }

    }
    public void ShowDialogueWithString(string s)
    {
        TutorialDialogueState state;

        tdp.ShowPopup(s);
        state = currentState;
        DOTween.Sequence().SetId(schedulerId).AppendInterval(10).AppendCallback(()=>{

            if (state == currentState && currentState != TutorialDialogueState.Aim_Assist_Rotation)
            {
                if (canCallAttention)
                {
                    tdp.CallAttention();
                }
            }

        }).Play();
    }

    public void ShowDialogueWithState(TutorialDialogueState state)
    {

        if (Globals.isGameInTutorialState)
        {

            currentState = state;


            if (state == TutorialDialogueState.Movement_Start)
            {
                

            }
            else if (state == TutorialDialogueState.LockPlayerMovement)
            {
               
            }

        }
    }
}
