using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Spine;
using Spine.Unity;
public class Tesla : MonoBehaviour
{
    [SerializeField] private SkeletonAnimation tesla;
    [SerializeField] private GameObject[] zapline;
    [SerializeField] private GameObject[] zapper;
    [SerializeField] private Material zapperMatOriginal;
    [SerializeField] private Material zapperMat;

    [HideInInspector] public string tweenId;
    [HideInInspector] public string schedulerId;

    public void Create()
    {
        Init();
    }

    private void Init()
    {
        tweenId = "tesla" + GetInstanceID();
        schedulerId = "teslaS" + GetInstanceID();
        transform.position = new Vector3(Globals.CocosToUnity(900),Globals.LOWERBOUNDARY);
        tesla.state.SetAnimation(0, "idle", true);
        DOTween.Sequence().SetId(schedulerId).AppendInterval(0.1f).AppendCallback(Shoot).SetLoops(-1).Play();

        foreach (GameObject zap in zapper)
        {
            zap.GetComponent<SkeletonAnimation>().CustomMaterialOverride.Add(zapperMatOriginal, zapperMat);
            zap.transform.parent = null;
        }

        foreach (GameObject zap in zapline)
        {
            zap.transform.parent = null;
        }
    }

    private void Shoot()
    {
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.Tag != 2)
                enemy.Tag = 0;
        }

        Attack(new Vector2(tesla.transform.position.x, tesla.transform.position.y + Globals.CocosToUnity(800)), true);
    }

    private void Attack(Vector2 startPos, bool drawLine)
    {

        int attackCount = 0;
        foreach (Enemy enemy in GameSharedData.Instance.enemyList)
        {
            if (enemy.Tag != 2)
            {
                if (Vector2.SqrMagnitude((Vector2)enemy.transform.position - startPos) < Globals.CocosToUnity(18000) && enemy.Tag != 1)
                {
                    attackCount++;
                    if (drawLine)
                    {

                        bool didFindZap = false;
                        GameObject zapl = null;

                        foreach (GameObject z in zapline)
                        {
                            if (!z.activeSelf)
                            {
                                didFindZap = true;
                                zapl = z;
                                break;
                            }
                        }
                        if (!didFindZap)
                        {
                            return;
                        }

                        zapl.SetActive(true);
                        zapl.transform.position = startPos.GetMidPoint(enemy.transform.position);
                        var dir = Vector2.SignedAngle(Vector2.right, (Vector2)enemy.transform.position - startPos);
                        dir = dir < 0 ? 360 + dir : dir;
                        zapl.transform.rotation = Quaternion.AngleAxis(dir, Vector3.forward);
                        float boxsize = zapl.GetComponent<SpriteRenderer>().bounds.size.x * 1.1f;
                        if (boxsize == 0)
                        {
                            boxsize = 0.001f;
                        }
                        zapl.transform.SetScaleX(Vector2.Distance(startPos, enemy.transform.position) / boxsize);
                        zapl.transform.SetScaleY(2);
                        //DOTween.Sequence().SetId(tweenId).AppendInterval(0.1f).AppendCallback(() => { zapl.gameObject.SetActive(false); }).Play();

                    }

                    bool didFindZapper = false;
                    GameObject zap = null;

                    foreach (GameObject z in zapper)
                    {
                        if (!z.activeSelf)
                        {
                            didFindZapper = true;
                            zap = z;
                            break;
                        }
                    }
                    if (!didFindZapper)
                    {
                        return;
                    }
                    //zap.transform.parent = null;
                    enemy.Tag = 1;
                    zap.transform.position =enemy.transform.position;
                    zap.SetActive(true);
                    DOTween.Sequence().SetId(tweenId).AppendInterval(0.1f).AppendCallback(() => { zap.SetActive(false); }).Play();
                    zap.transform.SetScale(enemy._jsonScale/3);
                    if (attackCount < 4)
                    {
                        Attack(enemy.enemySprite.transform.position, true);
                    }
                    if (enemy.TakeHit(3))
                    {


                        GameData.instance.fileHandler.playerXP += (int)enemy.stats.maxHealth.Value;

                        GameData.instance.fileHandler.totalXP += (int)enemy.stats.maxHealth.Value;


                        if (Globals.gameType == GameType.Training)
                        {
                            GameData.instance.fileHandler.TrainingPoints += (int)enemy.stats.maxHealth.Value;

                        }
                        //for (int i = 0; i < enemy.stats.coinAwarded * (0.1 * 1); i++)
                        //{
                        //    //TODO
                        //    Coin coin = new Coin();
                        //    bool didFindCoin = false;
                        //    foreach (Coin c in GameSharedData.Instance.coinsPool)
                        //    {
                        //        if (!c.isInUse)
                        //        {
                        //            coin = c;
                        //            coin.isInUse = true;
                        //            didFindCoin = true;
                        //            break;
                        //        }
                        //    }
                        //    if (!didFindCoin)
                        //    {
                        //        return;
                        //    }

                        //    coin.gameObject.SetActive(true);
                        //    coin.CreateWithLocation(enemy.transform.position);
                        //    GameSharedData.Instance.coinsInUse.Add(coin);
                        //    //Coins* coin = Coins::createWithLocation(enemy.enemySprite.getPosition());
                        //    //this.addChild(coin);
                        //}

                        GameSharedData.Instance.explosions.GenerateParticlesAt(Explosions.ExplosionType.ExplosionTypeAir1, enemy.enemySprite.transform.position, false, 3, 0.5f, 150);
                        if (GameManager.instance.missionManager.missionType == Globals.MissionTypeKill && Globals.gameType == GameType.Training)
                        {
                            GameManager.instance.missionManager.AddPoint();
                        }
                        GameManager.instance.ShakeCamera(0.5f, 8);

                        GameData.instance.fileHandler.totalKills++;

                        if (!enemy.isDestroyed)
                        {
                            enemy.isDestroyed = true;
                            enemy.Destroy();
                        }
                        //GameSharedData::getInstance().g_enemyArray.eraseObject(enemy);
                        //enemy.removeAllChildrenWithCleanup(true);
                        //enemy.removeFromParentAndCleanup(true);
                        //enemy = nullptr;

                    }

                }
            }
        }
    }


    private void Update()
    {

    }

    public void Destroy()
    {

    }



}
