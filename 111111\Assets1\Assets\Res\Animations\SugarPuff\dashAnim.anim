%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: dashAnim
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.36666667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.a
    path: dash
    classID: 212
    script: {fileID: 0}
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.033333335
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.06666667
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.1
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.13333334
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.16666667
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.2
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.23333333
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.26666668
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.3
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.33333334
      value: {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    - time: 0.36666667
      value: {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba,
        type: 3}
    attribute: m_Sprite
    path: dash
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 30
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 2607734016
      attribute: 304273561
      script: {fileID: 0}
      typeID: 212
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2607734016
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -4601184599733052067, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
    - {fileID: -822152551968487720, guid: a1de3c56433ec498f8971ffc07fa46ba, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.4
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.36666667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.a
    path: dash
    classID: 212
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events:
  - time: 0.4
    functionName: DisableSelf
    data: 
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0
