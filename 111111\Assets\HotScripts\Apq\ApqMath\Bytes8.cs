﻿using System;

namespace Apq.ApqMath
{
    /// <summary>
    /// 8字节数值
    /// </summary>
    public struct Bytes8 : IEquatable<Bytes8>
    {
        public byte Byte1;
        public byte Byte2;
        public byte Byte3;
        public byte Byte4;
        public byte Byte5;
        public byte Byte6;
        public byte Byte7;
        public byte Byte8;

        #region IEquatable
        public override readonly int GetHashCode() => ((long)this).GetHashCode();

        public readonly bool Equals(Bytes8 other)
        {
            return (long)this == (long)other;
        }
        public override readonly bool Equals(object obj) => obj is Bytes8 other && Equals(other);

        public static bool operator ==(Bytes8 left, Bytes8 right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(Bytes8 left, Bytes8 right)
        {
            return !(left == right);
        }
        #endregion

        #region double
        public static implicit operator double(Bytes8 bin8)
        {
            var bytes = new byte[]
            {
                bin8.Byte1,
                bin8.Byte2,
                bin8.Byte3,
                bin8.Byte4,
                bin8.Byte5,
                bin8.Byte6,
                bin8.Byte7,
                bin8.Byte8,
            };

            return BitConverter.ToDouble(bytes);
        }
        public static implicit operator Bytes8(double num)
        {
            var bytes = BitConverter.GetBytes(num);
            var bin8 = new Bytes8()
            {
                Byte1 = bytes[0],
                Byte2 = bytes[1],
                Byte3 = bytes[2],
                Byte4 = bytes[3],
                Byte5 = bytes[4],
                Byte6 = bytes[5],
                Byte7 = bytes[6],
                Byte8 = bytes[7],
            };
            return bin8;
        }
        #endregion

        #region long
        public static implicit operator long(Bytes8 bin8)
        {
            var bytes = new byte[]
            {
                bin8.Byte1,
                bin8.Byte2,
                bin8.Byte3,
                bin8.Byte4,
                bin8.Byte5,
                bin8.Byte6,
                bin8.Byte7,
                bin8.Byte8,
            };

            return BitConverter.ToInt64(bytes);
        }
        public static implicit operator Bytes8(long num)
        {
            var bytes = BitConverter.GetBytes(num);
            var bin8 = new Bytes8()
            {
                Byte1 = bytes[0],
                Byte2 = bytes[1],
                Byte3 = bytes[2],
                Byte4 = bytes[3],
                Byte5 = bytes[4],
                Byte6 = bytes[5],
                Byte7 = bytes[6],
                Byte8 = bytes[7],
            };
            return bin8;
        }
        #endregion

        #region float
        public static implicit operator float(Bytes8 bin8)
        {
            var bytes = new byte[]
            {
                bin8.Byte1,
                bin8.Byte2,
                bin8.Byte3,
                bin8.Byte4,
                bin8.Byte5,
                bin8.Byte6,
                bin8.Byte7,
                bin8.Byte8,
            };

            return (float)BitConverter.ToDouble(bytes);
        }
        public static implicit operator Bytes8(float num)
        {
            var bytes = BitConverter.GetBytes((double)num);
            var bin8 = new Bytes8()
            {
                Byte1 = bytes[0],
                Byte2 = bytes[1],
                Byte3 = bytes[2],
                Byte4 = bytes[3],
                Byte5 = bytes[4],
                Byte6 = bytes[5],
                Byte7 = bytes[6],
                Byte8 = bytes[7],
            };
            return bin8;
        }
        #endregion
    }
}
