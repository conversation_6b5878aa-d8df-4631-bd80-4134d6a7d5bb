using System.Collections;
using System.Collections.Generic;
using Spine;
using Spine.Unity;
using DG.Tweening;
using UnityEngine;

public class BotController : MonoBehaviour
{
    [SerializeField] Sprite bulletEnemySprite;
    [SerializeField] Portal portalPrefab;
    [SerializeField] SkeletonAnimation botparentPrefab;
    [SerializeField] TinyBots tinyBotsPrefab;

    private PlayerController player;
    private GameObject botsContainer;

    [HideInInspector] public int totalBots = 0;

    [HideInInspector] public SkeletonAnimation _botParent;
    SkeletonAnimation portalSpine;
    [HideInInspector] public Portal portal;

    StateMachine _botStateMachine;

    Bone spineBone;
    Bone rootBone;

    [HideInInspector] public Vector2 PORTAL_POSITION;
    bool stick = false;
    bool _allowBotCollisionDamaage = false;
    bool scheduleUpdate;
    bool isEndAnimation = false, makeTrail;

    bool[] boneArray;
    int botRemaining;

    Sequence spawnBot, endAnimation;
    public Sequence circleShoot;

    public const uint TOTAL_BOTS = 300;
    public const float PORTAL_MID = 755;
    const string tweenID = "BotController", schedulerID = "BotControllerSchedules";

    void Start()
    {
        botRemaining = 300;
        stick = false;
        boneArray = new bool[31];
        for (int i = 0; i < 31; i++)
            boneArray[i] = false;

        Init();
    }

    public void SetStickyBot(bool val) { stick = val; }

    void Init()
    {
        GameSharedData.Instance.allBattleScript.Add(this);
        player = GameManager.instance.player;

        InitPortal();

        Vector2 winSize = new Vector2(1334, 750);
        PORTAL_POSITION = new Vector2(Globals.CocosToUnity(1000), Globals.LOWERBOUNDARY);
        botsContainer = new GameObject();
        botsContainer.name = "TinyBotsContainer";
        AudioManager.instance.PlaySound(AudioType.Loop, Constants_Audio.Audio.TinyBotBGloop);
        //Shared.playSound("res/Sounds/Bosses/boss9/Tiny Bot - BG (loop).mp3", true, 1.0f);

        scheduleUpdate = true;
        makeTrail = true;
        spawnBot = DOTween.Sequence().SetId(schedulerID).AppendInterval(0.1f).AppendCallback(SpawnBot).SetLoops(-1);

        _botStateMachine = StateMachine.Create();
        _botStateMachine.AddState<BotStateIdle>();
        _botStateMachine.AddState<BotStateWall>();
        _botStateMachine.AddState<BotStateHammer>();
        _botStateMachine.AddState<BotStateStar>();
        _botStateMachine.AddState<BotStateTank>();
        _botStateMachine.AddState<BotStateCircle>();

        _botStateMachine.FindState<BotStateIdle>().botController = this;
        _botStateMachine.FindState<BotStateWall>().botController = this;
        _botStateMachine.FindState<BotStateHammer>().botController = this;
        _botStateMachine.FindState<BotStateStar>().botController = this;
        _botStateMachine.FindState<BotStateTank>().botController = this;
        _botStateMachine.FindState<BotStateCircle>().botController = this;

        endAnimation = DOTween.Sequence().SetId(schedulerID).AppendInterval(0.75f).AppendCallback(EndAnimation);

        // TODO REMOVE
        //{
        //    DOTween.Kill(schedulerID);
        //    spawnBot.Kill();
        //    scheduleUpdate = false;
        //    makeTrail = true;
        //    DOTween.Sequence().AppendInterval(1).AppendCallback(() =>
        //    {
        //        portal.StartPortal();
        //    });
        //}
    }

    void EndAnimation()
    {
        _botStateMachine.SetState<BotStateIdle>();

        isEndAnimation = true;
    }

    void InitPortal()
    {
        portal = Instantiate(portalPrefab);
        portal.Init();
        PORTAL_POSITION = portal.transform.position;
        _botParent = Instantiate(botparentPrefab);
        _botParent.state.SetAnimation(0, "hammer", true);
        rootBone = _botParent.skeleton.FindBone("root");
        portal.SetEnemyDifficulty();
        Globals.bossPosition = PORTAL_POSITION + Vector2.up * 2;
        _botParent.state.Event += (TrackEntry entry, Spine.Event spineEvent) =>
        {
            if (spineEvent.Data.Name == "dmgOn")
            {
                _allowBotCollisionDamaage = true;
                stick = true;
            }
            if (spineEvent.Data.Name == "dmgOff")
            {
                _allowBotCollisionDamaage = false;
                stick = false;
            }
            if (spineEvent.Data.Name == "wallShoot")
            {
                WallShoot();
            }
        };
    }

    public void SpawnSlashForStar()
    {
        //SkeletonAnimation slash = SkeletonAnimation.createWithJsonFile("res/Enemy/slash.json", "res/Enemy/slash.atlas");
        //slash.SetScale(2);
        //slash.setOpacity(1);
        //slash.setPosition(_botParent.getPosition().x + rootBone.WorldX, _botParent.getPosition().y + rootBone.WorldY);
        //slash.runAction(Sequence.create(DelayTime.create(0.5), RotateBy.create(2.0, 5000), RemoveSelf.create(), NULL));
        //slash.runAction(Sequence.create(DelayTime.create(1.0), FadeIn.create(0.5), DelayTime.create(1), RemoveSelf.create(), NULL));


        //SkeletonAnimation* slash2 = SkeletonAnimation.createWithJsonFile("res/Enemy/slash.json", "res/Enemy/slash.atlas");
        //this.addChild(slash2, 5);
        //slash2.setGlobalZOrder(10);
        //slash2.setCameraMask(GAMECAMERA);
        //slash2.setScale(1);
        //slash2.setOpacity(1);
        //slash2.setPosition(_botParent.getPosition().x + rootBone.WorldX, _botParent.getPosition().y + rootBone.WorldY);
        //slash2.runAction(Sequence.create(DelayTime.create(0.5), RotateBy.create(2.0, 5000), RemoveSelf.create(), NULL));
        //slash2.runAction(Sequence.create(DelayTime.create(1.0), FadeTo.create(0.5, 255), DelayTime.create(1), RemoveSelf.create(), NULL));

        for (int i = 0; i < 25; i++)
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }

            bullet.gameObject.SetActive(false);
            bullet.setDamage(GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots ? 125 : 1);
            bullet.SetSpriteFrame(null);
            bullet.SetOpacity(1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(100));
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);

            bullet.transform.position = rootBone.GetWorldPosition(_botParent.transform);
            var randomRot = Random.value * 360;
            bullet.transform.SetRotation(randomRot);

            var fireball = bullet.transform.GetChild(5);
            fireball.gameObject.SetActive(false);
            bullet.transform.localScale = Vector3.one;

            DOTween.Sequence().SetId(tweenID).AppendInterval(1.5f + (i * 0.05f)).AppendCallback(() =>
            {
                bullet.gameObject.SetActive(true);
                fireball.gameObject.SetActive(true);
                var dest = new Vector2(Globals.CocosToUnity(2000) * Mathf.Sin(randomRot * Mathf.Deg2Rad),
                    Globals.CocosToUnity(2000) * Mathf.Cos(randomRot * Mathf.Deg2Rad));
                bullet.PlayBulletAnim(1.5f, dest);

                bullet.transform.right = dest.normalized;
            });

            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }
    }

    void WallShoot()
    {
        for (int i = 0; i < 20; i++)
        {
            Bullet bullet = null;

            bool didFindBullet = false;
            foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
            {
                if (!b.isInUse)
                {
                    bullet = b;
                    bullet.isInUse = true;
                    didFindBullet = true;
                    break;
                }
            }
            if (!didFindBullet)
            {
                return;
            }
            bullet.gameObject.SetActive(false);
            bullet.setDamage(GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots ? 100 : 1);
            bullet.SetSpriteFrame(null);
            bullet.SetOpacity(1);
            bullet.setRadiusEffectSquared(Globals.CocosToUnity(100));
            bullet.duration = 1.5f + (i * 0.05f);
            float randomValue = Random.value;
            bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);
            var bonePos = rootBone.GetWorldPosition(_botParent.transform);

            var fireball = bullet.transform.GetChild(5);
            fireball.gameObject.SetActive(false);
            bullet.transform.localScale = Vector3.one;

            if (randomValue < 0.5f)
            {
                bullet.transform.position = new Vector3(bonePos.x - Globals.CocosToUnity(1560),
                    -Globals.CocosToUnity(200) + bonePos.y + Random.value * Globals.CocosToUnity(2000));
                bullet.transform.SetRotation(0);
                DOTween.Sequence().SetId(tweenID).AppendInterval(1.5f + (i * 0.05f)).AppendCallback(() =>
                {
                    bullet.gameObject.SetActive(true);
                    fireball.gameObject.SetActive(true);
                    bullet.PlayBulletAnim(1.5f, new Vector2(Globals.CocosToUnity(3000), 0));
                });
            }
            else
            {
                bullet.transform.position = new Vector3(bonePos.x + Globals.CocosToUnity(1560),
                    -Globals.CocosToUnity(200) + bonePos.y + Random.value * Globals.CocosToUnity(2000));
                bullet.transform.SetRotation(180);
                DOTween.Sequence().SetId(tweenID).AppendInterval(1.5f + (i * 0.05f)).AppendCallback(() =>
                {
                    bullet.gameObject.SetActive(true);
                    fireball.gameObject.SetActive(true);
                    bullet.PlayBulletAnim(1.5f, new Vector2(-Globals.CocosToUnity(3000), 0));
                });
            }

            GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        }
    }

    public void CircleShoot()
    {
        Bullet bullet = null;

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.gameObject.SetActive(false);
        bullet.setDamage(GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots ? 100 : 1);
        bullet.SetSpriteFrame(null);
        bullet.SetOpacity(1);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(100));
        bullet.SetBulletType(Bullet.BulletType.EnemyBulletBlueExplosion);

        int arrayPos = Random.Range(0, int.MaxValue) % GameSharedData.Instance.enemyList.Count;
        if (arrayPos < GameSharedData.Instance.enemyList.Count)
        {
            Enemy enemy = GameSharedData.Instance.enemyList[arrayPos];
            if (enemy.enemySprite)
            {
                bullet.transform.position = GameSharedData.Instance.enemyList[arrayPos].transform.position;
            }
        }

        //var angle = Vector2.SignedAngle(_botParent.transform.position - bullet.transform.position, Vector2.right);
        //angle = angle < 0 ? 360 + angle : angle;
        var dirVector = _botParent.transform.position - bullet.transform.position;
        bullet.transform.right = dirVector.normalized;

        bullet.duration = 4.0f;
        var dest = dirVector.normalized * Globals.CocosToUnity(2000);

        bullet.PlayBulletAnim(bullet.duration, dest);
        var fireball = bullet.transform.GetChild(5);
        fireball.gameObject.SetActive(true);
        bullet.transform.localScale = Vector3.one;

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    public void CannonAttack()
    {
        _botParent.state.SetAnimation(0, "tankShoot", false);

        Bullet bullet = null;

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.gameObject.SetActive(false);
        bullet.setDamage(GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots ? 500 : 1);
        bullet.SetSpriteFrame(null);
        bullet.SetOpacity(1);
        bullet.setRadiusEffectSquared(Globals.CocosToUnity(400));
        spineBone = _botParent.skeleton.FindBone("tankShoot");
        var bonePos = spineBone.GetWorldPosition(_botParent.transform);

        bullet.transform.position = bonePos;

        bullet.duration = 3.5f;
        DOTween.Sequence().SetId(tweenID).AppendInterval(2).AppendCallback(() =>
        {
            TankSplash();
            bullet.gameObject.SetActive(true);
            bullet.PlayBulletAnim(1.5f, new Vector2(-Globals.CocosToUnity(3000), 0));
        });

        bullet.transform.localScale = new Vector3(3, 3, 1);
        var fireball = bullet.transform.GetChild(4);
        fireball.gameObject.SetActive(true);
        var fireballSkeleton = fireball.GetComponent<SkeletonAnimation>();
        if (fireballSkeleton != null && fireballSkeleton.state != null)
        {
            fireballSkeleton.state.SetAnimation(0, "blueFlame", true);
        }

        bullet.SetBulletType(Bullet.BulletType.EnemyBulletFireball);

        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
    }

    void TankSplash()
    {
        spineBone = _botParent.skeleton.FindBone("tankShoot");
        stick = false;
        for (int i = 0; i < 10; i++)
        {
            //Sprite* trail = Sprite.create("res/Arsenal/smokeParticle.png");
            //this.addChild(trail, 3);
            //trail.setPosition(_botParent.getPosition().x + _botParent.getScaleY() * spineBone.WorldX, _botParent.getPosition().y + _botParent.getScaleY() * spineBone.WorldY);
            //trail.setScale(10);
            //trail.setCameraMask(GAMECAMERA);
            //trail.runAction(MoveBy.create(0.5, cocos2d.Point(+500 + CCRANDOM_0_1() * 800, -400 + CCRANDOM_0_1() * 800)));
            //trail.setRotation(CCRANDOM_0_1() * 360);
            //trail.runAction(Sequence.create(ScaleTo.create(0.5 + CCRANDOM_0_1() * 0.3, 0), RemoveSelf.create(), NULL));
            //trail.setColor(Color3B(255, 255, 255));
            //trail.runAction(Sequence.create(TintTo.create(0.25, 7, 167, 255), TintTo.create(0.5, 40, 0, 190), NULL));
        }

        GameManager.instance.ShakeCamera(10, 5);
    }

    void Update()
    {
        if (!scheduleUpdate)
            return;

        if (_botStateMachine != null)
        {
            _botStateMachine.UpdateWithDeltaTime();
        }


        CollisionDamage();
        //Globals.bossPosition = new Vector3(portal.transform.position.x, portal.transform.position.y + PORTAL_MID/3);

        foreach (var enemy in GameSharedData.Instance.enemyList)
        {
            TinyBots bot = (TinyBots)enemy;
            if (bot.AttachToBone)
            {
                string ch = "bone" + bot.GetBoneAssigned();
                spineBone = _botParent.skeleton.FindBone(ch);
                var spineBonePos = spineBone.GetWorldPosition(_botParent.transform);

                if (spineBone == null)
                {
                    return;
                }

                if (stick == false)
                {
                    float mult = 10;
                    if (isEndAnimation == false)
                    {
                        mult = 1;
                    }

                    var dist = Vector2.Distance(bot.transform.position, spineBonePos);

                    if (dist > Globals.CocosToUnity(175))
                    {
                        bot.transform.position =
                            new Vector3(bot.transform.position.x
                            + (spineBonePos.x - bot.transform.position.x)
                            * Time.deltaTime * mult,
                            bot.transform.position.y + (spineBonePos.y - bot.transform.position.y) * Time.deltaTime * mult);
                    }
                    else
                    {
                        bot.transform.position =
                            new Vector3(bot.transform.position.x
                            + (spineBonePos.x - bot.transform.position.x)
                            * Time.deltaTime * 15,
                            bot.transform.position.y + (spineBonePos.y - bot.transform.position.y) * Time.deltaTime * 15);
                    }
                }
                else if (stick == true)
                {
                    bot.transform.position = spineBonePos;
                }

                if (spineBone.ShearX == Mathf.Round(0.1f * 100) / 100)
                {
                    bot.enemySprite.state.SetAnimation(0, "red", false);
                }
                else if (spineBone.ShearX == Mathf.Round(0.2f * 100) / 100)
                {
                    bot.enemySprite.state.SetAnimation(0, "blur", false);
                }
                else
                {
                    bot.enemySprite.state.SetAnimation(0, "blue", false);
                }
                bot.transform.rotation = Quaternion.Euler(Vector3.forward * spineBone.LocalToWorldRotation(spineBone.Rotation));
                bot.transform.localScale = new Vector3(spineBone.ScaleX * rootBone.ScaleX, spineBone.ScaleX * rootBone.ScaleX,
                    1);
                // TODO 
                bot.enemyCollisionRadius = 1; //Globals.CocosToUnity(10000) * spineBone.ScaleX * rootBone.ScaleX; 
            }


        }
    }

    void SpawnBot()
    {
        if (totalBots > TOTAL_BOTS)
        {
            if (GameSharedData.Instance.enemyList.Count == 0)
            {
                portal.StartPortal();
                DOTween.Kill(schedulerID);
                spawnBot.Kill();
                circleShoot.Kill();
                scheduleUpdate = false;
                makeTrail = true;
            }
            return;
        }
        if (GameSharedData.Instance.enemyList.Count < 31)
        {
            totalBots++;
            TinyBots tb = Instantiate(tinyBotsPrefab);
            tb.Init();
            if (tb.transform == null)
            {
                return;
            }
            tb.transform.parent = botsContainer.transform;
            tb.transform.position = portal.PortalMid;
            RefreshBoneArray();

            for (int i = 0; i < 31; i++)
            {
                if (boneArray[i] == false)
                {
                    boneArray[i] = true;
                    tb.AssignBone(i);
                    return;
                }
            }
        }
    }

    void CollisionDamage()
    {
        if (_allowBotCollisionDamaage == false)
            return;

        foreach (var enemy in GameSharedData.Instance.enemyList)
        {
            var dist = Vector2.Distance(player.transform.position, enemy.transform.position);
            if (dist < enemy.enemyCollisionRadius + player.CollisionRadius)
            {
                player.GotHit(GameData.instance.fileHandler.currentEvent == (int)EventBoss.kBossTinyBots ? 20 : 1, false);
            }
        }
    }

    void RefreshBoneArray()
    {
        for (int i = 0; i < 31; i++)
        {
            boneArray[i] = false;
        }
        foreach (var enemy in GameSharedData.Instance.enemyList)
        {
            TinyBots bot = (TinyBots)enemy;
            boneArray[bot.GetBoneAssigned()] = true;
        }
    }

    private void OnDestroy()
    {
        AudioManager.instance.StopSoundEffectByName(Constants_Audio.Audio.TinyBotBGloop);
    }
}



public class BotStateIdle : State
{
    public BotController botController;
    float timeToRun = 3;
    string previousState;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 3.0f;
        this.previousState = previousState == null ? "" : previousState.GetStateType();
        botController._botParent.state.SetAnimation(0, "idle", true);
        botController._botParent.transform.position = botController.portal.PortalMid;
    }

    public override void UpdateState()
    {
        timeToRun -= Time.deltaTime;

        if (timeToRun < 0)
        {
            MoveToNextState();
        }
    }

    public override string GetStateType()
    {
        return "Idle";
    }

    void MoveToNextState()
    {
        if ((previousState == "Hammer" || previousState == "Star") && botController.totalBots > BotController.TOTAL_BOTS * 0.33f)
        {
            stateMachine.SetState<BotStateTank>();
            return;
        }

        if ((previousState == "Tank" || previousState == "Wall") && botController.totalBots > BotController.TOTAL_BOTS * 0.66f)
        {
            stateMachine.SetState<BotStateCircle>();
            return;
        }

        if (previousState == "")
        {
            stateMachine.SetState<BotStateHammer>();
        }

        if (previousState == "Hammer")
        {
            stateMachine.SetState<BotStateStar>();
        }
        else if (previousState == "Star")
        {
            stateMachine.SetState<BotStateHammer>();
        }

        if (previousState == "Tank")
        {
            stateMachine.SetState<BotStateWall>();
        }
        else if (previousState == "Wall")
        {
            stateMachine.SetState<BotStateTank>();
        }

        if (previousState == "Circle")
        {
            stateMachine.SetState<BotStateCircle>();
        }
    }
}

public class BotStateWall : State
{
    public BotController botController;
    float timeToRun = 3;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 6.0f;

        SkeletonAnimation portal = botController._botParent;

        float p = GameManager.instance.player.transform.position.x;

        if (p < Globals.LEFTBOUNDARY + Globals.CocosToUnity(1300))
        {
            p = Globals.LEFTBOUNDARY + Globals.CocosToUnity(1300);
        }

        if (p > Globals.RIGHTBOUNDARY - Globals.CocosToUnity(1300))
        {
            p = Globals.RIGHTBOUNDARY - Globals.CocosToUnity(1300);
        }

        portal.transform.position = new Vector3(p, -Globals.CocosToUnity(400), portal.transform.position.z);
        portal.state.SetAnimation(0, "wall", false);
        portal.state.AddAnimation(0, "wallShoot", false);
    }

    public override void UpdateState()
    {
        timeToRun -= Time.deltaTime;

        if (timeToRun < 0)
        {
            stateMachine.SetState<BotStateIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Wall";
    }
}

public class BotStateHammer : State
{
    public BotController botController;
    float timeToRun = 3;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 3.8f;

        SkeletonAnimation portal = botController._botParent;
        portal.transform.position =
            new Vector3(GameManager.instance.player.transform.position.x
            - Globals.CocosToUnity(500) + Random.value * Globals.CocosToUnity(1000), -Globals.CocosToUnity(300));
        portal.state.SetAnimation(0, "hammer", false);
        portal.state.AddAnimation(0, "hammerTime", false);
    }

    public override void UpdateState()
    {
        timeToRun -= Time.deltaTime;

        if (timeToRun < 0)
        {
            stateMachine.SetState<BotStateIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Hammer";
    }
}

public class BotStateStar : State
{
    public BotController botController;
    float timeToRun = 3;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 6.0f;

        SkeletonAnimation portal = botController._botParent;
        portal.transform.position =
            new Vector3(GameManager.instance.player.transform.position.x
            - Globals.CocosToUnity(500) + Random.value * Globals.CocosToUnity(1000), -Globals.CocosToUnity(300));
        portal.state.SetAnimation(0, "star", false);
        portal.state.AddAnimation(0, "starShoot", false);
        portal.state.AddAnimation(0, "starShoot", false);
        portal.state.AddAnimation(0, "starShoot", false);
        portal.state.AddAnimation(0, "starShoot", false);
        portal.state.AddAnimation(0, "starReverse", false);

        DOTween.Sequence().SetId("BotController").AppendInterval(1.8f).AppendCallback(() =>
        {
            botController.SetStickyBot(true);
            botController.SpawnSlashForStar();
        });
    }

    public override void UpdateState()
    {
        timeToRun -= Time.deltaTime;
        if (timeToRun < 0)
        {
            stateMachine.SetState<BotStateIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Star";
    }
}

public class BotStateTank : State
{
    public BotController botController;
    float timeToRun = 3;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 4.5f;

        SkeletonAnimation portal = botController._botParent;
        portal.state.SetAnimation(0, "tank", false);
        portal.transform.position = new Vector3(GameManager.instance.player.transform.position.x + Globals.CocosToUnity(1600),
            Globals.LOWERBOUNDARY);

        DOTween.Sequence().SetId("BotController").AppendInterval(0.5f).AppendCallback(() =>
        {
            botController.SetStickyBot(true);
            botController.CannonAttack();
        });
    }

    public override void UpdateState()
    {
        timeToRun -= Time.deltaTime;

        if (timeToRun < 0)
        {
            stateMachine.SetState<BotStateIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Tank";
    }


}

public class BotStateCircle : State
{
    public BotController botController;
    float timeToRun = 3;

    public override void DidEnterWithPreviousState(State previousState)
    {
        timeToRun = 15.0f;

        SkeletonAnimation portal = botController._botParent;
        portal.state.SetAnimation(0, "circle", false);
        portal.transform.position = GameManager.instance.player.transform.position;
        portal.skeleton.FindBone("root").ScaleX = 2.35f;
        portal.skeleton.FindBone("root").ScaleY = 2.35f;
        botController.circleShoot = DOTween.Sequence().SetId("BossControllerSchedules").AppendCallback(botController.CircleShoot)
            .AppendInterval(0.5f).SetLoops(-1);
    }

    public override void UpdateState()
    {
        SkeletonAnimation portal = botController._botParent;

        var portalPos = portal.transform.position;
        var playerPos = GameManager.instance.player.transform.position;
        portal.transform.position = new Vector3(portalPos.x + (playerPos.x - portalPos.x) * Time.deltaTime * 2,
            portalPos.y + (playerPos.y - portalPos.y) * Time.deltaTime * 2);
        portal.skeleton.FindBone("root").ScaleX = 1.5f;
        portal.skeleton.FindBone("root").ScaleY = 1.5f;

        timeToRun -= Time.deltaTime;

        if (timeToRun < 0)
        {
            botController.circleShoot.Kill();

            stateMachine.SetState<BotStateIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Circle";
    }

}
