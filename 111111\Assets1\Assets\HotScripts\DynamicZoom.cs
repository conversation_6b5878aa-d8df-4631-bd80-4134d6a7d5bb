using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DynamicZoom : MonoBehaviour
{
    PlayerController player;

    float _gameZoomFactor;
    Transform targetTransform;
    float _missionPingCounter = 0, _magnitude, _speed, _distance;
    bool _allowMissionPing = false;

    public float Magnitude { get { return _magnitude; } set { _magnitude = value; } }
    public float Speed { get { return _speed; } set { _speed = value; } }
    public float Distance { get { return _distance; } set { _distance = value; } }

    public void SetAllowMissionPing(bool allowPing) { _allowMissionPing = allowPing; }

    public static DynamicZoom Create(Transform target, float magnitude, float speed, float distance)
    {
        DynamicZoom dz = new GameObject().AddComponent<DynamicZoom>();

        dz.targetTransform = target;
        dz.Speed = speed;
        dz.Magnitude = magnitude;
        dz.Distance = distance;
        dz.Init();
        return dz;
    }


    public void Init()
    {
        _gameZoomFactor = Globals.zoomValueWhileGame;
        player = GameManager.instance.player;
    }

    void Update()
    {
        if (targetTransform == null)
            return;

        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_FLYING)
        {
            Vector2 playerPos = player.transform.position;
            float dis = Mathf.Abs(playerPos.x - targetTransform.position.x);
            Globals.bossPosition = targetTransform.position;
            if (dis < _distance)
            {
                if (Globals.zoomValueWhileGame > _gameZoomFactor * _magnitude)
                {
                    Globals.zoomValueWhileGame -= Time.deltaTime * _speed;
                }
                if (_allowMissionPing)
                {
                    _missionPingCounter = 0;
                }
            }
            else
            {
                if (Globals.zoomValueWhileGame < _gameZoomFactor)
                {
                    Globals.zoomValueWhileGame += Time.deltaTime * _speed;
                }

                if (_allowMissionPing)
                {
                    _missionPingCounter += Time.deltaTime;
                    if (_missionPingCounter > 15.0f)
                    {
                        _missionPingCounter = 0;

                        //Enemy enemy = dynamic_cast<Enemy*>(_obj->getParent()); // TODO

                        //if (enemy) TODO
                        //{
                        //    if (enemy->_ping)
                        //    {
                        //        enemy->_ping->runAction(Repeat::create(Sequence::create(ScaleBy::create(0.2f, 1.75f), ScaleTo::create(0.2f, 0.65f), NULL), 5));
                        //    }

                        //}


                    }
                }
            }
        }


    }
}
