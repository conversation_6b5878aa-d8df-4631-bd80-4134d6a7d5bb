using UnityEngine;

using X.PB;

public class Weapon : MonoBehaviour
{
    enum GunTypeStrings
    {
        WARMACHINE = 0,
        LOOSECANNON = 1,
        LASER = 2,
        DEATOMIZER = 3,
        ROCKET = 4,
    }

    #region Private Serialized Variables

    [SerializeField] public PlayerController player;
    [SerializeField] private RuntimeAnimatorController plasmaAnimatorController;
    [SerializeField] private Sprite[] bulletSprites;
    [SerializeField]
    private GameObject bulletFlashParent, multiCanonEffectPrefab, plasmaEffectPrefab,
        rocketEffectPrefab;
    #endregion

    #region Public Hidden Variables

    [HideInInspector] public bool isShooting = false;

    #endregion

    #region Private Variables

    private bool disableShooting = false;

    private Transform playerSkeletonTran;
    private GameObject[] bulletFlash;
    private Animator multiCanonEffectAnim, plasmaEffectAnim, rocketEffectAnim;

    #endregion

    #region New Variables
    [HideInInspector] public SkillProps WeaponSkill { get; protected set; }
    private Equipment.Item csvRow_Equipment;
    public GameObject skyBoomPrefab;
    public GameObject skyBoomMaxPrefab;
    public GameObject skyMissilePrefab;
    public float real攻击速度;

    //public double defaultDamege;
    #endregion

    // 激光相关预制件
    public GameObject laserStartPrefab, laserPrefab, laserImpactPrefab, laserStartParticlePrefab, laserStartGlowPrefab, SectorLaserPrefab;

    private float rocketSkillCD = 0;

    public void Start()
    {
        if (LuaToCshapeManager.Instance.EquipmentID != 0 && !player.isWhiteCatType)
        {
            csvRow_Equipment = EquipmentScheme.Instance.GetItem(LuaToCshapeManager.Instance.EquipmentID);
        
            // // 加入武器技能
            // WeaponSkill = player.SkillScheduler.AddSkill(csvRow_Equipment.ConsignmentStyle);
            // //WeaponSkill = player.SkillScheduler.AddSkill(1041);
            // string json_skill = WeaponSkill?.ToJson_Lua() ?? string.Empty;
            //
            // // 通过Lua获取武器技能的初始等级
            // int skillLvl = LuaManager.Instance.InvokeLuaFunction<int>("BattleManager.Provider_WeaponSkillLvl");
            // while (WeaponSkill.SkillLvl < skillLvl)
            // {
            //     json_skill = WeaponSkill.Upgrade_RandomEffect()?.ToJson_Lua() ?? string.Empty;
            // }
            //
            // // 引发Lua中的技能升级事件
            // LuaManager.Instance.RunLuaFunction("BattleManager.FireEvent_UpgradeSkill", json_skill);
        }

        Init();
    }
    public void Init()
    {
        playerSkeletonTran = player.skeletonAnimationTran;

        // 技能升级后,处理正在执行中的技能
        // WeaponSkill.SkillEffects.ListChanged += WeaponSkillEffects_ListChanged;

        // 初始化战斗技能管理器(它实现了常规技能)
        BattleSkillManager.Instance.Init(player, csvRow_Equipment, bulletSprites, playerSkeletonTran, bulletFlash, skyBoomPrefab, skyBoomMaxPrefab, this);
    }

    public void SetShootMode(bool val)
    {
        if (disableShooting || Globals.resetControls || Time.timeScale == 0 || !Globals.unlockTapShoot)
            return;
        isShooting = val;

        // if (!val)
        // {
        //     WeaponSkill.CTS_SkillFinish.Cancel();
        // }
    }
}
