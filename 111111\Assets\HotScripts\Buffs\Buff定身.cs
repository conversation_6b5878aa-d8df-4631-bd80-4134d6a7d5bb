﻿using System.Threading;

using UnityEngine;

using X.PB;

namespace Buffs
{
    /// <summary>
    /// 停留效果(需要配置成接管移动)
    /// </summary>
    public class Buff定身 : BuffEffectBase
    {
        protected override void DoWork_Do(CancellationToken cancel)
        {
            if (Buff.BuffScheduler.Creature.FightProp.CreatureType != CreatureType.CreatureTypeMonster) return;
            var 怪物刚体 = Buff.BuffScheduler.gameObject.GetComponentInChildren<Rigidbody2D>();
            if (!怪物刚体) return;
            
            //// 10亿
            //Buff.BuffScheduler.Creature.FightProp.Mass.Value = 1_000_000_000;

            怪物刚体.isKinematic = true;
            怪物刚体.velocity = Vector3.zero;
        }

        protected override void DoKill_Do()
        {
            if (Buff.BuffScheduler.Creature.FightProp.CreatureType != CreatureType.CreatureTypeMonster) return;
            
            var 怪物刚体 = Buff.BuffScheduler.gameObject.GetComponentInChildren<Rigidbody2D>();
            if (怪物刚体)
            {
                //Buff.BuffScheduler.Creature.FightProp.Mass.Value = Buff.BuffScheduler.Creature.FightProp.ActorProp.Mass;

                怪物刚体.isKinematic = false;
            }
        }
    }
}