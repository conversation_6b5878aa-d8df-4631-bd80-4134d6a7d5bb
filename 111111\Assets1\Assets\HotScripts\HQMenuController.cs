using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine;
using Spine.Unity;
using TMPro;
using UnityEngine.UI;
using DG.Tweening;
using UnityEngine.SceneManagement;
public class HQMenuController : MonoBehaviour
{
    private int currentSelected = 0;
    private bool isOpen = true;
    [SerializeField] private Animator mainHQMat;
    [SerializeField] private SkeletonAnimation mainHQ;
    [SerializeField] private TextMeshPro leaderBoardLabel;
    [SerializeField] private TextMeshPro shopLabel;
    [SerializeField] private TextMeshPro sidekicksLabel;
    [SerializeField] private TextMeshPro survivalLabel;
    [SerializeField] private HQCustomButton leaderBoardButton;
    [SerializeField] private HQCustomButton shopButton;
    [SerializeField] private HQCustomButton sidekicksButton;
    [SerializeField] private HQCustomButton surviveButton;
    [SerializeField] private TextMeshPro tempLabel;
    [SerializeField] private GameObject worldMenu;
    [SerializeField] private GameObject shopMenu;
    [SerializeField] private Button sideKickButton;
    [SerializeField] private Image blackScreen;
    [SerializeField] MainMenuScreen shopScreen, sidekickScreen;
    private List<HQCustomButton> helpArray = new List<HQCustomButton>();
    private List<TextMeshPro> labelArray = new List<TextMeshPro>();

    private void Start()
    {
            Init();
    }

    private void Update()
    {
        if (Input.anyKeyDown)
        {
            //OnKeyDown();
        }
    }

    private void Init()
    {
        mainHQ.state.SetAnimation(0, "balls", true);
        mainHQ.state.SetAnimation(1, "hqidle", true);
        mainHQ.state.SetAnimation(2, "buildingidle", true);
        mainHQ.state.SetAnimation(3, "building1idle", true);
        mainHQ.state.SetAnimation(4, "building2idle", true);
        mainHQ.state.SetAnimation(5, "building3", true);
        mainHQ.state.SetAnimation(6, "glaciers", true);

        //mainHQ.setAllowCulling(false);

        {
            shopButton.onClick = ShopButtonCallBack;
            shopButton.SetTag(2);
            helpArray.Add(shopButton);
            shopLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["shopButton"] as string;
            sidekicksButton.onClick = SidekicksButtonCallBack;
            helpArray.Add(sidekicksButton);
            sidekicksButton.SetTag(3);
            sidekicksLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["sidekicksButton"] as string;
            //sidekicksLabel = Label::createWithTTF(GameData::getInstance().getMenuData(GAME_DATA_SCENE::MAP_MENU).at("sidekicksButton").asString(), GAME_FONT, 35);

            leaderBoardButton.onClick = LeaderBoardButtonCallBack;
            helpArray.Add(leaderBoardButton);
            leaderBoardButton.SetTag(1);
            leaderBoardLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.SURVIVAL)["leaderBoard"] as string;
            //leaderBoardLabel = Label::createWithTTF(GameData::getInstance().getMenuData(GAME_DATA_SCENE::SURVIVAL).at("leaderBoard").asString(), GAME_FONT, 35);

            surviveButton.onClick = SurvivalButtonCallBack;
            helpArray.Add(surviveButton);
            surviveButton.SetTag(4);

            //TODO Ask Bilal Bhai
            //tempLabel = Label::createWithTTF("SURVIVE", GAME_FONT, 35);
            //surviveButton.addChild(tempLabel);
            //Shared::fontToCustom(tempLabel);
            //tempLabel.setPosition(150, 30);
            //tempLabel.setVisible(false);
            //survivalLabel = Label::createWithTTF(GameData::getInstance().getMenuData(GAME_DATA_SCENE::MAP_MENU).at("surviveButton").asString(), GAME_FONT, 35);
            survivalLabel.text = GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["surviveButton"] as string;

            labelArray.Add(leaderBoardLabel);
            labelArray.Add(shopLabel);
            labelArray.Add(sidekicksLabel);
            labelArray.Add(tempLabel);
            labelArray.Add(survivalLabel);

            //CustomMenu* menu2 = CustomMenu::create(helpArray); TODO Look Into
            //this.addChild(menu2);
            //menu2.setPosition(cocos2d::Point::ZERO);

            Globals.onHQController = true;
            Globals.onMainHQ = true; // check this
            currentSelected = 3;

            //    this.runAction(Sequence::create(DelayTime::create(0.5f),CallFunc::create([=](){
            //        Observer.DispatchCustomEvent("on_focus_button");
            //
            //    }), NULL));

            CreateKeyBoardAndControllerSupport();
            CreateMouseSupport();
            if (Globals.isJoystickConnected)
            {
                //labelArray[currentSelected].color = Color.yellow; TODO Uncomment
            }
            Observer.RegisterCustomEvent(gameObject, "activate_controller_support_hq", () =>
             {
                 currentSelected = 3;
                 Observer.DispatchCustomEvent("on_focus_button");

                 //            labelArray[0).setColor(Color.Yellow);

             });
            Observer.RegisterCustomEvent(gameObject, "ON_ENABLE_HQ", () =>
             {
                 foreach (TextMeshPro label in labelArray)
                 {
                     label.DOFade(1, 0.25f);
                 }
                 //mainHQ.GetComponent<Renderer>().material.DOBlendableColor(Color.white, 0.25f);
                 mainHQMat.SetInteger("Fade", 0);
             });
            Observer.RegisterCustomEvent(gameObject, "ON_DISABLE_HQ", () =>
             {
                 foreach (TextMeshPro label in labelArray)
                 {
                     label.DOFade(0, 0.25f);
                 }
                 //mainHQ.GetComponent<Renderer>().material.DOBlendableColor(new Color(175 / 255, 175 / 255, 175 / 255), 0.25f);
                 mainHQMat.SetInteger("Fade", 1);

             });
        }
    }


    private void ShopButtonCallBack()
    {
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
        //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        //Director::getInstance().replaceScene(TransitionFade::create(0.25, ShopScene::createScene(false), Color3B::BLACK));
        //blackScreen.DOFade(1, 0.25f).OnComplete(() =>
        //{
        //    //worldMenu.SetActive(false);
        //    //shopMenu.SetActive(true);
        //    SceneManager.LoadScene("MainMenu");
        //}).Play();
        //MainMenuController.instance.ChangeScreen(shopScreen);
        MainMenuController.startScreen = MainMenuController.MainScreenType.Shop;
        SceneManager.LoadScene("Shop");
        //ShopPanel.SetActive(true);
    }

    private void SidekicksButtonCallBack()
    {
        // FIXME:    Globals.PlaySound(SOUND_BUTTON_TAP); //Is played in the scene
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);
        //Director::getInstance().replaceScene(TransitionFade::create(0.25, SideKickMenuScene::createScene(true), Color3B::BLACK)); TODO
        //ShopPanel.SetActive(true);
        //blackScreen.DOFade(1, 0.25f).OnComplete(() =>
        //{
        //    //sideKickButton.onClick.Invoke();
        //    //worldMenu.SetActive(false);
        //    //shopMenu.SetActive(true);
        //    SceneManager.LoadScene("MainMenu");
        //}).Play();
        MainMenuController.startScreen = MainMenuController.MainScreenType.Sidekick;
        SceneManager.LoadScene("Shop");
    }

    private void LeaderBoardButtonCallBack()
    {
        //TODO
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        //ThirdPartyInterface::showGameCenterInterface();
    }

    private void SurvivalButtonCallBack()
    {
        Globals.gameType = GameType.Survival;
        AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

        //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);
        ///Director::getInstance().replaceScene(TransitionFade::create(0.25, SurvivalScene::createScene(), Color3B::BLACK));
        SceneManager.LoadScene("Survival");
    }

    private void CreateKeyBoardAndControllerSupport()
    {
        //var root = GetComponent<UIDocument>().rootVisualElement;
        //root.RegisterCallback<KeyDownEvent>(OnKeyDown, TrickleDown.TrickleDown);
        //GamePad_Apple* ga = GamePad_Apple::create();
        //this.addChild(ga);
    }

    private void OnKeyDown()
    {
        if (Globals.hqMenuEnabledController)
        {
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                Globals.hqMenuEnabledController = false;
                Globals.hudMenuEnabledController = true;
                currentSelected = 0;
                foreach (TextMeshPro a in labelArray)
                {
                    a.color = Color.white;
                }
                Observer.DispatchCustomEvent("Enable_Map_Menu_On_Controller");


            }

            if (Input.GetKeyDown(KeyCode.UpArrow))
            {

                Observer.DispatchCustomEvent("not_on_focus_button");

                if (currentSelected == 4)
                {
                    foreach (TextMeshPro a in labelArray)
                    {
                        a.color = Color.white;

                    }
                    currentSelected = 1; // get last here
                    labelArray[currentSelected].color = Color.yellow;

                }
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                mainHQ.state.SetAnimation(2, "buildingidle", true);
                mainHQ.state.SetAnimation(3, "building1idle", true);
                mainHQ.state.SetAnimation(4, "building2idle", true);
                mainHQ.state.SetAnimation(5, "building3", true);

                if (currentSelected == 1)
                {
                    mainHQ.state.SetAnimation(4, "building2", true);

                }
                if (currentSelected == 2)
                {
                    mainHQ.state.SetAnimation(3, "building1", true);

                }

                if (currentSelected == 4)
                {
                    mainHQ.state.SetAnimation(2, "building4", true);

                }
            }

            else if (Input.GetKeyDown( KeyCode.DownArrow))
            {

                Observer.DispatchCustomEvent("not_on_focus_button");

                if (currentSelected != 4)
                {
                    foreach (TextMeshPro a in labelArray)
                    {
                        a.color = Color.white;

                    }
                    currentSelected = 4;
                    labelArray[currentSelected].color = Color.yellow;

                }
                AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                mainHQ.state.SetAnimation(2, "buildingidle", true);
                mainHQ.state.SetAnimation(3, "building1idle", true);
                mainHQ.state.SetAnimation(4, "building2idle", true);
                mainHQ.state.SetAnimation(5, "building3", true);

                if (currentSelected == 1)
                {
                    mainHQ.state.SetAnimation(4, "building2", true);

                }
                if (currentSelected == 2)
                {
                    mainHQ.state.SetAnimation(3, "building1", true);

                }

                if (currentSelected == 4)
                {
                    mainHQ.state.SetAnimation(2, "building4", true);

                }
            }

            else if (Input.GetKeyDown( KeyCode.RightArrow))
            {
                if (currentSelected < 4)
                {
                    currentSelected++;
                    foreach (TextMeshPro a in labelArray)
                    {
                        a.color = Color.white;

                    }
                    Observer.DispatchCustomEvent("not_on_focus_button");

                    if (currentSelected == 3)
                    {
                        Observer.DispatchCustomEvent("on_focus_button");

                    }
                    else
                    {
                        labelArray[currentSelected].color = Color.yellow;
                    }
                    mainHQ.state.SetAnimation(2, "buildingidle", true);
                    mainHQ.state.SetAnimation(3, "building1idle", true);
                    mainHQ.state.SetAnimation(4, "building2idle", true);
                    mainHQ.state.SetAnimation(5, "building3", true);
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                    //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                    if (currentSelected == 1)
                    {
                        mainHQ.state.SetAnimation(4, "building2", true);

                    }
                    if (currentSelected == 2)
                    {
                        mainHQ.state.SetAnimation(3, "building1", true);

                    }

                    if (currentSelected == 4)
                    {
                        mainHQ.state.SetAnimation(2, "building4", true);

                    }
                }
            }

            else if (Input.GetKeyDown( KeyCode.LeftArrow))
            {
                if (currentSelected > 0)
                {

                    currentSelected--;
                    foreach (TextMeshPro a in labelArray)
                    {
                        a.color = Color.white;

                    }
                    Observer.DispatchCustomEvent("not_on_focus_button");

                    if (currentSelected == 3)
                    {
                        Observer.DispatchCustomEvent("on_focus_button");

                    }
                    else
                    {
                        labelArray[currentSelected].color = Color.yellow;
                    }
                    mainHQ.state.SetAnimation(2, "buildingidle", true);
                    mainHQ.state.SetAnimation(3, "building1idle", true);
                    mainHQ.state.SetAnimation(4, "building2idle", true);
                    mainHQ.state.SetAnimation(5, "building3", true);
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_BUTTON_TAP);

                    //Globals.PlaySound(Constants.AudioClips.SOUND_BUTTON_TAP);

                    if (currentSelected == 1)
                    {
                        mainHQ.state.SetAnimation(4, "building2", true);

                    }
                    if (currentSelected == 2)
                    {
                        mainHQ.state.SetAnimation(3, "building1", true);

                    }

                    if (currentSelected == 4)
                    {
                        mainHQ.state.SetAnimation(2, "building4", true);

                    }
                }
            }

            else if (Input.GetKeyDown( KeyCode.Return))
            {
                if (currentSelected == 0)
                {
                    LeaderBoardButtonCallBack();
                }
                if (currentSelected == 1)
                {
                    ShopButtonCallBack();
                }
                if (currentSelected == 2)
                {
                    SidekicksButtonCallBack();
                }
                if (currentSelected == 3)
                {
                    Observer.DispatchCustomEvent("call_focus_button");
                }
                if (currentSelected == 4)
                {
                    SurvivalButtonCallBack();
                }

            }
        }
    }

    private void CreateMouseSupport()
    {
        foreach (HQCustomButton a in helpArray)
        {

            a.onEnter = () =>
            {
                if (!Globals.onMap)
                {
                    AudioManager.instance.PlaySound(AudioType.Menu, Constants.AudioClips.SOUND_HOVER);

                    //Globals.PlaySound(Constants.AudioClips.SOUND_HOVER, false, 1.0f);
                    if (a.GetTag() == 1)
                    {
                        labelArray[0].color = Color.yellow;
                    }
                    else if (a.GetTag() == 2)
                    {
                        // SHOP
                        mainHQ.state.SetAnimation(4, "building2", true);
                        labelArray[1].color = Color.yellow;
                    }
                    else if (a.GetTag() == 3)
                    {
                        // SIDEKICKS
                        mainHQ.state.SetAnimation(3, "building1", true);
                        labelArray[2].color = Color.yellow;

                    }
                    else if (a.GetTag() == 4)
                    {
                        // SURVIVAL
                        mainHQ.state.SetAnimation(2, "building4", true);
                        labelArray[4].color = Color.yellow;//
                    }
                }

            };
            a.onExit = () =>
            {
                if (!Globals.onMap)
                {
                    if (a.GetTag() == 1)
                    {
                        labelArray[0].color = Color.white;

                    }
                    if (a.GetTag() == 2)
                    {
                        mainHQ.state.SetAnimation(4, "building2idle", true);
                        labelArray[1].color = Color.white;


                    }
                    if (a.GetTag() == 3)
                    {
                        mainHQ.state.SetAnimation(3, "building1idle", true);
                        labelArray[2].color = Color.white;


                    }
                    if (a.GetTag() == 4)
                    {
                        mainHQ.state.SetAnimation(2, "buildingidle", true);
                        labelArray[4].color = Color.white;
                    }
                }
            };
        }
    }

}
