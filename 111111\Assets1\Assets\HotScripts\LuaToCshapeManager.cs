using System.Collections.Generic;

using GameEvent;

using Newtonsoft.Json;

using UnityEngine;

using ViewModel;

public enum PLAYER_FIELD
{
    CREATURE_FIELD_LEVEL = 1,	// 等级
    CREATURE_FIELD_CAMPTYPE = 2,						// 阵营类型
    CREATURE_FIELD_CAMPID = 3,							// 阵营ID
    CREATURE_FIELD_END = 4,
    PLAYER_FIELD_EMBLEM = 5, // 成就等级
    PLAYER_FIELD_COUNTRY = 6, // 国家
    PLAYER_FIELD_VIPLEVEL = 7, // VIP等级
    PLAYER_FIELD_POWER = 8, // 战斗力
    PLAYER_FIELD_HP = 9, // 生命
    PLAYER_FIELD_MAXHP = 10, // 最大生命

    PLAYER_FIELD_MOVE_SPEED = 11, // 移动速度
    PLAYER_FIELD_DIE = 12, // 是否死亡
    PLAYER_FIELD_CUREXP = 13, // 当前经验值
    PLAYER_FIELD_MAXEXP = 14, // 经验值上限
    PLAYER_FIELD_SOCIETY = 15, // 帮会
    PLAYER_FIELD_ATTACKMODE = 16, // 攻击模式
    PLAYER_FIELD_PKVALUE = 17, // PK值，杀气
    PLAYER_FIELD_LASTPKTIME = 18, // 上次攻击玩家时间
    PLAYER_FIELD_EQUIPMODE = 19, // 人物装备模型
    PLAYER_FIELD_MILITARY = 20, // 军衔积分
    PLAYER_FIELD_WARBAND = 21, // 战队
    PLAYER_FIELD_GENRE = 22, // 流派
    PLAYER_FIELD_BLUEVIPLEVEL = 23,                // 蓝钻等级
    PLAYER_FIELD_BLUEVIPTYPE = 24,                 // 蓝钻类型
    PLAYER_FIELD_BROADCAST = 25, // !>以上为所有玩家可见属性

    PLAYER_FIELD_PHYSICS_ATTACK = 26, // 物理攻击
    PLAYER_FIELD_MAGIC_ATTACK = 27, // 法术攻击
    PLAYER_FIELD_PHYSICS_DEFENSE = 28, // 物理防御
    PLAYER_FIELD_MAGIC_DEFENSE = 29, // 法术防御
    PLAYER_FIELD_CRITICAL_STRIKE = 30, // 暴击
    PLAYER_FIELD_ANTI_CRITICAL_STRIKE = 31, // 防暴
    PLAYER_FIELD_PARRY = 32, // 格挡
    PLAYER_FIELD_ANTI_PARRY = 33, // 穿透
    PLAYER_FIELD_DODGE = 34, // 闪避
    PLAYER_FIELD_HIT = 35, // 命中
    PLAYER_FIELD_ARMOR = 36, // 护甲
    PLAYER_FIELD_ANTI_ARMOR = 37, // 破甲
    PLAYER_FIELD_DAMAGE_REDUCTION = 38, // 减伤
    PLAYER_FIELD_DAMAGE_ADD = 39, // 伤害加深
    PLAYER_FIELD_PLAYERDAMAGE_ADD = 40, // 对角色伤害加深
    PLAYER_FIELD_MONSTERDAMAGE_ADD = 41, // 对怪物伤害加深
    PLAYER_FIELD_DRAINRATE = 42, // 吸血比例
    PLAYER_FIELD_REFLECTRATE = 43, // 反伤比例

    PLAYER_FIELD_CURVIPSCORE = 44, // 当前VIP积分
    PLAYER_FIELD_CURENERGY = 45, // 当前体力值
    PLAYER_FIELD_DIAMOND = 46, // 钻石数
    PLAYER_FIELD_MONEY = 47, // 游戏币
    PLAYER_FIELD_MORAL = 48, // 修为值
    PLAYER_FIELD_TALENT = 49, // 天赋值(用做段位赛段位)
    PLAYER_FIELD_EXPLOIT = 50, // 功勋值(用做段位赛星星数)
    PLAYER_FIELD_BINDDIAMOND = 51, // 绑定元宝
    PLAYER_FIELD_COUPONS = 52, // 点券
    PLAYER_FIELD_EXCHANGETICKET = 53, // 兑换券
    PLAYER_FIELD_GOLDCOIN = 54, // 金币(韩版用)
    PLAYER_FIELD_TRAIN = 55, // 剩余帮贡值
    PLAYER_FIELD_PRESTIGE = 56, // 声望值
    PLAYER_FIELD_CHARM = 57, // 魅力值
    PLAYER_FIELD_RECHARGE = 58, // 累计充值
    PLAYER_FIELD_FORBIDTIME = 59, // 禁言时间
    PLAYER_FIELD_COST = 60, // 累计消费
    PLAYER_FIELD_SYNTHESISVALUE = 61, // 熔炼值
    PLAYER_FIELD_EXPERIENCE = 62, // 历练值
    PLAYER_FIELD_CURFINISHEDLEVELTASK = 63, // 当前完成的升级任务等级(需完成对应等级才能转生继续升级)
    PLAYER_FIELD_FURY = 64, // 怒气值
    PLAYER_FIELD_ACHIEVE = 65, // 成就值
    PLAYER_FIELD_ADFORBIDTIME = 66, // 广告禁言
    PLAYER_FIELD_AURA = 67,          // 灵气值
    PLAYER_FIELD_EXPADDRATE = 68,    // 经验加成比例
    PLAYER_FIELD_MONEYADDRATE = 69,  // 金钱加成比例
    PLAYER_FIELD_ACTVCOUNTSOCIETY = 70,  // 帮会累计活跃度
    PLAYER_FIELD_ACTVCOUNTWARBAND = 71,  // 战队累计活跃度
    PLAYER_FIELD_WARBANDSTACKVALUE = 72, // 战队荣誉值
    PLAYER_FIELD_AGE = 73, // 年龄
    PLAYER_FIELD_VIP_EXTRAEXP = 74,          // VIP存储池经验值(额外)
    PLAYER_FIELD_VIP_EXTRAMONEY = 75,			// VIP存储池金钱值(额外)
    PLAYER_FIELD_VIP_EVERY_GIFTVALUE = 76,		// VIP每日礼包价值(VIP存储池)
    PLAYER_FIELD_REALRECHARGE = 77,				// 真实累计充值
    PLAYER_FIELD_NEWBIEPROPLEVEL = 78,                   // 武学属性等级
    PLAYER_FIELD_NEWBIEPROPEXP = 79,                     // 武学属性经验
    PLAYER_FIELD_SYNC_END = 80, // !>以上为玩家自己可见属性
    PLAYER_FIELD_LOGINTIME = 81, // 登录时间
    PLAYER_FIELD_LOGOUTTIME = 82, // 登出时间
    PLAYER_FIELD_UPGRADETIME = 83, // 最后升级时间
    PLAYER_FIELD_SAVEDBTIME = 84, // 上次存盘时间
    PLAYER_FIELD_END = 85, //
}

public class LuaToCshapeManager : Singleton<LuaToCshapeManager>
{
    /// <summary>
    /// 是否战斗状态
    /// </summary>
    public bool IsFighting { get; set; }
    /// <summary>
    /// 开始战斗前给服务器发送的数据(让服务器知道进入副本了,才能得到奖励)
    /// </summary>
    public string SentStr_BeginState { get; set; } = string.Empty;

    [HideInInspector] public int StageID;
    [HideInInspector] public int AircraftSkinID;
    [HideInInspector] public int EquipmentID;
    [HideInInspector] public int SecondEquipmentID;
    [HideInInspector] public string[] SidekickEquipmentIDs;
    //[HideInInspector] public CatSkill.Item SidekickSkill;
    //[HideInInspector] public Weapon WeaponComp;
    [HideInInspector] public int BattleAddCoin;

    [HideInInspector] public bool stopSendSkill;
    [HideInInspector] public bool canCollected;
    [HideInInspector] public int curReviveNum;
    //游戏结束
    [HideInInspector] public bool GameOverMovileIsShow;
    //战斗中掉落的装备等
    [HideInInspector] public Dictionary<int, int> GameDropGoodsData;


    ///宠物相关的
    [HideInInspector] public int CurrentSidekickID;
    [HideInInspector] public List<int> AllBuySideKickID = new() { 1, 2, 3, 4, 5, 6 };
    [HideInInspector] public List<int> AllHadBuyEquipmentID = new();
    [HideInInspector] public Dictionary<int, SidekickStoreData> StoreData;
    [HideInInspector] public SidekicksPanel SidekicksPanel;

    /// <summary>
    /// 进入战斗时,Lua先将玩家属性数值传给C#,并存放在这里
    /// </summary>
    public PlayerFieldsByActor AllPlayerAttribute2;
    public Dictionary<int, float> AllPlayerAttribute;
    public Dictionary<int, float> AllEquipmentBuff;

    [HideInInspector] public bool OpenReviveDialog;

    [HideInInspector] public PhysicsManager curPhysicsManager;
    [HideInInspector] public BuffEffect.Item curEnemyBuffEffect;
    [HideInInspector] public bool isFirstBattle;

    //技能伤害相关的
    [HideInInspector] public double AllSkillDamagerCount;
    [HideInInspector] public Dictionary<int, double> SkillTypeToDamageMap;
    [HideInInspector] public Dictionary<int, string> BuffDesMap;

    [HideInInspector] public int totalDiamond;
    [HideInInspector] public GameOverMobile gameOver;
    [HideInInspector] public bool HadBuyADFree;

    [HideInInspector] public Dictionary<int, int> SkillAttributeCountList;//临时技能属性点数
    [HideInInspector] public Dictionary<int, int> SkillAttributePercentList;//临时技能属性百分比
    [HideInInspector] public int totalYinBi = 0;
    protected override void Awake()
    {
        base.Awake();
        AllPlayerAttribute = new Dictionary<int, float>();
        AllEquipmentBuff = new Dictionary<int, float>();
        AllPlayerAttribute2 = new();
        SkillTypeToDamageMap = new Dictionary<int, double>();
        BuffDesMap = new Dictionary<int, string>();
        GameDropGoodsData = new Dictionary<int, int>();
        SkillAttributeCountList = new Dictionary<int, int>();
        SkillAttributePercentList = new Dictionary<int, int>();
    }

    /// <summary>
    /// 进入战斗时Lua发过来的数据
    /// </summary>
    public void LuaSendData(int maxStage, int stageID, int AircraftSkin, int equipmentID, string playerAttribute, int secondID, string sidekickIDListString, int totalMoney, string allEquipBuff, bool isFirst, bool hadBuyADFree)
    {
        //Globals.whiteCatAssistDirection = 5;
        if (GameData.instance == null)
        {
            gameObject.AddComponent<GameData>();
            GameData.instance.InitNew();
        }
        GameData.instance.fileHandler.missionsCompleted = maxStage;
        StageID = stageID;
        AircraftSkinID = AircraftSkin;
        EquipmentID = equipmentID;
        SecondEquipmentID = secondID;
        SidekickEquipmentIDs = sidekickIDListString.Split('|');
        HadBuyADFree = hadBuyADFree;
        isFirstBattle = isFirst;

        Globals.game_speed = 1;
        GameData.instance.fileHandler.coins = 0;
        totalDiamond = 0;
        HandlePlayerData2(playerAttribute);
        HandleEquipBuffData(allEquipBuff);
        FileHandler.PlayerLevel = System.Convert.ToInt32(Instance.AllPlayerAttribute2.level);
        //初始化战斗
        PrepareBattleManager.Instance.SetMissionsData(stageID);
        ResetAllBattleData();
        GameOverMovileIsShow = false;
        AllSkillDamagerCount = 0;
        SkillTypeToDamageMap.Clear();
        BuffDesMap.Clear();
        Globals.isBossMode = false;
        SkillAttributeCountList.Clear();//临时技能属性点数
        SkillAttributePercentList.Clear();//临时技能属性百分比
        totalYinBi = 0;
        Globals.isManualExit = false;
    }

    public void ResetAllBattleData()
    {
        stopSendSkill = false;
        canCollected = true;
        curReviveNum = 0;
        OpenReviveDialog = false;
        BattleAddCoin = 0;
        curEnemyBuffEffect = null;
        GameOverMovileIsShow = false;
        totalDiamond = 0;
        GameDropGoodsData.Clear();
    }
    //临时处理的
    protected void HandlePlayerData2(string str)
    {
        // 进入战斗时,Lua将玩家属性值传入C#
        AllPlayerAttribute2 = JsonConvert.DeserializeObject<PlayerFieldsByActor>(str);

        //string[] dataMap = str.Split('|');
        //foreach (string data in dataMap)
        //{
        //    if (data != "")
        //    {
        //        string[] kV = data.Split(';');
        //        string k = kV[0];
        //        float v = kV[1] == "nil" ? 0f : System.Convert.ToSingle(kV[1]);
        //        if (AllPlayerAttribute2.ContainsKey(k))
        //        {
        //            AllPlayerAttribute2[k] = v;
        //        }
        //        else
        //        {
        //            AllPlayerAttribute2.Add(k, v);
        //        }
        //    }
        //}

    }
    protected void HandlePlayerData(string str)
    {
        string[] dataMap = str.Split('|');
        foreach (string data in dataMap)
        {
            if (data != "")
            {
                string[] kV = data.Split(';');
                int k = System.Convert.ToInt32(kV[0]);
                float v = kV[1] == "nil" ? 0f : System.Convert.ToInt64(kV[1]);
                if (AllPlayerAttribute.ContainsKey(k))
                {
                    AllPlayerAttribute[k] = v;
                }
                else
                {
                    AllPlayerAttribute.Add(k, v);
                }
            }
        }
    }

    protected void HandleEquipBuffData(string str)
    {
        string[] dataMap = str.Split('|');
        foreach (string data in dataMap)
        {
            if (data != "")
            {
                string[] kV = data.Split(';');
                int k = System.Convert.ToInt32(kV[0]);
                float v = kV[1] == "nil" ? 0f : System.Convert.ToInt64(kV[1]);
                if (AllEquipmentBuff.ContainsKey(k))
                {
                    AllEquipmentBuff[k] = v;
                }
                else
                {
                    AllEquipmentBuff.Add(k, v);
                }
            }
        }
    }
    public void LuaControlPlayerCallSkill(int skillID)
    {

        //BulletManager.Instance.AddSkillQue(HelpFunc.PLAYER_ID, skillID);
        //BattleScene.Instance.isAutoShoot = true;
        if (!stopSendSkill)
        {
            StartCoroutine(BattleSkillManager.Instance.AutoRunSkill(skillID));
        }

    }

    /// <summary>
    /// 引发事件：技能升级(或获得技能)
    /// </summary>
    public void FireSkillUp(int skillID)
    {
        var e = new GameEvent.SkillUp
        {
            csvRow_CatSkill = CatSkillScheme.Instance.GetItem(skillID)
        };
        Publisher.Instance.PublishMessage(e);
    }

    public void LuaRequestCreaterMonster(int brushID, string UID)
    {
        //加载怪物用协程来避免卡顿
        //StartCoroutine(MonsterManager.Instance.LoadEnemyByBrushData(brushID, UID));
    }

    public void LuaRequestUpdateEnergy(float curValue, float maxValue)
    {
        //PlayerAttributeManager.Instance.UpdateEnergyBar(curValue, maxValue);
    }

    public void PauseOrResumeBattle(float value)
    {
        if (value == 1) value = Globals.game_speed;
        Time.timeScale = value;
        //GameManager.instance.timeManager.SetTimescale(value);
    }
    //这里是lua那边发送buffid 和主动技能ID
    public void UpgradeBuff(int buffId)
    {


        if (buffId < 1000000)//技能
        {
            GameManager.instance.player.weapon.UpdateSkill(buffId);
        }
        else//buff
        {
            PlayerAttributeManager.Instance.UpgradeBuff(buffId);

        }
    }

    public void LuaSendLevelAndExp(int curLevel, int curExp)
    {
        // Debug.LogWarning("传来的等级" + curLevel + "  经验" + curExp);
        GameManager.instance.AddXp(curLevel, curExp);
        //PlayerAttributeManager.Instance.SetPlayerExp(curLevel, curExp);
    }

    public void StopShoot()
    {
        if (GameManager.instance.player.weapon != null)
        {
            //GameManager.instance.player.weapon.isShooting = false;
            GameManager.instance.EndAutoShoot();
            GameManager.instance.EndAutoShootAB();
        }
    }

    public void RevivePlayerResult(int isWin)
    {
        PlayerAttributeManager.Instance.RevivePlayer(isWin == 1);
    }

    /////////////////////////////////////////////////
    ///宠物商店相关的
    ////////////////////////////////////////////////
    public void LuaSendcurrentSideKick(int currentSideKickID, string allBuyGroup)
    {
        CurrentSidekickID = currentSideKickID;
        if (AllHadBuyEquipmentID == null)
        {
            AllHadBuyEquipmentID = new List<int>();
        }
        AllHadBuyEquipmentID.Clear();
        string[] strs = allBuyGroup.Split(',');
        foreach (string str in strs)
        {
            int equipmentID = System.Convert.ToInt32(str);
            var item = EquipmentScheme.Instance.GetItem(equipmentID);
            AllHadBuyEquipmentID.Add(item.GroupID);
        }

    }
    public void LuaSendMissionsCompleted(int maxStage)
    {
        //当前通关的关卡
        GameData.instance.fileHandler.missionsCompleted = maxStage;
    }
    public struct SidekickStoreData
    {
        public int itemID;
        public int price;
        public int priceType;
        public SidekickStoreData(int ItemID, int Price, int PriceType)
        {
            itemID = ItemID;
            price = Price;
            priceType = PriceType;
        }
    }
    //消耗类型1钻石2金币
    public void LuaSendStoreData(int itemID, int price, int priceType)
    {
        if (StoreData == null)
        {
            StoreData = new Dictionary<int, SidekickStoreData>();
        }
        int key = itemID - 1001;
        if (!StoreData.ContainsKey(key))
        {
            StoreData.Add(key, new SidekickStoreData(itemID, price, priceType));
        }
    }
    public void AddEnemyBuff(BuffEffect.Item item)
    {
        curEnemyBuffEffect = item;
    }

    public void BuySidekickSuccess()
    {
        SidekicksPanel.LuaBuySuccess();
    }
    public void ShowAutoUpgradeTip(string result)
    {
        GameManager.instance.playerHud.ShowAutoUpgradeView(result);
    }

    public void AddSkillDamageCount(int skillType, double damage)
    {
        AllSkillDamagerCount += damage;
        if (SkillTypeToDamageMap.ContainsKey(skillType))
        {
            SkillTypeToDamageMap[skillType] = SkillTypeToDamageMap[skillType] + damage;
        }
        else
        {
            SkillTypeToDamageMap.Add(skillType, damage);
        }
    }

    public string LuaGetAllDamageCount()
    {
        string resule = AllSkillDamagerCount + "|";
        foreach (var item in SkillTypeToDamageMap)
        {
            resule += (item.Key + "," + item.Value + ";");
        }
        return resule;
    }

    public void AddBuffDes(int id, string des, string old, string newValue)
    {
        if (BuffDesMap.ContainsKey(id))
        {
            BuffDesMap[id] = des + "," + old + "," + newValue;
        }
        else
        {
            BuffDesMap.Add(id, des + "," + old + "," + newValue);
        }
    }

    public string LuaGetAllBuffDes()
    {
        string resule = "";
        foreach (var item in BuffDesMap)
        {
            resule += (item.Key + ";" + item.Value + "|");
        }
        return resule;

    }

    public void CheckAutoShootMode()
    {
        //Globals.autoShootMode = PlayerPrefs.GetInt("SYSTEM_AUTO_SHOOT", 0) == 1;
        Globals.autoShootMode = true;
    }
    /// <summary>
    /// 得到战斗中拾取的物品数据
    /// </summary>
    /// <returns></returns>
    public string GetDropGoodsStr()
    {
        string result = "0";
        if (GameDropGoodsData.Count > 0)
        {
            result = "";
            foreach (var item in GameDropGoodsData)
            {
                result += item.Key.ToString() + ',' + item.Value.ToString() + ';';
            }
        }
        return result;
    }
    //临时用来结束战斗--手动退出游戏

    public int GetAllKillNum()
    {
        Globals.isManualExit = true;
        GameManager.instance.player.ChangePlayerDeath();
        return GameManager.instance.gameOverMenu.totalKillsCount;
    }
    /// <summary>
    /// 广告得到双倍奖励的回调
    /// </summary>
    /// <param name="value"></param>
    public void GetDoubleRewardByAD(int value)
    {
        gameOver.ADGetDoubleCallback(value == 1);
    }



    /// <summary>
    /// 金币购买临时技能
    /// </summary>
    public void UpgrageSkillAttribute(int id, int value1, int value2, int leftSilverCoin)
    {
        if (SkillAttributeCountList.ContainsKey(id))
        {
            //int tempValue = SkillAttributeCountList[id];
            //tempValue += value1;
            SkillAttributeCountList[id] = value1;
        }
        else
        {
            SkillAttributeCountList.Add(id, value1);
        }
        if (SkillAttributePercentList.ContainsKey(id))
        {
            //int tempValue = SkillAttributePercentList[id];
            //tempValue += value2;
            SkillAttributePercentList[id] = value2;
        }
        else
        {
            SkillAttributePercentList.Add(id, value2);
        }
        totalYinBi = leftSilverCoin;
        GameManager.instance.playerHud.UpdateYinBi();
        GameManager.instance.player.SetUpgradeSkillAttributeHealth(id, value1, value2);
    }


    public int GetSkillAttributeCount(Globals.UpgradeSkillAttibute upgradeType)
    {
        int id = (int)upgradeType;
        if (SkillAttributeCountList.ContainsKey(id))
        {
            return SkillAttributeCountList[id];
        }
        return 0;
    }
    public int GetSkillAttributePercent(Globals.UpgradeSkillAttibute upgradeType)
    {
        int id = (int)upgradeType;
        if (SkillAttributePercentList.ContainsKey(id))
        {
            return SkillAttributePercentList[id];
        }
        return 0;
    }

    //这个是辅助瞄准相关的
    public bool GetEnmeyEnterScope()
    {
        bool enter = false;
        PlayerController player = GameManager.instance.player;
        if (player == null) return enter;
        PlayerMovement movement = player.playerMovement;
        if (movement == null) return enter;
        return movement.assistDirAngle != -1;
    }



    //加速
    public float AddSpeed(int index) {
        if(Globals.g_currentStageData == null) return Globals.game_speed;
        var diffLevels = Globals.g_currentStageData.DiffLevels;
        if(diffLevels.Length <= 0) return Globals.game_speed;
        if(index >= diffLevels.Length) index = 0;
        Globals.game_speed = diffLevels[index];
        PauseOrResumeBattle(Globals.game_speed);
        return Globals.game_speed;
    }
}

