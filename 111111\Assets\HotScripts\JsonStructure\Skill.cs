﻿using System.Collections.Generic;

namespace JsonStructure
{
    /// <summary>
    /// 技能列表中的一项
    /// </summary>
    public class Skill
    {
        public int SkillID { get; set; }
        public int MaxLevel { get; set; }
        public int Type { get; set; }
        public float CD { get; set; }
        public float MinCD { get; set; }
        public string Icon { get; set; }
        public List<int> SkillEffectIDs { get; set; }

        /// <summary>
        /// 技能等级
        /// </summary>
        public int SkillLvl => SkillEffectIDs.Count + 1;
    }
}
