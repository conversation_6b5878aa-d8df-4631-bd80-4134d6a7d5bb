﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using UnityEngine;

namespace Buffs
{
	/// <summary>
	/// Buff调度器
	/// </summary>
	[DisallowMultipleComponent]
	public class BuffScheduler : MonoBehaviour
	{
		/// <summary>
		/// 属于此生物
		/// </summary>
		public CreatureBase Creature => gameObject.GetComponent<CreatureBase>();

		/// <summary>
		/// Buff列表
		/// </summary>
		public List<Buff> Buffs { get; } = new();

		/// <summary>
		/// Buff加入后
		/// </summary>
		public event System.Action<BuffScheduler, Buff> AfterAddBuff;

		protected void OnAfterAddBuff(Buff buff)
		{
			FireAfterAddBuff(buff);
		}

		public void FireAfterAddBuff(Buff buff)
		{
			AfterAddBuff?.Invoke(this, buff);
		}

		/// <summary>
		/// 被施加Buff
		/// </summary>
		public Buff AddBuff(int buffID)
		{
			if (buffID <= 0) return null;

			var csvRow_Buff = BuffScheme.Instance.GetItem(buffID)
				?? throw new System.Exception($"不存在此BuffID:{buffID}");

			//var clsName = $"Buff.Buff{csv_Buff.BuffCls}";
			//var csCls = TypeCache.Instance.GetType(clsName);
			//if (Activator.CreateInstance(csCls) is not BuffBase buff)
			//{
			//    throw new Exception($"未实现此Buff: BuffID [{buffID}] BuffCls [Buff{csv_Buff.BuffCls}]");
			//}

			var buff = new Buff
			{
				CsvRow_Buff = csvRow_Buff,
				BuffScheduler = this,
			};

			if (!Creature.CanAddBuff(buff))
			{
				return null;
			}

			// 指定插入的位置(默认值-1表示队尾)
			int index = -1;

			// 替换同类,保持列表中的位置(每类最多拥有一个)
			if (buff.CsvRow_Buff.Overlay == 1)
			{
				for (int i = 0; i < Buffs.Count; i++)
				{
					if (Buffs[i].CsvRow_Buff.Type == csvRow_Buff.Type)
					{
						index = i;
						Buffs[i].StopBuff();
						break;
					}
				}
				//// 顶掉同类Buff(可升级的Buff一定要顶掉同类Buff)
				//foreach (var buffOther in Buffs.Where(x => x.CsvRow_Buff.Type == csvRow_Buff.Type)
				//    .ToList())
				//{
				//    buffOther.StopBuff();
				//}
			}

			buff.InitEffects();

			if (index > -1)
			{
				Buffs.Insert(index, buff);
			}
			else
			{
				Buffs.Add(buff);
			}

			OnAfterAddBuff(buff);

			if (buff.UseScheduler && buff.CsvRow_Buff.DayLimit > 0)
			{
				// 使用调度器只需要引发启动前后事件即可(相当于启动了)
				buff.OnBeforeStart(this.GetCancellationTokenOnDestroy());
				buff.OnAfterStart(this.GetCancellationTokenOnDestroy());
			}
			else
			{
				// 不使用调度器就使用Buff自已启动任务
				buff.StartBuff(this.GetCancellationTokenOnDestroy()).Forget();
			}

			return buff;
		}

		/// <summary>
		/// 移动是否已被某个Buff接管
		/// </summary>
		public bool IsTakeMove()
		{
			return Buffs.Any(x => x.BuffEffects.Any(y => y.CsvRow_BuffEffect.TakeMove));
		}

		/// <summary>
		/// 启动调度器
		/// </summary>
		public void StartSchedule(float interval = 0.04f)
		{
			var token = this.GetCancellationTokenOnDestroy();
			Task_Schedule(token).Forget();
			//ScheduleTask ??= Observable.Interval(System.TimeSpan.FromSeconds(interval))
			//    .Where(_ => Time.deltaTime > 0 && Buffs.Count > 0)
			//    .Subscribe(t =>
			//    {
			//        foreach (var buff in Buffs.Where(x => x.UseScheduler && x.CsvRow_Buff.DayLimit > 0
			//            && x.LastDoTime.AddMilliseconds(x.CsvRow_Buff.DayLimit) <= System.DateTime.Now)
			//        )
			//        {
			//            DoBuff(buff).Forget();
			//        }
			//    })
			//    .AddTo(this);


		}

		private async UniTaskVoid Task_Schedule(CancellationToken token)
		{
			try
			{
				for (; ; await UniTask.NextFrame())
				{
					if (token.IsCancellationRequested) break;
					if (Time.deltaTime <= 0) continue;

					// 执行Buff过程中可能会对Buffs集合进行删除，所以必须生成一个新列表。
					var lst = Buffs.Where(x => x.UseScheduler
						&& x.BeginTime.HasValue // 有值表示这个Buff启动了
					).ToList();
					foreach (var buff in lst)
					{
						DoBuff(buff).Forget();
					}
				}
			}
			catch (System.OperationCanceledException) { throw; }
			catch (MissingReferenceException) { }
			catch (System.Exception ex)
			{
				Debug.LogException(ex);
			}
			//catch { }
			finally
			{

			}
		}

		/// <summary>
		/// 执行一次指定的Buff
		/// </summary>
		protected async UniTaskVoid DoBuff(Buff buff)
		{
			try
			{
				await UniTask.SwitchToMainThread(this.GetCancellationTokenOnDestroy());
				buff.DoBuff(this.GetCancellationTokenOnDestroy()).Forget();
			}
			catch (System.OperationCanceledException) { throw; }
			catch (MissingReferenceException) { }
			catch (System.Exception ex)
			{
				Debug.LogException(ex);
			}
			//catch { }
		}

		///// <summary>
		///// 是否处于停留状态
		///// </summary>
		//public virtual bool IsStay()
		//{
		//    return Buffs.Any(x => x.CSV_Buff.TakeMove && typeof(BuffPropAddition).IsAssignableFrom(x.GetType()));
		//}
	}
}
