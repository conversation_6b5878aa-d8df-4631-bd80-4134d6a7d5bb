﻿using System.Collections.Generic;
using System.Linq;

using Apq.Extension;

using CreatureSkills;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using Newtonsoft.Json;

using UnityEngine;

namespace HotScripts.Pets
{
	/// <summary>
	/// 宠物
	/// </summary>
	public class Pet : CreatureBase
	{
		/// <summary>
		/// 宠物的主人
		/// </summary>
		public CreatureBase Master { get; set; }

		/// <summary>
		/// 环绕角速度
		/// </summary>
		public float angleSpeed = 45f;
		/// <summary>
		/// 每轮环绕移动时的最小圈数
		/// </summary>
		public float minCircles = 0.3f;
		/// <summary>
		/// 每轮环绕移动时的最大圈数
		/// </summary>
		public float maxCircles = 1.3f;
		/// <summary>
		/// 执行环绕移动的间隔-秒
		/// </summary>
		public float surroundInterval = 0.04f;
		/// <summary>
		/// 最小停留时长-秒
		/// </summary>
		public float minStayTime = 5f;  //秒
		/// <summary>
		/// 最大停留时长-秒
		/// </summary>
		public float maxStayTime = 10f; //秒
		/// <summary>
		/// 是否处于环绕移动中
		/// </summary>
		public bool surroundMoving;

		/// <summary>
		/// [递归]间歇性转圈(随机方向)
		/// </summary>
		public async UniTaskVoid MoveCircle()
		{
			try
			{
				surroundMoving = false;
				await UniTask.Delay(System.TimeSpan.FromSeconds(Random.Range(minStayTime, maxStayTime)));
				if (!this) return;

				// 转圈速度(+-号表示方向)
				var angleSpeed1 = angleSpeed * (Random.Range(-1f, 1f) < 0 ? -1 : 1);
				//Debug.Log($"角速度:{angleSpeed}");
				// 待转总角度
				var totalAngle = Random.Range(minCircles, maxCircles) * 360;
				// 待转时长-秒
				var duration = totalAngle / angleSpeed;
				// 待转次数
				var rotateTimes = duration / surroundInterval;

				// 最后位置
				var p = transform.position;
				surroundMoving = true;
				for (var i = 0; i < rotateTimes; i++)
				{
					if (!this) return;

					// 计算新的位置
					var dest = p.RotateAround(Master.transform.position, Vector3.forward, angleSpeed1 * surroundInterval);
					transform.DOMove(dest, surroundInterval);
					p = dest;
					await UniTask.Delay(System.TimeSpan.FromSeconds(surroundInterval));
				}

				// 继续
				MoveCircle().Forget();
			}
			catch (System.Exception ex)
			{
				Debug.LogWarning($"绕角色转圈时出错:{ex.Message}");

				//// 有异常也继续
				//MoveCircle().Forget();
			}
		}

		/// <summary>
		/// 技能射程内的敌人
		/// </summary>
		public virtual List<Enemy> FindSkillEnemy(SkillProps skill)
		{
			List<Enemy> rtn = new();
			if (skill.攻击距离.Value <= 0) return rtn;

			// // 射程直接使用圈的半径
			// var distance = Globals.CycleRadius;
			// 射程直接使用圈的半径
			var distance = skill.攻击距离.Value;

			rtn = GameSharedData.Instance.enemyList
				.Where(x => !x.isDestroyed && x.canCheckDistance)
				.Select(x => new
				{
					Enemy = x,
					Distance = x ? (transform.position - x.transform.position).magnitude : float.PositiveInfinity,
				})
				//.Where(x => x.Distance.DistanceToEdge - CollisionRadius <= skill.攻击距离.Value)
				.Where(x => x.Distance <= distance)
				.Select(x => x.Enemy)
				.ToList();
			return rtn;
		}

		/// <summary>
		/// 是否应该触发技能
		/// </summary>
		public override bool ShouldDoSkill(SkillProps skill)
		{
			// 没有敌人也射击
			if (skill.CsvRow_CatSkill.ShootWhenNoEnemy)
			{
				return true;
			}

			// 有射程,射程内有敌人才射击
			if (skill.攻击距离.Value > 0)
			{
				return FindSkillEnemy(skill).Count > 0;
			}

			// 默认总是射击
			return true;
		}

		/// <summary>
		/// 使用技能
		/// </summary>
		public override async UniTaskVoid DoSkill(SkillProps skill)
		{
			await UniTask.SwitchToMainThread();
			//Debug.Log($"宠物或分身 使用技能:SkillID = {skill.SkillID} CD={skill.CD.Value} 伤害系数={skill.DamageCoe.Value} 爆炸伤害系数={skill.爆炸伤害系数.Value}");
			//Debug.Log($"宠物的攻击速度:{FightProp.攻击速度.Value} 技能基础CD={skill.CsvRow_CatSkill.Cd} 当前Buff={JsonConvert.SerializeObject(BuffScheduler.Buffs.Select(x => x.CsvRow_Buff.Id).ToList())}");
			base.DoSkill(skill).Forget();
		}

		/// <summary>
		/// 创建宠物的子弹
		/// </summary>
		/// <param name="creatureSkill">技能</param>
		/// <param name="dir_1">子弹方向</param>
		/// <param name="provider_Pos">提供初始位置算法</param>
		/// <param name="separateNo">第几次分裂的子弹(0表示直接发射的)</param>
		/// <param name="penetrateTimes">剩余穿透次数</param>
		/// <param name="bounceTimes">剩余反弹次数</param>
		/// <param name="separateTimes">剩余分裂次数</param>
		public override Bullet CreateBullet(
			CreatureSkillBase creatureSkill,
			Vector3 dir_1,
			System.Func<Vector3, Vector3> provider_Pos,
			int separateNo = 0,
			float penetrateTimes = 0,
			float bounceTimes = 0,
			float separateTimes = 0
		)
		{
			//Debug.Log($"创建技能[{creatureSkill.Skill.CsvRow_CatSkill.Id}]的子弹");

			var bullet = base.CreateBullet(creatureSkill, dir_1, provider_Pos,
				separateNo,
				penetrateTimes,
				bounceTimes,
				separateTimes);

			// 添加到使用中的玩家子弹列表
			GameSharedData.Instance.playerBulletInUse.Add(bullet);
			return bullet;
		}
	}
}