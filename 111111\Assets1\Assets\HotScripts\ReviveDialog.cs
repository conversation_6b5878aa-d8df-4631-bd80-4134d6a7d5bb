using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using TMPro;

public class ReviveDialog : MonoBehaviour
{
    public TextMeshProUGUI timeTMP, coinNumTMP;
    public CustomButton adReviveButton, coinRevievButton, diamondButton, closeButton;

    // Start is called before the first frame update
    void Start()
    {
    //    local seq = DOTween.Sequence()

    //self.objList.ItemsContainer.transform.localScale = CachedVector3(0, 0, 0)

    //seq: Append(self.objList.ItemsContainer.transform:DOScale(CachedVector3(1.1, 1.1, 1.1), 0.15))

    //seq: Append(self.objList.ItemsContainer.transform:DOScale(CachedVector3(1, 1, 1), 0.1))

    //seq: SetUpdate(true)
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
