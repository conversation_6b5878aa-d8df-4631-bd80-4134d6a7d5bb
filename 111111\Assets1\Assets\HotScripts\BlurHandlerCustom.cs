using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityStandardAssets.ImageEffects;
using UnityEngine.UI;

public class BlurHandlerCustom : MonoBehaviour
{
    [Range(0, 3)]
    public int downsample = 1;

    public enum BlurType
    {
        StandardGauss = 0,
        SgxGauss = 1,
    }

    [Range(0.0f, 10.0f)]
    public float blurSize = 3.0f;

    [Range(1, 5)]
    public int blurIterations = 2;

    public BlurType blurType = BlurType.StandardGauss;

    public Material blurMaterial = null;
    public RawImage blurLayer;

    private Texture2D screenGrab;
    private RenderTexture source;
    private RenderTexture destination;

    [HideInInspector] public bool capturedScreenshot = false;

    public void BeginBlur()
    {
        blurSize = 0;
        StartCoroutine(CaptureCoroutine());
    }

    IEnumerator CaptureCoroutine()
    {
        yield return new WaitForEndOfFrame();
        screenGrab = ScreenCapture.CaptureScreenshotAsTexture();
        //blurMaterial.SetTexture("_MainTex", screenGrab);
        //blurMaterial.SetTexture("_RenTex", screenGrab);
        capturedScreenshot = true;
        source = RenderTexture.GetTemporary(screenGrab.width, screenGrab.height, 0);
        Graphics.Blit(screenGrab, source);
        while (blurSize < 5)
        {

            blurLayer.enabled = false;
            blurSize += Time.unscaledDeltaTime*8;
            RenderImage();
            yield return null;
        }

    }

    public void DisableBlur()
    {
        StopAllCoroutines();
        capturedScreenshot = false;
        RenderTexture.ReleaseTemporary(source);
        RenderTexture.ReleaseTemporary(destination);
        DestroyImmediate(screenGrab);
        blurLayer.enabled = false;
    }

    private void RenderImage()
    {
        float widthMod = 1.0f / (1.0f * (1 << downsample));

        blurMaterial.SetVector("_Parameter", new Vector4(blurSize * widthMod, -blurSize * widthMod, 0.0f, 0.0f));
        source.filterMode = FilterMode.Bilinear;

        int rtW = source.width >> downsample;
        int rtH = source.height >> downsample;
        destination = RenderTexture.GetTemporary(rtW, rtH, 0, source.format);
        // downsample
        RenderTexture rt = RenderTexture.GetTemporary(rtW, rtH, 0, source.format);

        rt.filterMode = FilterMode.Bilinear;
        Graphics.Blit(source, rt, blurMaterial, 0);

        var passOffs = blurType == BlurType.StandardGauss ? 0 : 2;

        for (int i = 0; i < blurIterations; i++)
        {
            float iterationOffs = (i * 1.0f);
            blurMaterial.SetVector("_Parameter", new Vector4(blurSize * widthMod + iterationOffs, -blurSize * widthMod - iterationOffs, 0.0f, 0.0f));

            // vertical blur
            RenderTexture rt2 = RenderTexture.GetTemporary(rtW, rtH, 0, source.format);
            rt2.filterMode = FilterMode.Bilinear;
            Graphics.Blit(rt, rt2, blurMaterial, 1 + passOffs);
            RenderTexture.ReleaseTemporary(rt);
            rt = rt2;

            // horizontal blur
            rt2 = RenderTexture.GetTemporary(rtW, rtH, 0, source.format);
            rt2.filterMode = FilterMode.Bilinear;
            Graphics.Blit(rt, rt2, blurMaterial, 2 + passOffs);
            RenderTexture.ReleaseTemporary(rt);
            rt = rt2;
        }

        Graphics.Blit(rt, destination);

        blurMaterial.SetTexture("_MainTex", destination);
        //blurLayer.texture = destination;

        blurLayer.enabled = true;
        RenderTexture.ReleaseTemporary(rt);
    }

}
