using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlayerFlying : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        if (player.playerMovement.GotMovementInput)
            player.SetPlayerAnimation(PlayerController.PlayerAnimation.Flying);
        else
            playerStateMachine.SetState<PlayerIdle>();
    }

    public override void UpdateState()
    {
        if (player.weapon.isShooting)
            playerStateMachine.SetState<PlayerShoot>();

        if (!player.playerMovement.GotMovementInput)
            playerStateMachine.SetState<PlayerIdle>();
    }

    public override string GetStateType()
    {
        return "Flying";
    }
}


public class PlayerIdle : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        if (player.playerMovement.GotMovementInput)
            playerStateMachine.SetState<PlayerFlying>();
        else
            player.SetPlayerAnimation(PlayerController.PlayerAnimation.Idle);
    }

    public override void UpdateState()
    {
        if (player.weapon.isShooting)
            playerStateMachine.SetState<PlayerShoot>();

        if (player.playerMovement.GotMovementInput)
            playerStateMachine.SetState<PlayerFlying>();
    }

    public override string GetStateType()
    {
        return "Idle";
    }
}


public class PlayerFlip : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        player.SetPlayerAnimation(PlayerController.PlayerAnimation.Flip);
    }

    public override string GetStateType()
    {
        return "Flip";
    }
}

public class PlayerDash : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        player.PlayerDash();
    }

    public override string GetStateType()
    {
        return "Dash";
    }
}

public class PlayerShoot : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        player.SetPlayerAnimation(PlayerController.PlayerAnimation.Shoot);
    }

    public override void UpdateState()
    {
        if (!player.weapon.isShooting)
        {
            if (player.playerMovement.GotMovementInput)
                playerStateMachine.SetState<PlayerFlying>();
            else playerStateMachine.SetState<PlayerIdle>();
        }
    }

    public override string GetStateType()
    {
        return "Shoot";
    }
}

public class PlayerDeath : State
{
    PlayerStateMachine playerStateMachine;
    PlayerController player;
    float initialY;
    bool goingUp;

//    PlayerStateDeath::PlayerStateDeath()
//{
//      Player::getInstance()->setMix( "flyingUp", "freeFall",0.2f);
//    Player::getInstance()->setMix( "freeFall", "afterDeath",0.2f);

//}

    void Init()
    {
        playerStateMachine = stateMachine as PlayerStateMachine;
        player = playerStateMachine.player;

        initialY = player.transform.position.y;
        goingUp = true;
    }

    public override void DidEnterWithPreviousState(State previousState)
    {
        if (playerStateMachine == null)
            Init();

        player.transform.localScale = new Vector3(-0.21f, 0.21f, 1);
        //player.GetSkeletonAnimation().transform.SetRotation(0);
        //player.GetSkeletonAnimation().transform.localScale = new Vector3(player.GetSkeletonAnimation().transform.localScale.y < 0 ? -1 : 1, 1, 1);
        //player.GetSkeletonAnimation().state.SetAnimation(0, "sadKitty", true);
        AudioManager.instance.PlaySound(AudioType.Player, Constants_Audio.Audio.playerBoostStart);
        //Shared::playSound("res/Sounds/SFX/playerBoostStart.mp3", false, 1.0f); TODO

        //player.GetSkeletonAnimation().skeleton.SetAttachment("wing1", null);
        //player.GetSkeletonAnimation().skeleton.SetAttachment("wing2", null);
        player.DisableHealthBar();
        //player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE;

        GameManager.instance.PlayPlayerLoseSequence();
        player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_ENDMISSION;



        //float accX = 0, accY = 0;

        //    accY = Globals.CocosToUnity(20) / (Time.deltaTime * 60);


        //if (player.transform.localScale.y < 0)
        //{
        //    accX = Globals.CocosToUnity(3);
        //}
        //else
        //{
        //    accX = -Globals.CocosToUnity(3);
        //}

        //player.playerMovement.SetAcceleration(new Vector2(accX, accY));
    }

    public override void UpdateState()
    {
        //float accX = player.playerMovement.GetAcceleration().x, accY = player.playerMovement.GetAcceleration().y;

        //if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE)
        //{
        //    //turn on when required!
        //    accY = accY - Globals.GRAVITY * Time.deltaTime * 60;
        //    accY = Mathf.Clamp(accY, -Globals.SPEEDLIMIT, Globals.SPEEDLIMIT);
        //    accX = accX * Globals.DRAG;

        //    if (player.transform.position.y > Globals.UPPERBOUNDARY)
        //    {
        //        accY = accY - Globals.GRAVITY * Time.deltaTime * 60;
        //    }

        //    player.playerMovement.SetAcceleration(new Vector2(accX, accY));
        //    //player.transform.SetWorldPosition(player.transform.position.x + accX * Time.deltaTime * 60, player.transform.position.y + accY * Time.deltaTime * 60);
        //}
        //else if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_DEATHDROP)
        //{
        //    //turn on when required!
        //    //accY = accY - Globals.GRAVITY * Time.deltaTime * 60;
        //    //accY = Mathf.Clamp(accY, -Globals.SPEEDLIMIT / 500, Globals.SPEEDLIMIT / 500);
        //    accY = 0;
        //    accX = accX * Globals.DRAG;
        //    if (GameManager.instance.timeManager.TimeScale != 1)
        //    {
        //        GameManager.instance.timeManager.SetTimescale(1);
        //    }

        //    if (player.transform.position.y > Globals.UPPERBOUNDARY)
        //    {
        //        accY = accY - Globals.GRAVITY * Time.deltaTime * 60;
        //    }

        //    player.playerMovement.SetAcceleration(new Vector2(accX, accY));
        //    GameManager.instance.PlayPlayerLoseSequence();
        //    player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_ENDMISSION;
        //    //player.transform.SetWorldPosition(player.transform.position.x + accX * Time.deltaTime * 60, player.transform.position.y + accY * Time.deltaTime * 60);
        //}
        if (player.Mode == PlayerController.PLAYERMODE.PLAYER_MODE_BEFORECHUTE)
        {
            //if (goingUp && accY < 0)
            //{
            //    goingUp = false;
            //    player.GetSkeletonAnimation().state.SetAnimation(0, "freeFall", true);
            //}
            //if ((player.transform.position.y < initialY - 0.5f || player.transform.position.y < Globals.LOWERBOUNDARY + 1)
            //    && accY < 0)
            //{
            //    player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_DEATHDROP;
            //    player.GetSkeletonAnimation().state.SetAnimation(0, "sadKitty", true);
            //}

            //player.GetSkeletonAnimation().state.SetAnimation(0, "sadKitty", true);
            //GameManager.instance.PlayPlayerLoseSequence();
            //player.Mode = PlayerController.PLAYERMODE.PLAYER_MODE_ENDMISSION;
        }
    }

    public override string GetStateType()
    {
        return "Death";
    }
}
