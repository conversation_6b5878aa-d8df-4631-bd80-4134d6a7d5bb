﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Apq.Net
{
    /// <summary>
    /// 代表一个连接到服务端的长连接(客户端)
    /// </summary>
    public class TcClient : PersistentClient
    {
        /// <summary>
        /// 代表一个连接到服务端的长连接(客户端)
        /// </summary>
        /// <param name="provider_NewClient">提供创建新连接的方法</param>
        public TcClient(Func<object> provider_NewClient) : base(provider_NewClient)
        {
        }

        /// <summary>
        /// TcpClient
        /// </summary>
        /// <remarks>创建后才不为null</remarks>
        public TcpClient TcpClient => Client as TcpClient;

        #region 连接

        /// <summary>
        /// 设置Client相关事件的处理
        /// </summary>
        protected override void BindClientEvents()
        {
        }

        /// <summary>
        /// 取消Client相关事件的处理
        /// </summary>
        protected override void UnBindClientEvents()
        {
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public override bool Connected => TcpClient is { Connected: true };

        /// <summary>
        /// Client发起连接
        /// </summary>
        protected override async UniTaskVoid DoTask_Connect(CancellationToken token = default)
        {
            // 必须切换到线程池发起连接
            await UniTask.SwitchToThreadPool();
            
            IAsyncResult asyncResult = null;
            try
            {
                asyncResult = TcpClient.BeginConnect(Server, Port, null, null);
                asyncResult.AsyncWaitHandle.WaitOne();
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                Debug.LogException(ex);
            }
            finally
            {
                if (asyncResult != null) TcpClient.EndConnect(asyncResult);
            }
        }

        #endregion

        #region 关闭

        /// <summary>
        /// 调用Client的关闭方法
        /// </summary>
        protected override void CloseClient()
        {
            TcpClient?.Close();
        }

        #endregion

        #region 发送

        /// <summary>
        /// 发送数据到服务端(实现)
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected override async UniTask<bool> DoTask_Send(IList<byte> data, CancellationToken token = default)
        {
            if (!Connected)
            {
                throw new SocketException((int)SocketError.SocketError);
            }

            var networkStream = TcpClient.GetStream();
            if (networkStream is not { CanWrite: true })
            {
                throw new SocketException((int)SocketError.AccessDenied);
            }

            try
            {
                await networkStream.WriteAsync(data.ToArray().AsMemory(0, data.Count), token);
                await networkStream.FlushAsync(token);

                // 因为发送成功才会执行到这里,所以可以更新最后活动时间
                LastActiveTime = Time.unscaledTime;
            }
            catch (Exception ex)
            {
                Debug.LogError($"发送数据时异常!{Environment.NewLine}{ex?.Message}{Environment.NewLine}{ex?.StackTrace}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 发送消息到服务端
        /// </summary>
        /// <returns>是否发送成功</returns>
        protected override UniTask<bool> DoTask_Send(string msg, CancellationToken token = default)
        {
            // var data = Encoding.UTF8.GetBytes(msg);
            // return await DoTask_Send(data, token);
            return UniTask.FromResult(true);
        }

        #endregion

        #region 接收

        /// <summary>
        /// 任务实现:接收
        /// </summary>
        protected override async UniTaskVoid DoTask_Receive(CancellationToken token = default)
        {
            try
            {
                while (true)
                {
                    await UniTask.NextFrame();
                    await UniTask.WaitUntil(() => TcpClient is { Connected: true, Available: > 0 },
                        cancellationToken: token);
                    try
                    {
                        token.ThrowIfCancellationRequested();

                        var networkStream = TcpClient.GetStream();
                        if (networkStream is not { CanRead: true })
                        {
                            continue;
                        }

                        var buffer = new byte[TcpClient.ReceiveBufferSize];
                        var len = await networkStream.ReadAsync(buffer.AsMemory(0, TcpClient.ReceiveBufferSize), token);
                        if (len <= 0) continue;

                        var data = new byte[len];
                        Array.Copy(buffer, data, len);

                        OnAfterReceiveData(data);
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError(ex);
                        Debug.LogException(ex);
                        // 只要不是操作取消,就一直运行
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                Debug.LogException(ex);
            }
        }

        #endregion
    }
}