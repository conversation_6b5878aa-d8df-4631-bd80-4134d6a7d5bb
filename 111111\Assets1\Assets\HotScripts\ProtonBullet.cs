﻿using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class ProtonBullet : Bullet
{
    [SerializeField] Sprite sprite;
    private bool dynamicPosition;
    //bool init();
    private float turnSpeed = 2;
    private float radius = 100;
    private Transform player;
    private bool allowUpdate;

    //public void SpecialAbility();

    [HideInInspector] public Vector2 staticPosition;
    //void initProtonBullet();
    //void staticUpdate(float dt);
    void setTurnSpeed(float speed) { turnSpeed = speed; }

    public void Init()
    {
        allowUpdate = false;
        setReactToWater(false);
        setDamage(50);
        radius = Globals.CocosToUnity(100);
        turnSpeed = 2;
        bulletRotation = 0;
        SetSpriteFrame(sprite);
    }

    void InitProtonBullet()
    {

        if (dynamicPosition)
        {
            spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g,
                spriteRenderer.color.b, 0);
            spriteRenderer.DOFade(1, 0.5f);
            //MotionStreak* streak = MotionStreak::create(0.1f, 1, 45, Color3B::WHITE, "res/Arsenal/blueStreak.png");

            allowUpdate = true;
        }
        else
        {
            spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g,
                spriteRenderer.color.b, 0);
            spriteRenderer.DOFade(1, 0.5f);
            //MotionStreak* streak = MotionStreak::create(0.15f, 1.0, 45, Color3B::WHITE, "res/Arsenal/blueStreak.png");

            //this->schedule(schedule_selector(ProtonBullet::staticUpdate), 0);
        }
    }

    void Update()
    {
        transform.position = player.transform.position * Vector2.one + new Vector2(radius * Mathf.Cos(bulletRotation * Mathf.Deg2Rad), radius * Mathf.Sin(bulletRotation * Mathf.Deg2Rad));
        bulletRotation = bulletRotation + (turnSpeed * Time.deltaTime * 60);
        //this->getChildByTag(5)->setPosition(bulletSprite->getPosition());
    }

    //void ProtonBullet::staticUpdate(float dt)
    //{

    //    bulletSprite->setPosition(staticPosition.x + radius * sinf(CC_DEGREES_TO_RADIANS(bulletRotation)), staticPosition.y + radius * cosf(CC_DEGREES_TO_RADIANS(bulletRotation)));
    //    bulletRotation = bulletRotation + (TurnSpeed * dt * 60);

    //    this->getChildByTag(5)->setPosition(bulletSprite->getPosition());
    //}
}
