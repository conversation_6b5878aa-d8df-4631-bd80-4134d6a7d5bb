using UnityEngine;
using System.Collections;
using Spine;
using Spine.Unity;
using DG.Tweening;

public class FlyBee : Enemy
{
    [SerializeField] private EnemyFall enemyFallPrefab;
    [SerializeField] private Sprite bulletSprite;

    private int enemyLevel;//all

    private bool allowShooting;//all
    private bool allowBoost;//2+

    private float scaleFactor;//all
    private Vector2 chaseOffset;
    private float chaseDistance;
    private bool allowRotation = true;

    private WaveStructure _info;

    private bool _createdAsType = false;
    private Sprite _ping = null;


    private bool _charge = false;
    private bool _isIntroComplete = false;


    private Bullet bullet;

    private float tSpeed;
    private float rSpeed;
    float totalDistanceSquared;
    Vector2 PlayerPositionToFollow;
    float playerRotation;

    public override void Init()
    {
        if (initialized)
            return;
        base.Init();
        allowFollow = true;
        SetVariable();
        SetAnimationVariables();
    }

    private void SetVariable()
    {
        scheduleUpdate = true;
        enemySchedulerSpeed = 0.075f;
        scaleFactor = 0.21f;
        allowPushBack = true;
        allowStack = false;
        allowDeathParticles = true;
        float offset = Globals.CocosToUnity(100);
        chaseOffset = new Vector2((-offset / 2) + (Random.value * offset), (-offset / 2) + (Random.value * offset));
        chaseDistance = Globals.CocosToUnity(30000) + Random.value * Globals.CocosToUnity(70000);
        InitStats();
    }

    private void SetAnimationVariables()
    {

        Vector2 posDiff = player.transform.position - enemyPoint.position;
        float lookAtPlayer = Mathf.Atan2(posDiff.y, posDiff.x) * Mathf.Rad2Deg;

        enemyPoint.rotation = Quaternion.Euler(0, 0, lookAtPlayer);
    }

    private void Update()
    {

        SetUpdateVariables();
        HandleRotation();
        HandleShooting();
        HandleMovement();

        if (healthBar)
        {
            healthBar.transform.position = new Vector2(enemyPoint.position.x, enemyPoint.position.y - Globals.CocosToUnity(75));
        }
    }
    private void SetUpdateVariables()
    {

        totalDistanceSquared = Vector2.SqrMagnitude(enemyPoint.position - player.transform.position);//  transform.position.distanceSquared(Player::getInstance()->getPosition());
        PlayerPositionToFollow = player.transform.position;
        if (totalDistanceSquared > chaseDistance && !isBoss)
        {
            PlayerPositionToFollow += chaseOffset;
        }

        tSpeed = 1;
        rSpeed = 1;
        if (enemyPoint.position.y < Globals.LOWERBOUNDARY)
        {
            rSpeed = 3.5f;
            tSpeed = 0.25f;

        }
        if (enemyPoint.position.y > Globals.UPPERBOUNDARY)
        {
            rSpeed = 3.5f;

        }
    }
    
    private void HandleRotation()
    {
        Vector2 posDiff1 = player.transform.position - transform.position;
        playerRotation = Vector2.Angle(posDiff1, transform.right);
        if (playerRotation == 0)
        {
            playerRotation = 1;
        }

        if (player.transform.position.y < transform.position.y)
        {
            playerRotation *= -1;
            playerRotation += 360;
        }

        if (_info)
        {
            playerRotation = Globals.CalcAngle(enemyPoint.position,
                new Vector2(PlayerPositionToFollow.x + _info.sInfo._position.x, PlayerPositionToFollow.y + _info.sInfo._position.y)) - 180;
        }
        if (playerRotation - enemyPoint.eulerAngles.z > 200)
        {
            int i = (int)(playerRotation - enemyPoint.eulerAngles.z) / 360;
            playerRotation = playerRotation - (360 * (i + 1));


        }
        if (playerRotation - enemyPoint.eulerAngles.z < -200)
        {
            int i = (int)(playerRotation - enemyPoint.eulerAngles.z) / 360;

            playerRotation = playerRotation + (360 * (i + 1));

        }
        if (allowFollow)
        {

            if (allowRotation)
            {
                float angleDifference = -enemyPoint.eulerAngles.z + playerRotation;
                if (Mathf.Abs(angleDifference) < 60 * Time.deltaTime) // finished rotation
                {
                    enemyPoint.eulerAngles = new Vector3(0, 0, playerRotation);
                }
                else if (angleDifference > 0)
                {
                    enemyPoint.rotation = Quaternion.Euler(0, 0, enemyPoint.eulerAngles.z + 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
                else
                {
                    enemyPoint.rotation = Quaternion.Euler(0, 0, enemyPoint.eulerAngles.z - 50 * Time.deltaTime * rSpeed * stats.turnSpeed);

                }
            }

        }
    }

    private void HandleShooting()
    {
        curCheckStateTime += Time.deltaTime;
        if(curCheckStateTime > checkStateTime)
        {
            curCheckStateTime = 0;
            posDiff2 = player.transform.position - enemyPoint.position;
            allowShooting = false;
            shootRandom = Random.Range(0, 10000);
            if (shootRandom > skillShootProbability)
            {
                allowShooting = false;
                return;
            }

            if (totalDistanceSquared < stats.distanceToShoot && !_charge)
            {
                //Debug.Log("totalDistanceSquared=" + totalDistanceSquared + "   stats.distanceToShoot=" + stats.distanceToShoot + "   allowShooting=" + allowShooting);
                if (!allowShooting)
                {
                    allowShooting = true;
                    shootLeft();
                }

            }
            else
            {
                allowShooting = false;
            }
        }


    }
    
    private void HandleMovement()
    {
        CheckUpdate();
        //transform.position = new Vector2(transform.position.x + stats.speed * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z + 90)) * Time.deltaTime * Globals.CocosToUnity(60f), transform.position.y + stats.speed * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 90)) * Time.deltaTime * Globals.CocosToUnity(60f) * tSpeed);
        if (allowBoost && Vector2.SqrMagnitude(enemyPoint.position - player.transform.position) > Globals.CocosToUnity(625))
        {
            //enemyPoint.position = new Vector2(enemyPoint.position.x + stats.speed / 2 * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z + 90)), enemyPoint.position.y + stats.speed / 2 * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 90)));
            if (allowFollow)
            {
                enemyPoint.eulerAngles = new Vector3(0, 0, enemyPoint.eulerAngles.z + (-enemyPoint.eulerAngles.z + playerRotation) * Time.deltaTime * stats.turnSpeed / 2);
            }
        }
    }

    private void HandleSpineEvent( TrackEntry trackEntry, Spine.Event spineEvent)
    {
        if (spineEvent.Data.Name== "shootright")
        {
            if (enemyPoint.localScale.y > 0)
            {
                shootLeft();
            }
            else
            {
                shootRight();
            }
        }
        else if (spineEvent.Data.Name== "shootleft")
        {
            if (enemyPoint.localScale.y > 0)
            {
                shootRight();
            }
            else
            {
                shootLeft();
            }
        }
    }
    public override void SetEnemySpecialAttributes()
    {

    }

    

    public void shootLeft()
    {
        if (!allowShooting)
        {
            return;
        }
        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }
        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);

        bullet.transform.position = new Vector2(enemyPoint.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 45)),
           enemyPoint.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 45)));
        bullet.transform.eulerAngles = new Vector3(0, 0, enemyPoint.eulerAngles.z - 90);
        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z)));
        dest *= stats.attackDistance;
        bullet.gameObject.SetActive(true);
        bullet.PlayBulletAnim(stats.bulletSpeed, dest,false);
       
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all


    public void shootRight()
    {
        if (!allowShooting)
        {
            return;
        }

        bool didFindBullet = false;
        foreach (Bullet b in GameSharedData.Instance.enemyBulletPool)
        {
            if (!b.isInUse)
            {
                bullet = b;
                bullet.isInUse = true;
                didFindBullet = true;
                break;
            }
        }
        if (!didFindBullet)
        {
            return;
        }

        bullet.SetSpriteFrame(bulletSprite);
        bullet.setDamage(stats.bulletDamage);

        bullet.transform.position = new Vector2(enemyPoint.position.x + Globals.CocosToUnity(35) * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 135)),
            enemyPoint.position.y + Globals.CocosToUnity(35) * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z - 135)));

        bullet.transform.eulerAngles = new Vector3(0, 0, enemyPoint.eulerAngles.z - 90);

        Vector2 dest = new Vector2((Globals.CocosToUnity(3000) * Mathf.Cos(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z))), Globals.CocosToUnity(3000) * Mathf.Sin(Mathf.Deg2Rad * (enemyPoint.eulerAngles.z)));
        dest *= stats.attackDistance;
        bullet.gameObject.SetActive(true);
        bullet.PlayBulletAnim(stats.bulletSpeed, dest,false);
        GameSharedData.Instance.enemyBulletInUse.Add(bullet);
        bullet.setRadiusEffectSquared(1);
    }//all

    private void InitStats()
    {
        stats = new Attributes();
        baseStats = new Attributes();

        stats.speed = baseStats.speed = 5;
        stats.turnSpeed = baseStats.turnSpeed = 0.85f;
        stats.health = baseStats.health =100;// = (::getStats()->maxHealth + 20 + difficulty) / 1.5f;
        //    stats.health = baseStats.health = 600;
        stats.maxHealth.Value = baseStats.maxHealth.Value = stats.health;
        stats.bulletDamage = baseStats.bulletDamage = 2.5f +Globals.difficulty + (5.0f * GameData.instance.fileHandler.currentMission/ 5);
        stats.bulletSpeed = baseStats.bulletSpeed = 7;
        stats.coinAwarded = baseStats.coinAwarded = 1;
        stats.xp = baseStats.xp = stats.maxHealth.Value;

        SetEnemyDifficulty();
    }//all

    public override void Destroy()
    {
        if(gameObject != null && gameObject.activeInHierarchy)
        {
            StartCoroutine(DestroyCoroutine());
        }
        //if (allowDeathParticles) //&& FileHandler::getInstance().currentMission == 2 && gameType == GameType::Arena)TODO
        //{
        //    dynamicFallEnemy(enemy);
        //}
    }

    private IEnumerator DestroyCoroutine()
    {
        yield return new WaitForEndOfFrame();
        EnemyFall enemyFall = null;
        if (Random.value < 0.2f && allowDeathParticles)
        {
            // enemyFall = Instantiate(enemyFallPrefab);
            // enemyFall.InitFallEnemy(this, 1);
            for (int i = 0; i < 3; i++)
            {
                GameObject go = GameSharedData.Instance.GetPrefabByType(DefaultGameObjectType.Debris);
                Gibs debris = go.GetComponent<Gibs>();
                debris.isInUse = true;    
                debris.CreateWithData(400, 5, true, true);
                debris.transform.position = transform.position;
            }
        }
        yield return new WaitForEndOfFrame();
        base.Destroy();
    }

    public static HammerHead createWithInfo(WaveStructure info)
    {
        return null;
    }

}
