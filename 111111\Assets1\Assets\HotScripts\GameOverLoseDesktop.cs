using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Spine.Unity;
using UnityEngine.SceneManagement;
using TMPro;
public class GameOverLoseDesktop : BaseMenu
{
    [SerializeField] private Image blackOverlay;
    [SerializeField] private MainMenuButton retryButton;
    [SerializeField] private MainMenuButton difficultyButton;
    [SerializeField] private MainMenuButton shopButton;
    [SerializeField] private MainMenuButton hqButton;
    [SerializeField] private GameObject shopHighlight;
    [SerializeField] private TextMeshProUGUI shopHighlightText;


    public void Show()
    {
        if (!gameObject.activeSelf)
        {
            gameObject.SetActive(true);
            selectedPosition = 0;
            InitChild();
        }
    }

    private void InitChild()
    {


#if UNITY_STANDALONE
        //TODO Create Pointer
        //DesktopPointer *dp = DesktopPointer::create(true);
        //this.addChild(dp, INT_MAX);
#endif
        Globals.StopAllSounds();

        // AudioManager.instance.PlayMusic(Track.bossLoose);
        AudioManager.instance.PlayMusic(7004);
        //Globals.PlaySound("res/Sounds/BGM/bossLoose.mp3");
        //TODO Curosor
        //Director::getInstance().getOpenGLView().setCursorVisible(false);
        //    Shared::pauseRecursive(this.getParent(), true);
        exitIndex = -1;


        {
            retryButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["retryButton"] as string, () =>
            {

                GameData.instance.fileHandler.ReadGuns();
                GameData.instance.fileHandler.SaveData();
                GameManager.instance.timeManager.SetTimescale(1);
                //Director::getInstance()->resume();
                //Director::getInstance()->getScheduler()->setTimeScale(1.0f);

                //Director::getInstance()->replaceScene(GameScene::createScene());
                SceneManager.LoadScene(4);
            });
        }
        buttonsList.Add(retryButton);
        {
            //if (Globals.gameType != GameType.Survival)
            //{
            //    difficultyButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["selectDifficulty"] as string, () =>
            //    {
            //        GameData.instance.fileHandler.ReadGuns();
            //        GameData.instance.fileHandler.SaveData();
            //        GameManager.instance.timeManager.SetTimescale(1);

            //        Globals.g_showDifficultyInUtilityMenu = true;
            //        SceneManager.LoadScene(3);
            //    });
            //    difficultyButton.gameObject.SetActive(true);
            //    buttonsList.Add(difficultyButton);
            //}
            //else
            //{
                difficultyButton.gameObject.SetActive(false);
            //}
        }
        {
            if (GameData.instance.fileHandler.missionsCompleted >= 1)
            {
                shopButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.PAUSE_MENU)["visitShop"] as string, () =>
                {

                    GameData.instance.fileHandler.ReadGuns();
                    GameData.instance.fileHandler.SaveData();
                    GameManager.instance.timeManager.SetTimescale(1);
                    Globals.showShop = true;
                    SceneManager.LoadScene(3);
                });

                int getShopItemsAvailable = GameData.instance.CalculateShopItemsToBeUnlocked();
                if (getShopItemsAvailable > 0)
                {
                    shopHighlight.SetActive(true);
                    DOTween.Sequence().SetId("highlight").SetUpdate(true).Append(shopHighlight.transform.DOScale(Vector3.one * 1.87f, 0.75f).SetEase(Ease.InOutSine)).Append(shopHighlight.transform.DOScale(Vector3.one * 1.75f, 0.75f).SetEase(Ease.InOutSine)).SetLoops(-1).Play();
                    shopHighlightText.text = getShopItemsAvailable.ToString();
                    //Sprite* circle = Sprite::create("res/CustomButton/Circle2.png");
                    //node->getMainLabel()->addChild(circle);
                    //circle->setTag(5);
                    //circle->setPosition(node->getMainLabel()->getContentSize() - circle->getContentSize() / 4);
                    //circle->setColor(Color3B::RED);
                    //circle->setScale(1.75f);
                    //circle->runAction(RepeatForever::create(Sequence::create(EaseSineInOut::create(ScaleTo::create(0.75f, 1.87f)), EaseSineInOut::create(ScaleTo::create(0.75f, 1.75f)), NULL)));

                    //Label* numberOfItems = Label::createWithTTF(to_string(getShopItemsAvailable), GAME_FONT, 20);
                    //circle->addChild(numberOfItems);
                    //numberOfItems->setPosition(circle->getContentSize() / 2);
                    //Shared::fontToCustom(numberOfItems);
                }

                buttonsList.Add(shopButton);
            }
            else
            {
                shopButton.gameObject.SetActive(false);
            }
        }

        if (GameData.instance.fileHandler.missionsCompleted >= 0)
        {
            hqButton.InitVariables(GameData.instance.GetMenuData(Constants.GAME_DATA_SCENE.MAP_MENU)["headQuarters"] as string, () => {
                Globals.backFromGamePlay = true;


                GameData.instance.fileHandler.ReadGuns();
                GameData.instance.fileHandler.SaveData();
                GameManager.instance.timeManager.SetTimescale(1);
                //Director::getInstance()->getOpenGLView()->setCursorVisible(false);
                //SceneManager.LoadScene(2);
                LuaManager.Instance.RunLuaFunction<int, int, int>("BattleManager.BattleEnd", 0, LuaToCshapeManager.Instance.BattleAddCoin, 0);
            });
            buttonsList.Add(hqButton);
        }

        Init();
        selectedPosition=0;
        UpdateSelected();
    }


}
